from math import pi, radians, tan, floor
import numpy as np
import pandas as pd

from build_fem.builder_soil_spring import nearest_borehole, read_nh, transform_borehole_data
# Use __init__.py imports for design_fdn modules
from design_fdn.uls_design_hp import design_strength
from design_fdn.design_fdn_config import (
    SHEET_PILE_CAPACITY, SHEET_BP_CAPACITY, SHEET_SHP_CAPACITY, 
    SHEET_DHP_CAPACITY, SHEET_MP_CAPACITY
)

# --- Constants ---
# Material Properties
E_STEEL_GPA = 205
E_GROUT_GPA = 22.2
DEFAULT_GROUT_FCU_MPA = 30
FY_REBAR_MPA = 500

# Soil Properties
DEFAULT_SOIL_SUBGRADE_REACTION_NH_KN_M3 = 4400

# Design Factors & Coefficients
STEEL_CAPACITY_FACTOR_SHP_DHP = 0.5
ALLOWABLE_STEEL_GROUT_FBSG_MPA_WITH_STUD = 0.48
ALLOWABLE_STEEL_GROUT_FBSG_MPA_NO_STUD = 0.32
REBAR_CAPACITY_FACTOR_MP = 0.475
ALLOWABLE_STEEL_GROUT_FBSG_MPA_MP = 0.8

# Minipile specific
MIN_REBAR_SPACING_MM_MP = 20
MAX_REBAR_NUM_MP = 5
MAX_REBAR_SIZE_MM_MP = 50
MAX_CAPACITY_COP_WO_WL_KN_MP = 2350

# Rock parameters lookup tables
ROCK_PARAMS_TGN53 = {
    'Rock (1a)': {'min_socket': 0.5, 'bond_c': 1000, 'bond_tt': 1000, 'bond_pt': 500, 'bearing': 12500},
    'Rock (1b)': {'min_socket': 0.5, 'bond_c': 1000, 'bond_tt': 1000, 'bond_pt': 500, 'bearing': 10000},
    'Rock (1c)': {'min_socket': 0.3, 'bond_c': 700, 'bond_tt': 700, 'bond_pt': 350, 'bearing': 7500},
    'Rock (1d)': {'min_socket': 0.3, 'bond_c': 300, 'bond_tt': 300, 'bond_pt': 150, 'bearing': 3000},
    'Rock (2)': {'min_socket': 0.3, 'bond_c': 300, 'bond_tt': 300, 'bond_pt': 150, 'bearing': 3000}
}

ROCK_PARAMS_COP2017 = {
    'Rock (1a)': {'min_socket': 0.5, 'bond_c': 700, 'bond_tt': 700, 'bond_pt': 350, 'bearing': 10000},
    'Rock (1b)': {'min_socket': 0.5, 'bond_c': 700, 'bond_tt': 700, 'bond_pt': 350, 'bearing': 7500},
    'Rock (1c)': {'min_socket': 0.3, 'bond_c': 700, 'bond_tt': 700, 'bond_pt': 350, 'bearing': 5000},
    'Rock (1d)': {'min_socket': 0.3, 'bond_c': 300, 'bond_tt': 300, 'bond_pt': 150, 'bearing': 3000},
    'Rock (2)': {'min_socket': 0.3, 'bond_c': 300, 'bond_tt': 300, 'bond_pt': 150, 'bearing': 3000}
}

# --- Helper Functions ---

def get_rock_parameters(target_strata, use_tgn53=1):
    """Get rock parameters for multiple strata efficiently."""
    params_dict = ROCK_PARAMS_TGN53 if use_tgn53 == 1 else ROCK_PARAMS_COP2017
    
    # Create DataFrame from target_strata
    df = pd.DataFrame({'stratum': target_strata})
    
    # Map parameters
    for col, key in [('Min Rock Socket Length (m)', 'min_socket'),
                     ('Allowable Bond [Compression] (kPa)', 'bond_c'),
                     ('Allowable Bond [Transient Tension] (kPa)', 'bond_tt'),
                     ('Allowable Bond [Permanent Tension] (kPa)', 'bond_pt'),
                     ('Allowable Bearing (kPa)', 'bearing')]:
        df[col] = df['stratum'].map(lambda x: params_dict.get(x, {}).get(key, np.nan))
    
    return df.drop('stratum', axis=1)

def apply_floor_to_capacity_columns(df):
    """Apply floor function to capacity columns."""
    capacity_cols = [col for col in df.columns if 'Capacity' in col or col.startswith(('Ra [', 'Ru ['))]
    df[capacity_cols] = df[capacity_cols].apply(lambda x: x.apply(floor))
    return df

def calculate_friction_capacities(df, perimeter_col, length_col, bond_cols, result_prefix):
    """Calculate friction capacities for different conditions."""
    # Handle both column name (string) and Series
    if isinstance(perimeter_col, str):
        perimeter_values = df[perimeter_col]
    elif isinstance(perimeter_col, pd.Series):
        perimeter_values = perimeter_col.values
    else:
        perimeter_values = perimeter_col
        
    for condition, bond_col in bond_cols.items():
        df[f'{result_prefix} [{condition}] (kN)'] = (
            perimeter_values * df[length_col].values * df[bond_col].values
        )
    return df

def cal_capacity_bp(excel_inputs, use_tgn53=1, log_callback=None):
    df_pile = excel_inputs.Pile
    bp = df_pile[df_pile['Pile Type'] == 'BP'].copy()
    
    if bp.empty:
        return bp
    
    # Apply rock parameters
    rock_params = get_rock_parameters(bp['Target Stratum'].values, use_tgn53)
    bp = pd.concat([bp, rock_params], axis=1)
    
    # Vectorized calculations
    bp['Bellout Depth (m)'] = (bp['Pile Base Diameter (m)'] - bp['Pile Shaft Diameter (m)']) / 2 / tan(radians(30))
    bp['Max Effective Rock Socket Length Without Bell-Out [Compression] (m)'] = np.minimum(2 * bp['Pile Shaft Diameter (m)'], 6)
    bp['Max Effective Rock Socket Length With Bell-Out [Compression] (m)'] = np.minimum(bp['Pile Shaft Diameter (m)'], 3)

    # Effective socket length calculations
    esl_normal = np.maximum(0, bp['Socket Length (m)'] - bp['Min Rock Socket Length (m)'])
    esl_normal = np.minimum(bp['Max Effective Rock Socket Length Without Bell-Out [Compression] (m)'], esl_normal)
    
    esl_bellout = np.maximum(0, bp['Socket Length (m)'] - bp['Min Rock Socket Length (m)'] - bp['Bellout Depth (m)'])
    esl_bellout = np.minimum(bp['Max Effective Rock Socket Length With Bell-Out [Compression] (m)'], esl_bellout)
    
    # Conditional assignment
    has_bellout = bp['Pile Shaft Diameter (m)'] < bp['Pile Base Diameter (m)']
    bp['Suggested Effective Socket Length [Compression] (m)'] = np.where(has_bellout, esl_bellout, esl_normal)
    
    # Checked effective socket length
    is_straight = bp['Pile Socket Diameter (m)'] == bp['Pile Base Diameter (m)']
    bp['Checked Effective Socket Length [Compression] (m)'] = np.where(
        is_straight,
        np.minimum(bp['Effective Socket Length [Compression] (m)'], bp['Max Effective Rock Socket Length Without Bell-Out [Compression] (m)']),
        np.minimum(bp['Effective Socket Length [Compression] (m)'], bp['Max Effective Rock Socket Length With Bell-Out [Compression] (m)'])
    )
    
    # Soil/rock cone calculations
    bp['Ultimate Soil Column & Soil/Rock Cone (kN)'] = bp['Effective Weight of Soil Column and Soil/Rock Cone (kN)']
    # bp['Allowable Soil Column & Soil/Rock Cone (kN)'] = bp['Ultimate Soil Column & Soil/Rock Cone (kN)'] / 2
    
    # SWP calculation
    bp['SWP (kN)'] = (pi / 4 * bp['Pile Shaft Diameter (m)']**2 * 
                      (bp['Pile Cap Bottom Level (mPD)'] - bp['Founding Level (mPD)']) * (24.5 - 9.81))
    
    # Rock bearing
    bp['Rock Bearing (kN)'] = pi / 4 * bp['Pile Base Diameter (m)']**2 * bp['Allowable Bearing (kPa)']
    
    # Create a perimeter column for friction calculations
    bp['Rock Socket Perimeter (m)'] = pi * bp['Pile Socket Diameter (m)']
    
    # Friction calculations
    bp['Allowable Rock Friction [Compression] (kN)'] = bp['Rock Socket Perimeter (m)'] * bp['Checked Effective Socket Length [Compression] (m)'] * bp['Allowable Bond [Compression] (kPa)']

    bp['Allowable Rock Friction [Permanent Tension] (kN)'] = bp['Rock Socket Perimeter (m)'] * bp['Effective Socket Length [Tension] (m)'] * bp['Allowable Bond [Permanent Tension] (kPa)']

    bp['Allowable Rock Friction [Transient Tension] (kN)'] = bp['Rock Socket Perimeter (m)'] * bp['Effective Socket Length [Tension] (m)'] * bp['Allowable Bond [Transient Tension] (kPa)']

    # Ultimate friction
    for tension_type in ['Permanent Tension', 'Transient Tension']:
        bp[f'Ultimate Rock Friction [{tension_type}] (kN)'] = bp[f'Allowable Rock Friction [{tension_type}] (kN)'] * 2
    
    # Check for invalid socket length
    invalid_socket = bp['Checked Effective Socket Length [Compression] (m)'] <= 0
    if invalid_socket.any():
        bp.loc[invalid_socket, 'REMARK'] = 'ROCK SOCKET LENGTH IS LESS THAN MINIMUM REQUIREMENT'
    
    # Final capacities
    bp['Compression Capacity [w/o WL] (kN)'] = bp['Rock Bearing (kN)'] + bp['Allowable Rock Friction [Compression] (kN)']
    bp['Compression Capacity [w/ WL] (kN)'] = 1.25 * bp['Compression Capacity [w/o WL] (kN)']
      # Tension capacities
    for tension_type, prefix in [('Transient Tension', 'Transient'), ('Permanent Tension', 'Permanent')]:
        bp[f'Ra [{prefix}] (kN)'] = bp[f'Allowable Rock Friction [{tension_type}] (kN)']
        bp[f'Ru [{prefix}] (kN)'] = np.minimum(
            bp[f'Ultimate Rock Friction [{tension_type}] (kN)'],
            bp['Ultimate Soil Column & Soil/Rock Cone (kN)']
        )

    bp = apply_floor_to_capacity_columns(bp)
    
    if log_callback:
        log_callback('✅ Calculated comprehensive Bored Pile (BP) capacities!')
    return bp

def cal_capacity_shp(excel_inputs, use_tgn53=1, log_callback=None):
    df_pile = excel_inputs.Pile
    df_hp_prop = excel_inputs.HPProp
    df_section_h = excel_inputs.SteelSectionH
    
    shp = df_pile[df_pile['Pile Type'] == 'SHP'].copy()
    if shp.empty:
        return shp
    
    # Get steel grades efficiently - handle missing values
    hp_prop_dict = df_hp_prop.set_index('Column')['Material'].to_dict()
    steel_grades = shp['Pile Section'].map(hp_prop_dict)
    # Only split if not null
    steel_grades = steel_grades.apply(lambda x: x.split(' ')[0] if pd.notna(x) else None)
    shp['Steel Grade'] = steel_grades
    
    # Constants
    shp['Soil Subgrade Reaction nh (kN/m^3)'] = DEFAULT_SOIL_SUBGRADE_REACTION_NH_KN_M3
    shp['Sleeve Length e1 (m)'] = shp['Sleeve Length (m)']
    shp['Es (GPa)'] = E_STEEL_GPA
    shp['Egrout (GPa)'] = E_GROUT_GPA
    shp['Grout fcu (N/mm^2)'] = DEFAULT_GROUT_FCU_MPA
    
    # Allowable steel & grout based on stud condition
    shp['Allowable Steel & Grout fbsg (MPa)'] = np.where(
        shp['Rock Socket Shear Stud (Y/N)'] == 'Y',
        ALLOWABLE_STEEL_GROUT_FBSG_MPA_WITH_STUD,
        ALLOWABLE_STEEL_GROUT_FBSG_MPA_NO_STUD
    )
    
    shp['Socket Length (m)'] = shp['Target Level (mPD)'] - shp['Founding Level (mPD)']
    
    # Get rock bond strengths efficiently
    rock_params = get_rock_parameters(shp['Target Stratum'].values, use_tgn53)
    shp['Min Rock Socket Length (m)'] = rock_params['Min Rock Socket Length (m)'].values
    shp['Allowable Grout & Rock fbrg [Compression] (MPa)'] = rock_params['Allowable Bond [Compression] (kPa)'].values / 1000
    shp['Allowable Grout & Rock fbrg [Transient Tension] (MPa)'] = rock_params['Allowable Bond [Transient Tension] (kPa)'].values / 1000
    shp['Allowable Grout & Rock fbrg [Permanent Tension] (MPa)'] = rock_params['Allowable Bond [Permanent Tension] (kPa)'].values / 1000
    
    # Get steel properties from section database
    steel_props = df_section_h.set_index('Steel Section')[['Ix (cm4)', 'Iy (cm4)', 'A (cm2)', 'B (mm)', 'D (mm)', 't (mm)']].to_dict('index')
    
    for prop in ['Ix (cm4)', 'Iy (cm4)', 'Area (m2)', 'Perimeter (m)', 'fy (N/mm2)']:
        shp[prop] = np.nan
    
    # Vectorized steel property assignment
    for section in shp['Pile Section'].unique():
        mask = shp['Pile Section'] == section
        if section in steel_props:
            props = steel_props[section]
            shp.loc[mask, 'Ix (cm4)'] = props['Ix (cm4)']
            shp.loc[mask, 'Iy (cm4)'] = props['Iy (cm4)']
            shp.loc[mask, 'Area (m2)'] = props['A (cm2)'] / 10000
            
            b, d, t = props['B (mm)'], props['D (mm)'], props['t (mm)']
            shp.loc[mask, 'Perimeter (m)'] = ((b + d) * 2 + (b - t) * 2) / 1000
            
            steel_grade = shp.loc[mask, 'Steel Grade'].iloc[0] if mask.any() else None
            if steel_grade:
                shp.loc[mask, 'fy (N/mm2)'] = design_strength(excel_inputs, section, steel_grade)
    
    # Effective socket length
    shp['Suggested Effective Socket Length [Compression] (m)'] = shp['Socket Length (m)'] - shp['Min Rock Socket Length (m)']
    
    # Soil cone calculations
    shp['Ultimate Soil Column & Soil/Rock Cone (kN)'] = shp['Effective Weight of Soil Column and Soil/Rock Cone (kN)']
    # shp['Allowable Soil Column & Soil/Rock Cone (kN)'] = shp['Ultimate Soil Column & Soil/Rock Cone (kN)'] / 2
    
    # Moment of inertia calculations
    shp['ISteel (m4)'] = np.minimum(shp['Ix (cm4)'], shp['Iy (cm4)']) / 1e8
    shp['Igrout (m4)'] = (pi * shp['Pile Socket Diameter (m)']**4) / 64 - shp['ISteel (m4)']
    shp['EsIs+EgIg (kNm2)'] = (shp['Es (GPa)'] * shp['ISteel (m4)'] + shp['Egrout (GPa)'] * shp['Igrout (m4)']) * 1e6
    
    # Steel capacities
    shp['Steel Compression Capacity (kN)'] = shp['fy (N/mm2)'] * STEEL_CAPACITY_FACTOR_SHP_DHP * shp['Area (m2)'] * 1000
    shp['Steel Tension Capacity [w/o WL] (kN)'] = shp['Steel Compression Capacity (kN)']
    shp['Steel Tension Capacity [w/ WL] (kN)'] = shp['Steel Tension Capacity [w/o WL] (kN)'] * 1.25
    
    # Steel & grout friction
    shp['Allowable Steel & Grout Friction (Compression) [w/o WL] (kN)'] = (
        shp['Allowable Steel & Grout fbsg (MPa)'] * 
        shp['Effective Socket Length [Compression] (m)'] * 
        shp['Perimeter (m)'] * 1000
    )
    shp['Allowable Steel & Grout Friction (Compression) [w/ WL] (kN)'] = shp['Allowable Steel & Grout Friction (Compression) [w/o WL] (kN)'] * 1.25
    shp['Ultimate Steel & Grout Friction (Compression) [w/o WL] (kN)'] = shp['Allowable Steel & Grout Friction (Compression) [w/o WL] (kN)'] * 2
    shp['Ultimate Steel & Grout Friction (Compression) [w/ WL] (kN)'] = shp['Allowable Steel & Grout Friction (Compression) [w/ WL] (kN)'] * 2


    shp['Allowable Steel & Grout Friction [Permanent Tension] (kN)'] = (
        shp['Allowable Steel & Grout fbsg (MPa)'] * 
        shp['Effective Socket Length [Tension] (m)'] * 
        shp['Perimeter (m)'] * 1000
    )
    shp['Allowable Steel & Grout Friction [Transient Tension] (kN)'] = shp['Allowable Steel & Grout Friction [Permanent Tension] (kN)'] * 1.25
    shp['Ultimate Steel & Grout Friction [Permanent Tension] (kN)'] = shp['Allowable Steel & Grout Friction [Permanent Tension] (kN)'] * 2
    shp['Ultimate Steel & Grout Friction [Transient Tension] (kN)'] = shp['Allowable Steel & Grout Friction [Transient Tension] (kN)'] * 2

    # Rock & grout friction
    for condition, col in [('Compression', 'Allowable Grout & Rock fbrg [Compression] (MPa)'),
                          ('Transient Tension', 'Allowable Grout & Rock fbrg [Transient Tension] (MPa)'),
                          ('Permanent Tension', 'Allowable Grout & Rock fbrg [Permanent Tension] (MPa)')]:
        shp[f'Allowable Rock & Grout Friction [{condition}] (kN)'] = (
            shp[col] * pi * shp['Pile Socket Diameter (m)'] * 
            shp['Effective Socket Length [Tension] (m)'] * 1000
        )
        shp[f'Ultimate Rock & Grout Friction [{condition}] (kN)'] = (
            shp[f'Allowable Rock & Grout Friction [{condition}] (kN)'] * 2
        )
    
    # Buckling capacity
    common_term_lc = (shp['EsIs+EgIg (kNm2)'] / shp['Soil Subgrade Reaction nh (kN/m^3)']) ** 0.2
    
    # PIN condition
    pin_mask = shp['Pile Head Condition (PIN/FIX)'] == 'PIN'
    shp.loc[pin_mask, 'Lc (m)'] = 4 * common_term_lc[pin_mask]
    shp.loc[pin_mask, 'Buckling Capacity (kN)'] = (
        pi**2 * shp.loc[pin_mask, 'EsIs+EgIg (kNm2)'] /
        (4 * (shp.loc[pin_mask, 'Sleeve Length e1 (m)'] + shp.loc[pin_mask, 'Lc (m)'] / 2)**2) / 3
    )
    
    # FIX condition
    fix_mask = shp['Pile Head Condition (PIN/FIX)'] == 'FIX'
    shp.loc[fix_mask, 'Lc (m)'] = common_term_lc[fix_mask]
    shp.loc[fix_mask, 'Buckling Capacity (kN)'] = (
        pi**2 * shp.loc[fix_mask, 'EsIs+EgIg (kNm2)'] /
        ((shp.loc[fix_mask, 'Sleeve Length e1 (m)'] + shp.loc[fix_mask, 'Lc (m)'] / 2)**2) / 3
    )
    
    # SWP calculation
    shp['SWP (kN)'] = shp['Area (m2)'] * (shp['Pile Cap Bottom Level (mPD)'] - shp['Founding Level (mPD)']) * (78.5 - 9.81)
    
    # Final capacities
    shp['Compression Capacity [w/o WL] (kN)'] = np.minimum.reduce([
        shp['Steel Compression Capacity (kN)'],
        shp['Allowable Steel & Grout Friction (Compression) [w/o WL] (kN)'],
        shp['Allowable Rock & Grout Friction [Compression] (kN)']
    ])
    shp['Compression Capacity [w/ WL] (kN)'] = shp['Compression Capacity [w/o WL] (kN)'] * 1.25
    
    # Tension capacities
    shp['Ra [Transient] (kN)'] = np.minimum.reduce([
        shp['Allowable Steel & Grout Friction [Transient Tension] (kN)'],
        shp['Allowable Rock & Grout Friction [Transient Tension] (kN)'],
        # shp['Allowable Soil Column & Soil/Rock Cone (kN)'],
        shp['Steel Tension Capacity [w/ WL] (kN)']
    ])
    
    shp['Ra [Permanent] (kN)'] = np.minimum.reduce([
        shp['Allowable Steel & Grout Friction [Permanent Tension] (kN)'],
        shp['Allowable Rock & Grout Friction [Permanent Tension] (kN)'],
        # shp['Allowable Soil Column & Soil/Rock Cone (kN)'],
        shp['Steel Tension Capacity [w/o WL] (kN)']
    ])
    
    shp['Ru [Transient] (kN)'] = np.minimum.reduce([
        shp['Ultimate Steel & Grout Friction [Transient Tension] (kN)'],
        shp['Ultimate Rock & Grout Friction [Transient Tension] (kN)'],
        shp['Ultimate Soil Column & Soil/Rock Cone (kN)'],
        shp['Steel Tension Capacity [w/ WL] (kN)']
    ])
    
    shp['Ru [Permanent] (kN)'] = np.minimum.reduce([
        shp['Ultimate Steel & Grout Friction [Permanent Tension] (kN)'],
        shp['Ultimate Rock & Grout Friction [Permanent Tension] (kN)'],
        shp['Ultimate Soil Column & Soil/Rock Cone (kN)'],
        shp['Steel Tension Capacity [w/o WL] (kN)']
    ])
    
    shp = apply_floor_to_capacity_columns(shp)
    
    if log_callback:
        log_callback('✅ Calculated comprehensive Socket H-Pile (SHP) capacities!')
    return shp

def cal_capacity_dhp(excel_inputs, log_callback=None):
    df_pile = excel_inputs.Pile
    df_hp_prop = excel_inputs.HPProp
    df_section_h = excel_inputs.SteelSectionH
    df_borehole = excel_inputs.Borehole
    df_pile_soil_spring_setting = excel_inputs.PileSoilSpringSetting
    
    dhp = df_pile[df_pile['Pile Type'] == 'DHP'].copy()
    if dhp.empty:
        return dhp
    
    # Initialize Steel Grade column first
    dhp['Steel Grade'] = None
    
    # Prepare borehole data
    df_borehole_sorted = df_borehole.copy()
    df_borehole_sorted = df_borehole_sorted.sort_values(['Borehole', 'SPT Level (mPD)'], ascending=[True, False])
    df_borehole_transformed = transform_borehole_data(df_borehole_sorted)
    
    # Merge with soil spring settings
    dhp = dhp.merge(df_pile_soil_spring_setting, how='left', on='Pile Mark')
    
    # Get steel properties efficiently
    steel_props = df_section_h.set_index('Steel Section')[['A (cm2)', 'D (mm)', 'B (mm)', 't (mm)', 'r (mm)']].to_dict('index')
    hp_materials = df_hp_prop.set_index('Column')['Material'].to_dict()
    
    # Vectorized steel property calculations
    for section in dhp['Pile Section'].unique():
        mask = dhp['Pile Section'] == section
        if section in steel_props:
            props = steel_props[section]
            dhp.loc[mask, 'Area (m2)'] = props['A (cm2)'] / 10000
            dhp.loc[mask, 'D (m)'] = props['D (mm)'] / 1000
            dhp.loc[mask, 'B (m)'] = props['B (mm)'] / 1000
            dhp.loc[mask, 't (m)'] = props['t (mm)'] / 1000
            dhp.loc[mask, 'r (m)'] = props['r (mm)'] / 1000
            
            # Perimeter calculation
            perimeter_mm = 2 * props['D (mm)'] + 2 * props['B (mm)'] + 2 * (props['B (mm)'] - props['t (mm)'] - props['r (mm)'] - props['r (mm)'])
            dhp.loc[mask, 'Perimeter (m)'] = perimeter_mm / 1000
            
            # Steel grade and design strength
            if section in hp_materials:
                steel_grade = hp_materials[section]
                dhp.loc[mask, 'Steel Grade'] = steel_grade
                fy = design_strength(excel_inputs, section, steel_grade)
                dhp.loc[mask, 'fy (N/mm2)'] = fy
                
                # DHP capacity formula: 0.3 * fy * area_cm2 * 0.1
                steel_capacity = 0.3 * fy * props['A (cm2)'] * 0.1
                dhp.loc[mask, 'Steel Compression Capacity (kN)'] = steel_capacity
                dhp.loc[mask, 'Steel Tension Capacity (kN)'] = steel_capacity
    
    # Group capacities
    dhp['Group Steel Compression Capacity [w/o WL] (kN)'] = dhp['Steel Compression Capacity (kN)'] * dhp['Group Reduction Factor']
    dhp['Group Steel Compression Capacity [w/ WL] (kN)'] = dhp['Group Steel Compression Capacity [w/o WL] (kN)'] * 1.25
    dhp['Group Steel Tension Capacity [w/o WL] (kN)'] = dhp['Group Steel Compression Capacity [w/o WL] (kN)']
    dhp['Group Steel Tension Capacity [w/ WL] (kN)'] = dhp['Group Steel Tension Capacity [w/o WL] (kN)'] * 1.25
    
    # Initial capacity assignment
    dhp['Compression Capacity [w/o WL] (kN)'] = dhp['Group Steel Compression Capacity [w/o WL] (kN)']
    dhp['Compression Capacity [w/ WL] (kN)'] = dhp['Group Steel Compression Capacity [w/ WL] (kN)']
    dhp['Tension Capacity [w/o WL] (kN)'] = dhp['Group Steel Tension Capacity [w/o WL] (kN)']
    dhp['Tension Capacity [w/ WL] (kN)'] = dhp['Group Steel Tension Capacity [w/ WL] (kN)']
    
    # Constant shaft friction
    dhp['Allowable Shaft Friction (kPa)'] = 10
    dhp['Allowable Shaft Friction (kN/m)'] = dhp['Perimeter (m)'] * 10
    
    # Process borehole data for each pile
    borehole_results = []
    for idx, row in dhp.iterrows():
        x, y = row['X (m)'], row['Y (m)']
        cap_bot_lv = row['Pile Cap Bottom Level (mPD)']
        ground_lv = row['Ground Level (mPD)']
        founding_lv = row['Founding Level (mPD)']
        sleeve_length = row['Sleeve Length (m)']
        
        sleeve_lv = cap_bot_lv - sleeve_length
        zero_lv = min(float(sleeve_lv), float(ground_lv))
        
        borehole = row['Borehole']
        if pd.isna(borehole) or borehole == ' ':
            borehole = nearest_borehole(x, y, df_borehole_transformed)
        
        list_seg, list_z_center, list_dist_gl, list_dz, list_soil_type, list_spt_n, list_nh = read_nh(
            borehole, df_borehole_transformed, ground_lv, zero_lv, cap_bot_lv, founding_lv
        )
        
        # Calculate SPTN>=10 length
        dz_sum_sptn_ge10 = sum(dz for dz, sptn in zip(list_dz, list_spt_n) if sptn >= 10)
        processed_dz = [dz if sptn >= 10 else 0 for dz, sptn in zip(list_dz, list_spt_n)]
        
        borehole_results.append({
            'idx': idx,
            'Borehole': borehole,
            'Segment_Z (mPD)': str(list_seg),
            'dZ (m)': str(processed_dz),
            'SPTN Value': str(list_spt_n),
            'SPTN>=10 Length (m)': dz_sum_sptn_ge10
        })
    
    # Apply borehole results
    borehole_df = pd.DataFrame(borehole_results).set_index('idx')
    for col in borehole_df.columns:
        dhp[col] = borehole_df[col]
    
    # Shaft friction calculations
    dhp['Allowable Shaft Friction [Transient] (kN)'] = dhp['Allowable Shaft Friction (kN/m)'] * dhp['SPTN>=10 Length (m)']
    dhp['Allowable Shaft Friction [Permanent] (kN)'] = dhp['Allowable Shaft Friction [Transient] (kN)']
    dhp['Ultimate Shaft Friction [Transient] (kN)'] = dhp['Allowable Shaft Friction [Transient] (kN)'] * 2
    dhp['Ultimate Shaft Friction [Permanent] (kN)'] = dhp['Ultimate Shaft Friction [Transient] (kN)']
    
    # Soil cone calculations
    dhp['Ultimate Soil Column & Soil/Rock Cone (kN)'] = dhp['Effective Weight of Soil Column and Soil/Rock Cone (kN)']
    # dhp['Allowable Soil Column & Soil/Rock Cone (kN)'] = dhp['Ultimate Soil Column & Soil/Rock Cone (kN)'] / 2
    
    # Final tension capacities
    dhp['Ra [Transient] (kN)'] = np.minimum.reduce([
        dhp['Allowable Shaft Friction [Transient] (kN)'],
        # dhp['Allowable Soil Column & Soil/Rock Cone (kN)'],
        dhp['Tension Capacity [w/ WL] (kN)']
    ])
    
    dhp['Ra [Permanent] (kN)'] = np.minimum.reduce([
        dhp['Allowable Shaft Friction [Permanent] (kN)'],
        # dhp['Allowable Soil Column & Soil/Rock Cone (kN)'],
        dhp['Tension Capacity [w/o WL] (kN)']
    ])
    
    dhp['Ru [Transient] (kN)'] = np.minimum.reduce([
        dhp['Ultimate Shaft Friction [Transient] (kN)'],
        dhp['Ultimate Soil Column & Soil/Rock Cone (kN)'],
        dhp['Tension Capacity [w/ WL] (kN)']
    ])
    
    dhp['Ru [Permanent] (kN)'] = np.minimum.reduce([
        dhp['Ultimate Shaft Friction [Permanent] (kN)'],
        dhp['Ultimate Soil Column & Soil/Rock Cone (kN)'],
        dhp['Tension Capacity [w/o WL] (kN)']
    ])
    
    dhp = apply_floor_to_capacity_columns(dhp)
    
    if log_callback:
        log_callback('✅ Calculated comprehensive Driven H-Pile (DHP) capacities!')
    return dhp

def cal_capacity_mp(excel_inputs, use_tgn53=1, log_callback=None):
    df_pile = excel_inputs.Pile
    mp = df_pile[df_pile['Pile Type'] == 'MP'].copy()
    
    if mp.empty:
        return mp
    
    # Parse pile section efficiently - handle potential parsing errors
    pile_section_split = mp['Pile Section'].str.split('T', n=1, expand=True)
    mp['Number of Rebar'] = pd.to_numeric(pile_section_split[0], errors='coerce').fillna(0).clip(upper=MAX_REBAR_NUM_MP)
    mp['Rebar Size (mm)'] = pd.to_numeric(pile_section_split[1], errors='coerce').fillna(0).clip(upper=MAX_REBAR_SIZE_MM_MP)
    
    # Rebar calculations
    mp['Rebar Area (mm2)'] = pi / 4 * mp['Rebar Size (mm)']**2
    mp['Area (mm2)'] = mp['Number of Rebar'] * mp['Rebar Area (mm2)']
    mp['fy (N/mm2)'] = FY_REBAR_MPA
    mp['0.475fy (N/mm2)'] = REBAR_CAPACITY_FACTOR_MP * FY_REBAR_MPA
    mp['Rebar Capacity [w/o WL] (kN)'] = mp['0.475fy (N/mm2)'] * mp['Area (mm2)'] / 1000
    mp['Rebar Capacity [w/ WL] (kN)'] = mp['Rebar Capacity [w/o WL] (kN)'] * 1.25
    
    # Shear plane perimeter calculations
    mp['Shear Plane Perimeter (mm)'] = 0
    
    # 4 rebars
    mask_4 = mp['Number of Rebar'] == 4
    mp.loc[mask_4, 'Shear Plane Perimeter (mm)'] = (
        4 * pi / 4 * mp.loc[mask_4, 'Rebar Size (mm)'] + 
        (mp.loc[mask_4, 'Rebar Size (mm)'] + MIN_REBAR_SPACING_MM_MP) * 4
    )
    
    # 5 rebars
    mask_5 = mp['Number of Rebar'] == 5
    if mask_5.any():
        radius_shear_plane = (
            (mp.loc[mask_5, 'Rebar Size (mm)'] + MIN_REBAR_SPACING_MM_MP) / 2 / 
            np.sin(np.radians(36)) +  # 360/5/2 = 36
            mp.loc[mask_5, 'Rebar Size (mm)'] / 2
        )
        mp.loc[mask_5, 'Shear Plane Perimeter (mm)'] = 2 * pi * (
            mp.loc[mask_5, 'Rebar Size (mm)'] / 2 + radius_shear_plane
        )
    
    # Apply rock parameters
    rock_params = get_rock_parameters(mp['Target Stratum'].values, use_tgn53)
    mp = pd.concat([mp, rock_params], axis=1)
    
    # Socket calculations
    mp['Effective Rock Socket Length (m)'] = mp['Socket Length (m)'] - mp['Min Rock Socket Length (m)']
    mp['Allowable Steel & Grout fbsg (MPa)'] = ALLOWABLE_STEEL_GROUT_FBSG_MPA_MP
    
    # Rebar & grout friction
    mp['Allowable Rebar & Grout Friction (kN)'] = (
        mp['Allowable Steel & Grout fbsg (MPa)'] * 
        mp['Shear Plane Perimeter (mm)'] / 1000 * 
        mp['Effective Rock Socket Length (m)'] * 1000
    )
    mp['Ultimate Rebar & Grout Friction (kN)'] = mp['Allowable Rebar & Grout Friction (kN)'] * 2
    
    # Grout & rock friction
    mp['Effective Pile Socket Diameter (m)'] = mp['Pile Socket Diameter (m)'].clip(upper=0.45)
    mp['Pile Rock Socket Perimeter (m)'] = pi * mp['Effective Pile Socket Diameter (m)']
    
    for condition in ['Compression', 'Transient Tension', 'Permanent Tension']:
        mp[f'Allowable Grout & Rock Friction [{condition}] (kN)'] = (
            mp['Pile Rock Socket Perimeter (m)'] * 
            mp['Effective Rock Socket Length (m)'] * 
            mp[f'Allowable Bond [{condition}] (kPa)']
        )
        if condition != 'Compression':
            mp[f'Ultimate Grout & Rock Friction [{condition}] (kN)'] = (
                mp[f'Allowable Grout & Rock Friction [{condition}] (kN)'] * 2
            )
    
    # Max capacity limits
    mp['Max Capacity in CoP [w/o WL] (kN)'] = MAX_CAPACITY_COP_WO_WL_KN_MP
    mp['Max Capacity in CoP [w/ WL] (kN)'] = MAX_CAPACITY_COP_WO_WL_KN_MP * 1.25
    
    # Final capacities
    mp['Compression Capacity [w/o WL] (kN)'] = np.minimum.reduce([
        mp['Rebar Capacity [w/o WL] (kN)'],
        mp['Allowable Rebar & Grout Friction (kN)'],
        mp['Allowable Grout & Rock Friction [Compression] (kN)'],
        mp['Max Capacity in CoP [w/o WL] (kN)']
    ])
    mp['Compression Capacity [w/ WL] (kN)'] = mp['Compression Capacity [w/o WL] (kN)'] * 1.25
    
    # Tension capacities
    for tension_type, wl_suffix, cap_suffix in [
        ('Permanent Tension', 'w/o WL', 'Permanent'),
        ('Transient Tension', 'w/ WL', 'Transient')
    ]:
        mp[f'Ra [{cap_suffix}] (kN)'] = np.minimum.reduce([
            mp[f'Rebar Capacity [{wl_suffix}] (kN)'],
            mp['Allowable Rebar & Grout Friction (kN)'],
            mp[f'Allowable Grout & Rock Friction [{tension_type}] (kN)'],
            mp[f'Max Capacity in CoP [{wl_suffix}] (kN)']
                ])
        
        mp[f'Ru [{cap_suffix}] (kN)'] = np.minimum.reduce([
            mp[f'Rebar Capacity [{wl_suffix}] (kN)'],
            mp['Ultimate Rebar & Grout Friction (kN)'],
            mp[f'Ultimate Grout & Rock Friction [{tension_type}] (kN)'],
            mp[f'Max Capacity in CoP [{wl_suffix}] (kN)']
        ])
    
    mp = apply_floor_to_capacity_columns(mp)
    
    if log_callback:
        log_callback('✅ Calculated comprehensive Mini Pile (MP) capacities!')
    return mp

def cal_capacity_piles(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, use_tgn53=1, log_callback=None):
    """Calculate pile capacities for all pile types and export to Excel."""
    path_excel_output_pile_capacity = file_paths.ResultPileCapacity
    df_pile = excel_inputs.Pile
    
    # Check if Pile dataframe exists and has data
    if df_pile is None or df_pile.empty:
        if log_callback:
            log_callback('No pile data found!')
        return design_results
    
    # Calculate capacities for each pile type
    pile_types = {
        'BP': cal_capacity_bp,
        'SHP': cal_capacity_shp,
        'DHP': cal_capacity_dhp,
        'MP': cal_capacity_mp
    }
    
    results = {}
    for pile_type, calc_func in pile_types.items():
        if df_pile['Pile Type'].str.contains(pile_type).any():
            if pile_type in ['BP', 'SHP', 'MP']:
                results[pile_type] = calc_func(excel_inputs, use_tgn53, log_callback)
            else:  # DHP doesn't use use_tgn53
                results[pile_type] = calc_func(excel_inputs, log_callback)
    
    # Combine all results
    pile_capacity = pd.concat(results.values(), ignore_index=True) if results else pd.DataFrame()
    
    # Merge with original pile data - ensure capacity columns exist
    if not pile_capacity.empty:
        capacity_cols = [
            'Pile Mark', 'Compression Capacity [w/o WL] (kN)', 'Compression Capacity [w/ WL] (kN)',
            'Ra [Transient] (kN)', 'Ra [Permanent] (kN)', 'Ru [Transient] (kN)', 'Ru [Permanent] (kN)'
        ]
        
        # Filter only existing columns
        existing_capacity_cols = [col for col in capacity_cols if col in pile_capacity.columns]
        
        df_pile_capacity = df_pile.merge(
            pile_capacity[existing_capacity_cols], 
            how='left', 
            on='Pile Mark'
        )
    else:
        df_pile_capacity = df_pile.copy()
      # Export to Excel
    with pd.ExcelWriter(path_excel_output_pile_capacity) as writer:
        df_pile_capacity.to_excel(writer, sheet_name=SHEET_PILE_CAPACITY, index=False)
        
        # Export individual pile type sheets with selected columns
        sheet_columns = {
            'BP': ['Pile Mark', 'Concrete Grade', 'Pile Head Condition (PIN/FIX)', 'Sleeve Length (m)',
                   'Pile Shaft Diameter (m)', 'Pile Socket Diameter (m)', 'Pile Base Diameter (m)',
                   'Pile Cap Bottom Level (mPD)', 'Ground Level (mPD)', 'Target Stratum',
                   'Target Level (mPD)', 'Socket Length (m)', 
                   'Effective Socket Length [Compression] (m)', 'Effective Socket Length [Tension] (m)',
                   'Founding Level (mPD)', 'Effective Weight of Soil Column and Soil/Rock Cone (kN)', 
                   'Min Rock Socket Length (m)', 'Allowable Bond [Compression] (kPa)', 
                   'Allowable Bond [Transient Tension] (kPa)', 'Allowable Bond [Permanent Tension] (kPa)', 
                   'Allowable Bearing (kPa)', 'Bellout Depth (m)', 
                   'Max Effective Rock Socket Length Without Bell-Out [Compression] (m)',
                   'Max Effective Rock Socket Length With Bell-Out [Compression] (m)',
                   'Suggested Effective Socket Length [Compression] (m)', 'Checked Effective Socket Length [Compression] (m)',
                   'Ultimate Soil Column & Soil/Rock Cone (kN)',
                   'SWP (kN)', 'Rock Bearing (kN)', 'Allowable Rock Friction [Compression] (kN)',
                   'Allowable Rock Friction [Permanent Tension] (kN)',
                   'Allowable Rock Friction [Transient Tension] (kN)',
                   'Ultimate Rock Friction [Permanent Tension] (kN)',
                   'Ultimate Rock Friction [Transient Tension] (kN)', 
                   'Compression Capacity [w/o WL] (kN)', 'Compression Capacity [w/ WL] (kN)', 
                   'Ra [Permanent] (kN)', 'Ra [Transient] (kN)', 'Ru [Permanent] (kN)', 
                   'Ru [Transient] (kN)', 'REMARK'],
            'SHP': ['Pile Mark', 'Pile Type', 'Pile Section', 'Steel Grade',
                    'Pile Head Condition (PIN/FIX)', 'Rock Socket Shear Stud (Y/N)',
                    'Pile Shaft Diameter (m)', 'Pile Socket Diameter (m)', 'Pile Base Diameter (m)',
                    'Pile Cap Bottom Level (mPD)', 'Ground Level (mPD)', 'Target Stratum',
                    'Target Level (mPD)', 'Socket Length (m)', 
                    'Effective Socket Length [Compression] (m)', 'Effective Socket Length [Tension] (m)',
                    'Founding Level (mPD)', 'Effective Weight of Soil Column and Soil/Rock Cone (kN)',
                    'Soil Subgrade Reaction nh (kN/m^3)', 'Sleeve Length e1 (m)', 'Ix (cm4)', 
                    'Iy (cm4)', 'Area (m2)', 'Perimeter (m)', 'Es (GPa)', 'Egrout (GPa)', 
                    'ISteel (m4)', 'Igrout (m4)', 'EsIs+EgIg (kNm2)', 'Lc (m)', 
                    'Buckling Capacity (kN)', 'Grout fcu (N/mm^2)', 'Allowable Steel & Grout fbsg (MPa)',
                    'Allowable Grout & Rock fbrg [Compression] (MPa)',
                    'Allowable Grout & Rock fbrg [Transient Tension] (MPa)',
                    'Allowable Grout & Rock fbrg [Permanent Tension] (MPa)', 
                    'Min Rock Socket Length (m)', 'Suggested Effective Socket Length [Compression] (m)', 
                    'fy (N/mm2)', 'Ultimate Soil Column & Soil/Rock Cone (kN)',
                    'Steel Compression Capacity (kN)',
                    'Steel Tension Capacity [w/o WL] (kN)', 'Steel Tension Capacity [w/ WL] (kN)',
                    'Allowable Steel & Grout Friction [w/o WL] (kN)',
                    'Allowable Steel & Grout Friction [w/ WL] (kN)',
                    'Ultimate Steel & Grout Friction [w/o WL] (kN)',
                    'Ultimate Steel & Grout Friction [w/ WL] (kN)',
                    'Allowable Rock & Grout Friction [Compression] (kN)',
                    'Allowable Rock & Grout Friction [Transient Tension] (kN)',
                    'Allowable Rock & Grout Friction [Permanent Tension] (kN)',
                    'Ultimate Rock & Grout Friction [Compression] (kN)',
                    'Ultimate Rock & Grout Friction [Transient Tension] (kN)',
                    'Ultimate Rock & Grout Friction [Permanent Tension] (kN)',
                    'Compression Capacity [w/o WL] (kN)', 'Compression Capacity [w/ WL] (kN)',
                    'Ra [Permanent] (kN)', 'Ra [Transient] (kN)', 'Ru [Permanent] (kN)', 
                    'Ru [Transient] (kN)'],
            'DHP': ['Pile Mark', 'Steel Section', 'Steel Grade', 'Pile Cap Bottom Level (mPD)',
                    'Ground Level (mPD)', 'Target Stratum', 'Founding Level (mPD)',
                    'Effective Weight of Soil Column and Soil/Rock Cone (kN)', 'fy (N/mm2)', 
                    'D (m)', 'B (m)', 't (m)', 'r (m)', 'Perimeter (m)', 'Area (m2)',
                    'Steel Compression Capacity (kN)', 'Steel Tension Capacity (kN)',
                    'Group Reduction Factor', 'Compression Capacity [w/o WL] (kN)',
                    'Compression Capacity [w/ WL] (kN)', 'Tension Capacity [w/o WL] (kN)',
                    'Tension Capacity [w/ WL] (kN)', 'Allowable Shaft Friction (kPa)',
                    'Allowable Shaft Friction (kN/m)', 'Borehole', 'Segment_Z (mPD)', 
                    'SPTN Value', 'dZ (m)', 'SPTN>=10 Length (m)',
                    'Allowable Shaft Friction [Transient] (kN)',
                    'Allowable Shaft Friction [Permanent] (kN)',
                    'Ultimate Shaft Friction [Transient] (kN)',
                    'Ultimate Shaft Friction [Permanent] (kN)',
                    'Ultimate Soil Column & Soil/Rock Cone (kN)',
                    'Ra [Permanent] (kN)', 'Ra [Transient] (kN)',
                    'Ru [Permanent] (kN)', 'Ru [Transient] (kN)']        }
        
        # Map pile types to sheet name constants
        sheet_name_mapping = {
            'BP': SHEET_BP_CAPACITY,
            'SHP': SHEET_SHP_CAPACITY,
            'DHP': SHEET_DHP_CAPACITY,
            'MP': SHEET_MP_CAPACITY
        }
        
        for pile_type, df in results.items():
            sheet_name = sheet_name_mapping.get(pile_type, pile_type)
            if pile_type in sheet_columns:
                cols = [col for col in sheet_columns[pile_type] if col in df.columns]
                df[cols].to_excel(writer, sheet_name=sheet_name, index=False)
            else:  # MP or any other type not in sheet_columns
                df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    design_results.PileCapacity = df_pile_capacity
    
    if log_callback:
        log_callback('✅ Exported comprehensive pile capacity analysis to Excel!')
    
    return design_results
