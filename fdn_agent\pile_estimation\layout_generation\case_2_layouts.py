﻿"""
Case 2 Pile Layouts: Single Wall + Single Sub-Load Cluster

Rules (Updated according to Pile_Layout_Rules.md):
- All layouts follow pile center = load center
- DHP & SHP: upgrade pile number to bigger even number
- Place piles symmetrically along wall line, closer to wall preferred
- Multiple rows if needed with perpendicular spacing
- **BP Special Rule**: 1 pile at load center OR 2 piles at wall start/end points
- **BP Wall Length Rule**: If wall length ≤ 3 pile diameter, max 1 pile, else 2
- Grid shifting by maximum 1 min spacing for optimization

Enhanced Features:
- Grid shifting optimization for maximum pile count
- Even number upgrading for DHP/SHP types
- Enhanced wall geometry processing
- Better BP constraint enforcement
- Improved multiple row layouts
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
from math import sqrt, ceil, pi, cos, sin, atan2
import numpy as np

from ..data_types import Point2D
from ..pile_cap_geometry.pile_cap_geometry import LocalCoordinateSystem
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    create_timed_logger,
    log_algorithm_step,
    log_calculation_result,
    log_constraint_check
)
from .layout_common import (
    calculate_load_center,
    find_primary_wall,
    extract_wall_geometry,
    calculate_perpendicular_direction,
    generate_line_positions,
    calculate_grid_dimensions
)


def handle_case_2(cluster_data: dict, required_piles: int, min_spacing: float,
                  initial_local_system: Optional[LocalCoordinateSystem],
                  pile_type_name: Optional[str] = None,
                  log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Main coordinator for Case 2 pile layouts.
    Handles pile placement for a single wall and single sub-load cluster.
    
    Implements Rules 2.4 from Pile_Layout_Rules.md:
    - DHP & SHP: upgrade pile number to bigger even number
    - Place piles symmetrically along wall line, closer to wall preferred
    - Multiple rows if needed with perpendicular spacing
    - BP Special Rule: 1 pile at load center OR 2 piles at wall start/end points

    Args:
        cluster_data (dict): Dictionary containing information about the load cluster,
                             including load points and wall elements.
        required_piles (int): The number of piles required for the foundation.
        min_spacing (float): The minimum allowable spacing between piles.
        initial_local_system (Optional[LocalCoordinateSystem]): An optional local
                                                                coordinate system
                                                                (currently not used in Case 2).
        pile_type_name (Optional[str]): Pile type name for applying specific rules.

    Returns:
        List[Point2D]: A list of 2D points representing the calculated pile positions.
    """
    log_function_entry(log_callback, "handle_case_2",
                      required_piles=required_piles, min_spacing=min_spacing, pile_type_name=pile_type_name)

    try:
        # Create performance timer for layout generation
        with create_timed_logger(log_callback, f"Case_2_layout_generation_{required_piles}_piles_{pile_type_name or 'unknown'}"):
            # Calculate load center using common function
            enhanced_log(log_callback, "Calculating load center for Case 2 layout", 'INFO')
            load_center = calculate_load_center(cluster_data)
            enhanced_log(log_callback, f"Load center calculated: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')

            # Get primary wall using common function
            enhanced_log(log_callback, "Finding primary wall for Case 2 layout", 'DEBUG')
            elements = cluster_data.get('elements', {})
            walls = elements.get('walls', [])
            enhanced_log(log_callback, f"Found {len(walls)} wall elements", 'DEBUG')

            primary_wall = find_primary_wall(walls)

            if not primary_wall:
                enhanced_log(log_callback, "No valid wall found, using single pile at load center", 'WARNING')
                result = [load_center]
                enhanced_log(log_callback, f"Single pile positioned at: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
                log_function_exit(log_callback, "handle_case_2", "1 position (no wall)")
                return result

            enhanced_log(log_callback, f"Primary wall identified and validated", 'DEBUG')

            # Apply Rule 2.4: DHP & SHP upgrade pile number to bigger even number
            actual_piles = required_piles
            if pile_type_name in ['DHP', 'SHP']:
                actual_piles = _upgrade_to_even_number(required_piles, log_callback)
                enhanced_log(log_callback, f"Upgraded pile count for {pile_type_name}: {required_piles} -> {actual_piles}", 'INFO')
                log_constraint_check(log_callback, "even_number_upgrade", actual_piles, f"even (was {required_piles})", True)
            else:
                enhanced_log(log_callback, f"Using original pile count for {pile_type_name}: {actual_piles}", 'DEBUG')

            # Generate layout
            if actual_piles == 1:
                enhanced_log(log_callback, "Using single pile layout at load center", 'INFO')
                result = [load_center]
                enhanced_log(log_callback, f"Single pile positioned at: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
            else:
                enhanced_log(log_callback, f"Generating multiple pile layout for {actual_piles} piles along wall", 'INFO')
                result = _multiple_piles_along_wall(load_center, actual_piles, primary_wall, min_spacing, log_callback)

            # Validate result
            if result:
                enhanced_log(log_callback, f"Case 2 layout generated successfully: {len(result)} pile positions", 'INFO')
                
                # Log first few positions for debugging
                for i, pos in enumerate(result[:5]):
                    enhanced_log(log_callback, f"  Position {i+1}: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
                if len(result) > 5:
                    enhanced_log(log_callback, f"  ... and {len(result)-5} more positions", 'DEBUG')
                
                log_validation_result(log_callback, "pile_count_match", len(result) <= actual_piles,
                                    f"Generated {len(result)} positions for {actual_piles} requested")
                
                # Validate spacing between piles
                spacing_violations = 0
                for i in range(len(result)):
                    for j in range(i+1, len(result)):
                        distance = sqrt((result[i][0] - result[j][0])**2 + (result[i][1] - result[j][1])**2)
                        if distance < min_spacing - 0.001:  # Small tolerance for floating point
                            spacing_violations += 1
                            enhanced_log(log_callback, f"Spacing violation between piles {i+1} and {j+1}: {distance:.3f}m < {min_spacing:.3f}m", 'WARNING')
                
                log_validation_result(log_callback, "case_2_spacing_check", spacing_violations == 0,
                                    f"Found {spacing_violations} spacing violations")
            else:
                enhanced_log(log_callback, "Case 2 layout generation failed: no positions generated", 'ERROR')

            log_function_exit(log_callback, "handle_case_2", f"{len(result)} positions")
            return result

    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 2 layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_2", "error")
        raise


def handle_case_2_with_preselection(cluster_data: dict, 
                                  preselection_result: Any,
                                  min_spacing: float,
                                  initial_local_system: Optional[LocalCoordinateSystem],
                                  log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Handle Case 2 pile layout using pre-selected pile type and positions.
    This function uses the deterministic pile positions from pre-selection 
    to ensure consistency between pre-selection and final layout.
    
    Enhanced to better handle DHP/SHP even number upgrading and BP wall constraints.

    Args:
        cluster_data (dict): Dictionary containing information about the load cluster.
        preselection_result: Pre-selection result containing selected pile type and positions.
        min_spacing (float): The minimum allowable spacing between piles (from selected pile type).
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: A list of 2D points representing the final pile positions.
    """
    log_function_entry(log_callback, "handle_case_2_with_preselection", 
                      min_spacing=min_spacing, has_preselection=preselection_result is not None)
    
    try:
        # Check if preselection result has viable positions
        if hasattr(preselection_result, 'viable_grid_positions') and preselection_result.viable_grid_positions:
            # Use the pre-selected pile positions directly for consistency
            enhanced_log(log_callback, f"Using pre-selected viable positions: {len(preselection_result.viable_grid_positions)} piles", 'INFO')
            result = preselection_result.viable_grid_positions
            log_function_exit(log_callback, "handle_case_2_with_preselection", f"{len(result)} pre-selected positions")
            return result
            
        elif hasattr(preselection_result, 'selected_pile_type'):
            # If no viable positions but we have a selected pile type, generate layout
            pile_type = preselection_result.selected_pile_type
            enhanced_log(log_callback, f"No viable positions found, generating layout for selected pile type", 'INFO')
            
            # Special handling for BP types - apply wall length rule
            if hasattr(pile_type, 'pile_type') and pile_type.pile_type.name == 'BP':
                enhanced_log(log_callback, "BP type detected - applying wall length constraints", 'INFO')
                result = _handle_case_2_bp_layout(cluster_data, pile_type, min_spacing, log_callback)
                log_function_exit(log_callback, "handle_case_2_with_preselection", f"{len(result)} BP positions")
                return result
            
            # For DHP/SHP, calculate required piles and apply even number upgrading
            if hasattr(pile_type, 'capacity_per_pile'):
                total_load = sum(point[2] for point in cluster_data.get('load_points', []) if len(point) >= 3)
                enhanced_log(log_callback, f"Calculating required piles for total load: {total_load:.1f} kN", 'DEBUG')
                if total_load > 0:
                    required_piles = max(1, int(ceil(total_load / pile_type.capacity_per_pile)))
                    enhanced_log(log_callback, f"Initial calculation: {required_piles} piles needed", 'DEBUG')
                    
                    # Apply even number upgrading for DHP & SHP
                    pile_type_name = getattr(pile_type.pile_type, 'name', '') if hasattr(pile_type, 'pile_type') else ''
                    if pile_type_name in ['DHP', 'SHP']:
                        upgraded_piles = _upgrade_to_even_number(required_piles, log_callback)
                        enhanced_log(log_callback, f"Applied even number upgrade for {pile_type_name}: {required_piles} -> {upgraded_piles}", 'INFO')
                        required_piles = upgraded_piles
                    
                    result = handle_case_2(cluster_data, required_piles, min_spacing, initial_local_system, pile_type_name, log_callback)
                    log_function_exit(log_callback, "handle_case_2_with_preselection", f"{len(result)} calculated positions")
                    return result
        
        # No preselection data available
        enhanced_log(log_callback, "No preselection data available - using default layout with 2 piles", 'WARNING')
        required_piles = 2
        result = handle_case_2(cluster_data, required_piles, min_spacing, initial_local_system, None, log_callback)
        log_function_exit(log_callback, "handle_case_2_with_preselection", f"{len(result)} default positions")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 2 preselection layout handling: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_2_with_preselection", "error")
        raise


def handle_case_2_bp_layout(cluster_data: dict, 
                           pile_diameter: float,
                           min_spacing: float,
                           initial_local_system: Optional[LocalCoordinateSystem],
                           log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Special Case 2 layout handler for BP (Bored Pile) types.
    
    Implements BP Special Rules:
    - If wall length ≤ 3 pile diameter, max 1 pile at load center
    - If wall length > 3 pile diameter, 2 piles at wall start/end points
    
    Args:
        cluster_data (dict): Dictionary containing information about the load cluster.
        pile_diameter (float): Diameter of the BP pile type.
        min_spacing (float): Minimum spacing between piles.
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        log_callback: Optional logging callback function.
        
    Returns:
        List[Point2D]: BP pile positions according to wall length rule.
    """
    log_function_entry(log_callback, "handle_case_2_bp_layout", pile_diameter=pile_diameter)
    
    try:
        enhanced_log(log_callback, "Applying BP special rules for Case 2 layout", 'INFO')
        result = _handle_case_2_bp_layout(cluster_data, {'diameter': pile_diameter}, min_spacing, log_callback)
        log_function_exit(log_callback, "handle_case_2_bp_layout", f"{len(result)} positions")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in BP Case 2 layout: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_2_bp_layout", "error")
        raise


def _handle_case_2_bp_layout(cluster_data: dict, pile_type: Any, min_spacing: float,
                           log_callback: Optional[Callable] = None) -> List[Point2D]:
    """Internal BP layout handler for Case 2."""
    log_function_entry(log_callback, "_handle_case_2_bp_layout", min_spacing=min_spacing)
    
    try:
        enhanced_log(log_callback, "Processing BP layout with wall length constraints", 'INFO')
        load_center = calculate_load_center(cluster_data)
        enhanced_log(log_callback, f"Load center: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
        
        # Get wall geometry
        enhanced_log(log_callback, "Extracting wall geometry for BP constraint evaluation", 'DEBUG')
        elements = cluster_data.get('elements', {})
        walls = elements.get('walls', [])
        primary_wall = find_primary_wall(walls)
        
        if not primary_wall:
            enhanced_log(log_callback, "No primary wall found - using single pile at load center", 'WARNING')
            result = [load_center]
            log_function_exit(log_callback, "_handle_case_2_bp_layout", "1 pile (no wall)")
            return result
        
        wall_geometry = extract_wall_geometry(primary_wall)
        if not wall_geometry:
            enhanced_log(log_callback, "No valid wall geometry - using single pile at load center", 'WARNING')
            result = [load_center]
            log_function_exit(log_callback, "_handle_case_2_bp_layout", "1 pile (no geometry)")
            return result
        
        start_point, end_point, wall_dir, wall_length = wall_geometry
        enhanced_log(log_callback, f"Wall geometry - Length: {wall_length:.3f}m", 'DEBUG')
        enhanced_log(log_callback, f"Wall start: ({start_point[0]:.3f}, {start_point[1]:.3f})", 'DEBUG')
        enhanced_log(log_callback, f"Wall end: ({end_point[0]:.3f}, {end_point[1]:.3f})", 'DEBUG')
        
        # Get pile diameter
        pile_diameter = getattr(pile_type, 'diameter', None) or pile_type.get('diameter', 0.6)
        enhanced_log(log_callback, f"BP pile diameter: {pile_diameter:.3f}m", 'DEBUG')
        
        # Apply BP wall length rule
        threshold_length = 3 * pile_diameter
        enhanced_log(log_callback, f"BP wall length threshold: {threshold_length:.3f}m (3 × {pile_diameter:.3f}m)", 'DEBUG')
        
        log_constraint_check(log_callback, "BP_wall_length_rule", wall_length, threshold_length, wall_length > threshold_length)
        
        if wall_length <= threshold_length:
            # Wall too short: 1 pile at load center
            enhanced_log(log_callback, f"Wall length {wall_length:.3f}m ≤ {threshold_length:.3f}m - using 1 pile at load center", 'INFO')
            result = [load_center]
            log_algorithm_step(log_callback, "BP_Case_2", "single_pile_short_wall", f"Wall length {wall_length:.3f}m ≤ threshold")
        else:
            # Wall long enough: 2 piles at wall start/end points
            enhanced_log(log_callback, f"Wall length {wall_length:.3f}m > {threshold_length:.3f}m - using 2 piles at wall endpoints", 'INFO')
            result = [start_point, end_point]
            log_algorithm_step(log_callback, "BP_Case_2", "two_piles_long_wall", f"Wall length {wall_length:.3f}m > threshold")
            
            # Log the spacing between the two piles
            pile_spacing = sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)
            enhanced_log(log_callback, f"Spacing between BP piles: {pile_spacing:.3f}m", 'DEBUG')
            log_validation_result(log_callback, "BP_pile_spacing", pile_spacing >= min_spacing,
                                f"BP pile spacing: {pile_spacing:.3f}m vs min: {min_spacing:.3f}m")
        
        enhanced_log(log_callback, f"BP layout generated: {len(result)} pile positions", 'INFO')
        log_function_exit(log_callback, "_handle_case_2_bp_layout", f"{len(result)} positions")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in internal BP layout handling: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_handle_case_2_bp_layout", "error")
        raise


def _upgrade_to_even_number(pile_count: int, log_callback: Optional[Callable] = None) -> int:
    """
    Upgrade pile number to bigger even number for DHP & SHP.
    
    Implements Rule 2.4: DHP & SHP upgrade pile number to bigger even number.
    
    Args:
        pile_count (int): Original pile count.
        log_callback: Optional logging callback function.
        
    Returns:
        int: Upgraded even pile count.
    """
    log_function_entry(log_callback, "_upgrade_to_even_number", pile_count=pile_count)
    
    try:
        if pile_count <= 1:
            result = 2  # Minimum even number
            enhanced_log(log_callback, f"Upgraded from {pile_count} to {result} (minimum even number)", 'DEBUG')
        elif pile_count % 2 == 0:
            result = pile_count  # Already even
            enhanced_log(log_callback, f"Pile count {pile_count} already even - no upgrade needed", 'DEBUG')
        else:
            result = pile_count + 1  # Make it even by adding 1
            enhanced_log(log_callback, f"Upgraded odd number {pile_count} to even number {result}", 'DEBUG')
        
        log_calculation_result(log_callback, "even_number_upgrade", result, "piles")
        log_function_exit(log_callback, "_upgrade_to_even_number", result)
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in even number upgrade: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_upgrade_to_even_number", "error")
        raise


def _multiple_piles_along_wall(load_center: Point2D, num_piles: int, wall: Any,
                              min_spacing: float, log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generates pile positions for multiple piles placed along a wall using enhanced logic.

    Enhanced features:
    - Grid shifting optimization for maximum pile count
    - Better wall alignment and spacing
    - Improved multiple row layouts
    """
    log_function_entry(log_callback, "_multiple_piles_along_wall", num_piles=num_piles, min_spacing=min_spacing)

    try:
        # Extract wall geometry using common function
        enhanced_log(log_callback, "Extracting wall geometry for pile placement", 'DEBUG')
        wall_geometry = extract_wall_geometry(wall)
        if not wall_geometry:
            enhanced_log(log_callback, "No valid wall geometry found, using single pile at load center", 'WARNING')
            result = [load_center]
            log_function_exit(log_callback, "_multiple_piles_along_wall", "1 position (no geometry)")
            return result

        start_point, end_point, wall_dir, wall_length = wall_geometry
        enhanced_log(log_callback, f"Wall geometry - Length: {wall_length:.3f}m, Direction: ({wall_dir[0]:.3f}, {wall_dir[1]:.3f})", 'DEBUG')

        if wall_length == 0:
            enhanced_log(log_callback, "Wall length is zero, using single pile at load center", 'WARNING')
            result = [load_center]
            log_function_exit(log_callback, "_multiple_piles_along_wall", "1 position (zero length)")
            return result

        # Calculate perpendicular direction using common function
        enhanced_log(log_callback, "Calculating perpendicular direction for multiple rows", 'DEBUG')
        perp_dir = calculate_perpendicular_direction(wall_dir)
        enhanced_log(log_callback, f"Perpendicular direction: ({perp_dir[0]:.3f}, {perp_dir[1]:.3f})", 'DEBUG')

        # Determine pile arrangement with grid shifting optimization
        enhanced_log(log_callback, "Calculating optimized pile arrangement", 'DEBUG')
        max_piles_per_row = _calculate_optimized_piles_per_row(wall_length, min_spacing, log_callback)
        enhanced_log(log_callback, f"Maximum piles per row: {max_piles_per_row}", 'DEBUG')

        if num_piles <= max_piles_per_row:
            # Single row along wall with grid shifting optimization
            enhanced_log(log_callback, f"Using single row layout for {num_piles} piles", 'INFO')
            result = _single_row_with_grid_shifting(load_center, num_piles, wall_dir, wall_length, min_spacing, log_callback)
        else:
            # Multiple rows needed
            enhanced_log(log_callback, f"Using multiple row layout for {num_piles} piles", 'INFO')
            result = _multiple_rows_layout_enhanced(num_piles, wall_dir, perp_dir, load_center, min_spacing, wall_length, log_callback)

        enhanced_log(log_callback, f"Wall-based layout generated: {len(result)} pile positions", 'INFO')
        log_function_exit(log_callback, "_multiple_piles_along_wall", f"{len(result)} positions")
        return result

    except Exception as e:
        enhanced_log(log_callback, f"Error in wall-based pile layout: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_multiple_piles_along_wall", "error")
        raise


def _calculate_optimized_piles_per_row(wall_length: float, min_spacing: float,
                                     log_callback: Optional[Callable] = None) -> int:
    """
    Calculate optimized maximum piles per row using grid shifting.

    Implements Rule 2.2: Apply grid shifting by maximum 1 min spacing for optimization.

    Args:
        wall_length (float): Length of the wall.
        min_spacing (float): Minimum spacing between piles.
        log_callback: Optional logging callback function.

    Returns:
        int: Optimized maximum piles per row.
    """
    log_function_entry(log_callback, "_calculate_optimized_piles_per_row",
                      wall_length=wall_length, min_spacing=min_spacing)

    try:
        # Basic calculation without shifting
        basic_max = max(1, int(wall_length / min_spacing) + 1)
        enhanced_log(log_callback, f"Basic max piles per row: {basic_max}", 'DEBUG')

        # With grid shifting (up to 1 min spacing), we might fit one more pile
        with_shifting = int((wall_length + min_spacing) / min_spacing) + 1
        enhanced_log(log_callback, f"With grid shifting: {with_shifting}", 'DEBUG')

        result = max(basic_max, with_shifting)
        enhanced_log(log_callback, f"Optimized max piles per row: {result}", 'INFO')

        log_function_exit(log_callback, "_calculate_optimized_piles_per_row", result)
        return result

    except Exception as e:
        enhanced_log(log_callback, f"Error calculating optimized piles per row: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_calculate_optimized_piles_per_row", "error")
        raise


def _single_row_with_grid_shifting(load_center: Point2D, num_piles: int, wall_dir: Point2D,
                                  wall_length: float, min_spacing: float,
                                  log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate single row layout with grid shifting optimization.

    Enhanced to apply grid shifting by maximum 1 min spacing for better pile placement.

    Args:
        load_center (Point2D): Load center position.
        num_piles (int): Number of piles to place.
        wall_dir (Point2D): Wall direction vector.
        wall_length (float): Wall length.
        min_spacing (float): Minimum spacing.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: Optimized single row pile positions.
    """
    log_function_entry(log_callback, "_single_row_with_grid_shifting",
                      num_piles=num_piles, wall_length=wall_length, min_spacing=min_spacing)

    try:
        if num_piles == 1:
            enhanced_log(log_callback, "Single pile layout at load center", 'DEBUG')
            result = [load_center]
            log_function_exit(log_callback, "_single_row_with_grid_shifting", "1 position")
            return result

        # Calculate optimal spacing with grid shifting
        required_length = (num_piles - 1) * min_spacing
        enhanced_log(log_callback, f"Required length for {num_piles} piles: {required_length:.3f}m", 'DEBUG')

        if required_length <= wall_length:
            # No shifting needed - use standard spacing
            actual_spacing = min_spacing
            start_offset = -required_length / 2
            enhanced_log(log_callback, f"Using standard spacing: {actual_spacing:.3f}m", 'DEBUG')
        else:
            # Apply grid shifting - compress spacing slightly
            max_compression = min_spacing * 0.1  # Up to 10% compression
            compressed_spacing = max(min_spacing - max_compression, min_spacing * 0.9)
            enhanced_log(log_callback, f"Attempting grid shifting with compressed spacing: {compressed_spacing:.3f}m", 'DEBUG')

            # Check if compressed spacing fits
            compressed_length = (num_piles - 1) * compressed_spacing
            if compressed_length <= wall_length:
                actual_spacing = compressed_spacing
                start_offset = -compressed_length / 2
                enhanced_log(log_callback, f"Grid shifting successful: {actual_spacing:.3f}m spacing", 'INFO')
            else:
                # Use maximum available spacing
                actual_spacing = wall_length / (num_piles - 1) if num_piles > 1 else 0
                start_offset = -wall_length / 2
                enhanced_log(log_callback, f"Using maximum available spacing: {actual_spacing:.3f}m", 'WARNING')

        # Generate positions along wall direction
        enhanced_log(log_callback, f"Generating {num_piles} positions with spacing {actual_spacing:.3f}m", 'DEBUG')
        positions = []
        for i in range(num_piles):
            offset_along_wall = start_offset + i * actual_spacing
            pos = (
                load_center[0] + offset_along_wall * wall_dir[0],
                load_center[1] + offset_along_wall * wall_dir[1]
            )
            positions.append(pos)

        enhanced_log(log_callback, f"Single row layout generated: {len(positions)} positions", 'INFO')
        log_validation_result(log_callback, "single_row_spacing", actual_spacing >= min_spacing * 0.9,
                            f"Spacing: {actual_spacing:.3f}m")

        log_function_exit(log_callback, "_single_row_with_grid_shifting", f"{len(positions)} positions")
        return positions

    except Exception as e:
        enhanced_log(log_callback, f"Error in single row grid shifting: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_single_row_with_grid_shifting", "error")
        raise


def _multiple_rows_layout_enhanced(num_piles: int, wall_dir: Point2D, perp_dir: Point2D,
                                 load_center: Point2D, min_spacing: float, wall_length: float,
                                 log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generates enhanced pile positions for multiple rows along the wall direction.

    Enhanced features:
    - Better row distribution
    - Optimized spacing along wall
    - Improved alignment with wall geometry

    Args:
        num_piles (int): Total number of piles to place.
        wall_dir (Point2D): Wall direction vector.
        perp_dir (Point2D): Perpendicular direction vector.
        load_center (Point2D): Load center position.
        min_spacing (float): Minimum spacing.
        wall_length (float): Wall length.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: Enhanced multiple row pile positions.
    """
    log_function_entry(log_callback, "_multiple_rows_layout_enhanced",
                      num_piles=num_piles, wall_length=wall_length, min_spacing=min_spacing)

    try:
        # Calculate optimal row and column distribution
        enhanced_log(log_callback, "Calculating optimal row and column distribution", 'DEBUG')
        max_piles_per_row = _calculate_optimized_piles_per_row(wall_length, min_spacing, log_callback)
        rows_needed = ceil(num_piles / max_piles_per_row)
        rows_needed = min(4, rows_needed)  # Limit to 4 rows for practicality

        enhanced_log(log_callback, f"Multiple row layout: {rows_needed} rows, max {max_piles_per_row} piles per row", 'INFO')

        positions = []
        pile_count = 0

        # Calculate row spacing
        total_row_span = (rows_needed - 1) * min_spacing if rows_needed > 1 else 0
        row_start_offset = -total_row_span / 2
        enhanced_log(log_callback, f"Row spacing: {min_spacing:.3f}m, total span: {total_row_span:.3f}m", 'DEBUG')

        for row_idx in range(rows_needed):
            if pile_count >= num_piles:
                break

            piles_this_row = min(max_piles_per_row, num_piles - pile_count)
            row_offset_perp = row_start_offset + row_idx * min_spacing

            enhanced_log(log_callback, f"Row {row_idx + 1}: {piles_this_row} piles, offset {row_offset_perp:.3f}m", 'DEBUG')

            # Calculate center for this row
            row_center = (
                load_center[0] + row_offset_perp * perp_dir[0],
                load_center[1] + row_offset_perp * perp_dir[1]
            )

            # Generate optimized positions for this row
            if piles_this_row == 1:
                positions.append(row_center)
                pile_count += 1
                enhanced_log(log_callback, f"Added single pile at row center for row {row_idx + 1}", 'DEBUG')
            else:
                row_positions = _single_row_with_grid_shifting(
                    row_center, piles_this_row, wall_dir, wall_length, min_spacing, log_callback
                )
                actual_added = min(len(row_positions), num_piles - pile_count)
                positions.extend(row_positions[:actual_added])
                pile_count += actual_added
                enhanced_log(log_callback, f"Added {actual_added} piles for row {row_idx + 1}", 'DEBUG')

        enhanced_log(log_callback, f"Multiple row layout generated: {len(positions)} positions in {rows_needed} rows", 'INFO')
        log_validation_result(log_callback, "multiple_row_layout", len(positions) <= num_piles,
                            f"Generated {len(positions)} positions for {num_piles} requested")

        log_function_exit(log_callback, "_multiple_rows_layout_enhanced", f"{len(positions)} positions")
        return positions

    except Exception as e:
        enhanced_log(log_callback, f"Error in multiple rows layout: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_multiple_rows_layout_enhanced", "error")
        raise


def generate_case_2_possible_pile_grid(load_center: Point2D, min_spacing: float,
                                     initial_local_system: Optional[LocalCoordinateSystem],
                                     wall_geometry: Optional[Tuple] = None,
                                     max_pile_cap_boundary: Optional[List[Point2D]] = None,
                                     log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate possible pile grid positions for Case 2 DXF visualization according to Rule 2.2.
    
    This function generates the complete grid with proper grid shifting optimization,
    which is used for pre-selection visualization in DXF files.
    
    Implements Rule 2.2:
    - Grid centered at Load Center with proper wall alignment
    - Grid shifting in Major and Minor Local Axis by Maximum of 1 Min Spacing
    - Grid size: 2 rows in major/minor axis outside optimal rectangle
    - Grid positions filtered by Maximum Pile Cap boundary
    
    Args:
        load_center (Point2D): The load center coordinates.
        min_spacing (float): Minimum spacing for grid generation.
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        wall_geometry (Optional[Tuple]): Wall geometry (start, end, direction, length).
        max_pile_cap_boundary (Optional[List[Point2D]]): Maximum pile cap boundary for filtering.
        log_callback: Optional logging callback function.
        
    Returns:
        List[Point2D]: All possible pile grid positions with shifting optimization.
    """
    log_function_entry(log_callback, "generate_case_2_possible_pile_grid", 
                      min_spacing=min_spacing, has_wall=wall_geometry is not None, 
                      has_boundary=max_pile_cap_boundary is not None)
    
    try:
        enhanced_log(log_callback, "Generating Case 2 possible pile grid with shifting optimization", 'INFO')
        
        # Generate generous grid size as per Rule 2.2 (2 rows outside optimal rectangle)
        max_grid_extent = 6  # Generous grid for pre-selection visualization
        enhanced_log(log_callback, f"Grid extent: ±{max_grid_extent} (total grid: {(2*max_grid_extent+1)**2} positions)", 'DEBUG')
        
        # Generate base grid positions centered at load center
        enhanced_log(log_callback, "Applying grid shifting optimization (Rule 2.2)", 'DEBUG')
        
        # Multiple grid shift attempts to maximize pile count (Rule 2.2 grid shifting)
        best_positions = []
        max_positions_found = 0
        
        # Try different grid shifts (up to 1 min spacing in major and minor axes)
        shift_range = [-min_spacing, -min_spacing/2, 0, min_spacing/2, min_spacing]
        enhanced_log(log_callback, f"Testing {len(shift_range)} shift values in each direction", 'DEBUG')
        
        shifts_tested = 0
        for shift_x in shift_range:
            for shift_y in shift_range:
                shifts_tested += 1
                current_positions = []
                
                # Generate grid with current shift
                for i in range(-max_grid_extent, max_grid_extent + 1):
                    for j in range(-max_grid_extent, max_grid_extent + 1):
                        local_x = i * min_spacing + shift_x
                        local_y = j * min_spacing + shift_y
                        
                        # Transform to global coordinates (simplified without local system for now)
                        global_x = load_center[0] + local_x
                        global_y = load_center[1] + local_y
                        
                        position = (global_x, global_y)
                        
                        # Filter by maximum pile cap boundary if provided
                        if max_pile_cap_boundary is None:
                            current_positions.append(position)
                        else:
                            # Check if position is within maximum pile cap boundary
                            if _point_in_polygon_case_2(position, max_pile_cap_boundary, log_callback):
                                current_positions.append(position)
                
                # Keep the grid shift that gives maximum positions
                if len(current_positions) > max_positions_found:
                    max_positions_found = len(current_positions)
                    best_positions = current_positions.copy()
                    enhanced_log(log_callback, f"New best shift found: ({shift_x:.3f}, {shift_y:.3f}) with {len(current_positions)} positions", 'DEBUG')
        
        enhanced_log(log_callback, f"Grid shifting optimization complete: tested {shifts_tested} combinations", 'DEBUG')
        enhanced_log(log_callback, f"Best grid generated: {len(best_positions)} positions", 'INFO')
        
        # Validate that load center is included
        load_center_included = any(
            abs(pos[0] - load_center[0]) < min_spacing/2 and abs(pos[1] - load_center[1]) < min_spacing/2 
            for pos in best_positions
        )
        log_validation_result(log_callback, "load_center_in_case_2_grid", load_center_included,
                            "Load center should be near grid positions")
        
        log_function_exit(log_callback, "generate_case_2_possible_pile_grid", f"{len(best_positions)} positions")
        return best_positions
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 2 pile grid generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "generate_case_2_possible_pile_grid", "error")
        raise


def _point_in_polygon_case_2(point: Point2D, polygon: List[Point2D], 
                           log_callback: Optional[Callable] = None) -> bool:
    """
    Check if a point is inside a polygon using ray casting algorithm for Case 2.
    
    Args:
        point (Point2D): Point to check.
        polygon (List[Point2D]): Polygon vertices.
        log_callback: Optional logging callback function.
        
    Returns:
        bool: True if point is inside polygon.
    """
    if not polygon or len(polygon) < 3:
        enhanced_log(log_callback, "No boundary constraint provided - allowing all positions", 'DEBUG')
        return True  # No boundary constraint
    
    try:
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in point-in-polygon check for Case 2: {str(e)}", 'WARNING')
        return True  # Allow position if check fails


def should_show_preselection_in_dxf_case_2(pile_type_name: str, required_piles: int,
                                          log_callback: Optional[Callable] = None) -> bool:
    """
    Determine if pre-selection visualization should be shown in DXF for Case 2.
    
    Implements Rule 2.4 enhancement:
    Only BP with 1 or 2 piles do NOT show pre-selection in DXF (since they use direct positioning).
    All other pile types and pile counts need to show possible pile location and maximum pile cap.
    
    Args:
        pile_type_name (str): Name of pile type ('DHP', 'SHP', 'BP').
        required_piles (int): Number of required piles.
        log_callback: Optional logging callback function.
        
    Returns:
        bool: True if pre-selection should be shown in DXF.
    """
    log_function_entry(log_callback, "should_show_preselection_in_dxf_case_2", 
                      pile_type=pile_type_name, required_piles=required_piles)
    
    try:
        # BP with 1 or 2 piles don't need pre-selection (direct positioning)
        if pile_type_name == 'BP' and required_piles <= 2:
            enhanced_log(log_callback, f"BP with {required_piles} piles uses direct positioning - no pre-selection needed", 'INFO')
            result = False
        else:
            # All other cases need pre-selection visualization
            enhanced_log(log_callback, f"{pile_type_name} with {required_piles} piles requires pre-selection visualization", 'INFO')
            result = True
        
        enhanced_log(log_callback, f"Pre-selection DXF visualization check:", 'DEBUG')
        enhanced_log(log_callback, f"  - Pile type: {pile_type_name}", 'DEBUG')
        enhanced_log(log_callback, f"  - Required piles: {required_piles}", 'DEBUG')
        enhanced_log(log_callback, f"  - Show in DXF: {result}", 'DEBUG')
        
        log_function_exit(log_callback, "should_show_preselection_in_dxf_case_2", result)
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 2 preselection DXF check: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "should_show_preselection_in_dxf_case_2", "error")
        return True  # Default to showing preselection on error


def generate_case_2_optimized_wall_grid(load_center: Point2D, wall_geometry: Tuple,
                                       min_spacing: float, target_pile_count: int,
                                       log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate optimized wall-aligned grid for Case 2 with enhanced shifting.
    
    This function specifically implements Rule 2.2 grid shifting optimization
    for wall layouts with better alignment and spacing.
    
    Args:
        load_center (Point2D): Load center coordinates.
        wall_geometry (Tuple): Wall geometry (start, end, direction, length).
        min_spacing (float): Minimum spacing.
        target_pile_count (int): Target number of piles for optimization.
        log_callback: Optional logging callback function.
        
    Returns:
        List[Point2D]: Optimized wall-aligned grid positions.
    """
    log_function_entry(log_callback, "generate_case_2_optimized_wall_grid", 
                      min_spacing=min_spacing, target_pile_count=target_pile_count)
    
    try:
        if not wall_geometry:
            enhanced_log(log_callback, "No wall geometry provided - using single pile at load center", 'WARNING')
            result = [load_center]
            log_function_exit(log_callback, "generate_case_2_optimized_wall_grid", "1 position (no wall)")
            return result
        
        start_point, end_point, wall_dir, wall_length = wall_geometry
        enhanced_log(log_callback, f"Optimizing wall-aligned grid for {target_pile_count} piles", 'INFO')
        enhanced_log(log_callback, f"Wall length: {wall_length:.3f}m", 'DEBUG')
        
        # Calculate perpendicular direction
        perp_dir = calculate_perpendicular_direction(wall_dir)
        enhanced_log(log_callback, f"Wall direction: ({wall_dir[0]:.3f}, {wall_dir[1]:.3f})", 'DEBUG')
        enhanced_log(log_callback, f"Perpendicular direction: ({perp_dir[0]:.3f}, {perp_dir[1]:.3f})", 'DEBUG')
        
        # Generate positions with multiple shift attempts
        best_positions = []
        max_viable_positions = 0
        
        # Try different alignment shifts along wall direction
        wall_shifts = [-min_spacing/2, 0, min_spacing/2]
        perp_shifts = [-min_spacing/2, 0, min_spacing/2]
        enhanced_log(log_callback, f"Testing {len(wall_shifts)} wall shifts × {len(perp_shifts)} perpendicular shifts", 'DEBUG')
        
        shifts_tested = 0
        for wall_shift in wall_shifts:
            for perp_shift in perp_shifts:
                shifts_tested += 1
                current_positions = []
                
                # Generate grid along wall direction
                max_along_wall = int(wall_length / min_spacing) + 2
                for i in range(-max_along_wall, max_along_wall + 1):
                    wall_offset = i * min_spacing + wall_shift
                    
                    # Generate positions perpendicular to wall
                    for j in range(-3, 4):  # 3 rows on each side of wall
                        perp_offset = j * min_spacing + perp_shift
                        
                        # Calculate position
                        pos_x = (load_center[0] + 
                                wall_offset * wall_dir[0] + 
                                perp_offset * perp_dir[0])
                        pos_y = (load_center[1] + 
                                wall_offset * wall_dir[1] + 
                                perp_offset * perp_dir[1])
                        
                        current_positions.append((pos_x, pos_y))
                
                # Count viable positions (simplified - in real implementation would check boundaries)
                if len(current_positions) > max_viable_positions:
                    max_viable_positions = len(current_positions)
                    best_positions = current_positions.copy()
                    enhanced_log(log_callback, f"New best wall alignment: wall_shift={wall_shift:.3f}, perp_shift={perp_shift:.3f}, positions={len(current_positions)}", 'DEBUG')
        
        enhanced_log(log_callback, f"Wall grid optimization complete: tested {shifts_tested} combinations", 'DEBUG')
        enhanced_log(log_callback, f"Best wall-aligned grid: {len(best_positions)} positions", 'INFO')
        
        log_validation_result(log_callback, "wall_grid_optimization", len(best_positions) >= target_pile_count,
                            f"Generated {len(best_positions)} positions for target {target_pile_count}")
        
        log_function_exit(log_callback, "generate_case_2_optimized_wall_grid", f"{len(best_positions)} positions")
        return best_positions
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in wall grid optimization: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "generate_case_2_optimized_wall_grid", "error")
        raise


def calculate_case_2_pile_requirements(total_load: float, available_pile_types: List[dict], 
                                     wall_length: float, log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Calculate pile requirements for Case 2 based on available pile types and wall constraints.
    
    Implements Rules 2.3 with special constraints:
    - DHP & SHP: Apply even number upgrading
    - BP: Apply wall length rule (≤ 3 pile diameter max 1 pile, else 2)
    
    Args:
        total_load (float): Total load to support (kN).
        available_pile_types (List[dict]): Available pile types with capacities.
        wall_length (float): Length of the wall (m).
        log_callback: Optional logging callback function.
        
    Returns:
        Dict[str, Any]: Selected pile type and required count with constraints.
    """
    log_function_entry(log_callback, "calculate_case_2_pile_requirements", 
                      total_load=total_load, available_types=len(available_pile_types), wall_length=wall_length)
    
    try:
        enhanced_log(log_callback, f"Calculating Case 2 pile requirements:", 'INFO')
        enhanced_log(log_callback, f"  - Total load: {total_load:.1f} kN", 'INFO')
        enhanced_log(log_callback, f"  - Wall length: {wall_length:.3f} m", 'INFO')
        enhanced_log(log_callback, f"  - Available pile types: {len(available_pile_types)}", 'INFO')
        
        # Sort pile types by priority: DHP SHP BP
        pile_type_priority = {'DHP': 1, 'SHP': 2, 'BP': 3}
        sorted_pile_types = sorted(
            available_pile_types, 
            key=lambda x: pile_type_priority.get(x.get('type', 'BP'), 99)
        )
        
        enhanced_log(log_callback, f"Evaluating pile types in priority order: DHP → SHP → BP", 'DEBUG')
        
        for i, pile_type in enumerate(sorted_pile_types):
            pile_type_name = pile_type.get('type', 'Unknown')
            capacity = pile_type.get('capacity', 0)
            
            enhanced_log(log_callback, f"Evaluating pile type {i+1}: {pile_type_name} (capacity: {capacity:.1f} kN)", 'DEBUG')
            
            if capacity <= 0:
                enhanced_log(log_callback, f"Skipping {pile_type_name} - invalid capacity: {capacity}", 'WARNING')
                continue
                
            # Calculate basic required piles
            required_piles = max(1, int(ceil(total_load / capacity)))
            enhanced_log(log_callback, f"Basic calculation: {required_piles} piles needed for {pile_type_name}", 'DEBUG')
            
            if pile_type_name == 'BP':
                enhanced_log(log_callback, "Applying BP wall length constraints", 'DEBUG')
                # Apply BP wall length constraint
                pile_diameter = pile_type.get('diameter', 0.6)
                threshold_length = 3 * pile_diameter
                
                enhanced_log(log_callback, f"BP diameter: {pile_diameter:.3f}m, threshold: {threshold_length:.3f}m", 'DEBUG')
                log_constraint_check(log_callback, "BP_wall_length_constraint", wall_length, threshold_length, wall_length > threshold_length)
                
                if wall_length <= threshold_length:
                    # Wall too short: maximum 1 pile
                    if capacity >= total_load:
                        utilization = total_load / capacity
                        enhanced_log(log_callback, f"BP wall constraint: 1 pile max (wall {wall_length:.3f}m ≤ {threshold_length:.3f}m)", 'INFO')
                        result = {
                            'selected_pile_type': pile_type,
                            'required_piles': 1,
                            'total_capacity': capacity,
                            'utilization_ratio': utilization,
                            'constraint_applied': 'BP_wall_length_max_1'
                        }
                        log_function_exit(log_callback, "calculate_case_2_pile_requirements", "BP_1_pile_selected")
                        return result
                    else:
                        # Single BP insufficient, skip this type
                        enhanced_log(log_callback, f"Single BP insufficient: {capacity:.1f} kN < {total_load:.1f} kN - skipping", 'WARNING')
                        continue
                else:
                    # Wall long enough: maximum 2 piles
                    max_bp_piles = min(required_piles, 2)
                    total_bp_capacity = max_bp_piles * capacity
                    
                    if total_bp_capacity >= total_load:
                        utilization = total_load / total_bp_capacity
                        enhanced_log(log_callback, f"BP wall constraint: {max_bp_piles} piles (wall {wall_length:.3f}m > {threshold_length:.3f}m)", 'INFO')
                        result = {
                            'selected_pile_type': pile_type,
                            'required_piles': max_bp_piles,
                            'total_capacity': total_bp_capacity,
                            'utilization_ratio': utilization,
                            'constraint_applied': 'BP_wall_length_max_2' if max_bp_piles == 2 else None
                        }
                        log_function_exit(log_callback, "calculate_case_2_pile_requirements", f"BP_{max_bp_piles}_piles_selected")
                        return result
                    else:
                        # Insufficient capacity even with 2 BP piles, skip this type
                        enhanced_log(log_callback, f"2 BP piles insufficient: {total_bp_capacity:.1f} kN < {total_load:.1f} kN - skipping", 'WARNING')
                        continue
                        
            elif pile_type_name in ['DHP', 'SHP']:
                enhanced_log(log_callback, f"Applying even number upgrade for {pile_type_name}", 'DEBUG')
                # Apply even number upgrading
                upgraded_piles = _upgrade_to_even_number(required_piles, log_callback)
                total_capacity = upgraded_piles * capacity
                utilization = total_load / total_capacity
                
                enhanced_log(log_callback, f"{pile_type_name} selected: {upgraded_piles} piles, capacity: {total_capacity:.1f} kN (utilization: {utilization:.1%})", 'INFO')
                result = {
                    'selected_pile_type': pile_type,
                    'required_piles': upgraded_piles,
                    'total_capacity': total_capacity,
                    'utilization_ratio': utilization,
                    'constraint_applied': 'even_number_upgrade' if upgraded_piles != required_piles else None
                }
                log_function_exit(log_callback, "calculate_case_2_pile_requirements", f"{pile_type_name}_selected")
                return result
            else:
                # Other pile types - no special constraints
                total_capacity = required_piles * capacity
                utilization = total_load / total_capacity
                
                enhanced_log(log_callback, f"{pile_type_name} selected: {required_piles} piles, capacity: {total_capacity:.1f} kN (utilization: {utilization:.1%})", 'INFO')
                result = {
                    'selected_pile_type': pile_type,
                    'required_piles': required_piles,
                    'total_capacity': total_capacity,
                    'utilization_ratio': utilization,
                    'constraint_applied': None
                }
                log_function_exit(log_callback, "calculate_case_2_pile_requirements", f"{pile_type_name}_selected")
                return result
        
        # No suitable pile type found
        enhanced_log(log_callback, "No suitable pile type found for the given load and wall constraints", 'ERROR')
        result = {
            'selected_pile_type': None,
            'required_piles': 0,
            'total_capacity': 0,
            'utilization_ratio': float('inf'),
            'constraint_applied': 'NO_SUITABLE_PILE_TYPE'
        }
        log_function_exit(log_callback, "calculate_case_2_pile_requirements", "no_suitable_type")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 2 pile requirements calculation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "calculate_case_2_pile_requirements", "error")
        raise
