# ETABS Integration Module Structure (`etabs_cwls/`)

## Main ETABS Integration Directory
```
etabs_cwls/
├── 📄 cwls_gui.py               # Loading Schedule GUI
├── 📄 _main.py                  # Primary workflow coordination
├── 📄 _class.py                 # Core data classes
├── 📄 _read.py                  # Database reading functions
├── 📄 _write.py                 # Excel writing functions
├── 📄 _logging.py               # Logging configuration
├── 📄 configuration_manager.py  # System configuration
├── 📄 console_interface.py      # Command-line interface
├── 📄 coordinate_transformer.py # Coordinate transformations
├── 📄 initialization_processor.py # Model initialization
├── 📄 joint_reaction_processor.py # Joint reaction processing
├── 📄 pier_force_processor.py   # Pier force processing
├── 📄 schedule_generator.py     # Schedule generation
├── 📄 file_path_manager.py      # File path management
├── 📄 logging_config.py         # Advanced logging
├── 📄 exceptions.py             # Exception hierarchy
├── 📄 test_basic_imports.py     # Basic functionality tests
├── 📄 test_integration.py       # Integration testing
├── 📄 validate_refactoring.py   # Code validation
├── 📄 README.md                 # ETABS integration docs
├── 📄 INTEGRATION_TEST_REPORT.md # Test results
├── 📄 REFACTORING_SUMMARY.md    # Refactoring history
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## Key ETABS Integration Features
- **Multi-processor support**: Handles different ETABS analysis processors
- **Load extraction**: Joint reactions, pier forces, story data
- **Coordinate transformation**: Global to local coordinate systems
- **Schedule generation**: Automated loading schedule creation
- **Excel integration**: Professional Excel output formatting
- **Validation system**: Comprehensive testing and validation
- **Error handling**: Robust exception management
- **Logging system**: Performance monitoring and debugging
- **File management**: Intelligent file path handling
- **Configuration**: Flexible system configuration options