﻿"""
Load-Based Clustering Functions

Contains functions for creating sub-clusters within structural groups based on loading
patterns using K-Means clustering and pile distribution calculations.
"""

from typing import List, Dict, Any
import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score

from ..data_types import GroupElements
from ..exceptions import GroupingError
from ..load_calculator import calculate_total_load_from_axial
from ..utils.logging_utils import (
    enhanced_log, log_function_entry, log_function_exit, log_progress,
    log_performance_metric, log_validation_result, log_algorithm_step,
    log_calculation_result, log_error_with_context, create_timed_logger
)


def create_load_based_sub_clusters(group_elements: GroupElements, excel_inputs: Any,
                                  load_types: List[str] = None,
                                  max_sub_clusters: int = 5,
                                  min_sub_cluster_size: int = 1,
                                  log_callback=None) -> Dict[str, Dict[str, Any]]:
    """
    Create sub-clusters within a structural group based on loading using K-Means clustering.
    
    This function performs load-aware sub-clustering where:
    - Column loads are treated as point loads at their locations
    - Wall loads are distributed as point loads per meter along wall length
    - K-Means clustering finds optimal sub-groups based on load distribution
    - Silhouette score determines the optimal number of sub-clusters
    
    Args:
        group_elements: Group containing columns and walls to sub-cluster
        excel_inputs: Excel inputs containing load data
        load_types: List of load types to consider (default: ['DL', 'SDL', 'LL'])
        max_sub_clusters: Maximum number of sub-clusters to consider
        min_sub_cluster_size: Minimum elements per sub-cluster
        log_callback: Optional callback for logging clustering progress
        
    Returns:
        Dictionary mapping sub-cluster IDs to sub-cluster data containing:
        - 'elements': GroupElements for this sub-cluster
        - 'load_points': List of (x, y, load) tuples representing load distribution
        - 'centroid': Load-weighted centroid of the sub-cluster
        - 'total_load': Total load in the sub-cluster
        - 'aspect_ratio': Recommended aspect ratio for pile grid
        - 'bounding_box': (min_x, min_y, max_x, max_y) of load points
        
    Raises:
        GroupingError: If sub-clustering fails
        LoadCalculationError: If load calculation fails
    """
    # Set default load types if not provided
    if load_types is None:
        load_types = ['DL', 'SDL', 'LL']
    
    log_function_entry(log_callback, "create_load_based_sub_clusters",
                      columns_count=len(group_elements.get('columns', [])),
                      walls_count=len(group_elements.get('walls', [])),
                      load_types=load_types, max_sub_clusters=max_sub_clusters,
                      min_sub_cluster_size=min_sub_cluster_size)
    
    with create_timed_logger(log_callback, "Load-Based Sub-Clustering") as timer:
        try:
            log_algorithm_step(log_callback, "Load-Based Clustering", "Starting load-based sub-clustering")
            
            num_columns = len(group_elements.get('columns', []))
            num_walls = len(group_elements.get('walls', []))
            log_performance_metric(log_callback, "Input Columns", num_columns, "elements")
            log_performance_metric(log_callback, "Input Walls", num_walls, "elements")
            log_calculation_result(log_callback, "Load Types", str(load_types))
            log_performance_metric(log_callback, "Max Sub-clusters", max_sub_clusters, "clusters")
            log_performance_metric(log_callback, "Min Sub-cluster Size", min_sub_cluster_size, "elements")            
            # Step 1: Extract load points from columns and walls
            log_algorithm_step(log_callback, "Load-Based Clustering", "Extracting load points from structural elements")
            load_points = []
            element_load_mapping = {}  # Maps (x, y) to element info
            total_column_load = 0.0
            total_wall_load = 0.0
            
            # Process columns - treat as point loads
            columns = group_elements.get('columns', [])
            columns_processed = 0
            columns_with_load = 0
            
            log_algorithm_step(log_callback, "Load-Based Clustering", "Processing columns as point loads",
                             f"{len(columns)} columns")
            
            for i, column_data in enumerate(columns):
                log_progress(log_callback, i + 1, len(columns), "column load processing")
                
                if not column_data or len(column_data) < 4:
                    enhanced_log(log_callback, f"Skipping invalid column data: {column_data}", 'WARNING')
                    continue
                    
                column_name, x, y, base_level = column_data[:4]
                
                # Calculate column load
                try:
                    df_column = excel_inputs.InputLoadColumn.copy()
                    column_load, _ = calculate_total_load_from_axial(df_column, column_name, load_types)
                    
                    if column_load > 0:
                        load_points.append((float(x), float(y), float(column_load)))
                        columns_with_load += 1
                        total_column_load += column_load
                        enhanced_log(log_callback, f"Column '{column_name}' at ({x:.2f}, {y:.2f}): {column_load:.1f} kN", 'DEBUG')
                    else:
                        load_points.append((float(x), float(y), 0.0))
                        enhanced_log(log_callback, f"Column '{column_name}' at ({x:.2f}, {y:.2f}): 0.0 kN (no load data)", 'DEBUG')
                    
                    element_load_mapping[(float(x), float(y))] = {
                        'type': 'column',
                        'name': column_name,
                        'data': column_data,
                        'load': column_load
                    }
                    columns_processed += 1
                    
                except Exception as e:
                    log_error_with_context(log_callback, e, f"column load calculation for {column_name}")
                    raise GroupingError(f"Failed to process column {column_name}: {str(e)}") from e
                
            log_calculation_result(log_callback, "Columns Processed", columns_processed, "elements")
            log_calculation_result(log_callback, "Columns with Load Data", columns_with_load, "elements")
            log_calculation_result(log_callback, "Total Column Load", total_column_load, "kN")
            log_performance_metric(log_callback, "Column Processing Success Rate",
                                 columns_processed / len(columns) * 100 if columns else 100, "%")                
            # Process walls - distribute load per meter
            walls = group_elements.get('walls', [])
            walls_processed = 0
            walls_with_load = 0
            total_wall_load_points = 0
            
            log_algorithm_step(log_callback, "Load-Based Clustering", "Processing walls with distributed loading",
                             f"{len(walls)} walls")
            
            for i, wall_data in enumerate(walls):
                log_progress(log_callback, i + 1, len(walls), "wall load processing")
                
                if not wall_data or len(wall_data) < 3:
                    enhanced_log(log_callback, f"Skipping invalid wall data: {wall_data}", 'WARNING')
                    continue
                    
                wall_name, wall_points, base_level = wall_data[:3]
                
                if not wall_points or len(wall_points) < 2:
                    error_msg = f"Invalid wall '{wall_name}': insufficient points ({len(wall_points) if wall_points else 0})"
                    log_error_with_context(log_callback, ValueError(error_msg), f"wall validation for {wall_name}")
                    raise GroupingError(error_msg)
                
                # Calculate total wall load
                try:
                    df_wall = excel_inputs.InputLoadWall.copy()
                    wall_load, _ = calculate_total_load_from_axial(df_wall, wall_name, load_types)
                    if wall_load > 0:
                        walls_with_load += 1
                        total_wall_load += wall_load
                        enhanced_log(log_callback, f"Wall '{wall_name}': {wall_load:.1f} kN total load", 'DEBUG')
                except Exception as e:
                    log_error_with_context(log_callback, e, f"wall load calculation for {wall_name}")
                    raise GroupingError(f"Failed to get load for wall {wall_name}: {str(e)}") from e
                
                # Calculate wall length
                wall_length = 0.0
                for j in range(len(wall_points) - 1):
                    x1, y1 = wall_points[j][:2]
                    x2, y2 = wall_points[j + 1][:2]
                    segment_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                    wall_length += segment_length
                
                if wall_length > 0:
                    load_per_meter = wall_load / wall_length
                    wall_load_points_count = 0
                    
                    log_calculation_result(log_callback, f"Wall '{wall_name}' Metrics",
                                         f"{wall_length:.2f}m length, {load_per_meter:.1f} kN/m")
                    
                    # Distribute load points along wall at 1-meter intervals
                    for j in range(len(wall_points) - 1):
                        x1, y1 = wall_points[j][:2]
                        x2, y2 = wall_points[j + 1][:2]
                        segment_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                        
                        if segment_length > 0:
                            num_points = max(1, int(np.ceil(segment_length)))
                            for k in range(num_points):
                                t = k / num_points if num_points > 1 else 0.5
                                x = x1 + t * (x2 - x1)
                                y = y1 + t * (y2 - y1)
                                point_load = load_per_meter * (segment_length / num_points)
                                
                                load_points.append((float(x), float(y), float(point_load)))
                                element_load_mapping[(float(x), float(y))] = {
                                    'type': 'wall',
                                    'name': wall_name,
                                    'data': wall_data,
                                    'load': point_load,
                                    'wall_segment': j,
                                    'position_ratio': t
                                }
                                wall_load_points_count += 1
                                total_wall_load_points += 1
                    
                    enhanced_log(log_callback, f"Wall '{wall_name}': distributed into {wall_load_points_count} load points", 'DEBUG')
                        
                walls_processed += 1
            
            # Log processing summary
            log_calculation_result(log_callback, "Walls Processed", walls_processed, "elements")
            log_calculation_result(log_callback, "Walls with Load Data", walls_with_load, "elements")
            log_calculation_result(log_callback, "Total Wall Load", total_wall_load, "kN")
            log_calculation_result(log_callback, "Wall Load Points", total_wall_load_points, "points")
            log_calculation_result(log_callback, "Grand Total Load", total_column_load + total_wall_load, "kN")
            log_performance_metric(log_callback, "Total Load Points Created", len(load_points), "points")
            log_performance_metric(log_callback, "Wall Processing Success Rate",
                                 walls_processed / len(walls) * 100 if walls else 100, "%")            
            # Handle case where no load points are created
            if not load_points:
                enhanced_log(log_callback, "No load points created, returning single cluster", 'WARNING')
                log_validation_result(log_callback, "Load Points Generation", False, "no load points created")
                result = {
                    'sub_cluster_1': {
                        'elements': group_elements,
                        'load_points': [],
                        'centroid': (0.0, 0.0),
                        'total_load': 0.0,
                        'aspect_ratio': 1.0,
                        'bounding_box': (0.0, 0.0, 0.0, 0.0)
                    }
                }
                log_function_exit(log_callback, "create_load_based_sub_clusters", "1 default cluster")
                return result
            
            log_validation_result(log_callback, "Load Points Generation", True, f"{len(load_points)} points created")
            
            # Calculate the total number of unique structural elements
            num_elements_in_group = len(group_elements.get('columns', [])) + len(group_elements.get('walls', []))
            log_performance_metric(log_callback, "Elements in Group", num_elements_in_group, "elements")

            # Step 2: Prepare data for K-Means clustering
            log_algorithm_step(log_callback, "Load-Based Clustering", "Preparing data for K-Means clustering")
            
            load_array = np.array(load_points)
            positions = load_array[:, :2]  # x, y coordinates
            loads = load_array[:, 2]       # load values
            
            # Create load-weighted positions for clustering
            max_load = np.max(loads) if np.max(loads) > 0 else 1.0
            normalized_loads = loads / max_load
            
            min_load = np.min(loads)
            avg_load = np.mean(loads)
            log_calculation_result(log_callback, "Load Statistics", 
                                 f"Min: {min_load:.1f} kN, Max: {max_load:.1f} kN, Avg: {avg_load:.1f} kN")
            
            # Weight positions by normalized loads (higher load = more influence)
            weighted_positions = positions * (1.0 + normalized_loads.reshape(-1, 1))
            log_performance_metric(log_callback, "Weighted Positions Created", len(weighted_positions), "points")            
            # Step 3: Find optimal number of clusters using silhouette score
            log_algorithm_step(log_callback, "Load-Based Clustering", "Finding optimal number of clusters using silhouette score")
            
            if len(load_points) <= 2:
                optimal_k = 1
                enhanced_log(log_callback, f"Only {len(load_points)} load points, using k=1", 'INFO')
                log_calculation_result(log_callback, "Optimal K Selection", f"k=1 (insufficient points)")
            else:
                optimal_k = 1
                best_score = -1
                
                # Determine the maximum number of clusters to test
                k_max_inclusive = len(load_points) - 1  # Max k for silhouette score
                if num_elements_in_group > 0:
                    k_max_inclusive = min(k_max_inclusive, num_elements_in_group)
                k_max_inclusive = min(k_max_inclusive, max_sub_clusters)

                log_performance_metric(log_callback, "K Search Range", f"2 to {k_max_inclusive}")
                enhanced_log(log_callback, f"Testing k from 2 to {k_max_inclusive} (limited by {len(load_points)} load points, {num_elements_in_group} elements, max={max_sub_clusters})", 'DEBUG')

                k_evaluation_results = []
                
                for k in range(2, k_max_inclusive + 1):
                    try:
                        log_progress(log_callback, k - 1, k_max_inclusive - 1, f"K-means evaluation (k={k})")
                        
                        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                        cluster_labels = kmeans.fit_predict(weighted_positions)
                        
                        # Check minimum cluster size
                        unique_labels, counts = np.unique(cluster_labels, return_counts=True)
                        min_cluster_size = np.min(counts)
                        
                        if min_cluster_size < min_sub_cluster_size:
                            enhanced_log(log_callback, f"k={k}: Minimum cluster size {min_cluster_size} < {min_sub_cluster_size}, skipping", 'DEBUG')
                            continue
                        
                        # Calculate silhouette score
                        score = silhouette_score(weighted_positions, cluster_labels)
                        k_evaluation_results.append((k, len(unique_labels), counts.tolist(), score))
                        
                        log_calculation_result(log_callback, f"K={k} Evaluation", 
                                             f"{len(unique_labels)} clusters, sizes {counts.tolist()}, score {score:.3f}")
                        enhanced_log(log_callback, f"k={k}: {len(unique_labels)} clusters, sizes {counts.tolist()}, silhouette score {score:.3f}", 'DEBUG')
                        
                        if score > best_score:
                            best_score = score
                            optimal_k = k
                            enhanced_log(log_callback, f"New best k={k} with score {score:.3f}", 'INFO')
                            
                    except Exception as e:
                        log_error_with_context(log_callback, e, f"K-means evaluation for k={k}")
                        enhanced_log(log_callback, f"K-Means failed for k={k}: {e}", 'WARNING')
                        continue
                
                # Log evaluation summary
                log_performance_metric(log_callback, "K Values Evaluated", len(k_evaluation_results), "values")
                log_calculation_result(log_callback, "Best Silhouette Score", best_score)
                if k_evaluation_results:
                    sorted_results = sorted(k_evaluation_results, key=lambda x: x[3], reverse=True)
                    enhanced_log(log_callback, "Top 3 K values by silhouette score:", 'DEBUG')
                    for i, (k, n_clusters, sizes, score) in enumerate(sorted_results[:3]):
                        enhanced_log(log_callback, f"#{i+1}: k={k} ({n_clusters} clusters, score {score:.3f})", 'DEBUG')
            
            log_calculation_result(log_callback, "Optimal Number of Sub-clusters", optimal_k)
            enhanced_log(log_callback, f"Optimal number of sub-clusters selected: k={optimal_k}", 'INFO')            
            # Step 4: Apply optimal clustering
            log_algorithm_step(log_callback, "Load-Based Clustering", "Applying optimal clustering", f"k={optimal_k}")
                
            if optimal_k == 1:
                cluster_labels = np.zeros(len(load_points), dtype=int)
                enhanced_log(log_callback, "Using single cluster (k=1)", 'INFO')
            else:
                kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(weighted_positions)
                log_validation_result(log_callback, "K-Means Clustering", True, f"k={optimal_k} applied successfully")
                enhanced_log(log_callback, f"K-Means clustering completed with k={optimal_k}", 'INFO')
            
            # Step 5: Form sub-clusters
            log_algorithm_step(log_callback, "Load-Based Clustering", "Forming sub-clusters and calculating statistics")
                
            sub_clusters = {}
            
            for cluster_id in np.unique(cluster_labels):
                log_progress(log_callback, cluster_id + 1, len(np.unique(cluster_labels)), 
                           f"sub-cluster formation (cluster {cluster_id + 1})")
                
                cluster_mask = cluster_labels == cluster_id
                cluster_load_points = load_array[cluster_mask]
                
                enhanced_log(log_callback, f"Processing cluster {cluster_id + 1} with {len(cluster_load_points)} load points", 'DEBUG')
                
                # Separate columns and walls for this cluster
                cluster_columns = []
                cluster_walls = []
                cluster_wall_names = set()
                
                for j, (x, y, load) in enumerate(cluster_load_points):
                    key = (float(x), float(y))
                    if key in element_load_mapping:
                        element_info = element_load_mapping[key]
                        
                        if element_info['type'] == 'column':
                            # Add column if not already added
                            if element_info['data'] not in cluster_columns:
                                cluster_columns.append(element_info['data'])
                        
                        elif element_info['type'] == 'wall':
                            # Add wall if not already added
                            wall_name = element_info['name']
                            if wall_name not in cluster_wall_names:
                                cluster_walls.append(element_info['data'])
                                cluster_wall_names.add(wall_name)
                
                # Calculate cluster statistics
                cluster_total_load = np.sum(cluster_load_points[:, 2])
                
                # Load-weighted centroid
                if cluster_total_load > 0:
                    cluster_centroid_x = np.sum(cluster_load_points[:, 0] * cluster_load_points[:, 2]) / cluster_total_load
                    cluster_centroid_y = np.sum(cluster_load_points[:, 1] * cluster_load_points[:, 2]) / cluster_total_load
                else:
                    cluster_centroid_x = np.mean(cluster_load_points[:, 0])
                    cluster_centroid_y = np.mean(cluster_load_points[:, 1])
                
                # Bounding box
                min_x, min_y = np.min(cluster_load_points[:, :2], axis=0)
                max_x, max_y = np.max(cluster_load_points[:, :2], axis=0)
                
                # Calculate aspect ratio for pile grid
                width = max_x - min_x
                height = max_y - min_y
                aspect_ratio = max(width, height) / max(min(width, height), 0.1) if max(width, height) > 0 else 1.0
                
                sub_cluster_name = f'sub_cluster_{cluster_id + 1}'
                sub_clusters[sub_cluster_name] = {
                    'elements': {
                        'columns': cluster_columns,
                        'walls': cluster_walls
                    },
                    'load_points': [(float(x), float(y), float(load)) for x, y, load in cluster_load_points],
                    'centroid': (float(cluster_centroid_x), float(cluster_centroid_y)),
                    'total_load': float(cluster_total_load),
                    'aspect_ratio': float(aspect_ratio),
                    'bounding_box': (float(min_x), float(min_y), float(max_x), float(max_y))
                }
                
                # Log detailed cluster information
                log_calculation_result(log_callback, f"{sub_cluster_name} Elements", 
                                     f"{len(cluster_columns)} columns, {len(cluster_walls)} walls")
                log_calculation_result(log_callback, f"{sub_cluster_name} Load Points", len(cluster_load_points))
                log_calculation_result(log_callback, f"{sub_cluster_name} Total Load", cluster_total_load, "kN")
                log_calculation_result(log_callback, f"{sub_cluster_name} Centroid", 
                                     f"({cluster_centroid_x:.2f}, {cluster_centroid_y:.2f})")
                log_calculation_result(log_callback, f"{sub_cluster_name} Bounding Box", 
                                     f"({min_x:.2f}, {min_y:.2f}) to ({max_x:.2f}, {max_y:.2f})")
                log_calculation_result(log_callback, f"{sub_cluster_name} Aspect Ratio", f"{aspect_ratio:.2f}")
                
                enhanced_log(log_callback, f"{sub_cluster_name}:", 'DEBUG')
                enhanced_log(log_callback, f"  Elements: {len(cluster_columns)} columns, {len(cluster_walls)} walls", 'DEBUG')
                enhanced_log(log_callback, f"  Load points: {len(cluster_load_points)}", 'DEBUG')
                enhanced_log(log_callback, f"  Total load: {cluster_total_load:.1f} kN", 'DEBUG')
                enhanced_log(log_callback, f"  Centroid: ({cluster_centroid_x:.2f}, {cluster_centroid_y:.2f})", 'DEBUG')
                enhanced_log(log_callback, f"  Bounding box: ({min_x:.2f}, {min_y:.2f}) to ({max_x:.2f}, {max_y:.2f})", 'DEBUG')
                enhanced_log(log_callback, f"  Aspect ratio: {aspect_ratio:.2f}", 'DEBUG')
            
            # Final summary
            total_clustered_load = sum(cluster['total_load'] for cluster in sub_clusters.values())
            log_performance_metric(log_callback, "Sub-clusters Created", len(sub_clusters), "clusters")
            log_performance_metric(log_callback, "Total Load Distributed", total_clustered_load, "kN")
            log_performance_metric(log_callback, "Load Distribution Accuracy", 
                                 total_clustered_load / (total_column_load + total_wall_load) * 100 
                                 if (total_column_load + total_wall_load) > 0 else 100, "%")
            
            enhanced_log(log_callback, f"Load-based sub-clustering completed successfully", 'INFO')
            enhanced_log(log_callback, f"Created {len(sub_clusters)} sub-clusters with optimal k={optimal_k}", 'INFO')
            enhanced_log(log_callback, f"Total load distributed: {total_clustered_load:.1f} kN", 'INFO')
            
            log_function_exit(log_callback, "create_load_based_sub_clusters", f"{len(sub_clusters)} sub-clusters")
            return sub_clusters
            
        except Exception as e:
            log_error_with_context(log_callback, e, "create_load_based_sub_clusters")
            raise GroupingError(f"Load-based sub-clustering failed: {str(e)}") from e


def calculate_sub_cluster_pile_distribution(sub_clusters: Dict[str, Dict[str, Any]], 
                                          total_piles: int,
                                          min_piles_per_cluster: int = 1,
                                          log_callback=None) -> Dict[str, int]:
    """
    Distribute piles among sub-clusters based on their load ratios.
    
    Args:
        sub_clusters: Dictionary of sub-cluster data
        total_piles: Total number of piles to distribute
        min_piles_per_cluster: Minimum piles per sub-cluster
        log_callback: Optional callback for logging distribution process
        
    Returns:
        Dictionary mapping sub-cluster names to pile counts
    """
    log_function_entry(log_callback, "calculate_sub_cluster_pile_distribution",
                      sub_clusters_count=len(sub_clusters), total_piles=total_piles,
                      min_piles_per_cluster=min_piles_per_cluster)
    
    with create_timed_logger(log_callback, "Pile Distribution Calculation") as timer:
        try:
            if not sub_clusters:
                enhanced_log(log_callback, "No sub-clusters provided for pile distribution", 'WARNING')
                log_function_exit(log_callback, "calculate_sub_cluster_pile_distribution", "empty distribution")
                return {}
            
            log_algorithm_step(log_callback, "Pile Distribution", "Starting pile distribution calculation")
            log_performance_metric(log_callback, "Total Piles to Distribute", total_piles, "piles")
            log_performance_metric(log_callback, "Number of Sub-clusters", len(sub_clusters), "clusters")
            log_performance_metric(log_callback, "Minimum Piles per Cluster", min_piles_per_cluster, "piles")
            
            # Calculate total load across all sub-clusters
            total_load = sum(cluster['total_load'] for cluster in sub_clusters.values())
            log_calculation_result(log_callback, "Total Load Across Sub-clusters", total_load, "kN")
            
            # Log individual cluster loads
            for cluster_name, cluster_data in sub_clusters.items():
                cluster_load = cluster_data['total_load']
                load_percentage = (cluster_load / total_load * 100) if total_load > 0 else 0
                log_calculation_result(log_callback, f"{cluster_name} Load", 
                                     f"{cluster_load:.1f} kN ({load_percentage:.1f}%)")
                enhanced_log(log_callback, f"{cluster_name}: {cluster_load:.1f} kN ({load_percentage:.1f}%)", 'DEBUG')
            
            if total_load <= 0:
                log_algorithm_step(log_callback, "Pile Distribution", "Applying equal distribution (no load data)")
                enhanced_log(log_callback, "No load data available, distributing piles equally", 'WARNING')
                
                # Distribute equally if no load data
                piles_per_cluster = max(min_piles_per_cluster, total_piles // len(sub_clusters))
                remaining_piles = total_piles - (piles_per_cluster * len(sub_clusters))
                
                distribution = {}
                cluster_names = list(sub_clusters.keys())
                
                for i, cluster_name in enumerate(cluster_names):
                    piles = piles_per_cluster
                    if i < remaining_piles:
                        piles += 1
                    distribution[cluster_name] = max(min_piles_per_cluster, piles)
                
                log_algorithm_step(log_callback, "Pile Distribution", "Equal distribution completed")
                for cluster_name, pile_count in distribution.items():
                    log_calculation_result(log_callback, f"{cluster_name} Pile Allocation", pile_count, "piles")
                    enhanced_log(log_callback, f"{cluster_name}: {pile_count} piles", 'DEBUG')
                
                log_function_exit(log_callback, "calculate_sub_cluster_pile_distribution", "equal distribution")
                return distribution
            
            # Distribute based on load ratios
            log_algorithm_step(log_callback, "Pile Distribution", "Distributing piles based on load ratios")
                
            distribution = {}
            allocated_piles = 0
            load_ratio_data = []
            
            for cluster_name, cluster_data in sub_clusters.items():
                cluster_load = cluster_data['total_load']
                load_ratio = cluster_load / total_load
                
                # Calculate piles for this cluster
                cluster_piles = max(min_piles_per_cluster, int(round(total_piles * load_ratio)))
                distribution[cluster_name] = cluster_piles
                allocated_piles += cluster_piles
                
                load_ratio_data.append((cluster_name, load_ratio, cluster_piles))
                log_calculation_result(log_callback, f"{cluster_name} Load Ratio", f"{load_ratio:.3f}")
                log_calculation_result(log_callback, f"{cluster_name} Initial Allocation", cluster_piles, "piles")
                enhanced_log(log_callback, f"{cluster_name}: Load ratio {load_ratio:.3f} -> {cluster_piles} piles (before adjustment)", 'DEBUG')
            
            # Adjust for rounding differences
            pile_difference = total_piles - allocated_piles
            log_calculation_result(log_callback, "Pile Allocation Status", 
                                 f"Initial: {allocated_piles}, Target: {total_piles}, Difference: {pile_difference}")
            enhanced_log(log_callback, f"Initial allocation: {allocated_piles} piles, target: {total_piles}, difference: {pile_difference}", 'DEBUG')
            
            if pile_difference != 0:
                log_algorithm_step(log_callback, "Pile Distribution", "Adjusting for rounding differences")
                    
                # Sort clusters by load descending to prioritize high-load clusters
                sorted_clusters = sorted(sub_clusters.items(), 
                                       key=lambda x: x[1]['total_load'], 
                                       reverse=True)
                
                adjustments_made = 0
                for cluster_name, cluster_data in sorted_clusters:
                    if pile_difference == 0:
                        break
                    
                    if pile_difference > 0:
                        distribution[cluster_name] += 1
                        pile_difference -= 1
                        adjustments_made += 1
                        enhanced_log(log_callback, f"Added 1 pile to {cluster_name} (high load priority)", 'DEBUG')
                    elif pile_difference < 0 and distribution[cluster_name] > min_piles_per_cluster:
                        distribution[cluster_name] -= 1
                        pile_difference += 1
                        adjustments_made += 1
                        enhanced_log(log_callback, f"Removed 1 pile from {cluster_name} (above minimum)", 'DEBUG')
                
                log_performance_metric(log_callback, "Rounding Adjustments Made", adjustments_made, "adjustments")
            
            # Final distribution summary
            log_algorithm_step(log_callback, "Pile Distribution", "Computing final distribution summary")
            total_distributed = 0
            load_per_pile_data = []
            
            for cluster_name, pile_count in distribution.items():
                cluster_load = sub_clusters[cluster_name]['total_load']
                load_per_pile = cluster_load / pile_count if pile_count > 0 else 0
                total_distributed += pile_count
                
                load_per_pile_data.append((cluster_name, pile_count, load_per_pile))
                log_calculation_result(log_callback, f"{cluster_name} Final Allocation", 
                                     f"{pile_count} piles ({load_per_pile:.1f} kN/pile)")
                enhanced_log(log_callback, f"{cluster_name}: {pile_count} piles ({load_per_pile:.1f} kN/pile)", 'DEBUG')
            
            # Validation and final metrics
            log_performance_metric(log_callback, "Total Piles Distributed", total_distributed, "piles")
            log_performance_metric(log_callback, "Distribution Accuracy", 
                                 total_distributed / total_piles * 100 if total_piles > 0 else 100, "%")
            
            if total_distributed != total_piles:
                log_validation_result(log_callback, "Pile Distribution", False, 
                                    f"Distributed {total_distributed} instead of {total_piles}")
                enhanced_log(log_callback, f"WARNING: Total distributed ({total_distributed}) != target ({total_piles})", 'WARNING')
            else:
                log_validation_result(log_callback, "Pile Distribution", True, 
                                    f"All {total_piles} piles distributed correctly")
            
            # Calculate distribution statistics
            if load_per_pile_data:
                loads_per_pile = [data[2] for data in load_per_pile_data if data[2] > 0]
                if loads_per_pile:
                    avg_load_per_pile = sum(loads_per_pile) / len(loads_per_pile)
                    min_load_per_pile = min(loads_per_pile)
                    max_load_per_pile = max(loads_per_pile)
                    log_performance_metric(log_callback, "Average Load per Pile", avg_load_per_pile, "kN/pile")
                    log_performance_metric(log_callback, "Load per Pile Range", 
                                         f"{min_load_per_pile:.1f} to {max_load_per_pile:.1f}", "kN/pile")
            
            enhanced_log(log_callback, "Final pile distribution:", 'INFO')
            enhanced_log(log_callback, f"Total piles distributed: {total_distributed} (target: {total_piles})", 'INFO')
            
            log_function_exit(log_callback, "calculate_sub_cluster_pile_distribution", 
                            f"{total_distributed} piles distributed")
            return distribution
            
        except Exception as e:
            log_error_with_context(log_callback, e, "calculate_sub_cluster_pile_distribution")
            raise GroupingError(f"Pile distribution calculation failed: {str(e)}") from e

