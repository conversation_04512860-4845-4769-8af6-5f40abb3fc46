# Foundation Automation - SAFE API Integration Package

The safe_api package provides comprehensive integration with SAFE (Structural Analysis & Design) software, enabling automated foundation analysis and design through a modular interface.

## Package Structure

The package is organized into several specialized modules:

### Core Modules

1. `__init__.py`
   - Package initialization
   - Exports main API functions
   - Defines package interface

2. `main.py`
   - Main orchestration logic
   - Implements:
     - `automate_build_fem`: FEM model building
     - `automate_foundation_design`: Foundation design automation
     - `automate_complete_workflow`: Complete workflow automation
     - `AutomationLogger`: Logging functionality

### SAFE Integration Modules

1. `safe_environment.py`
   - SAFE API environment setup
   - Implements:
     - Environment configuration
     - SAFE initialization
     - Resource management

2. `safe_connection.py`
   - SAFE connection management
   - Implements:
     - Connection setup
     - Session management
     - Error handling

3. `safe22_api.py`
   - SAFE 22 specific API implementation
   - Implements:
     - SAFE 22 data extraction
     - Model manipulation
     - Analysis automation

### Data Processing Modules

1. `data_processor.py`
   - Data extraction and processing
   - Implements:
     - SAFE 22 dataframe extraction
     - Point object connectivity
     - Frame assignments
     - Joint displacements
     - Element forces
     - Load combinations

2. `database_manager.py`
   - Database operations
   - Implements:
     - SQLite database operations
     - Dataframe saving
     - Database path management

### Model Management Modules

1. `models.py`
   - Model initialization and analysis
   - Implements:
     - File path management
     - Model initialization
     - Analysis import and run
     - Model setup
     - SAP model object handling

2. `config.py`
   - Configuration management
   - Implements:
     - SAFE settings
     - API configuration
     - Environment variables

3. `ref.py`
   - Reference data and constants
   - Implements:
     - SAFE constants
     - Reference data
     - Configuration values

## Key Functions

### Main Automation
- `automate_build_fem`: Automate FEM model building
- `automate_foundation_design`: Automate foundation design
- `automate_complete_workflow`: Complete workflow automation

### SAFE Connection
- `setup_safe_connection`: SAFE connection setup
  - Input: Connection parameters
  - Output: SAFE connection object
  - Features: Error handling, session management

### Data Processing
- `extract_safe22_dataframe`: Extract SAFE 22 data
  - Input: Model data
  - Output: Processed dataframes
  - Features: Data validation, processing

- `process_point_object_connectivity`: Process point connectivity
  - Input: Point data
  - Output: Connectivity matrix
  - Features: Topology analysis

- `process_frame_assignments_local_axes`: Process frame assignments
  - Input: Frame data
  - Output: Local axes assignments
  - Features: Axis transformation

### Database Operations
- `save_df_to_sqlite`: Save dataframe to SQLite
  - Input: Dataframe, table name
  - Output: Database operation
  - Features: Data validation

- `save_df_to_database`: Save to database
  - Input: Dataframe, connection
  - Output: Database operation
  - Features: Transaction handling

### Model Operations
- `initialize_model`: Model initialization
  - Input: Model parameters
  - Output: Initialized model
  - Features: Model setup

- `import_and_run_analysis`: Run analysis
  - Input: Analysis parameters
  - Output: Analysis results
  - Features: Error handling

## Usage Examples

### Basic SAFE Connection
```python
from safe_api.safe_connection import setup_safe_connection

# Setup SAFE connection
safe_connection = setup_safe_connection(
    safe_version='22',
    timeout=30,
    log_level='INFO'
)
```

### Complete Workflow Automation
```python
from safe_api.main import automate_complete_workflow

# Run complete workflow
workflow_results = automate_complete_workflow(
    input_data=input_data,
    analysis_parameters=analysis_parameters,
    output_path=output_path
)
```

## Best Practices

1. Use proper error handling
2. Validate input data
3. Follow SAFE API conventions
4. Use logging for debugging
5. Handle resources properly
6. Use configuration management

## Error Handling

The package includes error handling for:
- SAFE connection failures
- Data processing errors
- Model initialization failures
- Analysis errors
- Database operations
- Resource management

## Integration Points

This package integrates with:
- Foundation Automation core system
- SAFE software
- Database systems
- Logging system
- Configuration management

## Dependencies

The package relies on external dependencies:
- pythonnet: SAFE API integration
- pandas: Data handling
- sqlite3: Database operations
- logging: System logging
- numpy: Numerical calculations

## Version

Current version: 1.0.0

## Related Documentation

For more detailed documentation on related packages:
- [build_fem/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/build_fem/README.md:0:0-0:0): FEM model building
- [design_fdn/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/design_fdn/README.md:0:0-0:0): Foundation design
- [config/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/config/README.md:0:0-0:0): Configuration management

## Notes

1. All API functions include comprehensive error handling
2. Data validation is performed at all stages
3. Logging is implemented for debugging and tracking
4. Resource management is handled automatically
5. SAFE 22 specific features are supported
6. Modular design allows for easy extension
