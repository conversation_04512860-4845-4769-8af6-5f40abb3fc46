﻿"""
Genetic Algorithm Fitness Evaluator for Pile Layout Optimization

This module provides fitness evaluation functions for genetic algorithms used in
complex pile layout generation (Case 4). It evaluates pile layouts based on
multiple objectives: safety, geometry, spacing, and loading.

MAIN PURPOSE: Fitness Function for Genetic Algorithms
- Evaluates pile layout solutions for multi-objective optimization
- Calculates safety metrics (pile utilization, load distribution)
- Evaluates geometric quality (centroid alignment, pile count)
- As<PERSON><PERSON> spacing constraints and distribution
- Provides loading-based optimization scores

Used by: layout_engine.py for Case 4 complex layout generation
Uses: Scientific libraries (NumPy, SciPy, scikit-learn) for performance

Author: Foundation Automation System
Date: June 14, 2025
"""

import numpy as np
from scipy.spatial import distance_matrix, cKDTree
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from typing import List, Dict, Any, Tuple, Optional, Callable
from dataclasses import dataclass

from ..data_types import Point2D
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_algorithm_step,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)

# Module-level logger configuration
MODULE_NAME = "genetic_fitness"
enhanced_log(None, f"Loading {MODULE_NAME} module with enhanced logging support", 'INFO')


@dataclass
class GeneticAlgorithmConfig:
    """Configuration parameters for genetic algorithm fitness evaluation."""
    # Objective weights
    safety_weight: float = 1.0
    geometric_weight: float = 1.0
    spacing_weight: float = 1.0
    loading_weight: float = 1.0
    
    # Penalty parameters
    count_penalty_base: float = 100.0
    spacing_violation_penalty: float = 200.0
    max_utilization_threshold: float = 1.0
    
    # Enhanced parameters
    use_spatial_indexing: bool = True
    use_clustering: bool = True
    interpolation_method: str = 'linear'  # 'linear', 'cubic', 'nearest'


class GeneticFitnessEvaluator:
    """
    Fitness evaluator for genetic algorithms used in pile layout optimization.
    
    This class provides multi-objective fitness evaluation for genetic algorithms,
    focusing on safety, geometric quality, spacing constraints, and loading optimization.
    
    Used specifically for Case 4 complex layout generation where genetic algorithms
    are employed to find optimal pile arrangements.
    """
    
    def __init__(self,
                 grid_positions: List[Point2D],
                 sub_clusters: Dict[str, Dict[str, Any]],
                 target_pile_count: int,
                 min_spacing: float,
                 struct_sites: List[Tuple[Point2D, float]],
                 pile_capacity: float,
                 loading_heatmap: Optional[Dict[Point2D, float]] = None,
                 config: Optional[GeneticAlgorithmConfig] = None,
                 log_callback: Optional[Callable] = None):

        self.log_callback = log_callback
        log_function_entry(log_callback, "GeneticFitnessEvaluator.__init__",
                          grid_positions_count=len(grid_positions), target_pile_count=target_pile_count,
                          min_spacing=min_spacing, pile_capacity=pile_capacity)

        try:
            self.grid_positions = grid_positions
            self.sub_clusters = sub_clusters
            self.target_pile_count = target_pile_count
            self.min_spacing = min_spacing
            self.struct_sites = struct_sites
            self.pile_capacity = pile_capacity
            self.loading_heatmap = loading_heatmap or {}
            self.config = config or GeneticAlgorithmConfig()

            enhanced_log(log_callback, f"Initializing genetic fitness evaluator with {len(grid_positions)} grid positions", 'INFO')
            enhanced_log(log_callback, f"Target pile count: {target_pile_count}, Min spacing: {min_spacing:.3f}m", 'INFO')
            enhanced_log(log_callback, f"Pile capacity: {pile_capacity:.0f} kN, Struct sites: {len(struct_sites)}", 'INFO')

            # Pre-calculate enhanced data structures with validation
            enhanced_log(log_callback, "Calculating overall load center", 'DEBUG')
            self.overall_load_center = self._calculate_overall_load_center()
            enhanced_log(log_callback, f"Overall load center: ({self.overall_load_center[0]:.3f}, {self.overall_load_center[1]:.3f})", 'DEBUG')

            # Validate and convert struct_sites to arrays with proper error handling
            enhanced_log(log_callback, "Validating and processing structural sites", 'DEBUG')
            try:
                if self.struct_sites:
                    # Validate and filter struct_sites for numeric coordinates
                    valid_struct_sites = []
                    invalid_count = 0
                    for pos, load in self.struct_sites:
                        try:
                            # Handle various coordinate formats
                            if isinstance(pos, (list, tuple)) and len(pos) >= 2:
                                x, y = float(pos[0]), float(pos[1])
                            elif hasattr(pos, '__getitem__'):  # dict-like or other indexable
                                # Skip non-numeric positions
                                invalid_count += 1
                                continue
                            else:
                                invalid_count += 1
                                continue

                            # Validate coordinates are finite numbers
                            if np.isfinite(x) and np.isfinite(y) and isinstance(load, (int, float)) and np.isfinite(load):
                                valid_struct_sites.append(((x, y), float(load)))
                            else:
                                invalid_count += 1
                        except (ValueError, TypeError, IndexError):
                            # Skip invalid coordinates
                            invalid_count += 1
                            continue

                    if valid_struct_sites:
                        self.struct_sites = valid_struct_sites
                        self.struct_array = np.array([pos for pos, _ in valid_struct_sites])
                        self.struct_loads = np.array([load for _, load in valid_struct_sites])
                        enhanced_log(log_callback, f"Processed {len(valid_struct_sites)} valid structural sites, skipped {invalid_count} invalid", 'INFO')
                    else:
                        # No valid struct sites
                        self.struct_sites = []
                        self.struct_array = np.array([]).reshape(0, 2)
                        self.struct_loads = np.array([])
                        enhanced_log(log_callback, f"No valid structural sites found, skipped {invalid_count} invalid", 'WARNING')
                else:
                    self.struct_array = np.array([]).reshape(0, 2)
                    self.struct_loads = np.array([])
                    enhanced_log(log_callback, "No structural sites provided", 'DEBUG')
            except Exception as e:
                # Fallback to empty arrays if there's any error
                enhanced_log(log_callback, f"Error processing structural sites: {str(e)}", 'ERROR')
                self.struct_sites = []
                self.struct_array = np.array([]).reshape(0, 2)
                self.struct_loads = np.array([])

            enhanced_log(log_callback, "Converting grid positions to numpy array", 'DEBUG')
            self.grid_array = np.array(self.grid_positions)

            # Enhanced spatial indexing with validation
            enhanced_log(log_callback, "Setting up spatial indexing", 'DEBUG')
            if (self.config.use_spatial_indexing and
                len(self.struct_sites) > 0 and
                self.struct_array.size > 0 and
                self.struct_array.ndim == 2):
                try:
                    self.struct_kdtree = cKDTree(self.struct_array)
                    self.grid_kdtree = cKDTree(self.grid_array)
                    enhanced_log(log_callback, "Spatial indexing (KDTree) enabled for performance optimization", 'INFO')
                except Exception as e:
                    # Disable spatial indexing if it fails
                    enhanced_log(log_callback, f"Failed to create spatial index: {str(e)}", 'WARNING')
                    self.struct_kdtree = None
                    self.grid_kdtree = None
            else:
                self.struct_kdtree = None
                self.grid_kdtree = None
                enhanced_log(log_callback, "Spatial indexing disabled (insufficient data or disabled in config)", 'DEBUG')

            # Clustering for load distribution analysis
            enhanced_log(log_callback, "Setting up load clustering", 'DEBUG')
            if self.config.use_clustering and len(self.struct_sites) > 5:
                enhanced_log(log_callback, f"Enabling clustering analysis for {len(self.struct_sites)} structural sites", 'INFO')
                self._setup_clustering()
            else:
                self.load_clusters = None
                enhanced_log(log_callback, "Clustering disabled (insufficient sites or disabled in config)", 'DEBUG')

            # Enhanced heatmap interpolation
            enhanced_log(log_callback, "Setting up heatmap interpolation", 'DEBUG')
            if self.loading_heatmap:
                enhanced_log(log_callback, f"Enabling heatmap interpolation with {len(self.loading_heatmap)} data points", 'INFO')
                self._setup_heatmap_interpolation()
            else:
                self.heatmap_interpolator = None
                enhanced_log(log_callback, "Heatmap interpolation disabled (no heatmap data)", 'DEBUG')

            enhanced_log(log_callback, "Genetic fitness evaluator initialization completed successfully", 'INFO')
            log_function_exit(log_callback, "GeneticFitnessEvaluator.__init__", "success")

        except Exception as e:
            enhanced_log(log_callback, f"Error initializing genetic fitness evaluator: {str(e)}", 'ERROR')
            log_function_exit(log_callback, "GeneticFitnessEvaluator.__init__", "error")
            raise
        
    def __call__(self, individual: List[int]) -> Tuple[float, ...]:
        """
        Evaluate an individual (pile layout) and return fitness objectives.

        This is the main fitness function called by genetic algorithms.

        Args:
            individual: Binary list indicating which grid positions are selected as piles

        Returns:
            Tuple of objective values (lower is better for all objectives)
        """
        # Validate individual with safe boolean handling
        try:
            # Safe check for empty individual
            if hasattr(individual, '__len__'):
                if len(individual) == 0:
                    enhanced_log(self.log_callback, "Empty individual received in fitness evaluation", 'WARNING')
                    return (1000.0, 1000.0, 1000.0, 1000.0)
            else:
                enhanced_log(self.log_callback, "Invalid individual type received in fitness evaluation", 'WARNING')
                return (1000.0, 1000.0, 1000.0, 1000.0)
        except Exception:
            enhanced_log(self.log_callback, "Exception during individual validation in fitness evaluation", 'ERROR')
            return (1000.0, 1000.0, 1000.0, 1000.0)
        
        # Check if individual is a numpy array and handle appropriately
        if hasattr(individual, '__len__') and hasattr(individual, 'dtype'):
            # It's likely a numpy array
            try:
                # Convert to list for compatibility
                individual_list = individual.tolist() if hasattr(individual, 'tolist') else list(individual)
            except Exception:
                # Fallback to direct list conversion
                individual_list = [int(x) for x in individual]
        else:
            individual_list = individual
        
        # Get pile positions with enhanced error handling
        try:
            pile_positions = []
            for i, (pos, selected) in enumerate(zip(self.grid_positions, individual_list)):
                # Safe boolean check to avoid array ambiguity
                if hasattr(selected, '__len__') and len(selected) > 1:
                    # If it's an array, use any() to get a boolean
                    is_selected = bool(selected.any()) if hasattr(selected, 'any') else bool(any(selected))
                else:
                    is_selected = bool(selected)
                
                if is_selected:
                    pile_positions.append(pos)
        except Exception as e:
            # Handle any indexing or conversion errors
            return (1000.0, 1000.0, 1000.0, 1000.0)
        
        # Handle empty solutions
        if not pile_positions:
            enhanced_log(self.log_callback, "Empty pile positions in fitness evaluation", 'WARNING')
            return (1000.0, 1000.0, 1000.0, 1000.0)
        
        # Convert to numpy array with error handling
        try:
            pile_array = np.array(pile_positions)
            if pile_array.size == 0 or pile_array.ndim != 2 or pile_array.shape[1] != 2:
                return (1000.0, 1000.0, 1000.0, 1000.0)
        except Exception:
            return (1000.0, 1000.0, 1000.0, 1000.0)
        
        # Log fitness evaluation details for debugging (only for first few evaluations)
        if hasattr(self, '_eval_count'):
            self._eval_count += 1
        else:
            self._eval_count = 1

        if self._eval_count <= 3:  # Log details for first 3 evaluations
            enhanced_log(self.log_callback, f"Fitness evaluation #{self._eval_count}: {len(pile_positions)} piles selected", 'DEBUG')

        # Calculate four objectives using enhanced methods
        try:
            enhanced_log(self.log_callback, "Calculating safety objective", 'DEBUG') if self._eval_count <= 3 else None
            obj1 = self._calculate_safety_objective_enhanced(pile_array)

            enhanced_log(self.log_callback, "Calculating geometric objective", 'DEBUG') if self._eval_count <= 3 else None
            obj2 = self._calculate_geometric_objective_enhanced(pile_array)

            enhanced_log(self.log_callback, "Calculating spacing objective", 'DEBUG') if self._eval_count <= 3 else None
            obj3 = self._calculate_spacing_objective_enhanced(pile_array)

            enhanced_log(self.log_callback, "Calculating loading objective", 'DEBUG') if self._eval_count <= 3 else None
            obj4 = self._calculate_loading_objective_enhanced(pile_positions)

            # Ensure all objectives are finite numbers
            objectives = [obj1, obj2, obj3, obj4]
            for i, obj in enumerate(objectives):
                if not isinstance(obj, (int, float)) or not np.isfinite(obj):
                    objectives[i] = 1000.0

            # Log objective values for first few evaluations
            if self._eval_count <= 3:
                enhanced_log(self.log_callback, f"Objectives: Safety={obj1:.3f}, Geometric={obj2:.3f}, "
                                              f"Spacing={obj3:.3f}, Loading={obj4:.3f}", 'DEBUG')

            # Log performance metrics periodically
            if self._eval_count % 100 == 0:
                log_performance_metric(self.log_callback, "fitness_evaluations_completed", self._eval_count, "evaluations")

            return tuple(objectives)

        except Exception as e:
            # Return high penalty values for any calculation errors
            enhanced_log(self.log_callback, f"Error calculating fitness objectives: {str(e)}", 'ERROR')
            return (1000.0, 1000.0, 1000.0, 1000.0)
    
    def _calculate_overall_load_center(self) -> Point2D:
        """Calculate load-weighted centroid using NumPy operations."""
        log_function_entry(self.log_callback, "_calculate_overall_load_center")

        try:
            if not self.sub_clusters:
                enhanced_log(self.log_callback, "No sub-clusters available for load center calculation", 'WARNING')
                log_function_exit(self.log_callback, "_calculate_overall_load_center", "(0.0, 0.0)")
                return (0.0, 0.0)

            loads = []
            positions = []

            enhanced_log(self.log_callback, f"Processing {len(self.sub_clusters)} sub-clusters for load center", 'DEBUG')
            for cluster_name, cluster_data in self.sub_clusters.items():
                load_points = cluster_data.get('load_points', [])
                enhanced_log(self.log_callback, f"Cluster '{cluster_name}': {len(load_points)} load points", 'DEBUG')
                for x, y, load in load_points:
                    loads.append(load)
                    positions.append((x, y))

            if not loads:
                enhanced_log(self.log_callback, "No load points found in sub-clusters", 'WARNING')
                log_function_exit(self.log_callback, "_calculate_overall_load_center", "(0.0, 0.0)")
                return (0.0, 0.0)

            loads = np.array(loads)
            positions = np.array(positions)

            # Vectorized weighted centroid calculation
            total_load = np.sum(loads)
            enhanced_log(self.log_callback, f"Total load: {total_load:.1f} kN from {len(loads)} load points", 'DEBUG')

            if total_load > 0:
                weighted_centroid = np.sum(positions * loads.reshape(-1, 1), axis=0) / total_load
                result = tuple(weighted_centroid)
                enhanced_log(self.log_callback, f"Load center calculated: ({result[0]:.3f}, {result[1]:.3f})", 'INFO')
                log_function_exit(self.log_callback, "_calculate_overall_load_center", f"({result[0]:.3f}, {result[1]:.3f})")
                return result

            enhanced_log(self.log_callback, "Total load is zero, using origin as load center", 'WARNING')
            log_function_exit(self.log_callback, "_calculate_overall_load_center", "(0.0, 0.0)")
            return (0.0, 0.0)

        except Exception as e:
            enhanced_log(self.log_callback, f"Error calculating overall load center: {str(e)}", 'ERROR')
            log_function_exit(self.log_callback, "_calculate_overall_load_center", "error")
            return (0.0, 0.0)
    
    def _setup_clustering(self):
        """Setup clustering for load distribution analysis using scikit-learn."""
        log_function_entry(self.log_callback, "_setup_clustering")

        try:
            if not self.struct_sites:
                enhanced_log(self.log_callback, "No structural sites available for clustering", 'DEBUG')
                self.load_clusters = None
                log_function_exit(self.log_callback, "_setup_clustering", "no sites")
                return

            enhanced_log(self.log_callback, f"Setting up clustering for {len(self.struct_sites)} structural sites", 'INFO')

            # Prepare data for clustering
            positions = np.array([pos for pos, _ in self.struct_sites])
            loads = np.array([load for _, load in self.struct_sites])
            enhanced_log(self.log_callback, f"Load range: {np.min(loads):.1f} to {np.max(loads):.1f} kN", 'DEBUG')

            # Normalize positions and loads for clustering
            scaler = StandardScaler()
            positions_scaled = scaler.fit_transform(positions)
            loads_scaled = loads.reshape(-1, 1)
            loads_scaled = StandardScaler().fit_transform(loads_scaled).flatten()
            enhanced_log(self.log_callback, "Data normalized for clustering analysis", 'DEBUG')

            # Combine position and load information
            features = np.column_stack([positions_scaled, loads_scaled])

            # Determine optimal number of clusters
            n_clusters = min(5, max(2, len(self.struct_sites) // 3))
            enhanced_log(self.log_callback, f"Using {n_clusters} clusters for load distribution analysis", 'INFO')

            # Perform clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(features)

            self.load_clusters = {
                'labels': cluster_labels,
                'centers': kmeans.cluster_centers_,
                'scaler': scaler
            }

            enhanced_log(self.log_callback, f"Clustering setup completed with {n_clusters} clusters", 'INFO')
            log_function_exit(self.log_callback, "_setup_clustering", f"{n_clusters} clusters")

        except Exception as e:
            enhanced_log(self.log_callback, f"Error setting up clustering: {str(e)}", 'ERROR')
            self.load_clusters = None
            log_function_exit(self.log_callback, "_setup_clustering", "error")
    
    def _setup_heatmap_interpolation(self):
        """Setup heatmap interpolation using SciPy."""
        log_function_entry(self.log_callback, "_setup_heatmap_interpolation")

        try:
            if not self.loading_heatmap:
                enhanced_log(self.log_callback, "No loading heatmap data provided for interpolation", 'DEBUG')
                self.heatmap_interpolator = None
                log_function_exit(self.log_callback, "_setup_heatmap_interpolation", "disabled")
                return
            
            enhanced_log(self.log_callback, f"Setting up heatmap interpolation with {len(self.loading_heatmap)} data points", 'INFO')
            
            # Extract points and values with validation
            points_list = list(self.loading_heatmap.keys())
            values_list = list(self.loading_heatmap.values())
            
            # Validate data points
            valid_points = []
            valid_values = []
            for point, value in zip(points_list, values_list):
                if isinstance(point, (tuple, list)) and len(point) >= 2:
                    try:
                        x, y = float(point[0]), float(point[1])
                        val = float(value)
                        if np.isfinite(x) and np.isfinite(y) and np.isfinite(val):
                            valid_points.append((x, y))
                            valid_values.append(val)
                    except (ValueError, TypeError):
                        continue
            
            if not valid_points:
                enhanced_log(self.log_callback, "No valid heatmap data points found", 'WARNING')
                self.heatmap_interpolator = None
                log_function_exit(self.log_callback, "_setup_heatmap_interpolation", "no_valid_data")
                return
            
            # Store for interpolation
            points = np.array(valid_points)
            values = np.array(valid_values)
            
            self.heatmap_points = points
            self.heatmap_values = values
            self.heatmap_interpolator = True  # Flag that interpolation is available
            
            # Log heatmap statistics
            enhanced_log(self.log_callback, f"Heatmap interpolation setup completed with {len(valid_points)} valid points", 'INFO')
            log_calculation_result(self.log_callback, "heatmap_value_range", f"{np.min(values):.3f} to {np.max(values):.3f}", "loading_units")
            
            log_function_exit(self.log_callback, "_setup_heatmap_interpolation", "success")

        except Exception as e:
            enhanced_log(self.log_callback, f"Error setting up heatmap interpolation: {str(e)}", 'ERROR')
            self.heatmap_interpolator = None
            log_function_exit(self.log_callback, "_setup_heatmap_interpolation", "error")
    
    def _calculate_utilizations_enhanced(self, pile_array: np.ndarray) -> np.ndarray:
        """Enhanced pile utilization calculation using spatial indexing."""
        log_function_entry(self.log_callback, "_calculate_utilizations_enhanced", pile_count=len(pile_array))
        
        try:
            if not self.struct_sites:
                enhanced_log(self.log_callback, "No structural sites available for utilization calculation", 'DEBUG')
                result = np.zeros(len(pile_array))
                log_function_exit(self.log_callback, "_calculate_utilizations_enhanced", "zero_utilizations")
                return result
            
            enhanced_log(self.log_callback, f"Calculating utilizations for {len(pile_array)} piles using {len(self.struct_sites)} structural sites", 'DEBUG')
            utilizations = np.zeros(len(pile_array))
            
            # Select calculation method based on problem size and configuration
            use_spatial_indexing = self.config.use_spatial_indexing and len(pile_array) > 10
            enhanced_log(self.log_callback, f"Using {'spatial indexing' if use_spatial_indexing else 'direct calculation'} method", 'DEBUG')
        
            # Calculate distance matrix between piles and structural sites
            if use_spatial_indexing:
                enhanced_log(self.log_callback, "Using KDTree spatial indexing for large problem", 'DEBUG')
                # Use KDTree for large problems
                for i, pile_pos in enumerate(pile_array):
                    distances = np.linalg.norm(self.struct_array - pile_pos, axis=1)
                    weights = 1.0 / (distances + 0.1)
                    weights /= np.sum(weights)
                    utilizations[i] = np.sum(weights * self.struct_loads) / self.pile_capacity
            else:
                enhanced_log(self.log_callback, "Using direct calculation method", 'DEBUG')
                # Direct calculation for smaller problems
                for i, pile_pos in enumerate(pile_array):
                    total_load = 0.0
                    for struct_pos, load in self.struct_sites:
                        distance = np.linalg.norm(np.array(pile_pos) - np.array(struct_pos))
                        influence = 1.0 / (distance + 0.1)
                        total_load += load * influence
                    utilizations[i] = total_load / self.pile_capacity
            
            # Log utilization statistics
            max_util = np.max(utilizations) if len(utilizations) > 0 else 0.0
            mean_util = np.mean(utilizations) if len(utilizations) > 0 else 0.0
            over_util_count = np.sum(utilizations > 1.0) if len(utilizations) > 0 else 0
            
            enhanced_log(self.log_callback, f"Utilization statistics: max={max_util:.3f}, mean={mean_util:.3f}, over-utilized={over_util_count}", 'DEBUG')
            log_calculation_result(self.log_callback, "max_pile_utilization", max_util)
            
            if over_util_count > 0:
                enhanced_log(self.log_callback, f"WARNING: {over_util_count} piles exceed capacity", 'WARNING')
            
            log_function_exit(self.log_callback, "_calculate_utilizations_enhanced", f"max_util={max_util:.3f}")
            return utilizations
            
        except Exception as e:
            enhanced_log(self.log_callback, f"Error calculating pile utilizations: {str(e)}", 'ERROR')
            result = np.zeros(len(pile_array))
            log_function_exit(self.log_callback, "_calculate_utilizations_enhanced", "error")
            return result
    
    def _calculate_safety_objective_enhanced(self, pile_array: np.ndarray) -> float:
        """Enhanced safety objective using advanced statistical measures."""
        log_function_entry(self.log_callback, "_calculate_safety_objective_enhanced", pile_count=len(pile_array))
        
        try:
            enhanced_log(self.log_callback, "Calculating enhanced safety objective with statistical measures", 'DEBUG')
            
            # Get pile utilizations
            utilizations = self._calculate_utilizations_enhanced(pile_array)
            
            if len(utilizations) == 0:
                enhanced_log(self.log_callback, "No utilizations available for safety calculation", 'WARNING')
                log_function_exit(self.log_callback, "_calculate_safety_objective_enhanced", "1000.0")
                return 1000.0
            
            # Over-utilization penalty with exponential scaling
            over_util_mask = utilizations > 1.0
            if np.any(over_util_mask):
                over_utilizations = utilizations[over_util_mask] - 1.0
                over_util_penalty = np.sum(over_utilizations ** 2) * 500.0
                enhanced_log(self.log_callback, f"Over-utilization detected: {np.sum(over_util_mask)} piles, penalty={over_util_penalty:.1f}", 'WARNING')
            else:
                over_util_penalty = 0.0
                enhanced_log(self.log_callback, "No over-utilization detected", 'DEBUG')
            
            # Statistical measures for utilization distribution
            util_mean = np.mean(utilizations)
            util_std = np.std(utilizations)
            util_max = np.max(utilizations)
            
            enhanced_log(self.log_callback, f"Utilization statistics: mean={util_mean:.3f}, std={util_std:.3f}, max={util_max:.3f}", 'DEBUG')
            
            # Coefficient of variation penalty (prefer uniform distribution)
            cv_penalty = (util_std / (util_mean + 1e-6)) * 100.0
            log_calculation_result(self.log_callback, "coefficient_of_variation_penalty", cv_penalty)
            
            # Maximum utilization penalty with smooth transition
            max_util_penalty = np.maximum(0, util_max - 0.9) ** 2 * 150.0
            if max_util_penalty > 0:
                enhanced_log(self.log_callback, f"High utilization penalty: max_util={util_max:.3f}, penalty={max_util_penalty:.1f}", 'DEBUG')
            
            # Robustness measure (penalize solutions close to failure)
            safety_margin = 1.0 - util_max
            robustness_penalty = np.maximum(0, 0.1 - safety_margin) * 300.0
            
            # Constraint checks
            log_constraint_check(self.log_callback, "max_utilization", util_max, 1.0, util_max <= 1.0)
            log_constraint_check(self.log_callback, "safety_margin", safety_margin, 0.1, safety_margin >= 0.1)
            
            # Total safety objective
            total_safety_objective = over_util_penalty + cv_penalty + max_util_penalty + robustness_penalty
            
            enhanced_log(self.log_callback, f"Safety objective components: over_util={over_util_penalty:.1f}, cv={cv_penalty:.1f}, "
                                          f"max_util={max_util_penalty:.1f}, robustness={robustness_penalty:.1f}", 'DEBUG')
            
            log_calculation_result(self.log_callback, "total_safety_objective", total_safety_objective)
            log_function_exit(self.log_callback, "_calculate_safety_objective_enhanced", f"objective={total_safety_objective:.3f}")
            
            return total_safety_objective
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "calculating safety objective")
            log_function_exit(self.log_callback, "_calculate_safety_objective_enhanced", "error")
            return 1000.0
    
    def _calculate_geometric_objective_enhanced(self, pile_array: np.ndarray) -> float:
        """Enhanced geometric objective using clustering and spatial analysis."""
        log_function_entry(self.log_callback, "_calculate_geometric_objective_enhanced", pile_count=len(pile_array))
        
        try:
            enhanced_log(self.log_callback, "Calculating enhanced geometric objective with centroid and distribution analysis", 'DEBUG')
            
            # Centroid deviation with weighted importance
            pile_centroid = np.mean(pile_array, axis=0)
            centroid_deviation = np.linalg.norm(pile_centroid - self.overall_load_center)
            
            enhanced_log(self.log_callback, f"Pile centroid: ({pile_centroid[0]:.3f}, {pile_centroid[1]:.3f})", 'DEBUG')
            enhanced_log(self.log_callback, f"Load center: ({self.overall_load_center[0]:.3f}, {self.overall_load_center[1]:.3f})", 'DEBUG')
            log_calculation_result(self.log_callback, "centroid_deviation", centroid_deviation, "meters")
            
            # Enhanced pile count penalty with asymmetric weighting
            pile_count = len(pile_array)
            count_deviation = pile_count - self.target_pile_count
            
            enhanced_log(self.log_callback, f"Pile count: {pile_count}, target: {self.target_pile_count}, deviation: {count_deviation}", 'DEBUG')
            
            if count_deviation > 0:
                # Penalty for too many piles (waste)
                count_penalty = count_deviation * self.config.count_penalty_base * 1.5
                enhanced_log(self.log_callback, f"Too many piles: penalty={count_penalty:.1f}", 'DEBUG')
            else:
                # Higher penalty for too few piles (safety risk)
                count_penalty = abs(count_deviation) * self.config.count_penalty_base * 2.0
                enhanced_log(self.log_callback, f"Too few piles: penalty={count_penalty:.1f}", 'WARNING')
            
            # Constraint check for pile count
            log_constraint_check(self.log_callback, "pile_count_deviation", abs(count_deviation), 
                               self.target_pile_count * 0.2, abs(count_deviation) <= self.target_pile_count * 0.2)
            
            # Spatial distribution quality using clustering
            distribution_penalty = 0.0
            if len(pile_array) > 3:
                enhanced_log(self.log_callback, "Calculating spatial distribution uniformity", 'DEBUG')
                # Calculate spatial distribution uniformity
                center = np.mean(pile_array, axis=0)
                distances_from_center = np.linalg.norm(pile_array - center, axis=1)
                distribution_penalty = np.std(distances_from_center) * 10.0
                
                enhanced_log(self.log_callback, f"Distribution analysis: mean_distance={np.mean(distances_from_center):.3f}, "
                                              f"std_distance={np.std(distances_from_center):.3f}", 'DEBUG')
                log_calculation_result(self.log_callback, "distribution_penalty", distribution_penalty)
            else:
                enhanced_log(self.log_callback, "Insufficient piles for distribution analysis", 'DEBUG')
            
            # Total geometric objective
            total_geometric_objective = centroid_deviation * 25.0 + count_penalty + distribution_penalty
            
            enhanced_log(self.log_callback, f"Geometric objective components: centroid={centroid_deviation * 25.0:.1f}, "
                                          f"count={count_penalty:.1f}, distribution={distribution_penalty:.1f}", 'DEBUG')
            
            log_calculation_result(self.log_callback, "total_geometric_objective", total_geometric_objective)
            log_function_exit(self.log_callback, "_calculate_geometric_objective_enhanced", f"objective={total_geometric_objective:.3f}")
            
            return total_geometric_objective
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "calculating geometric objective")
            log_function_exit(self.log_callback, "_calculate_geometric_objective_enhanced", "error")
            return 1000.0
    
    def _calculate_spacing_objective_enhanced(self, pile_array: np.ndarray) -> float:
        """Enhanced spacing objective using efficient distance calculations."""
        if len(pile_array) <= 1:
            return 0.0
        
        try:
            # Efficient distance matrix calculation
            distances = distance_matrix(pile_array, pile_array)
            np.fill_diagonal(distances, np.inf)
            
            # Minimum spacing analysis
            min_distances = np.min(distances, axis=1)
            min_overall = np.min(min_distances)
            
            # Critical spacing violation with exponential penalty
            spacing_penalty = 0.0
            if min_overall < self.min_spacing:
                violation = self.min_spacing - min_overall
                spacing_penalty = (violation ** 2) * self.config.spacing_violation_penalty
            
            # Spacing distribution analysis
            target_spacing = self.min_spacing * 1.4
            spacing_deviations = np.abs(min_distances - target_spacing)
            spacing_uniformity = np.mean(spacing_deviations) * 0.2
            
            # Density gradient penalty (avoid clustering)
            density_variance = 0.0
            if len(pile_array) > 5:
                # Calculate local density variations with safe boolean handling
                local_densities = []
                threshold_distance = self.min_spacing * 3.0
                
                for i, pile_pos in enumerate(pile_array):
                    nearby_distances = distances[i]
                    # Safe boolean array handling - use explicit comparison and sum
                    nearby_mask = nearby_distances < threshold_distance
                    nearby_count = int(np.sum(nearby_mask))  # Explicit conversion to int
                    local_densities.append(nearby_count)
                
                if local_densities:  # Only calculate variance if we have data
                    density_variance = np.var(local_densities) * 5.0
            
            result = spacing_penalty + spacing_uniformity + density_variance
            
            # Ensure result is a finite scalar
            if not np.isfinite(result):
                return 1000.0  # Return high penalty for invalid results
            
            return float(result)
            
        except Exception as e:
            # Return high penalty for any calculation errors
            return 1000.0
    
    def _calculate_loading_objective_enhanced(self, pile_positions: List[Point2D]) -> float:
        """Enhanced loading objective using interpolation and clustering."""
        log_function_entry(self.log_callback, "_calculate_loading_objective_enhanced", pile_count=len(pile_positions))
        
        try:
            if not self.loading_heatmap:
                enhanced_log(self.log_callback, "No loading heatmap available for loading objective calculation", 'DEBUG')
                log_function_exit(self.log_callback, "_calculate_loading_objective_enhanced", "0.0")
                return 0.0
            
            enhanced_log(self.log_callback, f"Calculating loading objective for {len(pile_positions)} piles using heatmap", 'DEBUG')
            pile_array = np.array(pile_positions)
            
            # Enhanced heatmap value calculation
            if self.heatmap_interpolator:
                enhanced_log(self.log_callback, "Using heatmap interpolation for better accuracy", 'DEBUG')
                # Use interpolation for better accuracy
                heatmap_values = []
                for i, pile_pos in enumerate(pile_positions):
                    value = self._get_heatmap_value_enhanced(pile_pos)
                    heatmap_values.append(value)
                    if i < 3:  # Log first few values for debugging
                        enhanced_log(self.log_callback, f"Pile {i} at {pile_pos}: heatmap_value={value:.3f}", 'DEBUG')
                
                total_heatmap_value = sum(heatmap_values)
                enhanced_log(self.log_callback, f"Interpolation method: total_heatmap_value={total_heatmap_value:.3f}", 'DEBUG')
            else:
                enhanced_log(self.log_callback, "Using direct heatmap lookup", 'DEBUG')
                # Direct lookup
                heatmap_values = [self.loading_heatmap.get(pos, 0.0) for pos in pile_positions]
                total_heatmap_value = sum(heatmap_values)
                enhanced_log(self.log_callback, f"Direct lookup method: total_heatmap_value={total_heatmap_value:.3f}", 'DEBUG')
            
            # Normalize and evaluate
            if hasattr(self, 'heatmap_values') and hasattr(self.heatmap_values, 'size') and self.heatmap_values.size > 0:
                max_possible = len(pile_positions) * np.max(self.heatmap_values)
            else:
                max_possible = 1.0  # Fallback value
            
            enhanced_log(self.log_callback, f"Loading optimization: total_value={total_heatmap_value:.3f}, max_possible={max_possible:.3f}", 'DEBUG')
            
            if max_possible > 0:
                # Convert to minimization objective (lower is better)
                loading_objective = (max_possible - total_heatmap_value) / max_possible * 100.0
                
                # Validate result
                if not np.isfinite(loading_objective):
                    enhanced_log(self.log_callback, "Invalid loading objective calculated, using fallback", 'WARNING')
                    loading_objective = 0.0
                
                log_calculation_result(self.log_callback, "loading_objective", loading_objective, "percent")
                log_function_exit(self.log_callback, "_calculate_loading_objective_enhanced", f"objective={loading_objective:.3f}")
                return loading_objective
            
            enhanced_log(self.log_callback, "Max possible value is zero, returning 0.0", 'DEBUG')
            log_function_exit(self.log_callback, "_calculate_loading_objective_enhanced", "0.0")
            return 0.0
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "calculating loading objective")
            log_function_exit(self.log_callback, "_calculate_loading_objective_enhanced", "error")
            return 0.0
    
    def _get_heatmap_value_enhanced(self, pile_pos: Point2D) -> float:
        """Enhanced heatmap value lookup using spatial indexing."""
        try:
            # Direct lookup first
            if pile_pos in self.loading_heatmap:
                value = self.loading_heatmap[pile_pos]
                enhanced_log(self.log_callback, f"Direct heatmap lookup at {pile_pos}: {value:.3f}", 'DEBUG')
                return value
            
            # Use KDTree for efficient nearest neighbor if available
            if hasattr(self, 'heatmap_kdtree') and self.heatmap_kdtree is not None:
                distance, index = self.heatmap_kdtree.query(pile_pos)
                value = self.heatmap_values[index]
                enhanced_log(self.log_callback, f"KDTree heatmap lookup at {pile_pos}: {value:.3f} (distance={distance:.3f})", 'DEBUG')
                return value
            
            # Use efficient vectorized nearest neighbor
            if hasattr(self, 'heatmap_points') and self.heatmap_points is not None and len(self.heatmap_points) > 0:
                distances = np.linalg.norm(self.heatmap_points - np.array(pile_pos), axis=1)
                closest_index = np.argmin(distances)
                value = self.heatmap_values[closest_index]
                closest_distance = distances[closest_index]
                enhanced_log(self.log_callback, f"Nearest neighbor heatmap lookup at {pile_pos}: {value:.3f} (distance={closest_distance:.3f})", 'DEBUG')
                return value
            
            enhanced_log(self.log_callback, f"No heatmap data available for position {pile_pos}, returning 0.0", 'DEBUG')
            return 0.0
            
        except Exception as e:
            enhanced_log(self.log_callback, f"Error in heatmap value lookup at {pile_pos}: {str(e)}", 'ERROR')
            return 0.0


def collect_structural_sites(sub_clusters: Dict[str, Dict[str, Any]], log_callback: Optional[Callable] = None) -> List[Tuple[Point2D, float]]:
    """
    Collect all structural element locations from sub-clusters and associate them with loads.
    Enhanced with NumPy operations for better performance.
    
    Args:
        sub_clusters: Dictionary of sub-cluster data containing structural elements and load points
        log_callback: Optional callback function for logging
        
    Returns:
        List of tuples containing structural site positions and their associated loads
    """
    log_function_entry(log_callback, "collect_structural_sites", cluster_count=len(sub_clusters))
    
    try:
        sites: List[Tuple[Point2D, float]] = []
        total_processed_clusters = 0
        total_columns = 0
        total_walls = 0
        total_wall_segments = 0
        total_load_distributed = 0.0
        
        enhanced_log(log_callback, f"Processing {len(sub_clusters)} sub-clusters for structural site collection", 'INFO')
        
        for cluster_name, cluster_data in sub_clusters.items():
            enhanced_log(log_callback, f"Processing cluster '{cluster_name}'", 'DEBUG')
            
            # Calculate total load for this cluster
            load_points = cluster_data.get("load_points", [])
            total_load = sum(l for *_, l in load_points)
            enhanced_log(log_callback, f"Cluster '{cluster_name}': {len(load_points)} load points, total load: {total_load:.1f} kN", 'DEBUG')
            
            elements = cluster_data.get("elements", {})
            columns = elements.get("columns", [])
            walls = elements.get("walls", [])
            
            n_col = len(columns)
            total_columns += n_col
            total_walls += len(walls)
            
            enhanced_log(log_callback, f"Cluster '{cluster_name}': {n_col} columns, {len(walls)} walls", 'DEBUG')
            
            # Enhanced wall segmentation using NumPy
            wall_points: List[Tuple[float, float]] = []
            for wall_idx, wall in enumerate(walls):
                try:
                    # WallData is (name, points_list, base_level)
                    name, points_list, base_level = wall
                    if len(points_list) >= 2:
                        x1, y1 = points_list[0]  # First point
                        x2, y2 = points_list[-1]  # Last point
                        
                        # Calculate wall length
                        wall_length = np.linalg.norm([x2-x1, y2-y1])
                        
                        # Simple segmentation - can be enhanced with NumPy for complex walls
                        n_segments = max(1, int(wall_length / 2.0))
                        enhanced_log(log_callback, f"Wall '{name}': length={wall_length:.2f}m, segments={n_segments}", 'DEBUG')
                        
                        for i in range(n_segments):
                            t = (i + 0.5) / n_segments
                            x = x1 + t * (x2 - x1)
                            y = y1 + t * (y2 - y1)
                            wall_points.append((x, y))
                            total_wall_segments += 1
                    else:
                        enhanced_log(log_callback, f"Wall '{name}' has insufficient points ({len(points_list)}), skipping", 'WARNING')
                        
                except Exception as e:
                    enhanced_log(log_callback, f"Error processing wall {wall_idx} in cluster '{cluster_name}': {str(e)}", 'ERROR')
                    continue
            
            n_wall_pts = len(wall_points)
            n_sites = n_col + n_wall_pts
            
            if n_sites == 0:
                enhanced_log(log_callback, f"Cluster '{cluster_name}': no structural sites found, skipping", 'WARNING')
                continue
                
            load_per_site = total_load / n_sites
            enhanced_log(log_callback, f"Cluster '{cluster_name}': {n_sites} total sites, {load_per_site:.2f} kN per site", 'DEBUG')
            
            # Add column sites
            for i, col in enumerate(columns):
                try:
                    # ColumnData is (name, x, y, base_level)
                    name, x, y, base_level = col
                    pos = (float(x), float(y))
                    sites.append((pos, load_per_site))
                    total_load_distributed += load_per_site
                except Exception as e:
                    enhanced_log(log_callback, f"Error processing column {i} in cluster '{cluster_name}': {str(e)}", 'ERROR')
                    continue
            
            # Add wall sites
            for pt in wall_points:
                try:
                    sites.append((pt, load_per_site))
                    total_load_distributed += load_per_site
                except Exception as e:
                    enhanced_log(log_callback, f"Error adding wall point in cluster '{cluster_name}': {str(e)}", 'ERROR')
                    continue
            
            total_processed_clusters += 1
        
        # Log collection summary
        enhanced_log(log_callback, f"Structural site collection completed: {len(sites)} sites from {total_processed_clusters} clusters", 'INFO')
        enhanced_log(log_callback, f"Site breakdown: {total_columns} columns, {total_wall_segments} wall segments from {total_walls} walls", 'INFO')
        log_calculation_result(log_callback, "total_load_distributed", total_load_distributed, "kN")
        log_validation_result(log_callback, "structural_sites_collection", len(sites) > 0, f"{len(sites)} sites collected")
        
        log_function_exit(log_callback, "collect_structural_sites", f"{len(sites)} sites")
        return sites
        
    except Exception as e:
        log_error_with_context(log_callback, e, "collecting structural sites")
        log_function_exit(log_callback, "collect_structural_sites", "error")
        return []


# Factory function for creating fitness evaluators
def create_genetic_fitness_evaluator(**kwargs):
    """
    Create a genetic fitness evaluator for pile layout optimization.
    
    Args:
        **kwargs: Arguments to pass to the evaluator constructor
    
    Returns:
        GeneticFitnessEvaluator instance
    """
    return GeneticFitnessEvaluator(**kwargs)


# NOTE: ZERO FALLBACK POLICY ENFORCED - All legacy fallbacks permanently removed.
# Use GeneticFitnessEvaluator and GeneticAlgorithmConfig directly for all fitness evaluation needs.

