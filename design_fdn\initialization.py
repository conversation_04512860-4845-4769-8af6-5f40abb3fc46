from math import pi

import pandas as pd


def init_excel_bp_rebar(file_paths, excel_inputs):
    # initialize the Excel input of bored pile rebar and export the Excel file to file path
    df_pile_bp = excel_inputs.BP.copy()

    # part 'A' of bored pile
    header = ['Pile Mark', 'Part', 'Target Utilization (<=1)',
              'Diameter (m)', 'Cover (mm)', 'Rebar Clear Spacing (mm)',
              'Length (m)', 'Fcu (MPa)', 'Fy (MPa)', 'Es (GPa)', 'Fyv (MPa)',
              'Rotational Angle (Empty or Deg)',
              'Column Braced (Empty or "x")', 'Omit Slenderness Moment (Empty or "x")',
              'Allow Angled Neutral Axis (Empty or "x")',
              'Top Condition', 'Bottom Condition',
              'Effective Length Factor (Beta)', 'Code',
              'Links Dia (mm)', 'Links Spacing (mm)', 'Links Legs',
              'Layer 1 Rebar Num', 'Layer 1 Rebar Dia (mm)',
              'Layer 2 Rebar Num', 'Layer 2 Rebar Dia (mm)',
              'Layer 3 Rebar Num', 'Layer 3 Rebar Dia (mm)'
              ]
    df_bp_rebar_a = pd.DataFrame(columns=header)
    df_bp_rebar_a['Pile Mark'] = df_pile_bp['Pile Mark'].copy()
    df_bp_rebar_a['Part'] = 'A'
    df_bp_rebar_a['Target Utilization (<=1)'] = 0.9

    df_bp_rebar_a['Pile Cap Bottom Level (mPD)'] = df_pile_bp['Pile Cap Bottom Level (mPD)']
    df_bp_rebar_a['Target Level (mPD)'] = df_pile_bp['Target Level (mPD)']
    df_bp_rebar_a['Founding Level (mPD)'] = df_pile_bp['Founding Level (mPD)']

    df_bp_rebar_a['Pile Shaft Diameter (m)'] = df_pile_bp['Pile Shaft Diameter (m)']
    df_bp_rebar_a['Pile Socket Diameter (m)'] = df_pile_bp['Pile Socket Diameter (m)']

    df_bp_rebar_a['Diameter (m)'] = df_pile_bp['Pile Shaft Diameter (m)']
    df_bp_rebar_a['Cover (mm)'] = (df_pile_bp['Pile Shaft Diameter (m)'] - df_pile_bp[
        'Pile Socket Diameter (m)']) / 2 * 1000 + 75

    df_bp_rebar_a['Rebar Clear Spacing (mm)'] = 100

    df_bp_rebar_a['Length (m)'] = 1

    df_lookup = pd.DataFrame()
    df_lookup['Pile Mark'] = df_pile_bp['Pile Mark']
    df_lookup['Concrete Grade'] = df_pile_bp['Concrete Grade']
    df_lookup['Fcu (MPa)'] = df_pile_bp['Concrete Grade'].str.replace('C', '').astype(int)
    lookup_dict = df_lookup.set_index('Pile Mark')['Fcu (MPa)'].to_dict()

    df_bp_rebar_a['Fcu (MPa)'] = df_bp_rebar_a['Pile Mark'].map(lookup_dict) * 0.8
    df_bp_rebar_a['Fy (MPa)'] = 500
    df_bp_rebar_a['Es (GPa)'] = 200

    df_bp_rebar_a['Fyv (MPa)'] = 500

    df_bp_rebar_a['Rotational Angle (Empty or Deg)'] = ''
    df_bp_rebar_a['Column Braced (Empty or "x")'] = 'x'
    df_bp_rebar_a['Omit Slenderness Moment (Empty or "x")'] = 'x'
    df_bp_rebar_a['Allow Angled Neutral Axis (Empty or "x")'] = ''
    df_bp_rebar_a['Top Condition'] = '2:Partially Fixed'
    df_bp_rebar_a['Bottom Condition'] = '2:Partially Fixed'
    df_bp_rebar_a['Effective Length Factor (Beta)'] = 1.5
    df_bp_rebar_a['Code'] = 'HK Concrete - 2013'

    df_bp_rebar_a['Links Dia (mm)'] = 12
    df_bp_rebar_a['Links Spacing (mm)'] = 300
    df_bp_rebar_a['Links Legs'] = 2

    df_bp_rebar_a['Layer 1 Rebar Num'] = 6
    df_bp_rebar_a['Layer 1 Rebar Dia (mm)'] = 50
    df_bp_rebar_a['Layer 2 Rebar Num'] = 0
    df_bp_rebar_a['Layer 2 Rebar Dia (mm)'] = 0
    df_bp_rebar_a['Layer 3 Rebar Num'] = 0
    df_bp_rebar_a['Layer 3 Rebar Dia (mm)'] = 0

    df_bp_rebar_a['BP Part Start (m)'] = 0
    df_bp_rebar_a['BP Part Length (m)'] = df_bp_rebar_a['Pile Cap Bottom Level (mPD)'] - df_bp_rebar_a['Target Level (mPD)']


    # copy the rows to add another set with Part 'B' in df_bp_rebar
    df_bp_rebar_b = df_bp_rebar_a.copy()
    df_bp_rebar_b['Part'] = 'B'
    df_bp_rebar_b['Diameter (m)'] = df_pile_bp['Pile Socket Diameter (m)']
    df_bp_rebar_b['Cover (mm)'] = 75
    df_bp_rebar_b['BP Part Start (m)'] = df_bp_rebar_b['Pile Cap Bottom Level (mPD)'] - df_bp_rebar_b['Target Level (mPD)']
    df_bp_rebar_b['BP Part Length (m)'] = df_bp_rebar_b['Target Level (mPD)'] - df_bp_rebar_b['Founding Level (mPD)']

    df_bp_rebar = pd.concat([df_bp_rebar_a, df_bp_rebar_b], ignore_index=True)

    excel_inputs.BPRebar = df_bp_rebar
    return excel_inputs


def init_excel_bp_segment_rebar(file_paths, excel_inputs, safe_mdbs):
    # initialize the Excel input of bored pile rebar and export the Excel file to file path
    df_bp_rebar = excel_inputs.BPRebar.copy()

    df_bp_rebar_a = df_bp_rebar[df_bp_rebar['Part'] == 'A']

    df_element_column = safe_mdbs.ElementForcesColumnsAndBraces.copy()
    df_element_column = df_element_column['Line'].drop_duplicates()

    # split df_element_column['Line'] by '_' and drop the row if the first part is not in df_user_bp_rebar['Pile Mark']
    df_element_column = df_element_column[df_element_column.str.split('_').str[0].isin(df_bp_rebar_a['Pile Mark'])]
    
    df_bp_segment_rebar = pd.DataFrame()
    df_bp_segment_rebar[['Pile Mark', 'Pile Segment']] = df_element_column.str.split('_', expand=True)
    df_bp_segment_rebar['Pile Segment'].loc[df_bp_segment_rebar['Pile Segment'] == 'T'] = '0'
    df_bp_segment_rebar['Pile Segment'].loc[df_bp_segment_rebar['Pile Segment'] == 'B'] = '999'
    df_bp_segment_rebar['Pile Segment'] = df_bp_segment_rebar['Pile Segment'].astype(float)

    df_bp_segment_rebar = df_bp_segment_rebar.sort_values(by=['Pile Mark', 'Pile Segment']).reset_index(drop=True)

    df_bp_segment_rebar = pd.merge(df_bp_segment_rebar, df_bp_rebar_a, on='Pile Mark', how='left')

    # Use vectorized operations for better performance
    condition = df_bp_segment_rebar['Pile Segment'] <= (df_bp_segment_rebar['Pile Cap Bottom Level (mPD)'] - df_bp_segment_rebar['Target Level (mPD)'])
    df_bp_segment_rebar.loc[condition, 'Part'] = 'A'
    df_bp_segment_rebar.loc[~condition, 'Part'] = 'B'

    condition = df_bp_segment_rebar['Part'] == 'A'
    df_bp_segment_rebar.loc[condition,'Diameter (m)'] = df_bp_segment_rebar['Pile Shaft Diameter (m)']
    df_bp_segment_rebar.loc[~condition, 'Diameter (m)'] = df_bp_segment_rebar['Pile Socket Diameter (m)']

    df_bp_segment_rebar.loc[condition, 'Cover (mm)'] = (df_bp_segment_rebar['Pile Shaft Diameter (m)'] - df_bp_segment_rebar[
        'Pile Socket Diameter (m)']) / 2 * 1000 + 75
    df_bp_segment_rebar.loc[~condition, 'Cover (mm)'] = 75


    df_bp_segment_rebar.loc[condition, 'BP Part Start (m)'] = 0
    df_bp_segment_rebar.loc[~condition, 'BP Part Start (m)'] = df_bp_segment_rebar['Pile Cap Bottom Level (mPD)'] - df_bp_segment_rebar['Target Level (mPD)']

    df_bp_segment_rebar.loc[condition, 'BP Part Length (m)'] = df_bp_segment_rebar['Pile Cap Bottom Level (mPD)'] - df_bp_segment_rebar['Target Level (mPD)']
    df_bp_segment_rebar.loc[~condition, 'BP Part Length (m)'] = df_bp_segment_rebar['Target Level (mPD)'] - df_bp_segment_rebar['Founding Level (mPD)']

    df_bp_segment_rebar['Pile Segment'] = df_bp_segment_rebar['Pile Segment'].apply(
        lambda x: str(x) if x % 1 != 0 else int(x))
    df_bp_segment_rebar['Pile Segment'].loc[df_bp_segment_rebar['Pile Segment'] == 0] = 'T'
    df_bp_segment_rebar['Pile Segment'].loc[df_bp_segment_rebar['Pile Segment'] == 999] = 'B'

    area = pi / 4 * (df_bp_segment_rebar['Diameter (m)'] * 1000) ** 2

    a_s = df_bp_segment_rebar['Layer 1 Rebar Num'] * (pi / 4 * df_bp_segment_rebar['Layer 1 Rebar Dia (mm)'] ** 2) + \
          df_bp_segment_rebar['Layer 2 Rebar Num'] * (pi / 4 * df_bp_segment_rebar['Layer 2 Rebar Dia (mm)'] ** 2) + \
          df_bp_segment_rebar['Layer 3 Rebar Num'] * (pi / 4 * df_bp_segment_rebar['Layer 3 Rebar Dia (mm)'] ** 2)

    steel_ratio = a_s / area
    df_bp_segment_rebar['Steel Ratio (%)'] = steel_ratio * 100

    excel_inputs.BPSegmentRebar = df_bp_segment_rebar
    return excel_inputs
