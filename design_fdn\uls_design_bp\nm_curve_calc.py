"""
Ultimate Limit State (ULS) N-M Interaction Curve Calculation for Bored Pile Foundations

This module provides comprehensive calculation capabilities for generating axial force-moment
(N-M) interaction curves for circular reinforced concrete bored pile sections under ultimate
limit state conditions. The calculations are essential for pile foundation design verification
according to structural design codes such as Eurocode 2 (EN 1992-1-1) and related standards.

Key Features:
- Complete N-M interaction curve generation for circular pile cross-sections
- Ultimate limit state capacity calculations for combined axial force and bending moment
- Reinforced concrete stress-strain relationship modeling with material nonlinearity
- Multi-case analysis covering compression, tension, and combined loading scenarios
- Adaptive calculation methodology for varying pile geometries and reinforcement layouts
- Integration with structural design codes for concrete and steel material properties

Author: Foundation-Automation Development Team
Version: Compatible with Eurocode 2 and international reinforced concrete design standards
"""

from math import pi, acos, asin, floor, sqrt
from typing import Tuple, Optional, List

import numpy as np
import pandas as pd
# Use direct imports to avoid circular import
from .circular_column import CircularColumn, calculate_rebar_coordinates


def calculate_rebar_stress(df_rebars: pd.DataFrame, x: float, radius: float, 
                          strain_cu: float, dia: float, f_y: float, e_s: float) -> pd.DataFrame:
    """
    Calculate stress distribution in reinforcement bars for given neutral axis position.
    
    This function determines the stress state of all reinforcement bars in a circular
    pile cross-section based on strain compatibility principles and material constitutive
    relationships. The analysis considers the position of the neutral axis, ultimate
    concrete strain, and steel stress-strain behavior to compute individual bar stresses.

    The calculation process includes:
    - Strain distribution calculation based on plane sections assumption
    - Individual reinforcement bar strain determination from geometric compatibility
    - Steel stress calculation using elastic-perfectly plastic material model
    - Stress limitation according to design yield strength (0.87 * fy)
    - Force and moment contribution calculation for each reinforcement bar
    """    
    # Create working copy of reinforcement data to avoid modifying original DataFrame
    df = df_rebars.copy()
    
    # Calculate individual reinforcement bar cross-sectional areas
    # Using standard circular area formula: A = π/4 × d²
    df['A_si (mm2)'] = pi / 4 * df['Rebar Dia (mm)'] ** 2
    
    # Calculate strain at neutral axis level based on strain compatibility
    # This represents the reference strain for the strain distribution calculation
    if x > -radius:
        # Neutral axis above bottom fiber: standard strain distribution
        strain_s = -strain_cu * (x + radius) / (radius - x)
    elif x < -radius:
        # Neutral axis below bottom fiber: reversed strain distribution
        strain_s = strain_cu * (-x - radius) / (radius - x)
    else:
        # Neutral axis at bottom fiber: zero strain at reference level
        strain_s = 0
    
    df['strain_s'] = strain_s
    
    # Calculate individual bar strains using linear strain distribution
    # Based on plane sections remain plane assumption: ε = ε₀ + κ⋅y
    # where κ is the curvature and y is the distance from neutral axis
    df['strain_g'] = (strain_cu - strain_s) / dia * (df['Y (mm)'] + radius) + strain_s
    
    # Calculate steel stress using elastic-perfectly plastic material model
    # Initial elastic calculation: σ = E⋅ε
    df['stress'] = e_s * df['strain_g']
    
    # Apply yield strength limits according to design code requirements
    # Design yield strength: 0.87 × fy (includes material partial safety factor)
    fy_limit = 0.87 * f_y
    strain_limit = fy_limit / e_s  # Yield strain for stress limitation
    
    # Apply stress limits using vectorized operations for computational efficiency
    # Positive limit for compression, negative limit for tension
    df.loc[df['strain_g'] > strain_limit, 'stress'] = fy_limit
    df.loc[df['strain_g'] < -strain_limit, 'stress'] = -fy_limit
    
    # Calculate derived quantities for force and moment contributions
    # These values are used in the integration for total section capacity
    df['stress*u'] = df['stress'] * df['Y (mm)']  # Stress × lever arm for moment
    df['A_si*stress'] = df['A_si (mm2)'] * df['stress']  # Individual bar force
    df['A_si*stress*u'] = df['A_si (mm2)'] * df['stress'] * df['Y (mm)']  # Individual bar moment
    
    return df


def calculate_nm_point(column: CircularColumn, x: float) -> Tuple[int, float, float]:
    """
    Calculate single point on N-M interaction curve for specified neutral axis position.
    
    This function computes the ultimate capacity (axial force N and bending moment M)
    for a circular reinforced concrete pile section at a specific neutral axis position.
    The calculation integrates concrete and steel contributions using strain compatibility
    and ultimate limit state principles consistent with structural design codes.

    The analysis methodology includes:
    - Case identification based on neutral axis position and stress distribution
    - Concrete contribution calculation using parabolic-rectangular stress block
    - Steel reinforcement contribution from individual bar stress analysis
    - Numerical integration for complex stress distributions in compression zone
    - Force and moment summation for total section capacity at ultimate limit state
    """    
    # Extract material and geometric properties from column definition
    # These values are used throughout the calculation for consistency
    f_cu = column.fcu  # Characteristic concrete compressive strength (N/mm²)
    y_m = column.ym    # Material partial safety factor for concrete
    e_s = column.es    # Steel elastic modulus (N/mm²)
    f_y = column.fy    # Characteristic steel yield strength (N/mm²)
    dia = column.diameter  # Pile diameter (mm)
    radius = column.radius # Pile radius (mm)
    
    # Pre-compute strain parameters according to design code requirements
    # These parameters define the concrete stress-strain relationship
    
    # Dynamic elastic modulus calculation based on concrete strength
    # Formula per structural design codes for concrete behavior modeling
    e_d = (3.46 * (f_cu / y_m) ** 0.5 + 3.21) * 1000
    
    # Ultimate concrete compression strain with high-strength concrete adjustment
    # Standard value 0.0035 reduced for high-strength concrete (fcu ≥ 60 N/mm²)
    strain_cu = 0.0035 - 0.00006 * (f_cu - 60) ** 0.5 if f_cu >= 60 else 0.0035
    
    # Strain at peak stress for parabolic-rectangular stress distribution
    # Critical parameter for defining concrete stress block geometry
    strain_0 = (1.34 * f_cu) / (e_d * y_m)
    
    # Strain ratio for stress distribution calculation
    # Used to determine transition between parabolic and rectangular portions
    strain_r = strain_0 / strain_cu
    
    # Determine analysis case based on neutral axis position
    # Case classification affects calculation methodology and stress integration
    if x <= radius:
        # Calculate neutral plane position for strain distribution analysis
        # This determines the extent of compression zone in the section
        neutral_plane = x + strain_r * (radius - x)
        
        # Case classification based on neutral axis and compression zone extent
        if neutral_plane <= -radius:
            case = 3  # Pure compression - entire section in compression
        elif x < -radius and neutral_plane > -radius:
            case = 2  # Partial compression with modified integration limits
        else:
            case = 1  # Standard combined loading case
        
        # Cases 1 and 2: Combined loading with compression and tension zones
        if case == 1 or case == 2:
            # Optimized calculations with pre-computed terms for efficiency
            # These terms are used repeatedly in stress integration formulas
            term = strain_r + (1 - strain_r) * x / radius
            term_squared = term * term
            sqrt_term = (1 - term_squared) ** 0.5
            
            # Concrete compression force and moment from parabolic stress block
            # Integration of parabolic portion of stress-strain curve
            f_c1 = (0.67 * f_cu / y_m * radius ** 2) * (acos(term) - term * sqrt_term)
            m_c1 = 1.34 * f_cu / (3 * y_m) * radius ** 3 * (sqrt_term ** 3)
            
            # Integration constants for rectangular stress block portion
            # These coefficients define the stress distribution in compression zone
            inv_radius = 1 / radius
            one_minus_x_div_r = 1 - x * inv_radius
            a = -((0.67 * f_cu * strain_cu ** 2) / (y_m * one_minus_x_div_r ** 2 * radius ** 2 * strain_0 ** 2))
            b = (1.34 * f_cu * strain_cu) / (y_m * one_minus_x_div_r * radius * strain_0)
            cx = a
            cy = b - 2 * a * x
            cz = a * x ** 2 - b * x
            
            # Calculate integration limits for stress block analysis
            # Upper and lower bounds for rectangular stress distribution
            u1 = strain_r * radius + (1 - strain_r) * x
            u2 = x if case == 1 else -radius
            
            # Use helper function for numerical integration of complex stress distribution
            # This calculates force and moment contributions from rectangular stress block
            f_u1, m_u1 = _calculate_integral(u1, radius, cx, cy, cz)
            f_u2, m_u2 = _calculate_integral(u2, radius, cx, cy, cz)
            
            # Combine parabolic and rectangular stress block contributions
            f_c2 = f_u1 - f_u2            # Total concrete contribution from both parabolic and rectangular stress blocks
            f_c = f_c1 + f_c2
            m_c2 = m_u1 - m_u2
            m_c = m_c1 + m_c2
            
            # Calculate steel reinforcement contributions using individual bar analysis
            # Generate reinforcement coordinates and analyze stress state for each bar
            df_rebars = calculate_rebar_coordinates(column)
            df_stress = calculate_rebar_stress(df_rebars, x, radius, strain_cu, dia, f_y, e_s)
            
            # Sum individual bar contributions for total steel force and moment
            f_s = df_stress['A_si*stress'].sum()  # Total steel force (N)
            m_s = df_stress['A_si*stress*u'].sum()  # Total steel moment (N⋅mm)
            
            # Combine concrete and steel contributions for total section capacity
            f_total = f_c + f_s  # Total axial force (N)
            m_total = m_c + m_s  # Total bending moment (N⋅mm)
            
        else:  # case == 3: Pure compression condition
            # Entire section in compression at ultimate strain
            # Maximum compression capacity with both concrete and steel contributing
            f_c = 0.67 * f_cu * pi / y_m * radius ** 2  # Maximum concrete compression force
            f_s = 0.87 * f_y * column.total_rebar_area   # Maximum steel compression force
            f_total = f_c + f_s  # Total compression capacity
            m_total = 0  # No moment capacity in pure compression at ultimate strain
            
    else:  # Pure tension condition (neutral axis outside section)
        case = 0
        # Only steel reinforcement contributes in pure tension
        # Concrete tensile strength is neglected in ultimate limit state analysis
        f_total = -0.87 * f_y * column.total_rebar_area  # Total tension capacity (negative)
        m_total = 0  # No moment capacity in pure tension condition
    
    # Convert units for engineering compatibility and return results
    # Force: N to kN, Moment: N⋅mm to kN⋅m for standard structural units
    return case, f_total / 1000, m_total / 1000000


def _calculate_integral(u: float, radius: float, cx: float, cy: float, cz: float) -> Tuple[float, float]:
    """
    Calculate numerical integrals for concrete stress distribution in circular sections.
    
    This helper function performs numerical integration of the concrete stress distribution
    over circular section geometry for N-M interaction curve calculations. The integration
    accounts for the parabolic-rectangular stress block model used in ultimate limit state
    analysis according to structural design codes.

    The integration formulas are derived from:
    - Circular section geometry with radius-based coordinate system
    - Parabolic-rectangular concrete stress distribution model
    - Force and moment equilibrium requirements for section capacity
    - Optimized mathematical expressions for computational efficiency
    """    
    # Calculate intermediate geometric terms for integration formulas
    # These terms appear repeatedly in the analytical integration expressions
    sqrt_r2_minus_u2 = (radius ** 2 - u ** 2) ** 0.5  # √(r² - u²) for circular geometry
    asin_u_r = asin(u / radius)  # arcsin(u/r) for circular arc calculations
    
    # Force contribution calculation using analytical integration over circular area
    # Integration of stress distribution over compression zone area
    f_u = (1 / 12) * (sqrt_r2_minus_u2 * (
        2 * u * (3 * u ** 2 * cx + 4 * u * cy + 6 * cz) - 
        radius ** 2 * (3 * u * cx + 8 * cy)
    ) + 3 * radius ** 2 * asin_u_r * (radius ** 2 * cx + 4 * cz))
    
    # Moment contribution calculation using analytical integration with lever arm
    # Integration of stress distribution × lever arm over compression zone area
    m_u = (1 / 60) * (sqrt_r2_minus_u2 * (
        -16 * radius ** 4 * cx - 
        radius ** 2 * (8 * u ** 2 * cx + 15 * u * cy + 40 * cz) +
        2 * u ** 2 * (12 * u ** 2 * cx + 15 * u * cy + 20 * cz)
    ) + 15 * radius ** 4 * cy * asin_u_r)
    
    return f_u, m_u


def calculate_nm_curve(column: CircularColumn) -> pd.DataFrame:
    """
    Generate complete N-M interaction curve for circular reinforced concrete pile section.
    
    This function creates a comprehensive axial force-moment interaction curve for ultimate
    limit state design verification of circular pile foundations. The curve represents all
    possible combinations of axial force (N) and bending moment (M) that the section can
    sustain at ultimate limit state, essential for foundation design under combined loading.

    The curve generation process includes:
    - Adaptive neutral axis position selection for comprehensive curve coverage
    - Multiple calculation points covering tension, combined, and compression zones
    - Symmetric curve generation for both positive and negative moment directions
    - Integration of concrete and steel contributions at each calculation point
    - Quality control through filtering and validation of calculation results
    """    
    # Extract material constants for curve generation parameter calculation
    f_cu = column.fcu  # Characteristic concrete compressive strength (N/mm²)
    radius = column.radius  # Pile section radius (mm)
    y_m = 1.5  # Material partial safety factor for concrete (typical design value)
    
    # Calculate strain parameters for neutral axis position determination
    # These parameters define the limits of the interaction curve calculation range
    e_d = (3.46 * (f_cu / y_m) ** 0.5 + 3.21) * 1000  # Dynamic modulus for strain calculations
    strain_cu = 0.0035 - 0.00006 * (f_cu - 60) ** 0.5 if f_cu >= 60 else 0.0035  # Ultimate strain
    strain_0 = (1.34 * f_cu) / (e_d * y_m)  # Strain at peak stress for stress block definition
    strain_r = strain_0 / strain_cu  # Strain ratio for transition point calculation
    
    # Calculate starting neutral axis position for curve generation
    # This position represents the transition between different analysis cases
    x_init = (strain_r * radius + radius) / (strain_r - 1)
    x_start = floor(x_init)  # Floor to integer for consistent step calculation
    
    # Determine adaptive step size based on pile geometry for optimal curve resolution
    # Smaller piles require finer resolution, larger piles can use coarser steps
    step = 50 if column.diameter <= 1000 else 100  # Step size in mm
    
    # Generate neutral axis position array for comprehensive curve coverage
    # Range covers from starting position to section boundary plus extension for pure tension
    x_values = np.arange(x_start, radius + step, step)
    
    # Calculate interaction curve points for each neutral axis position
    # Each point represents ultimate capacity for specific loading combination
    nm_points = []
    for x in x_values:
        case, n, m = calculate_nm_point(column, x)
        nm_points.append([case, x, n, m])
    
    # Add pure tension point beyond section boundary for complete curve definition
    # This point represents maximum tension capacity with zero moment
    case, n, m = calculate_nm_point(column, radius + step)
    n_min = n  # Store minimum axial capacity for curve filtering
    nm_points.append([case, radius + step, n, m])
    
    # Create DataFrame from calculated points with descriptive column names
    df = pd.DataFrame(nm_points, columns=['Case', 'x (mm)', 'N (kN)', 'M (kNm)'])
    
    # Filter curve to include only valid capacity combinations
    # Remove points below minimum tension capacity to ensure physically meaningful results
    df = df[df['N (kN)'] >= n_min].reset_index(drop=True)
    
    # Create symmetric curve for negative moment direction
    # Structural sections have symmetric moment capacity about the axial load axis
    df_invert = df.copy().iloc[::-1]  # Reverse order for smooth curve transition
    df_invert['M (kNm)'] = -df_invert['M (kNm)']  # Invert moment values for symmetry
    
    # Combine positive and negative moment portions for complete interaction envelope
    # This creates the full N-M interaction curve covering all possible loading combinations
    df = pd.concat([df, df_invert], ignore_index=True)
    
    # Add pile identification for result tracking and documentation
    # This enables curve association with specific pile elements in foundation design
    df.insert(0, 'Pile Mark', column.segment_mark)
    
    return df
