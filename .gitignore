# =============================================================================
# PYTHON FILES
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*.pyc
*.pyo
*.pyd
*$py.class

# C extensions
*.so
*.dll
*.dylib

# =============================================================================
# PYTHON DISTRIBUTION / PACKAGING
# =============================================================================

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.whl
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# =============================================================================
# BUILD TOOLS & COMPILERS
# =============================================================================

# PyInstaller
# Usually these files are written by a PyInstaller script
*.spec
*.pyzw
*.uproject
pyarmor_runtime_000000/

# Nuitka build artifacts
*.build/
*.dist/
*.onefile-build/
nuitka-crash-report.xml

# Cython debug symbols
cython_debug/

# =============================================================================
# LOGS & INSTALLERS
# =============================================================================

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Application logs
*.log
logs/
log/
*.log.*
run_log.log
gui_debug.log
debug.log
error.log
access.log
application.log

# =============================================================================
# TESTING & COVERAGE
# =============================================================================

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
.nyc_output

# Test databases
test.db
test.sqlite
test_*.db

# =============================================================================
# VIRTUAL ENVIRONMENTS
# =============================================================================

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
pyvenv.cfg

# Conda environments
*.conda
environment.yml
environment.yaml

# pyenv
.python-version

# pipenv
Pipfile.lock

# =============================================================================
# IDE & EDITOR CONFIGURATIONS
# =============================================================================

# PyCharm
/.idea/
*.iml
*.iws
*.ipr
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries
.idea/vcs.xml
.idea/jsLibraryMappings.xml
.idea/datasources.xml
.idea/dataSources.ids
.idea/sqlDataSources.xml
.idea/dynamic.xml
.idea/uiDesigner.xml
.idea/AugmentWebviewStateStore.xml
.idea/Foundation-Automation.iml
.idea/misc.xml

# Ensure PyCharm IDE files are fully ignored
.idea/**

# VS Code
/.vscode/
*.code-workspace
.history/

# Visual Studio
.vs/
*.vsidx
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.vim/
tags

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Eclipse
.project
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# =============================================================================
# DOCUMENTATION & NOTEBOOKS
# =============================================================================

# Jupyter Notebook
.ipynb_checkpoints/
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# Sphinx documentation
docs/_build/
doc/_build/
.buildinfo
.doctrees

# mkdocs documentation
/site

# Jupyter Book
_build/

# =============================================================================
# STATIC ANALYSIS & TYPE CHECKING
# =============================================================================

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static analyzer
.pytype/

# Pylint
.pylintrc
pylint-report.txt

# Bandit
.bandit
bandit-report.json

# =============================================================================
# PYTHON PROJECT TOOLS
# =============================================================================

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# PEP 582
__pypackages__/

# =============================================================================
# CONFIGURATION & SECRETS
# =============================================================================

# Configuration files (personal/sensitive)
config.ini
settings.ini
credentials.json
secrets.yaml
.secrets
local_settings.py
local_config.py

# API Keys and Tokens
.api_keys
*.key
*.pem
*.p12
.token

# Database files
*.db
*.sqlite
*.sqlite3
*.mdb
*.accdb
*.ldb

# SSL certificates
*.crt
*.cer
*.p12
*.pfx

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# FOUNDATION AUTOMATION SPECIFIC FILES
# =============================================================================

# CAD/Engineering files (temporary/backup)
*.dwg.bak
*.dwg~
*.dxf.bak
*.dxf~
*.dwl
*.dwl2
*.dwl2~

# Excel backup files
*.xl*~
*.xls#
*.xlsx#
~$*.xl*

# SAFE/ETABS temporary files
*.$2k
*.lock
*.bak
*.old

# Output/Result files (configure based on your needs)
**/output/
**/results/
**/temp/
**/temporary/
**/cache/

# Generated reports
reports/
output_reports/
generated_reports/

# User-specific data directories
user_data/
local_data/
project_data/

# =============================================================================
# BACKUP & TEMPORARY FILES
# =============================================================================

# General backup files
*.backup
*.bak
*.old
*.orig
*.rej
*.tmp
*.temp

# Autosave files
*.autosave
*.recover

# Archive files (uncomment if not needed in repository)
# *.zip
# *.tar
# *.tar.gz
# *.rar
# *.7z
# *.gz
# *.bz2
# *.xz

# =============================================================================
# DEVELOPMENT & DEBUGGING FILES
# =============================================================================

# Profiling data
*.prof
*.pstats
*.cprof

# Memory dumps
*.dmp
*.mdmp

# Debug files
debug/
*.debug
*.stackdump

# Performance data
*.perf
*.trace

# =============================================================================
# NETWORK & CACHE FILES
# =============================================================================

# Network debugging
*.pcap
*.cap

# Cache directories
.cache/

# Temporary internet files
Temporary Internet Files/

# =============================================================================
# WEB FRAMEWORKS (if applicable)
# =============================================================================

# Flask stuff
instance/
.webassets-cache

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# FastAPI
__pycache__/

# =============================================================================
# DATA & MACHINE LEARNING
# =============================================================================

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# Data files (configure based on your needs)
*.csv
*.xlsx
*.xls
!Library_Steel/SteelSection.xlsx  # Keep this specific file
*.json
*.xml
*.parquet
*.h5
*.hdf5

# Model files
*.pkl
*.pickle
*.joblib
*.model
*.pt
*.pth
*.onnx

# Dataset directories
data/
datasets/
raw_data/
processed_data/

# =============================================================================
# MISCELLANEOUS
# =============================================================================

# Unit test reports
htmlcov/
.coverage
.coverage.*
nosetests.xml
coverage.xml
*.cover

# Translations
*.mo
*.pot

# Scrapy stuff
.scrapy

# PyBuilder
target/
.idea/Foundation-Automation.iml
.idea/misc.xml

# =============================================================================
# DOCKER
# =============================================================================

# Docker files
*.dockerignore
Dockerfile*
docker-compose*.yml
docker-compose*.yaml
docker-compose.override.yml
docker-compose.override.yaml

# Docker build artifacts
.docker/
docker_temp/
docker_cache/

# =============================================================================
# KUBERNETES
# =============================================================================

# Kubernetes files
*.kubeconfig
*.k8s.yaml
*.k8s.yml
kubeconfig.yaml
kubeconfig.yml

# Kubernetes generated files
k8s_temp/
k8s_output/

# =============================================================================
# TERRAFORM
# =============================================================================

# Terraform files
*.tfstate
*.tfstate.*
*.tfvars
*.tfvars.json
.terraform/
terraform.tfstate.backup
terraform.lock.hcl

# =============================================================================
# ADDITIONAL PYTHON FILES
# =============================================================================

# Python profiling and debugging
*.lprof
*.pydevproject

# Python notebook outputs
*.nbconvert.ipynb

# Python virtual environment files
pip-selfcheck.json

# =============================================================================
# ADDITIONAL MISCELLANEOUS FILES
# =============================================================================

# System-generated files
*.pid
*.seed
*.manifest
*.swo
*.swp

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
backup/
backups/
*.bak
*.backup
*.old
*.orig
