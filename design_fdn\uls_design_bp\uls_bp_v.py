"""
Bored Pile Shear Design Module for Ultimate Limit State (ULS) Analysis

This module provides comprehensive shear design capabilities for bored pile foundations 
as part of Ultimate Limit State (ULS) design verification in reinforced concrete 
foundation engineering. It implements advanced analytical methods for evaluating 
shear capacity and designing transverse reinforcement (links/stirrups) for circular 
pile sections subjected to ultimate limit state loading conditions.

The module performs shear design analysis including:
- Concrete shear capacity calculations based on sectional properties and material strengths
- Transverse reinforcement (links) design for shear resistance enhancement
- Critical shear stress evaluation with axial force interaction effects
- Design optimization algorithms for link diameter, spacing, and configuration
- Comprehensive design verification against structural code requirements

Key Features:
- Automated concrete shear strength calculations using established design formulas
- Intelligent transverse reinforcement optimization with constructability constraints
- Vectorized operations for efficient processing of multiple pile elements
- Integration with structural analysis outputs for critical load identification
- Comprehensive design documentation and verification reporting
- Code compliance checking with standard shear design criteria

Author: Foundation Design Automation Team
Version: 1.0
"""

from math import pi, ceil
from typing import Any, Dict, List, Optional, Callable, Tuple

import numpy as np
import pandas as pd

from design_fdn.design_fdn_config import (
    SHEET_BP_SHEAR_CHECK, SHEET_BP_SHEAR_DESIGN, SHEET_BP_ERROR_LOG
)


def check_bp_shear(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Dict[str, Any], 
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Perform comprehensive shear design verification for bored pile foundations.
    
    This function orchestrates the complete shear analysis workflow for bored pile
    elements, including concrete shear capacity calculations, critical force 
    identification, and design verification against ultimate limit state criteria.
    It implements vectorized operations for efficient processing of multiple pile
    elements while maintaining design accuracy and comprehensive documentation.
    
    The analysis workflow includes:
    1. Pile geometry and material property processing from design inputs
    2. Critical shear force identification from structural analysis results
    3. Concrete shear capacity calculations with axial force interaction
    4. Transverse reinforcement adequacy verification
    5. Design documentation and error logging for quality control
    """
    # Extract design input data for shear analysis
    df_bp_shear = excel_inputs.BPRebar
    df_pile = excel_inputs.Pile
    df_pile_forces = excel_outputs.PileULS
    path_excel_output = file_paths.ResultBPShearCheck
    
    # Filter for bored pile (BP) elements only
    # Shear design is specific to pile type and construction method
    df_pile_bp = df_pile[df_pile['Pile Type'] == 'BP']
    
    # Filter ULS force combinations using vectorized operations for efficiency
    # Create search pattern for all BP pile marks to filter relevant force data
    search_pattern = '|'.join(df_pile_bp['Pile Mark'].values)
    df_bp_forces = df_pile_forces[
        df_pile_forces['OutputCase'].str.contains('ULS') & 
        df_pile_forces['Line'].str.contains(search_pattern)
    ].copy()
    
    # Extract pile mark and segment identifiers from analysis line names
    # Standard naming convention: PILEMARK_SEGMENT (e.g., BP01_T, BP01_1, etc.)
    df_bp_forces[['Pile Mark', 'Pile Segment']] = df_bp_forces['Line'].str.split('_', expand=True)
    
    # Create vectorized mapping of pile segments to Part A or Part B
    # Part A typically represents upper pile portion with higher moment demands
    target_pile_a = []
    for _, row in df_bp_shear.iterrows():
        pile_mark = row['Pile Mark']
        bp_length = ceil(float(row['BP Part Length (m)']))
        
        # Add standard segment identifiers for Part A
        # 'T' represents top segment, '0.5' represents transition segment
        target_pile_a.extend([f'{pile_mark}_T', f'{pile_mark}_0.5'])
        
        # Add numbered segments based on Part A length specification
        target_pile_a.extend([f'{pile_mark}_{i}' for i in range(1, bp_length)])
    
    # Assign Part A/B classification based on segment identification
    df_bp_forces['Part'] = np.where(df_bp_forces['Line'].isin(target_pile_a), 'A', 'B')
    
    # Calculate force resultants from structural analysis outputs
    # Transform SAFE analysis outputs to design-oriented force notation
    df_bp_forces['N (kN)'] = -df_bp_forces['P']  # Convert to compression positive
    df_bp_forces['V (kN)'] = np.sqrt(df_bp_forces['V2']**2 + df_bp_forces['V3']**2)  # Resultant shear
    df_bp_forces['M (kNm)'] = np.sqrt(df_bp_forces['M2']**2 + df_bp_forces['M3']**2)  # Resultant moment
    
    # Prepare pile geometry data for shear capacity calculations
    df_bp_shear['Diameter (mm)'] = df_bp_shear['Diameter (m)'].astype(float) * 1000
    
    # Calculate reinforcement centroid using vectorized operations
    # This determines the effective depth for shear capacity calculations
    layer1_mask = df_bp_shear['Layer 1 Rebar Num'] > 0
    layer2_mask = df_bp_shear['Layer 2 Rebar Num'] > 0
    layer3_mask = df_bp_shear['Layer 3 Rebar Num'] > 0
    
    # Layer 1 only: centroid at half diameter from edge
    df_bp_shear.loc[layer1_mask, 'Rebar Centroid (mm)'] = df_bp_shear.loc[layer1_mask, 'Layer 1 Rebar Dia (mm)'] / 2
    
    # Layer 2 active: centroid considering both layers with 40mm spacing
    df_bp_shear.loc[layer2_mask, 'Rebar Centroid (mm)'] = (
        df_bp_shear.loc[layer2_mask, 'Layer 1 Rebar Dia (mm)'] + 40 +
        df_bp_shear.loc[layer2_mask, 'Layer 2 Rebar Dia (mm)']
    ) / 2
    
    # Layer 3 active: centroid considering all three layers with progressive spacing
    df_bp_shear.loc[layer3_mask, 'Rebar Centroid (mm)'] = (
        df_bp_shear.loc[layer3_mask, 'Layer 1 Rebar Dia (mm)'] + 80 +
        df_bp_shear.loc[layer3_mask, 'Layer 2 Rebar Dia (mm)'] +
        df_bp_shear.loc[layer3_mask, 'Layer 3 Rebar Dia (mm)']
    ) / 2
    
    # Vectorized geometric calculations for circular section shear analysis
    # Calculate radius to reinforcement centroid for effective area determination
    df_bp_shear['rs (mm)'] = (df_bp_shear['Diameter (mm)'] - 
                              2 * (df_bp_shear['Cover (mm)'] + 
                                   df_bp_shear['Links Dia (mm)'] + 
                                   df_bp_shear['Rebar Centroid (mm)'])) / 2
    
    # Trigonometric calculations for circular section effective area
    # These parameters define the compression zone geometry for shear calculations
    df_bp_shear['sin(a)'] = 2 * df_bp_shear['rs (mm)'] / (pi * df_bp_shear['Diameter (mm)'] / 2)
    df_bp_shear['a (rad)'] = np.arcsin(df_bp_shear['sin(a)'])
    df_bp_shear['a (deg)'] = np.degrees(df_bp_shear['a (rad)'])
    df_bp_shear['cos(a)'] = np.cos(df_bp_shear['a (rad)'])
    
    # Effective depth for shear calculations (circular section)
    df_bp_shear['d (mm)'] = df_bp_shear['Diameter (mm)'] / 2 * (1 + df_bp_shear['sin(a)'])
    
    # Effective area of concrete in compression for shear resistance
    df_bp_shear['A (mm2)'] = (df_bp_shear['Diameter (mm)'] / 2)**2 * (
        pi / 2 + df_bp_shear['a (rad)'] + df_bp_shear['sin(a)'] * df_bp_shear['cos(a)']
    )
    
    # Effective width for shear calculations (circular sections)
    df_bp_shear['bv (mm)'] = df_bp_shear['A (mm2)'] / df_bp_shear['d (mm)']
    
    # Calculate total longitudinal reinforcement area using vectorized operations
    # Only half the total reinforcement is effective for each shear direction
    df_bp_shear['As (mm2)'] = (
        pi / 4 * (
            df_bp_shear['Layer 1 Rebar Num'] * df_bp_shear['Layer 1 Rebar Dia (mm)']**2 +
            df_bp_shear['Layer 2 Rebar Num'] * df_bp_shear['Layer 2 Rebar Dia (mm)']**2 +
            df_bp_shear['Layer 3 Rebar Num'] * df_bp_shear['Layer 3 Rebar Dia (mm)']**2
        )
    ) / 2
    
    # Calculate reinforcement ratio for concrete shear capacity enhancement
    # Limited to maximum value of 3% per design code requirements
    df_bp_shear['100As/bvd'] = np.minimum(3, 100 * df_bp_shear['As (mm2)'] / df_bp_shear['A (mm2)'])
    
    # Size effect factor for concrete shear capacity
    # Accounts for reduced shear capacity in larger sections
    df_bp_shear['(400/d)^0.25'] = np.maximum(1, (400 / df_bp_shear['d (mm)'])**0.25)
    
    # Concrete shear capacity calculation using standard design formula
    # Incorporates reinforcement ratio, size effect, and material strength factors
    df_bp_shear['vc (N/mm2)'] = (0.79 * df_bp_shear['100As/bvd']**(1/3) * 
                                 df_bp_shear['(400/d)^0.25'] / 1.25 * 
                                 (df_bp_shear['Fcu (MPa)'] / 25)**(1/3))
    
    # Merge force data with shear capacity data for comprehensive analysis
    df_bp_forces = df_bp_forces.merge(df_bp_shear, how='left', on=['Pile Mark', 'Part'])
    
    # Vectorized force-based calculations for shear verification
    # Calculate gross and effective concrete areas for different applications
    df_bp_forces['Ac (m2)'] = pi / 4 * df_bp_forces['Diameter (m)']**2
    df_bp_forces['Acs (m2)'] = (df_bp_forces['Diameter (mm)'] / 2)**2 * (
        pi / 2 + df_bp_forces['a (rad)'] + df_bp_forces['sin(a)'] * df_bp_forces['cos(a)']
    ) / 1e6
    
    # Moment-shear interaction factor for enhanced concrete capacity
    # Limits the enhancement based on the relative magnitude of moment and shear
    df_bp_forces['Vh/M'] = np.minimum(1, df_bp_forces['V (kN)'] * df_bp_forces['d (mm)'] / 1000 / df_bp_forces['M (kNm)'])
    
    # Axial compression enhancement factor for concrete shear capacity
    # Compression increases concrete contribution to shear resistance
    df_bp_forces['0.6N/Ac (N/mm2)'] = 0.6 * df_bp_forces['N (kN)'] / df_bp_forces['Ac (m2)'] * 0.001
    
    # Enhanced concrete shear capacity considering axial force effects
    df_bp_forces['Corrected vc (N/mm2)'] = (df_bp_forces['vc (N/mm2)'] + 
                                           df_bp_forces['0.6N/Ac (N/mm2)'] * df_bp_forces['Vh/M'])
    
    # Applied shear stress calculation
    df_bp_forces['v (N/mm2)'] = df_bp_forces['V (kN)'] / df_bp_forces['Acs (m2)'] * 0.001
    
    # Ultimate shear strength limitation to prevent compression failures
    # Based on concrete compressive strength with code-specified limits
    df_bp_forces['0.8fcu^0.5 or 7'] = np.minimum(7, 0.8 * np.sqrt(df_bp_forces['Fcu (MPa)']))
    df_bp_forces['0.8fcu^0.5 or 7 Check'] = np.where(
        df_bp_forces['v (N/mm2)'] <= df_bp_forces['0.8fcu^0.5 or 7'], 'OK', 'FAIL'
    )

    # Vectorized shear design case classification
    # Determines required transverse reinforcement based on applied shear stress
    df_bp_forces['Case'] = np.select(
        [
            # Case 1: Low shear - minimal transverse reinforcement required
            df_bp_forces['v (N/mm2)'] <= 0.5 * df_bp_forces['Corrected vc (N/mm2)'],
            
            # Case 2: Moderate shear - minimum transverse reinforcement required
            (df_bp_forces['v (N/mm2)'] > 0.5 * df_bp_forces['Corrected vc (N/mm2)']) &
            (df_bp_forces['v (N/mm2)'] <= df_bp_forces['Corrected vc (N/mm2)']),
            
            # Case 3: High shear - calculated transverse reinforcement required
            (df_bp_forces['v (N/mm2)'] > df_bp_forces['Corrected vc (N/mm2)']) &
            (df_bp_forces['v (N/mm2)'] <= df_bp_forces['0.8fcu^0.5 or 7'])
        ],
        ['v<0.5vc', '0.5vc<v<=vc', 'vc<v<=(0.8fcu**0.5 or 7)'],
        default='v>(0.8fcu**0.5 or 7)'  # Case 4: Excessive shear - section inadequate
    )
    
    # Calculate excess shear to be carried by transverse reinforcement
    df_bp_forces['v - Corrected vc (N/mm2)'] = df_bp_forces['v (N/mm2)'] - df_bp_forces['Corrected vc (N/mm2)']
    
    # Minimum transverse reinforcement ratio calculation
    # Based on concrete strength with enhancement for high-strength concrete
    df_bp_forces['vr (N/mm2)'] = np.where(
        df_bp_forces['Fcu (MPa)'] <= 40,
        0.4,  # Standard minimum for normal strength concrete
        0.4 * np.minimum(80, df_bp_forces['Fcu (MPa)'] / 40)**(2/3)  # Enhanced for high strength
    )
    
    # Minimum transverse reinforcement area per unit length
    df_bp_forces['Asv/Sv min'] = df_bp_forces['vr (N/mm2)'] * df_bp_forces['bv (mm)'] / (0.87 * df_bp_forces['Fyv (MPa)'])
    
    # Required transverse reinforcement based on shear design case
    df_bp_forces['Asv/Sv Req'] = np.select(
        [
            df_bp_forces['Case'] == 'v<0.5vc',           # Minimal reinforcement case
            df_bp_forces['Case'] == '0.5vc<v<=vc',       # Minimum reinforcement case
            df_bp_forces['Case'] == 'vc<v<=(0.8fcu**0.5 or 7)'  # Calculated reinforcement case
        ],
        [
            0,  # No additional reinforcement required
            df_bp_forces['Asv/Sv min'],  # Minimum code requirements
            # Calculated reinforcement for excess shear resistance
            df_bp_forces['v - Corrected vc (N/mm2)'] * df_bp_forces['Diameter (mm)'] / (0.87 * df_bp_forces['Fyv (MPa)'])
        ]
    )
    
    # Provided transverse reinforcement area per unit length
    df_bp_forces['Asv/Sv Prov'] = (pi / 4 * df_bp_forces['Links Dia (mm)']**2 * 
                                   df_bp_forces['Links Legs'] / df_bp_forces['Links Spacing (mm)'])
    
    # Design adequacy verification
    df_bp_forces['Asv/Sv Check'] = np.where(
        df_bp_forces['Asv/Sv Prov'] >= df_bp_forces['Asv/Sv Req'], 'OK', 'FAIL'
    )

    # Get maximum critical shear condition for each pile using groupby operations
    # Initialize error logging for missing or problematic pile data
    df_error_log = pd.DataFrame(columns=['Error', 'Remarks'])
    
    # Group by Pile Mark and Part, find index of maximum excess shear stress
    # Handle NaN values by dropping them before finding idxmax to avoid errors
    idx = df_bp_forces.groupby(['Pile Mark', 'Part'])['v - Corrected vc (N/mm2)'].idxmax()
    # Remove NaN indices that can occur when all values in a group are NaN
    idx = idx.dropna()
    
    # Extract critical design conditions for each pile/part combination
    temp = df_bp_forces.loc[idx, [
        'Pile Mark', 'Part', 'Line', 'OutputCase', 'N (kN)', 'V (kN)', 'M (kNm)', 
        'Ac (m2)', 'Acs (m2)', 'Vh/M', '0.6N/Ac (N/mm2)', 'Corrected vc (N/mm2)', 
        'v (N/mm2)', 'v - Corrected vc (N/mm2)', 'vr (N/mm2)', 'Case', 
        'Asv/Sv min', 'Asv/Sv Req', 'Asv/Sv Prov', 'Asv/Sv Check', 
        '0.8fcu^0.5 or 7', '0.8fcu^0.5 or 7 Check'
    ]]
    
    # Identify and log missing pile data for quality control
    missing_piles = set(df_bp_shear[['Pile Mark', 'Part']].apply(tuple, axis=1)) - set(temp[['Pile Mark', 'Part']].apply(tuple, axis=1))
    for pile_mark, part in missing_piles:
        df_error_log.loc[len(df_error_log)] = ['CANNOT SEARCH', f'{pile_mark} Part {part}']
        if log_callback:
            log_callback(f"'CANNOT SEARCH', {pile_mark} Part {part}")
    
    # Merge critical conditions with pile design data
    df_bp_shear = df_bp_shear.merge(temp, how='left', on=['Pile Mark', 'Part'])
    
    # Store results in design database
    design_results.BPShearCheck = df_bp_shear
    
    # Prepare comprehensive output data for design documentation
    selected_columns = [
        'Pile Mark', 'Part', 'BP Part Length (m)', 'Diameter (m)', 'Cover (mm)',
        'Layer 1 Rebar Num', 'Layer 1 Rebar Dia (mm)', 'Layer 2 Rebar Num', 
        'Layer 2 Rebar Dia (mm)', 'Layer 3 Rebar Num', 'Layer 3 Rebar Dia (mm)',
        'Steel Ratio (%)', 'Fcu (MPa)', 'Fy (MPa)', 'Es (GPa)', 'Fyv (MPa)', 
        'Links Dia (mm)', 'Links Spacing (mm)', 'Links Legs', 'Diameter (mm)',
        'Rebar Centroid (mm)', 'rs (mm)', 'sin(a)', 'a (deg)', 'd (mm)', 
        'A (mm2)', 'bv (mm)', 'As (mm2)', '100As/bvd', '(400/d)^0.25', 
        'vc (N/mm2)', 'vr (N/mm2)', 'Line', 'OutputCase', 'N (kN)', 'V (kN)', 
        'M (kNm)', 'Ac (m2)', 'Acs (m2)', 'Vh/M', '0.6N/Ac (N/mm2)',
        'Corrected vc (N/mm2)', 'v (N/mm2)', 'v - Corrected vc (N/mm2)', 
        'Case', 'Asv/Sv min', 'Asv/Sv Req', 'Asv/Sv Prov', 'Asv/Sv Check', 
        '0.8fcu^0.5 or 7', '0.8fcu^0.5 or 7 Check'
    ]
    
    # Export comprehensive results to Excel workbook with multiple sheets
    with pd.ExcelWriter(path_excel_output) as writer:
        df_bp_shear[selected_columns].to_excel(writer, sheet_name=SHEET_BP_SHEAR_CHECK, index=False)
        df_error_log.to_excel(writer, sheet_name=SHEET_BP_ERROR_LOG, index=False)
    
    if log_callback:
        log_callback('Checked Bored Pile Shear!')
    
    return design_results


def design_bp_links(
    links_dia: float, 
    links_spacing: float, 
    links_legs: int, 
    asv_sv_req: float, 
    scale: float
) -> Tuple[float, float, int]:
    """
    Design optimized transverse reinforcement (links) for bored pile shear resistance.
    
    This function implements an intelligent optimization algorithm to determine the
    most efficient transverse reinforcement configuration that satisfies shear design
    requirements while maintaining constructability and code compliance. The optimization
    considers standard reinforcement sizes and spacing increments commonly used in
    foundation construction.
    
    The design algorithm systematically evaluates combinations of:
    - Link diameters: Standard bar sizes for transverse reinforcement
    - Link spacing: Code-compliant spacing intervals for effective shear transfer
    - Link leg configuration: Multi-leg arrangements for enhanced capacity
    """
    # Standard reinforcement options commonly available and used in construction
    dia_options = [12, 16, 20, 25]      # Standard link diameters (mm)
    spacing_options = [300, 250, 200, 150]  # Standard spacing intervals (mm)
    
    # Systematic evaluation of all diameter and spacing combinations
    # Prioritize maintaining or improving current configuration
    for dia in dia_options:
        if dia < links_dia:
            continue  # Skip smaller diameters than current configuration
            
        for spacing in spacing_options:
            if spacing > links_spacing:
                continue  # Skip larger spacings than current configuration
                
            # Calculate provided transverse reinforcement area per unit length
            asv_sv = pi / 4 * dia**2 * links_legs / spacing
            
            # Check if configuration satisfies design requirements with utilization factor
            if asv_sv * scale >= asv_sv_req:
                return dia, spacing, links_legs
    
    # Return original configuration if no suitable optimization found
    return links_dia, links_spacing, links_legs


def design_bp_shear(
    design_results: Dict[str, Any], 
    file_paths: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Optimize transverse reinforcement design for bored pile shear resistance.
    
    This function performs comprehensive optimization of transverse reinforcement
    configurations for all bored pile elements that require shear design enhancement.
    It implements intelligent design algorithms to determine optimal link configurations
    while maintaining constructability constraints and code compliance requirements.
    
    The optimization process includes:
    1. Identification of piles requiring transverse reinforcement design modification
    2. Systematic evaluation of standard reinforcement configurations
    3. Design adequacy verification against ultimate limit state criteria
    4. Comprehensive documentation of design changes and recommendations
    5. Quality control through error checking and validation procedures
    """
    # Extract shear check results for optimization processing
    df_bp_shear_design = design_results.BPShearCheck.copy()
    
    # Extract design parameters using vectorized operations for efficiency
    scale = df_bp_shear_design['Target Utilization (<=1)'].astype(float)
    shear_max_check = df_bp_shear_design['0.8fcu^0.5 or 7 Check']
    asv_sv_check = df_bp_shear_design['Asv/Sv Check']
    asv_sv_req = df_bp_shear_design['Asv/Sv Req']
    
    # Extract current transverse reinforcement configurations
    links_dia_org = df_bp_shear_design['Links Dia (mm)'].astype(float)
    links_spacing_org = df_bp_shear_design['Links Spacing (mm)'].astype(float)
    links_legs_org = df_bp_shear_design['Links Legs'].astype(float)
    
    # Initialize design result tracking with default 'OK' status
    df_bp_shear_design['Result'] = 'OK'
    
    # Handle cases where shear stress exceeds ultimate capacity limits
    # These cases require section enlargement rather than reinforcement modification
    fail_mask = shear_max_check != 'OK'
    df_bp_shear_design.loc[fail_mask, 'Result'] = 'Increase Pile Diameter. '
    
    # Identify elements requiring transverse reinforcement design optimization
    # Process only elements that pass ultimate shear check but fail adequacy check
    design_mask = (asv_sv_check != 'OK') & ~fail_mask
    
    if design_mask.any():
        # Process each element requiring design modification individually
        # This allows for customized optimization based on specific requirements
        for idx in df_bp_shear_design[design_mask].index:
            row = df_bp_shear_design.loc[idx]
            
            # Apply intelligent link design optimization algorithm
            links_dia, links_spacing, links_legs = design_bp_links(
                links_dia_org[idx],     # Current/minimum diameter
                links_spacing_org[idx], # Current/maximum spacing
                links_legs_org[idx],    # Current leg configuration
                asv_sv_req[idx],        # Required reinforcement area per length
                scale[idx]              # Design utilization factor
            )
            
            # Update design configuration with optimized values
            df_bp_shear_design.loc[idx, 'Links Dia (mm)'] = links_dia
            df_bp_shear_design.loc[idx, 'Links Spacing (mm)'] = links_spacing
            df_bp_shear_design.loc[idx, 'Links Legs'] = links_legs
            
            # Recalculate provided reinforcement area with optimized configuration
            asv_sv_prov = pi / 4 * links_dia**2 * links_legs / links_spacing
            df_bp_shear_design.loc[idx, 'Asv/Sv Prov'] = asv_sv_prov
            
            # Verify design adequacy with optimized configuration
            df_bp_shear_design.loc[idx, 'Asv/Sv Check'] = 'OK' if asv_sv_prov >= asv_sv_req[idx] else 'NOT OK'
            
            # Generate descriptive result message documenting design changes
            result_parts = []
            if links_dia > links_dia_org[idx]:
                result_parts.append('Increased Links Dia.')
            if links_spacing < links_spacing_org[idx]:
                result_parts.append('Reduced Links Spacing.')
            
            # Document design modifications for construction reference
            if result_parts:
                df_bp_shear_design.loc[idx, 'Result'] = ' '.join(result_parts) + ' '
    
    # Store optimized design results in design database
    design_results.ResultBPShearDesign = df_bp_shear_design

    # Export comprehensive design results to Excel for documentation and construction
    with pd.ExcelWriter(file_paths.ResultBPShearDesign) as writer:
        df_bp_shear_design.to_excel(writer, sheet_name=SHEET_BP_SHEAR_DESIGN, index=False)
    
    if log_callback:
        log_callback('Designed Bored Pile Shear!')
    
    return design_results
