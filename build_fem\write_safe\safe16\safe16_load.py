"""Convert structural loads from Excel to SAFE16 format.

Processes line loads, pile loads, slab loads, beam loads, column loads, wall loads,
corewall loads, and point loads with coordinate transformations and group assignments."""

import pandas as pd

from build_fem import functions


def _write_line_load_safe16(excel_inputs, safe16_dfs):
    """Convert line load data from Excel to SAFE16 format with coordinate transformations."""    
    # Copy and prepare input data
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()

    # Calculate line geometry properties
    df_line_length_data = functions.cal_line_length(excel_inputs)  # Get line lengths and angles
    df_lineload = excel_inputs.LineLoad.copy()

    # Extract start and end points from line load definitions
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # Create load group definition in SAFE16
    safe16_dfs.GroupDefinitions.loc[safe16_dfs.GroupDefinitions.index.size] = ['A.Load_Line', 'Red']

    # Extract load case names (exclude 'Line Data' column group)
    all_columns = df_input_load_line.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Line Data' not in col]

    # Process each line load element
    for i, df_row in df_input_load_line.iterrows():
        name = df_row[('Line Data', 'Line Load')]

        # Look up line geometric properties
        condition_length = df_line_length_data['Line Load'] == name
        length = df_line_length_data.loc[condition_length, 'Line Length (m)'].item()
        deg = df_line_length_data.loc[condition_length, 'Line Theta (deg)'].item()
        t = df_row[('Line Data', 'Cap Thickness (m)')]

        # Assign line elements and points to load group
        line_assign_row = df_lineload[df_lineload['Line Load'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        # Add elements to load group for organization
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Line', 'Line', name]
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Line', 'Point', pt_i]
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Line', 'Point', pt_j]

        # Include points in analysis mesh for proper load transfer
        safe16_dfs.ObjsAnalysisMesh01Points.loc[safe16_dfs.ObjsAnalysisMesh01Points.index.size] = [pt_i, 'Yes']
        safe16_dfs.ObjsAnalysisMesh01Points.loc[safe16_dfs.ObjsAnalysisMesh01Points.index.size] = [pt_j, 'Yes']

        # Process loads for each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_1 = load_data['V1 (kN/m)']
            v_3 = load_data['V3 (kN/m)']
            f_z = load_data['Axial (kN/m)']
            m_1_input = load_data['M1 (kNm/m)']  # Moment about local axis 1
            m_3_input = load_data['M3 (kNm/m)']  # Moment about local axis 3
            m_z = load_data['Mz (kNm/m)']

            # Transform loads from local to global coordinates
            m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_line_load_local(
                    t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            # Apply transformed loads to SAFE16 distributed load assignments
            if s_1 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Local 1', 'RelDist', 0, 1, 0, length, s_1, s_1, None, None]

            if s_3 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Local 3', 'RelDist', 0, 1, 0, length, s_3, s_3, None, None]

            if s_z_start != 0 or s_z_end != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start, s_z_end, None, None]

            if s_m1 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1, s_m1]

            if s_mz != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Gravity', 'RelDist', 0, 1, 0, length, None, None, -s_mz, -s_mz]

            # Apply additional loads due to shear effects
            if s_z_start_add != 0 or s_z_end_add != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None]

            if s_m1_add != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1_add, s_m1_add]

    return safe16_dfs


def _write_pile_load_safe16(excel_inputs, safe16_dfs):
    """Convert pile load data from Excel to SAFE16 point loads with group assignments."""    
    # Copy and prepare input data
    df_input_load_pile = excel_inputs.InputLoadPile.fillna(0).copy()

    group_name = 'A.Load_Pile'

    # Create pile load group definition in SAFE16
    safe16_dfs.GroupDefinitions.loc[safe16_dfs.GroupDefinitions.index.size] = [group_name, 'Red']

    # Extract load case names (exclude 'Pile Data' column group)
    all_columns = df_input_load_pile.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Pile Data' not in col]

    # Prepare lists for batch DataFrame operations (improved performance)
    group_assignments_16_list = []
    load_assignments_16_list = []

    # Process each pile load element
    for _, df_row in df_input_load_pile.iterrows():  # Use _ for unused index
        pile_mark = df_row[('Pile Data', 'Pile Mark')]
        point = f"{pile_mark}_T"  # Add '_T' suffix for pile top point

        # Add pile top point to load group
        group_assignments_16_list.append([group_name, 'Point', point])

        # Process loads for each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x = load_data['Vx (kN)']
            v_y = load_data['Vy (kN)']
            f_z = load_data['Axial (kN)']
            m_x = load_data['Mx (kNm)']
            m_y = load_data['My (kNm)']

            # Only add load assignment if there are non-zero loads
            if v_x != 0 or v_y != 0 or f_z != 0 or m_x != 0 or m_y != 0:
                load_assignments_16_list.append([point, load_case, v_x, v_y, f_z, m_x, m_y, 0, 0, 0])

    # Batch append group assignments to DataFrame
    df_append = pd.DataFrame(group_assignments_16_list, columns=safe16_dfs.GroupAssignments.columns)
    if not df_append.empty:
        if safe16_dfs.GroupAssignments.empty:
            safe16_dfs.GroupAssignments = df_append.copy()
        else:
            safe16_dfs.GroupAssignments = pd.concat(
                [safe16_dfs.GroupAssignments, df_append], ignore_index=True
            )

    # Batch append load assignments to DataFrame
    df_append = pd.DataFrame(load_assignments_16_list, columns=safe16_dfs.LoadAssignmentsPointLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignmentsPointLoads.empty:
            safe16_dfs.LoadAssignmentsPointLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignmentsPointLoads = pd.concat(
                [safe16_dfs.LoadAssignmentsPointLoads, df_append], ignore_index=True
            )

    return safe16_dfs


def _write_slab_load_safe16(excel_inputs, safe16_dfs):
    """Convert slab surface load data from Excel to SAFE16 format."""    
    # Copy and prepare input data
    df_input_load_slab = excel_inputs.InputLoadSlab.fillna(0).copy()

    # Extract load case names (exclude 'Slab Data' column group)
    all_columns = df_input_load_slab.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Slab Data' not in col]

    # Prepare list for batch DataFrame operations (improved performance)
    load_assigns_surface_16_list = []

    # Process each slab load element
    for _, df_row in df_input_load_slab.iterrows():  # Use _ for unused index
        slab_mark = df_row[('Slab Data', 'Slab Mark')]

        # Process loads for each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x = load_data['Vx (kN/m2)']
            v_y = load_data['Vy (kN/m2)']
            f_z = load_data['Axial (kN/m2)']

            # Apply loads in respective global directions (only non-zero loads)
            if v_x != 0:
                load_assigns_surface_16_list.append([slab_mark, load_case, 'Global X', v_x, 0, 0, 0])

            if v_y != 0:
                load_assigns_surface_16_list.append([slab_mark, load_case, 'Global Y', v_y, 0, 0, 0])

            if f_z != 0:
                load_assigns_surface_16_list.append([slab_mark, load_case, 'Gravity', f_z, 0, 0, 0])

    # Batch append surface load assignments to DataFrame
    df_append = pd.DataFrame(load_assigns_surface_16_list, columns=safe16_dfs.LoadAssignsSurfaceLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignsSurfaceLoads.empty:
            safe16_dfs.LoadAssignsSurfaceLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignsSurfaceLoads = pd.concat(
                [safe16_dfs.LoadAssignsSurfaceLoads, df_append], ignore_index=True
            )

    return safe16_dfs


def _write_lkp_load_safe16(excel_inputs, safe16_dfs):
    """Convert LKP (Load Patch) data from Excel to SAFE16 surface loads for concentrated areas."""
    df_slab = excel_inputs.Slab.copy()
    df_lkp = excel_inputs.LKP.copy()

    # Ensure load group columns are string type for consistent matching
    df_slab['Load Group'] = df_slab['Load Group'].astype(str)
    df_lkp['Load Group'] = df_lkp['Load Group'].astype(str)

    df_input_load_lkp = excel_inputs.InputLoadLKP.fillna(0).copy()

    # Extract load case names (exclude 'LKP Data' column group)
    all_lkp_columns = df_input_load_lkp.columns.get_level_values(0).unique()
    load_cases_lkp = [col for col in all_lkp_columns if 'LKP Data' not in col]

    # Prepare list for batch DataFrame operations (improved performance)
    load_assigns_surface_16_list = []

    # Process LKP loads and apply to slabs with matching load groups
    for _, df_row_lkp in df_input_load_lkp.iterrows():  # Use _ for unused index
        lkp_type = df_row_lkp[('LKP Data', 'LKP Type')]

        # Convert float LKP type to string for consistent matching
        if isinstance(lkp_type, float):
            lkp_type = str(int(lkp_type))

        # Find slabs with matching load group
        df_load_group = df_slab[df_slab['Load Group'] == lkp_type]

        # Apply loads to each slab in the matching load group
        for _, df_row_group in df_load_group.iterrows():  # Use _ for unused index
            slab_mark = df_row_group['Slab']

            # Process loads for each load case
            for load_case in load_cases_lkp:
                load_data = df_row_lkp[load_case]
                v_x = load_data['Vx (kN/m2)']
                v_y = load_data['Vy (kN/m2)']
                f_z = load_data['Axial (kN/m2)']

                # Apply loads in respective global directions (only non-zero loads)
                if v_x != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Global X', v_x, 0, 0, 0])

                if v_y != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Global Y', v_y, 0, 0, 0])

                if f_z != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Gravity', f_z, 0, 0, 0])

    # Batch append surface load assignments to DataFrame (for slabs)
    df_append = pd.DataFrame(load_assigns_surface_16_list, columns=safe16_dfs.LoadAssignsSurfaceLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignsSurfaceLoads.empty:
            safe16_dfs.LoadAssignsSurfaceLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignsSurfaceLoads = pd.concat(
                [safe16_dfs.LoadAssignsSurfaceLoads, df_append], ignore_index=True
            )

    # Reset list for LKP element loads
    load_assigns_surface_16_list = []

    # Process LKP loads and apply to LKP elements with matching load groups
    for _, df_row_lkp in df_input_load_lkp.iterrows():  # Use _ for unused index
        lkp_type = df_row_lkp[('LKP Data', 'LKP Type')]

        # Convert float LKP type to string for consistent matching
        if isinstance(lkp_type, float):
            lkp_type = str(int(lkp_type))

        # Find LKP elements with matching load group
        df_load_group = df_lkp[df_lkp['Load Group'] == lkp_type]

        # Apply loads to each LKP element in the matching load group
        for _, df_row_group in df_load_group.iterrows():  # Use _ for unused index
            slab_mark = df_row_group['LKP']

            # Process loads for each load case
            for load_case in load_cases_lkp:
                load_data = df_row_lkp[load_case]
                v_x = load_data['Vx (kN/m2)']
                v_y = load_data['Vy (kN/m2)']
                f_z = load_data['Axial (kN/m2)']

                # Apply loads in respective global directions (only non-zero loads)
                if v_x != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Global X', v_x, 0, 0, 0])

                if v_y != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Global Y', v_y, 0, 0, 0])

                if f_z != 0:
                    load_assigns_surface_16_list.append([slab_mark, load_case, 'Gravity', f_z, 0, 0, 0])

    # Batch append surface load assignments to DataFrame (for LKP elements)
    df_append = pd.DataFrame(load_assigns_surface_16_list, columns=safe16_dfs.LoadAssignsSurfaceLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignsSurfaceLoads.empty:
            safe16_dfs.LoadAssignsSurfaceLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignsSurfaceLoads = pd.concat(
                [safe16_dfs.LoadAssignsSurfaceLoads, df_append], ignore_index=True
            )
    return safe16_dfs


def _write_beam_load_safe16(excel_inputs, safe16_dfs):
    """
    Write beam load data to SAFE16 format.
    
    Processes beam load data from Excel inputs and converts them to SAFE16 format.
    Handles load transformations from local coordinates, creates load groups,
    and applies distributed loads to beam elements.
    """    
    # Copy and prepare input data
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()
    df_beam_length_data = functions.cal_beam_length(excel_inputs)
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for SAFE16 compatibility and extract points
    df_beam.rename(columns={'Beam': 'Line (Text)'}, inplace=True)
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Create beam load group definition in SAFE16
    safe16_dfs.GroupDefinitions.loc[safe16_dfs.GroupDefinitions.index.size] = ['A.Load_Beam', 'Red']

    # Extract load case names (exclude 'Beam Data' column group)
    all_columns = df_input_load_beam.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Beam Data' not in col]

    # Process each beam load element
    for i, df_row in df_input_load_beam.iterrows():
        name = df_row[('Beam Data', 'Beam Mark')]

        # Look up beam geometric properties
        condition_length = df_beam_length_data['Beam Mark'] == name
        length = df_beam_length_data.loc[condition_length, 'Beam Length (m)'].item()
        deg = df_beam_length_data.loc[condition_length, 'Beam Theta (deg)'].item()
        t = df_row[('Beam Data', 'Cap Thickness (m)')]

        # Assign beam elements and points to load group
        line_assign_row = df_beam[df_beam['Line (Text)'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        # Add elements to load group for organization
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Beam', 'Line', name]
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Beam', 'Point', pt_i]
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Beam', 'Point', pt_j]

        # Include points in analysis mesh for proper load transfer
        safe16_dfs.ObjsAnalysisMesh01Points.loc[safe16_dfs.ObjsAnalysisMesh01Points.index.size] = [pt_i, 'Yes']
        safe16_dfs.ObjsAnalysisMesh01Points.loc[safe16_dfs.ObjsAnalysisMesh01Points.index.size] = [pt_j, 'Yes']

        # Process loads for each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_1 = load_data['V1 (kN/m)']
            v_3 = load_data['V3 (kN/m)']
            f_z = load_data['Axial (kN/m)']
            m_1_input = load_data['M1 (kNm/m)']  # Moment about local axis 1
            m_3_input = load_data['M3 (kNm/m)']  # Moment about local axis 3
            m_z = load_data['Mz (kNm/m)']

            # Transform loads from local to global coordinates
            m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_line_load_local(
                    t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            # Apply transformed loads to SAFE16 distributed load assignments
            if s_1 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Local 1', 'RelDist', 0, 1, 0, length, s_1, s_1, None, None]
            if s_3 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Local 3', 'RelDist', 0, 1, 0, length, s_3, s_3, None, None]

            if s_z_start != 0 or s_z_end != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start, s_z_end, None, None]

            if s_m1 != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1, s_m1]

            if s_mz != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Gravity', 'RelDist', 0, 1, 0, length, None, None, -s_mz, -s_mz]

            # Apply additional loads due to shear effects
            if s_z_start_add != 0 or s_z_end_add != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None]

            if s_m1_add != 0:
                safe16_dfs.LoadsLinesDistributed.loc[safe16_dfs.LoadsLinesDistributed.index.size] = [
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1_add, s_m1_add]

    return safe16_dfs


def _write_column_load_safe16(excel_inputs, safe16_dfs):
    """
    Write column load data to SAFE16 format.
    
    Processes column load data from Excel inputs and converts them to SAFE16 format.
    Handles both area loads and point loads, applies moment corrections due to
    shear effects, and manages load group assignments.
    """
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()
    df_column = excel_inputs.Column.copy()

    df = pd.DataFrame()
    df['Column'] = df_input_load_column['Column Data', 'Column Mark']
    merged_df = pd.merge(df, df_column, on='Column', how='left')
    df_input_load_column['Column Data', 'Center Point (Text)'] = merged_df['Center Point']

    # Initialize lists to collect new rows for DataFrames
    s16_group_definitions_rows = []
    s16_group_assignments_rows = []
    s16_objs_analysis_mesh_01_points_rows = []
    s16_load_assignments_point_loads_rows = []
    s16_load_assigns_surface_loads_rows = []

    # DEFINE Load GROUP IN SAFE
    s16_group_definitions_rows.append(['A.Load_Column', 'Red'])

    for i, row in df_input_load_column.iterrows():
        name = row[('Column Data', 'Column Mark')]
        area = row[('Column Data', 'Area (m2)')]
        center = row[('Column Data', 'Center Point (Text)')]
        t = row[('Column Data', 'Cap Thickness (m)')]
        load_type = row[('Column Data', 'Area/Point Load (A/P)')]

        # DEFINE COLUMN POSITION TO SAFE & ASSIGN TO GROUP
        s16_group_assignments_rows.append(['A.Load_Column', 'Point', center])
        s16_group_assignments_rows.append(['A.Load_Column', 'Area', name])
        s16_objs_analysis_mesh_01_points_rows.append([center, 'Yes'])  # INCLUDE COLUMN POINT FOR MESH

        # WRITE COLUMN Load TO SAFE
        cols = df_input_load_column.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Column Data')
        load_cases = cols[condition]
        for load_case in load_cases:
            load_data = row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            # WRITE COLUMN CENTER Load (Moments) TO SAFE
            if (m_x != 0) or (m_y != 0) or (m_z != 0):
                s16_load_assignments_point_loads_rows.append([center, load_case, 0, 0, 0, m_x, m_y, m_z, 0, 0])

            # WRITE COLUMN ADDITIONAL CENTER Load DUE TO SHEAR TO SAFE
            m_x_add = 0 - v_y * t
            m_y_add = 0 + v_x * t
            if (m_x_add != 0) or (m_y_add != 0):
                s16_load_assignments_point_loads_rows.append([center, load_case, 0, 0, 0, m_x_add, m_y_add, 0, 0, 0])

            # WRITE COLUMN AREA/POINT Load TO SAFE
            if load_type == 'A':
                if area == 0:  # Avoid division by zero if area is 0 for an area load
                    # Potentially log a warning or handle as an error
                    # For now, skip adding these loads if area is zero
                    pass
                else:
                    if v_x != 0:
                        s16_load_assigns_surface_loads_rows.append([name, load_case, 'Global X', v_x / area, 0, 0, 0])
                    if v_y != 0:
                        s16_load_assigns_surface_loads_rows.append([name, load_case, 'Global Y', v_y / area, 0, 0, 0])
                    if f_z != 0:
                        s16_load_assigns_surface_loads_rows.append([name, load_case, 'Gravity', f_z / area, 0, 0, 0])
            elif load_type == 'P':
                if (v_x != 0) or (v_y != 0) or (f_z != 0):
                    s16_load_assignments_point_loads_rows.append([center, load_case, v_x, v_y, f_z, 0, 0, 0, 0, 0])

    # Concatenate collected rows to the respective DataFrames
    df_append = pd.DataFrame(s16_group_definitions_rows, columns=safe16_dfs.GroupDefinitions.columns)
    if not df_append.empty:
        if safe16_dfs.GroupDefinitions.empty:
            safe16_dfs.GroupDefinitions = df_append.copy()
        else:
            safe16_dfs.GroupDefinitions = pd.concat([safe16_dfs.GroupDefinitions, df_append], ignore_index=True)

    df_append = pd.DataFrame(s16_group_assignments_rows, columns=safe16_dfs.GroupAssignments.columns)
    if not df_append.empty:
        if safe16_dfs.GroupAssignments.empty:
            safe16_dfs.GroupAssignments = df_append.copy()
        else:
            safe16_dfs.GroupAssignments = pd.concat([safe16_dfs.GroupAssignments, df_append], ignore_index=True)

    df_append = pd.DataFrame(s16_objs_analysis_mesh_01_points_rows, columns=safe16_dfs.ObjsAnalysisMesh01Points.columns)
    if not df_append.empty:
        if safe16_dfs.ObjsAnalysisMesh01Points.empty:
            safe16_dfs.ObjsAnalysisMesh01Points = df_append.copy()
        else:
            safe16_dfs.ObjsAnalysisMesh01Points = pd.concat([safe16_dfs.ObjsAnalysisMesh01Points, df_append],
                                                            ignore_index=True)

    df_append = pd.DataFrame(s16_load_assignments_point_loads_rows,
                             columns=safe16_dfs.LoadAssignmentsPointLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignmentsPointLoads.empty:
            safe16_dfs.LoadAssignmentsPointLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignmentsPointLoads = pd.concat([safe16_dfs.LoadAssignmentsPointLoads, df_append],
                                                             ignore_index=True)

    df_append = pd.DataFrame(s16_load_assigns_surface_loads_rows,
                             columns=safe16_dfs.LoadAssignsSurfaceLoads.columns)
    if not df_append.empty:
        if safe16_dfs.LoadAssignsSurfaceLoads.empty:
            safe16_dfs.LoadAssignsSurfaceLoads = df_append.copy()
        else:
            safe16_dfs.LoadAssignsSurfaceLoads = pd.concat([safe16_dfs.LoadAssignsSurfaceLoads, df_append],
                                                           ignore_index=True)

    return safe16_dfs


def _write_wall_load_safe16(excel_inputs, safe16_dfs):
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()
    df_wall_length = functions.cal_wall_length(excel_inputs)
    df_wall = excel_inputs.Wall.copy()

    GroupDefinitions_rows = []
    GroupAssignments_rows = []
    LoadsLinesDistributed_rows = []
    ObjsAnalysisMesh01Points_rows = []  # Initialize ObjsAnalysisMesh01Points_rows

    # DEFINE Load GROUP IN SAFE
    GroupDefinitions_rows.append(['A.Load_Wall', 'Red'])

    # split Points into PointI and PointJ
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)

    for i, df_row in df_input_load_wall.iterrows():
        name = df_row[('Wall Data', 'Wall Mark')]
        condition = df_wall_length['Wall Name'] == name
        length = df_wall_length.loc[condition, 'Wall Length (m)'].values[0]
        deg = df_wall_length.loc[condition, 'Wall Theta (deg)'].values[0]
        t = df_row[('Wall Data', 'Cap Thickness (m)')]

        # ASSIGN WALL POINT AND LINE TO GROUP
        # WALL LINE
        index_target = df_wall.index[df_wall['Wall'] == name]
        pt_i = df_wall.loc[index_target, 'PointI (Text)'].values[0]
        pt_j = df_wall.loc[index_target, 'PointJ (Text)'].values[0]
        center = df_wall.loc[index_target, 'Center Point'].values[0]

        # DEFINE COLUMN POSITION TO SAFE & ASSIGN TO GROUP
        GroupAssignments_rows.append(['A.Load_Wall', 'Line', name])
        GroupAssignments_rows.append(['A.Load_Wall', 'Point', pt_i])
        GroupAssignments_rows.append(['A.Load_Wall', 'Point', pt_j])
        GroupAssignments_rows.append(['A.Load_Wall', 'Point', center])
        df_append = pd.DataFrame(GroupAssignments_rows, columns=safe16_dfs.GroupAssignments.columns)
        if not df_append.empty:
            if safe16_dfs.GroupAssignments.empty:
                safe16_dfs.GroupAssignments = df_append.copy()
            else:
                safe16_dfs.GroupAssignments = pd.concat(
                    [safe16_dfs.GroupAssignments, df_append], ignore_index=True
                )

        # INCLUDE COLUMN POINT FOR MESH IN SAFE
        ObjsAnalysisMesh01Points_rows.append([pt_i, 'Yes'])
        ObjsAnalysisMesh01Points_rows.append([pt_j, 'Yes'])
        ObjsAnalysisMesh01Points_rows.append([center, 'Yes'])

        # WRITE WALL Load TO SAFE
        cols = df_input_load_wall.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Wall Data')
        load_cases = cols[condition]
        for load_case in load_cases:
            # load data of each wall under each load case
            load_data = df_row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            mx_add, my_add, m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add, s_x, s_y, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_wall_load_global(
                    t, length, deg, v_x, v_y, f_z, m_x, m_y, m_z)

            not_zero = (s_x != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Force', 'Global X', 'RelDist', 0, 1, 0, length, s_x, s_x, None, None])

            not_zero = (s_y != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Force', 'Global Y', 'RelDist', 0, 1, 0, length, s_y, s_y, None, None])

            not_zero = (s_z_start != 0) or (s_z_end != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start, s_z_end, None, None])

            not_zero = (s_m1 != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1, s_m1])

            not_zero = (s_mz != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Moment', 'Gravity', 'RelDist', 0, 1, 0, length, None, None, -s_mz, -s_mz])

            # Additional Load due to Shear
            not_zero = (s_z_start_add != 0) or (s_z_end_add != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Force', 'Gravity', 'RelDist', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None])

            not_zero = (s_m1_add != 0)
            if not_zero:
                LoadsLinesDistributed_rows.append([
                    name, load_case, 'Moment', 'Local 1', 'RelDist', 0, 1, 0, length, None, None, s_m1_add, s_m1_add])

    df_append = pd.DataFrame(GroupDefinitions_rows, columns=safe16_dfs.GroupDefinitions.columns)
    if not df_append.empty:
        if safe16_dfs.GroupDefinitions.empty:
            safe16_dfs.GroupDefinitions = df_append.copy()
        else:
            safe16_dfs.GroupDefinitions = pd.concat([safe16_dfs.GroupDefinitions, df_append], ignore_index=True)

    df_append = pd.DataFrame(ObjsAnalysisMesh01Points_rows, columns=safe16_dfs.ObjsAnalysisMesh01Points.columns)
    if not df_append.empty:
        if safe16_dfs.ObjsAnalysisMesh01Points.empty:
            safe16_dfs.ObjsAnalysisMesh01Points = df_append.copy()
        else:
            safe16_dfs.ObjsAnalysisMesh01Points = pd.concat(
                [safe16_dfs.ObjsAnalysisMesh01Points, df_append], ignore_index=True
            )

    df_append = pd.DataFrame(LoadsLinesDistributed_rows, columns=safe16_dfs.LoadsLinesDistributed.columns)
    if not df_append.empty:
        if safe16_dfs.LoadsLinesDistributed.empty:
            safe16_dfs.LoadsLinesDistributed = df_append.copy()
        else:
            existing_df = safe16_dfs.LoadsLinesDistributed
            df_to_add = df_append.copy()

            # Align dtypes before concatenation to avoid FutureWarning
            for col in df_to_add.columns:
                if col in existing_df.columns:
                    # If the new data column is all-NA, cast it to match existing dtype
                    if df_to_add[col].isnull().all():
                        try:
                            # For numeric columns, use nullable dtypes that can hold NaN
                            if pd.api.types.is_numeric_dtype(existing_df[col].dtype):
                                if pd.api.types.is_integer_dtype(existing_df[col].dtype):
                                    # Use nullable integer type
                                    df_to_add[col] = pd.array([pd.NA] * len(df_to_add), dtype=pd.Int64Dtype())
                                else:
                                    # For float types, NaN is already supported
                                    df_to_add[col] = df_to_add[col].astype(existing_df[col].dtype)
                            else:
                                df_to_add[col] = df_to_add[col].astype(existing_df[col].dtype)
                        except Exception:
                            # If casting fails, leave as is
                            pass
                    # If existing column is all-NA but new data has values, update existing dtype
                    elif existing_df[col].isnull().all() and not df_to_add[col].isnull().all():
                        try:
                            existing_df[col] = existing_df[col].astype(df_to_add[col].dtype)
                        except Exception:
                            pass

            # Concatenate the DataFrames
            safe16_dfs.LoadsLinesDistributed = pd.concat(
                [existing_df, df_to_add], ignore_index=True
            )
    return safe16_dfs


def write_corewall_group_safe16(excel_inputs, safe16_dfs):  # Changed df_core_wall_group to excel_inputs
    df_wall = excel_inputs.Wall.copy()  # Read from excel_inputs.Wall

    new_group_defs_16_rows = []
    new_group_assign_16_rows = []

    # Store original column names
    group_defs_16_cols = safe16_dfs.GroupDefinitions.columns
    group_assign_16_cols = safe16_dfs.GroupAssignments.columns

    # Create group definitions from unique 'Wall Group' names
    if 'Wall Group' in df_wall.columns:
        unique_core_wall_names = df_wall['Wall Group'].dropna().unique()
        unique_core_wall_names = [name for name in unique_core_wall_names if isinstance(name, str) and name.strip() != '']

        for core_wall_name in unique_core_wall_names:
            new_group_defs_16_rows.append([core_wall_name, core_wall_name])
    else:
        print("Warning: 'Wall Group' column not found in excel_inputs.Wall. Cannot create corewall group definitions for SAFE16.")

    # Create group assignments
    if 'Wall Group' in df_wall.columns and 'Wall' in df_wall.columns:
        for _, df_row in df_wall.iterrows():
            core_wall_name = df_row.get('Wall Group')
            wall_name = df_row.get('Wall')

            if pd.notna(core_wall_name) and isinstance(core_wall_name, str) and core_wall_name.strip() != '' and pd.notna(wall_name):
                new_group_assign_16_rows.append([core_wall_name, 'Line', wall_name])
    else:
        print("Warning: 'Wall Group' or 'Wall' column not found in excel_inputs.Wall. Cannot create corewall group assignments for SAFE16.")

    df_append_defs = pd.DataFrame(new_group_defs_16_rows, columns=group_defs_16_cols)
    if not df_append_defs.empty:
        if safe16_dfs.GroupDefinitions.empty:
            safe16_dfs.GroupDefinitions = df_append_defs.copy()  # Corrected to use df_append_defs
        else:
            safe16_dfs.GroupDefinitions = pd.concat([safe16_dfs.GroupDefinitions, df_append_defs], ignore_index=True)

    df_append_assign = pd.DataFrame(new_group_assign_16_rows, columns=group_assign_16_cols)
    if not df_append_assign.empty:
        if safe16_dfs.GroupAssignments.empty:
            safe16_dfs.GroupAssignments = df_append_assign.copy()
        else:
            safe16_dfs.GroupAssignments = pd.concat([safe16_dfs.GroupAssignments, df_append_assign], ignore_index=True)
    return safe16_dfs


def _write_corewall_load_safe16(excel_inputs, safe16_dfs):
    """Write Corewall Load. Optimized for performance and readability."""
    df_wall_definitions = excel_inputs.Wall.copy()  # Use Wall for wall-to-group mapping

    df_input_load_corewall = excel_inputs.InputLoadCoreWall.fillna(0).copy()
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()

    df_wall_length_calc = functions.cal_wall_length(excel_inputs)  # External function
    df_corewall_length_calc = functions.cal_corewall_length(excel_inputs, df_wall_length_calc)  # External function

    # Prepare for faster lookups
    df_corewall_length_indexed = df_corewall_length_calc.set_index('CoreWall Name')

    wall_mark_col = ('Wall Data', 'Wall Mark')
    wall_len_col = ('Wall Data', 'Wall Length (m)')
    # Create a mapping from Wall Mark to Wall Length, assuming Wall Marks are unique
    if wall_mark_col in df_input_load_wall.columns and wall_len_col in df_input_load_wall.columns:
        wall_length_map = pd.Series(
            df_input_load_wall[wall_len_col].values,
            index=df_input_load_wall[wall_mark_col]
        ).to_dict()
    else:
        wall_length_map = {}
        print(f"Warning: Columns for wall length mapping ('{wall_mark_col}', '{wall_len_col}') not found in df_input_load_wall for SAFE16.")

    # Call the group writing function (already updated to use excel_inputs.Wall indirectly via excel_inputs)
    safe16_dfs = write_corewall_group_safe16(excel_inputs, safe16_dfs)

    new_loads_lines_dist_16_rows = []

    # Store original column names
    loads_lines_dist_16_cols = safe16_dfs.LoadsLinesDistributed.columns

    for i, df_corewall_row in df_input_load_corewall.iterrows():
        corewall_name = df_corewall_row[('CoreWall Data', 'CoreWall Mark')]

        try:
            corewall_len = df_corewall_length_indexed.at[corewall_name, 'CoreWall Length (m)']
        except KeyError:
            print(f"Warning: Corewall '{corewall_name}' not found in length data for SAFE16. Skipping this corewall.")
            continue

        cap_thickness = df_corewall_row[('CoreWall Data', 'Cap Thickness (m)')]

        # Determine walls for this corewall_name using df_wall_definitions
        if 'Wall Group' in df_wall_definitions.columns and 'Wall' in df_wall_definitions.columns:
            associated_walls_df = df_wall_definitions[df_wall_definitions['Wall Group'] == corewall_name]
            if associated_walls_df.empty:
                print(f"Warning: No walls found assigned to corewall group '{corewall_name}' in excel_inputs.Wall for SAFE16. Loads for this group will not be distributed.")
                walls_for_corewall = []
            else:
                walls_for_corewall = associated_walls_df['Wall'].tolist()
        else:
            print(f"Warning: 'Wall Group' or 'Wall' column not found in excel_inputs.Wall. Cannot determine walls for corewall group '{corewall_name}' for SAFE16.")
            walls_for_corewall = []

        # Identify load case columns (level 0 of MultiIndex, excluding 'CoreWall Data')
        all_level0_cols = df_input_load_corewall.columns.get_level_values(0).unique()
        load_cases_level0 = [col_l0 for col_l0 in all_level0_cols if col_l0 != 'CoreWall Data']

        for load_case_name in load_cases_level0:
            load_data_subset = df_corewall_row[load_case_name]

            v_x = load_data_subset.get('Vx (kN)', 0.0)
            v_y = load_data_subset.get('Vy (kN)', 0.0)
            f_z = load_data_subset.get('Axial (kN)', 0.0)
            m_x = load_data_subset.get('Mx (kNm)', 0.0)
            m_y = load_data_subset.get('My (kNm)', 0.0)
            m_z = load_data_subset.get('Mz (kNm)', 0.0)

            mx_add = - (v_y * cap_thickness)
            my_add = + (v_x * cap_thickness)

            s_x, s_y, s_z, s_m_x, s_m_y, s_m_z, s_m_x_add, s_m_y_add = (0.0,) * 8
            if corewall_len != 0:
                s_x = v_x / corewall_len
                s_y = v_y / corewall_len
                s_z = f_z / corewall_len
                s_m_x = m_x / corewall_len
                s_m_y = m_y / corewall_len
                s_m_z = m_z / corewall_len
                s_m_x_add = mx_add / corewall_len
                s_m_y_add = my_add / corewall_len

            for wall_name_str in walls_for_corewall:
                wall_len = wall_length_map.get(wall_name_str)
                if wall_len is None:
                    continue

                # Append rows to lists
                if s_x != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Global X', 'RelDist', 0, 1, 0, wall_len,
                        s_x, s_x, None, None])
                if s_y != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Global Y', 'RelDist', 0, 1, 0, wall_len,
                        s_y, s_y, None, None])
                if s_z != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Gravity', 'RelDist', 0, 1, 0, wall_len,
                        s_z, s_z, None, None])
                if s_m_x != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global X', 'RelDist', 0, 1, 0, wall_len, None, None,
                        s_m_x, s_m_x])
                if s_m_y != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global Y', 'RelDist', 0, 1, 0, wall_len, None, None,
                        s_m_y, s_m_y])
                if s_m_z != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Gravity', 'RelDist', 0, 1, 0, wall_len, None, None,
                        -s_m_z, -s_m_z])  # Note: original code uses -s_m_z
                if s_m_x_add != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global X', 'RelDist', 0, 1, 0, wall_len, None, None,
                        s_m_x_add, s_m_x_add])
                if s_m_y_add != 0:
                    new_loads_lines_dist_16_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global Y', 'RelDist', 0, 1, 0, wall_len, None, None,
                        s_m_y_add, s_m_y_add])

    # Concatenate all collected rows at once
    df_append = pd.DataFrame(new_loads_lines_dist_16_rows, columns=loads_lines_dist_16_cols)
    if not df_append.empty:
        if safe16_dfs.LoadsLinesDistributed.empty:
            safe16_dfs.LoadsLinesDistributed = df_append.copy()
        else:
            safe16_dfs.LoadsLinesDistributed = pd.concat(
                [safe16_dfs.LoadsLinesDistributed, df_append], ignore_index=True
            )

    return safe16_dfs


def _write_point_load_safe16(excel_inputs, safe16_dfs):
    """
    Write point load data to SAFE16 format.
    
    Processes point load data from Excel inputs and converts them to SAFE16 format.
    Applies loads directly to points with moment corrections due to shear effects
    and cap thickness considerations.
    """
    # Copy and prepare input data
    df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()
    
    # Create point load group definition in SAFE16
    safe16_dfs.GroupDefinitions.loc[safe16_dfs.GroupDefinitions.index.size] = ['A.Load_Point', 'Red']
    
    # Extract load case names (exclude 'Point Data' column group)
    load_cases = [col for col in df_input_load_point.columns.get_level_values(0).unique() if 'Point Data' not in col]

    df_point_load = excel_inputs.PointLoad.copy()

    # Process each point load element
    for i, df_row in df_input_load_point.iterrows():
        name = df_row[('Point Data', 'Point Load')]
        center = df_row[('Point Data', 'Point Load')]
        t = df_row[('Point Data', 'Cap Thickness (m)')]
        
        # Link point load name to actual point geometry
        condition = df_point_load['Point Load'] == center
        matching_points = df_point_load.loc[condition, 'Point'].values
        if len(matching_points) == 0:
            print(f"Warning: Point not found for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue
        point = matching_points[0]
        if point is None:
            print(f"Warning: Point value is None for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue

        # Add point to load group and analysis mesh
        safe16_dfs.GroupAssignments.loc[safe16_dfs.GroupAssignments.index.size] = ['A.Load_Point', 'Point', point]
        safe16_dfs.ObjsAnalysisMesh01Points.loc[safe16_dfs.ObjsAnalysisMesh01Points.index.size] = [point, 'Yes']

        # Process loads for each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x = load_data['Vx (kN)']
            v_y = load_data['Vy (kN)']
            f_z = load_data['Axial (kN)']
            m_x = load_data['Mx (kNm)']
            m_y = load_data['My (kNm)']
            m_z = load_data['Mz (kNm)']

            # Apply moment loads to point
            if m_x != 0 or m_y != 0 or m_z != 0:
                safe16_dfs.LoadAssignmentsPointLoads.loc[safe16_dfs.LoadAssignmentsPointLoads.index.size] = [
                    point, load_case, 0, 0, 0, m_x, m_y, m_z, 0, 0]

            # Calculate and apply additional moments due to shear effects
            m_x_add = -v_y * t  # Additional moment about X due to Y shear
            m_y_add = v_x * t   # Additional moment about Y due to X shear

            if m_x_add != 0 or m_y_add != 0:
                safe16_dfs.LoadAssignmentsPointLoads.loc[safe16_dfs.LoadAssignmentsPointLoads.index.size] = [
                    point, load_case, 0, 0, 0, m_x_add, m_y_add, 0, 0, 0]

            # Apply direct point loads (forces)
            if v_x != 0 or v_y != 0 or f_z != 0:
                safe16_dfs.LoadAssignmentsPointLoads.loc[safe16_dfs.LoadAssignmentsPointLoads.index.size] = [
                    point, load_case, v_x, v_y, f_z, 0, 0, 0, 0, 0]    
    return safe16_dfs