import pandas as pd


def _load_sheet_data(excel_path, sheet_name, excel_inputs_obj, attribute_name, processing_function, header=None):
    """
    Helper function to load data from a specific sheet in an Excel file,
    process it, and store it in the excel_inputs_obj.
    Includes error handling for missing sheets or processing issues.
    If excel_path is not found, it re-raises FileNotFoundError.
    """
    try:
        df = pd.read_excel(excel_path, sheet_name=sheet_name, header=header)
        processed_df = processing_function(df.copy())
        setattr(excel_inputs_obj, attribute_name, processed_df)
    except FileNotFoundError:
        print(
            f"ERROR: Excel file not found at '{excel_path}'. Cannot read sheet '{sheet_name}' for attribute '{attribute_name}'.")
        raise
    except ValueError:
        print(f"Warning: Sheet '{sheet_name}' not found in '{excel_path}'. '{attribute_name}' data will be empty.")
        setattr(excel_inputs_obj, attribute_name, pd.DataFrame())
    except KeyError as e:
        print(
            f"Warning: Column {e} missing in '{sheet_name}' sheet from '{excel_path}'. {attribute_name} data processing failed; data will be empty.")
        setattr(excel_inputs_obj, attribute_name, pd.DataFrame())
    except Exception as e:
        print(
            f"An unexpected error occurred while processing '{sheet_name}' sheet from '{excel_path}': {e}. {attribute_name} data will be empty.")
        setattr(excel_inputs_obj, attribute_name, pd.DataFrame())
