﻿"""
<PERSON>le Workflow Coordinator

This module serves as the main workflow coordinator for the entire pile estimation process.
It orchestrates all components and provides the primary interface for pile estimation.

MAIN PURPOSE: Workflow Coordination & Orchestration
- Coordinates the entire pile estimation workflow from start to finish
- Manages preprocessing, grouping, load calculation, and layout generation
- Integrates pile type selection and layout optimization
- Provides unified interface for all pile estimation functionality
- Handles multiprocessing and performance optimization

WORKFLOW ORCHESTRATION:
1. Preprocessing: Resolve coordinates from Excel inputs
2. Grouping: Create element groups with auto-threshold
3. Load Calculation: Calculate group loads and centroids
4. Pile Cap Creation: Generate pile cap geometry for each group
5. Layout Generation: Generate pile layouts (via layout_engine.py)
6. Type Selection: Optimize pile types (via multi_type_optimizer.py)
7. Visualization: Create DXF outputs and results

Used by: GUI and other high-level interfaces
Uses: All pile estimation modules as orchestrated components

Author: Foundation Automation System  
Date: June 14, 2025
"""

from __future__ import annotations
from shapely.geometry import Polygon, Point, LineString, MultiPoint
from math import ceil, sqrt
import multiprocessing as mp
from multiprocessing import Pool, cpu_count
from functools import partial
from typing import List, Tuple, Dict, Any, Optional, TypedDict, DefaultDict, Union, Sequence
from dataclasses import dataclass
from pathlib import Path
import time

# Import local modules
from .utils import resolve_coordinates_from_excel
from .clustering import create_element_groups_with_auto_threshold
from .load_calculator import calculate_group_loads, calculate_load_centroids
from .pile_cap_geometry.pile_cap_geometry import create_pile_cap_polygon, PileCapConfig
from .layout_generation.layout_engine import (
    generate_pile_layout,
    generate_pile_layout_with_possible_positions,
    calculate_required_piles
)
from .utils.layout_utils import (
    determine_layout_case
)
from .visualizer import create_pile_estimation_dxf
from .utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)

# Import ExcelInputs for type conversion
from .data_types import (
    Point2D,
    Point3D,
    ColumnData,
    WallData,
    GroupElements,
    PileGroupResult,
    PileConfig,
    PileLocation,
    LocalCoordinateSystem,
    ExcelInputs
)
from .exceptions import (
    PileEstimationError,
    GroupingError,
    GeometryError,
    LoadCalculationError,
    PileLayoutError,
    ValidationError,
    InputDataError,
    ConfigurationError,
    OptimizationError,
    VisualizationError
)
from .utils import geometry_utils as geom_utils

from .clustering import create_load_based_sub_clusters, calculate_sub_cluster_pile_distribution
# Removed - function doesn't exist, using generate_pile_layout instead
from .pile_cap_geometry.pile_cap_geometry import create_pile_cap_polygon
from .pile_cap_geometry.pile_cap_geometry import create_pile_cap_for_piles_and_structures
from shapely.geometry import MultiPoint
from shapely.ops import unary_union

# Import the pile type pre-selection system
from .pile_type_selection import (
    IntegratedPileEstimationEngine,
    PileTypePreselector,
    PileTypePreselectionCriteria,
    PileTypeCandidate,
    PileTypePreselectionResult,
    PileType,
    create_pile_type_candidates_from_gui,
    integrate_with_existing_capacity_calculation
)

# Import the enhanced visualization function
from .visualizer import create_pile_estimation_dxf_with_possible_positions

# Type aliases for better code readability
ElementPoints = List[Point3D]
PileLocations = List[Point2D]
LoadDetails = Dict[str, float]
PileEstimationResult = Dict[str, Any]


def coordinate_pile_estimation_workflow(
    excel_inputs: ExcelInputs,
    pile_capacity: float = 3663.0,
    pile_diameter: float = 0.6,
    min_spacing: float = 1.8,
    edge_dist: float = 0.4,
    visualize: bool = True,
    save_path: Optional[str] = None,
    log_callback: Optional[Callable] = None,
    **kwargs
) -> PileEstimationResult:
    """
    Main pile estimation workflow coordinator that orchestrates the complete foundation design process.

    This function serves as the central orchestrator for the entire pile estimation workflow,
    managing all aspects from initial data preprocessing through final visualization. It implements
    a sophisticated multi-stage process that combines intelligent element grouping, load-based
    sub-clustering, optimized pile layout generation, and comprehensive validation.

    Workflow Architecture:
        The coordinator implements a 7-stage workflow with comprehensive error handling,
        performance optimization, and detailed logging at each stage:

        Stage 1: Configuration Preparation
            - Validates all input parameters and constraints
            - Prepares workflow configuration with default values
            - Sets up performance and processing parameters

        Stage 2: Data Preprocessing
            - Resolves structural element coordinates from Excel inputs
            - Validates data completeness and consistency
            - Extracts columns and walls with geometric properties

        Stage 3: Site Boundary Extraction
            - Identifies site boundaries from input data
            - Creates geometric constraints for pile placement
            - Validates boundary conditions

        Stage 4: Intelligent Element Grouping
            - Uses auto-threshold optimization for optimal grouping
            - Implements silhouette analysis for group validation
            - Creates balanced groups for efficient processing

        Stage 5: Group Processing Coordination
            - Supports both parallel and sequential processing modes
            - Implements load-based sub-clustering for each group
            - Generates optimized pile layouts using appropriate strategies
            - Creates pile caps with geometric validation

        Stage 6: Results Compilation
            - Aggregates results from all processed groups
            - Calculates total pile requirements and statistics
            - Validates overall design consistency

        Stage 7: Visualization Generation
            - Creates professional AutoCAD DXF files
            - Includes detailed annotations and layer organization
            - Supports multiple visualization formats

    Args:
        excel_inputs (ExcelInputs): Comprehensive Excel input data structure containing:
                                   - Structural element definitions (columns, walls)
                                   - Load data for all elements
                                   - Geometric constraints and site boundaries
                                   - Material properties and design parameters
                                   Must be properly initialized and validated.

        pile_capacity (float, optional): Maximum load capacity per pile in kN.
                                       Must be positive value representing the allowable
                                       load for a single pile. Used for calculating
                                       minimum pile requirements. Defaults to 3663.0 kN.

        pile_diameter (float, optional): Diameter of each pile in meters.
                                       Used for spacing calculations and pile cap geometry.
                                       Must be positive value. Defaults to 0.6 meters.

        min_spacing (float, optional): Minimum center-to-center spacing between piles in meters.
                                     Critical for structural integrity and construction feasibility.
                                     Must be positive and typically ≥ 2.5 × pile_diameter.
                                     Defaults to 1.8 meters.

        edge_dist (float, optional): Minimum distance from pile centerline to pile cap edge in meters.
                                   Ensures adequate concrete cover and structural integrity.
                                   Must be non-negative. Defaults to 0.4 meters.

        visualize (bool, optional): Flag to enable DXF visualization generation.
                                  When True, creates professional AutoCAD DXF files
                                  with detailed annotations. Defaults to True.

        save_path (Optional[str]): Custom path for saving visualization files.
                                 If None, uses default output directory structure.
                                 Directory will be created if it doesn't exist.

        log_callback (Optional[Callable]): Enhanced logging callback function.
                                          Supports both legacy and enhanced logging interfaces.
                                          Used for progress tracking and debugging throughout
                                          the entire workflow.

        **kwargs: Additional configuration parameters for advanced customization:
                 - grouping_min_threshold (float): Minimum threshold for element grouping
                 - grouping_max_threshold (float): Maximum threshold for element grouping
                 - use_multiprocessing (bool): Enable parallel processing for groups
                 - safety_factor (float): Safety factor for pile capacity calculations
                 - use_sub_clustering (bool): Enable load-based sub-clustering
                 - And many other advanced parameters for fine-tuning

    Returns:
        PileEstimationResult: Comprehensive results dictionary containing:
            - 'success' (bool): Overall operation success indicator
            - 'group_results' (Dict[str, PileGroupResult]): Detailed results for each group
            - 'total_results' (Dict[str, Any]): Aggregated statistics and totals
            - 'warnings' (List[str]): Non-critical warnings encountered
            - 'errors' (List[str]): Error messages if any failures occurred
            - 'dxf_file_path' (Optional[str]): Path to generated DXF visualization
            - 'config' (Dict[str, Any]): Final configuration used for processing

    Raises:
        InputDataError: If excel_inputs is invalid or contains no structural elements
        ConfigurationError: If pile parameters are invalid (negative or zero values)
        GroupingError: If element grouping fails to create valid groups
        PileLayoutError: If pile layout generation fails for all groups
        GeometryError: If geometric calculations fail due to invalid coordinates
        LoadCalculationError: If load calculations fail due to missing or invalid data
        VisualizationError: If DXF generation fails when visualization is requested

    Example:
        >>> # Basic pile estimation workflow
        >>> results = coordinate_pile_estimation_workflow(
        ...     excel_inputs=project_data,
        ...     pile_capacity=5000.0,
        ...     pile_diameter=0.8,
        ...     min_spacing=2.0,
        ...     edge_dist=0.5,
        ...     visualize=True,
        ...     log_callback=logger.info
        ... )

        >>> if results['success']:
        ...     print(f"Generated {results['total_results']['total_piles']} piles")
        ...     print(f"DXF file: {results['dxf_file_path']}")
        ... else:
        ...     print(f"Errors: {results['errors']}")

    Performance Characteristics:
        - Processing time scales with number of structural elements and groups
        - Parallel processing significantly improves performance for multiple groups
        - Memory usage is optimized through efficient data structures
        - Supports projects ranging from small buildings to large complexes

    Technical Implementation:
        - Uses advanced clustering algorithms for optimal element grouping
        - Implements load-based sub-clustering for precise pile placement
        - Supports multiple pile layout strategies (Case 1, 2, 4)
        - Generates professional-quality DXF files with proper layer organization
        - Comprehensive validation ensures structural and geometric integrity

    Integration Notes:
        - Primary interface for Foundation Agent GUI
        - Compatible with batch processing workflows
        - Supports automated testing and validation frameworks
        - Integrates with downstream design and analysis tools
    """
    log_function_entry(log_callback, "coordinate_pile_estimation_workflow",
                      pile_capacity=pile_capacity, pile_diameter=pile_diameter,
                      min_spacing=min_spacing, edge_dist=edge_dist,
                      visualize=visualize)
    
    warnings_list: List[str] = []
    errors_list: List[str] = []
    
    try:
        enhanced_log(log_callback, "=== PILE ESTIMATION WORKFLOW COORDINATION STARTED ===", 'INFO')
        
        with create_timed_logger(log_callback, "complete pile estimation workflow") as workflow_timer:
            # Step 1: Prepare configuration
            enhanced_log(log_callback, "Step 1: Preparing workflow configuration", 'INFO')
            config = _prepare_workflow_configuration(pile_capacity, pile_diameter, 
                                                    min_spacing, edge_dist, **kwargs)
            
            log_calculation_result(log_callback, "Workflow configuration", "Prepared successfully")
            enhanced_log(log_callback, f"Configuration parameters: {len(config)} settings", 'DEBUG')
            
            # Step 2: Preprocessing - resolve coordinates and validate inputs
            enhanced_log(log_callback, "Step 2: Preprocessing - resolving coordinates", 'INFO')
            with create_timed_logger(log_callback, "coordinate resolution") as coord_timer:
                columns, walls = resolve_coordinates_from_excel(excel_inputs)
            
            log_calculation_result(log_callback, "Elements resolved", f"{len(columns)} columns, {len(walls)} walls")
            
            if not columns and not walls:
                enhanced_log(log_callback, "No structural elements found in input data", 'ERROR')
                log_constraint_check(log_callback, "Structural elements present", 
                                   len(columns) + len(walls), "> 0", False)
                raise InputDataError("No structural elements found in input data")
            
            log_constraint_check(log_callback, "Structural elements present", 
                               len(columns) + len(walls), "> 0", True)
            
            # Step 3: Extract site boundary if available
            enhanced_log(log_callback, "Step 3: Extracting site boundary", 'DEBUG')
            site_polygon = _extract_site_polygon(excel_inputs)
            
            has_site_boundary = site_polygon is not None and not site_polygon.is_empty
            log_validation_result(log_callback, "Site boundary extraction", has_site_boundary,
                                f"Site boundary {'found' if has_site_boundary else 'not found'}")
            
            # Step 4: Element grouping with auto-threshold optimization
            enhanced_log(log_callback, "Step 4: Starting intelligent element grouping", 'INFO')
            
            with create_timed_logger(log_callback, "element grouping") as grouping_timer:
                element_groups = create_element_groups_with_auto_threshold(
                    columns, walls,
                    min_threshold=config.get('grouping_min_threshold', 1.0),
                    max_threshold=config.get('grouping_max_threshold', 20.0), 
                    step=config.get('grouping_step', 0.5),
                    metric=config.get('grouping_metric', 'silhouette'),
                    log_callback=log_callback
                )
            
            num_groups = len(element_groups)
            log_calculation_result(log_callback, "Element groups created", num_groups)
            
            if not element_groups:
                enhanced_log(log_callback, "No valid element groups created", 'ERROR')
                log_constraint_check(log_callback, "Valid groups created", num_groups, "> 0", False)
                raise GroupingError("No valid element groups created")
            
            log_constraint_check(log_callback, "Valid groups created", num_groups, "> 0", True)
            enhanced_log(log_callback, f"Created {num_groups} optimized element groups", 'INFO')
            
            # Step 5: Process each group using workflow coordination
            enhanced_log(log_callback, "Step 5: Processing groups with workflow coordination", 'INFO')
            
            group_results: Dict[str, PileGroupResult] = {}
              # Determine if parallel processing should be used
            use_parallel = config.get('use_multiprocessing', True) and num_groups > 1
            
            if use_parallel and config.get('parallel_groups', True):
                # Parallel group processing
                enhanced_log(log_callback, f"Using parallel processing for {num_groups} groups", 'INFO')
                log_performance_metric(log_callback, "Processing mode", "Parallel", "")
                
                with create_timed_logger(log_callback, "parallel group processing") as parallel_timer:
                    group_results = _process_groups_in_parallel(
                        element_groups, excel_inputs, site_polygon, config, log_callback
                    )
            else:
                # Sequential group processing
                enhanced_log(log_callback, f"Using sequential processing for {num_groups} groups", 'INFO')
                log_performance_metric(log_callback, "Processing mode", "Sequential", "")
                
                with create_timed_logger(log_callback, "sequential group processing") as seq_timer:
                    for group_name, group_elements in element_groups.items():
                        enhanced_log(log_callback, f"Processing group: {group_name}", 'DEBUG')
                        result = _process_single_group_with_workflow_coordination(
                            group_name, group_elements, excel_inputs, site_polygon, 
                            config, warnings_list, log_callback
                        )
                        if result:
                            group_results[group_name] = result
                            enhanced_log(log_callback, f"Group {group_name} processed successfully", 'DEBUG')
                        else:
                            enhanced_log(log_callback, f"Group {group_name} processing failed", 'WARNING')
            
            processed_groups = len(group_results)
            log_calculation_result(log_callback, "Groups processed successfully", 
                                 f"{processed_groups}/{num_groups}")
            
            if not group_results:
                enhanced_log(log_callback, "No valid pile groups generated", 'ERROR')
                log_constraint_check(log_callback, "Valid groups generated", processed_groups, "> 0", False)
                raise PileLayoutError("No valid pile groups generated")
            
            log_constraint_check(log_callback, "Valid groups generated", processed_groups, "> 0", True)
            enhanced_log(log_callback, f"Successfully processed {processed_groups} groups", 'INFO')
            
            # Step 6: Compile total results
            enhanced_log(log_callback, "Step 6: Compiling total results", 'INFO')
            with create_timed_logger(log_callback, "results compilation") as results_timer:
                total_results = _calculate_total_results(group_results)
            
            total_piles = total_results['total_piles']
            total_groups = total_results['total_groups']
            log_calculation_result(log_callback, "Total estimation results", 
                                 f"{total_piles} piles across {total_groups} groups")
              # Step 7: Create visualization if requested
            enhanced_log(log_callback, "Step 7: Creating visualization", 'INFO')
            dxf_path = None
            if visualize:
                enhanced_log(log_callback, "Creating DXF visualization", 'INFO')
                
                with create_timed_logger(log_callback, "DXF visualization creation") as viz_timer:
                    dxf_path = _create_visualization(
                        group_results, site_polygon, columns, walls, 
                        save_path, excel_inputs, config, log_callback
                    )
                
                if dxf_path:
                    log_calculation_result(log_callback, "DXF file created", dxf_path)
                    enhanced_log(log_callback, f"Visualization saved to: {dxf_path}", 'INFO')
                else:
                    enhanced_log(log_callback, "Visualization creation failed", 'WARNING')
            else:
                enhanced_log(log_callback, "Visualization skipped (not requested)", 'DEBUG')
        
        enhanced_log(log_callback, "=== PILE ESTIMATION WORKFLOW COMPLETED SUCCESSFULLY ===", 'INFO')
        
        # Log overall workflow performance
        workflow_duration = workflow_timer.get_duration()
        if workflow_duration:
            log_performance_metric(log_callback, "Total workflow time", workflow_duration, "seconds")
            
            # Calculate processing efficiency
            piles_per_second = total_results['total_piles'] / workflow_duration
            log_performance_metric(log_callback, "Processing efficiency", piles_per_second, "piles/sec")
        
        # Compile final results
        final_results = {
            'success': True,
            'group_results': group_results,
            'total_results': total_results,
            'warnings': warnings_list,
            'errors': errors_list,
            'dxf_file_path': dxf_path,
            'config': config
        }
        
        log_validation_result(log_callback, "Pile estimation workflow", True,
                            f"Completed with {total_results['total_piles']} piles")
        
        log_function_exit(log_callback, "coordinate_pile_estimation_workflow", final_results)
        return final_results
        
    except Exception as e:
        error_msg = f"Pile estimation workflow failed: {str(e)}"
        errors_list.append(error_msg)
        enhanced_log(log_callback, error_msg, 'ERROR')
        log_error_with_context(log_callback, e, "coordinate_pile_estimation_workflow")
        
        error_results = {
            'success': False,
            'group_results': {},
            'total_results': {},
            'warnings': warnings_list,
            'errors': errors_list,
            'dxf_file_path': None,
            'config': {},        }
        
        enhanced_log(log_callback, "=== PILE ESTIMATION WORKFLOW FAILED ===", 'ERROR')
        log_function_exit(log_callback, "coordinate_pile_estimation_workflow", error_results)
        return error_results


def _prepare_workflow_configuration(pile_capacity: float, pile_diameter: float, 
                                  min_spacing: float, edge_dist: float,
                                  **kwargs) -> Dict[str, Any]:
    """Prepare configuration parameters for workflow coordination."""
    config = {
        # Basic pile parameters
        'pile_capacity': pile_capacity,
        'pile_diameter': pile_diameter,
        'min_spacing': min_spacing,
        'edge_dist': edge_dist,
        'min_piles_per_group': kwargs.get('min_piles_per_group', 1),
        'safety_factor': kwargs.get('safety_factor', 1.2),
        
        # Grouping parameters
        'grouping_min_threshold': kwargs.get('grouping_min_threshold', 1),
        'grouping_max_threshold': kwargs.get('grouping_max_threshold', 10),
        'grouping_step': kwargs.get('grouping_step', 1.0),
        'grouping_metric': kwargs.get('grouping_metric', 'silhouette'),
        'use_auto_grouping': kwargs.get('use_auto_grouping', True),
        
        # Sub-clustering parameters
        'use_sub_clustering': kwargs.get('use_sub_clustering', True),
        'max_sub_clusters': kwargs.get('max_sub_clusters', 1000),
        'min_sub_cluster_size': kwargs.get('min_sub_cluster_size', 1),
        
        # Visualization parameters
        'units': kwargs.get('units', 'meters'),
        'text_height': kwargs.get('text_height', 0.5),
        
        # Performance parameters
        'use_multiprocessing': kwargs.get('use_multiprocessing', True),
        'n_processes': kwargs.get('n_processes', None),
        'parallel_groups': kwargs.get('parallel_groups', True),
        
        # Layout generation parameters
        'case4_use_multiprocessing': kwargs.get('case4_use_multiprocessing', True),
        'case4_force_sequential': kwargs.get('case4_force_sequential', False),
        'case4_max_processes': kwargs.get('case4_max_processes', None),
    }
    
    # Validate configuration
    if pile_capacity <= 0:
        raise ConfigurationError("Pile capacity must be positive")
    if pile_diameter <= 0:
        raise ConfigurationError("Pile diameter must be positive")
    if min_spacing <= 0:
        raise ConfigurationError("Minimum spacing must be positive")
    if edge_dist < 0:
        raise ConfigurationError("Edge distance cannot be negative")
    
    return config


def _extract_site_polygon(excel_inputs: Any) -> Optional[Polygon]:
    """Extract site boundary polygon from Excel inputs."""
    try:
        # Implementation depends on Excel input structure
        # This is a placeholder - actual implementation would extract boundary
        return None
    except Exception:
        return None


def _process_single_group_with_workflow_coordination(group_name: str, 
                                                   group_elements: GroupElements, 
                                                   excel_inputs: Any,
                                                   site_polygon: Optional[Polygon],
                                                   config: Dict[str, Any], 
                                                   warnings_list: List[str],
                                                   log_callback=None) -> Optional[PileGroupResult]:
    """
    Process a single group using workflow coordination with load-based sub-clustering.
    
    This function coordinates the complete workflow for a single group:
    1. Load calculation and centroid determination
    2. Sub-cluster creation for optimized pile placement
    3. Pile distribution among sub-clusters
    4. Layout generation using appropriate strategy
    5. Pile cap creation and validation
    """
    # Track group processing details
    num_columns = len(group_elements.get('columns', []))
    num_walls = len(group_elements.get('walls', []))
    if log_callback:
        log_callback(f"Processing {group_name}: {num_columns} columns, {num_walls} walls")

    # Step 1: Calculate load and centroid
    try:
        # Validate group_elements structure
        if not isinstance(group_elements, dict):
            if log_callback:
                log_callback(f"   ERROR: group_elements is not a dictionary, it's {type(group_elements)}")
                log_callback(f"   Converting to proper GroupElements format...")
            
            # Try to handle if group_elements is a tuple or other format
            if isinstance(group_elements, (tuple, list)) and len(group_elements) == 2:
                # Assume it's (columns, walls) format
                group_elements = {
                    'columns': group_elements[0] if group_elements[0] else [],
                    'walls': group_elements[1] if group_elements[1] else []
                }
                if log_callback:
                    log_callback(f"   Converted tuple to dict format: {len(group_elements['columns'])} columns, {len(group_elements['walls'])} walls")
            else:
                raise TypeError(f"Cannot convert group_elements of type {type(group_elements)} to GroupElements format")
        
        total_load_group, load_warnings = calculate_group_loads(group_elements, excel_inputs, log_callback)
        load_centroid, load_details, centroid_warnings = calculate_load_centroids(group_elements, excel_inputs, log_callback)
        
        # Create comprehensive load details
        load_details = {
            'total_load_kn': total_load_group,
            'centroid': load_centroid or (0.0, 0.0),
            'warnings': load_warnings + centroid_warnings
        }
        
        if log_callback:
            log_callback(f" {group_name}: Total load = {total_load_group:.1f} kN, Centroid = ({load_centroid[0]:.2f}, {load_centroid[1]:.2f})")

    except Exception as e:
        if log_callback:
            log_callback(f"{group_name}: Load calculation failed - {e}")
        warnings_list.append(f"Load calculation failed for group {group_name}: {str(e)}")
        return None

    # Step 2: Calculate required piles
    min_piles = calculate_required_piles(total_load_group, config['pile_capacity'], config['safety_factor'])
    if log_callback:
        log_callback(f" {group_name}: Minimum required piles = {min_piles}")
    
    try:
        # Step 3: Create load-based sub-clusters for optimized placement
        sub_clusters = create_load_based_sub_clusters(
            group_elements, excel_inputs, config, log_callback
        )
        
        if log_callback:
            log_callback(f" {group_name}: Created {len(sub_clusters)} sub-clusters")
            
    except Exception as e:
        if log_callback:
            log_callback(f"{group_name}: Sub-clustering failed - {e}")
        warnings_list.append(f"Sub-clustering failed for group {group_name}: {str(e)}")
        return None

    # Step 4: Distribute piles among sub-clusters
    pile_distribution = calculate_sub_cluster_pile_distribution(
        sub_clusters, 
        min_piles,
        min_piles_per_cluster=1
    )
    
    if log_callback:
        log_callback(f" {group_name}: Pile distribution = {dict(pile_distribution)}")
    
    # Step 5: Create initial pile cap to get unified local coordinate system
    initial_cap_result = create_pile_cap_polygon(
        group_elements, excel_inputs, site_polygon, config['edge_dist']
    )
    
    # Extract unified local coordinate system
    unified_local_system = None
    if initial_cap_result['is_valid']:
        unified_local_system = initial_cap_result.get('local_coordinate_system')
    else:
        if log_callback:
            log_callback(f" {group_name}: Initial pile cap creation failed")

    # Step 6: Generate pile layout using workflow coordination
    pile_locations, actual_piles, layout_warnings = generate_pile_layout(
        sub_clusters=sub_clusters,
        pile_distribution=pile_distribution,
        pile_capacity=config['pile_capacity'],
        pile_diameter=config['pile_diameter'],
        min_spacing=config['min_spacing'],
        edge_dist=config['edge_dist'],
        site_boundary=site_polygon,
        initial_local_system=unified_local_system,
        excel_inputs=excel_inputs,
        config_overrides=config
    )

    warnings_list.extend(layout_warnings)

    if not pile_locations:
        if log_callback:
            log_callback(f"{group_name}: No pile locations generated")
        warnings_list.append(f"No pile locations generated for group {group_name}")
        return None

    # Step 7: Create final pile cap that accommodates both structures and piles
    final_cap_result = create_pile_cap_for_piles_and_structures(
        group_elements=group_elements,
        pile_locations=pile_locations,
        excel_inputs=excel_inputs,
        site_poly=site_polygon,
        edge_dist=config['edge_dist'],
        pile_radius=config['pile_diameter'] / 2.0
    )
    
    if log_callback:
        log_callback(f" {group_name}: Final pile cap created")
    
    if final_cap_result['is_valid'] and final_cap_result['polygon']:
        expanded_cap_polygon = final_cap_result['polygon']
    else:
        expanded_cap_polygon = initial_cap_result.get('polygon')
    
    if not final_cap_result['is_valid']:
        if log_callback:
            log_callback(f" {group_name}: Final pile cap validation failed")
        warnings_list.append(f"Final pile cap validation failed for group {group_name}")

    # Step 8: Calculate utilization and compile results
    utilization = total_load_group / (actual_piles * config['pile_capacity']) if actual_piles > 0 else 0
    if log_callback:
        log_callback(f" {group_name}: Final results - {actual_piles} piles, {utilization:.3f} utilization")
    
    group_result = PileGroupResult(
        group_name=group_name,
        elements=group_elements,
        total_load_kn=total_load_group,
        load_centroid=load_centroid,
        load_details=load_details,
        pile_cap_polygon=expanded_cap_polygon,
        pile_locations=pile_locations,
        num_piles=actual_piles,
        pile_capacity_kn=config['pile_capacity'],
        utilization_ratio=utilization,
        warnings=layout_warnings,
        sub_clusters=sub_clusters,
        pile_distribution=pile_distribution
    )

    # Validate group result for complex cases
    if log_callback and len(sub_clusters) > 1:
        log_callback(f"{group_name}: Complex multi-cluster group processed successfully")

    return group_result


def _process_groups_in_parallel(element_groups: Dict[str, GroupElements],
                              excel_inputs: Any,
                              site_polygon: Optional[Polygon],
                              config: Dict[str, Any],
                              log_callback=None) -> Dict[str, PileGroupResult]:
    """Process multiple groups in parallel using multiprocessing."""
    group_results = {}
    
    # Prepare arguments for parallel processing
    process_args = [
        (group_name, group_elements, excel_inputs, site_polygon, config)
        for group_name, group_elements in element_groups.items()
    ]
    
    # Determine number of processes
    n_processes = config.get('n_processes') or min(cpu_count(), len(element_groups))
    
    try:
        with Pool(processes=n_processes) as pool:
            results = pool.map(_process_group_wrapper, process_args)
        
        # Collect results
        for group_name, result, warnings in results:
            if result:
                group_results[group_name] = result
                if log_callback:
                    log_callback(f"{group_name}: Parallel processing completed")
        
        return group_results
        
    except Exception as e:
        error_msg = f"Parallel processing failed: {e}"
        if log_callback:
            log_callback(f"ERROR: {error_msg}")
        raise PileLayoutError(error_msg) from e


def _process_group_wrapper(args: Tuple[str, GroupElements, Any, Optional[Polygon], Dict[str, Any]]) -> Tuple[str, Optional[PileGroupResult], List[str]]:
    """
    Multiprocessing wrapper for processing a single group.
    Returns tuple of (group_name, result, warnings) for safe multiprocessing.
    """
    group_name, group_elements, excel_inputs, site_polygon, config = args
    warnings_list = []
    
    try:
        result = _process_single_group_with_workflow_coordination(
            group_name, group_elements, excel_inputs, site_polygon, 
            config, warnings_list, None  # No log callback for parallel processing
        )
        return (group_name, result, warnings_list)
        
    except Exception as e:
        return (group_name, None, [f"Group {group_name} processing failed: {str(e)}"])


def _calculate_total_results(group_results: Dict[str, PileGroupResult]) -> Dict[str, Any]:
    """Calculate total results across all groups."""
    total_piles = sum(result.num_piles for result in group_results.values())
    total_load = sum(result.total_load_kn for result in group_results.values())
    total_capacity = sum(result.num_piles * result.pile_capacity_kn for result in group_results.values())
    
    overall_utilization = total_load / total_capacity if total_capacity > 0 else 0
    
    return {
        'total_groups': len(group_results),
        'total_piles': total_piles,
        'total_load_kn': total_load,
        'total_capacity_kn': total_capacity,
        'overall_utilization_ratio': overall_utilization,
        'average_piles_per_group': total_piles / len(group_results) if group_results else 0
    }


def _create_visualization(group_results: Dict[str, PileGroupResult], 
                         site_polygon: Optional[Polygon],
                         columns: List[ColumnData], walls: List[WallData],
                         save_path: Optional[str], excel_inputs: Any,
                         config: Dict[str, Any], log_callback=None) -> Optional[str]:
    """Create DXF visualization of pile estimation results."""
    try:
        # Prepare group results for visualization
        viz_group_results = {
            name: data['group_result'] 
            for name, data in group_results.items()
        }
        
        # No need to collect separate viable positions - preselection data is now in group_result
        if log_callback:
            total_preselection_positions = 0
            for name, group_result in viz_group_results.items():
                if hasattr(group_result, 'preselection_analysis') and group_result.preselection_analysis:
                    preselection_data = group_result.preselection_analysis
                    all_pile_type_positions = preselection_data.get('all_pile_type_positions', {})
                    for pile_type, positions in all_pile_type_positions.items():
                        total_preselection_positions += len(positions) if positions else 0
            
            if total_preselection_positions > 0:
                log_callback(f" Groups contain {total_preselection_positions} total preselection positions for visualization")
        
        # Use standard visualization - preselection data is embedded in group_results
        dxf_file_path = create_pile_estimation_dxf(
            group_results=viz_group_results,
            site_boundary=site_polygon,
            columns=columns,
            walls=walls,
            save_path=save_path,
            excel_inputs=excel_inputs,
            pile_diameter=0.6,  # Default pile diameter for visualization
            log_callback=log_callback
        )
        
        if log_callback and dxf_file_path:
            log_callback(f"DXF visualization created: {dxf_file_path}")
        
        return dxf_file_path
        
    except Exception as e:
        if log_callback:
            log_callback(f"Visualization creation failed: {e}")
        return None


def coordinate_pile_estimation_with_multi_type_optimization(
    excel_inputs: ExcelInputs,
    selected_pile_types: List[Dict[str, Any]],
    edge_dist: float = 0.4,
    optimization_method: str = "cost_efficiency",
    output_dir: Optional[str] = None,
    visualize: bool = True,
    save_path: Optional[str] = None,
    log_callback: Optional[Callable] = None,
    **kwargs
) -> PileEstimationResult:
    """
    Coordinate advanced pile estimation workflow with AI-driven pile type pre-selection and NSGA-III optimization.

    This function represents the pinnacle of the Foundation Agent's intelligent design capabilities,
    integrating artificial intelligence for pile type pre-selection with state-of-the-art multi-objective
    optimization using the NSGA-III algorithm. It provides a complete end-to-end solution for optimal
    pile foundation design with professional visualization and comprehensive analysis.

    Advanced AI Integration Workflow:
        The function implements a sophisticated 7-stage workflow that combines multiple AI techniques
        with advanced optimization algorithms:

        Stage 1: Data Preprocessing and Validation
            - Resolves structural element coordinates from Excel inputs
            - Validates data completeness and geometric consistency
            - Extracts site boundaries and constraints

        Stage 2: Intelligent Element Grouping
            - Uses auto-threshold optimization with silhouette analysis
            - Creates balanced groups for efficient AI processing
            - Optimizes group sizes for parallel processing

        Stage 3: AI Pile Type Pre-Selection Engine
            - Initializes IntegratedPileEstimationEngine with AI capabilities
            - Analyzes load patterns, soil conditions, and structural requirements
            - Applies priority-based selection (DHP → SHP → BP) with intelligent criteria

        Stage 4: Load Analysis and Centroid Calculation
            - Calculates comprehensive load distributions for each group
            - Determines load-weighted centroids for optimal pile placement
            - Validates load calculations with error recovery

        Stage 5: NSGA-III Multi-Objective Optimization
            - Runs advanced genetic algorithm optimization for each group
            - Optimizes multiple objectives: cost, performance, constructability
            - Generates optimal pile layouts with constraint satisfaction

        Stage 6: Professional Visualization Generation
            - Creates comprehensive AutoCAD DXF files with layer organization
            - Includes pre-selection analysis and final optimized results
            - Provides detailed annotations and construction documentation

        Stage 7: Results Compilation and Quality Assurance
            - Aggregates optimization results across all groups
            - Calculates performance metrics and utilization ratios
            - Validates design consistency and structural integrity

    AI Pre-Selection Intelligence:
        The AI system evaluates multiple factors for optimal pile type selection:
        - Load magnitude and distribution patterns
        - Soil conditions and bearing capacity requirements
        - Construction constraints and site accessibility
        - Cost-effectiveness and material availability
        - Structural performance and safety factors

    Args:
        excel_inputs (ExcelInputs): Comprehensive Excel input data structure containing:
                                   - Structural element definitions with complete geometry
                                   - Load data for all structural components
                                   - Site constraints and boundary conditions
                                   - Material properties and design parameters
                                   Must be properly validated and initialized.

        selected_pile_types (List[Dict[str, Any]]): List of pile type configurations from GUI.
                                                   Each dictionary must contain:
                                                   - 'type': Pile type identifier ('DHP', 'SHP', 'BP')
                                                   - 'capacity': Pile capacity in kN (validated positive)
                                                   - 'diameter': Pile diameter in meters (for BP) or None
                                                   - 'section': Steel section identifier (for DHP/SHP)
                                                   - 'min_spacing': Minimum spacing in meters (validated positive)
                                                   Used by AI for intelligent type selection.

        edge_dist (float, optional): Edge distance for pile caps in meters (user-defined from GUI).
                                   Defines minimum distance from pile centerline to cap edge.
                                   Critical for structural integrity and construction feasibility.
                                   Must be non-negative. Defaults to 0.4 meters.

        optimization_method (str, optional): NSGA-III optimization strategy.
                                           Supported methods:
                                           - 'cost_efficiency': Minimize cost while maximizing efficiency
                                           - 'performance': Prioritize structural performance
                                           - 'balanced': Balance cost and performance objectives
                                           Defaults to 'cost_efficiency'.

        output_dir (Optional[str]): Output directory for all generated files and results.
                                  If None, uses default directory structure.
                                  Directory will be created if it doesn't exist.
                                  Used for DXF files, analysis reports, and metadata.

        visualize (bool, optional): Flag to enable comprehensive DXF visualization generation.
                                  When True, creates professional AutoCAD DXF files with:
                                  - Pre-selection analysis layers
                                  - Final optimized layouts
                                  - Detailed annotations and dimensions
                                  Defaults to True.

        save_path (Optional[str]): Custom path for saving visualization files.
                                 Overrides output_dir for visualization files only.
                                 If None, uses output_dir or default structure.

        log_callback (Optional[Callable]): Enhanced logging callback function.
                                          Supports both legacy and enhanced logging interfaces.
                                          Provides detailed progress tracking throughout
                                          the entire AI optimization workflow.

        **kwargs: Advanced configuration parameters for fine-tuning:
                 - grouping_min_threshold (float): Minimum threshold for element grouping
                 - grouping_max_threshold (float): Maximum threshold for element grouping
                 - config_overrides (Dict): Override default optimization parameters
                 - And other advanced parameters for expert users

    Returns:
        PileEstimationResult: Comprehensive optimization results dictionary containing:
            - 'success' (bool): Overall operation success indicator
            - 'summary' (Dict): Summary statistics with total groups and piles
            - 'optimization' (Dict): NSGA-III optimization results and metrics
            - 'groups' (Dict): Detailed results for each optimized group
            - 'group_results' (Dict): PileGroupResult objects for each group
            - 'total_results' (Dict): Aggregated statistics and performance metrics
            - 'warnings' (List[str]): Non-critical warnings encountered
            - 'errors' (List[str]): Error messages if any failures occurred
            - 'dxf_file' (Optional[str]): Path to comprehensive DXF visualization
            - 'output_dir' (Optional[str]): Directory containing all output files
            - 'config' (Dict): Final configuration used for optimization

    Raises:
        InputDataError: If excel_inputs contains no valid structural elements
        GroupingError: If element grouping fails to create valid groups
        OptimizationError: If NSGA-III optimization fails for all groups
        VisualizationError: If DXF generation fails when visualization is requested
        ConfigurationError: If pile type configurations are invalid
        RuntimeError: If AI pre-selection engine initialization fails

    Example:
        >>> # Define pile types for AI selection
        >>> pile_types = [
        ...     {
        ...         'type': 'DHP',
        ...         'capacity': 3663.0,
        ...         'section': 'UBP_305x305x223',
        ...         'diameter': None,
        ...         'min_spacing': 1.2
        ...     },
        ...     {
        ...         'type': 'SHP',
        ...         'capacity': 6106.0,
        ...         'section': 'UBP_305x305x223',
        ...         'diameter': None,
        ...         'min_spacing': 1.85
        ...     },
        ...     {
        ...         'type': 'BP',
        ...         'capacity': 5000.0,
        ...         'section': None,
        ...         'diameter': 0.6,
        ...         'min_spacing': 1.8
        ...     }
        ... ]

        >>> # Run AI optimization
        >>> results = coordinate_pile_estimation_with_multi_type_optimization(
        ...     excel_inputs=project_data,
        ...     selected_pile_types=pile_types,
        ...     edge_dist=0.4,
        ...     optimization_method="cost_efficiency",
        ...     output_dir="./ai_optimization_results",
        ...     visualize=True,
        ...     log_callback=logger.info
        ... )

        >>> if results['success']:
        ...     print(f"AI optimized {results['summary']['total_groups']} groups")
        ...     print(f"Total piles: {results['summary']['total_piles']}")
        ...     print(f"Pile types used: {list(results['optimization']['pile_type_usage'].keys())}")
        ...     print(f"DXF file: {results['dxf_file']}")

    Technical Implementation:
        - Utilizes NSGA-III (Non-dominated Sorting Genetic Algorithm III) for multi-objective optimization
        - Implements AI-driven pile type pre-selection with intelligent criteria evaluation
        - Supports parallel processing for large-scale optimization problems
        - Generates professional AutoCAD DXF files with comprehensive layer organization
        - Provides real-time progress tracking and detailed performance metrics

    Performance Characteristics:
        - AI pre-selection significantly reduces optimization search space
        - NSGA-III provides globally optimal solutions for complex design problems
        - Processing time scales efficiently with problem complexity
        - Memory usage is optimized for large-scale foundation design projects
        - Supports projects from small buildings to large industrial complexes

    Integration Notes:
        - Primary interface for Foundation Agent GUI's advanced optimization features
        - Compatible with automated design workflows and batch processing
        - Integrates with downstream analysis and construction documentation tools
        - Supports export to various CAD and analysis software platforms
    """
    warnings_list: List[str] = []
    errors_list: List[str] = []
    
    if log_callback:
        log_callback(" Starting AI pile type pre-selection with NSGA-III optimization workflow...")
        log_callback(" Using AI Agent to determine optimal pile types for each cluster")
    
    try:
        # Step 1: Preprocessing - resolve coordinates and validate inputs
        columns, walls = resolve_coordinates_from_excel(excel_inputs)
        
        if log_callback:
            log_callback(f" Preprocessing complete: {len(columns)} columns, {len(walls)} walls")
        
        if not columns and not walls:
            raise InputDataError("No structural elements found in input data")
        
        # Step 2: Extract site boundary if available
        site_polygon = _extract_site_polygon(excel_inputs)
        
        # Step 3: Element grouping with auto-threshold optimization
        if log_callback:
            log_callback(" Creating optimized element groups for AI pile type selection...")
        
        element_groups = create_element_groups_with_auto_threshold(
            columns, walls,
            min_threshold=kwargs.get('grouping_min_threshold', 1.0),
            max_threshold=kwargs.get('grouping_max_threshold', 20.0), 
            step=kwargs.get('grouping_step', 0.5),
            metric=kwargs.get('grouping_metric', 'silhouette'),
            log_callback=log_callback
        )
        
        if log_callback:
            log_callback(f"Created {len(element_groups)} optimized element groups")
        
        if not element_groups:
            raise GroupingError("No valid element groups created")
        
        # Step 4: Initialize the Integrated Pile Estimation Engine
        integrated_engine = IntegratedPileEstimationEngine(log_callback=log_callback)
        
        # Step 5: Process each group with AI pile type pre-selection + NSGA-III optimization
        if log_callback:
            log_callback(" Starting AI pile type pre-selection and NSGA-III optimization for each group...")
        
        optimized_groups = {}
        pile_type_usage = {}
        total_piles = 0
        total_load = 0.0
        total_capacity = 0.0
        
        for group_name, group_elements in element_groups.items():
            if log_callback:
                log_callback(f" Processing group: {group_name}")
                log_callback("   Step 1: Calculating group loads...")
                # Debug logging to understand group_elements structure
                log_callback(f"   group_elements type: {type(group_elements)}")
                if hasattr(group_elements, 'keys'):
                    log_callback(f"   group_elements keys: {list(group_elements.keys())}")
                log_callback(f"   group_elements content: {str(group_elements)[:200]}...")
            
            # Calculate group loads
            try:
                # Validate group_elements structure
                if not isinstance(group_elements, dict):
                    if log_callback:
                        log_callback(f"   ERROR: group_elements is not a dictionary, it's {type(group_elements)}")
                        log_callback(f"   Converting to proper GroupElements format...")
                    
                    # Try to handle if group_elements is a tuple or other format
                    if isinstance(group_elements, (tuple, list)) and len(group_elements) == 2:
                        # Assume it's (columns, walls) format
                        group_elements = {
                            'columns': group_elements[0] if group_elements[0] else [],
                            'walls': group_elements[1] if group_elements[1] else []
                        }
                        if log_callback:
                            log_callback(f"   Converted tuple to dict format: {len(group_elements['columns'])} columns, {len(group_elements['walls'])} walls")
                    else:
                        raise TypeError(f"Cannot convert group_elements of type {type(group_elements)} to GroupElements format")
                
                total_load_group, load_warnings = calculate_group_loads(group_elements, excel_inputs, log_callback)
                load_centroid, load_details, centroid_warnings = calculate_load_centroids(group_elements, excel_inputs, log_callback)
                
                # Create comprehensive load details
                load_details = {
                    'total_load_kn': total_load_group,
                    'centroid': load_centroid or (0.0, 0.0),
                    'warnings': load_warnings + centroid_warnings
                }
                
                if log_callback:
                    log_callback(f"   Total load: {total_load_group:.1f} kN")
                    log_callback(f"   Load centroid: ({load_centroid[0]:.2f}, {load_centroid[1]:.2f})" if load_centroid else "   Load centroid: (0.00, 0.00)")
                    log_callback("   Step 2: AI pile type pre-selection (DHP SHP BP)...")
            
            except Exception as e:
                if log_callback:
                    log_callback(f"   Failed to calculate loads for group {group_name}: {e}")
                errors_list.append(f"Load calculation failed for group {group_name}: {str(e)}")
                continue
            
            # Run integrated pile estimation with pre-selection + NSGA-III
            try:
                estimation_result = integrated_engine.estimate_pile_layout_with_preselection(
                    group_elements=group_elements,
                    required_load=total_load_group,
                    excel_inputs=excel_inputs,
                    selected_pile_types=selected_pile_types,
                    user_edge_distance=edge_dist,  # Use GUI edge distance
                    site_boundary=site_polygon,
                    config_overrides=kwargs.get('config_overrides'),
                    create_preselection_dxf=False,  # Disabled - using consolidated DXF instead
                    output_dir=output_dir  # Pass output directory for DXF files
                )
                
                # Extract results
                selected_pile_type = estimation_result['selected_pile_type']
                optimized_layout = estimation_result['optimized_layout']
                pile_positions = optimized_layout['pile_positions']
                
                if log_callback:
                    log_callback(f"   AI selected pile type: {selected_pile_type['display_name']}")
                    log_callback(f"   NSGA-III optimized layout: {len(pile_positions)} piles")
                    log_callback(f"   Capacity per pile: {selected_pile_type['capacity_per_pile']:.1f} kN")
                
                # Create pile cap polygon for the optimized layout
                pile_cap_result = create_pile_cap_polygon(
                    group_elements=group_elements,
                    excel_inputs=excel_inputs,
                    site_poly=site_polygon,
                    edge_dist=edge_dist
                )
                
                # Convert positions to PileLocation format (Point2D tuples)
                pile_locations = [(pos[0], pos[1]) for pos in pile_positions]
                
                # Create PileTypeSpec from selected pile type for proper BP visualization
                from .data_types.pile_types import PileTypeSpec, PileType

                # Create pile type enum
                pile_type_enum = PileType.BP if selected_pile_type['type'] == 'BP' else (
                    PileType.SHP if selected_pile_type['type'] == 'SHP' else PileType.DHP
                )

                # Create pile spec with all necessary information
                pile_spec = PileTypeSpec(
                    pile_type=pile_type_enum,
                    capacity=selected_pile_type['capacity_per_pile'],
                    diameter=selected_pile_type.get('diameter'),  # This will be None for DHP/SHP
                    section=selected_pile_type.get('section'),
                    min_spacing=selected_pile_type.get('min_spacing', 1.8)
                )

                # Create group result with enhanced data including pile spec
                group_result = PileGroupResult(
                    group_name=group_name,
                    elements=group_elements,
                    total_load_kn=total_load_group,
                    load_centroid=load_centroid,
                    load_details=load_details,
                    pile_cap_polygon=pile_cap_result.get('polygon'),
                    pile_locations=pile_locations,
                    num_piles=len(pile_positions),
                    pile_capacity_kn=selected_pile_type['capacity_per_pile'],
                    utilization_ratio=total_load_group / (len(pile_positions) * selected_pile_type['capacity_per_pile']),
                    warnings=[],
                    sub_clusters={},
                    pile_distribution={},
                    selected_pile_spec=pile_spec  # Add the pile spec for proper BP visualization
                )
                
                # Add pile specification to group result for visualization
                group_result.pile_spec = selected_pile_type
                
                # CRITICAL FIX: Add preselection_analysis to group_result for visualization
                group_result.preselection_analysis = estimation_result['preselection_analysis']
                group_result.selected_pile_spec = selected_pile_type  # For enhanced visualization
                
                # Store optimized group with enhanced visualization data
                optimized_groups[group_name] = {
                    'group_result': group_result,
                    'pile_type': selected_pile_type['type'],
                    'pile_type_display': selected_pile_type['display_name'],
                    'pile_spec': selected_pile_type,
                    'preselection_analysis': estimation_result['preselection_analysis'],
                    'pile_cap_geometry': estimation_result['pile_cap_geometry'],
                    'process_metadata': estimation_result['process_metadata']
                }
                
                # Update pile type usage tracking
                pile_type_name = selected_pile_type['type']
                if pile_type_name not in pile_type_usage:
                    pile_type_usage[pile_type_name] = 0
                pile_type_usage[pile_type_name] += len(pile_positions)
                
                # Update totals
                total_piles += len(pile_positions)
                total_load += total_load_group
                total_capacity += len(pile_positions) * selected_pile_type['capacity_per_pile']
                
            except Exception as e:
                error_msg = f"AI pile estimation failed for group {group_name}: {str(e)}"
                if log_callback:
                    log_callback(f"   {error_msg}")
                errors_list.append(error_msg)
                continue
        
        # Step 6: Create visualization if requested
        dxf_file_path = None
        if visualize and optimized_groups:
            if log_callback:
                log_callback(" Creating comprehensive AutoCAD DXF visualization with preselection analysis and optimized layouts...")
            
            try:
                # Prepare group results for visualization
                viz_group_results = {
                    name: data['group_result'] 
                    for name, data in optimized_groups.items()
                }
                
                # No need to collect separate viable positions - preselection data is now in group_result
                if log_callback:
                    total_preselection_positions = 0
                    for name, group_result in viz_group_results.items():
                        if hasattr(group_result, 'preselection_analysis') and group_result.preselection_analysis:
                            preselection_data = group_result.preselection_analysis
                            all_pile_type_positions = preselection_data.get('all_pile_type_positions', {})
                            for pile_type, positions in all_pile_type_positions.items():
                                total_preselection_positions += len(positions) if positions else 0
                    
                    if total_preselection_positions > 0:
                        log_callback(f" Groups contain {total_preselection_positions} total preselection positions for visualization")
                
                # Use standard visualization - preselection data is embedded in group_results
                dxf_file_path = create_pile_estimation_dxf(
                    group_results=viz_group_results,
                    site_boundary=site_polygon,
                    columns=columns,
                    walls=walls,
                    save_path=save_path or output_dir,
                    excel_inputs=excel_inputs,
                    pile_diameter=0.6,  # Default pile diameter for visualization
                    log_callback=log_callback
                )
                
                if log_callback:
                    log_callback(f"Comprehensive pile estimation DXF created: {dxf_file_path}")
                    log_callback(f"    Includes preselection analysis AND final optimized results")
                    log_callback(f"    Shows all pile type options and optimal rectangles for {len(optimized_groups)} groups")
                
            except Exception as e:
                if log_callback:
                    log_callback(f" Visualization creation failed: {e}")
                warnings_list.append(f"Visualization failed: {e}")
        
        # Step 7: Compile final results
        overall_utilization = total_load / total_capacity if total_capacity > 0 else 0
        
        optimization_summary = {
            'method': 'AI_preselection_plus_NSGA3',
            'pile_type_usage': pile_type_usage,
            'optimized': True,
            'preselection_order': ['DHP', 'SHP', 'BP'],
            'groups_processed': len(optimized_groups),
            'total_groups_attempted': len(element_groups)
        }
        
        total_results = {
            'total_groups': len(optimized_groups),
            'total_piles': total_piles,
            'total_load_kn': total_load,
            'total_capacity_kn': total_capacity,
            'overall_utilization_ratio': overall_utilization,
            'average_piles_per_group': total_piles / len(optimized_groups) if optimized_groups else 0,
            'optimization_summary': optimization_summary
        }
        
        if log_callback:
            log_callback("AI pile type pre-selection with NSGA-III optimization completed!")
            log_callback(f" Summary: {len(optimized_groups)} groups, {total_piles} piles")
            log_callback(f" Pile types used: {', '.join(pile_type_usage.keys())}")
            if dxf_file_path:
                log_callback(f" AutoCAD DXF file: {dxf_file_path}")
        
        # Return results in format expected by calling code
        return {
            'success': True,
            'summary': {
                'total_groups': len(optimized_groups),
                'total_piles': total_piles
            },
            'optimization': {
                'optimized_layout': f"AI-optimized with {', '.join(pile_type_usage.keys())} pile types",
                'pile_type_usage': pile_type_usage
            },
            'groups': optimized_groups,
            'totals': total_results,
            'group_results': {name: data['group_result'] for name, data in optimized_groups.items()},
            'total_results': total_results,
            'warnings': warnings_list,
            'errors': errors_list,
            'dxf_file': dxf_file_path,
            'dxf_file_path': dxf_file_path,
            'output_dir': output_dir or save_path,
            'config': {
                'edge_dist': edge_dist,
                'optimization_method': optimization_method,
                'selected_pile_types': selected_pile_types
            }
        }
        
    except Exception as e:
        error_msg = f"AI pile type pre-selection with NSGA-III optimization workflow failed: {str(e)}"
        errors_list.append(error_msg)
        if log_callback:
            log_callback(f"{error_msg}")
        
        return {
            'success': False,
            'summary': {'total_groups': 0, 'total_piles': 0},
            'optimization': {'optimized_layout': 'Failed', 'pile_type_usage': {}},
            'optimization_results': {},
            'warnings': warnings_list,
            'errors': errors_list,
            'error': str(e)
        }


# NOTE: ZERO FALLBACK POLICY ENFORCED - All legacy fallbacks permanently removed
# Use coordinate_pile_estimation_workflow and coordinate_pile_estimation_with_multi_type_optimization directly




