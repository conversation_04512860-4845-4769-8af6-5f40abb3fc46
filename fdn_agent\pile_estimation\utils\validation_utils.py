﻿"""
Validation utility functions for pile estimation
"""

from typing import List, Dict, Any, <PERSON>, Optional, Tuple, Callable
import pandas as pd
import numpy as np
import time
from shapely.geometry import <PERSON>ygon, Point, LineString

from ..data_types import ColumnData, WallData, GroupElements, PileLocation, Point2D, LocalCoordinateSystem
from ..exceptions import ValidationError, InputDataError, GeometryError
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def validate_coordinate_dataframes(df_point: pd.DataFrame, df_column: pd.DataFrame,
                                  df_wall: pd.DataFrame, log_callback: Optional[Callable] = None) -> None:
    """
    Validate input dataframes for coordinate processing.

    Args:
        df_point: DataFrame with point coordinates
        df_column: DataFrame with column data
        df_wall: DataFrame with wall data
        log_callback: Optional callback for logging validation progress

    Raises:
        InputDataError: If required columns are missing or data is invalid
    """
    log_function_entry(log_callback, "validate_coordinate_dataframes",
                      point_rows=len(df_point), column_rows=len(df_column), wall_rows=len(df_wall))

    with create_timed_logger(log_callback, "coordinate_dataframes_validation") as timer:
        try:
            # Check required columns in point data
            enhanced_log(log_callback, "Validating point DataFrame structure", 'DEBUG')
            point_required = ['Point', 'X (m)', 'Y (m)']
            _check_required_columns(df_point, point_required, 'Point', log_callback)
            log_validation_result(log_callback, "Point DataFrame Structure", True,
                                f"All required columns present: {point_required}")

            # Check required columns in column data
            enhanced_log(log_callback, "Validating column DataFrame structure", 'DEBUG')
            column_required = ['Column', 'Center Point', 'Base Level', 'Points']
            _check_required_columns(df_column, column_required, 'Column', log_callback)
            log_validation_result(log_callback, "Column DataFrame Structure", True,
                                f"All required columns present: {column_required}")

            # Check required columns in wall data
            enhanced_log(log_callback, "Validating wall DataFrame structure", 'DEBUG')
            wall_required = ['Wall', 'Center Point', 'Points', 'Base Level', 'Wall Group']
            _check_required_columns(df_wall, wall_required, 'Wall', log_callback)
            log_validation_result(log_callback, "Wall DataFrame Structure", True,
                                f"All required columns present: {wall_required}")

            # Check for duplicate points
            enhanced_log(log_callback, "Checking for duplicate point names", 'DEBUG')
            if df_point.duplicated('Point').any():
                duplicates = df_point[df_point.duplicated('Point', keep=False)]['Point'].unique()
                error_msg = f"Duplicate point names found: {duplicates}"
                log_validation_result(log_callback, "Point Name Uniqueness", False, error_msg)
                log_error_with_context(log_callback, InputDataError(error_msg), "validate_coordinate_dataframes")
                raise InputDataError(error_msg)
            else:
                log_validation_result(log_callback, "Point Name Uniqueness", True,
                                    f"All {len(df_point)} point names are unique")

            enhanced_log(log_callback, "All coordinate DataFrame validations passed", 'INFO')

        except Exception as e:
            log_error_with_context(log_callback, e, "validate_coordinate_dataframes")
            raise

    log_function_exit(log_callback, "validate_coordinate_dataframes", "Validation completed successfully")


def _check_required_columns(df: pd.DataFrame, required: List[str], df_name: str,
                           log_callback: Optional[Callable] = None) -> None:
    """
    Check if required columns exist in a DataFrame.

    Args:
        df: DataFrame to check
        required: List of required column names
        df_name: Name of the DataFrame for error messages
        log_callback: Optional callback for logging validation progress

    Raises:
        InputDataError: If any required columns are missing
    """
    log_function_entry(log_callback, "_check_required_columns",
                      df_name=df_name, required_count=len(required), df_columns=len(df.columns))

    enhanced_log(log_callback, f"Checking {len(required)} required columns in {df_name} DataFrame", 'DEBUG')
    enhanced_log(log_callback, f"Required columns: {required}", 'DEBUG')
    enhanced_log(log_callback, f"Available columns: {list(df.columns)}", 'DEBUG')

    missing = [col for col in required if col not in df.columns]

    if missing:
        error_msg = f"Missing required columns in {df_name} data: {missing}"
        log_validation_result(log_callback, f"{df_name} Required Columns", False, error_msg)
        log_error_with_context(log_callback, InputDataError(error_msg), "_check_required_columns")
        log_function_exit(log_callback, "_check_required_columns", "Failed - missing columns")
        raise InputDataError(error_msg)
    else:
        log_validation_result(log_callback, f"{df_name} Required Columns", True,
                            f"All {len(required)} required columns found")
        log_function_exit(log_callback, "_check_required_columns", "Success - all columns present")


def validate_excel_inputs(excel_inputs: Any, log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate Excel inputs structure and required data.

    Args:
        excel_inputs: ExcelInputs object to validate
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_excel_inputs",
                      excel_inputs_type=type(excel_inputs).__name__)

    warnings = []

    with create_timed_logger(log_callback, "excel_inputs_validation"):
        # Check for required attributes
        enhanced_log(log_callback, "Validating Excel inputs structure and required attributes", 'INFO')
        required_attrs = ['Point', 'Column', 'Wall', 'SiteBoundary', 'InputLoadColumn', 'InputLoadWall']

        for attr in required_attrs:
            enhanced_log(log_callback, f"Checking attribute: {attr}", 'DEBUG')

            if not hasattr(excel_inputs, attr):
                warning_msg = f"Missing required attribute: {attr}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Attribute {attr}", False, "Attribute not found")
                enhanced_log(log_callback, warning_msg, 'WARNING')
                continue

            df = getattr(excel_inputs, attr)
            if df is None or df.empty:
                warning_msg = f"Empty or None DataFrame: {attr}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"DataFrame {attr}", False, "DataFrame is empty or None")
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"DataFrame {attr}", True,
                                    f"DataFrame present with {len(df)} rows")
                enhanced_log(log_callback, f"DataFrame {attr}: {len(df)} rows, {len(df.columns)} columns", 'DEBUG')

        # Validate Point DataFrame
        if hasattr(excel_inputs, 'Point') and not excel_inputs.Point.empty:
            enhanced_log(log_callback, "Validating Point DataFrame details", 'DEBUG')
            point_warnings = _validate_point_dataframe(excel_inputs.Point, log_callback)
            warnings.extend(point_warnings)
            log_performance_metric(log_callback, "Point_validation_warnings", len(point_warnings), "warnings")

        # Validate Column DataFrame
        if hasattr(excel_inputs, 'Column') and not excel_inputs.Column.empty:
            enhanced_log(log_callback, "Validating Column DataFrame details", 'DEBUG')
            column_warnings = _validate_column_dataframe(excel_inputs.Column, log_callback)
            warnings.extend(column_warnings)
            log_performance_metric(log_callback, "Column_validation_warnings", len(column_warnings), "warnings")

        # Validate Wall DataFrame
        if hasattr(excel_inputs, 'Wall') and not excel_inputs.Wall.empty:
            enhanced_log(log_callback, "Validating Wall DataFrame details", 'DEBUG')
            wall_warnings = _validate_wall_dataframe(excel_inputs.Wall, log_callback)
            warnings.extend(wall_warnings)
            log_performance_metric(log_callback, "Wall_validation_warnings", len(wall_warnings), "warnings")

        # Log overall validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, "Total_validation_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, "Excel inputs validation completed successfully with no warnings", 'INFO')
        else:
            enhanced_log(log_callback, f"Excel inputs validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_excel_inputs", f"{len(warnings)} warnings")
    return warnings


def validate_pile_configuration(pile_capacity: float, pile_diameter: float,
                               min_spacing: float, edge_dist: float,
                               log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate pile configuration parameters.

    Args:
        pile_capacity: Pile capacity in kN
        pile_diameter: Pile diameter in meters
        min_spacing: Minimum pile spacing in meters
        edge_dist: Edge distance in meters
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_pile_configuration",
                      pile_capacity=pile_capacity, pile_diameter=pile_diameter,
                      min_spacing=min_spacing, edge_dist=edge_dist)

    warnings = []

    with create_timed_logger(log_callback, "pile_configuration_validation"):
        enhanced_log(log_callback, "Validating pile configuration parameters", 'INFO')

        # Validate pile capacity
        enhanced_log(log_callback, f"Checking pile capacity: {pile_capacity} kN", 'DEBUG')
        if pile_capacity <= 0:
            warning_msg = "Pile capacity must be positive"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Pile Capacity Positive", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Pile Capacity Positive", True, f"Capacity: {pile_capacity} kN")

        # Validate pile diameter
        enhanced_log(log_callback, f"Checking pile diameter: {pile_diameter} m", 'DEBUG')
        if pile_diameter <= 0:
            warning_msg = "Pile diameter must be positive"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Pile Diameter Positive", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Pile Diameter Positive", True, f"Diameter: {pile_diameter} m")

        # Validate minimum spacing
        enhanced_log(log_callback, f"Checking minimum spacing: {min_spacing} m", 'DEBUG')
        if min_spacing <= 0:
            warning_msg = "Minimum spacing must be positive"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Min Spacing Positive", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Min Spacing Positive", True, f"Spacing: {min_spacing} m")

        # Validate edge distance
        enhanced_log(log_callback, f"Checking edge distance: {edge_dist} m", 'DEBUG')
        if edge_dist <= 0:
            warning_msg = "Edge distance must be positive"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Edge Distance Positive", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Edge Distance Positive", True, f"Edge distance: {edge_dist} m")

        # Check if spacing meets 3D rule
        if pile_diameter > 0:  # Only check if diameter is valid
            three_d_spacing = 3 * pile_diameter
            enhanced_log(log_callback, f"Checking 3D spacing rule: {min_spacing} vs {three_d_spacing}", 'DEBUG')
            log_constraint_check(log_callback, "3D Spacing Rule", min_spacing, three_d_spacing,
                               min_spacing >= three_d_spacing)

            if min_spacing < three_d_spacing:
                warning_msg = f"Minimum spacing ({min_spacing}m) is less than 3D ({three_d_spacing}m)"
                warnings.append(warning_msg)
                log_validation_result(log_callback, "3D Spacing Rule", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "3D Spacing Rule", True,
                                    f"Spacing {min_spacing}m >= 3D {three_d_spacing}m")

        # Check reasonable values
        enhanced_log(log_callback, "Checking for reasonable parameter values", 'DEBUG')

        # Check pile capacity range
        log_constraint_check(log_callback, "Reasonable Pile Capacity", pile_capacity, 10000, pile_capacity <= 10000)
        if pile_capacity > 10000:
            warning_msg = f"Pile capacity ({pile_capacity} kN) seems unusually high"
            warnings.append(warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')

        # Check pile diameter range
        log_constraint_check(log_callback, "Reasonable Pile Diameter", pile_diameter, 2.0, pile_diameter <= 2.0)
        if pile_diameter > 2.0:
            warning_msg = f"Pile diameter ({pile_diameter}m) seems unusually large"
            warnings.append(warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')

        # Log validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, "Configuration_validation_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, "Pile configuration validation passed with no warnings", 'INFO')
        else:
            enhanced_log(log_callback, f"Pile configuration validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_pile_configuration", f"{len(warnings)} warnings")
    return warnings


def validate_structural_elements(columns: List[ColumnData], walls: List[WallData],
                               log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate structural elements data.

    Args:
        columns: List of column data
        walls: List of wall data
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_structural_elements",
                      num_columns=len(columns), num_walls=len(walls))

    warnings = []

    with create_timed_logger(log_callback, "structural_elements_validation"):
        # Check for empty inputs
        enhanced_log(log_callback, f"Validating {len(columns)} columns and {len(walls)} walls", 'INFO')

        if not columns and not walls:
            warning_msg = "No structural elements provided"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Structural Elements Present", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_function_exit(log_callback, "validate_structural_elements", f"{len(warnings)} warnings")
            return warnings
        else:
            log_validation_result(log_callback, "Structural Elements Present", True,
                                f"{len(columns)} columns, {len(walls)} walls")

        # Validate columns
        enhanced_log(log_callback, f"Validating {len(columns)} columns", 'DEBUG')
        column_names = set()
        valid_columns = 0

        for i, col in enumerate(columns):
            enhanced_log(log_callback, f"Validating column {i}: {col[:1] if col else 'empty'}", 'DEBUG')

            if len(col) < 4:
                warning_msg = f"Column {i} has insufficient data"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Column {i} Data Sufficiency", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                continue

            name, x, y, z = col[:4]

            # Check for duplicate names
            if name in column_names:
                warning_msg = f"Duplicate column name: {name}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Column {name} Name Uniqueness", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"Column {name} Name Uniqueness", True, "Name is unique")
            column_names.add(name)

            # Check coordinate validity
            coords_valid = all(isinstance(coord, (int, float)) and np.isfinite(coord) for coord in [x, y, z])
            if not coords_valid:
                warning_msg = f"Invalid coordinates for column {name}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Column {name} Coordinates", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"Column {name} Coordinates", True,
                                    f"Valid coordinates: ({x:.3f}, {y:.3f}, {z:.3f})")
                valid_columns += 1

        log_performance_metric(log_callback, "Valid_columns", valid_columns, "columns")
        enhanced_log(log_callback, f"Column validation completed: {valid_columns}/{len(columns)} valid", 'DEBUG')

        # Validate walls
        enhanced_log(log_callback, f"Validating {len(walls)} walls", 'DEBUG')
        wall_names = set()
        valid_walls = 0

        for i, wall in enumerate(walls):
            enhanced_log(log_callback, f"Validating wall {i}: {wall[:1] if wall else 'empty'}", 'DEBUG')

            if len(wall) < 3:
                warning_msg = f"Wall {i} has insufficient data"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Wall {i} Data Sufficiency", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                continue

            name, points, z = wall[:3]

            # Check for duplicate names
            if name in wall_names:
                warning_msg = f"Duplicate wall name: {name}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Wall {name} Name Uniqueness", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"Wall {name} Name Uniqueness", True, "Name is unique")
            wall_names.add(name)

            # Check points validity
            if not points or len(points) < 2:
                warning_msg = f"Wall {name} must have at least 2 points"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Wall {name} Point Count", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                continue
            else:
                log_validation_result(log_callback, f"Wall {name} Point Count", True,
                                    f"{len(points)} points provided")

            # Validate individual points
            valid_points = 0
            for j, point in enumerate(points):
                point_valid = (len(point) >= 2 and
                             all(isinstance(coord, (int, float)) and np.isfinite(coord) for coord in point[:2]))
                if not point_valid:
                    warning_msg = f"Invalid point {j} for wall {name}"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, warning_msg, 'WARNING')
                else:
                    valid_points += 1

            log_performance_metric(log_callback, f"Wall_{name}_valid_points", valid_points, "points")

            # Check z coordinate
            if not isinstance(z, (int, float)) or not np.isfinite(z):
                warning_msg = f"Invalid base level for wall {name}"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"Wall {name} Base Level", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"Wall {name} Base Level", True, f"Base level: {z:.3f}")
                if valid_points == len(points):  # All points valid
                    valid_walls += 1

        log_performance_metric(log_callback, "Valid_walls", valid_walls, "walls")
        enhanced_log(log_callback, f"Wall validation completed: {valid_walls}/{len(walls)} valid", 'DEBUG')

        # Log overall validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, "Structural_elements_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, "All structural elements validation passed", 'INFO')
        else:
            enhanced_log(log_callback, f"Structural elements validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_structural_elements", f"{len(warnings)} warnings")
    return warnings


def validate_pile_locations(pile_locations: List[PileLocation],
                           cap_polygon: Optional[Polygon] = None,
                           min_spacing: float = 1.8,
                           log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate pile locations for spacing and containment.

    Args:
        pile_locations: List of pile locations
        cap_polygon: Pile cap polygon for containment check
        min_spacing: Minimum required spacing between piles
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_pile_locations",
                      num_piles=len(pile_locations), min_spacing=min_spacing,
                      has_cap_polygon=cap_polygon is not None)

    warnings = []

    with create_timed_logger(log_callback, "pile_locations_validation"):
        enhanced_log(log_callback, f"Validating {len(pile_locations)} pile locations", 'INFO')

        if not pile_locations:
            warning_msg = "No pile locations provided"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Pile Locations Present", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_function_exit(log_callback, "validate_pile_locations", f"{len(warnings)} warnings")
            return warnings
        else:
            log_validation_result(log_callback, "Pile Locations Present", True,
                                f"{len(pile_locations)} pile locations provided")

        # Check spacing between piles
        enhanced_log(log_callback, f"Checking spacing between {len(pile_locations)} piles (min: {min_spacing}m)", 'DEBUG')
        spacing_violations = 0
        total_pairs = 0

        for i in range(len(pile_locations)):
            for j in range(i + 1, len(pile_locations)):
                total_pairs += 1
                p1 = pile_locations[i]
                p2 = pile_locations[j]

                try:
                    distance = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                    log_calculation_result(log_callback, f"Distance_piles_{i}_{j}", distance, "m")

                    spacing_ok = distance >= min_spacing
                    log_constraint_check(log_callback, f"Pile Spacing {i}-{j}", distance, min_spacing, spacing_ok)

                    if not spacing_ok:
                        warning_msg = f"Piles {i} and {j} are too close: {distance:.2f}m < {min_spacing}m"
                        warnings.append(warning_msg)
                        spacing_violations += 1
                        enhanced_log(log_callback, warning_msg, 'WARNING')

                except (IndexError, TypeError, ValueError) as e:
                    warning_msg = f"Error calculating distance between piles {i} and {j}: {e}"
                    warnings.append(warning_msg)
                    log_error_with_context(log_callback, e, f"pile spacing calculation {i}-{j}")
                    enhanced_log(log_callback, warning_msg, 'WARNING')

        log_performance_metric(log_callback, "Pile_pairs_checked", total_pairs, "pairs")
        log_performance_metric(log_callback, "Spacing_violations", spacing_violations, "violations")

        if spacing_violations == 0:
            log_validation_result(log_callback, "Pile Spacing", True,
                                f"All {total_pairs} pile pairs meet minimum spacing requirement")
        else:
            log_validation_result(log_callback, "Pile Spacing", False,
                                f"{spacing_violations}/{total_pairs} pairs violate spacing requirement")

        # Check containment within pile cap
        if cap_polygon and cap_polygon.is_valid:
            enhanced_log(log_callback, "Checking pile containment within pile cap polygon", 'DEBUG')
            containment_violations = 0

            for i, pile_loc in enumerate(pile_locations):
                try:
                    point = Point(pile_loc)
                    contained = cap_polygon.contains(point) or cap_polygon.touches(point)

                    if not contained:
                        warning_msg = f"Pile {i} is outside pile cap boundary"
                        warnings.append(warning_msg)
                        containment_violations += 1
                        log_validation_result(log_callback, f"Pile {i} Containment", False, warning_msg)
                        enhanced_log(log_callback, warning_msg, 'WARNING')
                    else:
                        log_validation_result(log_callback, f"Pile {i} Containment", True,
                                            f"Pile at ({pile_loc[0]:.3f}, {pile_loc[1]:.3f}) is within cap")

                except Exception as e:
                    warning_msg = f"Error checking containment for pile {i}: {e}"
                    warnings.append(warning_msg)
                    log_error_with_context(log_callback, e, f"pile containment check {i}")
                    enhanced_log(log_callback, warning_msg, 'WARNING')

            log_performance_metric(log_callback, "Containment_violations", containment_violations, "violations")

            if containment_violations == 0:
                log_validation_result(log_callback, "Pile Cap Containment", True,
                                    f"All {len(pile_locations)} piles are within pile cap")
            else:
                log_validation_result(log_callback, "Pile Cap Containment", False,
                                    f"{containment_violations}/{len(pile_locations)} piles outside cap")
        else:
            enhanced_log(log_callback, "No valid pile cap polygon provided - skipping containment check", 'DEBUG')

        # Check for coordinate validity
        enhanced_log(log_callback, "Validating pile coordinate validity", 'DEBUG')
        invalid_coordinates = 0

        for i, pile_loc in enumerate(pile_locations):
            if len(pile_loc) < 2:
                warning_msg = f"Pile {i} has insufficient coordinates"
                warnings.append(warning_msg)
                invalid_coordinates += 1
                log_validation_result(log_callback, f"Pile {i} Coordinate Count", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                continue

            x, y = pile_loc[:2]
            coords_valid = (isinstance(x, (int, float)) and isinstance(y, (int, float)) and
                           np.isfinite(x) and np.isfinite(y))

            if not coords_valid:
                warning_msg = f"Pile {i} has invalid coordinates"
                warnings.append(warning_msg)
                invalid_coordinates += 1
                log_validation_result(log_callback, f"Pile {i} Coordinates", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"Pile {i} Coordinates", True,
                                    f"Valid coordinates: ({x:.3f}, {y:.3f})")

        log_performance_metric(log_callback, "Invalid_coordinates", invalid_coordinates, "piles")

        # Log overall validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, "Pile_locations_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, "All pile location validations passed", 'INFO')
        else:
            enhanced_log(log_callback, f"Pile location validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_pile_locations", f"{len(warnings)} warnings")
    return warnings


def validate_group_elements(group_elements: GroupElements,
                           log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate group elements structure and data.

    Args:
        group_elements: Group elements to validate
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_group_elements",
                      group_elements_type=type(group_elements).__name__)

    warnings = []

    with create_timed_logger(log_callback, "group_elements_validation"):
        enhanced_log(log_callback, "Validating group elements structure and data", 'INFO')

        # Check if group_elements is a dictionary
        if not isinstance(group_elements, dict):
            warning_msg = "Group elements must be a dictionary"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Group Elements Type", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_function_exit(log_callback, "validate_group_elements", f"{len(warnings)} warnings")
            return warnings
        else:
            log_validation_result(log_callback, "Group Elements Type", True, "Group elements is a dictionary")

        # Check required keys
        enhanced_log(log_callback, "Checking for required keys in group elements", 'DEBUG')

        if 'columns' not in group_elements:
            warning_msg = "Missing 'columns' key in group elements"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Columns Key Present", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Columns Key Present", True, "Columns key found")

        if 'walls' not in group_elements:
            warning_msg = "Missing 'walls' key in group elements"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Walls Key Present", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Walls Key Present", True, "Walls key found")

        # Validate columns
        columns = group_elements.get('columns', [])
        enhanced_log(log_callback, f"Validating columns data: {len(columns)} columns", 'DEBUG')

        if not isinstance(columns, list):
            warning_msg = "Columns must be a list"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Columns Type", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Columns Type", True, f"Columns is a list with {len(columns)} items")

            if columns:  # Only validate if there are columns
                enhanced_log(log_callback, "Validating individual column elements", 'DEBUG')
                column_warnings = validate_structural_elements(columns, [], log_callback)
                column_specific_warnings = [w for w in column_warnings if 'column' in w.lower()]
                warnings.extend(column_specific_warnings)
                log_performance_metric(log_callback, "Column_specific_warnings", len(column_specific_warnings), "warnings")

        # Validate walls
        walls = group_elements.get('walls', [])
        enhanced_log(log_callback, f"Validating walls data: {len(walls)} walls", 'DEBUG')

        if not isinstance(walls, list):
            warning_msg = "Walls must be a list"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Walls Type", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Walls Type", True, f"Walls is a list with {len(walls)} items")

            if walls:  # Only validate if there are walls
                enhanced_log(log_callback, "Validating individual wall elements", 'DEBUG')
                wall_warnings = validate_structural_elements([], walls, log_callback)
                wall_specific_warnings = [w for w in wall_warnings if 'wall' in w.lower()]
                warnings.extend(wall_specific_warnings)
                log_performance_metric(log_callback, "Wall_specific_warnings", len(wall_specific_warnings), "warnings")

        # Check if group has any elements
        total_elements = len(columns) + len(walls)
        log_performance_metric(log_callback, "Total_group_elements", total_elements, "elements")

        if not columns and not walls:
            warning_msg = "Group has no structural elements"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Group Has Elements", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Group Has Elements", True,
                                f"Group contains {len(columns)} columns and {len(walls)} walls")

        # Log overall validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, "Group_elements_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, "Group elements validation passed with no warnings", 'INFO')
        else:
            enhanced_log(log_callback, f"Group elements validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_group_elements", f"{len(warnings)} warnings")
    return warnings


def validate_polygon(polygon: Polygon, name: str = "polygon",
                    log_callback: Optional[Callable] = None) -> List[str]:
    """
    Validate a Shapely polygon.

    Args:
        polygon: Polygon to validate
        name: Name for error messages
        log_callback: Optional callback for logging validation progress

    Returns:
        List of validation warning messages
    """
    log_function_entry(log_callback, "validate_polygon",
                      polygon_type=type(polygon).__name__, name=name)

    warnings = []

    with create_timed_logger(log_callback, f"polygon_validation_{name}"):
        enhanced_log(log_callback, f"Validating polygon: {name}", 'INFO')

        # Check if polygon is None
        if polygon is None:
            warning_msg = f"{name} is None"
            warnings.append(warning_msg)
            log_validation_result(log_callback, f"{name} Not None", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_function_exit(log_callback, "validate_polygon", f"{len(warnings)} warnings")
            return warnings
        else:
            log_validation_result(log_callback, f"{name} Not None", True, "Polygon object provided")

        # Check if polygon is empty
        enhanced_log(log_callback, f"Checking if {name} is empty", 'DEBUG')
        if polygon.is_empty:
            warning_msg = f"{name} is empty"
            warnings.append(warning_msg)
            log_validation_result(log_callback, f"{name} Not Empty", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, f"{name} Not Empty", True, "Polygon contains geometry")

        # Check if polygon is valid
        enhanced_log(log_callback, f"Checking if {name} is geometrically valid", 'DEBUG')
        if not polygon.is_valid:
            warning_msg = f"{name} is not valid"
            warnings.append(warning_msg)
            log_validation_result(log_callback, f"{name} Geometric Validity", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, f"{name} Geometric Validity", True, "Polygon is geometrically valid")

        # Check polygon area
        if not polygon.is_empty:
            area = polygon.area
            log_calculation_result(log_callback, f"{name}_area", area, "m²")

            enhanced_log(log_callback, f"Checking {name} area: {area:.3f} m²", 'DEBUG')

            if area <= 0:
                warning_msg = f"{name} has zero or negative area"
                warnings.append(warning_msg)
                log_validation_result(log_callback, f"{name} Positive Area", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, f"{name} Positive Area", True, f"Area: {area:.3f} m²")

            # Check for reasonable size (assuming meters)
            max_reasonable_area = 100000  # 100,000 sq meters
            min_reasonable_area = 1       # 1 sq meter

            log_constraint_check(log_callback, f"{name} Maximum Area", area, max_reasonable_area,
                               area <= max_reasonable_area)
            if area > max_reasonable_area:
                warning_msg = f"{name} area ({area:.0f} m²) seems unusually large"
                warnings.append(warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')

            log_constraint_check(log_callback, f"{name} Minimum Area", area, min_reasonable_area,
                               area >= min_reasonable_area)
            if area < min_reasonable_area:
                warning_msg = f"{name} area ({area:.2f} m²) seems unusually small"
                warnings.append(warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')

            # Additional geometric properties
            if hasattr(polygon, 'exterior') and polygon.exterior:
                perimeter = polygon.exterior.length
                log_calculation_result(log_callback, f"{name}_perimeter", perimeter, "m")

                # Calculate shape compactness (area to perimeter ratio)
                if perimeter > 0:
                    compactness = area / (perimeter ** 2)
                    log_calculation_result(log_callback, f"{name}_compactness", compactness, "ratio")
                    enhanced_log(log_callback, f"{name} compactness ratio: {compactness:.6f}", 'DEBUG')

        # Log overall validation summary
        total_warnings = len(warnings)
        log_performance_metric(log_callback, f"{name}_validation_warnings", total_warnings, "warnings")

        if total_warnings == 0:
            enhanced_log(log_callback, f"Polygon {name} validation passed with no warnings", 'INFO')
        else:
            enhanced_log(log_callback, f"Polygon {name} validation completed with {total_warnings} warnings", 'WARNING')

    log_function_exit(log_callback, "validate_polygon", f"{len(warnings)} warnings")
    return warnings


def validate_pile_cap_polygon(cap_poly: Polygon, all_points: List[Point2D], edge_dist: float,
                             use_structural_areas: bool = False, group_elements: GroupElements = None,
                             excel_inputs: Optional[Any] = None, errors_list: List[str] = None,
                             log_callback: Optional[Callable] = None) -> bool:
    """
    Validate that the pile cap meets all requirements.

    Args:
        cap_poly: Pile cap polygon to validate
        all_points: List of points that should be contained
        edge_dist: Minimum edge distance requirement
        use_structural_areas: Whether to validate structural element areas
        group_elements: Dictionary containing structural elements
        excel_inputs: Excel input data
        errors_list: List to append error messages
        log_callback: Optional callback for logging validation progress

    Returns:
        True if validation passes, False otherwise
    """
    log_function_entry(log_callback, "validate_pile_cap_polygon",
                      num_points=len(all_points), edge_dist=edge_dist,
                      use_structural_areas=use_structural_areas,
                      has_group_elements=group_elements is not None,
                      has_excel_inputs=excel_inputs is not None)

    if errors_list is None:
        errors_list = []

    with create_timed_logger(log_callback, "pile_cap_polygon_validation"):
        enhanced_log(log_callback, "Validating pile cap polygon requirements", 'INFO')

        # Basic polygon validation
        enhanced_log(log_callback, "Checking basic pile cap polygon validity", 'DEBUG')

        if not cap_poly or cap_poly.is_empty:
            error_msg = "Pile cap polygon is empty"
            errors_list.append(error_msg)
            log_validation_result(log_callback, "Pile Cap Polygon Present", False, error_msg)
            log_error_with_context(log_callback, ValueError(error_msg), "validate_pile_cap_polygon")
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "validate_pile_cap_polygon", False)
            return False
        else:
            log_validation_result(log_callback, "Pile Cap Polygon Present", True,
                                f"Polygon with area {cap_poly.area:.3f} m²")

        # Check polygon validity
        if not cap_poly.is_valid:
            enhanced_log(log_callback, "Pile cap polygon is invalid, attempting repair", 'WARNING')
            cap_poly = cap_poly.buffer(0)

            if not cap_poly.is_valid:
                error_msg = "Pile cap polygon is invalid and cannot be repaired"
                errors_list.append(error_msg)
                log_validation_result(log_callback, "Pile Cap Polygon Validity", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), "validate_pile_cap_polygon")
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_function_exit(log_callback, "validate_pile_cap_polygon", False)
                return False
            else:
                log_validation_result(log_callback, "Pile Cap Polygon Validity", True,
                                    "Polygon repaired successfully")
                enhanced_log(log_callback, "Pile cap polygon repaired successfully", 'INFO')
        else:
            log_validation_result(log_callback, "Pile Cap Polygon Validity", True, "Polygon is geometrically valid")

        # Choose validation method
        validation_result = False

        if use_structural_areas and group_elements is not None and excel_inputs is not None:
            enhanced_log(log_callback, "Using structural areas validation method", 'INFO')
            validation_result = _validate_structural_areas(cap_poly, group_elements, excel_inputs,
                                                         errors_list, log_callback)
        else:
            enhanced_log(log_callback, "Using point containment validation method", 'INFO')
            validation_result = _validate_point_containment(cap_poly, all_points, edge_dist,
                                                          errors_list, log_callback)

        # Log final validation result
        if validation_result:
            enhanced_log(log_callback, "Pile cap polygon validation passed", 'INFO')
            log_validation_result(log_callback, "Overall Pile Cap Validation", True,
                                "All validation requirements met")
        else:
            enhanced_log(log_callback, f"Pile cap polygon validation failed with {len(errors_list)} errors", 'ERROR')
            log_validation_result(log_callback, "Overall Pile Cap Validation", False,
                                f"{len(errors_list)} validation errors")

        log_performance_metric(log_callback, "Pile_cap_validation_errors", len(errors_list), "errors")

    log_function_exit(log_callback, "validate_pile_cap_polygon", validation_result)
    return validation_result


def clip_to_site_boundary(cap_poly: Polygon, site_poly: Polygon, warnings_list: List[str],
                         log_callback: Optional[Callable] = None) -> Polygon:
    """
    Clip the pile cap polygon to the site boundary.

    Args:
        cap_poly: Original pile cap polygon
        site_poly: Site boundary polygon
        warnings_list: List to append warning messages
        log_callback: Optional callback for logging validation progress

    Returns:
        Clipped pile cap polygon
    """
    log_function_entry(log_callback, "clip_to_site_boundary",
                      cap_poly_area=cap_poly.area if cap_poly else 0,
                      site_poly_area=site_poly.area if site_poly else 0)

    with create_timed_logger(log_callback, "site_boundary_clipping"):
        enhanced_log(log_callback, "Clipping pile cap polygon to site boundary", 'INFO')

        # Check if site polygon is valid
        if not site_poly or site_poly.is_empty:
            enhanced_log(log_callback, "No site boundary polygon provided, returning original cap", 'DEBUG')
            log_validation_result(log_callback, "Site Boundary Present", False, "No site boundary to clip to")
            log_function_exit(log_callback, "clip_to_site_boundary", "Original polygon (no clipping)")
            return cap_poly
        else:
            log_validation_result(log_callback, "Site Boundary Present", True,
                                f"Site boundary area: {site_poly.area:.3f} m²")

        if not site_poly.is_valid:
            warning_msg = "Site boundary polygon is not valid, skipping clip"
            warnings_list.append(warning_msg)
            log_validation_result(log_callback, "Site Boundary Validity", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_function_exit(log_callback, "clip_to_site_boundary", "Original polygon (invalid site boundary)")
            return cap_poly
        else:
            log_validation_result(log_callback, "Site Boundary Validity", True, "Site boundary is geometrically valid")

        # Ensure cap_poly is valid before clipping
        original_cap_area = cap_poly.area
        log_calculation_result(log_callback, "Original_cap_area", original_cap_area, "m²")

        if not cap_poly.is_valid:
            enhanced_log(log_callback, "Pile cap polygon is invalid, attempting repair before clipping", 'WARNING')
            cap_poly = cap_poly.buffer(0)

            if cap_poly.is_valid:
                enhanced_log(log_callback, "Pile cap polygon repaired successfully", 'INFO')
            else:
                warning_msg = "Could not repair pile cap polygon for clipping"
                warnings_list.append(warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')

        # Perform the intersection
        enhanced_log(log_callback, "Performing geometric intersection", 'DEBUG')

        try:
            clipped = cap_poly.intersection(site_poly)
            log_calculation_result(log_callback, "Intersection_operation", "completed", "")

        except Exception as e:
            error_msg = f"Error during intersection operation: {e}"
            warnings_list.append(error_msg)
            log_error_with_context(log_callback, e, "clip_to_site_boundary intersection")
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "clip_to_site_boundary", "Original polygon (intersection failed)")
            return cap_poly

        # Handle potential geometry collections or multi-polygons
        enhanced_log(log_callback, f"Processing intersection result: {type(clipped).__name__}", 'DEBUG')

        if hasattr(clipped, 'geoms'):  # Handle MultiPolygon
            enhanced_log(log_callback, f"Intersection resulted in {len(clipped.geoms)} geometries", 'DEBUG')

            # Use the largest valid polygon from the intersection
            valid_polys = [p for p in clipped.geoms
                         if isinstance(p, Polygon) and p.area > 0]

            log_performance_metric(log_callback, "Valid_polygons_from_intersection", len(valid_polys), "polygons")

            if valid_polys:
                clipped = max(valid_polys, key=lambda p: p.area)
                enhanced_log(log_callback, f"Selected largest polygon with area {clipped.area:.3f} m²", 'DEBUG')
            else:
                warning_msg = "No valid polygons found after site boundary clipping"
                warnings_list.append(warning_msg)
                log_validation_result(log_callback, "Valid Clipped Polygons", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                log_function_exit(log_callback, "clip_to_site_boundary", "Original polygon (no valid intersection)")
                return cap_poly

        # Validate the clipped result
        if not isinstance(clipped, Polygon) or clipped.is_empty:
            error_msg = "Pile cap lies completely outside site boundary"
            log_validation_result(log_callback, "Pile Cap Within Site Boundary", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "clip_to_site_boundary")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise GeometryError(error_msg)
        else:
            log_validation_result(log_callback, "Pile Cap Within Site Boundary", True,
                                "Intersection produced valid polygon")

        # Check if the clipped area is significantly smaller than original
        clipped_area = clipped.area
        log_calculation_result(log_callback, "Clipped_cap_area", clipped_area, "m²")

        area_ratio = clipped_area / original_cap_area if original_cap_area > 0 else 0
        log_calculation_result(log_callback, "Area_retention_ratio", area_ratio, "ratio")

        area_reduction_threshold = 0.9  # 90% retention threshold
        log_constraint_check(log_callback, "Area Retention", area_ratio, area_reduction_threshold,
                           area_ratio >= area_reduction_threshold)

        if area_ratio < area_reduction_threshold:  # More than 10% clipped
            area_reduction_percent = 100 * (1 - area_ratio)
            warning_msg = (f"Pile cap area reduced by {area_reduction_percent:.1f}% "
                          "due to site boundary constraints")
            warnings_list.append(warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
            log_performance_metric(log_callback, "Area_reduction_percent", area_reduction_percent, "%")
        else:
            enhanced_log(log_callback, f"Area reduction minimal: {100 * (1 - area_ratio):.1f}%", 'DEBUG')

        enhanced_log(log_callback, f"Site boundary clipping completed successfully", 'INFO')
        enhanced_log(log_callback, f"Final clipped area: {clipped_area:.3f} m² (was {original_cap_area:.3f} m²)", 'INFO')

    log_function_exit(log_callback, "clip_to_site_boundary", f"Clipped polygon ({clipped_area:.3f} m²)")
    return clipped


def _validate_structural_areas(cap_poly: Polygon, group_elements: GroupElements,
                              excel_inputs: Any, errors_list: List[str],
                              log_callback: Optional[Callable] = None) -> bool:
    """
    Validate that all structural element areas are contained within the pile cap.
    """
    log_function_entry(log_callback, "_validate_structural_areas",
                      num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])))

    valid = True

    with create_timed_logger(log_callback, "structural_areas_validation"):
        enhanced_log(log_callback, "Validating structural element areas within pile cap", 'DEBUG')

        # Validate columns
        columns = group_elements.get('columns', [])
        enhanced_log(log_callback, f"Validating {len(columns)} column containments", 'DEBUG')

        valid_columns = 0
        for i, column in enumerate(columns):
            enhanced_log(log_callback, f"Checking column {i} containment", 'DEBUG')

            if _validate_column_containment(column, cap_poly, excel_inputs, errors_list, log_callback):
                valid_columns += 1
            else:
                valid = False

        log_performance_metric(log_callback, "Valid_column_containments", valid_columns, "columns")
        log_validation_result(log_callback, "Column Containments", valid_columns == len(columns),
                            f"{valid_columns}/{len(columns)} columns properly contained")

        # Validate walls
        walls = group_elements.get('walls', [])
        enhanced_log(log_callback, f"Validating {len(walls)} wall containments", 'DEBUG')

        valid_walls = 0
        for i, wall in enumerate(walls):
            enhanced_log(log_callback, f"Checking wall {i} containment", 'DEBUG')

            if _validate_wall_containment(wall, cap_poly, excel_inputs, errors_list, log_callback):
                valid_walls += 1
            else:
                valid = False

        log_performance_metric(log_callback, "Valid_wall_containments", valid_walls, "walls")
        log_validation_result(log_callback, "Wall Containments", valid_walls == len(walls),
                            f"{valid_walls}/{len(walls)} walls properly contained")

        # Overall validation result
        total_elements = len(columns) + len(walls)
        valid_elements = valid_columns + valid_walls

        log_performance_metric(log_callback, "Total_structural_elements_validated", total_elements, "elements")
        log_performance_metric(log_callback, "Valid_structural_containments", valid_elements, "elements")

        if valid:
            enhanced_log(log_callback, f"All {total_elements} structural elements are properly contained", 'INFO')
        else:
            failed_elements = total_elements - valid_elements
            enhanced_log(log_callback, f"{failed_elements}/{total_elements} structural elements failed containment", 'WARNING')

    log_function_exit(log_callback, "_validate_structural_areas", valid)
    return valid


def _validate_point_containment(cap_poly: Polygon, all_points: List[Point2D],
                               edge_dist: float, errors_list: List[str],
                               log_callback: Optional[Callable] = None) -> bool:
    """
    Validate that all points are within the pile cap with required edge distance.
    """
    log_function_entry(log_callback, "_validate_point_containment",
                      num_points=len(all_points), edge_dist=edge_dist)

    with create_timed_logger(log_callback, "point_containment_validation"):
        enhanced_log(log_callback, f"Validating {len(all_points)} points within pile cap with {edge_dist}m edge distance", 'DEBUG')

        # Check all points are within cap_poly buffered inward by edge_dist
        enhanced_log(log_callback, f"Creating inner polygon with {edge_dist}m buffer", 'DEBUG')

        try:
            inner_poly = cap_poly.buffer(-edge_dist)
            log_calculation_result(log_callback, "Inner_polygon_area", inner_poly.area if not inner_poly.is_empty else 0, "m²")

        except Exception as e:
            error_msg = f"Error creating inner polygon buffer: {e}"
            errors_list.append(error_msg)
            log_error_with_context(log_callback, e, "_validate_point_containment buffer operation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_point_containment", False)
            return False

        if inner_poly.is_empty:
            error_msg = "Pile cap too small after applying edge distance buffer"
            errors_list.append(error_msg)
            log_validation_result(log_callback, "Inner Polygon Valid", False, error_msg)
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_point_containment", False)
            return False
        else:
            log_validation_result(log_callback, "Inner Polygon Valid", True,
                                f"Inner polygon area: {inner_poly.area:.3f} m²")

        # Check each point for containment
        contained_points = 0
        failed_points = []

        for i, pt in enumerate(all_points):
            enhanced_log(log_callback, f"Checking point {i}: ({pt[0]:.3f}, {pt[1]:.3f})", 'DEBUG')

            try:
                point_geom = Point(pt)
                is_contained = inner_poly.contains(point_geom)

                if not is_contained:
                    error_msg = f"Point {pt} not contained within pile cap with required edge distance"
                    errors_list.append(error_msg)
                    failed_points.append(i)
                    log_validation_result(log_callback, f"Point {i} Containment", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                else:
                    contained_points += 1
                    log_validation_result(log_callback, f"Point {i} Containment", True,
                                        f"Point properly contained with edge distance")

            except Exception as e:
                error_msg = f"Error checking containment for point {i}: {e}"
                errors_list.append(error_msg)
                failed_points.append(i)
                log_error_with_context(log_callback, e, f"point containment check {i}")
                enhanced_log(log_callback, error_msg, 'ERROR')

        # Log containment summary
        log_performance_metric(log_callback, "Points_checked", len(all_points), "points")
        log_performance_metric(log_callback, "Points_contained", contained_points, "points")
        log_performance_metric(log_callback, "Points_failed_containment", len(failed_points), "points")

        validation_passed = len(failed_points) == 0

        if validation_passed:
            enhanced_log(log_callback, f"All {len(all_points)} points are properly contained within pile cap", 'INFO')
            log_validation_result(log_callback, "Overall Point Containment", True,
                                f"All points meet edge distance requirement")
        else:
            enhanced_log(log_callback, f"{len(failed_points)}/{len(all_points)} points failed containment check", 'ERROR')
            log_validation_result(log_callback, "Overall Point Containment", False,
                                f"{len(failed_points)} points outside required edge distance")

    log_function_exit(log_callback, "_validate_point_containment", validation_passed)
    return validation_passed


def _validate_column_containment(column, cap_poly: Polygon, excel_inputs: Any,
                                errors_list: List[str], log_callback: Optional[Callable] = None) -> bool:
    """Validate that a column is contained within the pile cap."""
    column_name = column[0] if column else "Unknown"

    log_function_entry(log_callback, "_validate_column_containment", column_name=column_name)

    enhanced_log(log_callback, f"Validating column {column_name} containment within pile cap", 'DEBUG')

    try:
        df_column = excel_inputs.Column
        df_point = excel_inputs.Point

        enhanced_log(log_callback, f"Looking up column {column_name} in Excel data", 'DEBUG')
        column_row = df_column[df_column['Column'] == column_name]

        if column_row.empty:
            error_msg = f"Column {column_name} not found in Excel data"
            errors_list.append(error_msg)
            log_validation_result(log_callback, f"Column {column_name} Data Found", False, error_msg)
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_column_containment", False)
            return False
        else:
            log_validation_result(log_callback, f"Column {column_name} Data Found", True, "Column found in Excel data")

        if 'Points' not in df_column.columns:
            error_msg = f"Points column missing from Column DataFrame for {column_name}"
            errors_list.append(error_msg)
            log_validation_result(log_callback, f"Column {column_name} Points Field", False, error_msg)
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_column_containment", False)
            return False

        points_field = column_row['Points'].iloc[0]
        enhanced_log(log_callback, f"Column {column_name} points field: {points_field}", 'DEBUG')

        if pd.notna(points_field) and points_field:
            point_names = [p.strip() for p in points_field.split(';') if p.strip()]
            log_performance_metric(log_callback, f"Column_{column_name}_points_count", len(point_names), "points")
            enhanced_log(log_callback, f"Column {column_name} has {len(point_names)} points: {point_names}", 'DEBUG')

            contained_points = 0

            for point_name in point_names:
                enhanced_log(log_callback, f"Checking point {point_name} for column {column_name}", 'DEBUG')

                point_row = df_point[df_point['Point'] == point_name]
                if point_row.empty:
                    error_msg = f"Point {point_name} for column {column_name} not found in Point data"
                    errors_list.append(error_msg)
                    log_validation_result(log_callback, f"Point {point_name} Data", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_function_exit(log_callback, "_validate_column_containment", False)
                    return False

                x = point_row['X (m)'].iloc[0]
                y = point_row['Y (m)'].iloc[0]

                enhanced_log(log_callback, f"Point {point_name} coordinates: ({x:.3f}, {y:.3f})", 'DEBUG')

                point_geom = Point(x, y)
                is_contained = cap_poly.contains(point_geom)

                if not is_contained:
                    error_msg = f"Column {column_name} point {point_name} not contained in pile cap"
                    errors_list.append(error_msg)
                    log_validation_result(log_callback, f"Point {point_name} Containment", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_function_exit(log_callback, "_validate_column_containment", False)
                    return False
                else:
                    contained_points += 1
                    log_validation_result(log_callback, f"Point {point_name} Containment", True,
                                        f"Point contained within pile cap")

            log_performance_metric(log_callback, f"Column_{column_name}_contained_points", contained_points, "points")
            enhanced_log(log_callback, f"Column {column_name}: all {contained_points} points contained", 'DEBUG')
        else:
            enhanced_log(log_callback, f"Column {column_name} has no points field or empty points", 'DEBUG')
            log_validation_result(log_callback, f"Column {column_name} Points", True, "No points to validate")

    except Exception as e:
        error_msg = f"Error validating column {column_name} containment: {e}"
        errors_list.append(error_msg)
        log_error_with_context(log_callback, e, f"_validate_column_containment for {column_name}")
        enhanced_log(log_callback, error_msg, 'ERROR')
        log_function_exit(log_callback, "_validate_column_containment", False)
        return False

    log_validation_result(log_callback, f"Column {column_name} Overall Containment", True,
                        "Column fully contained within pile cap")
    log_function_exit(log_callback, "_validate_column_containment", True)
    return True


def _validate_wall_containment(wall, cap_poly: Polygon, excel_inputs: Any,
                              errors_list: List[str], log_callback: Optional[Callable] = None) -> bool:
    """Validate that a wall is contained within the pile cap."""
    wall_name = wall[0] if wall else "Unknown"

    log_function_entry(log_callback, "_validate_wall_containment", wall_name=wall_name)

    enhanced_log(log_callback, f"Validating wall {wall_name} containment within pile cap", 'DEBUG')

    try:
        df_wall = excel_inputs.Wall
        df_point = excel_inputs.Point

        enhanced_log(log_callback, f"Looking up wall {wall_name} in Excel data", 'DEBUG')
        wall_row = df_wall[df_wall['Wall'] == wall_name]

        if wall_row.empty:
            error_msg = f"Wall {wall_name} not found in Excel data"
            errors_list.append(error_msg)
            log_validation_result(log_callback, f"Wall {wall_name} Data Found", False, error_msg)
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_wall_containment", False)
            return False
        else:
            log_validation_result(log_callback, f"Wall {wall_name} Data Found", True, "Wall found in Excel data")

        # Check required columns
        required_cols = ['Points', 'Thickness (mm)']
        missing_cols = [col for col in required_cols if col not in df_wall.columns]

        if missing_cols:
            error_msg = f"Missing required columns for wall {wall_name}: {missing_cols}"
            errors_list.append(error_msg)
            log_validation_result(log_callback, f"Wall {wall_name} Required Columns", False, error_msg)
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_function_exit(log_callback, "_validate_wall_containment", False)
            return False
        else:
            log_validation_result(log_callback, f"Wall {wall_name} Required Columns", True,
                                "All required columns present")

        points_field = wall_row['Points'].iloc[0]
        thickness_mm = wall_row['Thickness (mm)'].iloc[0]

        enhanced_log(log_callback, f"Wall {wall_name} - Points: {points_field}, Thickness: {thickness_mm}mm", 'DEBUG')

        if pd.notna(points_field) and points_field and pd.notna(thickness_mm):
            point_names = [p.strip() for p in points_field.split(';') if p.strip()]
            log_performance_metric(log_callback, f"Wall_{wall_name}_points_count", len(point_names), "points")
            enhanced_log(log_callback, f"Wall {wall_name} has {len(point_names)} points: {point_names}", 'DEBUG')

            coords = []

            for point_name in point_names:
                enhanced_log(log_callback, f"Looking up point {point_name} for wall {wall_name}", 'DEBUG')

                point_row = df_point[df_point['Point'] == point_name]
                if point_row.empty:
                    error_msg = f"Point {point_name} for wall {wall_name} not found in Point data"
                    errors_list.append(error_msg)
                    log_validation_result(log_callback, f"Point {point_name} Data", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_function_exit(log_callback, "_validate_wall_containment", False)
                    return False

                x = point_row['X (m)'].iloc[0]
                y = point_row['Y (m)'].iloc[0]
                coords.append((x, y))
                enhanced_log(log_callback, f"Point {point_name}: ({x:.3f}, {y:.3f})", 'DEBUG')

            log_performance_metric(log_callback, f"Wall_{wall_name}_valid_coords", len(coords), "coordinates")

            if len(coords) >= 2:
                # Create wall polygon with thickness
                thickness_m = thickness_mm / 1000
                log_calculation_result(log_callback, f"Wall_{wall_name}_thickness", thickness_m, "m")

                enhanced_log(log_callback, f"Creating wall geometry for {wall_name} with {thickness_m:.3f}m thickness", 'DEBUG')

                try:
                    line = LineString(coords)
                    wall_poly = line.buffer(thickness_m / 2)

                    log_calculation_result(log_callback, f"Wall_{wall_name}_area", wall_poly.area, "m²")
                    enhanced_log(log_callback, f"Wall {wall_name} polygon area: {wall_poly.area:.3f} m²", 'DEBUG')

                    # Check containment
                    is_contained = cap_poly.contains(wall_poly)

                    if not is_contained:
                        error_msg = f"Wall {wall_name} not fully contained in pile cap"
                        errors_list.append(error_msg)
                        log_validation_result(log_callback, f"Wall {wall_name} Containment", False, error_msg)
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        log_function_exit(log_callback, "_validate_wall_containment", False)
                        return False
                    else:
                        log_validation_result(log_callback, f"Wall {wall_name} Containment", True,
                                            "Wall fully contained within pile cap")
                        enhanced_log(log_callback, f"Wall {wall_name} is fully contained within pile cap", 'DEBUG')

                except Exception as e:
                    error_msg = f"Error creating wall geometry for {wall_name}: {e}"
                    errors_list.append(error_msg)
                    log_error_with_context(log_callback, e, f"wall geometry creation for {wall_name}")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_function_exit(log_callback, "_validate_wall_containment", False)
                    return False
            else:
                error_msg = f"Wall {wall_name} has insufficient coordinates ({len(coords)} < 2)"
                errors_list.append(error_msg)
                log_validation_result(log_callback, f"Wall {wall_name} Coordinate Count", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_function_exit(log_callback, "_validate_wall_containment", False)
                return False
        else:
            enhanced_log(log_callback, f"Wall {wall_name} has missing or invalid points/thickness data", 'DEBUG')
            log_validation_result(log_callback, f"Wall {wall_name} Data Validity", True,
                                "No valid data to validate (skipped)")

    except Exception as e:
        error_msg = f"Error validating wall {wall_name} containment: {e}"
        errors_list.append(error_msg)
        log_error_with_context(log_callback, e, f"_validate_wall_containment for {wall_name}")
        enhanced_log(log_callback, error_msg, 'ERROR')
        log_function_exit(log_callback, "_validate_wall_containment", False)
        return False

    log_validation_result(log_callback, f"Wall {wall_name} Overall Containment", True,
                        "Wall validation completed successfully")
    log_function_exit(log_callback, "_validate_wall_containment", True)
    return True


# Private helper functions

def _validate_point_dataframe(df: pd.DataFrame, log_callback: Optional[Callable] = None) -> List[str]:
    """Validate Point DataFrame structure."""
    log_function_entry(log_callback, "_validate_point_dataframe", df_shape=(len(df), len(df.columns)))

    warnings = []

    with create_timed_logger(log_callback, "point_dataframe_validation"):
        enhanced_log(log_callback, f"Validating Point DataFrame structure ({len(df)} rows, {len(df.columns)} columns)", 'DEBUG')

        # Check required columns
        required_cols = ['Point', 'X (m)', 'Y (m)']
        enhanced_log(log_callback, f"Checking for required columns: {required_cols}", 'DEBUG')

        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            warning_msg = f"Point DataFrame missing columns: {missing_cols}"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Point DataFrame Required Columns", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Point DataFrame Required Columns", True,
                                "All required columns present")

        # Check for duplicate point names
        if 'Point' in df.columns:
            enhanced_log(log_callback, "Checking for duplicate point names", 'DEBUG')
            duplicates = df['Point'].duplicated()

            if duplicates.any():
                duplicate_count = duplicates.sum()
                warning_msg = "Duplicate point names found in Point DataFrame"
                warnings.append(warning_msg)
                log_validation_result(log_callback, "Point Name Uniqueness", False,
                                    f"{duplicate_count} duplicate point names found")
                log_performance_metric(log_callback, "Duplicate_point_names", duplicate_count, "duplicates")
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "Point Name Uniqueness", True,
                                    f"All {len(df)} point names are unique")

        # Additional data quality checks
        if 'X (m)' in df.columns and 'Y (m)' in df.columns:
            enhanced_log(log_callback, "Checking coordinate data quality", 'DEBUG')

            # Check for null coordinates
            null_x = df['X (m)'].isnull().sum()
            null_y = df['Y (m)'].isnull().sum()

            log_performance_metric(log_callback, "Null_X_coordinates", null_x, "points")
            log_performance_metric(log_callback, "Null_Y_coordinates", null_y, "points")

            if null_x > 0 or null_y > 0:
                warning_msg = f"Found {null_x} null X coordinates and {null_y} null Y coordinates"
                warnings.append(warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')

        log_performance_metric(log_callback, "Point_DataFrame_warnings", len(warnings), "warnings")

    log_function_exit(log_callback, "_validate_point_dataframe", f"{len(warnings)} warnings")
    return warnings


def _validate_column_dataframe(df: pd.DataFrame, log_callback: Optional[Callable] = None) -> List[str]:
    """Validate Column DataFrame structure."""
    log_function_entry(log_callback, "_validate_column_dataframe", df_shape=(len(df), len(df.columns)))

    warnings = []

    with create_timed_logger(log_callback, "column_dataframe_validation"):
        enhanced_log(log_callback, f"Validating Column DataFrame structure ({len(df)} rows, {len(df.columns)} columns)", 'DEBUG')

        # Check required columns
        required_cols = ['Column', 'Center Point', 'Base Level']
        enhanced_log(log_callback, f"Checking for required columns: {required_cols}", 'DEBUG')

        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            warning_msg = f"Column DataFrame missing columns: {missing_cols}"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Column DataFrame Required Columns", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Column DataFrame Required Columns", True,
                                "All required columns present")

        # Check for duplicate column names
        if 'Column' in df.columns:
            enhanced_log(log_callback, "Checking for duplicate column names", 'DEBUG')
            duplicates = df['Column'].duplicated()

            if duplicates.any():
                duplicate_count = duplicates.sum()
                warning_msg = "Duplicate column names found in Column DataFrame"
                warnings.append(warning_msg)
                log_validation_result(log_callback, "Column Name Uniqueness", False,
                                    f"{duplicate_count} duplicate column names found")
                log_performance_metric(log_callback, "Duplicate_column_names", duplicate_count, "duplicates")
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "Column Name Uniqueness", True,
                                    f"All {len(df)} column names are unique")

        log_performance_metric(log_callback, "Column_DataFrame_warnings", len(warnings), "warnings")

    log_function_exit(log_callback, "_validate_column_dataframe", f"{len(warnings)} warnings")
    return warnings


def _validate_wall_dataframe(df: pd.DataFrame, log_callback: Optional[Callable] = None) -> List[str]:
    """Validate Wall DataFrame structure."""
    log_function_entry(log_callback, "_validate_wall_dataframe", df_shape=(len(df), len(df.columns)))

    warnings = []

    with create_timed_logger(log_callback, "wall_dataframe_validation"):
        enhanced_log(log_callback, f"Validating Wall DataFrame structure ({len(df)} rows, {len(df.columns)} columns)", 'DEBUG')

        # Check required columns
        required_cols = ['Wall', 'Points', 'Base Level']
        enhanced_log(log_callback, f"Checking for required columns: {required_cols}", 'DEBUG')

        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            warning_msg = f"Wall DataFrame missing columns: {missing_cols}"
            warnings.append(warning_msg)
            log_validation_result(log_callback, "Wall DataFrame Required Columns", False, warning_msg)
            enhanced_log(log_callback, warning_msg, 'WARNING')
        else:
            log_validation_result(log_callback, "Wall DataFrame Required Columns", True,
                                "All required columns present")

        # Check for duplicate wall names
        if 'Wall' in df.columns:
            enhanced_log(log_callback, "Checking for duplicate wall names", 'DEBUG')
            duplicates = df['Wall'].duplicated()

            if duplicates.any():
                duplicate_count = duplicates.sum()
                warning_msg = "Duplicate wall names found in Wall DataFrame"
                warnings.append(warning_msg)
                log_validation_result(log_callback, "Wall Name Uniqueness", False,
                                    f"{duplicate_count} duplicate wall names found")
                log_performance_metric(log_callback, "Duplicate_wall_names", duplicate_count, "duplicates")
                enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "Wall Name Uniqueness", True,
                                    f"All {len(df)} wall names are unique")

        log_performance_metric(log_callback, "Wall_DataFrame_warnings", len(warnings), "warnings")

    log_function_exit(log_callback, "_validate_wall_dataframe", f"{len(warnings)} warnings")
    return warnings

