"""
Ultimate Limit State (ULS) N-M Interaction Validation for Bored Pile Foundations

This module provides comprehensive validation and analysis tools for N-M (axial force-moment)
interaction in Ultimate Limit State design of bored pile foundations. The module implements
sophisticated geometric validation algorithms, load data processing, and structural adequacy
assessment methods to ensure foundation designs meet safety requirements and code compliance.

Key Features:
- Geometric containment validation using polygon-based algorithms for precise load point analysis
- Advanced load data processing with eccentricity considerations and code-compliant minimum values
- Utilization ratio calculations for optimized design verification and capacity assessment
- Multi-scale validation supporting various safety factors and design margin requirements
- Efficient data filtering and transformation for segment-specific pile analysis
- Integration with structural analysis results and reinforced concrete design standards

Author: Foundation-Automation Development Team
Version: Compatible with ULS design workflows and international foundation design standards
"""

import numpy as np
import pandas as pd
import matplotlib.path as mplPath
from typing import Union, Tuple, Optional


def check_nm_points(df_nm_curve: pd.DataFrame, 
                   df_load_nm: pd.DataFrame, 
                   scale: float = 1.0) -> bool:
    """
    Validate load points against scaled N-M interaction curve using geometric containment analysis.
    
    This function performs comprehensive validation of applied loads against the Ultimate Limit State
    N-M interaction curve boundary. Using sophisticated polygon-based geometric algorithms, it determines
    whether all load combinations fall within the acceptable design envelope, ensuring structural
    adequacy and code compliance for bored pile foundations under ultimate loading conditions.

    The validation process includes:
    - Curve scaling for safety factor application and design margin assessment
    - High-precision geometric containment analysis using matplotlib path algorithms
    - Optimized single-point and multi-point validation with robust performance characteristics
    - Comprehensive boundary condition handling for edge cases and critical load combinations
    """
    # Apply scaling factor to interaction curve coordinates for safety margin assessment
    # This enables validation against factored capacity curves per design code requirements
    curve_m = df_nm_curve['M (kNm)'].values * scale  # Scale moment capacity uniformly
    curve_n = df_nm_curve['N (kN)'].values * scale   # Scale axial force capacity uniformly
    curve_points = np.column_stack((curve_m, curve_n))  # Combine into coordinate pairs
    
    # Create geometric path object for high-precision containment analysis
    # matplotlib.path provides robust polygon algorithms for complex boundary shapes
    poly_path = mplPath.Path(curve_points)
    
    # Extract applied load coordinates for validation analysis
    load_m = df_load_nm['M (kNm)'].values  # Applied moment values from structural analysis
    load_n = df_load_nm['N (kN)'].values   # Applied axial force values from analysis
    load_points = np.column_stack((load_m, load_n))  # Combine into coordinate pairs
    
    # Perform geometric containment analysis based on load point count
    # Optimized algorithms handle both single-point and multi-point validation efficiently
    if len(load_points) == 1:
        # Single point validation using optimized contains_point method
        return poly_path.contains_point(load_points[0])
    else:
        # Multi-point validation using vectorized contains_points method
        # Returns True only if ALL load points are within the interaction curve boundary
        return np.all(poly_path.contains_points(load_points))


def filter_load_by_segment(pile_uls_df: pd.DataFrame, 
                          pile_segment_mark: str) -> pd.DataFrame:
    """
    Extract and transform pile load data for specific segment analysis with code-compliant processing.
    
    This function processes raw structural analysis results for a specific pile segment, applying
    proper load transformations, sign conventions, and minimum eccentricity requirements per
    structural design codes. The processing ensures compatibility with N-M interaction validation
    and maintains consistency with Ultimate Limit State design methodology.

    The data processing includes:
    - Segment-specific data filtering based on pile identification systems
    - Load sign convention application (compression positive for axial forces)
    - Biaxial moment combination using vector addition principles
    - Minimum eccentricity enforcement per structural design code requirements
    - Systematic load combination identification and organization for analysis workflows
    """
    # Parse pile segment identifier to extract base pile mark and part designation
    # Format: 'PileMark_Part' enables systematic organization of pile foundation data
    pile_mark, part = pile_segment_mark.split('_')
    
    # Filter structural analysis data for the specified pile segment
    # This isolates relevant load combinations for focused analysis and verification
    df_filtered = pile_uls_df[pile_uls_df['Line'] == pile_segment_mark].copy()
    
    # Handle empty dataset case with consistent return format
    # Ensures robust operation even when specified segment has no analysis data
    if df_filtered.empty:
        return pd.DataFrame(columns=['Pile Mark', 'Part', 'Load Combination', 'N (kN)', 'M (kNm)'])
    
    # Apply proper sign convention for axial forces in foundation design
    # Structural analysis convention (tension positive) → Foundation design (compression positive)
    axial_loads = -df_filtered['P'].values  # Negate to convert to compression positive
    
    # Calculate combined moments from biaxial bending components
    # Uses vector addition to determine resultant moment for interaction analysis
    mxy = np.sqrt(df_filtered['M2'].values**2 + df_filtered['M3'].values**2)
    
    # Apply minimum eccentricity requirements per structural design codes
    # Standard requirement: e_min = 2% of axial force magnitude
    m_min = 0.02 * np.abs(df_filtered['P'].values)  # Minimum moment from eccentricity
    
    # Determine final design moments using maximum of calculated and minimum values
    # This ensures code compliance and accounts for construction tolerances
    moments = np.maximum(mxy, m_min)
    
    # Create systematically organized result DataFrame for N-M interaction analysis
    return pd.DataFrame({
        'Pile Mark': pile_mark,  # Base pile identifier for organization
        'Part': part + '_' + df_filtered['Station'].astype(str),  # Detailed location identifier
        'Load Combination': (df_filtered['Line'] + '_' + 
                           df_filtered['Station'].astype(str) + '-' + 
                           df_filtered['OutputCase']),  # Comprehensive load case ID
        'N (kN)': axial_loads,   # Processed axial forces (compression positive)
        'M (kNm)': moments       # Final design moments with minimum eccentricity
    })


def calculate_utilization(df_nm_curve: pd.DataFrame, 
                         df_load_nm: pd.DataFrame, 
                         precision: float = 0.01, 
                         max_scale: float = 1.0) -> float:
    """
    Calculate maximum capacity utilization ratio for applied loads against N-M interaction curve.
    
    This function determines the maximum utilization of the N-M interaction curve capacity by
    iteratively scaling the curve until all applied loads fall within the boundary. The utilization
    ratio provides critical insight for design optimization, capacity assessment, and reinforcement
    efficiency evaluation in Ultimate Limit State foundation design.

    The utilization analysis includes:
    - Iterative curve scaling with configurable precision for accurate capacity assessment
    - Binary search optimization for efficient utilization determination
    - Comprehensive validation ensuring all load combinations remain within safe boundaries
    - Design optimization feedback for reinforcement ratio adjustment and pile sizing
    """
    # Iterative analysis using configurable precision for accurate utilization determination
    # Start from minimum utilization and increment until loads exceed curve boundary
    for scale in np.arange(precision, max_scale + precision, precision):
        # Validate all load points against scaled interaction curve
        # Returns True if all loads are within the scaled capacity boundary
        if check_nm_points(df_nm_curve, df_load_nm, scale):
            # Continue iteration to find maximum utilization ratio
            continue
        else:
            # First scale where loads exceed boundary indicates maximum utilization
            # Return previous scale value as the maximum safe utilization
            return max(scale - precision, 0.0)
    
    # If all iterations pass validation, return maximum scale factor
    # This indicates utilization at or below the specified maximum limit
    return max_scale
