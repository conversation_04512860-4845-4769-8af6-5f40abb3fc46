import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from etabs_cwls.interface import _main
from etabs_cwls.core import _class
import sys
import os
import subprocess
import io
from contextlib import redirect_stdout
import logging
from tkinter import ttk
import math
import pandas as pd

# Set up logging
script_dir = os.path.dirname(os.path.abspath(__file__))
# Logging will be configured dynamically after the Excel output folder is known
logger = logging.getLogger(__name__)

class LoadingScheduleGUI:
    def __init__(self, root):
        logger.debug("Initializing GUI")
        self.root = root
        self.root.title("Loading Schedule Automation")
        self.root.geometry("600x500")
        # Explicitly set AIS.ico icon for this window
        try:
            from config.app_config import ICON_PATH
            import os
            if ICON_PATH.exists():
                self.root.iconbitmap(str(ICON_PATH))
        except Exception as e:
            logger.warning(f"Could not set window icon in LoadingScheduleGUI: {e}")
        
        # Initialize data classes
        logger.debug("Initializing data classes")
        self.file_path = _class.file_path()
        self.design_data = _class.design_data()
        self.ETABS_converter = _class.ETABS_converter()
        
        # Set window icon
        # (Removed AIS.ico icon setting as per user request)

        # Create main frame
        logger.debug("Creating main frame")
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create all UI elements first
        self.create_version_selection()
        self.create_file_selection()
        self.create_load_type_selection()
        self.create_action_buttons()
        self.load_factor_inputs()
        
        # Status Label
        self.status_label = ttk.Label(self.main_frame, text="")
        self.status_label.grid(row=5, column=0, columnspan=2, pady=10)
        
        # Set initial state after all UI elements are created
        self.on_version_change()
        
        logger.debug("GUI initialization completed")
        
    def create_version_selection(self):
        version_frame = ttk.LabelFrame(self.main_frame, text="ETABS Version", padding="5")
        version_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.version_var = tk.IntVar(value=2)  # Set default to ETABS 2021
        
        ttk.Radiobutton(version_frame, text="ETABS 2016/2017", 
                       variable=self.version_var, value=1,
                       command=self.on_version_change).grid(row=0, column=0, padx=5)
        ttk.Radiobutton(version_frame, text="ETABS 2021", 
                       variable=self.version_var, value=2,
                       command=self.on_version_change).grid(row=0, column=1, padx=5)

    def create_file_selection(self):
        file_frame = ttk.LabelFrame(self.main_frame, text="File Selection", padding="5")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # First ETABS file
        ttk.Label(file_frame, text="Gravity Model:").grid(row=0, column=0, sticky=tk.W)
        self.file1_entry = ttk.Entry(file_frame, width=50)
        self.file1_entry.grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="Browse", 
                  command=lambda: self.browse_file(1)).grid(row=0, column=2)
        
        # Second ETABS file (optional)
        ttk.Label(file_frame, text="Wind Model (Optional):").grid(row=1, column=0, sticky=tk.W)
        self.file2_entry = ttk.Entry(file_frame, width=50)
        self.file2_entry.grid(row=1, column=1, padx=5)
        ttk.Button(file_frame, text="Browse", 
                  command=lambda: self.browse_file(2)).grid(row=1, column=2)
        
        # Excel file display
        ttk.Label(file_frame, text="Excel File:").grid(row=2, column=0, sticky=tk.W)
        self.excel_entry = ttk.Entry(file_frame, width=50, state='readonly')
        self.excel_entry.grid(row=2, column=1, padx=5)
        self.open_excel_button = ttk.Button(file_frame, text="Open Excel", 
                                          command=self.open_excel_file, state='disabled')
        self.open_excel_button.grid(row=2, column=2)

    def create_load_type_selection(self):
        # Only show Pier Forces as the selectable load type
        self.load_frame = ttk.LabelFrame(self.main_frame, text="Load Type Selection", padding="5")
        self.load_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.load_type_var = tk.IntVar(value=2)  # Default to Pier Forces
        self.pier_force_radio = ttk.Radiobutton(self.load_frame, text="Pier Forces", variable=self.load_type_var, value=2)
        self.pier_force_radio.grid(row=0, column=0, padx=5)
        # Hide the frame since only one option is available
        self.load_frame.grid_remove()

    def load_factor_inputs(self):
        load_frame = ttk.LabelFrame(self.main_frame, text="Load Factor", padding="5")
        load_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # Wind Factor
        ttk.Label(load_frame, text="Wind Factor:").grid(row=0, column=0, sticky=tk.W)
        self.wind_factor_var = tk.DoubleVar(value=1.0)
        ttk.Entry(load_frame, textvariable=self.wind_factor_var, width=10).grid(row=0, column=1, sticky=tk.W)

        # Add Torsion checkbox (aligned with Wind Factor)
        self.torsion_var = tk.BooleanVar(value=True)
        self.torsion_checkbox = ttk.Checkbutton(load_frame, text="Torsion", variable=self.torsion_var)
        self.torsion_checkbox.grid(row=0, column=2, padx=10, sticky=tk.W)

        # Soil Load Factor
        ttk.Label(load_frame, text="Soil Load Factor:").grid(row=1, column=0, sticky=tk.W)
        self.soil_factor_var = tk.DoubleVar(value=1.0)
        ttk.Entry(load_frame, textvariable=self.soil_factor_var, width=10).grid(row=1, column=1, sticky=tk.W)

        # Earthquake Factor
        ttk.Label(load_frame, text="Earthquake Factor:").grid(row=2, column=0, sticky=tk.W)
        self.earthquake_factor_var = tk.DoubleVar(value=1.0)
        ttk.Entry(load_frame, textvariable=self.earthquake_factor_var, width=10).grid(row=2, column=1, sticky=tk.W)

        # Uplift Factor
        ttk.Label(load_frame, text="Uplift Factor:").grid(row=3, column=0, sticky=tk.W)
        self.uplift_factor_var = tk.DoubleVar(value=1.0)
        ttk.Entry(load_frame, textvariable=self.uplift_factor_var, width=10).grid(row=3, column=1, sticky=tk.W)

        # Create load factor matrix frame (put below all factors)
        load_factor_frame = ttk.LabelFrame(load_frame, text="Load Factor Matrix")
        load_factor_frame.grid(row=4, column=0, columnspan=3, padx=(10, 10), pady=5, sticky="nsew")

        # Create headers for load types
        for i, load_type in enumerate(['Dead', 'SDL', 'Live']):
            ttk.Label(load_factor_frame, text=load_type).grid(row=0, column=i+1, padx=5, pady=5)

        # Create headers for load types (rotated)
        canvas = tk.Canvas(load_factor_frame, width=20, height=100)
        canvas.grid(row=1, column=0, rowspan=3)
        canvas.create_text(10, 50, text="Load\nFactor", angle=90, anchor="w")

        # Create matrix entries and variables
        self.matrix_entries = []
        self.matrix_vars = []
        for i in range(3):
            row_entries = []
            row_vars = []
            for j in range(3):
                var = tk.StringVar(value="1.0" if i == j else "0.0")
                entry = ttk.Entry(load_factor_frame, width=8, textvariable=var)
                entry.grid(row=i+1, column=j+1, padx=5, pady=5)
                row_entries.append(entry)
                row_vars.append(var)
            self.matrix_entries.append(row_entries)
            self.matrix_vars.append(row_vars)
    
    def create_action_buttons(self):
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Initialize Design Excel", 
                  command=self.initialize_design).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Create Load Schedule", 
                  command=self.create_schedule).grid(row=0, column=1, padx=5)
    def on_version_change(self):
        # Only Pier Forces is available, so always set to 2 and hide the frame
        self.load_type_var.set(2)
        self.load_frame.grid_remove()

    def browse_file(self, file_num):
        filetypes = [("Microsoft Access Database", "*.accdb"), ("All Files", "*.*")]
        filename = filedialog.askopenfilename(filetypes=filetypes)
        if filename:
            if file_num == 1:
                self.file1_entry.delete(0, tk.END)
                self.file1_entry.insert(0, filename)
                # Update Excel file path when gravity model is selected
                self.file_path.accessfile1 = filename
                self.file_path.excel_outputfolder = os.path.dirname(filename)
                self.file_path.design_excel_path = os.path.join(
                    self.file_path.excel_outputfolder,
                    os.path.splitext(os.path.basename(filename))[0] + '_loading_schedule.xlsx'
                )
                # Setup logging to the Excel output folder after file selection
                log_path = os.path.join(self.file_path.excel_outputfolder, 'gui_debug.log')
                for handler in logger.handlers[:]:
                    logger.removeHandler(handler)
                logging.basicConfig(
                    level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.FileHandler(log_path),
                        logging.StreamHandler()
                    ]
                )
                logger.debug(f"Logging redirected to {log_path}")
                self.update_excel_display()
            else:
                self.file2_entry.delete(0, tk.END)
                self.file2_entry.insert(0, filename)

    def initialize_design(self):
        try:
            # Validate inputs
            if not self.version_var.get():
                messagebox.showerror("Error", "Please select ETABS version")
                return
                
            if not self.file1_entry.get():
                messagebox.showerror("Error", "Please select the Gravity Model file")
                return
                
            if self.version_var.get() == 1 and not self.load_type_var.get():
                messagebox.showerror("Error", "Please select load type")
                return
            
            # Set up file paths
            self.file_path.accessfile1 = self.file1_entry.get()
            self.file_path.accessfile2 = self.file2_entry.get() if self.file2_entry.get() else None
            
            # Set up design data
            self.design_data.etabsversion = self.version_var.get()
            if self.version_var.get() == 1:  # ETABS 2016/2017
                self.design_data.use_joint_reactions = (self.load_type_var.get() == 1)
            
            # Assign ETABS converter
            self.ETABS_converter = _main.assign_ETABS_converter(self.ETABS_converter, self.design_data)
            
            # Get file paths
            self.file_path = _main.filepath_selection(self.file_path, 1, self.ETABS_converter)
            
            # Initialize design
            self.file_path, self.design_data = _main.initialization(self.file_path, self.design_data, self.ETABS_converter)
            
            # Update Excel file display
            self.update_excel_display()
            
            messagebox.showinfo("Success", "Design Excel initialized successfully!")
            self.status_label.config(text="Design Excel initialized successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", str(e))
            self.status_label.config(text=f"Error: {str(e)}")

    def create_schedule(self):
        try:
            # Validate inputs
            if not self.file1_entry.get():
                messagebox.showerror("Error", "Please select the Gravity Model file")
                return
                
            if self.version_var.get() == 1 and not self.load_type_var.get():
                messagebox.showerror("Error", "Please select load type")
                return
            
            # Clear design data
            self.design_data.df_mapping = None
            self.design_data.df_jtreaction = None
            self.design_data.df_schedule = None
            self.design_data.df_pierforceG = None
            self.design_data.df_pierforceL = None
            self.design_data.df_framepier = None
            self.design_data.df_shellpier = None
            self.design_data.df_piersection = None
            self.design_data.df_addrotation = None
            
            # Set up file paths
            self.file_path.accessfile1 = self.file1_entry.get()
            self.file_path.accessfile2 = self.file2_entry.get() if self.file2_entry.get() else None
            
            # Get file paths
            self.file_path = _main.filepath_selection(self.file_path, 2, self.ETABS_converter)
            
            self.design_data.etabsversion = self.version_var.get()

            # Store torsion option
            self.design_data.consider_torsion = self.torsion_var.get()

            # Create schedule based on load type selection
            wind_factor = self.wind_factor_var.get()
            matrix = [[float(var.get()) for var in row] for row in self.matrix_vars]
            self.design_data.wind_factor = wind_factor
            self.design_data.load_matrix = matrix
            if self.load_type_var.get() == 2:  # Pier Force selected
                self.design_data = _main.create_schedule_pier_force(
                    self.file_path, self.design_data, self.ETABS_converter
                )
            else:  # Joint Reaction selected
                self.design_data = _main.create_schedule_joint_reaction(self.file_path, self.design_data)
            
            # Update Excel file display
            self.update_excel_display()
            
            messagebox.showinfo("Success", "Load schedule created successfully!")
            self.status_label.config(text="Load schedule created successfully!")
        except Exception as e:
            messagebox.showerror("Error", str(e))
            self.status_label.config(text=f"Error: {str(e)}")

    def update_excel_display(self):
        """Update the Excel file display and open button state"""
        if hasattr(self.file_path, 'design_excel_path') and self.file_path.design_excel_path:
            self.excel_entry.config(state='normal')
            self.excel_entry.delete(0, tk.END)
            self.excel_entry.insert(0, self.file_path.design_excel_path)
            self.excel_entry.config(state='readonly')
            self.open_excel_button.config(state='normal')
        else:
            self.excel_entry.config(state='normal')
            self.excel_entry.delete(0, tk.END)
            self.excel_entry.config(state='readonly')
            self.open_excel_button.config(state='disabled')

    def open_excel_file(self):
        """Open the Excel file in the default application"""
        if hasattr(self.file_path, 'design_excel_path') and os.path.exists(self.file_path.design_excel_path):
            try:
                if sys.platform == 'win32':
                    os.startfile(self.file_path.design_excel_path)
                else:
                    subprocess.run(['xdg-open', self.file_path.design_excel_path])
                logger.debug("Excel file opened successfully")
            except Exception as e:
                logger.error(f"Error opening Excel file: {str(e)}")
                messagebox.showerror("Error", f"Could not open Excel file: {str(e)}")
        else:
            messagebox.showerror("Error", "Excel file not found")

def main():
    logger.debug("Starting application")
    root = tk.Tk()
    app = LoadingScheduleGUI(root)
    root.mainloop()
    logger.debug("Application closed")

if __name__ == "__main__":
    main() 