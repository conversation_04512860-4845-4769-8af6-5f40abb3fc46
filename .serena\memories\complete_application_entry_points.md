# Complete Application Entry Points

## Main Application Entry
- **`main.py`**: Primary application entry point
  - Command line debug mode support (`--debug` or `-d`)
  - Logging configuration (DEBUG/INFO levels)
  - Warning filters for pandas and general warnings
  - Creates root Tkinter window and launches ApplicationController

## Application Controller (`app_controller.py`)
- **Main Controller Class**: `ApplicationController`
  - **Authentication System**: Integrated with SecurityManager
  - **Session Management**: Token-based with configurable timeouts
  - **Multi-version Support**: Base/Ultimate user access levels
  - **Module Launchers**: 
    - ETABS CWLs (`run_etabs_cwls()`)
    - SAFE Model Builder (`run_safe_model_builder()`)
    - SAFE Design Checker (`run_safe_design_checker()`)
    - Foundation Agent (`run_foundation_agent()`)
  - **Security Features**:
    - Failed login attempt tracking
    - Account lockout mechanisms
    - Session verification with configurable intervals
    - Development bypass for testing

## Data Classes (`main_class.py`)
- **`ExcelInputs`**: Complete data structure for all input types
  - Material properties, soil properties, structural elements
  - Geometry data (points, beams, columns, walls, slabs)
  - Pile data (BP, SHP, DHP, MP configurations)
  - Geotechnical data (boreholes, SPT values, soil springs)
  - Loading data (patterns, cases, combinations)
  - Steel section and rebar data
- **`ExcelOutputs`**: Output data containers for all analysis results
- **`SafeMdbs`**: SAFE database result containers (versions 16, 22)
- **`FilePaths`**: Comprehensive file path management for all I/O operations
- **`DesignResults`**: Complete design result containers for all pile types

## Security Manager (`auth/security_manager.py`)
- **Authentication**: Password hashing with salt, session token generation
- **Access Control**: Google Drive integration for authorized user lists
- **Lockout Protection**: Configurable failed attempt tracking
- **Logging**: Comprehensive security event logging
- **Session Management**: Token verification and timeout handling