﻿"""
Pile Type Pre-Selection Utility Functions

This module contains utility functions for pile type pre-selection,
including GUI integration and capacity calculation integration.
"""

from typing import List, Dict, Any, Optional, Callable

from ..data_types.pile_preselection_types import PileTypeCandidate
from ..data_types.pile_types import PileType
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def create_pile_type_candidates_from_gui(selected_pile_types: List[Dict[str, Any]],
                                        log_callback: Optional[Callable] = None) -> List[PileTypeCandidate]:
    """
    Create pile type candidates from GUI selection data.

    Args:
        selected_pile_types: List of pile type data from GUI
        log_callback: Optional callback for logging preselection progress

    Returns:
        List of PileTypeCandidate objects
    """
    log_function_entry(log_callback, "create_pile_type_candidates_from_gui",
                      num_pile_types=len(selected_pile_types))

    candidates = []

    with create_timed_logger(log_callback, "pile_type_candidates_creation"):
        enhanced_log(log_callback, f"Creating pile type candidates from {len(selected_pile_types)} GUI selections", 'INFO')

        if not selected_pile_types:
            enhanced_log(log_callback, "No pile types selected from GUI", 'WARNING')
            log_validation_result(log_callback, "GUI Pile Types Present", False, "No pile types provided")
            log_function_exit(log_callback, "create_pile_type_candidates_from_gui", "Empty list")
            return candidates
        else:
            log_validation_result(log_callback, "GUI Pile Types Present", True,
                                f"{len(selected_pile_types)} pile types provided")

        # Map string to enum for validation
        type_mapping = {
            'DHP': PileType.DHP,
            'SHP': PileType.SHP,
            'BP': PileType.BP
        }
        enhanced_log(log_callback, f"Available pile type mappings: {list(type_mapping.keys())}", 'DEBUG')

        valid_candidates = 0
        invalid_candidates = 0

        for i, pile_data in enumerate(selected_pile_types):
            enhanced_log(log_callback, f"Processing pile type {i+1}/{len(selected_pile_types)}", 'DEBUG')

            try:
                # Extract and validate pile type string
                pile_type_str = pile_data.get('type', '').upper()
                enhanced_log(log_callback, f"Pile type {i+1}: {pile_type_str}", 'DEBUG')

                if not pile_type_str:
                    error_msg = f"Pile type {i+1} has empty type field"
                    log_validation_result(log_callback, f"Pile Type {i+1} Type Field", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    invalid_candidates += 1
                    continue

                if pile_type_str not in type_mapping:
                    error_msg = f"Unknown pile type: {pile_type_str}"
                    log_validation_result(log_callback, f"Pile Type {i+1} Recognition", False, error_msg)
                    log_error_with_context(log_callback, ValueError(error_msg), f"pile type validation {i+1}")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    invalid_candidates += 1
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, f"Pile Type {i+1} Recognition", True,
                                        f"Valid pile type: {pile_type_str}")

                # Extract and validate pile parameters
                capacity = pile_data.get('capacity', 0.0)
                min_spacing = pile_data.get('min_spacing', 1.5)
                section = pile_data.get('section', '')
                diameter = pile_data.get('diameter')

                enhanced_log(log_callback, f"Pile {i+1} parameters - Capacity: {capacity} kN, "
                           f"Spacing: {min_spacing} m, Section: {section}, Diameter: {diameter}", 'DEBUG')

                # Validate capacity
                if capacity <= 0:
                    error_msg = f"Pile type {i+1} has invalid capacity: {capacity} kN"
                    log_validation_result(log_callback, f"Pile Type {i+1} Capacity", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'WARNING')
                else:
                    log_validation_result(log_callback, f"Pile Type {i+1} Capacity", True,
                                        f"Valid capacity: {capacity} kN")

                # Validate minimum spacing
                if min_spacing <= 0:
                    error_msg = f"Pile type {i+1} has invalid minimum spacing: {min_spacing} m"
                    log_validation_result(log_callback, f"Pile Type {i+1} Min Spacing", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'WARNING')
                else:
                    log_validation_result(log_callback, f"Pile Type {i+1} Min Spacing", True,
                                        f"Valid spacing: {min_spacing} m")

                # Validate diameter for BP types
                if pile_type_str == 'BP':
                    if diameter is None or diameter <= 0:
                        error_msg = f"Bored pile (BP) type {i+1} requires valid diameter, got: {diameter}"
                        log_validation_result(log_callback, f"BP Type {i+1} Diameter", False, error_msg)
                        enhanced_log(log_callback, error_msg, 'WARNING')
                    else:
                        log_validation_result(log_callback, f"BP Type {i+1} Diameter", True,
                                            f"Valid diameter: {diameter} m")

                # Create candidate
                candidate = PileTypeCandidate(
                    pile_type=type_mapping[pile_type_str],
                    capacity_per_pile=capacity,
                    min_spacing=min_spacing,
                    section=section,
                    diameter=diameter  # None for H-piles, value for BP
                )

                candidates.append(candidate)
                valid_candidates += 1

                log_calculation_result(log_callback, f"Pile_candidate_{i+1}_created", candidate.display_name, "")
                enhanced_log(log_callback, f"Successfully created candidate {i+1}: {candidate.display_name}", 'DEBUG')

            except Exception as e:
                error_msg = f"Error processing pile type {i+1}: {e}"
                log_error_with_context(log_callback, e, f"pile candidate creation {i+1}")
                enhanced_log(log_callback, error_msg, 'ERROR')
                invalid_candidates += 1
                # Re-raise for critical errors like unknown pile types
                if isinstance(e, ValueError) and "Unknown pile type" in str(e):
                    raise

        # Log processing summary
        log_performance_metric(log_callback, "Valid_pile_candidates", valid_candidates, "candidates")
        log_performance_metric(log_callback, "Invalid_pile_candidates", invalid_candidates, "candidates")
        log_performance_metric(log_callback, "Total_candidates_processed", len(selected_pile_types), "candidates")

        if valid_candidates == 0:
            enhanced_log(log_callback, "No valid pile type candidates created from GUI data", 'ERROR')
            log_validation_result(log_callback, "Pile Candidates Creation", False,
                                "No valid candidates created")
        else:
            enhanced_log(log_callback, f"Successfully created {valid_candidates} pile type candidates", 'INFO')
            log_validation_result(log_callback, "Pile Candidates Creation", True,
                                f"{valid_candidates} valid candidates created")

            # Log candidate summary
            for i, candidate in enumerate(candidates):
                enhanced_log(log_callback, f"Candidate {i+1}: {candidate.display_name} - "
                           f"{candidate.capacity_per_pile} kN, {candidate.min_spacing} m spacing", 'INFO')

    log_function_exit(log_callback, "create_pile_type_candidates_from_gui", f"{len(candidates)} candidates")
    return candidates


def integrate_with_existing_capacity_calculation(pile_type: PileType,
                                               excel_inputs: Any,
                                               pile_mark: str = "TEMP",
                                               log_callback: Optional[Callable] = None) -> float:
    """
    Integrate with existing pile capacity calculation functions.

    Args:
        pile_type: Pile type to calculate capacity for
        excel_inputs: Excel input data
        pile_mark: Pile mark for capacity calculation
        log_callback: Optional callback for logging capacity calculation progress

    Returns:
        Calculated pile capacity in kN
    """
    log_function_entry(log_callback, "integrate_with_existing_capacity_calculation",
                      pile_type=pile_type.value, pile_mark=pile_mark,
                      excel_inputs_type=type(excel_inputs).__name__)

    with create_timed_logger(log_callback, "capacity_calculation_integration"):
        enhanced_log(log_callback, f"Integrating with existing capacity calculation for {pile_type.value}", 'INFO')

        try:
            # Import here to avoid circular dependencies
            enhanced_log(log_callback, "Importing pile capacity calculation functions", 'DEBUG')
            from design_fdn.pile_capacity import cal_capacity_dhp, cal_capacity_shp, cal_capacity_bp

            capacity = 0.0

            if pile_type == PileType.DHP:
                enhanced_log(log_callback, "Calculating DHP (Driven H-Pile) capacity", 'INFO')

                try:
                    dhp_results = cal_capacity_dhp(excel_inputs)
                    log_calculation_result(log_callback, "DHP_calculation_call", "completed", "")

                    if dhp_results.empty:
                        error_msg = "No DHP capacity results found"
                        log_validation_result(log_callback, "DHP Results Available", False, error_msg)
                        log_error_with_context(log_callback, ValueError(error_msg), "DHP capacity calculation")
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "DHP Results Available", True,
                                            f"DHP results DataFrame with {len(dhp_results)} rows")
                        enhanced_log(log_callback, f"DHP calculation returned {len(dhp_results)} results", 'DEBUG')

                    # Extract capacity value
                    capacity_column = 'Compression Capacity [w/o WL] (kN)'
                    if capacity_column not in dhp_results.columns:
                        error_msg = f"Required column '{capacity_column}' not found in DHP results"
                        log_validation_result(log_callback, "DHP Capacity Column", False, error_msg)
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "DHP Capacity Column", True,
                                            "Required capacity column found")

                    capacity = dhp_results[capacity_column].iloc[0]
                    log_calculation_result(log_callback, "DHP_capacity", capacity, "kN")
                    enhanced_log(log_callback, f"DHP capacity calculated: {capacity:.2f} kN", 'INFO')

                except Exception as e:
                    error_msg = f"Error in DHP capacity calculation: {e}"
                    log_error_with_context(log_callback, e, "DHP capacity calculation")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise

            elif pile_type == PileType.SHP:
                enhanced_log(log_callback, "Calculating SHP (Socket H-Pile) capacity", 'INFO')

                try:
                    shp_results = cal_capacity_shp(excel_inputs)
                    log_calculation_result(log_callback, "SHP_calculation_call", "completed", "")

                    if shp_results.empty:
                        error_msg = "No SHP capacity results found"
                        log_validation_result(log_callback, "SHP Results Available", False, error_msg)
                        log_error_with_context(log_callback, ValueError(error_msg), "SHP capacity calculation")
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "SHP Results Available", True,
                                            f"SHP results DataFrame with {len(shp_results)} rows")
                        enhanced_log(log_callback, f"SHP calculation returned {len(shp_results)} results", 'DEBUG')

                    # Extract capacity value
                    capacity_column = 'Compression Capacity [w/o WL] (kN)'
                    if capacity_column not in shp_results.columns:
                        error_msg = f"Required column '{capacity_column}' not found in SHP results"
                        log_validation_result(log_callback, "SHP Capacity Column", False, error_msg)
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "SHP Capacity Column", True,
                                            "Required capacity column found")

                    capacity = shp_results[capacity_column].iloc[0]
                    log_calculation_result(log_callback, "SHP_capacity", capacity, "kN")
                    enhanced_log(log_callback, f"SHP capacity calculated: {capacity:.2f} kN", 'INFO')

                except Exception as e:
                    error_msg = f"Error in SHP capacity calculation: {e}"
                    log_error_with_context(log_callback, e, "SHP capacity calculation")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise

            elif pile_type == PileType.BP:
                enhanced_log(log_callback, "Calculating BP (Bored Pile) capacity", 'INFO')

                try:
                    bp_results = cal_capacity_bp(excel_inputs)
                    log_calculation_result(log_callback, "BP_calculation_call", "completed", "")

                    if bp_results.empty:
                        error_msg = "No BP capacity results found"
                        log_validation_result(log_callback, "BP Results Available", False, error_msg)
                        log_error_with_context(log_callback, ValueError(error_msg), "BP capacity calculation")
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "BP Results Available", True,
                                            f"BP results DataFrame with {len(bp_results)} rows")
                        enhanced_log(log_callback, f"BP calculation returned {len(bp_results)} results", 'DEBUG')

                    # Extract capacity value
                    capacity_column = 'Compression Capacity [w/o WL] (kN)'
                    if capacity_column not in bp_results.columns:
                        error_msg = f"Required column '{capacity_column}' not found in BP results"
                        log_validation_result(log_callback, "BP Capacity Column", False, error_msg)
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, "BP Capacity Column", True,
                                            "Required capacity column found")

                    capacity = bp_results[capacity_column].iloc[0]
                    log_calculation_result(log_callback, "BP_capacity", capacity, "kN")
                    enhanced_log(log_callback, f"BP capacity calculated: {capacity:.2f} kN", 'INFO')

                except Exception as e:
                    error_msg = f"Error in BP capacity calculation: {e}"
                    log_error_with_context(log_callback, e, "BP capacity calculation")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise
            else:
                error_msg = f"Unknown pile type: {pile_type}"
                log_validation_result(log_callback, "Pile Type Recognition", False, error_msg)
                log_error_with_context(log_callback, ValueError(error_msg), "pile type validation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)

            # Validate calculated capacity
            if capacity <= 0:
                error_msg = f"Invalid calculated capacity: {capacity} kN"
                log_validation_result(log_callback, "Calculated Capacity Valid", False, error_msg)
                enhanced_log(log_callback, error_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "Calculated Capacity Valid", True,
                                    f"Valid capacity: {capacity:.2f} kN")

            # Check for reasonable capacity values
            max_reasonable_capacity = 50000  # 50,000 kN
            min_reasonable_capacity = 100    # 100 kN

            log_constraint_check(log_callback, "Reasonable Capacity Range", capacity,
                               f"{min_reasonable_capacity}-{max_reasonable_capacity}",
                               min_reasonable_capacity <= capacity <= max_reasonable_capacity)

            if capacity > max_reasonable_capacity:
                enhanced_log(log_callback, f"Calculated capacity ({capacity:.2f} kN) seems unusually high", 'WARNING')
            elif capacity < min_reasonable_capacity:
                enhanced_log(log_callback, f"Calculated capacity ({capacity:.2f} kN) seems unusually low", 'WARNING')

            # Log performance metrics
            log_performance_metric(log_callback, f"{pile_type.value}_capacity_calculated", capacity, "kN")

            enhanced_log(log_callback, f"Successfully integrated with {pile_type.value} capacity calculation", 'INFO')

        except Exception as e:
            error_msg = f"Failed to integrate with capacity calculation for {pile_type.value}: {e}"
            log_error_with_context(log_callback, e, "capacity calculation integration")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise

    log_function_exit(log_callback, "integrate_with_existing_capacity_calculation", f"{capacity:.2f} kN")
    return capacity

