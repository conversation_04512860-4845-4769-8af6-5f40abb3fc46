"""
Structure plotting functions for DXF visualization.

This module contains functions for plotting structural elements like
columns and walls, with both detailed and simple representations.
"""

from typing import List, Optional, Any
from ezdxf.enums import TextEntityAlignment

from ..data_types import ColumnData, WallData
from .text_manager import TextPlacementManager
from .utils import get_text_position_outside_polygon, create_sharp_corner_wall_polygon
from ..utils.geometry_utils import (
    create_column_polygons_from_excel,
    create_wall_polygons_from_excel,
    group_continuous_walls_from_excel
)


def plot_columns_dxf(msp, columns: List[ColumnData], excel_inputs: Optional[Any], text_height: float, 
                     text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot columns as polygons with marks positioned outside the column polygons.
    
    Args:
        msp: DXF model space
        columns: List of column data
        excel_inputs: Excel input data for detailed geometry (optional)
        text_height: Height for text labels
        text_manager: TextPlacementManager for label positioning
        log_callback: Optional logging callback
    """
    try:
        if not columns:
            if log_callback:
                log_callback("No columns to plot")
            return
        
        # Try to use detailed geometry from Excel inputs first
        detailed_success = False
        if excel_inputs is not None:
            detailed_success = _plot_detailed_structures(
                msp, columns, excel_inputs, text_height, text_manager, 
                'column', log_callback
            )
        
        # If detailed plotting failed or not available, use simple method
        if not detailed_success:
            _plot_simple_structures(
                msp, columns, text_height, text_manager, 
                'column', log_callback
            )
        
    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting columns: {e}")
        raise


def plot_walls_dxf(msp, walls: List[WallData], excel_inputs: Optional[Any], text_height: float, 
                   text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot walls with sharp corners for continuous groups and individual wall marks outside polygons.
    
    Args:
        msp: DXF model space
        walls: List of wall data
        excel_inputs: Excel input data for detailed geometry (optional)
        text_height: Height for text labels
        text_manager: TextPlacementManager for label positioning
        log_callback: Optional logging callback
    """
    try:
        if not walls:
            if log_callback:
                log_callback("No walls to plot")
            return
        
        # Try to use detailed geometry from Excel inputs first
        detailed_success = False
        if excel_inputs is not None:
            detailed_success = _plot_detailed_structures(
                msp, walls, excel_inputs, text_height, text_manager, 
                'wall', log_callback
            )
        
        # If detailed plotting failed or not available, use simple method
        if not detailed_success:
            _plot_simple_structures(
                msp, walls, text_height, text_manager, 
                'wall', log_callback
            )
        
    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting walls: {e}")
        raise


def _plot_detailed_structures(msp, structures: List, excel_inputs: Any, text_height: float, 
                             text_manager: TextPlacementManager, structure_type: str, 
                             log_callback=None) -> bool:
    """
    Plot structures using detailed geometry from Excel inputs.
    
    Args:
        msp: DXF model space
        structures: List of structure data (columns or walls)
        excel_inputs: Excel input data
        text_height: Height for text labels
        text_manager: TextPlacementManager for label positioning
        structure_type: 'column' or 'wall'
        log_callback: Optional logging callback
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Extract structure names from the provided list with robust error handling
        structure_names = []
        for struct in structures:
            if struct is not None and hasattr(struct, '__len__') and len(struct) >= 1:
                try:
                    # Safely extract the first element (structure name)
                    name = struct[0]
                    if name is not None and str(name).strip():  # Ensure name is valid
                        structure_names.append(str(name).strip())
                except (IndexError, TypeError) as e:
                    if log_callback:
                        log_callback(f"Warning: Skipping invalid structure entry: {struct} - {e}")
                    continue

        if not structure_names:
            if log_callback:
                log_callback(f"No valid {structure_type} names found for detailed plotting")
            return False

        if structure_type == 'column':
            return _plot_detailed_columns(msp, structure_names, excel_inputs, text_height, text_manager, log_callback)
        elif structure_type == 'wall':
            return _plot_detailed_walls(msp, structure_names, excel_inputs, text_height, text_manager, log_callback)
        else:
            return False

    except Exception as e:
        if log_callback:
            log_callback(f"Warning: Failed to plot detailed {structure_type}s: {e}")
        return False


def _plot_detailed_columns(msp, column_names: List[str], excel_inputs: Any, text_height: float, 
                          text_manager: TextPlacementManager, log_callback=None) -> bool:
    """Plot columns using detailed geometry from Excel inputs."""
    try:
        # Get detailed column polygons from Excel
        column_polygons = create_column_polygons_from_excel(excel_inputs, column_names)
        
        if not column_polygons:
            return False
        
        for column_name, column_data in column_polygons.items():
            polygon = column_data['polygon']
            mark = column_data['mark']
            centroid = column_data['centroid']
            
            # Plot column polygon
            coords = list(polygon.exterior.coords)
            if len(coords) >= 3:
                polyline = msp.add_lwpolyline(coords[:-1])  # Exclude duplicate last point
                polyline.close()
                polyline.dxf.layer = 'COLUMNS'
                
                # Position column mark outside the polygon with overlap prevention
                text_x, text_y = get_text_position_outside_polygon(polygon, centroid)
                final_x, final_y = text_manager.add_text_position(text_x, text_y, str(mark), text_height * 0.6)
                
                text = msp.add_text(
                    str(mark),
                    dxfattribs={
                        'layer': 'TEXT',
                        'height': text_height * 0.6
                    }
                )
                text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
        
        if log_callback:
            log_callback(f"Plotted {len(column_polygons)} columns with detailed geometry")
        return True
        
    except Exception as e:
        if log_callback:
            log_callback(f"Warning: Failed to plot detailed columns: {e}")
        return False


def _plot_detailed_walls(msp, wall_names: List[str], excel_inputs: Any, text_height: float, 
                        text_manager: TextPlacementManager, log_callback=None) -> bool:
    """Plot walls using detailed geometry from Excel inputs."""
    try:
        # Get individual wall polygons first
        wall_polygons = create_wall_polygons_from_excel(excel_inputs, wall_names)
        
        # Get grouped continuous walls with sharp corners
        grouped_walls = group_continuous_walls_from_excel(excel_inputs, wall_names)
        
        if not wall_polygons and not grouped_walls:
            return False
        
        # Plot grouped continuous walls first (these take priority)
        plotted_walls = set()
        if grouped_walls:
            for group_idx, group_data in enumerate(grouped_walls):
                wall_names_in_group = group_data['wall_names']
                
                # Create sharp-cornered polygon for continuous walls
                sharp_polygon = create_sharp_corner_wall_polygon(group_data, wall_polygons)
                
                # Plot unified wall polygon with sharp corners
                if sharp_polygon and not sharp_polygon.is_empty:
                    coords = list(sharp_polygon.exterior.coords)
                    if len(coords) >= 3:
                        polyline = msp.add_lwpolyline(coords[:-1])  # Exclude duplicate last point
                        polyline.close()
                        polyline.dxf.layer = 'WALLS'
                        
                        # Plot individual wall marks at their segment centroids, outside polygons
                        for wall_name in wall_names_in_group:
                            if wall_name in wall_polygons:
                                wall_data = wall_polygons[wall_name]
                                wall_polygon = wall_data['polygon']
                                wall_centroid = wall_polygon.centroid
                                
                                # Position wall mark outside the polygon with overlap prevention
                                text_x, text_y = get_text_position_outside_polygon(
                                    wall_polygon, (wall_centroid.x, wall_centroid.y)
                                )
                                final_x, final_y = text_manager.add_text_position(text_x, text_y, str(wall_name), text_height * 0.6)
                                
                                text = msp.add_text(
                                    str(wall_name),
                                    dxfattribs={
                                        'layer': 'TEXT',
                                        'height': text_height * 0.6
                                    }
                                )
                                text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
                        
                        # Mark these walls as plotted
                        plotted_walls.update(wall_names_in_group)
        
        # Plot individual walls that weren't part of any group
        if wall_polygons:
            for wall_name, wall_data in wall_polygons.items():
                if wall_name in plotted_walls:
                    continue  # Skip walls already plotted as part of groups
                    
                polygon = wall_data['polygon']
                mark = wall_data['mark']
                
                # Plot wall polygon
                coords = list(polygon.exterior.coords)
                if len(coords) >= 3:
                    polyline = msp.add_lwpolyline(coords[:-1])
                    polyline.close()
                    polyline.dxf.layer = 'WALLS'
                    
                    # Position wall mark outside the polygon with overlap prevention
                    wall_centroid = polygon.centroid
                    text_x, text_y = get_text_position_outside_polygon(
                        polygon, (wall_centroid.x, wall_centroid.y)
                    )
                    final_x, final_y = text_manager.add_text_position(text_x, text_y, str(mark), text_height * 0.6)
                    
                    text = msp.add_text(
                        str(mark),
                        dxfattribs={
                            'layer': 'TEXT',
                            'height': text_height * 0.6
                        }
                    )
                    text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
                    plotted_walls.add(wall_name)
        
        total_plotted = len(plotted_walls)
        group_count = len(grouped_walls) if grouped_walls else 0
        
        if log_callback:
            log_callback(f"Plotted {total_plotted} walls ({group_count} continuous groups) with detailed geometry")
        return True
        
    except Exception as e:
        if log_callback:
            log_callback(f"Warning: Failed to plot detailed walls: {e}")
        return False


def _plot_simple_structures(msp, structures: List, text_height: float,
                           text_manager: TextPlacementManager, structure_type: str,
                           log_callback=None) -> None:
    """
    Plot structures using simple representation.

    Args:
        msp: DXF model space
        structures: List of structure data (columns or walls)
        text_height: Height for text labels
        text_manager: TextPlacementManager for label positioning
        structure_type: 'column' or 'wall'
        log_callback: Optional logging callback
    """
    if structure_type == 'column':
        _plot_simple_columns(msp, structures, text_height, text_manager, log_callback)
    elif structure_type == 'wall':
        _plot_simple_walls(msp, structures, text_height, text_manager, log_callback)


def _plot_simple_columns(msp, columns: List[ColumnData], text_height: float,
                        text_manager: TextPlacementManager, log_callback=None) -> None:
    """Plot columns using simple representation."""
    plotted_count = 0

    for column in columns:
        # Robust validation of column data
        if column is None:
            if log_callback:
                log_callback("Warning: Skipping None column entry")
            continue

        if not hasattr(column, '__len__') or len(column) < 3:
            if log_callback:
                log_callback(f"Warning: Skipping invalid column entry (insufficient data): {column}")
            continue

        try:
            # Safely extract column data with validation
            col_name = column[0]
            if col_name is None or str(col_name).strip() == "":
                if log_callback:
                    log_callback(f"Warning: Skipping column with invalid name: {column}")
                continue

            col_x = float(column[1])
            col_y = float(column[2])

            # Validate coordinates
            if not all(isinstance(coord, (int, float)) and not (coord != coord) for coord in [col_x, col_y]):  # Check for NaN
                if log_callback:
                    log_callback(f"Warning: Skipping column {col_name} with invalid coordinates: ({col_x}, {col_y})")
                continue

            # Standard column representation as a small rectangle
            size = 0.3
            msp.add_lwpolyline([
                (col_x - size, col_y - size),
                (col_x + size, col_y - size),
                (col_x + size, col_y + size),
                (col_x - size, col_y + size)
            ], close=True, dxfattribs={'layer': 'COLUMNS'})

            # Add column label outside the rectangle with overlap prevention
            label_x, label_y = col_x + size + 0.1, col_y
            final_label_x, final_label_y = text_manager.add_text_position(label_x, label_y, str(col_name), text_height * 0.6)

            text = msp.add_text(
                str(col_name),
                dxfattribs={
                    'layer': 'TEXT',
                    'height': text_height * 0.6
                }
            )
            text.set_placement((final_label_x, final_label_y), align=TextEntityAlignment.LEFT)
            plotted_count += 1

        except Exception as e:
            if log_callback:
                log_callback(f"Warning: Failed to plot column {column}: {e}")
            continue

    if log_callback:
        log_callback(f"Plotted {plotted_count} columns (simple method)")


def _plot_simple_walls(msp, walls: List[WallData], text_height: float,
                      text_manager: TextPlacementManager, log_callback=None) -> None:
    """Plot walls using simple representation."""
    plotted_count = 0

    for wall in walls:
        if not wall or len(wall) < 2:
            continue

        try:
            wall_name, wall_points = wall[0], wall[1]

            if wall_points and len(wall_points) >= 2:
                # Convert points to coordinate list
                coords = []
                for point in wall_points:
                    if len(point) >= 2:
                        coords.append((float(point[0]), float(point[1])))

                if len(coords) >= 2:
                    # Create polyline for wall centerline
                    polyline = msp.add_lwpolyline(coords)
                    polyline.dxf.layer = 'WALLS'

                    # Add wall label at midpoint, offset from centerline with overlap prevention
                    if len(coords) >= 2:
                        mid_x = (coords[0][0] + coords[-1][0]) / 2
                        mid_y = (coords[0][1] + coords[-1][1]) / 2
                        final_x, final_y = text_manager.add_text_position(mid_x, mid_y + 0.3, str(wall_name), text_height * 0.6)

                        text = msp.add_text(
                            str(wall_name),
                            dxfattribs={
                                'layer': 'TEXT',
                                'height': text_height * 0.6
                            }
                        )
                        text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
                        plotted_count += 1

        except Exception as e:
            if log_callback:
                log_callback(f"Warning: Failed to plot wall {wall}: {e}")
            continue

    if log_callback:
        log_callback(f"Plotted {plotted_count} walls (simple method)")
