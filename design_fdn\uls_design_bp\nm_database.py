"""
Ultimate Limit State (ULS) N-M Interaction Database Management for Bored Pile Foundations

This module provides comprehensive database management capabilities for storing, retrieving,
and managing axial force-moment (N-M) interaction curves for bored pile foundation design
under Ultimate Limit State conditions. The module enables efficient pre-calculation,
caching, and retrieval of interaction curves for various pile configurations and
reinforcement ratios, significantly improving design workflow performance.

Key Features:
- High-performance parallel computation of N-M interaction curves for multiple configurations
- Persistent database storage using optimized pickle serialization for curve data
- Intelligent curve retrieval with steel ratio interpolation and matching algorithms
- Progress tracking and monitoring for large-scale curve generation operations
- Memory-efficient database management for extensive pile configuration libraries
- Integration with reinforcement design optimization and iterative design workflows

Author: Foundation-Automation Development Team
Version: Compatible with ULS design workflows and international foundation design standards
"""

import os
import pickle
import multiprocessing as mp
from typing import Dict, List, Optional, Callable, Tuple, Any
from copy import deepcopy
from tqdm import tqdm
# Use direct imports to avoid circular import issues in the ULS design package
from .circular_column import CircularColumn
from .rebar_design import update_rebar_configuration
from .nm_curve_calc import calculate_nm_curve
import pandas as pd

# Module Organization:
# - Database persistence: save_nm_curves_db(), load_nm_curves_db()
# - Parallel computation: _calculate_single_curve(), pre_calculate_nm_curves()
# - Curve retrieval: get_curve_from_db()
# - Integration support: Progress tracking, error handling, performance optimization

def save_nm_curves_db(nm_curves_db: Dict[str, Dict[float, pd.DataFrame]], file_path: str) -> None:
    """
    Save N-M interaction curves database to persistent storage for future retrieval.
    
    This function serializes and saves a complete N-M interaction curves database to
    a binary file using optimized pickle protocol. The database contains pre-calculated
    interaction curves for various pile configurations and steel reinforcement ratios,
    enabling rapid retrieval during design workflows without recalculation overhead.

    The storage process includes:
    - Directory creation for database file location if it doesn't exist
    - High-performance pickle serialization using the latest protocol version
    - Error handling and file system safety measures for reliable storage
    - Optimized compression and data organization for efficient file operations
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'wb') as f:
        pickle.dump(nm_curves_db, f, protocol=pickle.HIGHEST_PROTOCOL)


def load_nm_curves_db(file_path: str) -> Dict[str, Dict[float, pd.DataFrame]]:
    """
    Load N-M interaction curves database from persistent storage for design operations.
    
    This function deserializes and loads a complete N-M interaction curves database
    from a binary pickle file. The database contains pre-calculated interaction curves
    for various pile configurations and steel reinforcement ratios, enabling rapid
    curve retrieval during foundation design workflows without recalculation overhead.

    The loading process includes:
    - File existence validation with graceful handling of missing files
    - Safe deserialization using pickle with comprehensive error handling
    - Return of empty database structure if file is missing or corrupted
    - Preservation of original data structure and DataFrame formatting
    """
    # Check if database file exists at specified path
    if not os.path.exists(file_path):
        return {}
    
    try:
        # Load database using optimized pickle deserialization
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    except Exception:
        # Return empty database for any loading errors (corrupted file, permissions, etc.)
        return {}


def _calculate_single_curve(args: Tuple[CircularColumn, float, str]) -> Tuple[str, float, pd.DataFrame]:
    """
    Worker function for calculating a single N-M interaction curve in parallel processing.
    
    This function serves as a multiprocessing worker for calculating individual N-M
    interaction curves. It operates on a single pile configuration with a specific
    steel reinforcement ratio, enabling parallel computation of multiple curves
    across different CPU cores for optimal performance in large-scale calculations.

    The calculation process includes:
    - Unpacking of task arguments for pile configuration and steel ratio
    - Deep copying of column object to ensure thread safety in parallel processing
    - Rebar configuration update based on target steel reinforcement ratio
    - Complete N-M interaction curve calculation using specialized algorithms
    - Result packaging for integration into the main database structure
    """
    # Unpack task arguments for processing
    column_copy, steel_ratio, column_key = args
    
    # Update rebar configuration to match target steel reinforcement ratio
    # This modifies the column's reinforcement layout while preserving geometry
    modified_column = update_rebar_configuration(column_copy, steel_ratio)
    
    # Calculate complete N-M interaction curve for the configured pile
    # Returns DataFrame with axial force and moment capacity pairs
    df_nm_curve = calculate_nm_curve(modified_column)
    
    # Return results tuple for database integration
    return column_key, steel_ratio, df_nm_curve


def pre_calculate_nm_curves(unique_columns: List[CircularColumn], 
                          steel_ratios: Optional[List[float]] = None,
                          progress_callback: Optional[Callable[[str], None]] = None) -> Dict[str, Dict[float, pd.DataFrame]]:
    """
    Pre-calculate N-M interaction curves for multiple pile configurations and steel ratios.
    
    This function orchestrates the parallel calculation of N-M interaction curves for
    extensive pile configuration libraries. It utilizes multiprocessing to efficiently
    generate curves across multiple CPU cores, enabling rapid creation of comprehensive
    curve databases for foundation design automation workflows.

    The pre-calculation process includes:
    - Systematic generation of calculation tasks for all column-ratio combinations
    - Intelligent multiprocessing with optimal CPU core utilization
    - Progress monitoring and callback integration for user feedback
    - Efficient result aggregation into hierarchical database structure
    - Memory management for large-scale curve generation operations
    """
    # Set default steel ratios if not provided - covers typical design range
    if steel_ratios is None:
        steel_ratios = [0.005, 0.01, 0.015, 0.02, 0.025, 0.03, 0.035, 0.04]
    
    # Prepare calculation tasks for all column-ratio combinations
    tasks = []
    for column in unique_columns:
        column_key = column.generate_key()  # Generate unique identifier for column configuration
        for ratio in steel_ratios:
            # Create task tuple with deep copy to ensure thread safety
            tasks.append((deepcopy(column), ratio, column_key))
    
    # Calculate total number of tasks for progress tracking
    total_tasks = len(tasks)
    if progress_callback:
        progress_callback(f"Pre-calculating {total_tasks} NM curves...")
    
    # Determine optimal number of processes for parallel computation
    num_cpus = os.cpu_count() or 1
    num_processes = max(1, min(num_cpus - 1, 8))  # Reserve 1 CPU, cap at 8 processes
    
    # Initialize database structure and progress tracking
    nm_curves_db = {}
    completed_tasks = 0
    
    # Execute parallel calculation using multiprocessing pool
    with mp.Pool(processes=num_processes) as pool:
        # Configure progress display system
        use_tqdm = progress_callback is None  # Use terminal progress if no callback
        pbar = None
        
        if use_tqdm:
            pbar = tqdm(total=total_tasks, desc="Calculating NM curves")
        
        # Process tasks with unordered results for optimal performance
        for key, ratio, curve_df in pool.imap_unordered(_calculate_single_curve, tasks):
            # Organize results into hierarchical database structure
            if key not in nm_curves_db:
                nm_curves_db[key] = {}
            nm_curves_db[key][ratio] = curve_df
            completed_tasks += 1
            
            # Update progress displays
            if use_tqdm and pbar:
                pbar.update(1)
            
            if progress_callback:
                progress_pct = int(100 * completed_tasks / total_tasks)
                progress_callback(f"Calculating NM curves: {completed_tasks}/{total_tasks} ({progress_pct}%)")
        
        # Clean up terminal progress bar
        if pbar:
            pbar.close()
    
    # Final progress update
    if progress_callback:
        progress_callback(f"Completed {total_tasks} NM curve calculations")
    
    return nm_curves_db


def get_curve_from_db(nm_curves_db: Dict[str, Dict[float, pd.DataFrame]], 
                     column: CircularColumn, 
                     target_ratio: float) -> Optional[pd.DataFrame]:
    """
    Retrieve the most appropriate N-M interaction curve from the database for design verification.
    
    This function implements an intelligent curve retrieval system that matches pile
    configurations with pre-calculated N-M interaction curves. It utilizes sophisticated
    matching algorithms to find the optimal curve based on column properties and steel
    reinforcement requirements, with automatic interpolation and conservative selection
    strategies for safe foundation design.

    The retrieval process includes:
    - Unique column configuration identification through key generation
    - Steel ratio matching with conservative selection for structural safety
    - Automatic curve adaptation with pile marking for design integration
    """
    # Generate unique configuration key for database lookup
    key = column.generate_key()
    
    # Check if configuration exists in database
    if key not in nm_curves_db:
        return None  # Configuration not found, caller should calculate new curve
    
    # Get available steel ratios for this configuration and sort for analysis
    available_ratios = sorted(nm_curves_db[key].keys())
    
    # Implement conservative steel ratio selection strategy
    # Find ratios >= target_ratio for conservative design approach
    valid_ratios = [r for r in available_ratios if r >= target_ratio]
    
    if not valid_ratios:
        # No conservative option available, use highest available ratio
        # This provides maximum capacity while noting potential under-reinforcement
        closest_ratio = max(available_ratios)
    else:
        # Use minimum ratio >= target_ratio for conservative design
        # This ensures adequate reinforcement while minimizing over-design
        closest_ratio = min(valid_ratios)
    
    # Retrieve and customize curve for design integration
    df_curve = nm_curves_db[key][closest_ratio].copy()  # Create safe copy
    df_curve['Pile Mark'] = column.segment_mark  # Update with specific pile marking
    
    return df_curve
