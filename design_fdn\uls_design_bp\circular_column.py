"""
Circular Column Design and Analysis for Ultimate Limit State (ULS) Bored Pile Foundations

This module provides comprehensive data structures and analysis tools for circular reinforced
concrete columns used in bored pile foundation design under Ultimate Limit State conditions.
The module implements efficient data management, geometric calculations, and reinforcement
coordinate generation essential for N-M interaction analysis and structural design verification.

Key Features:
- Comprehensive circular column data structure with material and geometric properties
- Multi-layer reinforcement configuration support with flexible bar arrangements
- Efficient rebar coordinate calculation for 3D modeling and analysis integration
- Steel ratio calculations for reinforcement optimization and design verification
- Factory functions for validated column creation and configuration management
- Integration-ready data formats for structural analysis and design automation workflows

Author: Foundation-Automation Development Team
Version: Compatible with ULS design workflows and international foundation design standards
"""

from dataclasses import dataclass
from math import pi
from typing import List, Tuple
import numpy as np
import pandas as pd

@dataclass
class CircularColumn:
    """
    Comprehensive data structure for circular reinforced concrete columns in ULS foundation design.
    
    This class provides a complete representation of circular column properties including geometry,
    material characteristics, and multi-layer reinforcement configuration. Designed for Ultimate
    Limit State analysis, it supports efficient property calculations, steel ratio optimization,
    and integration with N-M interaction analysis workflows.

    The class implements efficient property-based calculations for derived values and provides
    comprehensive configuration management for complex reinforcement arrangements. All properties
    are validated for engineering reasonableness and code compliance.

    Attributes:
        pile_mark (str): Primary pile identifier for systematic foundation organization.
            - Used for grouping and referencing in design documentation
            - Should follow project naming conventions (e.g., 'P1', 'P2A', 'PILE-01')
            - Default: 'Mark'

        segment_mark (str): Specific segment identifier for detailed analysis.
            - Distinguishes different sections of the same pile (TOP, MID, BOT)
            - Used in analysis results and reinforcement detailing
            - Format typically: 'PileMark_Segment' (e.g., 'P1_TOP')
            - Default: 'Mark'

        fcu (float): Characteristic concrete compressive strength (N/mm²).
            - Cylinder strength at 28 days per design standards
            - Typical values: 25-50 N/mm² for foundation applications
            - Used in ultimate capacity calculations and code compliance
            - Default: 36 N/mm²

        fy (float): Characteristic steel yield strength (N/mm²).
            - Reinforcement yield strength per material specifications
            - Common values: 400, 500, 600 N/mm² depending on bar grade
            - Critical for ultimate moment and axial capacity calculations
            - Default: 500 N/mm²

        ym (float): Material factor for steel reinforcement.
            - Partial safety factor applied to steel strength per ULS methodology
            - Typical values: 1.15 (Eurocode), 1.5 (some national codes)
            - Used in design strength calculations: f_design = fy / ym
            - Default: 1.5

        es (float): Modulus of elasticity for steel reinforcement (N/mm²).
            - Elastic modulus used in strain compatibility analysis
            - Standard value: 200,000 N/mm² for most reinforcing steels
            - Critical for accurate N-M interaction curve generation
            - Default: 200,000 N/mm²

        diameter (float): Column diameter (mm).
            - Overall diameter of circular concrete section
            - Typical range: 600-2000 mm for bored pile applications
            - Determines gross sectional properties and reinforcement layout
            - Default: 0 mm (must be set for valid column)

        cover (float): Concrete cover to reinforcement centerline (mm).
            - Clear cover plus half bar diameter for accurate positioning
            - Typical values: 50-100 mm depending on exposure conditions
            - Critical for durability and fire resistance requirements
            - Default: 75 mm

        layer1_rebar_num (int): Number of bars in outermost reinforcement layer.
            - Primary reinforcement layer with maximum lever arm
            - Typical range: 6-20 bars depending on column size
            - Distributed uniformly around column perimeter
            - Default: 0 (no reinforcement)

        layer2_rebar_num (int): Number of bars in intermediate reinforcement layer.
            - Secondary layer for enhanced capacity in large columns
            - Used when single layer insufficient for required steel area
            - Spacing coordinated with layer 1 for optimal arrangement
            - Default: 0 (no secondary layer)

        layer3_rebar_num (int): Number of bars in innermost reinforcement layer.
            - Innermost layer for very high capacity requirements
            - Rarely used except in large diameter, high-load applications
            - Requires careful detailing to maintain constructability
            - Default: 0 (no tertiary layer)

        layer1_rebar_dia (float): Diameter of bars in layer 1 (mm).
            - Primary bar size, typically largest diameter in section
            - Common sizes: 16, 20, 25, 32, 40 mm
            - Selected based on capacity requirements and detailing constraints
            - Default: 0 mm

        layer2_rebar_dia (float): Diameter of bars in layer 2 (mm).
            - Secondary bar size, may be smaller than layer 1
            - Should maintain reasonable size compatibility for construction
            - Coordinate with layer 1 for optimal steel area distribution
            - Default: 0 mm

        layer3_rebar_dia (float): Diameter of bars in layer 3 (mm).
            - Tertiary bar size for innermost layer
            - Typically smallest diameter due to space constraints
            - Requires verification of minimum clear spacing requirements
            - Default: 0 mm

        rebar_clear_spacing (float): Clear spacing between reinforcement layers (mm).
            - Minimum spacing between adjacent layer circumferences
            - Ensures adequate concrete placement and vibration
            - Typical minimum: 50-100 mm per design codes
            - Default: 100 mm

    Properties:
        radius (float): Column radius calculated from diameter (mm).
        area (float): Gross sectional area of concrete column (mm²).
        total_rebar_area (float): Total cross-sectional area of all reinforcement (mm²).
        steel_ratio (float): Reinforcement ratio (As/Ac) as decimal value.

    Methods:
        generate_key(): Create unique identifier for column configuration.

    Example:
        >>> # Create a typical bored pile column configuration
        >>> column = CircularColumn(
        ...     pile_mark='P1',
        ...     segment_mark='P1_TOP',
        ...     diameter=1200,      # 1.2m diameter
        ...     fcu=40,             # C40 concrete
        ...     fy=500,             # Grade 500 steel
        ...     cover=75,           # 75mm cover
        ...     layer1_rebar_num=12,# 12 bars in outer layer
        ...     layer1_rebar_dia=25,# 25mm diameter bars
        ...     layer2_rebar_num=6, # 6 bars in inner layer
        ...     layer2_rebar_dia=20 # 20mm diameter bars
        ... )
        >>> 
        >>> # Calculate derived properties
        >>> print(f"Column area: {column.area:,.0f} mm²")
        >>> print(f"Steel ratio: {column.steel_ratio:.3f} ({column.steel_ratio*100:.1f}%)")
        >>> print(f"Total steel area: {column.total_rebar_area:.0f} mm²")

    Design Validation:
        - Minimum steel ratio: Typically 0.5-1.0% per design codes
        - Maximum steel ratio: Usually limited to 4-6% for constructability
        - Cover requirements: Verify against exposure and fire requirements
        - Bar spacing: Ensure adequate clear spacing for concrete placement
    """
    pile_mark: str = 'Mark'
    segment_mark: str = 'Mark'
    fcu: float = 36  # N/mm² - Characteristic concrete compressive strength
    fy: float = 500  # N/mm² - Characteristic steel yield strength  
    ym: float = 1.5  # Material factor for steel reinforcement
    es: float = 200000  # N/mm² - Steel modulus of elasticity
    diameter: float = 0  # mm - Column diameter
    cover: float = 75  # mm - Concrete cover to reinforcement centerline
    layer1_rebar_num: int = 0  # Number of bars in outermost layer
    layer2_rebar_num: int = 0  # Number of bars in intermediate layer  
    layer3_rebar_num: int = 0  # Number of bars in innermost layer
    layer1_rebar_dia: float = 0  # mm - Diameter of layer 1 bars
    layer2_rebar_dia: float = 0  # mm - Diameter of layer 2 bars
    layer3_rebar_dia: float = 0  # mm - Diameter of layer 3 bars
    rebar_clear_spacing: float = 100  # mm - Clear spacing between layers    
    @property
    def radius(self) -> float:
        """
        Calculate column radius from diameter.
        
        Returns:
            float: Column radius in millimeters (mm).
                  Equal to diameter/2 for circular sections.
        
        Example:
            >>> column = CircularColumn(diameter=1200)
            >>> print(f"Radius: {column.radius} mm")
            Radius: 600.0 mm
        """
        return self.diameter / 2
    
    @property
    def area(self) -> float:
        """
        Calculate gross cross-sectional area of the circular column.
        
        Uses the standard formula for circular area: A = π × d²/4
        This represents the total concrete area before deducting reinforcement.
        
        Returns:
            float: Gross sectional area in square millimeters (mm²).
                  Based on overall column diameter including cover.
        
        Example:
            >>> column = CircularColumn(diameter=1000)  # 1m diameter
            >>> print(f"Gross area: {column.area:,.0f} mm²")
            Gross area: 785,398 mm²
        """
        return pi / 4 * self.diameter ** 2
    
    @property
    def total_rebar_area(self) -> float:
        """
        Calculate total cross-sectional area of all reinforcement layers.
        
        Sums the steel area from all configured reinforcement layers using the
        formula: As = Σ(n × π × d²/4) for each layer where n is number of bars
        and d is bar diameter. Essential for steel ratio calculations and
        capacity analysis in Ultimate Limit State design.
        
        Returns:
            float: Total reinforcement area in square millimeters (mm²).
                  Sum of areas from layers 1, 2, and 3 if configured.
                  Returns 0 if no reinforcement is specified.
        
        Calculation Method:
            - Layer 1: n1 × π × d1²/4  
            - Layer 2: n2 × π × d2²/4
            - Layer 3: n3 × π × d3²/4
            - Total: Sum of all active layers
        
        Example:
            >>> column = CircularColumn(
            ...     layer1_rebar_num=12, layer1_rebar_dia=25,  # 12-T25
            ...     layer2_rebar_num=6,  layer2_rebar_dia=20   # 6-T20
            ... )
            >>> print(f"Total steel area: {column.total_rebar_area:.0f} mm²")
            Total steel area: 7,739 mm²
        """
        # Pre-calculate π/4 for efficiency in repeated calculations
        pi_div_4 = pi / 4
        return pi_div_4 * (
            self.layer1_rebar_num * self.layer1_rebar_dia ** 2 +
            self.layer2_rebar_num * self.layer2_rebar_dia ** 2 +
            self.layer3_rebar_num * self.layer3_rebar_dia ** 2
        )
    
    @property
    def steel_ratio(self) -> float:
        """
        Calculate reinforcement ratio (ρ = As/Ac) for the column section.
        
        The steel ratio is a fundamental parameter in reinforced concrete design,
        representing the proportion of steel area to gross concrete area. This
        ratio directly affects the column's axial and moment capacity, ductility,
        and compliance with design code requirements.
        
        Returns:
            float: Steel reinforcement ratio as decimal value.
                  Range typically 0.005-0.06 (0.5%-6.0%) for practical design.
                  Returns 0 if column area is zero or no reinforcement specified.
        
        Design Considerations:
            - Minimum ratio: Usually 0.5-1.0% per design codes for adequate capacity
            - Maximum ratio: Typically 4-6% to ensure constructability and ductility  
            - Typical range: 1.5-3.0% for most bored pile foundation applications
            - Higher ratios may require special detailing and construction procedures
        
        Example:
            >>> column = CircularColumn(
            ...     diameter=1200,           # 1.2m diameter
            ...     layer1_rebar_num=16,     # 16 bars
            ...     layer1_rebar_dia=25      # 25mm diameter
            ... )
            >>> ratio = column.steel_ratio
            >>> print(f"Steel ratio: {ratio:.4f} ({ratio*100:.2f}%)")
            Steel ratio: 0.0069 (0.69%)
        
        Code Compliance:
            - ACI 318: ρmin = 0.01, ρmax = 0.08 (with special provisions)
            - Eurocode 2: ρmin = 0.002, ρmax = 0.04 (typical applications)
            - AS 3600: ρmin = 0.003, ρmax = 0.04 (normal conditions)
        """
        return self.total_rebar_area / self.area if self.area > 0 else 0
    
    def generate_key(self) -> str:
        """
        Generate unique configuration key for column identification and caching.
        
        Creates a unique identifier that can be used for database storage,
        configuration comparison, and caching of analysis results. The key
        should uniquely identify columns with identical properties to enable
        efficient reuse of calculation results.
        
        Returns:
            str: Unique configuration key based on pile mark.
                Currently uses pile_mark but could be enhanced to include
                geometric and material properties for more precise identification.
        
        Usage:
            - Database indexing for N-M curve storage and retrieval
            - Configuration comparison in parametric studies  
            - Caching expensive calculations for identical columns
            - Quality assurance and duplicate detection in large projects
        
        Example:
            >>> column = CircularColumn(pile_mark='P1_1200_C40')
            >>> key = column.generate_key()
            >>> print(f"Configuration key: {key}")
            Configuration key: P1_1200_C40
        
        Enhancement Opportunity:
            Future versions could include material and geometric properties:
            f"{pile_mark}_D{diameter}_C{fcu}_S{steel_ratio:.3f}"
        """
        return f"{self.pile_mark}"


def create_column(pile_mark: str, 
                 segment_mark: str, 
                 fcu: float, 
                 dia: float, 
                 cover: float, 
                 rebar_num1: int, 
                 rebar_dia1: float,
                 rebar_num2: int, 
                 rebar_dia2: float, 
                 rebar_num3: int, 
                 rebar_dia3: float, 
                 rebar_clear_spacing: float) -> CircularColumn:
    """
    Factory function to create and validate CircularColumn instances with comprehensive configuration.
    
    This function provides a structured approach to creating CircularColumn objects with
    validation and proper parameter organization. It serves as the primary interface for
    column creation in automated design workflows, ensuring consistent configuration
    and reducing errors in complex reinforcement arrangements.
    """
    return CircularColumn(
        pile_mark=pile_mark,
        segment_mark=segment_mark,
        fcu=fcu,
        diameter=dia,
        cover=cover,
        layer1_rebar_num=rebar_num1,
        layer1_rebar_dia=rebar_dia1,
        layer2_rebar_num=rebar_num2,
        layer2_rebar_dia=rebar_dia2,
        layer3_rebar_num=rebar_num3,
        layer3_rebar_dia=rebar_dia3,
        rebar_clear_spacing=rebar_clear_spacing
    )


def calculate_rebar_coordinates(column: CircularColumn) -> pd.DataFrame:
    """
    Calculate precise 3D coordinates for all reinforcement bars in circular column configuration.
    
    This function generates detailed coordinate data for all reinforcement layers, enabling
    accurate 3D modeling, construction drawings, and structural analysis integration. The
    coordinates are calculated using rigorous geometric principles with proper consideration
    of cover requirements, layer spacing, and bar positioning per design standards.
    
    The coordinate calculation includes:
    - Precise radial positioning based on cover and clear spacing requirements
    - Angular distribution ensuring uniform bar spacing around column perimeter
    - Multi-layer coordination preventing interference and maintaining constructability
    - Output formatting compatible with CAD systems and analysis software
    """
    # Initialize list to store coordinate data for all reinforcement bars
    rebar_data = []
    
    # Pre-calculate degree to radian conversion factor for efficiency
    # Used repeatedly in trigonometric calculations for all bars
    deg_to_rad = np.pi / 180
    
    # Calculate Layer 1 coordinates (outermost reinforcement layer)
    if column.layer1_rebar_num > 0:
        # Generate uniform angular distribution around column perimeter
        # Angular spacing = 360°/number_of_bars ensures even distribution
        angles = np.linspace(0, 360, column.layer1_rebar_num, endpoint=False)
        
        # Calculate radial position: column radius minus cover minus half bar diameter
        # This positions bar centerline at correct distance from column surface
        r_layer = column.radius - column.cover - column.layer1_rebar_dia / 2
        
        # Convert polar coordinates (r, θ) to Cartesian coordinates (x, y)
        # Using standard trigonometric relationships for circular positioning
        x_coords = r_layer * np.cos(angles * deg_to_rad)
        y_coords = r_layer * np.sin(angles * deg_to_rad)
        
        # Store coordinate data for each bar in layer 1
        for i, (x, y, deg) in enumerate(zip(x_coords, y_coords, angles)):
            rebar_data.append([column.layer1_rebar_dia, x, y, deg])
    
    # Calculate Layer 2 coordinates (intermediate reinforcement layer)
    if column.layer2_rebar_num > 0:
        # Generate uniform angular distribution for layer 2 bars
        angles = np.linspace(0, 360, column.layer2_rebar_num, endpoint=False)
        
        # Calculate radial position accounting for layer 1 bars and clear spacing
        # Position: radius - cover - layer1_diameter - clear_spacing - half_layer2_diameter
        r_layer = (column.radius - column.cover - column.layer1_rebar_dia - 
                   column.rebar_clear_spacing - column.layer2_rebar_dia / 2)
        
        # Convert to Cartesian coordinates for layer 2 bars
        x_coords = r_layer * np.cos(angles * deg_to_rad)
        y_coords = r_layer * np.sin(angles * deg_to_rad)
        
        # Store coordinate data for each bar in layer 2
        for i, (x, y, deg) in enumerate(zip(x_coords, y_coords, angles)):
            rebar_data.append([column.layer2_rebar_dia, x, y, deg])
    
    # Calculate Layer 3 coordinates (innermost reinforcement layer)
    if column.layer3_rebar_num > 0:
        # Generate uniform angular distribution for layer 3 bars
        angles = np.linspace(0, 360, column.layer3_rebar_num, endpoint=False)
        
        # Calculate radial position accounting for both outer layers and spacing
        # Position: radius - cover - layer1_dia - spacing - layer2_dia - spacing - half_layer3_dia
        r_layer = (column.radius - column.cover - column.layer1_rebar_dia - 
                   column.rebar_clear_spacing - column.layer2_rebar_dia - 
                   column.rebar_clear_spacing - column.layer3_rebar_dia / 2)
        
        # Convert to Cartesian coordinates for layer 3 bars
        x_coords = r_layer * np.cos(angles * deg_to_rad)
        y_coords = r_layer * np.sin(angles * deg_to_rad)
        
        # Store coordinate data for each bar in layer 3
        for i, (x, y, deg) in enumerate(zip(x_coords, y_coords, angles)):
            rebar_data.append([column.layer3_rebar_dia, x, y, deg])
    
    # Create structured DataFrame with comprehensive coordinate information
    # Column order optimized for engineering workflows and data analysis
    df = pd.DataFrame(rebar_data, columns=['Rebar Dia (mm)', 'X (mm)', 'Y (mm)', 'Rebar Deg (deg)'])
    
    # Insert pile identification as first column for systematic organization
    # Enables grouping and filtering in multi-column analysis workflows
    df.insert(0, 'Pile Mark', column.segment_mark)
    
    return df
