# Core Architecture Modules

## Main Application Structure
- **`main.py`**: Entry point with DEBUG_MODE configuration
- **`main_class.py`**: Core data classes (ExcelInputs, ExcelOutputs, SafeMdbs, FilePaths, DesignResults)
- **`app_controller.py`**: Main ApplicationController class for orchestrating the application
- **`__init__.py`**: Package initialization with version info and module management

## Configuration Management (`config/`)
- **`app_config.py`**: Central configuration with constants for:
  - Authentication settings (MAX_LOGIN_ATTEMPTS, LOGIN_TIMEOUT_MINUTES)
  - Email configuration (EMAIL_SENDER, EMAIL_PASSWORD, EMAIL_DOMAIN)
  - SSL settings (SSL_VERIFY_REQUESTS, SSL_STRICT_SMTP)
  - User authorization URLs (BASE_USER_URL, ULTIMATE_USER_URL)
  - File paths and application metadata
- **`ssl_config.py`**: SSL context and requests SSL configuration functions

## Authentication System (`auth/`)
- **`security_manager.py`**: Complete SecurityManager class handling:
  - Session token generation and verification
  - Password hashing with salt
  - Failed login attempt tracking and account lockout
  - User authorization from Google Drive URLs
  - Security logging and session management

## User Interface (`ui/`)
- **`components.py`**: Base UI components (BaseFrame, HeaderFrame, TimerLabel, menu creation)
- **`login_frame.py`**: LoginFrame for user authentication
- **`main_menu_frame.py`**: MainMenuFrame for application navigation
- **`version_selection_frame.py`**: VersionSelectionFrame for software version selection