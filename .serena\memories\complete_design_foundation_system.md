# Complete Design Foundation System

## Foundation Design Core (`design_fdn/`)

### Main Design Engine (`designer_main.py`)
- **Complete Foundation Design Workflow**: End-to-end design automation
- **Multi-pile Type Support**: Comprehensive design for BP, SHP, DHP, MP pile types
- **SAFE Integration**: Direct result processing from SAFE structural analysis
- **Design Optimization**: Iterative refinement and section optimization capabilities
- **Code Compliance**: International foundation design codes (Eurocode 7, AASHTO, ACI 318, AISC 360)

### Design Components and GUI
- **`designer_gui.py`**: Foundation Design Checker GUI with interactive interface
- **`data_processing.py`**: Advanced design data processing and optimization
  - `_process_pile_sls_batch()` for efficient SLS load combination processing
  - `cal_pile_sls()` with multi-processing optimization for large-scale analysis
  - Batch processing capabilities with comprehensive progress tracking
- **`initialization.py`**: Design system initialization with comprehensive setup
- **`error_handling.py`**: Design-specific error management and recovery

### Pile Capacity Analysis (`pile_capacity.py`)
- **Multi-pile Type Calculations**: Specialized algorithms for each pile type
  - **Bored Piles (BP)**: Reinforced concrete capacity with N-M-V interaction
  - **Socket H-Piles (SHP)**: Steel pile capacity with socket connection design
  - **Driven H-Piles (DHP)**: Driven steel pile capacity with drivability analysis
  - **Mini Piles (MP)**: Small diameter pile capacity with specialized procedures

### Advanced Capacity Calculations
- **Rock Parameter Integration**: 
  - GEO TGN 53 standards for Hong Kong geotechnical practice
  - COP 2017 standards for alternative design approaches
  - Dynamic parameter selection based on user preferences
- **Friction Capacity Calculations**: Different soil/rock conditions with validated algorithms
- **Steel and Concrete Verification**: International code compliance (AISC 360, ACI 318)
- **Material Constants and Factors**: Engineering-validated design factors and coefficients

## Ultimate Limit State (ULS) Design

### Bored Pile ULS Design (`uls_design_bp/`)
- **`uls_bp_nm.py`**: N-M interaction analysis with advanced algorithms
- **`uls_bp_v.py`**: Shear capacity verification and design optimization
- **`rebar_design.py`**: Reinforcement design optimization with multiple layers
- **`circular_column.py`**: Circular column design with moment-axial interaction
- **`group_bp.py`**: Pile group analysis and design verification
- **`nm_curve_calc.py`**: N-M interaction curve generation and validation
- **`nm_database.py`**: N-M interaction database management and storage
- **`nm_validation.py`**: N-M interaction validation and quality assurance
- **`nm_visualization.py`**: Professional N-M interaction visualization
- **`prokon_c13.py`**: Prokon software integration for advanced analysis

### H-Pile ULS Design (`uls_design_hp/`)
- **`uls_hp_nmv.py`**: N-M-V interaction analysis for steel H-piles
- **`steel_design.py`**: Comprehensive steel design verification
  - AISC 360 compliance for steel design standards
  - BD (British Design) standard implementation
  - ASD (Allowable Stress Design) methodology
  - Advanced stress checks and capacity verification

## Serviceability Limit State (SLS) Design

### SLS Analysis Modules (`sls_design/`)
- **`settlement.py`**: Settlement analysis and prediction algorithms
  - Immediate settlement calculations
  - Consolidation settlement analysis
  - Long-term settlement prediction with time-dependent factors
- **`deflection.py`**: Deflection calculations and serviceability limits
  - Lateral deflection analysis under working loads
  - Deflection limits verification per design codes
  - Dynamic deflection analysis for special conditions
- **`diff_settlement.py`**: Differential settlement analysis
  - Relative settlement calculations between structural elements
  - Differential settlement limits verification
  - Tilt and angular distortion analysis
- **`angular_rotation.py`**: Angular rotation and tilt analysis
  - Foundation rotation under eccentric loading
  - Tilt limits verification per structural requirements
  - Dynamic rotation analysis for seismic conditions

## Design Data Management

### Core Data Systems
- **`piling_schedule.py`**: Comprehensive piling schedule generation
  - Pile type scheduling and sequencing
  - Construction methodology integration
  - Quality control and inspection schedules
- **`read.py`**: Design data reading and validation systems
  - Multi-source data integration capabilities
  - Data consistency checking and validation
  - Error detection and correction procedures

### Configuration and Standards
- **`design_fdn_config.py`**: Design configuration management
  - Sheet naming conventions for Excel integration
  - Design standard specifications and parameters
  - Code compliance requirements and verification criteria

## Integration and Quality Assurance

### Professional Features
- **Multi-objective Optimization**: Cost, performance, and constructability
- **Design Verification**: Comprehensive checking against multiple design codes
- **Quality Control**: Multi-level validation and verification procedures
- **Documentation**: Professional design reports and construction drawings
- **Database Integration**: SQLite storage for design results and analysis history

### Code Compliance Standards
- **International Codes**: Eurocode 7, AASHTO LRFD, ACI 318, AISC 360
- **Local Standards**: Hong Kong geotechnical practice (GEO TGN 53)
- **Alternative Standards**: COP 2017 and other regional standards
- **Custom Standards**: User-defined design criteria and safety factors

This comprehensive foundation design system provides professional-grade analysis and design capabilities for all major pile foundation types with full code compliance and optimization features.