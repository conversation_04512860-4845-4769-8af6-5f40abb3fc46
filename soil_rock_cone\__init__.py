"""
3D Pile Volume Analysis Package

This package provides comprehensive tools for analyzing pile systems with soil cylinder,
soil frustum, and rock frustum interaction volumes. It handles overlapping geometries
and provides detailed volume calculations for foundation engineering applications.

Main Components:
- DataProcessor: Excel data reading and validation
- GeometryEngine: 3D geometry creation using Trimesh
- BoundaryClipper: Site boundary processing with Shapely
- VolumeCalculator: Volume calculations and distribution
- OverlapAnalyzer: Overlap detection and processing
- ReportGenerator: Report generation and CSV export
- Visualizer: 3D visualization with PyVista
- MainAnalyzer: Main orchestration API

Example Usage:
    from soil_rock_cone import MainAnalyzer
    
    analyzer = MainAnalyzer()
    results = analyzer.analyze_pile_volumes(
        excel_file='data.xlsx',
        site_boundary_coords=[(0, 0), (100, 0), (100, 100), (0, 100)]
    )
"""

from .main_analyzer import MainAnalyzer
from .data_processor import DataProcessor
from .geometry_engine import GeometryEngine
from .boundary_clipper import BoundaryClipper
from .volume_calculator import VolumeCalculator
from .overlap_analyzer import OverlapAnalyzer
from .report_generator import ReportGenerator
from .visualizer import Visualizer

__version__ = "1.0.0"
__author__ = "Foundation Automation Team"

__all__ = [
    "MainAnalyzer",
    "DataProcessor", 
    "GeometryEngine",
    "BoundaryClipper",
    "VolumeCalculator",
    "OverlapAnalyzer", 
    "ReportGenerator",
    "Visualizer"
]
