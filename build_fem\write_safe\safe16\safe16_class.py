from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, Any

import pandas as pd


class Safe16DataFrames:
    """
    A class to hold and initialize various pandas DataFrames that represent
    tables and data structures from SAFE 16 software.

    The class organizes the creation of these DataFrames into logical groups,
    each handled by a separate initialization method, promoting clarity and
    maintainability. A helper method is used to standardize DataFrame creation.
    """

    def _create_dataframe(
            self,
            column_tuples: List[Tuple[str, str, str]],
            initial_data: Optional[List[Any]] = None
    ) -> pd.DataFrame:
        """
        Helper method to create a Pandas DataFrame with a MultiIndex.
        """
        if initial_data is None:
            initial_data = []
        multi_index_cols = pd.MultiIndex.from_tuples(column_tuples)
        return pd.DataFrame(initial_data, columns=multi_index_cols)

    def __init__(self) -> None:
        """
        Initializes all DataFrame attributes by calling specialized private methods
        for different categories of data tables.
        """
        self._initialize_program_definition_tables()
        self._initialize_object_geometry_tables()
        self._initialize_structural_definition_tables()
        self._initialize_load_definition_tables()
        self._initialize_group_tables()
        self._initialize_object_property_assignment_tables()
        self._initialize_load_to_object_assignment_tables()
        self._initialize_analysis_mesh_object_tables()
        self._initialize_design_parameter_tables()

    def _initialize_program_definition_tables(self) -> None:
        """Initializes DataFrames for program control, project information, and global settings."""
        # Program Control
        program_control_cols = [
            ('TABLE:  Program Control', 'ProgramName',
             'Text'), ('TABLE:  Program Control', 'Version', 'Text'),
            ('TABLE:  Program Control', 'ProgLevel',
             'Text'), ('TABLE:  Program Control', 'LicenseNum', 'Text'),
            ('TABLE:  Program Control', 'CurrUnits',
             'Text'), ('TABLE:  Program Control', 'MergeTol', 'm'),
            ('TABLE:  Program Control', 'ModelDatum',
             'm'), ('TABLE:  Program Control', 'StHtAbove', 'm'),
            ('TABLE:  Program Control', 'StHtBelow',
             'm'), ('TABLE:  Program Control', 'ConcCode', 'Text')
        ]
        program_control_data = [('SAFE', '12.3.1', 'Post Tensioning', 'Trial',
                                 'KN, m, C', '0.001', '0', '0', '3', 'Hong Kong CP-2004')]
        self.ProgramControl = self._create_dataframe(
            program_control_cols, program_control_data)

        # Mass Source
        mass_source_cols = [
            ('TABLE:  Mass Source', 'LoadPat', 'Text'),
            ('TABLE:  Mass Source', 'Multiplier', 'Text')
        ]
        mass_source_data = [('DL', 1)]
        self.MassSource = self._create_dataframe(
            mass_source_cols, mass_source_data)

        # Project Information
        project_information_cols = [
            ('TABLE:  Project Information', 'Item', 'Text'),
            ('TABLE:  Project Information', 'Data', 'Text')
        ]
        project_information_data = [
            ('Client Name', None), ('Project Name', None), ('Project Number', None),
            ('Company Name', None), ('Engineer', None), ('Checker', None),
            ('Supervisor', None), ('Model Name',
                                   None), ('Model Description', None),
            ('Revision Number', None), ('Issue Number', None)
        ]
        self.ProjectInformation = self._create_dataframe(
            project_information_cols, project_information_data)

        # Automatic Slab Mesh Options
        automatic_slab_mesh_options_cols = [
            ('TABLE:  Automatic Slab Mesh Options', 'MeshOpt', 'Text'),
            ('TABLE:  Automatic Slab Mesh Options', 'Localize', 'Yes/No'),
            ('TABLE:  Automatic Slab Mesh Options', 'Merge', 'Yes/No'),
            ('TABLE:  Automatic Slab Mesh Options', 'MaxSize', 'm')
        ]
        automatic_slab_mesh_options_data = [('Rectangular', 'No', 'No', 1)]
        self.AutomaticSlabMeshOptions = self._create_dataframe(
            automatic_slab_mesh_options_cols, automatic_slab_mesh_options_data)

        # Advanced SapFire Options
        advanced_sap_fire_options_cols = [
            ('TABLE:  Advanced SapFire Options', 'SolverOpt', 'Text'),
            ('TABLE:  Advanced SapFire Options', 'ProcessOpt', 'Text'),
            ('TABLE:  Advanced SapFire Options', 'Always32Bit', 'Yes/No')
        ]
        advanced_sap_fire_options_data = [('Advanced', 'Auto', 'No')]
        self.AdvancedSapFireOptions = self._create_dataframe(
            advanced_sap_fire_options_cols, advanced_sap_fire_options_data)

        # Advanced Modeling Options
        advanced_modeling_options_cols = [
            ('TABLE:  Advanced Modeling Options', '2DOnly', 'Yes/No'),
            ('TABLE:  Advanced Modeling Options', 'RigDiaTop', 'Yes/No'),
            ('TABLE:  Advanced Modeling Options', 'NoOffsets', 'Yes/No')
        ]
        advanced_modeling_options_data = [('No', 'No', 'Yes')]
        self.AdvancedModelingOptions = self._create_dataframe(
            advanced_modeling_options_cols, advanced_modeling_options_data)

        # Cracking Analysis Reinforcement
        cracking_analysis_reinforcement_cols = [
            ('TABLE:  Cracking Analysis Reinforcement', 'ReinfOpt', 'Text'),
            ('TABLE:  Cracking Analysis Reinforcement', 'MinReinfT', 'Unitless'),
            ('TABLE:  Cracking Analysis Reinforcement', 'MinReinfC', 'Unitless'),
            ('TABLE:  Cracking Analysis Reinforcement', 'UserModRup', 'Yes/No')
        ]
        cracking_analysis_reinforcement_data = [('FE Design', 0.0018, 0, 'No')]
        self.CrackingAnalysisReinforcement = self._create_dataframe(
            cracking_analysis_reinforcement_cols, cracking_analysis_reinforcement_data)

    def _initialize_object_geometry_tables(self) -> None:
        """Initializes DataFrames for defining the geometry of structural objects."""
        # Obj Geom - Point Coordinates
        obj_geom_point_coordinates_cols = [
            ('TABLE:  Object Geometry - Point Coordinates', 'Point', 'Text'),
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalX', 'm'),
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalY', 'm'),
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalZ', 'm'),
            ('TABLE:  Object Geometry - Point Coordinates', 'SpecialPt', 'Yes/No')
        ]
        self.ObjGeomPointCoordinates = self._create_dataframe(
            obj_geom_point_coordinates_cols)

        # Obj Geom - Lines 01 - General
        obj_geom_lines01_general_cols = [
            ('TABLE:  Object Geometry - Lines 01 - General', 'Line', 'Text'),
            ('TABLE:  Object Geometry - Lines 01 - General', 'PointI', 'Text'),
            ('TABLE:  Object Geometry - Lines 01 - General', 'PointJ', 'Text'),
            ('TABLE:  Object Geometry - Lines 01 - General', 'LineType', 'Text'),
            ('TABLE:  Object Geometry - Lines 01 - General', 'Length', 'm')
        ]
        self.ObjGeomLines01General = self._create_dataframe(
            obj_geom_lines01_general_cols)

        # Obj Geom - Lines 02 - Curved Bm
        obj_geom_lines02_curved_bm_cols = [
            ('TABLE:  Object Geometry - Lines 02 - Curved Beams', 'Line', 'Text'),
            ('TABLE:  Object Geometry - Lines 02 - Curved Beams', 'CurveType', 'Text'),
            ('TABLE:  Object Geometry - Lines 02 - Curved Beams',
             'ICPNumber', 'Unitless'),
            ('TABLE:  Object Geometry - Lines 02 - Curved Beams', 'GX', 'm'),
            ('TABLE:  Object Geometry - Lines 02 - Curved Beams', 'GY', 'm')
        ]
        self.ObjGeomLines02CurvedBm = self._create_dataframe(
            obj_geom_lines02_curved_bm_cols)

        # Obj Geom - Areas 01 - General
        obj_geom_areas01_general_cols = [
            ('TABLE:  Object Geometry - Areas 01 - General', 'Area', 'Text'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'NumPoints', 'Unitless'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'Point1', 'Text'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'Point2', 'Text'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'Point3', 'Text'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'Point4', 'Text'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'Auto', 'Yes/No'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'TotalArea', 'm2'),
            ('TABLE:  Object Geometry - Areas 01 - General', 'AreaType', 'Text')
        ]
        self.ObjGeomAreas01General = self._create_dataframe(
            obj_geom_areas01_general_cols)

        # Obj Geom - Areas 02 - Curved Sl
        obj_geom_areas02_curved_sl_cols = [
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges', 'Area', 'Text'),
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges',
             'EdgeNum', 'Unitless'),
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges', 'CurveType', 'Text'),
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges',
             'ICPNumber', 'Unitless'),
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges', 'GX', 'm'),
            ('TABLE:  Object Geometry - Areas 02 - Curved Slab Edges', 'GY', 'm')
        ]
        self.ObjGeomAreas02CurvedSl = self._create_dataframe(
            obj_geom_areas02_curved_sl_cols)

        # Obj Geom - Areas 03 - Curved Wl
        obj_geom_areas03_curved_wl_cols = [
            ('TABLE:  Object Geometry - Areas 03 - Curved Walls', 'Area', 'Text'),
            ('TABLE:  Object Geometry - Areas 03 - Curved Walls', 'CurveType', 'Text'),
            ('TABLE:  Object Geometry - Areas 03 - Curved Walls',
             'ICPNumber', 'Unitless'),
            ('TABLE:  Object Geometry - Areas 03 - Curved Walls', 'GX', 'm'),
            ('TABLE:  Object Geometry - Areas 03 - Curved Walls', 'GY', 'm')
        ]
        self.ObjGeomAreas03CurvedWl = self._create_dataframe(
            obj_geom_areas03_curved_wl_cols)

        # Obj Geom - Areas 04 - Wall Pnls
        obj_geom_areas04_wall_pnls_cols = [
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'Panel', 'Text'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels',
             'NumPoints', 'Unitless'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalX1', 'm'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalY1', 'm'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalX2', 'm'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalY2', 'm'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalX3', 'm'),
            ('TABLE:  Object Geometry - Areas 04 - Wall Panels', 'GlobalY3', 'm')
        ]
        self.ObjGeomAreas04WallPnls = self._create_dataframe(
            obj_geom_areas04_wall_pnls_cols)

        # Obj Geom - Tendons 01 - General
        obj_geom_tendons01_general_cols = [
            ('TABLE:  Object Geometry - Tendons 01 - General', 'Tendon', 'Text'),
            ('TABLE:  Object Geometry - Tendons 01 - General', 'NumPoints', 'Unitless'),
            ('TABLE:  Object Geometry - Tendons 01 - General', 'Point1', 'Text'),
            ('TABLE:  Object Geometry - Tendons 01 - General', 'Point2', 'Text')
        ]
        self.ObjGeomTendons01General = self._create_dataframe(
            obj_geom_tendons01_general_cols)

        # Obj Geom - Tendons 02 - HLayout
        obj_geom_tendons02_h_layout_cols = [
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout',
             'Tendon', 'Text'),
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout',
             'Segment', 'Unitless'),
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout',
             'CurveType', 'Text'),
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout',
             'ICPNumber', 'Unitless'),
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout', 'GX', 'm'),
            ('TABLE:  Object Geometry - Tendons 02 - Curved Horizontal Layout', 'GY', 'm')
        ]
        self.ObjGeomTendons02HLayout = self._create_dataframe(
            obj_geom_tendons02_h_layout_cols)

        # Obj Geom - Tendons 03 - Vert Pr
        obj_geom_tendons03_vert_pr_cols = [
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile', 'Tendon', 'Text'),
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile',
             'SpanLabel', 'Text'),
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile', 'SpanType', 'Text'),
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile', 'L', 'm'),
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile', 'ZL', 'm'),
            ('TABLE:  Object Geometry - Tendons 03 - Vertical Profile', 'ZR', 'm')
        ]
        self.ObjGeomTendons03VertPr = self._create_dataframe(
            obj_geom_tendons03_vert_pr_cols)

        # Obj Geom - Tendons 04 - Disc Pt
        obj_geom_tendons04_disc_pt_cols = [
            ('TABLE:  Object Geometry - Tendons 04 - Discretized Points', 'Tendon', 'Text'),
            ('TABLE:  Object Geometry - Tendons 04 - Discretized Points',
             'PointNum', 'Text'),
            ('TABLE:  Object Geometry - Tendons 04 - Discretized Points', 'GlobalX', 'm'),
            ('TABLE:  Object Geometry - Tendons 04 - Discretized Points', 'GlobalY', 'm'),
            ('TABLE:  Object Geometry - Tendons 04 - Discretized Points', 'GlobalZ', 'm')
        ]
        self.ObjGeomTendons04DiscPt = self._create_dataframe(
            obj_geom_tendons04_disc_pt_cols)

        # Obj Geom - Tendons 05 - Support
        obj_geom_tendons05_support_cols = [
            ('TABLE:  Object Geometry - Tendons 05 - Support Points', 'Tendon', 'Text'),
            ('TABLE:  Object Geometry - Tendons 05 - Support Points', 'GlobalX', 'm'),
            ('TABLE:  Object Geometry - Tendons 05 - Support Points', 'GlobalY', 'm')
        ]
        self.ObjGeomTendons05Support = self._create_dataframe(
            obj_geom_tendons05_support_cols)

        # Object Geometry - Slab Rebar
        object_geometry_slab_rebar_cols = [
            ('TABLE:  Object Geometry - Slab Rebar', 'SlabRebar', 'Text'),
            ('TABLE:  Object Geometry - Slab Rebar', 'GlobalX1', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'GlobalY1', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'GlobalX2', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'GlobalY2', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'OffsetVert', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'WidthLeft', 'm'),
            ('TABLE:  Object Geometry - Slab Rebar', 'WidthRight', 'm')
        ]
        self.ObjectGeometrySlabRebar = self._create_dataframe(
            object_geometry_slab_rebar_cols)

        # Obj Geom - Design Strips
        obj_geom_design_strips_cols = [
            ('TABLE:  Object Geometry - Design Strips', 'Strip', 'Text'),
            ('TABLE:  Object Geometry - Design Strips', 'Point', 'Text'),
            ('TABLE:  Object Geometry - Design Strips', 'GlobalX', 'm'),
            ('TABLE:  Object Geometry - Design Strips', 'GlobalY', 'm'),
            ('TABLE:  Object Geometry - Design Strips', 'WBLeft', 'm'),
            ('TABLE:  Object Geometry - Design Strips', 'WBRight', 'm'),
            ('TABLE:  Object Geometry - Design Strips', 'WALeft', 'm'),
            ('TABLE:  Object Geometry - Design Strips', 'WARight', 'm')
        ]
        self.ObjGeomDesignStrips = self._create_dataframe(
            obj_geom_design_strips_cols)

        # Object Geometry - Dimension Lns
        object_geometry_dimension_lns_cols = [
            ('TABLE:  Object Geometry - Dimension Lines', 'AlignType', 'Text'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalX1', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalY1', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalX2', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalY2', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalX3', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalY3', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalX4', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'GlobalY4', 'm'),
            ('TABLE:  Object Geometry - Dimension Lines', 'Length', 'm')
        ]
        self.ObjectGeometryDimensionLns = self._create_dataframe(
            object_geometry_dimension_lns_cols)

    def _initialize_structural_definition_tables(self) -> None:
        """Initializes DataFrames for defining structural properties like materials, sections, etc."""
        # Coordinate Systems
        coordinate_systems_cols = [
            ('TABLE:  Coordinate Systems', 'CoordSys', 'Text'),
            ('TABLE:  Coordinate Systems', 'Type', 'Text'),
            ('TABLE:  Coordinate Systems', 'OriginGx', 'm'),
            ('TABLE:  Coordinate Systems', 'OriginGy', 'm'),
            ('TABLE:  Coordinate Systems', 'RotAngle', 'Degrees')
        ]
        coordinate_systems_data = [('GLOBAL', 'Cartesian', 0, 0, 0)]
        self.CoordinateSystems = self._create_dataframe(
            coordinate_systems_cols, coordinate_systems_data)

        # Grid Lines
        grid_lines_cols = [
            ('TABLE:  Grid Lines', 'CoordSys', 'Text'),
            ('TABLE:  Grid Lines', 'AxisDir', 'Text'),
            ('TABLE:  Grid Lines', 'GridID', 'Text'),
            ('TABLE:  Grid Lines', 'Visible', 'Yes/No'),
            ('TABLE:  Grid Lines', 'BubbleLoc', 'Text'),
            ('TABLE:  Grid Lines', 'GridColor', 'Text'),
            ('TABLE:  Grid Lines', 'BubbleSize', 'm'),
            ('TABLE:  Grid Lines', 'HideAll', 'Yes/No')
        ]
        self.GridLines = self._create_dataframe(grid_lines_cols)

        # Material Prop 01 - General
        material_prop01_general_cols = [
            ('TABLE:  Material Properties 01 - General', 'Material', 'Text'),
            ('TABLE:  Material Properties 01 - General', 'Type', 'Text'),
            ('TABLE:  Material Properties 01 - General', 'Color', 'Text'),
            ('TABLE:  Material Properties 01 - General', 'Notes', 'Text')
        ]
        self.MaterialProp01General = self._create_dataframe(
            material_prop01_general_cols)

        # Material Prop 02 - Steel
        material_prop02_steel_cols = [
            ('TABLE:  Material Properties 02 - Steel', 'Material', 'Text'),
            ('TABLE:  Material Properties 02 - Steel', 'E', 'kN/m2'),
            ('TABLE:  Material Properties 02 - Steel', 'U', 'Unitless'),
            ('TABLE:  Material Properties 02 - Steel', 'A', '1/C'),
            ('TABLE:  Material Properties 02 - Steel', 'UnitWt', 'kN/m3'),
            ('TABLE:  Material Properties 02 - Steel', 'Fy', 'kN/m2'),
            ('TABLE:  Material Properties 02 - Steel', 'Fu', 'kN/m2')
        ]
        self.MaterialProp02Steel = self._create_dataframe(
            material_prop02_steel_cols)

        # Material Prop 03 - Concrete
        material_prop03_concrete_cols = [
            ('TABLE:  Material Properties 03 - Concrete', 'Material', 'Text'),
            ('TABLE:  Material Properties 03 - Concrete', 'E', 'kN/m2'),
            ('TABLE:  Material Properties 03 - Concrete', 'U', 'Unitless'),
            ('TABLE:  Material Properties 03 - Concrete', 'A', '1/C'),
            ('TABLE:  Material Properties 03 - Concrete', 'UnitWt', 'kN/m3'),
            ('TABLE:  Material Properties 03 - Concrete', 'Fc', 'kN/m2'),
            ('TABLE:  Material Properties 03 - Concrete', 'LtWtConc', 'Yes/No')
        ]
        self.MaterialProp03Concrete = self._create_dataframe(
            material_prop03_concrete_cols)

        # Material Prop 04 - Rebar
        material_prop04_rebar_cols = [
            ('TABLE:  Material Properties 04 - Rebar', 'Material', 'Text'),
            ('TABLE:  Material Properties 04 - Rebar', 'E', 'kN/m2'),
            ('TABLE:  Material Properties 04 - Rebar', 'UnitWt', 'kN/m3'),
            ('TABLE:  Material Properties 04 - Rebar', 'Fy', 'kN/m2'),
            ('TABLE:  Material Properties 04 - Rebar', 'Fu', 'kN/m2')
        ]
        self.MaterialProp04Rebar = self._create_dataframe(
            material_prop04_rebar_cols)

        # Material Prop 05 - Tendon
        material_prop05_tendon_cols = [
            ('TABLE:  Material Properties 05 - Tendon', 'Material', 'Text'),
            ('TABLE:  Material Properties 05 - Tendon', 'E', 'kN/m2'),
            ('TABLE:  Material Properties 05 - Tendon', 'UnitWt', 'kN/m3'),
            ('TABLE:  Material Properties 05 - Tendon', 'Fy', 'kN/m2'),
            ('TABLE:  Material Properties 05 - Tendon', 'Fu', 'kN/m2')
        ]
        self.MaterialProp05Tendon = self._create_dataframe(
            material_prop05_tendon_cols)

        # Material Prop 06 - Other
        material_prop06_other_cols = [
            ('TABLE:  Material Properties 06 - Other', 'Material', 'Text'),
            ('TABLE:  Material Properties 06 - Other', 'E', 'kN/m2'),
            ('TABLE:  Material Properties 06 - Other', 'U', 'Unitless'),
            ('TABLE:  Material Properties 06 - Other', 'A', '1/C'),
            ('TABLE:  Material Properties 06 - Other', 'UnitWt', 'kN/m3')
        ]
        self.MaterialProp06Other = self._create_dataframe(
            material_prop06_other_cols)

        # Beam Properties 01 - General
        beam_properties01_general_cols = [
            ('TABLE:  Beam Properties 01 - General', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 01 - General', 'Type', 'Text'),
            ('TABLE:  Beam Properties 01 - General', 'Color', 'Text')
        ]
        self.BeamProperties01General = self._create_dataframe(
            beam_properties01_general_cols)

        # Beam Properties 02 - Rectangle
        beam_properties02_rectangle_cols = [
            ('TABLE:  Beam Properties 02 - Rectangular Beam', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 02 - Rectangular Beam', 'MatProp', 'Text'),
            ('TABLE:  Beam Properties 02 - Rectangular Beam', 'Depth', 'm'),
            ('TABLE:  Beam Properties 02 - Rectangular Beam', 'WidthTop', 'm'),
            ('TABLE:  Beam Properties 02 - Rectangular Beam', 'WidthBot', 'm')
        ]
        self.BeamProperties02Rectangle = self._create_dataframe(
            beam_properties02_rectangle_cols)

        # Beam Properties 03 - T Beam
        beam_properties03_t_beam_cols = [
            ('TABLE:  Beam Properties 03 - T Beam', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 03 - T Beam', 'MatProp', 'Text'),
            ('TABLE:  Beam Properties 03 - T Beam', 'TotalDepth', 'm'),
            ('TABLE:  Beam Properties 03 - T Beam', 'SlabDepth', 'm'),
            ('TABLE:  Beam Properties 03 - T Beam', 'FlngWidth', 'm'),
            ('TABLE:  Beam Properties 03 - T Beam', 'WidthTop', 'm'),
            ('TABLE:  Beam Properties 03 - T Beam', 'WidthBot', 'm'),
            ('TABLE:  Beam Properties 03 - T Beam', 'Inverted', 'Yes/No')
        ]
        self.BeamProperties03TBeam = self._create_dataframe(
            beam_properties03_t_beam_cols)

        # Beam Properties 04 - L Beam
        beam_properties04_l_beam_cols = [
            ('TABLE:  Beam Properties 04 - L Beam', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 04 - L Beam', 'MatProp', 'Text'),
            ('TABLE:  Beam Properties 04 - L Beam', 'TotalDepth', 'm'),
            ('TABLE:  Beam Properties 04 - L Beam', 'SlabDepth', 'm'),
            ('TABLE:  Beam Properties 04 - L Beam', 'FlngWidth', 'm'),
            ('TABLE:  Beam Properties 04 - L Beam', 'WidthTop', 'm'),
            ('TABLE:  Beam Properties 04 - L Beam', 'WidthBot', 'm'),
            ('TABLE:  Beam Properties 04 - L Beam', 'Inverted', 'Yes/No')
        ]
        self.BeamProperties04LBeam = self._create_dataframe(
            beam_properties04_l_beam_cols)

        # Beam Props 05 - General Beam
        beam_props05_general_beam_cols = [
            ('TABLE:  Beam Properties 05 - General Beam', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 05 - General Beam', 'MatProp', 'Text'),
            ('TABLE:  Beam Properties 05 - General Beam', 'Area', 'm2'),
            ('TABLE:  Beam Properties 05 - General Beam', 'As2', 'm2'),
            ('TABLE:  Beam Properties 05 - General Beam', 'As3', 'm2'),
            ('TABLE:  Beam Properties 05 - General Beam', 'J', 'm4'),
            ('TABLE:  Beam Properties 05 - General Beam', 'I22', 'm4'),
            ('TABLE:  Beam Properties 05 - General Beam', 'I33', 'm4'),
            ('TABLE:  Beam Properties 05 - General Beam', 'DisDepth', 'm'),
            ('TABLE:  Beam Properties 05 - General Beam', 'DisWidth', 'm')
        ]
        self.BeamProps05GeneralBeam = self._create_dataframe(
            beam_props05_general_beam_cols)

        # Beam Props 06 - Design Data
        beam_props06_design_data_cols = [
            ('TABLE:  Beam Properties 06 - Design Data', 'Beam', 'Text'),
            ('TABLE:  Beam Properties 06 - Design Data', 'MatRebarL', 'Text'),
            ('TABLE:  Beam Properties 06 - Design Data', 'MatRebarS', 'Text'),
            ('TABLE:  Beam Properties 06 - Design Data', 'FlngWOpt', 'Text'),
            ('TABLE:  Beam Properties 06 - Design Data', 'CoverTop', 'm'),
            ('TABLE:  Beam Properties 06 - Design Data', 'CoverBot', 'm'),
            ('TABLE:  Beam Properties 06 - Design Data', 'NoDesign', 'Yes/No')
        ]
        self.BeamProps06DesignData = self._create_dataframe(
            beam_props06_design_data_cols)

        # Slab Properties 01 - General
        slab_properties01_general_cols = [
            ('TABLE:  Slab Properties 01 - General', 'Slab', 'Text'),
            ('TABLE:  Slab Properties 01 - General', 'Type', 'Text'),
            ('TABLE:  Slab Properties 01 - General', 'Color', 'Text')
        ]
        self.SlabProperties01General = self._create_dataframe(
            slab_properties01_general_cols)

        # Slab Prop 02 - Solid Slabs
        slab_prop02_solid_slabs_cols = [
            ('TABLE:  Slab Properties 02 - Solid Slabs', 'Slab', 'Text'),
            ('TABLE:  Slab Properties 02 - Solid Slabs', 'Type', 'Text'),
            ('TABLE:  Slab Properties 02 - Solid Slabs', 'MatProp', 'Text'),
            ('TABLE:  Slab Properties 02 - Solid Slabs', 'Thickness', 'm'),
            ('TABLE:  Slab Properties 02 - Solid Slabs', 'Ortho', 'Yes/No')
        ]
        self.SlabProp02SolidSlabs = self._create_dataframe(
            slab_prop02_solid_slabs_cols)

        # Slab Prop 03 - Ribs And Waffles
        slab_prop03_ribs_and_waffles_cols = [
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'Slab', 'Text'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'Type', 'Text'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'MatProp', 'Text'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'TotalDepth', 'm'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'SlabThick', 'm'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'WidthTop', 'm'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'WidthBot', 'm'),
            ('TABLE:  Slab Properties 03 - Ribbed And Waffle Slabs', 'RibSpace1', 'm')
        ]
        self.SlabProp03RibsAndWaffles = self._create_dataframe(
            slab_prop03_ribs_and_waffles_cols)

        # Tendon Properties
        tendon_properties_cols = [
            ('TABLE:  Tendon Properties', 'TendonProp', 'Text'),
            ('TABLE:  Tendon Properties', 'MatProp', 'Text'),
            ('TABLE:  Tendon Properties', 'StrandArea', 'm2'),
            ('TABLE:  Tendon Properties', 'Color', 'Text')
        ]
        self.TendonProperties = self._create_dataframe(tendon_properties_cols)

        # Reinforcing Bar Sizes
        reinforcing_bar_sizes_cols = [
            ('TABLE:  Reinforcing Bar Sizes', 'RebarID', 'Text'),
            ('TABLE:  Reinforcing Bar Sizes', 'Area', 'm2'),
            ('TABLE:  Reinforcing Bar Sizes', 'Diameter', 'm')
        ]
        reinforcing_bar_sizes_data = [
            (6, 2.82999992370605e-05, 0.006), (8, 5.02999992370605e-05, 0.008),
            (10, 7.85e-05, 0.01), (12, 0.000113099998474121, 0.012),
            (14, 0.000153899993896484, 0.014), (16, 0.000201100006103516, 0.016),
            (18, 0.0002545, 0.018), (20, 0.000314200012207031, 0.02),
            (22, 0.000380100006103516, 0.022), (25, 0.000490899993896484, 0.025),
            (26, 0.000530900024414063, 0.026), (28, 0.000615799987792969, 0.028),
            (32, 0.000804200012207031, 0.032), (36, 0.00101790002441406, 0.036),
            (40, 0.00125659997558594, 0.04), (50, 0.0019635, 0.05)
        ]
        self.ReinforcingBarSizes = self._create_dataframe(
            reinforcing_bar_sizes_cols, reinforcing_bar_sizes_data)

        # Column Properties 01 - General
        column_properties01_general_cols = [
            ('TABLE:  Column Properties 01 - General', 'Column', 'Text'),
            ('TABLE:  Column Properties 01 - General', 'Type', 'Text'),
            ('TABLE:  Column Properties 01 - General', 'Color', 'Text')
        ]
        self.ColumnProperties01General = self._create_dataframe(
            column_properties01_general_cols)

        # Column Props 02 - Rectangular
        column_props02_rectangular_cols = [
            ('TABLE:  Column Properties 02 - Rectangular', 'Column', 'Text'),
            ('TABLE:  Column Properties 02 - Rectangular', 'MatProp', 'Text'),
            ('TABLE:  Column Properties 02 - Rectangular', 'SecDim2', 'm'),
            ('TABLE:  Column Properties 02 - Rectangular', 'SecDim3', 'm'),
            ('TABLE:  Column Properties 02 - Rectangular', 'AutoRigid', 'Yes/No'),
            ('TABLE:  Column Properties 02 - Rectangular', 'AutoDrop', 'Yes/No'),
            ('TABLE:  Column Properties 02 - Rectangular', 'IncludeCap', 'Yes/No')
        ]
        self.ColumnProps02Rectangular = self._create_dataframe(
            column_props02_rectangular_cols)

        # Column Properties 03 - Circular
        column_properties03_circular_cols = [
            ('TABLE:  Column Properties 03 - Circular', 'Column', 'Text'),
            ('TABLE:  Column Properties 03 - Circular', 'MatProp', 'Text'),
            ('TABLE:  Column Properties 03 - Circular', 'Diameter', 'm'),
            ('TABLE:  Column Properties 03 - Circular', 'AutoRigid', 'Yes/No'),
            ('TABLE:  Column Properties 03 - Circular', 'AutoDrop', 'Yes/No'),
            ('TABLE:  Column Properties 03 - Circular', 'IncludeCap', 'Yes/No')
        ]
        self.ColumnProperties03Circular = self._create_dataframe(
            column_properties03_circular_cols)

        # Column Properties 04 - T Shape
        column_properties04_t_shape_cols = [
            ('TABLE:  Column Properties 04 - T Shape', 'Column', 'Text'),
            ('TABLE:  Column Properties 04 - T Shape', 'MatProp', 'Text'),
            ('TABLE:  Column Properties 04 - T Shape', 'TotalDepth', 'm'),
            ('TABLE:  Column Properties 04 - T Shape', 'FlngWidth', 'm'),
            ('TABLE:  Column Properties 04 - T Shape', 'FlngThick', 'm'),
            ('TABLE:  Column Properties 04 - T Shape', 'WebThick', 'm'),
            ('TABLE:  Column Properties 04 - T Shape', 'AutoDrop', 'Yes/No')
        ]
        self.ColumnProperties04TShape = self._create_dataframe(
            column_properties04_t_shape_cols)

        # Column Properties 05 - L Shape
        column_properties05_l_shape_cols = [
            ('TABLE:  Column Properties 05 - L Shape', 'Column', 'Text'),
            ('TABLE:  Column Properties 05 - L Shape', 'MatProp', 'Text'),
            ('TABLE:  Column Properties 05 - L Shape', 'TotalDepth', 'm'),
            ('TABLE:  Column Properties 05 - L Shape', 'FlngWidth', 'm'),
            ('TABLE:  Column Properties 05 - L Shape', 'FlngThick', 'm'),
            ('TABLE:  Column Properties 05 - L Shape', 'WebThick', 'm'),
            ('TABLE:  Column Properties 05 - L Shape', 'AutoDrop', 'Yes/No')
        ]
        self.ColumnProperties05LShape = self._create_dataframe(
            column_properties05_l_shape_cols)

        # Column Props 06- General Shape
        column_props06_general_shape_cols = [
            ('TABLE:  Column Properties 06 - General Shape', 'Column', 'Text'),
            ('TABLE:  Column Properties 06 - General Shape', 'MatProp', 'Text'),
            ('TABLE:  Column Properties 06 - General Shape', 'SecDim2', 'm'),
            ('TABLE:  Column Properties 06 - General Shape', 'SecDim3', 'm'),
            ('TABLE:  Column Properties 06 - General Shape', 'AutoRigid', 'Yes/No'),
            ('TABLE:  Column Properties 06 - General Shape', 'AutoDrop', 'Yes/No'),
            ('TABLE:  Column Properties 06 - General Shape', 'Area', 'm2'),
            ('TABLE:  Column Properties 06 - General Shape', 'As2', 'm2'),
            ('TABLE:  Column Properties 06 - General Shape', 'As3', 'm2'),
            ('TABLE:  Column Properties 06 - General Shape', 'J', 'm4'),
            ('TABLE:  Column Properties 06 - General Shape', 'I22', 'm4'),
            ('TABLE:  Column Properties 06 - General Shape', 'I33', 'm4')
        ]
        self.ColumnProps06GeneralShape = self._create_dataframe(
            column_props06_general_shape_cols)

        # Wall Properties
        wall_properties_cols = [
            ('TABLE:  Wall Properties', 'Wall', 'Text'),
            ('TABLE:  Wall Properties', 'MatProp', 'Text'),
            ('TABLE:  Wall Properties', 'Thickness', 'm'),
            ('TABLE:  Wall Properties', 'AutoRigid', 'Yes/No'),
            ('TABLE:  Wall Properties', 'OutOfPlane', 'Yes/No'),
            ('TABLE:  Wall Properties', 'Color', 'Text')
        ]
        self.WallProperties = self._create_dataframe(wall_properties_cols)

        # Soil Properties
        soil_properties_cols = [
            ('TABLE:  Soil Properties', 'Soil', 'Text'),
            ('TABLE:  Soil Properties', 'Subgrade', 'kN/m3'),
            ('TABLE:  Soil Properties', 'Color', 'Text')
        ]
        self.SoilProperties = self._create_dataframe(soil_properties_cols)

        # Spring Properties - Line
        spring_properties_line_cols = [
            ('TABLE:  Spring Properties - Line', 'Spring', 'Text'),
            ('TABLE:  Spring Properties - Line', 'VertStiff', 'kN/m/m'),
            ('TABLE:  Spring Properties - Line', 'RotStiff', 'kN/rad'),
            ('TABLE:  Spring Properties - Line', 'NonlinOpt', 'Text'),
            ('TABLE:  Spring Properties - Line', 'Color', 'Text')
        ]
        self.SpringPropertiesLine = self._create_dataframe(
            spring_properties_line_cols)

        # Spring Properties - Point
        spring_properties_point_cols = [
            ('TABLE:  Spring Properties - Point', 'Spring', 'Text'),
            ('TABLE:  Spring Properties - Point', 'Ux', 'kN/m'),
            ('TABLE:  Spring Properties - Point', 'Uy', 'kN/m'),
            ('TABLE:  Spring Properties - Point', 'Uz', 'kN/m'),
            ('TABLE:  Spring Properties - Point', 'Rx', 'kN-m/rad'),
            ('TABLE:  Spring Properties - Point', 'Ry', 'kN-m/rad'),
            ('TABLE:  Spring Properties - Point', 'Rz', 'kN-m/rad'),
            ('TABLE:  Spring Properties - Point', 'NonlinOpt', 'Text'),
            ('TABLE:  Spring Properties - Point', 'Color', 'Text')
        ]
        self.SpringPropertiesPoint = self._create_dataframe(
            spring_properties_point_cols)

    def _initialize_load_definition_tables(self) -> None:
        """Initializes DataFrames for defining load patterns, cases, combinations, and functions."""
        # Load Patterns
        load_patterns_cols = [
            ('TABLE:  Load Patterns', 'LoadPat', 'Text'),
            ('TABLE:  Load Patterns', 'Type', 'Text'),
            ('TABLE:  Load Patterns', 'SelfWtMult', 'Unitless')
        ]
        self.LoadPatterns = self._create_dataframe(load_patterns_cols)

        # Load Cases 01 - General
        load_cases01_general_cols = [
            ('TABLE:  Load Cases 01 - General', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 01 - General', 'Type', 'Text'),
            ('TABLE:  Load Cases 01 - General', 'DesignOpt', 'Text'),
            ('TABLE:  Load Cases 01 - General', 'DesignType', 'Text')
        ]
        self.LoadCases01General = self._create_dataframe(
            load_cases01_general_cols)

        # Load Cases 02 - Static
        load_cases02_static_cols = [
            ('TABLE:  Load Cases 02 - Static', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 02 - Static', 'InitialCond', 'Text'),
            ('TABLE:  Load Cases 02 - Static', 'AType', 'Text')
        ]
        self.LoadCases02Static = self._create_dataframe(
            load_cases02_static_cols)

        # Load Cases 03 - MultistepStatic
        load_cases03_multistep_static_cols = [
            ('TABLE:  Load Cases 03 - Multistep Static', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 03 - Multistep Static', 'InitialCond', 'Text'),
            ('TABLE:  Load Cases 03 - Multistep Static', 'ResType', 'Text')
        ]
        self.LoadCases03MultistepStatic = self._create_dataframe(
            load_cases03_multistep_static_cols)

        # Load Cases 04 - Modal
        load_cases04_modal_cols = [
            ('TABLE:  Load Cases 04 - Modal', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 04 - Modal', 'InitialCond', 'Text'),
            ('TABLE:  Load Cases 04 - Modal', 'ModeType', 'Text'),
            ('TABLE:  Load Cases 04 - Modal', 'MaxModes', 'Unitless'),
            ('TABLE:  Load Cases 04 - Modal', 'MinModes', 'Unitless')
        ]
        self.LoadCases04Modal = self._create_dataframe(load_cases04_modal_cols)

        # Load Cases 05 - Hyperstatic
        load_cases05_hyperstatic_cols = [
            ('TABLE:  Load Cases 05 - Hyperstatic', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 05 - Hyperstatic', 'LinCase', 'Text')
        ]
        self.LoadCases05Hyperstatic = self._create_dataframe(
            load_cases05_hyperstatic_cols)

        # Load Cases 06 - Loads Applied
        load_cases06_loads_applied_cols = [
            ('TABLE:  Load Cases 06 - Loads Applied', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 06 - Loads Applied', 'LoadPat', 'Text'),
            ('TABLE:  Load Cases 06 - Loads Applied', 'SF', 'Unitless')
        ]
        self.LoadCases06LoadsApplied = self._create_dataframe(
            load_cases06_loads_applied_cols)

        # Load Cases 07 - Resp Spec - Gen
        load_cases07_resp_spec_gen_cols = [
            ('TABLE:  Load Cases 07 - Response Spectrum - General', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 07 - Response Spectrum - General', 'ModalCase', 'Text'),
            ('TABLE:  Load Cases 07 - Response Spectrum - General', 'ModalComb', 'Text'),
            ('TABLE:  Load Cases 07 - Response Spectrum - General', 'DirComb', 'Text'),
            ('TABLE:  Load Cases 07 - Response Spectrum - General',
             'ConstDamp', 'Unitless'),
            ('TABLE:  Load Cases 07 - Response Spectrum - General', 'EccenCase', 'Text')
        ]
        self.LoadCases07RespSpecGen = self._create_dataframe(
            load_cases07_resp_spec_gen_cols)

        # Load Cases 08 - Resp Spec - LA
        load_cases08_resp_spec_la_cols = [
            ('TABLE:  Load Cases 08 - Response Spectrum - Loads Applied',
             'LoadCase', 'Text'),
            ('TABLE:  Load Cases 08 - Response Spectrum - Loads Applied',
             'LoadName', 'Text'),
            ('TABLE:  Load Cases 08 - Response Spectrum - Loads Applied',
             'Function', 'Text'),
            ('TABLE:  Load Cases 08 - Response Spectrum - Loads Applied',
             'TransAccSF', 'm/sec2')
        ]
        self.LoadCases08RespSpecLA = self._create_dataframe(
            load_cases08_resp_spec_la_cols)

        # Load Cases 09 - External MD
        load_cases09_external_md_cols = [
            ('TABLE:  Load Cases 09 - External Mode Data', 'LoadCase', 'Text'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DampElm', 'Unitless'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DampCModal', 'Unitless'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'MissMass', 'Yes/No'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'ModalMass', 'kN-m-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'ModalStiff', 'kN-m'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFUX', 'kN-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFUY', 'kN-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFUZ', 'kN-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFRX', 'kN-m-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFRY', 'kN-m-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'PFRZ', 'kN-m-s2'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRUX', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRUY', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRUZ', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRRX', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRRY', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'SRRZ', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRUX', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRUY', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRUZ', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRRX', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRRY', 'Percent'),
            ('TABLE:  Load Cases 09 - External Mode Data', 'DRRZ', 'Percent')
        ]
        self.LoadCases09ExternalMD = self._create_dataframe(
            load_cases09_external_md_cols)

        # Load Combinations
        load_combinations_cols = [
            ('TABLE:  Load Combinations', 'Combo', 'Text'),
            ('TABLE:  Load Combinations', 'Load', 'Text'),
            ('TABLE:  Load Combinations', 'SF', 'Unitless'),
            ('TABLE:  Load Combinations', 'Type', 'Text'),
            ('TABLE:  Load Combinations', 'DSStrength', 'Yes/No'),
            ('TABLE:  Load Combinations', 'DSServInit', 'Yes/No'),
            ('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No'),
            ('TABLE:  Load Combinations', 'DSServLong', 'Yes/No'),
            ('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No')
        ]
        self.LoadCombinations = self._create_dataframe(load_combinations_cols)

        # Function - Response Spectrum
        function_response_spectrum_cols = [
            ('TABLE:  Function - Response Spectrum', 'Func', 'Text'),
            ('TABLE:  Function - Response Spectrum', 'Period', 'Sec'),
            ('TABLE:  Function - Response Spectrum', 'Accel', 'Unitless'),
            ('TABLE:  Function - Response Spectrum', 'FuncDamp', 'Unitless')
        ]
        self.FunctionResponseSpectrum = self._create_dataframe(
            function_response_spectrum_cols)

    def _initialize_group_tables(self) -> None:
        """Initializes DataFrames for group definitions and assignments."""
        # Group Definitions
        group_definitions_cols = [
            ('TABLE:  Group Definitions', 'Group', 'Text'),
            ('TABLE:  Group Definitions', 'Color', 'Text')
        ]
        group_definitions_data = [('ALL', 'Red')]
        self.GroupDefinitions = self._create_dataframe(
            group_definitions_cols, group_definitions_data)

        # Group Assignments
        group_assignments_cols = [
            ('TABLE:  Group Assignments', 'Group', 'Text'),
            ('TABLE:  Group Assignments', 'ObjType', 'Text'),
            ('TABLE:  Group Assignments', 'ObjLabel', 'Text')
        ]
        self.GroupAssignments = self._create_dataframe(group_assignments_cols)

    def _initialize_object_property_assignment_tables(self) -> None:
        """Initializes DataFrames for assigning properties, modifiers, releases, supports, etc., to objects."""
        # Slab Property Assignments
        slab_property_assignments_cols = [
            ('TABLE:  Slab Property Assignments', 'Area', 'Text'),
            ('TABLE:  Slab Property Assignments', 'SlabProp', 'Text'),
            ('TABLE:  Slab Property Assignments', 'OpeningType', 'Text')
        ]
        self.SlabPropertyAssignments = self._create_dataframe(
            slab_property_assignments_cols)

        # Slab Property Modifiers
        slab_property_modifiers_cols = [
            ('TABLE:  Slab Property Modifiers', 'Area', 'Text'),
            ('TABLE:  Slab Property Modifiers', 'f11', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'f22', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'f12', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'm11', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'm22', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'm12', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'v13', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'v23', 'Unitless'),
            ('TABLE:  Slab Property Modifiers', 'Weight', 'Unitless')
        ]
        self.SlabPropertyModifiers = self._create_dataframe(
            slab_property_modifiers_cols)

        # Slab Vertical Offsets
        slab_vertical_offsets_cols = [
            ('TABLE:  Slab Vertical Offsets', 'Area', 'Text'),
            ('TABLE:  Slab Vertical Offsets', 'Offset', 'm')
        ]
        self.SlabVerticalOffsets = self._create_dataframe(
            slab_vertical_offsets_cols)

        # Slab Local Axes
        slab_local_axes_cols = [
            ('TABLE:  Slab Local Axes', 'Area', 'Text'),
            ('TABLE:  Slab Local Axes', 'Angle', 'Degrees')
        ]
        self.SlabLocalAxes = self._create_dataframe(slab_local_axes_cols)

        # Slab Edge Releases
        slab_edge_releases_cols = [
            ('TABLE:  Slab Edge Releases', 'Area', 'Text'),
            ('TABLE:  Slab Edge Releases', 'Edge', 'Text'),
            ('TABLE:  Slab Edge Releases', 'Shear', 'Yes/No'),
            ('TABLE:  Slab Edge Releases', 'Moment', 'Yes/No')
        ]
        self.SlabEdgeReleases = self._create_dataframe(slab_edge_releases_cols)

        # Slab Line Releases
        slab_line_releases_cols = [
            ('TABLE:  Slab Line Releases', 'Line', 'Text'),
            ('TABLE:  Slab Line Releases', 'ShearL', 'Yes/No'),
            ('TABLE:  Slab Line Releases', 'MomentL', 'Yes/No'),
            ('TABLE:  Slab Line Releases', 'ShearR', 'Yes/No'),
            ('TABLE:  Slab Line Releases', 'MomentR', 'Yes/No')
        ]
        self.SlabLineReleases = self._create_dataframe(slab_line_releases_cols)

        # Slab Rib Locations
        slab_rib_locations_cols = [
            ('TABLE:  Slab Rib Locations', 'Area', 'Text'),
            ('TABLE:  Slab Rib Locations', 'GlobalX', 'm'),
            ('TABLE:  Slab Rib Locations', 'GlobalY', 'm')
        ]
        self.SlabRibLocations = self._create_dataframe(slab_rib_locations_cols)

        # Beam Property Assignments
        beam_property_assignments_cols = [
            ('TABLE:  Beam Property Assignments', 'Line', 'Text'),
            ('TABLE:  Beam Property Assignments', 'BeamProp', 'Text')
        ]
        self.BeamPropertyAssignments = self._create_dataframe(
            beam_property_assignments_cols)

        # Beam Property Modifiers
        beam_property_modifiers_cols = [
            ('TABLE:  Beam Property Modifiers', 'Line', 'Text'),
            ('TABLE:  Beam Property Modifiers', 'Area', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'As2', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'As3', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'J', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'I22', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'I33', 'Unitless'),
            ('TABLE:  Beam Property Modifiers', 'Weight', 'Unitless')
        ]
        self.BeamPropertyModifiers = self._create_dataframe(
            beam_property_modifiers_cols)

        # Beam End Releases
        beam_end_releases_cols = [
            ('TABLE:  Beam End Releases', 'Line', 'Text'),
            ('TABLE:  Beam End Releases', 'TI', 'Yes/No'),
            ('TABLE:  Beam End Releases', 'M2I', 'Yes/No'),
            ('TABLE:  Beam End Releases', 'M3I', 'Yes/No'),
            ('TABLE:  Beam End Releases', 'TJ', 'Yes/No'),
            ('TABLE:  Beam End Releases', 'M2J', 'Yes/No'),
            ('TABLE:  Beam End Releases', 'M3J', 'Yes/No')
        ]
        self.BeamEndReleases = self._create_dataframe(beam_end_releases_cols)

        # Beam Insertion Point
        beam_insertion_point_cols = [
            ('TABLE:  Beam Insertion Point', 'Line', 'Text'),
            ('TABLE:  Beam Insertion Point', 'CardinalPt', 'Text'),
            ('TABLE:  Beam Insertion Point', 'OffsetXI', 'm'),
            ('TABLE:  Beam Insertion Point', 'OffsetYI', 'm'),
            ('TABLE:  Beam Insertion Point', 'OffsetZI', 'm'),
            ('TABLE:  Beam Insertion Point', 'OffsetXJ', 'm'),
            ('TABLE:  Beam Insertion Point', 'OffsetYJ', 'm'),
            ('TABLE:  Beam Insertion Point', 'OffsetZJ', 'm')
        ]
        self.BeamInsertionPoint = self._create_dataframe(
            beam_insertion_point_cols)

        # Tendon Property Assignments
        tendon_property_assignments_cols = [
            ('TABLE:  Tendon Property Assignments', 'Tendon', 'Text'),
            ('TABLE:  Tendon Property Assignments', 'TendonProp', 'Text'),
            ('TABLE:  Tendon Property Assignments', 'NumStrands', 'Unitless'),
            ('TABLE:  Tendon Property Assignments', 'BondType', 'Text')
        ]
        self.TendonPropertyAssignments = self._create_dataframe(
            tendon_property_assignments_cols)

        # Slab Rebar Property Assignments
        slab_rebar_property_assignments_cols = [
            ('TABLE:  Slab Rebar Property Assignments', 'SlabRebar', 'Text'),
            ('TABLE:  Slab Rebar Property Assignments', 'RebarID', 'Text'),
            ('TABLE:  Slab Rebar Property Assignments', 'MatProp', 'Text'),
            ('TABLE:  Slab Rebar Property Assignments', 'BarNumType', 'Text'),
            ('TABLE:  Slab Rebar Property Assignments', 'TotalBars', 'Unitless'),
            ('TABLE:  Slab Rebar Property Assignments', 'MaxSpacing', 'm')
        ]
        self.SlabRebarPropertyAssignments = self._create_dataframe(
            slab_rebar_property_assignments_cols)

        # Column Property Assignments
        column_property_assignments_cols = [
            ('TABLE:  Column Property Assignments', 'Line', 'Text'),
            ('TABLE:  Column Property Assignments', 'ColProp', 'Text')
        ]
        self.ColumnPropertyAssignments = self._create_dataframe(
            column_property_assignments_cols)

        # Column Property Modifiers
        column_property_modifiers_cols = [
            ('TABLE:  Column Property Modifiers', 'Line', 'Text'),
            ('TABLE:  Column Property Modifiers', 'Area', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'As2', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'As3', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'J', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'I22', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'I33', 'Unitless'),
            ('TABLE:  Column Property Modifiers', 'Weight', 'Unitless')
        ]
        self.ColumnPropertyModifiers = self._create_dataframe(
            column_property_modifiers_cols)

        # Column Local Axes
        column_local_axes_cols = [
            ('TABLE:  Column Local Axes', 'Line', 'Text'),
            ('TABLE:  Column Local Axes', 'Angle', 'Degrees')
        ]
        self.ColumnLocalAxes = self._create_dataframe(column_local_axes_cols)

        # Column End Releases
        column_end_releases_cols = [
            ('TABLE:  Column End Releases', 'Line', 'Text'),
            ('TABLE:  Column End Releases', 'TI', 'Yes/No'),
            ('TABLE:  Column End Releases', 'M2I', 'Yes/No'),
            ('TABLE:  Column End Releases', 'M3I', 'Yes/No'),
            ('TABLE:  Column End Releases', 'TJ', 'Yes/No'),
            ('TABLE:  Column End Releases', 'M2J', 'Yes/No'),
            ('TABLE:  Column End Releases', 'M3J', 'Yes/No')
        ]
        self.ColumnEndReleases = self._create_dataframe(
            column_end_releases_cols)

        # Column Insertion Point
        column_insertion_point_cols = [
            ('TABLE:  Column Insertion Point', 'Line', 'Text'),
            ('TABLE:  Column Insertion Point', 'CardinalPt', 'Text'),
            ('TABLE:  Column Insertion Point', 'OffsetXI', 'm'),
            ('TABLE:  Column Insertion Point', 'OffsetYI', 'm'),
            ('TABLE:  Column Insertion Point', 'OffsetZI', 'm'),
            ('TABLE:  Column Insertion Point', 'OffsetXJ', 'm'),
            ('TABLE:  Column Insertion Point', 'OffsetYJ', 'm'),
            ('TABLE:  Column Insertion Point', 'OffsetZJ', 'm')
        ]
        self.ColumnInsertionPoint = self._create_dataframe(
            column_insertion_point_cols)

        # Wall Property Assignments
        wall_property_assignments_cols = [
            ('TABLE:  Wall Property Assignments', 'Area', 'Text'),
            ('TABLE:  Wall Property Assignments', 'WallProp', 'Text')
        ]
        self.WallPropertyAssignments = self._create_dataframe(
            wall_property_assignments_cols)

        # Wall Property Modifiers
        wall_property_modifiers_cols = [
            ('TABLE:  Wall Property Modifiers', 'Area', 'Text'),
            ('TABLE:  Wall Property Modifiers', 'f11', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'f22', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'f12', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'm11', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'm22', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'm12', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'v13', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'v23', 'Unitless'),
            ('TABLE:  Wall Property Modifiers', 'Weight', 'Unitless')
        ]
        self.WallPropertyModifiers = self._create_dataframe(
            wall_property_modifiers_cols)

        # Wall Openings
        wall_openings_cols = [
            ('TABLE:  Wall Openings', 'Area', 'Text'),
            ('TABLE:  Wall Openings', 'Top', 'm'),
            ('TABLE:  Wall Openings', 'Left', 'm'),
            ('TABLE:  Wall Openings', 'Height', 'm'),
            ('TABLE:  Wall Openings', 'Width', 'm')
        ]
        self.WallOpenings = self._create_dataframe(wall_openings_cols)

        # Wall Normal Offsets
        wall_normal_offsets_cols = [
            ('TABLE:  Wall Normal Offsets', 'Area', 'Text'),
            ('TABLE:  Wall Normal Offsets', 'Offset', 'm')
        ]
        self.WallNormalOffsets = self._create_dataframe(
            wall_normal_offsets_cols)

        # Soil Property Assignments
        soil_property_assignments_cols = [
            ('TABLE:  Soil Property Assignments', 'Area', 'Text'),
            ('TABLE:  Soil Property Assignments', 'SoilProp', 'Text')
        ]
        self.SoilPropertyAssignments = self._create_dataframe(
            soil_property_assignments_cols)

        # Point Restraint Assignments
        point_restraint_assignments_cols = [
            ('TABLE:  Point Restraint Assignments', 'Point', 'Text'),
            ('TABLE:  Point Restraint Assignments', 'Ux', 'Yes/No'),
            ('TABLE:  Point Restraint Assignments', 'Uy', 'Yes/No'),
            ('TABLE:  Point Restraint Assignments', 'Uz', 'Yes/No'),
            ('TABLE:  Point Restraint Assignments', 'Rx', 'Yes/No'),
            ('TABLE:  Point Restraint Assignments', 'Ry', 'Yes/No'),
            ('TABLE:  Point Restraint Assignments', 'Rz', 'Yes/No')
        ]
        self.PointRestraintAssignments = self._create_dataframe(
            point_restraint_assignments_cols)

        # Point Spring Assignments
        point_spring_assignments_cols = [
            ('TABLE:  Point Spring Assignments', 'Point', 'Text'),
            ('TABLE:  Point Spring Assignments', 'Spring', 'Text')
        ]
        self.PointSpringAssignments = self._create_dataframe(
            point_spring_assignments_cols)

        # Line Spring Assignments
        line_spring_assignments_cols = [
            ('TABLE:  Line Spring Assignments', 'Line', 'Text'),
            ('TABLE:  Line Spring Assignments', 'Spring', 'Text')
        ]
        self.LineSpringAssignments = self._create_dataframe(
            line_spring_assignments_cols)

    def _initialize_load_to_object_assignment_tables(self) -> None:
        """Initializes DataFrames for assigning loads to various structural objects."""
        # Load Assigns - Surface Loads
        load_assigns_surface_loads_cols = [
            ('TABLE:  Load Assignments - Surface Loads', 'Area', 'Text'),
            ('TABLE:  Load Assignments - Surface Loads', 'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Surface Loads', 'Dir', 'Text'),
            ('TABLE:  Load Assignments - Surface Loads', 'UnifLoad', 'kN/m2'),
            ('TABLE:  Load Assignments - Surface Loads', 'A', 'kN/m3'),
            ('TABLE:  Load Assignments - Surface Loads', 'B', 'kN/m3'),
            ('TABLE:  Load Assignments - Surface Loads', 'C', 'kN/m2')
        ]
        self.LoadAssignsSurfaceLoads = self._create_dataframe(
            load_assigns_surface_loads_cols)

        # Load Assigns - Slab Temp Loads
        load_assigns_slab_temp_loads_cols = [
            ('TABLE:  Load Assignments - Slab Temperature Loads', 'Area', 'Text'),
            ('TABLE:  Load Assignments - Slab Temperature Loads', 'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Slab Temperature Loads', 'TopTemp', 'C'),
            ('TABLE:  Load Assignments - Slab Temperature Loads', 'BotTemp', 'C')
        ]
        self.LoadAssignsSlabTempLoads = self._create_dataframe(
            load_assigns_slab_temp_loads_cols)

        # Loads - Lines - Point
        loads_lines_point_cols = [
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'Line', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'Type', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'Dir', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'DistType', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads',
             'RelDist', 'Unitless'),
            ('TABLE:  Load Assignments - Line Objects - Point Loads', 'AbsDist', 'm')
        ]
        self.LoadsLinesPoint = self._create_dataframe(loads_lines_point_cols)

        # Loads - Lines - Distributed
        loads_lines_distributed_cols = [
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads', 'Line', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads', 'Type', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads', 'Dir', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'DistType', 'Text'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'RelDistA', 'Unitless'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'RelDistB', 'Unitless'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads', 'AbsDistA', 'm'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads', 'AbsDistB', 'm'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'FOverLA', 'kN/m'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'FOverLB', 'kN/m'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'MOverLA', 'kN-m/m'),
            ('TABLE:  Load Assignments - Line Objects - Distributed Loads',
             'MOverLB', 'kN-m/m')
        ]
        self.LoadsLinesDistributed = self._create_dataframe(
            loads_lines_distributed_cols)

        # Load Assignments - Point Loads
        load_assignments_point_loads_cols = [
            ('TABLE:  Load Assignments - Point Loads', 'Point', 'Text'),
            ('TABLE:  Load Assignments - Point Loads', 'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Point Loads', 'Fx', 'kN'),
            ('TABLE:  Load Assignments - Point Loads', 'Fy', 'kN'),
            ('TABLE:  Load Assignments - Point Loads', 'Fgrav', 'kN'),
            ('TABLE:  Load Assignments - Point Loads', 'Mx', 'kN-m'),
            ('TABLE:  Load Assignments - Point Loads', 'My', 'kN-m'),
            ('TABLE:  Load Assignments - Point Loads', 'Mz', 'kN-m'),
            ('TABLE:  Load Assignments - Point Loads', 'XDim', 'm'),
            ('TABLE:  Load Assignments - Point Loads', 'YDim', 'm')
        ]
        self.LoadAssignmentsPointLoads = self._create_dataframe(
            load_assignments_point_loads_cols)

        # Load Assignments - Point Displs
        load_assignments_point_displs_cols = [
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Point', 'Text'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'LoadPat', 'Text'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Ux', 'm'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Uy', 'm'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Ugrav', 'm'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Rx', 'Radians'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Ry', 'Radians'),
            ('TABLE:  Load Assignments - Point Displacement Loads', 'Rz', 'Radians')
        ]
        self.LoadAssignmentsPointDispls = self._create_dataframe(
            load_assignments_point_displs_cols)

        # Load Assignments - Tendon Loads
        load_assignments_tendon_loads_cols = [
            ('TABLE:  Load Assignments - Tendon Loads', 'Tendon', 'Text'),
            ('TABLE:  Load Assignments - Tendon Loads', 'LoadTrans', 'Text'),
            ('TABLE:  Load Assignments - Tendon Loads', 'LoadFinal', 'Text'),
            ('TABLE:  Load Assignments - Tendon Loads', 'JackLoc', 'Text'),
            ('TABLE:  Load Assignments - Tendon Loads', 'JackStress', 'kN/m2')
        ]
        self.LoadAssignmentsTendonLoads = self._create_dataframe(
            load_assignments_tendon_loads_cols)

        # Load Assignments - Tendon Loss
        load_assignments_tendon_loss_cols = [
            ('TABLE:  Load Assignments - Tendon Losses', 'Tendon', 'Text'),
            ('TABLE:  Load Assignments - Tendon Losses', 'LossType', 'Text')
        ]
        self.LoadAssignmentsTendonLoss = self._create_dataframe(
            load_assignments_tendon_loss_cols)

    def _initialize_analysis_mesh_object_tables(self) -> None:
        """Initializes DataFrames related to objects included in the analysis mesh."""
        # Objs Analysis Mesh 01 - Points
        objs_analysis_mesh01_points_cols = [
            ('TABLE:  Objects Included In Analysis Mesh 01 - Points', 'Point', 'Text'),
            ('TABLE:  Objects Included In Analysis Mesh 01 - Points', 'Include', 'Yes/No')
        ]
        self.ObjsAnalysisMesh01Points = self._create_dataframe(
            objs_analysis_mesh01_points_cols)

        # Objs Analysis Mesh 02 - Lines
        objs_analysis_mesh02_lines_cols = [
            ('TABLE:  Objects Included In Analysis Mesh 02 - Lines', 'Line', 'Text'),
            ('TABLE:  Objects Included In Analysis Mesh 02 - Lines', 'Include', 'Yes/No')
        ]
        self.ObjsAnalysisMesh02Lines = self._create_dataframe(
            objs_analysis_mesh02_lines_cols)

    def _initialize_design_parameter_tables(self) -> None:
        """Initializes DataFrames for design preferences, overwrites, and punching shear data."""
        # Design Pref 01 - Resist Factors
        design_pref01_resist_factors_cols = [
            ('TABLE:  Design Preferences 01 - Resistance Factors',
             'GammaSteel', 'Unitless'),
            ('TABLE:  Design Preferences 01 - Resistance Factors',
             'GammaConc', 'Unitless'),
            ('TABLE:  Design Preferences 01 - Resistance Factors',
             'GammaShear', 'Unitless')
        ]
        design_pref01_resist_factors_data = [(1.15, 1.5, 1.25)]
        self.DesignPref01ResistFactors = self._create_dataframe(
            design_pref01_resist_factors_cols, design_pref01_resist_factors_data)

        # Design Pref 02 - Cover - Slabs
        design_pref02_cover_slabs_cols = [
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'CoverTop', 'm'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'CoverBot', 'm'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'BarSize', 'Text'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'InnerLayer', 'Text'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'PTCGSTop', 'm'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'PTCGSBotExt', 'm'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'PTCGSBotInt', 'm'),
            ('TABLE:  Design Preferences 02 - Rebar Cover - Slabs', 'SlabType', 'Text')
        ]
        design_pref02_cover_slabs_data = [
            (0.015, 0.015, 18, 'B', 0.025, 0.04, 0.025, 'Two Way')]
        self.DesignPref02CoverSlabs = self._create_dataframe(
            design_pref02_cover_slabs_cols, design_pref02_cover_slabs_data)

        # Design Pref 03 - Cover - Beams
        design_pref03_cover_beams_cols = [
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'CoverTop', 'm'),
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'CoverBot', 'm'),
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'BarSizeF', 'Text'),
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'BarSizeS', 'Text'),
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'PTCGSTop', 'm'),
            ('TABLE:  Design Preferences 03 - Rebar Cover - Beams', 'PTCGSBot', 'm')
        ]
        design_pref03_cover_beams_data = [(0.04, 0.04, 28, 12, 0.05, 0.05)]
        self.DesignPref03CoverBeams = self._create_dataframe(
            design_pref03_cover_beams_cols, design_pref03_cover_beams_data)

        # Design Pref 04 - Prestress Data
        design_pref04_prestress_data_cols = [
            ('TABLE:  Design Preferences 04 - Prestress Data',
             'InitConcRat', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data',
             'InitTopTen', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data',
             'InitBotTen', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data',
             'InitExComp', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data', 'FinTopTen', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data', 'FinBotTen', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data', 'FinExComp', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data', 'SusExComp', 'Unitless'),
            ('TABLE:  Design Preferences 04 - Prestress Data', 'LLFraction', 'Unitless')
        ]
        design_pref04_prestress_data_data = [
            (0.8, 0.36, 0.36, 0.5, 0.36, 0.36, 0.4, 0.4, 0.5)]
        self.DesignPref04PrestressData = self._create_dataframe(
            design_pref04_prestress_data_cols, design_pref04_prestress_data_data)

        # Slab Design Overwrites 01 - Str
        slab_design_overwrites01_str_cols = [
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'Strip', 'Text'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'Layer', 'Text'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'DesignType', 'Text'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'RLLF', 'Unitless'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'Design', 'Yes/No'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'RebarMat', 'Text'),
            ('TABLE:  Slab Design Overwrites 01 - Strip Based', 'CoverType', 'Text')
        ]
        self.SlabDesignOverwrites01Str = self._create_dataframe(
            slab_design_overwrites01_str_cols)

        # Slab Design Overwrites 02 - FE
        slab_design_overwrites02_fe_cols = [
            ('TABLE:  Slab Design Overwrites 02 - Finite Element Based', 'Area', 'Text'),
            ('TABLE:  Slab Design Overwrites 02 - Finite Element Based',
             'RebarMat', 'Text'),
            ('TABLE:  Slab Design Overwrites 02 - Finite Element Based',
             'CoverType', 'Text'),
            ('TABLE:  Slab Design Overwrites 02 - Finite Element Based',
             'RLLF', 'Unitless'),
            ('TABLE:  Slab Design Overwrites 02 - Finite Element Based', 'Design', 'Yes/No')
        ]
        self.SlabDesignOverwrites02FE = self._create_dataframe(
            slab_design_overwrites02_fe_cols)

        # Beam Design Overwrites
        beam_design_overwrites_cols = [
            ('TABLE:  Beam Design Overwrites', 'Line', 'Text'),
            ('TABLE:  Beam Design Overwrites', 'CoverType', 'Text'),
            ('TABLE:  Beam Design Overwrites', 'RLLF', 'Unitless'),
            ('TABLE:  Beam Design Overwrites', 'IgnorePT', 'Yes/No')
        ]
        self.BeamDesignOverwrites = self._create_dataframe(
            beam_design_overwrites_cols)

        # Punching Shear 01 - General
        punching_shear01_general_cols = [
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'Point', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'Check', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'LocType', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'Perimeter', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'EffDepth', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'Openings', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 01 - General', 'ReinfType', 'Text')
        ]
        self.PunchingShear01General = self._create_dataframe(
            punching_shear01_general_cols)

        # Punching Shear 02 - User Perim
        punching_shear02_user_perim_cols = [
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter', 'Point', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter',
             'PointNum', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter', "x", 'm'),
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter', 'Y', 'm'),
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter', 'Radius', 'm'),
            ('TABLE:  Punching Shear Design Overwrites 02 - User Perimeter',
             'IsNull', 'Yes/No')
        ]
        self.PunchingShear02UserPerim = self._create_dataframe(
            punching_shear02_user_perim_cols)

        # Punching Shear 03 - User Opens
        punching_shear03_user_opens_cols = [
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings', 'Point', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings',
             'OpenNum', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings', 'Shape', 'Text'),
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings', 'XOffset', 'm'),
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings', 'YOffset', 'm'),
            ('TABLE:  Punching Shear Design Overwrites 03 - User Openings', 'Width', 'm')
        ]
        self.PunchingShear03UserOpens = self._create_dataframe(
            punching_shear03_user_opens_cols)
