# Finite Element Model Building (`build_fem/`)

## Main FEM Components
- **`builder_main.py`**: Core automation functions:
  - `automated_initialize_setup()` for project setup
  - `automated_generate_soil_spring()` for soil-structure interaction
  - `automated_generate_safe_model()` for complete model generation
- **`builder_gui.py`**: SafeModelBuilderGUI class and main interface function
- **`build_fem_config.py`**: Comprehensive configuration with 70+ constants for:
  - Excel filenames and sheet names for all input/output data
  - SAFE model export formats (F2K for SAFE16/22)
  - Folder structure organization

## Soil-Structure Interaction
- **`builder_soil_spring.py`**: Advanced soil spring calculations:
  - `nearest_borehole()`, `nearest_pile()` for spatial relationships
  - `subgrade_reaction()`, `group_reduction_factor()` for soil parameters
  - `soil_spring_top()`, `soil_spring_middle()`, `soil_spring_base()` for spring distribution
  - `gen_soil_spring()` main generation function

## SAFE Model Generation (`write_safe/`)
### SAFE16 Support (`write_safe/safe16/`)
- **Material definitions**: Concrete, steel, rebar, tendon properties
- **Structural properties**: Beam sections, column sections, slab properties
- **Geometry**: Point coordinates, beam/wall geometry, slab area assignments
- **Loading**: Point loads, line loads, pile loads, load combinations
- **Pile elements**: BP/SHP/DHP pile writing with springs and restraints

### SAFE22 Support (`write_safe/safe22/`)
- **Enhanced connectivity**: Point/beam/area object connectivity with GUIDs
- **Advanced assignments**: Insertion points, auto-mesh, edge constraints
- **Load combinations**: ULS/SLS envelopes, wind combinations
- **Material updates**: Updated material property definitions

## Specialized Components
- **`builder_safe.py`**: Main `gen_safe()` function for SAFE model generation
- **`functions.py`**: Utility functions for length calculations and load transformations
- **Design strips**: Automated design strip generation for slab reinforcement
- **Load output**: Comprehensive load output formatting and export