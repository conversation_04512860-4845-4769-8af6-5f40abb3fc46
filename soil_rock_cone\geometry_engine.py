"""
Geometry Engine Module for 3D Pile Volume Analysis

This module handles creation of 3D geometries using Trimesh including:
- Pile cylinders (Part 1)
- Soil/Rock frustums (Part 2) 
- Soil cylinders (Part 3)
- Boolean operations for overlaps and clipping
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PileGeometry:
    """Container for pile geometry components."""
    pile_id: str
    part1_cylinder: trimesh.Trimesh  # Pile cylinder
    part2_frustum: trimesh.Trimesh   # Soil/Rock frustum
    part3_cylinder: trimesh.Trimesh  # Soil cylinder
    combined_mesh: trimesh.Trimesh   # Union of all parts
    

class GeometryEngine:
    """
    Handles creation and manipulation of 3D pile geometries using Trimesh.
    """
    
    def __init__(self, mesh_resolution: int = 32):
        """
        Initialize the GeometryEngine.
        
        Args:
            mesh_resolution: Number of segments for cylindrical meshes
        """
        self.mesh_resolution = mesh_resolution
        
    def create_pile_geometry(self, pile_data: pd.Series) -> PileGeometry:
        """
        Create complete 3D geometry for a single pile.
        
        Args:
            pile_data: Series containing pile parameters
            
        Returns:
            PileGeometry object with all components
        """
        pile_id = pile_data['pile_id']
        x, y = pile_data['x_coord'], pile_data['y_coord']
        top_elev = pile_data['top_elevation']
        bottom_elev = pile_data['bottom_elevation']
        radius = pile_data['radius']
        frustum_base_radius = pile_data['frustum_base_radius']
        
        # Part 1: Pile cylinder
        part1 = self._create_cylinder(
            center=(x, y, (top_elev + bottom_elev) / 2),
            radius=radius,
            height=top_elev - bottom_elev
        )
        
        # Part 2: Soil/Rock frustum (truncated cone)
        part2 = self._create_frustum(
            center=(x, y, bottom_elev),
            top_radius=radius,
            bottom_radius=frustum_base_radius,
            height=top_elev - bottom_elev
        )
        
        # Part 3: Soil cylinder (extends from bottom of pile)
        # Height extends from bottom elevation down to frustum base
        part3_height = (frustum_base_radius - radius) / np.tan(np.radians(pile_data['projection_angle']))
        part3_bottom = bottom_elev - part3_height
        
        part3 = self._create_cylinder(
            center=(x, y, (bottom_elev + part3_bottom) / 2),
            radius=frustum_base_radius,
            height=part3_height
        )
        
        # Create combined mesh (union of all parts)
        try:
            combined = self._union_meshes([part1, part2, part3])
        except Exception as e:
            logger.warning(f"Failed to create union for pile {pile_id}: {e}")
            # Fallback: use individual meshes
            combined = part1
            
        return PileGeometry(
            pile_id=pile_id,
            part1_cylinder=part1,
            part2_frustum=part2,
            part3_cylinder=part3,
            combined_mesh=combined
        )
        
    def _create_cylinder(self, center: Tuple[float, float, float], 
                        radius: float, height: float) -> trimesh.Trimesh:
        """
        Create a cylindrical mesh.
        
        Args:
            center: (x, y, z) center coordinates
            radius: Cylinder radius
            height: Cylinder height
            
        Returns:
            Trimesh cylinder object
        """
        # Create cylinder primitive
        cylinder = trimesh.creation.cylinder(
            radius=radius,
            height=height,
            sections=self.mesh_resolution
        )
        
        # Translate to correct position
        cylinder.apply_translation(center)
        
        return cylinder
        
    def _create_frustum(self, center: Tuple[float, float, float],
                       top_radius: float, bottom_radius: float, 
                       height: float) -> trimesh.Trimesh:
        """
        Create a frustum (truncated cone) mesh.
        
        Args:
            center: (x, y, z) center coordinates of bottom
            top_radius: Radius at top of frustum
            bottom_radius: Radius at bottom of frustum
            height: Frustum height
            
        Returns:
            Trimesh frustum object
        """
        # Create cone and truncate it
        # Trimesh doesn't have direct frustum, so we create from vertices
        
        # Generate vertices for top and bottom circles
        angles = np.linspace(0, 2*np.pi, self.mesh_resolution, endpoint=False)
        
        # Bottom circle vertices
        bottom_x = center[0] + bottom_radius * np.cos(angles)
        bottom_y = center[1] + bottom_radius * np.sin(angles)
        bottom_z = np.full_like(bottom_x, center[2])
        
        # Top circle vertices  
        top_x = center[0] + top_radius * np.cos(angles)
        top_y = center[1] + top_radius * np.sin(angles)
        top_z = np.full_like(top_x, center[2] + height)
        
        # Combine vertices
        vertices = np.vstack([
            np.column_stack([bottom_x, bottom_y, bottom_z]),
            np.column_stack([top_x, top_y, top_z])
        ])
        
        # Create faces
        faces = []
        n = self.mesh_resolution
        
        # Side faces (quads split into triangles)
        for i in range(n):
            next_i = (i + 1) % n
            # Bottom triangle
            faces.append([i, next_i, n + i])
            # Top triangle
            faces.append([next_i, n + next_i, n + i])
            
        # Bottom face (fan triangulation)
        for i in range(1, n - 1):
            faces.append([0, i, i + 1])
            
        # Top face (fan triangulation)
        for i in range(1, n - 1):
            faces.append([n, n + i + 1, n + i])
            
        return trimesh.Trimesh(vertices=vertices, faces=faces)
        
    def _union_meshes(self, meshes: List[trimesh.Trimesh]) -> trimesh.Trimesh:
        """
        Compute union of multiple meshes.
        
        Args:
            meshes: List of Trimesh objects to union
            
        Returns:
            Union mesh
        """
        if not meshes:
            raise ValueError("No meshes provided for union")
            
        if len(meshes) == 1:
            return meshes[0]
            
        # Start with first mesh
        result = meshes[0]
        
        # Sequentially union with remaining meshes
        for mesh in meshes[1:]:
            try:
                result = result.union(mesh)
            except Exception as e:
                logger.warning(f"Union operation failed: {e}")
                # Continue with current result
                
        return result
        
    def create_all_pile_geometries(self, pile_data: pd.DataFrame) -> Dict[str, PileGeometry]:
        """
        Create geometries for all piles in the dataset.
        
        Args:
            pile_data: DataFrame with pile information
            
        Returns:
            Dictionary mapping pile_id to PileGeometry
        """
        geometries = {}
        
        for idx, pile in pile_data.iterrows():
            try:
                geometry = self.create_pile_geometry(pile)
                geometries[pile['pile_id']] = geometry
                logger.debug(f"Created geometry for pile {pile['pile_id']}")
            except Exception as e:
                logger.error(f"Failed to create geometry for pile {pile['pile_id']}: {e}")
                
        logger.info(f"Successfully created geometries for {len(geometries)} piles")
        return geometries
        
    def compute_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """
        Compute volume of a mesh.
        
        Args:
            mesh: Trimesh object
            
        Returns:
            Volume in cubic units
        """
        try:
            # Ensure mesh is watertight for accurate volume calculation
            if not mesh.is_watertight:
                logger.warning("Mesh is not watertight, volume may be inaccurate")
                
            return float(mesh.volume)
        except Exception as e:
            logger.error(f"Error computing mesh volume: {e}")
            return 0.0
            
    def compute_intersection_volume(self, mesh1: trimesh.Trimesh, 
                                  mesh2: trimesh.Trimesh) -> float:
        """
        Compute volume of intersection between two meshes.
        
        Args:
            mesh1: First mesh
            mesh2: Second mesh
            
        Returns:
            Intersection volume
        """
        try:
            intersection = mesh1.intersection(mesh2)
            return self.compute_mesh_volume(intersection)
        except Exception as e:
            logger.warning(f"Error computing intersection volume: {e}")
            return 0.0
            
    def validate_mesh(self, mesh: trimesh.Trimesh) -> bool:
        """
        Validate mesh integrity.
        
        Args:
            mesh: Trimesh object to validate
            
        Returns:
            True if mesh is valid
        """
        try:
            # Check basic properties
            if mesh.vertices is None or len(mesh.vertices) == 0:
                return False
            if mesh.faces is None or len(mesh.faces) == 0:
                return False
                
            # Check for degenerate faces
            if mesh.area == 0:
                return False
                
            return True
        except Exception:
            return False
