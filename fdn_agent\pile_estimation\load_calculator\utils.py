﻿"""
Utility functions for load calculations.
"""

from typing import List, <PERSON><PERSON>, Dict, Any, Union
import pandas as pd
import numpy as np
import logging

from ..exceptions import LoadCalculationError, InputDataError
from ..utils.logging_utils import (
    enhanced_log, 
    log_function_entry, 
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    setup_module_logger
)

# Set up module logger
logger = setup_module_logger(__name__)


def calculate_load_with_custom_types(df: pd.DataFrame, element_name: str, 
                                    load_types: List[str], log_callback=None) -> Tuple[Dict[str, float], List[str]]:
    """
    Calculate loads for custom load type combinations.
    
    Args:
        df: Multi-index DataFrame with load data
        element_name: Name of the element
        load_types: List of load types to include
        log_callback: Optional callback for logging calculation progress
        
    Returns:
        Tuple of (result_dict, warnings) where result_dict contains individual 
        load type values and total
        
    Raises:
        InputDataError: If input parameters are invalid
        LoadCalculationError: If load calculation fails
    """
    # Log function entry
    log_function_entry(
        logger, 
        'calculate_load_with_custom_types',
        element_name=element_name,
        load_types=load_types,
        dataframe_shape=df.shape if not df.empty else "empty"
    )
    
    enhanced_log(log_callback, f"Starting custom load calculation for element '{element_name}' with types {load_types}", 'INFO')
    
    try:
        # Input validation with enhanced logging
        if df.empty:
            error_msg = "Empty DataFrame provided for custom load calculation"
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name,
                load_types=load_types
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise InputDataError(error_msg)
            
        if not element_name or not element_name.strip():
            error_msg = "Invalid element name provided for custom load calculation"
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name,
                load_types=load_types
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise InputDataError(error_msg)
            
        if not load_types:
            error_msg = "No load types specified for custom load calculation"
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise InputDataError(error_msg)
        
        # Log validation success
        log_validation_result(
            logger,
            True,
            "Input validation passed for custom load calculation",
            element_name=element_name,
            load_types_count=len(load_types),
            dataframe_columns=len(df.columns)
        )
        
        enhanced_log(log_callback, f"Processing {len(load_types)} custom load types for element '{element_name}'", 'DEBUG')
        logger.debug(f"DataFrame shape: {df.shape}, Columns: {len(df.columns)}")
        
        result = {}
        total = 0.0
        warnings = []
        
        # Find element mark column
        enhanced_log(log_callback, f"Searching for element mark column in DataFrame with {len(df.columns)} columns", 'DEBUG')
        logger.debug("Starting element mark column search")
        
        mark_col = _find_element_mark_column(df)
        if not mark_col:
            error_msg = "No element mark column found in DataFrame for custom load calculation"
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name,
                dataframe_columns=list(df.columns)[:10]
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise LoadCalculationError(error_msg)
        
        enhanced_log(log_callback, f"Found element mark column: {mark_col}", 'DEBUG')
        logger.debug(f"Element mark column identified: {mark_col}")
        
        # Find element row
        enhanced_log(log_callback, f"Searching for element '{element_name}' in DataFrame", 'DEBUG')
        logger.debug(f"Searching for element '{element_name}' in mark column")
        
        element_marks = df[mark_col]
        matching_rows = element_marks == element_name
        if not matching_rows.any():
            error_msg = f"Element '{element_name}' not found in DataFrame for custom load calculation"
            available_elements = element_marks.dropna().unique()[:10]
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name,
                available_elements=list(available_elements)
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            enhanced_log(log_callback, f"Available elements (first 10): {list(available_elements)}", 'DEBUG')
            raise LoadCalculationError(error_msg)
        
        element_row_index = matching_rows.idxmax()
        element_row = df.loc[element_row_index]
        enhanced_log(log_callback, f"Found element '{element_name}' at row index {element_row_index}", 'DEBUG')
        logger.debug(f"Element '{element_name}' located at row index {element_row_index}")
        
        # Get available load types
        enhanced_log(log_callback, "Identifying available load types in DataFrame", 'DEBUG')
        logger.debug("Starting load type identification")
        
        available_load_types = _get_available_load_types(df)
        enhanced_log(log_callback, f"Available load types: {list(available_load_types)}", 'DEBUG')
        logger.debug(f"Available load types identified: {list(available_load_types)}")
        
        log_validation_result(
            logger,
            len(available_load_types) > 0,
            f"Load types availability check",
            available_count=len(available_load_types),
            requested_count=len(load_types)
        )
        
        # Process each requested load type
        loads_found = False
        processed_loads = []
        
        for i, load_type in enumerate(load_types):
            enhanced_log(log_callback, f"Processing custom load type {i+1}/{len(load_types)}: '{load_type}'", 'DEBUG')
            logger.debug(f"Processing load type {i+1}/{len(load_types)}: '{load_type}'")
            
            load_value = 0.0
            
            if load_type in available_load_types:
                logger.debug(f"Load type '{load_type}' found in available types")
                extracted_value = _extract_load_value(df, element_row, load_type)
                if extracted_value is not None:
                    load_value = extracted_value
                    loads_found = True
                    processed_loads.append(f"{load_type}: {load_value:.2f} kN")
                    enhanced_log(log_callback, f"Custom load '{load_type}': {load_value:.2f} kN", 'DEBUG')
                    logger.debug(f"Load value extracted for '{load_type}': {load_value:.2f} kN")
                else:
                    warning_msg = f"No valid load found for type {load_type}"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                    logger.warning(f"No valid load value found for type '{load_type}'")
            else:
                warning_msg = f"Load type {load_type} not available in DataFrame"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                logger.warning(f"Load type '{load_type}' not found in available types")
                
            result[load_type] = load_value
            total += load_value
            logger.debug(f"Load type '{load_type}' processed with value {load_value:.2f} kN, running total: {total:.2f} kN")
        
        # Validate that loads were found
        log_constraint_check(
            logger,
            loads_found,
            f"At least one valid load must be found",
            element_name=element_name,
            load_types=load_types,
            loads_processed=len(processed_loads)
        )
        
        if not loads_found:
            error_msg = f"No valid loads found for element '{element_name}' with custom load types {load_types}"
            log_error_with_context(
                logger,
                error_msg,
                function_name='calculate_load_with_custom_types',
                element_name=element_name,
                load_types=load_types,
                available_load_types=list(available_load_types)
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise LoadCalculationError(error_msg)
        
        result['Total'] = total
        
        # Log calculation results
        log_calculation_result(
            logger,
            f"Custom load calculation completed for '{element_name}'",
            total_load=total,
            load_types_processed=len(load_types),
            valid_loads_found=len(processed_loads),
            warnings_count=len(warnings)
        )
        
        enhanced_log(log_callback, f"Custom load calculation completed for '{element_name}':", 'INFO')
        enhanced_log(log_callback, f"  Total load: {total:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"  Load breakdown: {', '.join([f'{k}: {v:.2f} kN' for k, v in result.items() if k != 'Total'])}", 'DEBUG')
        
        if warnings:
            enhanced_log(log_callback, f"Custom load calculation completed with {len(warnings)} warnings", 'WARNING')
            logger.warning(f"Calculation completed with {len(warnings)} warnings: {warnings}")
        
        # Log function exit
        log_function_exit(
            logger,
            'calculate_load_with_custom_types',
            total_load=total,
            load_types_processed=len(load_types),
            warnings_count=len(warnings)
        )
        
        return result, warnings
        
    except Exception as e:
        log_error_with_context(
            logger,
            f"Unexpected error in custom load calculation: {str(e)}",
            function_name='calculate_load_with_custom_types',
            element_name=element_name,
            load_types=load_types,
            error_type=type(e).__name__
        )
        raise


def get_load_summary(df: pd.DataFrame, element_name: str, log_callback=None) -> Tuple[Dict[str, float], List[str]]:
    """
    Get a summary of all available loads for an element.
    
    Args:
        df: Multi-index DataFrame with load data
        element_name: Name of the element
        log_callback: Optional callback for logging summary generation progress
        
    Returns:
        Tuple of (load_summary, warnings)
        
    Raises:
        InputDataError: If input parameters are invalid
        LoadCalculationError: If load calculation fails
    """
    # Log function entry
    log_function_entry(
        logger,
        'get_load_summary',
        element_name=element_name,
        dataframe_shape=df.shape if not df.empty else "empty"
    )
    
    enhanced_log(log_callback, f"Starting load summary generation for element '{element_name}'", 'INFO')
    
    try:
        # Input validation with enhanced logging
        if df.empty:
            error_msg = "Empty DataFrame provided for load summary"
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise InputDataError(error_msg)
            
        if not element_name or not element_name.strip():
            error_msg = "Invalid element name provided for load summary"
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise InputDataError(error_msg)
        
        # Log validation success
        log_validation_result(
            logger,
            True,
            "Input validation passed for load summary",
            element_name=element_name,
            dataframe_columns=len(df.columns)
        )
        
        warnings = []
        load_summary = {}
        
        # Find element mark column
        enhanced_log(log_callback, f"Searching for element mark column in DataFrame with {len(df.columns)} columns", 'DEBUG')
        logger.debug("Starting element mark column search for load summary")
        
        mark_col = _find_element_mark_column(df)
        if not mark_col:
            error_msg = "No element mark column found in DataFrame for load summary"
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name,
                dataframe_columns=list(df.columns)[:10]
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise LoadCalculationError(error_msg)
        
        enhanced_log(log_callback, f"Found element mark column: {mark_col}", 'DEBUG')
        logger.debug(f"Element mark column identified for summary: {mark_col}")
        
        # Find element row
        enhanced_log(log_callback, f"Searching for element '{element_name}' in DataFrame", 'DEBUG')
        logger.debug(f"Searching for element '{element_name}' for load summary")
        
        element_marks = df[mark_col]
        matching_rows = element_marks == element_name
        if not matching_rows.any():
            error_msg = f"Element '{element_name}' not found in DataFrame for load summary"
            available_elements = element_marks.dropna().unique()[:10]
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name,
                available_elements=list(available_elements)
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            enhanced_log(log_callback, f"Available elements (first 10): {list(available_elements)}", 'DEBUG')
            raise LoadCalculationError(error_msg)
        
        element_row_index = matching_rows.idxmax()
        element_row = df.loc[element_row_index]
        enhanced_log(log_callback, f"Found element '{element_name}' at row index {element_row_index}", 'DEBUG')
        logger.debug(f"Element '{element_name}' located at row index {element_row_index} for summary")
        
        # Get all available load types
        enhanced_log(log_callback, "Identifying all available load types in DataFrame", 'DEBUG')
        logger.debug("Starting comprehensive load type identification")
        
        available_load_types = _get_available_load_types(df)
        
        if not available_load_types:
            error_msg = "No load types found in DataFrame for load summary"
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name,
                dataframe_columns=list(df.columns)[:10]
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise LoadCalculationError(error_msg)
        
        enhanced_log(log_callback, f"Processing load summary for {len(available_load_types)} load types: {list(available_load_types)}", 'INFO')
        logger.info(f"Load summary will process {len(available_load_types)} load types")
        
        log_validation_result(
            logger,
            len(available_load_types) > 0,
            f"Load types found for summary generation",
            available_count=len(available_load_types),
            element_name=element_name
        )
        
        total_load = 0.0
        loads_found = False
        processed_loads = []
        
        for i, load_type in enumerate(available_load_types):
            enhanced_log(log_callback, f"Processing load type {i+1}/{len(available_load_types)}: '{load_type}'", 'DEBUG')
            logger.debug(f"Processing summary load type {i+1}/{len(available_load_types)}: '{load_type}'")
            
            load_value = _extract_load_value(df, element_row, load_type)
            if load_value is not None:
                load_summary[load_type] = load_value
                total_load += load_value
                loads_found = True
                processed_loads.append(f"{load_type}: {load_value:.2f} kN")
                enhanced_log(log_callback, f"Load summary '{load_type}': {load_value:.2f} kN", 'DEBUG')
                logger.debug(f"Summary load value for '{load_type}': {load_value:.2f} kN")
            else:
                load_summary[load_type] = 0.0
                warning_msg = f"No valid load found for type {load_type}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                logger.warning(f"No valid load value found for summary type '{load_type}'")
        
        # Validate that loads were found
        log_constraint_check(
            logger,
            loads_found,
            f"At least one valid load must be found for summary",
            element_name=element_name,
            total_load_types=len(available_load_types),
            valid_loads_found=len(processed_loads)
        )
        
        if not loads_found:
            error_msg = f"No valid loads found for element '{element_name}' in load summary"
            log_error_with_context(
                logger,
                error_msg,
                function_name='get_load_summary',
                element_name=element_name,
                available_load_types=list(available_load_types)
            )
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            raise LoadCalculationError(error_msg)
        
        load_summary['Total'] = total_load
        
        # Log calculation results
        log_calculation_result(
            logger,
            f"Load summary generation completed for '{element_name}'",
            total_load=total_load,
            load_types_processed=len(available_load_types),
            valid_loads_found=len(processed_loads),
            warnings_count=len(warnings)
        )
        
        # Log performance metrics
        log_performance_metric(
            logger,
            "load_summary_generation",
            total_load_types=len(available_load_types),
            valid_loads_found=len(processed_loads),
            success_rate=len(processed_loads) / len(available_load_types) * 100
        )
        
        enhanced_log(log_callback, f"Load summary generation completed for '{element_name}':", 'INFO')
        enhanced_log(log_callback, f"  Total load types processed: {len(available_load_types)}", 'INFO')
        enhanced_log(log_callback, f"  Total load: {total_load:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"  Load breakdown: {', '.join(processed_loads)}", 'DEBUG')
        
        if warnings:
            enhanced_log(log_callback, f"Load summary generation completed with {len(warnings)} warnings", 'WARNING')
            logger.warning(f"Summary completed with {len(warnings)} warnings: {warnings}")
        
        # Log function exit
        log_function_exit(
            logger,
            'get_load_summary',
            total_load=total_load,
            load_types_processed=len(available_load_types),
            warnings_count=len(warnings)
        )
        
        return load_summary, warnings
        
    except Exception as e:
        log_error_with_context(
            logger,
            f"Unexpected error in load summary generation: {str(e)}",
            function_name='get_load_summary',
            element_name=element_name,
            error_type=type(e).__name__
        )
        raise


def _find_element_mark_column(df: pd.DataFrame):
    """Find the element mark column in multi-index DataFrame."""
    # Log function entry
    log_function_entry(
        logger,
        '_find_element_mark_column',
        dataframe_shape=df.shape if not df.empty else "empty"
    )
    
    try:
        if df.empty:
            logger.debug("DataFrame is empty, returning None for mark column")
            log_function_exit(logger, '_find_element_mark_column', result=None)
            return None
        
        logger.debug(f"Searching for element mark column in {len(df.columns)} columns")
        
        for i, col in enumerate(df.columns):
            logger.debug(f"Examining column {i+1}/{len(df.columns)}: {col}")
            if isinstance(col, tuple) and len(col) >= 2:
                if (col[0] in ['Wall Data', 'Column Data'] and 'Mark' in str(col[1])):
                    logger.debug(f"Element mark column found: {col}")
                    log_function_exit(logger, '_find_element_mark_column', result=col)
                    return col
        
        logger.debug("No element mark column found")
        log_function_exit(logger, '_find_element_mark_column', result=None)
        return None
        
    except Exception as e:
        log_error_with_context(
            logger,
            f"Error finding element mark column: {str(e)}",
            function_name='_find_element_mark_column',
            error_type=type(e).__name__
        )
        raise


def _get_available_load_types(df: pd.DataFrame) -> set:
    """Get all available load types from DataFrame columns."""
    # Log function entry
    log_function_entry(
        logger,
        '_get_available_load_types',
        dataframe_shape=df.shape if not df.empty else "empty"
    )
    
    try:
        if df.empty:
            logger.debug("DataFrame is empty, returning empty set for load types")
            log_function_exit(logger, '_get_available_load_types', result_count=0)
            return set()
        
        logger.debug(f"Analyzing {len(df.columns)} columns for load types")
        
        available_load_types = set()
        for i, col in enumerate(df.columns):
            logger.debug(f"Examining column {i+1}/{len(df.columns)} for load types: {col}")
            if isinstance(col, tuple) and len(col) >= 2:
                if 'Axial (kN)' in str(col[1]):
                    load_type = col[0]
                    if load_type not in ['Wall Data', 'Column Data']:
                        available_load_types.add(load_type)
                        logger.debug(f"Load type found: {load_type}")
        
        logger.debug(f"Found {len(available_load_types)} load types: {list(available_load_types)}")
        log_function_exit(logger, '_get_available_load_types', result_count=len(available_load_types))
        return available_load_types
        
    except Exception as e:
        log_error_with_context(
            logger,
            f"Error getting available load types: {str(e)}",
            function_name='_get_available_load_types',
            error_type=type(e).__name__
        )
        raise


def _extract_load_value(df: pd.DataFrame, element_row, load_type: str):
    """Extract load value for specific load type from element row."""
    # Log function entry
    log_function_entry(
        logger,
        '_extract_load_value',
        load_type=load_type,
        dataframe_shape=df.shape if not df.empty else "empty"
    )
    
    try:
        if df.empty:
            logger.debug("DataFrame is empty, returning None for load value")
            log_function_exit(logger, '_extract_load_value', result=None)
            return None
        
        logger.debug(f"Extracting load value for type '{load_type}'")
        
        # Try standard axial column format
        axial_col = (load_type, 'Axial (kN)')
        logger.debug(f"Checking standard axial column format: {axial_col}")
        
        if axial_col in df.columns:
            value = element_row[axial_col]
            logger.debug(f"Raw value from standard column: {value}")
            if pd.notna(value) and value != '':
                try:
                    float_value = float(value)
                    logger.debug(f"Successfully extracted load value: {float_value}")
                    log_function_exit(logger, '_extract_load_value', result=float_value)
                    return float_value
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to convert value to float: {e}")
        
        # Try alternative column formats
        logger.debug("Trying alternative column formats")
        for col in df.columns:
            logger.debug(f"Checking alternative column: {col}")
            if (isinstance(col, tuple) and len(col) >= 2 and
                str(col[0]).strip() == load_type and 'Axial' in str(col[1])):
                value = element_row[col]
                logger.debug(f"Raw value from alternative column: {value}")
                if pd.notna(value) and value != '':
                    try:
                        float_value = float(value)
                        logger.debug(f"Successfully extracted load value from alternative format: {float_value}")
                        log_function_exit(logger, '_extract_load_value', result=float_value)
                        return float_value
                    except (ValueError, TypeError) as e:
                        logger.debug(f"Failed to convert alternative value to float: {e}")
                        continue
        
        logger.debug(f"No valid load value found for type '{load_type}'")
        log_function_exit(logger, '_extract_load_value', result=None)
        return None
        
    except Exception as e:
        log_error_with_context(
            logger,
            f"Error extracting load value: {str(e)}",
            function_name='_extract_load_value',
            load_type=load_type,
            error_type=type(e).__name__
        )
        raise

