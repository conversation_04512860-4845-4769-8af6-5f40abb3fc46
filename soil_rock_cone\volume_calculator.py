"""
Volume Calculator Module for 3D Pile Volume Analysis

This module handles volume calculations for individual pile components
and manages volume distribution for overlapping geometries.
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass

from .geometry_engine import PileGeometry

logger = logging.getLogger(__name__)


@dataclass
class VolumeResult:
    """Container for volume calculation results."""
    pile_id: str
    part1_volume: float  # Pile cylinder volume
    part2_volume: float  # Frustum volume
    part3_volume: float  # Soil cylinder volume
    total_volume: float  # Total volume
    clipped_volume: float  # Volume after boundary clipping
    overlap_volume: float  # Volume lost to overlaps
    final_volume: float  # Final assigned volume after overlap distribution


class VolumeCalculator:
    """
    Handles volume calculations and distribution for pile geometries.
    """
    
    def __init__(self):
        """Initialize the VolumeCalculator."""
        pass
        
    def calculate_pile_volume(self, geometry: PileGeometry, 
                            is_clipped: bool = False) -> VolumeResult:
        """
        Calculate volumes for all components of a pile geometry.
        
        Args:
            geometry: Pile geometry object
            is_clipped: Whether geometry has been clipped to boundary
            
        Returns:
            VolumeResult with all volume calculations
        """
        try:
            # Calculate individual component volumes
            part1_vol = self._calculate_mesh_volume(geometry.part1_cylinder)
            part2_vol = self._calculate_mesh_volume(geometry.part2_frustum)
            part3_vol = self._calculate_mesh_volume(geometry.part3_cylinder)
            
            # Calculate total volume
            total_vol = part1_vol + part2_vol + part3_vol
            
            # Calculate combined mesh volume (may differ due to overlaps within pile)
            combined_vol = self._calculate_mesh_volume(geometry.combined_mesh)
            
            # Use combined volume as clipped volume if geometry is clipped
            clipped_vol = combined_vol if is_clipped else total_vol
            
            return VolumeResult(
                pile_id=geometry.pile_id,
                part1_volume=part1_vol,
                part2_volume=part2_vol,
                part3_volume=part3_vol,
                total_volume=total_vol,
                clipped_volume=clipped_vol,
                overlap_volume=0.0,  # Will be calculated later
                final_volume=clipped_vol  # Initial assignment
            )
            
        except Exception as e:
            logger.error(f"Error calculating volume for pile {geometry.pile_id}: {e}")
            return VolumeResult(
                pile_id=geometry.pile_id,
                part1_volume=0.0,
                part2_volume=0.0,
                part3_volume=0.0,
                total_volume=0.0,
                clipped_volume=0.0,
                overlap_volume=0.0,
                final_volume=0.0
            )
            
    def _calculate_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """
        Calculate volume of a mesh with error handling.
        
        Args:
            mesh: Trimesh object
            
        Returns:
            Volume in cubic units
        """
        try:
            if mesh is None or mesh.vertices is None or len(mesh.vertices) == 0:
                return 0.0
                
            # Check if mesh is watertight
            if not mesh.is_watertight:
                logger.warning("Mesh is not watertight, attempting repair")
                mesh = mesh.fill_holes()
                
            volume = float(mesh.volume)
            
            # Ensure positive volume
            if volume < 0:
                logger.warning("Negative volume detected, taking absolute value")
                volume = abs(volume)
                
            return volume
            
        except Exception as e:
            logger.warning(f"Error calculating mesh volume: {e}")
            return 0.0
            
    def calculate_all_volumes(self, geometries: Dict[str, PileGeometry], 
                            is_clipped: bool = False) -> Dict[str, VolumeResult]:
        """
        Calculate volumes for all pile geometries.
        
        Args:
            geometries: Dictionary of pile geometries
            is_clipped: Whether geometries have been clipped
            
        Returns:
            Dictionary mapping pile_id to VolumeResult
        """
        volume_results = {}
        
        for pile_id, geometry in geometries.items():
            volume_results[pile_id] = self.calculate_pile_volume(geometry, is_clipped)
            
        logger.info(f"Calculated volumes for {len(volume_results)} piles")
        return volume_results
        
    def distribute_overlap_volumes(self, volume_results: Dict[str, VolumeResult],
                                 overlap_data: Dict[Tuple[str, str], float]) -> Dict[str, VolumeResult]:
        """
        Distribute overlapping volumes among contributing piles.
        
        Args:
            volume_results: Current volume results
            overlap_data: Dictionary mapping pile pairs to overlap volumes
            
        Returns:
            Updated volume results with overlap distribution
        """
        # Create copy of results to modify
        updated_results = {k: VolumeResult(**v.__dict__) for k, v in volume_results.items()}
        
        # Track total overlap volume per pile
        pile_overlaps = {pile_id: 0.0 for pile_id in volume_results.keys()}
        
        # Process each overlap
        for (pile1_id, pile2_id), overlap_volume in overlap_data.items():
            if overlap_volume <= 0:
                continue
                
            # Get volumes of contributing piles
            vol1 = volume_results[pile1_id].clipped_volume
            vol2 = volume_results[pile2_id].clipped_volume
            
            if vol1 + vol2 == 0:
                continue
                
            # Distribute overlap proportionally
            ratio1 = vol1 / (vol1 + vol2)
            ratio2 = vol2 / (vol1 + vol2)
            
            overlap1 = overlap_volume * ratio1
            overlap2 = overlap_volume * ratio2
            
            # Update overlap tracking
            pile_overlaps[pile1_id] += overlap1
            pile_overlaps[pile2_id] += overlap2
            
        # Update final volumes
        for pile_id, total_overlap in pile_overlaps.items():
            result = updated_results[pile_id]
            result.overlap_volume = total_overlap
            result.final_volume = max(0.0, result.clipped_volume - total_overlap)
            
        logger.info(f"Distributed overlap volumes for {len(overlap_data)} overlaps")
        return updated_results
        
    def calculate_theoretical_volumes(self, pile_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate theoretical volumes using analytical formulas.
        
        Args:
            pile_data: DataFrame with pile parameters
            
        Returns:
            DataFrame with theoretical volume calculations
        """
        result = pile_data.copy()
        
        # Part 1: Pile cylinder volume
        result['part1_theoretical'] = (
            np.pi * result['radius']**2 * result['pile_length']
        )
        
        # Part 2: Frustum volume (truncated cone)
        # V = (π * h / 3) * (r1² + r1*r2 + r2²)
        r1 = result['radius']  # Top radius
        r2 = result['frustum_base_radius']  # Bottom radius
        h = result['pile_length']
        
        result['part2_theoretical'] = (
            np.pi * h / 3 * (r1**2 + r1*r2 + r2**2)
        )
        
        # Part 3: Soil cylinder volume
        # Height is calculated from projection angle
        part3_height = (r2 - r1) / np.tan(np.radians(result['projection_angle']))
        result['part3_theoretical'] = np.pi * r2**2 * part3_height
        
        # Total theoretical volume
        result['total_theoretical'] = (
            result['part1_theoretical'] + 
            result['part2_theoretical'] + 
            result['part3_theoretical']
        )
        
        return result
        
    def validate_volume_calculations(self, volume_results: Dict[str, VolumeResult],
                                   theoretical_volumes: pd.DataFrame,
                                   tolerance: float = 0.05) -> Dict[str, bool]:
        """
        Validate calculated volumes against theoretical values.
        
        Args:
            volume_results: Calculated volume results
            theoretical_volumes: DataFrame with theoretical calculations
            tolerance: Relative tolerance for validation
            
        Returns:
            Dictionary mapping pile_id to validation status
        """
        validation_results = {}
        
        for pile_id, result in volume_results.items():
            try:
                # Find corresponding theoretical values
                theoretical_row = theoretical_volumes[
                    theoretical_volumes['pile_id'] == pile_id
                ].iloc[0]
                
                # Compare total volumes
                calculated = result.total_volume
                theoretical = theoretical_row['total_theoretical']
                
                if theoretical == 0:
                    validation_results[pile_id] = calculated == 0
                else:
                    relative_error = abs(calculated - theoretical) / theoretical
                    validation_results[pile_id] = relative_error <= tolerance
                    
                    if relative_error > tolerance:
                        logger.warning(
                            f"Volume validation failed for pile {pile_id}: "
                            f"calculated={calculated:.3f}, theoretical={theoretical:.3f}, "
                            f"error={relative_error:.3%}"
                        )
                        
            except Exception as e:
                logger.error(f"Error validating volume for pile {pile_id}: {e}")
                validation_results[pile_id] = False
                
        passed = sum(validation_results.values())
        total = len(validation_results)
        logger.info(f"Volume validation: {passed}/{total} piles passed")
        
        return validation_results
        
    def get_volume_summary(self, volume_results: Dict[str, VolumeResult]) -> Dict[str, float]:
        """
        Generate summary statistics for volume calculations.
        
        Args:
            volume_results: Volume calculation results
            
        Returns:
            Dictionary with summary statistics
        """
        if not volume_results:
            return {}
            
        # Extract volumes
        total_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        overlap_volumes = [r.overlap_volume for r in volume_results.values()]
        
        return {
            'total_piles': len(volume_results),
            'total_volume_sum': sum(total_volumes),
            'final_volume_sum': sum(final_volumes),
            'total_overlap_volume': sum(overlap_volumes),
            'average_pile_volume': np.mean(total_volumes),
            'max_pile_volume': max(total_volumes),
            'min_pile_volume': min(total_volumes),
            'volume_efficiency': sum(final_volumes) / sum(total_volumes) if sum(total_volumes) > 0 else 0
        }
