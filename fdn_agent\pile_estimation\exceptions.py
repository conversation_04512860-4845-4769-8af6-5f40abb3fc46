﻿"""
Custom exceptions for the pile estimation system.

This module defines custom exceptions used throughout the pile estimation system
to provide more meaningful error messages and handle specific error conditions.
"""

class PileEstimationError(Exception):
    """Base class for all pile estimation exceptions."""
    pass

class GroupingError(PileEstimationError):
    """Raised when there's an error during element grouping."""
    pass

class GeometryError(PileEstimationError):
    """Raised when there's an error in geometric calculations."""
    pass

class LoadCalculationError(PileEstimationError):
    """Raised when there's an error in load calculations."""
    pass

class PileLayoutError(PileEstimationError):
    """Raised when there's an error in pile layout generation."""
    pass

class PileTypeSelectionError(PileEstimationError):
    """Raised when there's an error in pile type selection."""
    pass

class ValidationError(PileEstimationError):
    """Raised when validation of input data or results fails."""
    pass

class InputDataError(PileEstimationError):
    """Raised when there's an issue with the input data."""
    pass

class ConfigurationError(PileEstimationError):
    """Raised when there's an issue with the configuration."""
    pass

class OptimizationError(PileEstimationError):
    """Raised when an optimization process fails to converge or find a solution."""
    pass

class VisualizationError(PileEstimationError):
    """Raised when there's an error during visualization."""
    pass

class FileOperationError(PileEstimationError):
    """Raised when there's an error during file operations."""
    pass

class PileCapGeometryError(PileEstimationError):
    """Raised when there's an error in pile cap geometry operations."""
    pass

