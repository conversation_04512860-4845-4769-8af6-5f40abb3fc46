"""
SAFE 22 Structural Properties Module

This module provides functions for handling structural property definitions in
SAFE 22 finite element models. It manages the conversion of structural section
data from Excel input formats to SAFE 22 DataFrame structures.

The module handles:
- Beam section properties (rectangular concrete, steel I-sections)
- Column section properties (circular concrete, steel sections)
- Slab property definitions (thickness, material, modifiers)
- Section modifiers and design parameters
- Material assignments for structural elements
- Geometric properties and dimensions

Functions:
    Beam Sections:
        - beam_FrameSecDefConcRect_safe22: Create rectangular concrete beam sections

    Column Sections:
        - bp_FrameSecDefConcCircle_safe22: Create circular concrete column sections
        - hp_FrameSecDefSteelI_safe22: Create steel I-section columns

    Slab Sections:
        - slab_SlabPropertyDefinitions_safe22: Create slab property definitions

"""

import pandas as pd
from typing import Tuple, Any, Optional, Dict, List


def beam_FrameSecDefConcRect_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create rectangular concrete beam section definitions for SAFE 22 model.

    Processes beam property data from Excel inputs and creates SAFE 22 compatible
    rectangular concrete section definitions. Sets up all required properties
    including dimensions, materials, reinforcement, and modifiers.

    """    
    # Create working copy to avoid modifying original data
    df_beam_prop = excel_inputs.BeamProp.copy()

    # Set uniform width for rectangular sections (top width = bottom width)
    # This creates standard rectangular beams rather than tapered sections
    df_beam_prop['WidthTop (m)'] = df_beam_prop['Width (m)']
    df_beam_prop['WidthBot (m)'] = df_beam_prop['Width (m)']

    # Extract relevant columns for rectangular section property definition
    df_beam_prop = df_beam_prop[['Beam Prop', 'Material',
                               'Depth (m)', 'WidthTop (m)', 'WidthBot (m)']].copy()

    # Create SAFE22 format DataFrame with all required properties
    beam_props_22 = pd.DataFrame({
        'Name': df_beam_prop['Beam Prop'],
        'Material': df_beam_prop['Material'],
        'From File?': 'No',  # User-defined sections
        'Depth': df_beam_prop['Depth (m)'] * 1000,  # Convert to mm
        'Width': df_beam_prop['WidthTop (m)'] * 1000,  # Convert to mm
        'Rigid Zone?': 'No',  # No rigid zones by default
        'Column Drop Panel?': 'No',  # Not applicable for beams
        'Include Column Capital?': 'No',  # Not applicable for beams
        'Notional Size Type': 'User',  # User-defined notional size
        'Notional User Size': 100,  # Default notional size in mm
        'Section Type': 'Beam',  # Section classification
        'Longitudinal Rebar Material': 'REBAR13',  # Default rebar material
        'Shear Rebar Material': 'REBAR13',  # Default shear rebar material
        'Flange Dimension Option': 'Analysis Property',  # Use analysis properties
        'Cover Top': 50,  # Top concrete cover in mm
        'Cover Bottom': 50,  # Bottom concrete cover in mm
        'Area Modifier': 1,  # No area modification
        'As2 Modifier': 1,  # No shear area modification
        'As3 Modifier': 1,  # No shear area modification
        'J Modifier': 1,  # No torsional constant modification
        'I22 Modifier': 1,  # No moment of inertia modification
        'I33 Modifier': 1,  # No moment of inertia modification
        'Mass Modifier': 1,  # No mass modification
        'Weight Modifier': 1,  # No weight modification
        'Color': 'Red',  # Visualization color
        'GUID': None,  # Will be populated if needed
        'Notes': None  # No notes by default
    })    # Get column structure from target SAFE 22 DataFrame
    target_columns = safe22_dfs.FrameSecDefConcRect.columns.tolist()

    # Create new DataFrame with appropriate multi-level column structure for SAFE 22
    df_append = pd.DataFrame(
        beam_props_22.values,
        columns=pd.MultiIndex.from_tuples(target_columns)
    )

    # Append to existing rectangular concrete section definitions
    safe22_dfs.FrameSecDefConcRect = pd.concat(
        [safe22_dfs.FrameSecDefConcRect, df_append],
        ignore_index=True
    )

    return safe22_dfs, df_append


def bp_FrameSecDefConcCircle_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create circular concrete column section definitions for SAFE 22 model.

    Processes bore pile (BP) column property data from Excel inputs and creates
    SAFE 22 compatible circular concrete section definitions. Handles all required
    properties including diameter, materials, rigid zones, and section modifiers.

    """
    # Create working copy to avoid modifying original data
    df_bp_prop = excel_inputs.BPProp.copy()

    # Create SAFE 22 format DataFrame with all required circular section properties
    df_22 = pd.DataFrame({
        'Name': df_bp_prop['Column'],                               # Section identifier
        'Material': df_bp_prop['Material'],                        # Concrete material reference
        'From File?': 'No',                                        # User-defined sections (not from library)
        'Diameter': df_bp_prop['Diameter (m)'] * 1000,            # Convert diameter from m to mm
        'Rigid Zone?': df_bp_prop['AutoRigid (Yes/No)'],          # Rigid zone setting from input
        'Column Drop Panel?': 'No',                               # No drop panels for circular columns
        'Include Column Capital?': 'No',                          # No column capitals by default
        'Notional Size Type': 'User',                             # User-defined notional size for design
        'Notional User Size': 100,                                # Default notional size (100mm)
        'Area Modifier': 1,                                       # No cross-sectional area modification
        'As2 Modifier': 1,                                        # No shear area modification (2-direction)
        'As3 Modifier': 1,                                        # No shear area modification (3-direction)
        'J Modifier': 1,                                          # No torsional constant modification
        'I22 Modifier': 1,                                        # No moment of inertia modification (2-2 axis)
        'I33 Modifier': 1,                                        # No moment of inertia modification (3-3 axis)
        'Mass Modifier': 1,                                       # No mass modification
        'Weight Modifier': 1,                                     # No weight modification
        'Color': 'Green',                                         # Visualization color for columns
        'GUID': None,                                             # Will be generated if needed
        'Notes': None                                             # No additional notes
    })    # Get target column structure from SAFE 22 DataFrame
    target_columns = safe22_dfs.FrameSecDefConcCircle.columns.tolist()
    
    # Create DataFrame with proper multi-level column structure for SAFE 22
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing circular concrete section definitions
    safe22_dfs.FrameSecDefConcCircle = pd.concat(
        [safe22_dfs.FrameSecDefConcCircle, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def hp_FrameSecDefSteelI_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create steel I-section column definitions for SAFE 22 model.

    Processes H-pile (HP) column property data from Excel inputs and creates
    SAFE 22 compatible steel I-section definitions. Handles all geometric
    properties for doubly-symmetric I-shaped steel sections.

    """
    # Create working copy to avoid modifying original data
    df_hp_prop = excel_inputs.HPProp.copy()

    # Create SAFE 22 format DataFrame with all required steel I-section properties
    df_22 = pd.DataFrame({
        'Name': df_hp_prop['Column'],                              # Section identifier
        'Material': df_hp_prop['Material'],                       # Steel material reference
        'From File?': 'No',                                       # User-defined sections (not from library)
        'Total Depth': df_hp_prop['Total Depth (mm)'],           # Overall depth of I-section
        'Top Flange Width': df_hp_prop['Top Flange Width (mm)'], # Width of top flange
        'Top Flange Thickness': df_hp_prop['Top Flange Thickness (mm)'], # Thickness of top flange
        'Web Thickness': df_hp_prop['Web Thickness (mm)'],       # Thickness of web
        'Bottom Flange Width': df_hp_prop['Bottom Flange Width (mm)'], # Width of bottom flange
        'Bottom Flange Thickness': df_hp_prop['Bottom Flange Thickness (mm)'], # Thickness of bottom flange
        'Fillet Radius': df_hp_prop['Fillet Radius (mm)'],       # Radius at web-flange intersections
        'Area Modifier': 1,                                      # No cross-sectional area modification
        'As2 Modifier': 1,                                       # No shear area modification (2-direction)
        'As3 Modifier': 1,                                       # No shear area modification (3-direction)
        'J Modifier': 1,                                         # No torsional constant modification
        'I22 Modifier': 1,                                       # No moment of inertia modification (2-2 axis)
        'I33 Modifier': 1,                                       # No moment of inertia modification (3-3 axis)
        'Mass Modifier': 1,                                      # No mass modification
        'Weight Modifier': 1,                                    # No weight modification
        'Color': 'Green',                                        # Visualization color for steel sections
        'GUID': None,                                            # Will be generated if needed
        'Notes': None                                            # No additional notes
    })    # Get target column structure from SAFE 22 DataFrame
    target_columns = safe22_dfs.FrameSecDefSteelI.columns.tolist()
    
    # Create DataFrame with proper multi-level column structure for SAFE 22
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing steel I-section definitions
    safe22_dfs.FrameSecDefSteelI = pd.concat(
        [safe22_dfs.FrameSecDefSteelI, df_append], ignore_index=True
    )

    return safe22_dfs, df_append


def slab_SlabPropertyDefinitions_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create slab property definitions for SAFE 22 model.

    Processes slab property data from Excel inputs and creates SAFE 22 compatible
    slab section definitions. Sets up shell-thick modeling properties with all
    required stiffness parameters and material assignments.

    """
    # Create working copy to avoid modifying original data
    df_slab_prop = excel_inputs.SlabProp.copy()

    # Create SAFE 22 format DataFrame with all required slab properties
    # Note: Input columns expected are 'Slab Prop', 'Material', 'Thickness (m)'
    df_22 = pd.DataFrame({
        'Name': df_slab_prop['Slab Prop'],                        # Slab property identifier
        'Modeling Type': 'Shell-Thick',                          # Shell element with thickness (membrane + bending)
        'Property Type': 'Slab',                                 # Property classification
        'Material': df_slab_prop['Material'],                    # Concrete material reference
        'Slab Thickness': df_slab_prop['Thickness (m)'] * 1000, # Convert thickness from m to mm
        'Notional Size Type': 'User',                           # User-defined notional size for design
        'Notional User Size': 100,                              # Default notional size (100mm)
        'f11 Modifier': 1,                                      # In-plane stiffness modifier (1-1 direction)
        'f22 Modifier': 1,                                      # In-plane stiffness modifier (2-2 direction)
        'f12 Modifier': 1,                                      # In-plane stiffness modifier (1-2 coupling)
        'm11 Modifier': 1,                                      # Bending stiffness modifier (1-1 direction)
        'm22 Modifier': 1,                                      # Bending stiffness modifier (2-2 direction)
        'm12 Modifier': 1,                                      # Bending stiffness modifier (1-2 coupling)
        'v13 Modifier': 1,                                      # Transverse shear stiffness modifier (1-3 direction)
        'v23 Modifier': 1,                                      # Transverse shear stiffness modifier (2-3 direction)
        'Mass Modifier': 1,                                     # No mass modification
        'Weight Modifier': 1,                                   # No weight modification
        'Color': 'Yellow',                                      # Visualization color for slabs
        'GUID': None,                                           # Will be generated if needed
        'Notes': None,                                          # No additional notes
        'Orthotropic?': 'No',                                   # Isotropic behavior (no orthotropic properties)
        'Effective Thickness-11': None,                        # Not used for isotropic slabs
        'Effective Thickness-22': None,                        # Not used for isotropic slabs
        'Effective Thickness-12': None                         # Not used for isotropic slabs
    })    # Get target column structure from SAFE 22 DataFrame
    target_columns = safe22_dfs.SlabPropertyDefinitions.columns.tolist()
    
    # Create DataFrame with proper multi-level column structure for SAFE 22
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing slab property definitions
    safe22_dfs.SlabPropertyDefinitions = pd.concat(
        [safe22_dfs.SlabPropertyDefinitions, df_append], ignore_index=True
    )

    return safe22_dfs, df_append
