﻿"""
Pile-related type definitions
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
from .basic_types import Point2D

# Pile types
PileLocation = Point2D
PileGroup = List[PileLocation]

class PileType(Enum):
    """Enum for different pile types"""
    DHP = "DHP"  # Driven H-Pile
    SHP = "SHP"  # Socket H-Pile  
    BP = "BP"    # Bored Pile

@dataclass
class PileTypeSpec:
    """Specification for a specific pile type variant"""
    pile_type: PileType
    capacity: float  # kN
    diameter: Optional[float] = None  # m (for BP)
    section: Optional[str] = None  # section name (for DHP/SHP)
    min_spacing: float = 1.8  # m
    
    # Visualization parameters
    height: Optional[float] = None  # mm (for I-section height)
    width: Optional[float] = None   # mm (for I-section width)
    flange_thickness: Optional[float] = None  # mm
    web_thickness: Optional[float] = None     # mm
    socket_diameter: Optional[float] = None   # mm (for SHP socket)

@dataclass
class OptimizedPileLayout:
    """Result of pile layout optimization"""
    pile_locations: List[Point2D]
    selected_pile_spec: PileTypeSpec
    total_piles: int
    total_capacity: float
    utilization_ratio: float
    cost_efficiency: float

@dataclass
class PileConfig:
    """Configuration parameters for pile layout generation."""
    pile_capacity: float = 3663.0  # kN (backward compatibility)
    pile_diameter: float = 0.6  # m (backward compatibility)
    min_spacing: float = 1.8  # m (3D rule) (backward compatibility)
    edge_dist: float = 0.4  # m
    cap_offset: float = 0.5  # m
    max_column_pile_distance: float = 3.0  # m
    max_wall_end_pile_distance: float = 5.0  # m
    min_piles_per_group: int = 1
    
    # New multi-pile type parameters
    available_pile_types: List[PileTypeSpec] = None  # Available pile types for optimization
    optimization_method: str = "cost_efficiency"  # "cost_efficiency", "minimum_piles", "maximum_capacity"

