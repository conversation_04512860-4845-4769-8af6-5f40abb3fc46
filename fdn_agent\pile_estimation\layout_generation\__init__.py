﻿"""
Layout Generation Module

This module handles all aspects of pile layout generation, from simple calculated
layouts to complex genetic algorithm optimization.

MAIN COMPONENTS:
- layout_engine.py: Main coordination and routing
- genetic_fitness.py: Fitness evaluation for genetic algorithms
- case_1_layouts.py: Single column layouts (calculated)
- case_2_layouts.py: Single wall layouts (calculated)
- case_4_layouts.py: Complex layouts (genetic algorithm)
- layout_common.py: Common utilities for all layout types

WORKFLOW:
1. layout_engine.py determines the appropriate strategy
2. Routes simple cases to calculated methods (Cases 1 & 2)
3. Routes complex cases to genetic algorithm optimization (Case 4)
4. genetic_fitness.py provides evaluation for genetic algorithms
5. Returns unified results regardless of strategy used

Author: Foundation Automation System
Date: June 14, 2025
"""

from .layout_engine import (
    generate_pile_layout,
    generate_pile_layout_with_possible_positions,
    calculate_required_piles
)

from .genetic_fitness import (
    GeneticFitnessEvaluator,
    GeneticAlgorithmConfig,
    create_genetic_fitness_evaluator,
    collect_structural_sites
)

__all__ = [
    # Main functions
    'generate_pile_layout',
    'generate_pile_layout_with_possible_positions',
    'calculate_required_piles',
    
    # Genetic algorithm components
    'GeneticFitnessEvaluator',
    'GeneticAlgorithmConfig', 
    'create_genetic_fitness_evaluator',
    'collect_structural_sites'
]

