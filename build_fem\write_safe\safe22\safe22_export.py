"""
SAFE 22 Export Module

This module provides functionality for exporting SAFE 22 analysis data to various
file formats including F2K (SAFE native format) and Excel spreadsheets.

The module handles the conversion of pandas DataFrame structures to SAFE 22
compatible file formats while maintaining data integrity and proper formatting.

Functions:
    generate_unique_guid: Generate unique GUID identifiers
    append_safe22_f2k: Append DataFrame data to F2K format
    export_safe22_f2k: Export complete model to F2K file
    export_safe22_excel: Export complete model to Excel file

Example:
    >>> from safe22_export import export_safe22_f2k, export_safe22_excel
    >>> from safe22_class import Safe22DataFrames
    >>> safe_data = Safe22DataFrames()
    >>> # Export to F2K format
    >>> export_safe22_f2k(file_paths, safe_data)
    >>> # Export to Excel format
    >>> export_safe22_excel(file_paths, safe_data)
"""

import uuid
from datetime import datetime
from typing import List, Dict, Any, TextIO, Optional

import openpyxl as oxl
import pandas as pd

# Global list to track generated GUIDs and ensure uniqueness
generated_guids: List[str] = []


def generate_unique_guid() -> str:
    """
    Generate a unique GUID identifier for SAFE 22 objects.

    Creates a new UUID4 string and ensures it hasn't been generated before
    in the current session. Maintains a global list of generated GUIDs to
    prevent duplicates.

    Returns:
        str: A unique GUID string in standard UUID format

    Example:
        >>> guid = generate_unique_guid()
        >>> print(f"Generated GUID: {guid}")
        Generated GUID: 12345678-1234-5678-9abc-123456789abc

    Note:
        The function uses a global list to track generated GUIDs, which
        persists for the duration of the Python session.
    """    # Generate GUIDs in a loop until we find a unique one
    # This prevents duplicate identifiers in SAFE 22 objects
    while True:
        # Create a new UUID4 (random UUID)
        new_guid = str(uuid.uuid4())
        
        # Check if this GUID has been generated before in this session
        if new_guid not in generated_guids:
            # Add to the global tracking list to prevent future duplicates
            generated_guids.append(new_guid)
            return new_guid


def append_safe22_f2k(f2k: TextIO, df: pd.DataFrame) -> None:
    """
    Append DataFrame data to F2K file format.

    Converts a pandas DataFrame with multi-level columns to SAFE 22 F2K
    format and writes it to the specified file handle. Handles proper
    formatting of table headers and data rows according to F2K specifications.
    """    # Parse the table name from the multi-level column structure
    # F2K format expects uppercase table names with proper spacing
    split_title = df.columns[0][0].upper().split("  ")
    
    # Only process non-empty DataFrames to avoid writing empty table sections
    if not df.empty:
        # Simplify column structure for F2K output
        # Remove table name (level 0) and units (level 2), keep field names (level 1)
        df.columns = df.columns.droplevel((0, 2))
        
        # Write F2K table header with proper formatting
        f2k.write(' \n')
        f2k.write(f'{split_title[0]}  "{split_title[1]}"\n')

        # Convert each DataFrame row to F2K key-value format
        for i, r in df.iterrows():
            data = ''
            # Process each column in the current row
            for c in range(r.index.size):
                # Skip empty, None, or NaN values to keep F2K file clean
                # F2K format doesn't require all fields to be present
                if r[c] == '' or r[c] == None or pd.isna(r[c]):
                    pass
                else:
                    # Build F2K key-value pair format: "field_name"="value"
                    data = data + '   '
                    data = data + f'"{r.index[c]}"="{r[c]}"'
            # Write the complete row data as a single F2K line
            f2k.write(F'{data}\n')


def export_safe22_f2k(file_paths: Any, safe22_dfs: Any) -> str:
    """
    Export complete SAFE 22 model data to F2K file format.

    Converts a complete SAFE 22 model represented by DataFrame structures
    into the native F2K file format used by SAFE software. The function
    processes all model data including geometry, materials, loads, analysis
    options, and design parameters.
    """
    sheets_df = {'Program Control': safe22_dfs.ProgramControl,
                 'Database Table Names': safe22_dfs.DatabaseTableNames,
                 'Database Excel Names': safe22_dfs.DatabaseExcelNames,
                 'Database Field Names': safe22_dfs.DatabaseFieldNames,
                 'Grid Definitions - General': safe22_dfs.GridDefinitionsGeneral,
                 'Grid Definitions - Grid Lines': safe22_dfs.GridDefinitionsGridLines,
                 'Grid Definitions - Ref Points': safe22_dfs.GridDefinitionsRefPoints,
                 'Mat Prop - General': safe22_dfs.MatPropGeneral,
                 'Mat Prop - Basic Mech Props': safe22_dfs.MatPropBasicMechProps,
                 'Mat Prop - Steel Data': safe22_dfs.MatPropSteelData,
                 'Mat Prop - Concrete Data': safe22_dfs.MatPropConcreteData,
                 'Mat Prop - Rebar Data': safe22_dfs.MatPropRebarData,
                 'Mat Prop - Tendon Data': safe22_dfs.MatPropTendonData,
                 'Mat Prop - Other Data': safe22_dfs.MatPropOtherData,
                 'Frame Prop - Summary': safe22_dfs.FramePropSummary,
                 'Frame Sec Def - Steel I': safe22_dfs.FrameSecDefSteelI,
                 'Frame Sec Def - Steel Channel': safe22_dfs.FrameSecDefSteelChannel,
                 'Frame Sec Def - Steel Castelltd': safe22_dfs.FrameSecDefSteelCastelltd,
                 'Frame Sec Def - Steel Cellular': safe22_dfs.FrameSecDefSteelCellular,
                 'Frame Sec Def - Conc Rect': safe22_dfs.FrameSecDefConcRect,
                 'Frame Sec Def - Conc Trapezoid': safe22_dfs.FrameSecDefConcTrapezoid,
                 'Frame Sec Def - Conc Circle': safe22_dfs.FrameSecDefConcCircle,
                 'Frame Sec Def - Conc Tee': safe22_dfs.FrameSecDefConcTee,
                 'Frame Sec Def - Conc L': safe22_dfs.FrameSecDefConcL,
                 'Frame Sec Def - General': safe22_dfs.FrameSecDefGeneral,
                 'Frame Sec Def - Auto Sel List': safe22_dfs.FrameSecDefAutoSelList,
                 'Tendon Section Properties': safe22_dfs.TendonSectionProperties,
                 'Area Section Props - Summary': safe22_dfs.AreaSectionPropsSummary,
                 'Slab Property Definitions': safe22_dfs.SlabPropertyDefinitions,
                 'Deck Property Definitions': safe22_dfs.DeckPropertyDefinitions,
                 'Wall Property Def - Specified': safe22_dfs.WallPropertyDefSpecified,
                 'Wall Property Def - Auto Select': safe22_dfs.WallPropertyDefAutoSelect,
                 'Link Prop Def - Summary': safe22_dfs.LinkPropDefSummary,
                 'Link Prop Def - Linear': safe22_dfs.LinkPropDefLinear,
                 'Link Prop Def - Gap': safe22_dfs.LinkPropDefGap,
                 'Link Prop Def - Hook': safe22_dfs.LinkPropDefHook,
                 'Link Prop Def - Multi - Elastic': safe22_dfs.LinkPropDefMultiElastic,
                 'Pier Section Properties': safe22_dfs.PierSectionProperties,
                 'Spandrel Section Properties': safe22_dfs.SpandrelSectionProperties,
                 'Reinforcing Bar Sizes': safe22_dfs.ReinforcingBarSizes,
                 'Spring Props - Point Springs': safe22_dfs.SpringPropsPointSprings,
                 'Spring Props - Line Springs': safe22_dfs.SpringPropsLineSprings,
                 'Spring Props - Area Springs': safe22_dfs.SpringPropsAreaSprings,
                 'Load Pattern Definitions': safe22_dfs.LoadPatternDefinitions,
                 'Shell Uniform Load Sets': safe22_dfs.ShellUniformLoadSets,
                 'Group Definitions': safe22_dfs.GroupDefinitions,
                 'Group Assignments': safe22_dfs.GroupAssignments,
                 'Section Cut Definitions': safe22_dfs.SectionCutDefinitions,
                 'Floor Vibration Excitation Sets': safe22_dfs.FloorVibrationExcitationSets,
                 'Mass Summary by Group': safe22_dfs.MassSummarybyGroup,
                 'Mass Source Definition': safe22_dfs.MassSourceDefinition,
                 'Database Table Named Set Defs': safe22_dfs.DatabaseTableNamedSetDefs,
                 'Load Cases - Summary': safe22_dfs.LoadCasesSummary,
                 'Load Cases - Linear Static': safe22_dfs.LoadCasesLinearStatic,
                 'Load Cases - Nonlinear Static': safe22_dfs.LoadCasesNonlinearStatic,
                 'Load Cases - Nonlinear Staged': safe22_dfs.LoadCasesNonlinearStaged,
                 'Modal Cases - Eigen': safe22_dfs.ModalCasesEigen,
                 'Modal Cases - Ritz': safe22_dfs.ModalCasesRitz,
                 'Load Cases - Hyperstatic': safe22_dfs.LoadCasesHyperstatic,
                 'Imported Load Cases - Modal': safe22_dfs.ImportedLoadCasesModal,
                 'Imp Load Cases - RS': safe22_dfs.ImpLoadCasesRS,
                 'Imp Load Cases - RS - Func Data': safe22_dfs.ImpLoadCasesRSFuncData,
                 'Load Combination Definitions': safe22_dfs.LoadCombinationDefinitions,
                 'Point Object Connectivity': safe22_dfs.PointObjectConnectivity,
                 'Beam Object Connectivity': safe22_dfs.BeamObjectConnectivity,
                 'Beam Object Curve Data': safe22_dfs.BeamObjectCurveData,
                 'Column Object Connectivity': safe22_dfs.ColumnObjectConnectivity,
                 'Brace Object Connectivity': safe22_dfs.BraceObjectConnectivity,
                 'Null Line Object Connectivity': safe22_dfs.NullLineObjectConnectivity,
                 'Floor Object Connectivity': safe22_dfs.FloorObjectConnectivity,
                 'Floor Object Curved Edge Data': safe22_dfs.FloorObjectCurvedEdgeData,
                 'Wall Object Connectivity': safe22_dfs.WallObjectConnectivity,
                 'Wall Object Curve Data': safe22_dfs.WallObjectCurveData,
                 'Null Area Object Connectivity': safe22_dfs.NullAreaObjectConnectivity,
                 'Tendon Object Connectivity': safe22_dfs.TendonObjectConnectivity,
                 'Tendon Discretized Points': safe22_dfs.TendonDiscretizedPoints,
                 'Slab Rebar Object Geometry': safe22_dfs.SlabRebarObjectGeometry,
                 'Strip Object Connectivity': safe22_dfs.StripObjectConnectivity,
                 'Dimension Line Object Geometry': safe22_dfs.DimensionLineObjectGeometry,
                 'Jt Assigns - Summary': safe22_dfs.JtAssignsSummary,
                 'Joint Assigns - Restraints': safe22_dfs.JointAssignsRestraints,
                 'Joint Assigns - Springs': safe22_dfs.JointAssignsSprings,
                 'Jt Assigns - Additional Mass': safe22_dfs.JtAssignsAdditionalMass,
                 'Joint Assigns - Floor Mesh Opt': safe22_dfs.JointAssignsFloorMeshOpt,
                 'Joint Loads - Force': safe22_dfs.JointLoadsForce,
                 'Joint Loads - Grnd Displacement': safe22_dfs.JointLoadsGrndDisplacement,
                 'Joint Loads - Temperature': safe22_dfs.JointLoadsTemperature,
                 'Frame Assigns - Summary': safe22_dfs.FrameAssignsSummary,
                 'Frame Assigns - Sect Prop': safe22_dfs.FrameAssignsSectProp,
                 'Frame Assigns - Prop Modifiers': safe22_dfs.FrameAssignsPropModifiers,
                 'Frame Assigns - Releases': safe22_dfs.FrameAssignsReleases,
                 'Frame Assigns - End Len Offsets': safe22_dfs.FrameAssignsEndLenOffsets,
                 'Frame Assigns - Insertion Point': safe22_dfs.FrameAssignsInsertionPoint,
                 'Frame Assigns - Local Axes': safe22_dfs.FrameAssignsLocalAxes,
                 'Frame Assigns - Output Stations': safe22_dfs.FrameAssignsOutputStations,
                 'Frame Assigns - Line Springs': safe22_dfs.FrameAssignsLineSprings,
                 'Frame Assigns - Additional Mass': safe22_dfs.FrameAssignsAdditionalMass,
                 'Frame Assigns - Frame Auto Mesh': safe22_dfs.FrameAssignsFrameAutoMesh,
                 'Frame Assigns - Floor Mesh Opt': safe22_dfs.FrameAssignsFloorMeshOpt,
                 'Frame Assigns - Nonpris Params': safe22_dfs.FrameAssignsNonprisParams,
                 'Frame Assigns - Floor Cracking': safe22_dfs.FrameAssignsFloorCracking,
                 'Frame Loads - Point': safe22_dfs.FrameLoadsPoint,
                 'Frame Loads - Distributed': safe22_dfs.FrameLoadsDistributed,
                 'Frame Loads - Temperature': safe22_dfs.FrameLoadsTemperature,
                 'Tendon Assigns - Properties': safe22_dfs.TendonAssignsProperties,
                 'Tendon Loads - Jacking Stress': safe22_dfs.TendonLoadsJackingStress,
                 'Tendon Loads - Loss Options': safe22_dfs.TendonLoadsLossOptions,
                 'Tendon - Total Elongation': safe22_dfs.TendonTotalElongation,
                 'Area Assigns - Summary': safe22_dfs.AreaAssignsSummary,
                 'Area Assigns - Sect Prop': safe22_dfs.AreaAssignsSectProp,
                 'Area Assigns - Stiff Modifiers': safe22_dfs.AreaAssignsStiffModifiers,
                 'Area Assigns - Thick Overwrites': safe22_dfs.AreaAssignsThickOverwrites,
                 'Area Assigns - Insertion Point': safe22_dfs.AreaAssignsInsertionPoint,
                 'Area Assigns - Edge Releases': safe22_dfs.AreaAssignsEdgeReleases,
                 'Area Assigns - Slab Line Rel': safe22_dfs.AreaAssignsSlabLineRel,
                 'Area Assigns - Slab Rib Locs': safe22_dfs.AreaAssignsSlabRibLocs,
                 'Area Assigns - Local Axes': safe22_dfs.AreaAssignsLocalAxes,
                 'Area Assigns - Area Springs': safe22_dfs.AreaAssignsAreaSprings,
                 'Area Assigns - Additional Mass': safe22_dfs.AreaAssignsAdditionalMass,
                 'Area Assigns - Floor Auto Mesh': safe22_dfs.AreaAssignsFloorAutoMesh,
                 'Area Assigns - Wall Auto Mesh': safe22_dfs.AreaAssignsWallAutoMesh,
                 'Area Assigns - Edge Constraints': safe22_dfs.AreaAssignsEdgeConstraints,
                 'Area Assigns - Floor Cracking': safe22_dfs.AreaAssignsFloorCracking,
                 'Area Loads - Uniform': safe22_dfs.AreaLoadsUniform,
                 'Area Loads - Non-uniform': safe22_dfs.AreaLoadsNonuniform,
                 'Area Loads - Temperature': safe22_dfs.AreaLoadsTemperature,
                 'Area Loads - Wind Coefficients': safe22_dfs.AreaLoadsWindCoefficients,
                 'Area Loads - Uniform Load Sets': safe22_dfs.AreaLoadsUniformLoadSets,
                 'Slab Rebar Property Assignments': safe22_dfs.SlabRebarPropertyAssignments,
                 'Analysis Modeling Options': safe22_dfs.AnalysisModelingOptions,
                 'Analysis - SAPFire Options': safe22_dfs.AnalysisSAPFireOptions,
                 'Analysis - Dsgn and Rec Options': safe22_dfs.AnalysisDsgnandRecOptions,
                 'Analysis - Floor Mesh Settings': safe22_dfs.AnalysisFloorMeshSettings,
                 'Analysis - Wall Mesh Settings': safe22_dfs.AnalysisWallMeshSettings,
                 'Analysis - Cracking Analysis': safe22_dfs.AnalysisCrackingAnalysis,
                 'Preferences - Tolerance': safe22_dfs.PreferencesTolerance,
                 'Preferences - Graphics': safe22_dfs.PreferencesGraphics,
                 'Miscellaneous Options': safe22_dfs.MiscellaneousOptions,
                 'Options - Colors - Display': safe22_dfs.OptionsColorsDisplay,
                 'Options - Colors - Output': safe22_dfs.OptionsColorsOutput,
                 'Project Information': safe22_dfs.ProjectInformation,
                 'Material List by Object Type': safe22_dfs.MaterialListbyObjectType,
                 'Material List by Section Prop': safe22_dfs.MaterialListbySectionProp,
                 'Comp Bm Pref - AISC 360-05': safe22_dfs.CompBmPrefAISC36005,
                 'Comp Bm Pref - AISC 360-10': safe22_dfs.CompBmPrefAISC36010,
                 'Comp Bm Pref - AISC 360-16': safe22_dfs.CompBmPrefAISC36016,
                 'Comp Bm Pref - AISC 360-22': safe22_dfs.CompBmPrefAISC36022,
                 'Comp Bm Pref - BS 5950-90': safe22_dfs.CompBmPrefBS595090,
                 'Comp Bm Pref - Chinese 2010': safe22_dfs.CompBmPrefChinese2010,
                 'Comp Bm Pref - Chinese 2018': safe22_dfs.CompBmPrefChinese2018,
                 'Comp Bm Pref - CSA S16-09': safe22_dfs.CompBmPrefCSAS1609,
                 'Comp Bm Pref - CSA S16-14': safe22_dfs.CompBmPrefCSAS1614,
                 'Comp Bm Pref - CSA S16-19': safe22_dfs.CompBmPrefCSAS1619,
                 'Comp Bm Pref - CSA S16-24': safe22_dfs.CompBmPrefCSAS1624,
                 'Comp Bm Pref - Eurocode 4-2004': safe22_dfs.CompBmPrefEurocode42004,
                 'Comp Bm Pref - IS 11384_2022': safe22_dfs.CompBmPrefIS11384_2022,
                 'Conc Des Pref - ACI 318-08': safe22_dfs.ConcDesPrefACI31808,
                 'Conc Des Pref - ACI 318-11': safe22_dfs.ConcDesPrefACI31811,
                 'Conc Des Pref - ACI 318-14': safe22_dfs.ConcDesPrefACI31814,
                 'Conc Des Pref - ACI 318-19': safe22_dfs.ConcDesPrefACI31819,
                 'Conc Des Pref - AS 3600-09': safe22_dfs.ConcDesPrefAS360009,
                 'Conc Des Pref - AS 3600-2018': safe22_dfs.ConcDesPrefAS36002018,
                 'Conc Des Pref - BS 8110-97': safe22_dfs.ConcDesPrefBS811097,
                 'Conc Des Pref - Chinese 2010': safe22_dfs.ConcDesPrefChinese2010,
                 'Conc Des Pref - CSA A233-14': safe22_dfs.ConcDesPrefCSAA23314,
                 'Conc Des Pref - CSA A233-19': safe22_dfs.ConcDesPrefCSAA23319,
                 'Conc Des Pref - Eurocode 2-2004': safe22_dfs.ConcDesPrefEurocode22004,
                 'Conc Des Pref - H Kong CP 2013': safe22_dfs.ConcDesPrefHKongCP2013,
                 'Conc Des Pref - IS 456-2000': safe22_dfs.ConcDesPrefIS4562000,
                 'Conc Des Pref - Ital NTC 2008': safe22_dfs.ConcDesPrefItalNTC2008,
                 'Conc Des Pref - NZS 3101-2006': safe22_dfs.ConcDesPrefNZS31012006,
                 'Conc Des Pref - Sing CP 65-99': safe22_dfs.ConcDesPrefSingCP6599,
                 'Conc Des Pref - SP 63-13330-12': safe22_dfs.ConcDesPrefSP631333012,
                 'Conc Des Pref - TS 500-2000': safe22_dfs.ConcDesPrefTS5002000,
                 'Comp Bm Add Sections With Studs': safe22_dfs.CompBmAddSectionsWithStuds,
                 'Comp Bm Const Bracing - Point': safe22_dfs.CompBmConstBracingPoint,
                 'Comp Bm Const Bracing - Uniform': safe22_dfs.CompBmConstBracingUniform,
                 'Comp Bm Over AISC 360-05': safe22_dfs.CompBmOverAISC36005,
                 'Comp Bm Over AISC 360-10': safe22_dfs.CompBmOverAISC36010,
                 'Comp Bm Over AISC 360-16': safe22_dfs.CompBmOverAISC36016,
                 'Comp Bm Over AISC 360-22': safe22_dfs.CompBmOverAISC36022,
                 'Comp Bm Over BS 5950-1990': safe22_dfs.CompBmOverBS59501990,
                 'Comp Bm Over BS Chinese 2010': safe22_dfs.CompBmOverBSChinese2010,
                 'Comp Bm Over Chinese 2018': safe22_dfs.CompBmOverChinese2018,
                 'Comp Bm Over CSA S16-09': safe22_dfs.CompBmOverCSAS1609,
                 'Comp Bm Over CSA S16-14': safe22_dfs.CompBmOverCSAS1614,
                 'Comp Bm Over CSA S16-19': safe22_dfs.CompBmOverCSAS1619,
                 'Comp Bm Over CSA S16-24': safe22_dfs.CompBmOverCSAS1624,
                 'Comp Bm Over Eurocode 4-2004': safe22_dfs.CompBmOverEurocode42004,
                 'Comp Bm Over IS 11384_2022': safe22_dfs.CompBmOverIS11384_2022,
                 'Comp Bm Str Bracing - Point': safe22_dfs.CompBmStrBracingPoint,
                 'Comp Bm Str Bracing - Uniform': safe22_dfs.CompBmStrBracingUniform,
                 'Conc Bm Over ACI 318-08': safe22_dfs.ConcBmOverACI31808,
                 'Conc Bm Over ACI 318-11': safe22_dfs.ConcBmOverACI31811,
                 'Conc Bm Over ACI 318-14': safe22_dfs.ConcBmOverACI31814,
                 'Conc Bm Over ACI 318-19': safe22_dfs.ConcBmOverACI31819,
                 'Conc Bm Over AS 3600-09': safe22_dfs.ConcBmOverAS360009,
                 'Conc Bm Over AS 3600-2018': safe22_dfs.ConcBmOverAS36002018,
                 'Conc Bm Over BS 8110-97': safe22_dfs.ConcBmOverBS811097,
                 'Conc Bm Over Chinese 2010': safe22_dfs.ConcBmOverChinese2010,
                 'Conc Bm Over CSA A233-14': safe22_dfs.ConcBmOverCSAA23314,
                 'Conc Bm Over CSA A233-19': safe22_dfs.ConcBmOverCSAA23319,
                 'Conc Bm Over Eurocode 2-2004': safe22_dfs.ConcBmOverEurocode22004,
                 'Conc Bm Over Hong Kong CP 2013': safe22_dfs.ConcBmOverHongKongCP2013,
                 'Conc Bm Over IS 456-2000': safe22_dfs.ConcBmOverIS4562000,
                 'Conc Bm Over Italian NTC 2008': safe22_dfs.ConcBmOverItalianNTC2008,
                 'Conc Bm Over KBC 2009': safe22_dfs.ConcBmOverKBC2009,
                 'Conc Bm Over KBC 2016': safe22_dfs.ConcBmOverKBC2016,
                 'Conc Bm Over Mexican RCDF 2004': safe22_dfs.ConcBmOverMexicanRCDF2004,
                 'Conc Bm Over Mexican RCDF 2017': safe22_dfs.ConcBmOverMexicanRCDF2017,
                 'Conc Bm Over NZS 3101-2006': safe22_dfs.ConcBmOverNZS31012006,
                 'Conc Bm Over Singapore CP65-99': safe22_dfs.ConcBmOverSingaporeCP6599,
                 'Conc Bm Over SP 63-13330-2012': safe22_dfs.ConcBmOverSP63133302012,
                 'Conc Bm Over TCVN 5574-2012': safe22_dfs.ConcBmOverTCVN55742012,
                 'Conc Bm Over TS 500-2000': safe22_dfs.ConcBmOverTS5002000,
                 'Conc Bm Over TS 500-2000R2018': safe22_dfs.ConcBmOverTS5002000R2018,
                 'Stl Frm Over Chinese 2018': safe22_dfs.StlFrmOverChinese2018,
                 'Conc Slb Over - FE Based': safe22_dfs.ConcSlbOverFEBased,
                 'Conc Slb Over - Punch Shr - Gen': safe22_dfs.ConcSlbOverPunchShrGen,
                 'Conc Slb Over - Punch Shr - Opn': safe22_dfs.ConcSlbOverPunchShrOpn,
                 'Conc Slb Over - Punch Shr - Per': safe22_dfs.ConcSlbOverPunchShrPer,
                 'Conc Slb Over - Strip Based': safe22_dfs.ConcSlbOverStripBased,
                 'Conc Frame Design Combo Data': safe22_dfs.ConcFrameDesignComboData,
                 'Conc Slab Design Combo Data': safe22_dfs.ConcSlabDesignComboData                 }
    
    # Create the F2K output file with UTF-8 encoding for international character support
    with open(file_paths.f2kSAFE22Model, 'w+', encoding='utf-8') as f2k:
        # Write timestamped header for file tracking and version control
        now = datetime.now()
        timestamp = f'File {file_paths.f2kSAFE22Model} was saved on {now.strftime("%#m/%#d/%#Y")} at {now.strftime("%#I:%#M:%S")}\n'
        f2k.write(timestamp)

        # Process each DataFrame and append to F2K file in proper format
        # Each table is written as a separate section with table name and data
        for df_name, df in sheets_df.items():
            append_safe22_f2k(f2k, df)

        # Write F2K file termination marker
        f2k.write(' \n')
        f2k.write('END TABLE DATA\n')

    # Post-process the F2K file to clean up invalid data representations
    with open(file_paths.f2kSAFE22Model, 'r') as file:
        file_data = file.read()

    # Clean up string representations of None/NaN values
    # SAFE software expects empty strings rather than "None" or "NaN"
    file_data = file_data.replace('"None"', '""')
    file_data = file_data.replace('"Nan"', '""')
    file_data = file_data.replace('"none"', '""')
    file_data = file_data.replace('"nan"', '""')
    
    # Write the cleaned file content back to disk
    with open(file_paths.f2kSAFE22Model, 'w', encoding='utf-8') as file:
        file.write(file_data)

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} SAFE22 Model saved to f2k file')
    return file_data


def export_safe22_excel(file_paths, safe22_dfs):
    """
    Export complete SAFE 22 model data to Excel workbook format.

    Converts a complete SAFE 22 model represented by DataFrame structures
    into a multi-sheet Excel workbook. Each DataFrame is exported to a separate
    worksheet with proper formatting and structure. The function handles all
    SAFE 22 table types including geometry, materials, loads, analysis options,
    design parameters, and results.
    """
    # Define sheet names and corresponding dataframes
    sheets_df = {'Analysis - Cracking Analysis': safe22_dfs.AnalysisCrackingAnalysis,
                 'Analysis - Dsgn and Rec Options': safe22_dfs.AnalysisDsgnandRecOptions,
                 'Analysis - Floor Mesh Settings': safe22_dfs.AnalysisFloorMeshSettings,
                 'Analysis - SAPFire Options': safe22_dfs.AnalysisSAPFireOptions,
                 'Analysis - Wall Mesh Settings': safe22_dfs.AnalysisWallMeshSettings,
                 'Analysis Modeling Options': safe22_dfs.AnalysisModelingOptions,
                 'Area Assigns - Additional Mass': safe22_dfs.AreaAssignsAdditionalMass,
                 'Area Assigns - Area Springs': safe22_dfs.AreaAssignsAreaSprings,
                 'Area Assigns - Edge Constraints': safe22_dfs.AreaAssignsEdgeConstraints,
                 'Area Assigns - Edge Releases': safe22_dfs.AreaAssignsEdgeReleases,
                 'Area Assigns - Floor Auto Mesh': safe22_dfs.AreaAssignsFloorAutoMesh,
                 'Area Assigns - Floor Cracking': safe22_dfs.AreaAssignsFloorCracking,
                 'Area Assigns - Insertion Point': safe22_dfs.AreaAssignsInsertionPoint,
                 'Area Assigns - Local Axes': safe22_dfs.AreaAssignsLocalAxes,
                 'Area Assigns - Sect Prop': safe22_dfs.AreaAssignsSectProp,
                 'Area Assigns - Slab Line Rel': safe22_dfs.AreaAssignsSlabLineRel,
                 'Area Assigns - Slab Rib Locs': safe22_dfs.AreaAssignsSlabRibLocs,
                 'Area Assigns - Stiff Modifiers': safe22_dfs.AreaAssignsStiffModifiers,
                 'Area Assigns - Summary': safe22_dfs.AreaAssignsSummary,
                 'Area Assigns - Thick Overwrites': safe22_dfs.AreaAssignsThickOverwrites,
                 'Area Assigns - Wall Auto Mesh': safe22_dfs.AreaAssignsWallAutoMesh,
                 'Area Loads - Non-uniform': safe22_dfs.AreaLoadsNonuniform,
                 'Area Loads - Temperature': safe22_dfs.AreaLoadsTemperature,
                 'Area Loads - Uniform': safe22_dfs.AreaLoadsUniform,
                 'Area Loads - Uniform Load Sets': safe22_dfs.AreaLoadsUniformLoadSets,
                 'Area Loads - Wind Coefficients': safe22_dfs.AreaLoadsWindCoefficients,
                 'Area Section Props - Summary': safe22_dfs.AreaSectionPropsSummary,
                 'Beam Object Connectivity': safe22_dfs.BeamObjectConnectivity,
                 'Beam Object Curve Data': safe22_dfs.BeamObjectCurveData,
                 'Brace Object Connectivity': safe22_dfs.BraceObjectConnectivity,
                 'Column Object Connectivity': safe22_dfs.ColumnObjectConnectivity,
                 'Comp Bm Add Sections With Studs': safe22_dfs.CompBmAddSectionsWithStuds,
                 'Comp Bm Const Bracing - Point': safe22_dfs.CompBmConstBracingPoint,
                 'Comp Bm Const Bracing - Uniform': safe22_dfs.CompBmConstBracingUniform,
                 'Comp Bm Over AISC 360-05': safe22_dfs.CompBmOverAISC36005,
                 'Comp Bm Over AISC 360-10': safe22_dfs.CompBmOverAISC36010,
                 'Comp Bm Over AISC 360-16': safe22_dfs.CompBmOverAISC36016,
                 'Comp Bm Over AISC 360-22': safe22_dfs.CompBmOverAISC36022,
                 'Comp Bm Over BS 5950-1990': safe22_dfs.CompBmOverBS59501990,
                 'Comp Bm Over BS Chinese 2010': safe22_dfs.CompBmOverBSChinese2010,
                 'Comp Bm Over Chinese 2018': safe22_dfs.CompBmOverChinese2018,
                 'Comp Bm Over CSA S16-09': safe22_dfs.CompBmOverCSAS1609,
                 'Comp Bm Over CSA S16-14': safe22_dfs.CompBmOverCSAS1614,
                 'Comp Bm Over CSA S16-19': safe22_dfs.CompBmOverCSAS1619,
                 'Comp Bm Over CSA S16-24': safe22_dfs.CompBmOverCSAS1624,
                 'Comp Bm Over Eurocode 4-2004': safe22_dfs.CompBmOverEurocode42004,
                 'Comp Bm Over IS 11384_2022': safe22_dfs.CompBmOverIS11384_2022,
                 'Comp Bm Pref - AISC 360-05': safe22_dfs.CompBmPrefAISC36005,
                 'Comp Bm Pref - AISC 360-10': safe22_dfs.CompBmPrefAISC36010,
                 'Comp Bm Pref - AISC 360-16': safe22_dfs.CompBmPrefAISC36016,
                 'Comp Bm Pref - AISC 360-22': safe22_dfs.CompBmPrefAISC36022,
                 'Comp Bm Pref - BS 5950-90': safe22_dfs.CompBmPrefBS595090,
                 'Comp Bm Pref - Chinese 2010': safe22_dfs.CompBmPrefChinese2010,
                 'Comp Bm Pref - Chinese 2018': safe22_dfs.CompBmPrefChinese2018,
                 'Comp Bm Pref - CSA S16-09': safe22_dfs.CompBmPrefCSAS1609,
                 'Comp Bm Pref - CSA S16-14': safe22_dfs.CompBmPrefCSAS1614,
                 'Comp Bm Pref - CSA S16-19': safe22_dfs.CompBmPrefCSAS1619,
                 'Comp Bm Pref - CSA S16-24': safe22_dfs.CompBmPrefCSAS1624,
                 'Comp Bm Pref - Eurocode 4-2004': safe22_dfs.CompBmPrefEurocode42004,
                 'Comp Bm Pref - IS 11384_2022': safe22_dfs.CompBmPrefIS11384_2022,
                 'Comp Bm Str Bracing - Point': safe22_dfs.CompBmStrBracingPoint,
                 'Comp Bm Str Bracing - Uniform': safe22_dfs.CompBmStrBracingUniform,
                 'Conc Bm Over ACI 318-08': safe22_dfs.ConcBmOverACI31808,
                 'Conc Bm Over ACI 318-11': safe22_dfs.ConcBmOverACI31811,
                 'Conc Bm Over ACI 318-14': safe22_dfs.ConcBmOverACI31814,
                 'Conc Bm Over ACI 318-19': safe22_dfs.ConcBmOverACI31819,
                 'Conc Bm Over AS 3600-09': safe22_dfs.ConcBmOverAS360009,
                 'Conc Bm Over AS 3600-2018': safe22_dfs.ConcBmOverAS36002018,
                 'Conc Bm Over BS 8110-97': safe22_dfs.ConcBmOverBS811097,
                 'Conc Bm Over Chinese 2010': safe22_dfs.ConcBmOverChinese2010,
                 'Conc Bm Over CSA A233-14': safe22_dfs.ConcBmOverCSAA23314,
                 'Conc Bm Over CSA A233-19': safe22_dfs.ConcBmOverCSAA23319,
                 'Conc Bm Over Eurocode 2-2004': safe22_dfs.ConcBmOverEurocode22004,
                 'Conc Bm Over Hong Kong CP 2013': safe22_dfs.ConcBmOverHongKongCP2013,
                 'Conc Bm Over IS 456-2000': safe22_dfs.ConcBmOverIS4562000,
                 'Conc Bm Over Italian NTC 2008': safe22_dfs.ConcBmOverItalianNTC2008,
                 'Conc Bm Over KBC 2009': safe22_dfs.ConcBmOverKBC2009,
                 'Conc Bm Over KBC 2016': safe22_dfs.ConcBmOverKBC2016,
                 'Conc Bm Over Mexican RCDF 2004': safe22_dfs.ConcBmOverMexicanRCDF2004,
                 'Conc Bm Over Mexican RCDF 2017': safe22_dfs.ConcBmOverMexicanRCDF2017,
                 'Conc Bm Over NZS 3101-2006': safe22_dfs.ConcBmOverNZS31012006,
                 'Conc Bm Over Singapore CP65-99': safe22_dfs.ConcBmOverSingaporeCP6599,
                 'Conc Bm Over SP 63-13330-2012': safe22_dfs.ConcBmOverSP63133302012,
                 'Conc Bm Over TCVN 5574-2012': safe22_dfs.ConcBmOverTCVN55742012,
                 'Conc Bm Over TS 500-2000': safe22_dfs.ConcBmOverTS5002000,
                 'Conc Bm Over TS 500-2000R2018': safe22_dfs.ConcBmOverTS5002000R2018,
                 'Conc Des Pref - ACI 318-08': safe22_dfs.ConcDesPrefACI31808,
                 'Conc Des Pref - ACI 318-11': safe22_dfs.ConcDesPrefACI31811,
                 'Conc Des Pref - ACI 318-14': safe22_dfs.ConcDesPrefACI31814,
                 'Conc Des Pref - ACI 318-19': safe22_dfs.ConcDesPrefACI31819,
                 'Conc Des Pref - AS 3600-09': safe22_dfs.ConcDesPrefAS360009,
                 'Conc Des Pref - AS 3600-2018': safe22_dfs.ConcDesPrefAS36002018,
                 'Conc Des Pref - BS 8110-97': safe22_dfs.ConcDesPrefBS811097,
                 'Conc Des Pref - Chinese 2010': safe22_dfs.ConcDesPrefChinese2010,
                 'Conc Des Pref - CSA A233-14': safe22_dfs.ConcDesPrefCSAA23314,
                 'Conc Des Pref - CSA A233-19': safe22_dfs.ConcDesPrefCSAA23319,
                 'Conc Des Pref - Eurocode 2-2004': safe22_dfs.ConcDesPrefEurocode22004,
                 'Conc Des Pref - H Kong CP 2013': safe22_dfs.ConcDesPrefHKongCP2013,
                 'Conc Des Pref - IS 456-2000': safe22_dfs.ConcDesPrefIS4562000,
                 'Conc Des Pref - Ital NTC 2008': safe22_dfs.ConcDesPrefItalNTC2008,
                 'Conc Des Pref - NZS 3101-2006': safe22_dfs.ConcDesPrefNZS31012006,
                 'Conc Des Pref - Sing CP 65-99': safe22_dfs.ConcDesPrefSingCP6599,
                 'Conc Des Pref - SP 63-13330-12': safe22_dfs.ConcDesPrefSP631333012,
                 'Conc Des Pref - TS 500-2000': safe22_dfs.ConcDesPrefTS5002000,
                 'Conc Frame Design Combo Data': safe22_dfs.ConcFrameDesignComboData,
                 'Conc Slab Design Combo Data': safe22_dfs.ConcSlabDesignComboData,
                 'Conc Slb Over - FE Based': safe22_dfs.ConcSlbOverFEBased,
                 'Conc Slb Over - Punch Shr - Gen': safe22_dfs.ConcSlbOverPunchShrGen,
                 'Conc Slb Over - Punch Shr - Opn': safe22_dfs.ConcSlbOverPunchShrOpn,
                 'Conc Slb Over - Punch Shr - Per': safe22_dfs.ConcSlbOverPunchShrPer,
                 'Conc Slb Over - Strip Based': safe22_dfs.ConcSlbOverStripBased,
                 'Database Excel Names': safe22_dfs.DatabaseExcelNames,
                 'Database Field Names': safe22_dfs.DatabaseFieldNames,
                 'Database Table Named Set Defs': safe22_dfs.DatabaseTableNamedSetDefs,
                 'Database Table Names': safe22_dfs.DatabaseTableNames,
                 'Deck Property Definitions': safe22_dfs.DeckPropertyDefinitions,
                 'Dimension Line Object Geometry': safe22_dfs.DimensionLineObjectGeometry,
                 'Floor Object Connectivity': safe22_dfs.FloorObjectConnectivity,
                 'Floor Object Curved Edge Data': safe22_dfs.FloorObjectCurvedEdgeData,
                 'Floor Vibration Excitation Sets': safe22_dfs.FloorVibrationExcitationSets,
                 'Frame Assigns - Additional Mass': safe22_dfs.FrameAssignsAdditionalMass,
                 'Frame Assigns - End Len Offsets': safe22_dfs.FrameAssignsEndLenOffsets,
                 'Frame Assigns - Floor Cracking': safe22_dfs.FrameAssignsFloorCracking,
                 'Frame Assigns - Floor Mesh Opt': safe22_dfs.FrameAssignsFloorMeshOpt,
                 'Frame Assigns - Frame Auto Mesh': safe22_dfs.FrameAssignsFrameAutoMesh,
                 'Frame Assigns - Insertion Point': safe22_dfs.FrameAssignsInsertionPoint,
                 'Frame Assigns - Line Springs': safe22_dfs.FrameAssignsLineSprings,
                 'Frame Assigns - Local Axes': safe22_dfs.FrameAssignsLocalAxes,
                 'Frame Assigns - Nonpris Params': safe22_dfs.FrameAssignsNonprisParams,
                 'Frame Assigns - Output Stations': safe22_dfs.FrameAssignsOutputStations,
                 'Frame Assigns - Prop Modifiers': safe22_dfs.FrameAssignsPropModifiers,
                 'Frame Assigns - Releases': safe22_dfs.FrameAssignsReleases,
                 'Frame Assigns - Sect Prop': safe22_dfs.FrameAssignsSectProp,
                 'Frame Assigns - Summary': safe22_dfs.FrameAssignsSummary,
                 'Frame Loads - Distributed': safe22_dfs.FrameLoadsDistributed,
                 'Frame Loads - Point': safe22_dfs.FrameLoadsPoint,
                 'Frame Loads - Temperature': safe22_dfs.FrameLoadsTemperature,
                 'Frame Prop - Summary': safe22_dfs.FramePropSummary,
                 'Frame Sec Def - Auto Sel List': safe22_dfs.FrameSecDefAutoSelList,
                 'Frame Sec Def - Conc Circle': safe22_dfs.FrameSecDefConcCircle,
                 'Frame Sec Def - Conc L': safe22_dfs.FrameSecDefConcL,
                 'Frame Sec Def - Conc Rect': safe22_dfs.FrameSecDefConcRect,
                 'Frame Sec Def - Conc Tee': safe22_dfs.FrameSecDefConcTee,
                 'Frame Sec Def - Conc Trapezoid': safe22_dfs.FrameSecDefConcTrapezoid,
                 'Frame Sec Def - General': safe22_dfs.FrameSecDefGeneral,
                 'Frame Sec Def - Steel Castelltd': safe22_dfs.FrameSecDefSteelCastelltd,
                 'Frame Sec Def - Steel Cellular': safe22_dfs.FrameSecDefSteelCellular,
                 'Frame Sec Def - Steel Channel': safe22_dfs.FrameSecDefSteelChannel,
                 'Frame Sec Def - Steel I': safe22_dfs.FrameSecDefSteelI,
                 'Grid Definitions - General': safe22_dfs.GridDefinitionsGeneral,
                 'Grid Definitions - Grid Lines': safe22_dfs.GridDefinitionsGridLines,
                 'Grid Definitions - Ref Points': safe22_dfs.GridDefinitionsRefPoints,
                 'Group Assignments': safe22_dfs.GroupAssignments,
                 'Group Definitions': safe22_dfs.GroupDefinitions,
                 'Imp Load Cases - RS': safe22_dfs.ImpLoadCasesRS,
                 'Imp Load Cases - RS - Func Data': safe22_dfs.ImpLoadCasesRSFuncData,
                 'Imported Load Cases - Modal': safe22_dfs.ImportedLoadCasesModal,
                 'Joint Assigns - Floor Mesh Opt': safe22_dfs.JointAssignsFloorMeshOpt,
                 'Joint Assigns - Restraints': safe22_dfs.JointAssignsRestraints,
                 'Joint Assigns - Springs': safe22_dfs.JointAssignsSprings,
                 'Joint Loads - Force': safe22_dfs.JointLoadsForce,
                 'Joint Loads - Grnd Displacement': safe22_dfs.JointLoadsGrndDisplacement,
                 'Joint Loads - Temperature': safe22_dfs.JointLoadsTemperature,
                 'Jt Assigns - Additional Mass': safe22_dfs.JtAssignsAdditionalMass,
                 'Jt Assigns - Summary': safe22_dfs.JtAssignsSummary,
                 'Link Prop Def - Gap': safe22_dfs.LinkPropDefGap,
                 'Link Prop Def - Hook': safe22_dfs.LinkPropDefHook,
                 'Link Prop Def - Linear': safe22_dfs.LinkPropDefLinear,
                 'Link Prop Def - Multi - Elastic': safe22_dfs.LinkPropDefMultiElastic,
                 'Link Prop Def - Summary': safe22_dfs.LinkPropDefSummary,
                 'Load Cases - Hyperstatic': safe22_dfs.LoadCasesHyperstatic,
                 'Load Cases - Linear Static': safe22_dfs.LoadCasesLinearStatic,
                 'Load Cases - Nonlinear Staged': safe22_dfs.LoadCasesNonlinearStaged,
                 'Load Cases - Nonlinear Static': safe22_dfs.LoadCasesNonlinearStatic,
                 'Load Cases - Summary': safe22_dfs.LoadCasesSummary,
                 'Load Combination Definitions': safe22_dfs.LoadCombinationDefinitions,
                 'Load Pattern Definitions': safe22_dfs.LoadPatternDefinitions,
                 'Mass Source Definition': safe22_dfs.MassSourceDefinition,
                 'Mass Summary by Group': safe22_dfs.MassSummarybyGroup,
                 'Mat Prop - Basic Mech Props': safe22_dfs.MatPropBasicMechProps,
                 'Mat Prop - Concrete Data': safe22_dfs.MatPropConcreteData,
                 'Mat Prop - General': safe22_dfs.MatPropGeneral,
                 'Mat Prop - Other Data': safe22_dfs.MatPropOtherData,
                 'Mat Prop - Rebar Data': safe22_dfs.MatPropRebarData,
                 'Mat Prop - Steel Data': safe22_dfs.MatPropSteelData,
                 'Mat Prop - Tendon Data': safe22_dfs.MatPropTendonData,
                 'Material List by Object Type': safe22_dfs.MaterialListbyObjectType,
                 'Material List by Section Prop': safe22_dfs.MaterialListbySectionProp,
                 'Miscellaneous Options': safe22_dfs.MiscellaneousOptions,
                 'Modal Cases - Eigen': safe22_dfs.ModalCasesEigen,
                 'Modal Cases - Ritz': safe22_dfs.ModalCasesRitz,
                 'Null Area Object Connectivity': safe22_dfs.NullAreaObjectConnectivity,
                 'Null Line Object Connectivity': safe22_dfs.NullLineObjectConnectivity,
                 'Options - Colors - Display': safe22_dfs.OptionsColorsDisplay,
                 'Options - Colors - Output': safe22_dfs.OptionsColorsOutput,
                 'Pier Section Properties': safe22_dfs.PierSectionProperties,
                 'Point Object Connectivity': safe22_dfs.PointObjectConnectivity,
                 'Preferences - Graphics': safe22_dfs.PreferencesGraphics,
                 'Preferences - Tolerance': safe22_dfs.PreferencesTolerance,
                 'Program Control': safe22_dfs.ProgramControl,
                 'Project Information': safe22_dfs.ProjectInformation,
                 'Reinforcing Bar Sizes': safe22_dfs.ReinforcingBarSizes,
                 'Section Cut Definitions': safe22_dfs.SectionCutDefinitions,
                 'Shell Uniform Load Sets': safe22_dfs.ShellUniformLoadSets,
                 'Slab Property Definitions': safe22_dfs.SlabPropertyDefinitions,
                 'Slab Rebar Object Geometry': safe22_dfs.SlabRebarObjectGeometry,
                 'Slab Rebar Property Assignments': safe22_dfs.SlabRebarPropertyAssignments,
                 'Spandrel Section Properties': safe22_dfs.SpandrelSectionProperties,
                 'Spring Props - Area Springs': safe22_dfs.SpringPropsAreaSprings,
                 'Spring Props - Line Springs': safe22_dfs.SpringPropsLineSprings,
                 'Spring Props - Point Springs': safe22_dfs.SpringPropsPointSprings,
                 'Stl Frm Over Chinese 2018': safe22_dfs.StlFrmOverChinese2018,
                 'Strip Object Connectivity': safe22_dfs.StripObjectConnectivity,
                 'Tendon - Total Elongation': safe22_dfs.TendonTotalElongation,
                 'Tendon Assigns - Properties': safe22_dfs.TendonAssignsProperties,
                 'Tendon Discretized Points': safe22_dfs.TendonDiscretizedPoints,
                 'Tendon Loads - Jacking Stress': safe22_dfs.TendonLoadsJackingStress,
                 'Tendon Loads - Loss Options': safe22_dfs.TendonLoadsLossOptions,
                 'Tendon Object Connectivity': safe22_dfs.TendonObjectConnectivity,
                 'Tendon Section Properties': safe22_dfs.TendonSectionProperties,
                 'Wall Object Connectivity': safe22_dfs.WallObjectConnectivity,
                 'Wall Object Curve Data': safe22_dfs.WallObjectCurveData,
                 'Wall Property Def - Auto Select': safe22_dfs.WallPropertyDefAutoSelect,
                 'Wall Property Def - Specified': safe22_dfs.WallPropertyDefSpecified

                 }    # Create Excel writer object and export all DataFrames
    # Uses pandas ExcelWriter for efficient multi-sheet creation
    with pd.ExcelWriter(file_paths.ExcelSAFE22Model) as writer:
        # Iterate through all SAFE 22 tables and export each to separate sheet
        for sheet_name, df in sheets_df.items():
            # Export DataFrame to Excel sheet with proper table name
            df.to_excel(writer, sheet_name=sheet_name)

    # Post-process the Excel file to clean up formatting
    # Load the workbook for manual formatting adjustments
    wb = oxl.load_workbook(file_paths.ExcelSAFE22Model)
    
    # Clean up each worksheet by removing pandas default formatting
    for ws in wb:
        # Remove the pandas index column (column A)
        ws.delete_cols(1)
        # Remove the multi-level column header rows (row 4 contains units/descriptions)
        ws.delete_rows(4)
    
    # Save the cleaned workbook
    wb.save(file_paths.ExcelSAFE22Model)

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} SAFE22 Model saved to Excel file!')
    return safe22_dfs
