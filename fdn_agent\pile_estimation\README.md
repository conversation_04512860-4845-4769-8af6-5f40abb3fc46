# Pile Estimation Module

This module provides a comprehensive, function-based system for the estimation and design of pile foundations. It integrates various sub-modules to handle the entire workflow, from input data preprocessing to structural element analysis, pile layout generation, and visualization.

## Overview

The `pile_estimation` module serves as the core of the foundation automation system, orchestrating complex calculations and geometric operations to determine optimal pile configurations. Its modular design ensures maintainability, reusability, and scalability.

## Key Capabilities

- **Coordinate Processing**: Resolves and validates structural element coordinates from Excel inputs using comprehensive utility functions.
- **Structural Element Analysis**: Groups structural elements (columns and walls) in 3D and performs load-based sub-clustering.
- **Load Calculation**: Calculates loads from individual structural elements and groups, and determines load-weighted centroids.
- **Pile Cap Geometry**: Generates, manipulates, and validates pile cap polygons that enclose structural elements and pile layouts.
- **Intelligent Pile Cap Generation**: Advanced pile cap sizing that adapts to pile type and diameter for optimal layout boundaries.
- **Pile Layout Generation**: Implements rule-based and optimization-driven algorithms to create efficient pile layouts for various scenarios.
- **Multi-Objective Optimization**: Leverages NSGA-III for complex optimization problems in pile foundation design.
- **Visualization**: Generates detailed DXF drawings of the pile estimation results, including structural elements, pile caps, and pile layouts.
- **Utility Functions**: Provides a collection of general-purpose utilities for geometric operations, mathematical calculations, and data validation.
- **Type Definitions**: Standardizes data models and structures for consistency across the system.

## Sub-Packages

- [`analysis/`](./analysis/README.md): Core functions for 3D grouping and load-based clustering of structural elements.
- [`load_calculator/`](./load_calculator/README.md): Functions for calculating loads, pile requirements, and load-weighted centroids.
- [`optimization/`](./optimization/README.md): Multi-objective optimization algorithms (primarily NSGA-III) for pile foundation design.
- [`pile_cap_geometry/`](./pile_cap_geometry/README.md): Functions for generating, manipulating, and validating pile cap geometries.
- [`pile_layout/`](./pile_layout/README.md): Rule-based and optimization-driven algorithms for generating optimal pile layouts.
- [`types/`](./types/README.md): Defines all custom data types, structures, and protocols used throughout the system.
- [`utils/`](./utils/README.md): General-purpose utility functions including coordinate processing, geometry, layout, math, and validation.
- [`visualizer/`](./visualizer/README.md): Generates visual outputs (DXF files) to represent design and estimation results.

## Main Entry Point

- `pile_estimator.py`: Contains the main orchestration functions, such as `estimate_piles_main`, that coordinate the entire pile estimation process.
- `exceptions.py`: Defines custom exceptions for more meaningful error handling.

## Recent Improvements (v5.6.9+)

### Pile Cap Generation and Boundary Calculation Enhancements

The pile estimation system now features intelligent pile cap generation that adapts to different pile types and their physical characteristics:

#### Key Features

**Pile Type-Specific Maximum Pile Caps**:
- **H-piles (DHP/SHP)**: Use 0.6m equivalent diameter for appropriate cap sizing
- **Bored Piles (BP)**: Use actual pile diameter for precise cap sizing
- **Formula**: `Maximum Pile Cap = Initial Pile Cap + (1 × pile_diameter)`

**Accurate Boundary Calculations**:
- **Formula**: `Possible Pile Boundary = Maximum Pile Cap - pile_radius - edge_distance`
- Ensures proper clearance for pile installation and structural requirements
- Intelligent handling of edge cases and small pile caps

#### Benefits

1. **More Accurate Pile Layouts**: Each pile type gets appropriately sized boundaries
2. **Better Resource Utilization**: Smaller piles don't get oversized caps, larger piles get adequate space
3. **Improved Engineering Logic**: Pile cap size now scales with actual pile dimensions
4. **Enhanced Debugging**: Comprehensive logging shows exactly how calculations are performed

#### Implementation Details

The improvements are implemented in:
- `pile_type_selection/pile_type_preselection.py`: Main logic for pile type-specific cap generation
- `data_types/pile_preselection_types.py`: Updated data structures
- `utils/pile_geometry_utils.py`: Backward compatibility for deprecated functions

## Version

5.6.9