﻿"""
Utility functions for pile estimation calculations.
"""

from .geometry_utils import (
    distance_2d,
    distance_3d,
    closest_point_on_line,
    point_to_line_distance,
    polygon_centroid,
    is_point_in_polygon,
    calculate_polygon_area,
    get_polygon_bounds,
    offset_polygon,
    normalize_angle,
    angle_between_points,
    extract_all_structural_points,
    extract_wall_segments_from_excel,
    group_continuous_walls,
    create_continuous_path,
    create_column_polygons_from_excel,
    create_wall_polygons_from_excel,  
    group_continuous_walls_from_excel
)
from .logging_utils import (
    enhanced_log
)
from .layout_utils import (
    determine_layout_case,
    validate_spacing,
    calculate_pile_center,
    calculate_center_offset,
    apply_site_boundary_filter,
    generate_layout_summary
)
from .math_utils import (
    safe_divide,
    clamp,
    round_up_to_nearest,
    round_to_nearest,
    calculate_weighted_average,
    interpolate_linear,
    is_approximately_equal,
    calculate_statistics,
    find_nearest_value,
    generate_range
)
from .validation_utils import (
    validate_excel_inputs,
    validate_pile_configuration,
    validate_structural_elements,
    validate_pile_locations,
    validate_group_elements,
    validate_polygon,
    validate_coordinate_dataframes,
    validate_pile_cap_polygon,
    clip_to_site_boundary
)
from .coordinate_utils import (
    resolve_coordinates_from_excel,
    find_minimum_area_bounding_rectangle,
    global_to_local_coordinates,
    local_to_global_coordinates,
    generate_local_grid_positions
)
from .pile_preselection_utils import (
    create_pile_type_candidates_from_gui,
    integrate_with_existing_capacity_calculation
)
from .pile_geometry_utils import (
    create_initial_pile_cap,
    enlarge_pile_cap,
    establish_local_coordinate_system,
    generate_aligned_grid,
    filter_by_pile_cap_with_geometry,
    filter_by_site_boundary,
    trim_pile_cap_by_site_boundary
)

__all__ = [
    # Geometry utilities
    'distance_2d',
    'distance_3d',
    'closest_point_on_line',
    'point_to_line_distance',
    'polygon_centroid',
    'is_point_in_polygon',
    'calculate_polygon_area',
    'get_polygon_bounds',
    'offset_polygon',
    'normalize_angle',
    'angle_between_points',
    'extract_all_structural_points',
    'extract_wall_segments_from_excel',
    'group_continuous_walls',
    'create_continuous_path',
    'create_column_polygons_from_excel',
    'create_wall_polygons_from_excel',
    'group_continuous_walls_from_excel',
    
    # Layout utilities
    'determine_layout_case',
    'validate_spacing',
    'calculate_pile_center',
    'calculate_center_offset',
    'apply_site_boundary_filter',
    'generate_layout_summary',
    
    # Math utilities
    'safe_divide',
    'clamp',
    'round_up_to_nearest',
    'round_to_nearest',
    'calculate_weighted_average',
    'interpolate_linear',
    'is_approximately_equal',
    'calculate_statistics',
    'find_nearest_value',
    'generate_range',
    
    # Validation utilities
    'validate_excel_inputs',
    'validate_pile_configuration',
    'validate_structural_elements',
    'validate_pile_locations',
    'validate_group_elements',
    'validate_polygon',
    'validate_coordinate_dataframes',
    'validate_pile_cap_polygon',
    'clip_to_site_boundary',
    
    # Coordinate utilities
    'resolve_coordinates_from_excel',
    'find_minimum_area_bounding_rectangle',
    'global_to_local_coordinates',
    'local_to_global_coordinates',
    'generate_local_grid_positions',
    
    # Pile preselection utilities
    'create_pile_type_candidates_from_gui',
    'integrate_with_existing_capacity_calculation',
    
    # Pile geometry utilities
    'create_initial_pile_cap',
    'enlarge_pile_cap',
    'establish_local_coordinate_system',
    'generate_aligned_grid',
    'filter_by_pile_cap_with_geometry',
    'filter_by_site_boundary',
    'trim_pile_cap_by_site_boundary'
]

