# Visualizer Package for Pile Estimation

This package is responsible for generating visual outputs, primarily DXF files, to represent the pile foundation design and estimation results. It provides comprehensive plotting capabilities for structural elements, pile layouts, pile caps, and other relevant geometric information.

## Overview

The `visualizer` package allows for the creation of detailed engineering drawings from the computational models. It handles the rendering of various components, ensuring accurate representation and facilitating review and communication of the design. The package supports plotting actual pile locations as well as potential candidate positions for analysis.

The package has been redesigned with explicit error handling and predictable behavior, removing silent fallback mechanisms that could mask issues.

## Key Modules

- `core.py`: Contains the main functions for orchestrating the DXF drawing process, including `create_pile_estimation_dxf` and `create_pile_estimation_dxf_with_possible_positions` for generating the final DXF files with all necessary elements.
- `dxf_setup.py`: Provides utility functions for setting up the DXF document, including defining layers, units, and other drawing properties.
- `plotters.py`: Main interface module that imports and re-exports functions from specialized plotting modules for backward compatibility. Serves as a facade for the modular plotting system.
- `base_plotters.py`: Contains fundamental plotting utilities including site boundary plotting and common helper functions.
- `pile_plotters.py`: Specialized module for pile-related plotting including pile groups, pile caps, pile locations, and pile centroids.
- `structure_plotters.py`: Handles plotting of structural elements (columns and walls) with both detailed and simple representations.
- `analysis_plotters.py`: Contains functions for plotting preselection analysis data and possible pile positions.
- `dimension_plotters.py`: Manages dimension annotations, title blocks, and other drawing annotations.
- `text_manager.py`: Implements a text placement manager to avoid overlapping labels and ensure readability in the generated drawings.
- `utils.py`: Contains auxiliary functions for extracting data for visualization (e.g., site boundaries from Excel) and positioning text. Features improved `safe_get` function with explicit validation.
- `validation.py`: Provides comprehensive validation functions to check the integrity and correctness of data before visualization, ensuring robust drawing generation. Includes `ValidationError` exception for explicit error handling.
- `pile_drawer.py`: Specialized functions for drawing different pile types (DHP, SHP, BP) with accurate dimensions and proper DXF layer assignments. Improved with explicit parameter validation.
- `__init__.py`: Exports key functions from the core visualization modules for easy access.

## Key Improvements

### Removed Fallback Mechanisms
- **Pile Cap Creation**: Removed complex fallback logic that attempted to use original pile cap polygons when final cap creation failed. Now uses a single, reliable method.
- **Column/Wall Plotting**: Streamlined plotting functions with clear separation between detailed and simple methods, removing silent fallbacks.
- **Pile Label Drawing**: Removed silent error handling in pile labeling that could mask issues.

### Enhanced Error Handling
- **Explicit Validation**: Added `ValidationError` exception class for clear error reporting.
- **Predictable Behavior**: Functions now fail fast with clear error messages rather than falling back silently.
- **Comprehensive Validation**: Improved validation functions provide detailed feedback on data integrity.

### Improved Code Quality
- **Single Responsibility**: Functions now have clearer, single responsibilities without complex fallback paths.
- **Better Debugging**: Enhanced debugging functions provide more detailed analysis of data issues.
- **Consistent Error Reporting**: Standardized error handling across all modules. 