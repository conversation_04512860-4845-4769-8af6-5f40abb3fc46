"""SSL configuration for handling certificate issues."""

import ssl
import certifi
import os
import logging

def get_ssl_context():
    """Create SSL context with proper certificate handling."""
    context = ssl.create_default_context(cafile=certifi.where())
    
    # For corporate environments, you might need to add custom certificates
    custom_cert_path = os.getenv('CUSTOM_CA_BUNDLE')
    if custom_cert_path and os.path.exists(custom_cert_path):
        context.load_verify_locations(custom_cert_path)
        logging.info(f"Loaded custom CA bundle from: {custom_cert_path}")
    
    return context

def configure_requests_ssl():
    """Configure requests library for SSL handling."""
    import requests
    
    # Check for proxy settings
    http_proxy = os.getenv('HTTP_PROXY') or os.getenv('http_proxy')
    https_proxy = os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
    
    if http_proxy or https_proxy:
        logging.info(f"Proxy detected - HTTP: {http_proxy}, HTTPS: {https_proxy}")
    
    # Try to use system certificates
    try:
        response = requests.get('https://www.google.com', timeout=5)
        if response.status_code == 200:
            logging.info("SSL verification successful")
            return True
    except requests.exceptions.SSLError as e:
        logging.warning(f"SSL verification failed: {e}")
        # For corporate environments, you might need to disable SSL verification
        # This is not recommended for production but might be necessary
        import warnings
        warnings.warn("SSL verification disabled - this is insecure!")
        requests.packages.urllib3.disable_warnings()
        return False
    except Exception as e:
        logging.warning(f"Connection test failed: {e}")
        return True  # Assume SSL is OK, let actual request fail if needed
    
    return True
