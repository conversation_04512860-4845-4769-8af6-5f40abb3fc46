import math

import numpy as np
import pandas as pd
from scipy.spatial import distance
from math import acos, sqrt, pi
from tqdm import tqdm

# Use direct imports to avoid circular dependency
from design_fdn.read import read_pile_base_load
from design_fdn.design_fdn_config import SHEET_LOAD, SHEET_PILING_SCHEDULE


def cal_stepping_effect(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """Calculate pile stepping effect with optimized performance"""
    df_pile = excel_inputs.Pile
    df_load_pattern = excel_inputs.LoadPat
    df_section_h = excel_inputs.SteelSectionH
    df_element_forces = safe_mdbs.ElementForcesColumnsAndBraces
    path_excel_output = file_paths.ResultDesignLoad

    # Extract load patterns into a list
    load_patterns = df_load_pattern['LoadPat (Text)'].tolist()

    # Define initial compression and tension load
    df_init_fz = read_pile_base_load(df_pile, load_patterns, df_load_pattern, df_section_h, df_element_forces)
    piles = df_init_fz.index    # find pile type - vectorized operation
    pile_mask = df_pile['Pile Mark'].isin(piles)
    pile_types = df_pile.loc[pile_mask, 'Pile Type'].values
    is_bp = (pile_types == 'BP').astype(int)
    
    # Create pile type interaction matrix (square matrix)
    # Each cell (i,j) is 1 if both pile i and pile j are BP piles
    is_bp_matrix = np.outer(is_bp, is_bp)
    df_piletype = pd.DataFrame(is_bp_matrix, columns=piles, index=piles)    # if no 'BP' then skip
    if not is_bp.any():
        # Initialize sum DataFrame with zeros
        df_sum_add_fz = pd.DataFrame(0, index=df_init_fz.index, columns=df_init_fz.columns)
        df_fz = df_init_fz.copy()
        df_load = pd.concat([df_init_fz, df_sum_add_fz.add_prefix('Add_'), df_fz.add_prefix('StepE_')], axis=1)
        df_load.to_excel(path_excel_output, sheet_name=SHEET_LOAD)
        design_results.DesignLoad = df_load.reset_index()
        if log_callback:
            log_callback('⚠️  No Bored Piles (BP) detected - stepping effect calculation not required')
        return design_results
    
    # READ SAFE PILE FORCE, CALCULATE PILE SELF WEIGHT
    # Filter efficiently
    mask = safe_mdbs.ElementForcesColumnsAndBraces['Line'].str.contains('_B') & \
           (safe_mdbs.ElementForcesColumnsAndBraces['Station'] != 0)
    df_element_forces = safe_mdbs.ElementForcesColumnsAndBraces.loc[mask].reset_index(drop=True)

    # CALCULATE STEPPING EFFECT
    # Vectorized coordinate extraction
    coordinates = df_pile[['X (m)', 'Y (m)']].values
    dist_piles = distance.cdist(coordinates, coordinates, 'euclidean')
    df_lc = pd.DataFrame(dist_piles, index=piles, columns=piles)

    # Vectorized radius calculations
    diameters = df_pile.loc[pile_mask, 'Pile Base Diameter (m)'].values / 2
    df_r2 = pd.DataFrame(np.outer(diameters, np.ones(len(piles))), columns=piles, index=piles)
    np.fill_diagonal(df_r2.values, 0)
    df_r1 = df_r2.T.copy()

    # Vectorized array operations
    arr_l = dist_piles + df_r2.values
    np.fill_diagonal(arr_l, 0)
    df_l = pd.DataFrame(arr_l, index=piles, columns=piles)

    arr_le = dist_piles - df_r1.values - df_r2.values
    np.fill_diagonal(arr_le, 0)
    df_le = pd.DataFrame(arr_le, index=piles, columns=piles)

    # Vectorized founding level calculations
    fls = df_pile.loc[pile_mask, 'Founding Level (mPD)'].values
    fl_affected = np.tile(fls[:, np.newaxis], (1, len(piles)))
    fl_stepping = fl_affected.T
    np.fill_diagonal(fl_stepping, 0)
    np.fill_diagonal(fl_affected, 0)
    
    df_fl_pile_stepping = pd.DataFrame(fl_stepping, columns=piles, index=piles)
    df_fl_pile_affected = pd.DataFrame(fl_affected, columns=piles, index=piles)
    
    # Calculate level difference
    df_d = df_fl_pile_stepping - df_fl_pile_affected

    # Check piles affected by stepping effect
    pile_affected = (df_d.values >= arr_le).astype(int)
    np.fill_diagonal(pile_affected, 0)
    df_pile_affected = pd.DataFrame(np.where(pile_affected, 'Y', 'N'), index=piles, columns=piles)

    # Vectorized r calculation
    r_values = df_d.values + df_r1.values
    r_values[pile_affected == 0] = np.nan
    df_r = pd.DataFrame(r_values, index=piles, columns=piles).fillna('-')

    # Vectorized angle calculations using numpy
    lc_vals = df_lc.values
    r2_vals = df_r2.values
    l_vals = df_l.values
    
    # Calculate a1 and a2 using vectorized operations
    def vectorized_acos_calc(r_vals, lc_vals, r2_vals, l_vals, is_a1=True):
        result = np.full_like(r_vals, np.nan, dtype=float)
        
        # Create mask for valid calculations
        valid_mask = (r_vals != '-') & (r_vals < l_vals)
        
        if valid_mask.any():
            r_valid = r_vals[valid_mask].astype(float)
            lc_valid = lc_vals[valid_mask]
            
            if is_a1:
                r2_valid = r2_vals[valid_mask]
                numerator = r_valid**2 + lc_valid**2 - r2_valid**2
            else:
                numerator = r2_vals[valid_mask]**2 + lc_valid**2 - r_valid**2
            
            denominator = 2 * lc_valid * (r_valid if is_a1 else r2_vals[valid_mask])
            result[valid_mask] = np.arccos(np.clip(numerator / denominator, -1, 1))
        
        return result
    
    a1_values = vectorized_acos_calc(r_values, lc_vals, r2_vals, l_vals, is_a1=True)
    a2_values = vectorized_acos_calc(r_values, lc_vals, r2_vals, l_vals, is_a1=False)
    
    df_a1 = pd.DataFrame(a1_values, index=piles, columns=piles).fillna('-')
    df_a2 = pd.DataFrame(a2_values, index=piles, columns=piles).fillna('-')

    # Vectorized factor calculation
    factor_values = np.zeros_like(r_values, dtype=float)
    
    # Case 1: r >= l
    mask1 = (r_values != '-') & (r_values.astype(float) >= l_vals)
    if mask1.any():
        factor_values[mask1] = (np.pi * r2_vals[mask1]**2) / (np.pi * l_vals[mask1]**2)
    
    # Case 2: r < l
    mask2 = (r_values != '-') & (r_values.astype(float) < l_vals)
    if mask2.any():
        r_float = r_values[mask2].astype(float)
        a1_float = a1_values[mask2]
        a2_float = a2_values[mask2]
        
        f_1 = np.pi * r_float**2 * 2 * a1_float / (2 * np.pi)
        f_2 = r_float**2 * np.sin(a1_float) * np.cos(a1_float)
        f_3 = np.pi * r2_vals[mask2]**2 * 2 * a2_float / (2 * np.pi)
        f_4 = r2_vals[mask2]**2 * np.sin(a2_float) * np.cos(a2_float)
        f_5 = np.pi * r_float**2
        
        factor_values[mask2] = (f_1 - f_2 + f_3 - f_4) / f_5
    df_factor_add_fz = pd.DataFrame(factor_values * df_piletype.values, index=piles, columns=piles)

    # Vectorized additional fz calculation
    # Pre-allocate the entire result
    n_patterns = len(load_patterns)
    n_piles = len(piles)
    add_fz_data = np.zeros((n_patterns * n_piles, n_piles + 1), dtype=object)
    
    for i, load_pattern in enumerate(load_patterns):
        start_idx = i * n_piles
        end_idx = start_idx + n_piles
        
        # Get initial loads and clip negative values
        initial_loads = np.maximum(df_init_fz[load_pattern].values, 0)
        
        # Calculate additional loads
        temp_add = initial_loads[:, np.newaxis] * factor_values
        
        # Fill the data array
        add_fz_data[start_idx:end_idx, 0] = load_pattern
        add_fz_data[start_idx:end_idx, 1:] = temp_add
    
    # Create DataFrame from the pre-allocated array
    df_add_fz = pd.DataFrame(add_fz_data, columns=['Load Case'] + list(piles))
    df_add_fz['Pile'] = np.tile(piles, n_patterns)
    df_add_fz.set_index(['Load Case', 'Pile'], inplace=True)

    # Calculate sum of additional fz
    df_sum_add_fz = pd.DataFrame(index=df_init_fz.index, columns=load_patterns)
    for load_pattern in load_patterns:
        df_sum_add_fz[load_pattern] = df_add_fz.xs(load_pattern).sum(axis=1)

    # Write results
    with pd.ExcelWriter(path_excel_output) as writer:
        # Write all sheets
        sheets = {
            'Lc': df_lc, 'r1': df_r1, 'r2': df_r2, 'L': df_l, 'Le': df_le,
            'fl_pile_stepping': df_fl_pile_stepping, 'fl_pile_affected': df_fl_pile_affected,
            'D': df_d, 'PileAffected': df_pile_affected, 'R': df_r,
            'a1': df_a1, 'a2': df_a2, 'Factor_SteppingEffect': df_factor_add_fz,
            'Pile Data': df_pile, 'All Additional Load': df_add_fz,
            'Sum Additional Load': df_sum_add_fz
        }
        
        for sheet_name, df in sheets.items():
            df.to_excel(writer, sheet_name=sheet_name)
        
        # Write additional load patterns
        for load_pattern in load_patterns:
            sheet_name = f'Additional {load_pattern}'
            df_add_fz.xs(load_pattern).add_prefix('Additional Load Due To ').to_excel(writer, sheet_name=sheet_name)
          # Final load calculation
        df_fz = df_init_fz + df_sum_add_fz
        df_load = pd.concat([df_init_fz, df_sum_add_fz.add_prefix('Add_'), df_fz.add_prefix('StepE_')], axis=1)
        df_load.to_excel(writer, sheet_name=SHEET_LOAD)

    design_results.DesignLoad = df_load.reset_index()
    if log_callback:
        log_callback('✅ Generated pile design loads with stepping effect analysis!')
    return design_results


def cal_soil_rock_cone(tl, rl, fl, dia, tension_capacity):
    """Calculate soil rock cone with simplified calculations"""
    # Constants
    w_rock = 22 - 9.81  # kN/m^3
    w_soil = 19 - 9.81  # kN/m^3
    w_concrete = 24.5 - 9.81  # kN/m^3
    angle_rad = math.radians(30)
    tan_30 = math.tan(angle_rad)
    
    # Diameter calculations
    dia_rc = dia + (rl - fl) * tan_30 * 2  # m
    
    # Height calculations
    l2 = 0.5 * (dia_rc - dia) / tan_30
    l1 = l2 - (rl - fl)
    l3 = dia / 2 / tan_30
    
    # Volume calculations using simplified formulas
    pi_4 = math.pi / 4
    v1 = pi_4 * (dia_rc**2 - dia**2) * l1
    v2 = pi_4 / 3 * dia_rc**2 * (l2 + l3) - pi_4 * dia**2 * l2
    v3 = pi_4 / 3 * dia**2 * l3
    
    # Final volume calculations
    volume_soil_cylinder = pi_4 * (dia_rc**2 - dia**2) * (tl - rl)
    volume_rock_cone = v1 + v2 - v3
    volume_pile = pi_4 * dia**2 * (tl - fl)
    
    # Calculate uplift resistance
    ru_limit = w_concrete * volume_pile + w_soil * volume_soil_cylinder + w_rock * volume_rock_cone
    ra = tension_capacity
    ru = min(ru_limit, 2 * ra)
    
    return ru, ra


def gen_piling_schedule(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """Generate piling schedule with optimized performance"""
    df_load = design_results.DesignLoad
    df_pile_capacity = design_results.PileCapacity
    df_pile = excel_inputs.Pile
    df_load_pattern = excel_inputs.LoadPat
    path_output_piling_schedule = file_paths.ResultPilingSchedule
    
    # Prepare Ru Ra data
    ru_ra = df_pile_capacity[['Pile Mark', 'Ra [Transient] (kN)', 'Ra [Permanent] (kN)', 
                              'Ru [Transient] (kN)', 'Ru [Permanent] (kN)']].copy()

    # Initialize design load DataFrame
    load_columns = ['Pile Mark', 'SWP (kN)', 'Dmin (kN)', 'DL (kN)', 'SDL (kN)', 
                    'LL (kN)', 'SL (kN)', 'Ua (kN)', 'Up (kN)', 'U (kN)', 'NSF (kN)']
    df_init_design_load = pd.DataFrame(0, index=df_load.index, columns=load_columns)
    df_init_design_load['Pile Mark'] = df_load['Pile']
    
    # Map load patterns to columns efficiently
    load_mapping = {
        'SELF WEIGHT': 'SWP (kN)',
        'DEAD': 'DL (kN)',
        'SUPERIMPOSED DEAD': 'SDL (kN)',
        'LIVE': 'LL (kN)',
        'SOIL': 'SL (kN)',
        'ANTICIPATED UPLIFT': 'Ua (kN)',
        'POSSIBLE UPLIFT': 'Up (kN)',
        'NEGATIVE SKIN FRICTION': 'NSF (kN)'
    }
    
    list_wl = []
    for idx, row in df_load_pattern.iterrows():
        load_name = row['LoadPat (Text)']
        load_type = row['Load Type']
        
        if load_type in load_mapping:
            df_init_design_load[load_mapping[load_type]] = df_load[load_name]
        elif load_type == 'WIND':
            df_init_design_load[load_name] = df_load[load_name]
            list_wl.append(load_name)
    
    # Calculate wind loads using vectorized operations
    if list_wl:
        df_init_design_load['WL[Max Compression] (kN)'] = df_init_design_load[list_wl].max(axis=1).clip(lower=0)
        df_init_design_load['WL[Max Tension] (kN)'] = df_init_design_load[list_wl].min(axis=1).clip(upper=0)
    else:
        df_init_design_load['WL[Max Compression] (kN)'] = 0
        df_init_design_load['WL[Max Tension] (kN)'] = 0

    # Process stepping effect loads
    step_columns = ['Pile Mark', 'StepE_SWP (kN)', 'StepE_Dmin (kN)', 'StepE_DL (kN)', 
                    'StepE_SDL (kN)', 'StepE_LL (kN)', 'StepE_SL (kN)']
    df_step_design_load = pd.DataFrame(0, index=df_load.index, columns=step_columns)
    df_step_design_load['Pile Mark'] = df_load['Pile']
    
    step_load_mapping = {
        'SELF WEIGHT': 'StepE_SWP (kN)',
        'DEAD': 'StepE_DL (kN)',
        'SUPERIMPOSED DEAD': 'StepE_SDL (kN)',
        'LIVE': 'StepE_LL (kN)',
        'SOIL': 'StepE_SL (kN)'
    }
    
    list_step_wl = []
    for idx, row in df_load_pattern.iterrows():
        load_name = row['LoadPat (Text)']
        load_type = row['Load Type']
        step_load_name = f'StepE_{load_name}'
        
        if load_type in step_load_mapping:
            df_step_design_load[step_load_mapping[load_type]] = df_load[step_load_name]
        elif load_type == 'WIND':
            df_step_design_load[step_load_name] = df_load[step_load_name]
            list_step_wl.append(step_load_name)
    
    # Calculate stepping effect wind loads
    if list_step_wl:
        df_step_design_load['StepE_WL[Max Compression] (kN)'] = df_step_design_load[list_step_wl].max(axis=1).clip(lower=0)
        df_step_design_load['StepE_WL[Max Tension] (kN)'] = df_step_design_load[list_step_wl].min(axis=1).clip(upper=0)
    else:
        df_step_design_load['StepE_WL[Max Compression] (kN)'] = 0
        df_step_design_load['StepE_WL[Max Tension] (kN)'] = 0

    # Merge design loads
    df_design_load = pd.merge(df_init_design_load, df_step_design_load, on='Pile Mark')
    
    # Calculate U and Dmin
    df_design_load['U (kN)'] = df_design_load[['Ua (kN)', 'Up (kN)']].min(axis=1)
    df_design_load['Dmin (kN)'] = df_design_load['DL (kN)'] - df_design_load['SWP (kN)']
    df_design_load['StepE_Dmin (kN)'] = df_design_load['StepE_DL (kN)'] - df_design_load['StepE_SWP (kN)']
    
    # Calculate Ia (negative LL & SL)
    df_design_load['Ia (kN)'] = (df_design_load[['LL (kN)', 'SL (kN)']].clip(upper=0).sum(axis=1))
    
    # Add RuRa to design load
    df_design_load = df_design_load.merge(ru_ra, how='left', on='Pile Mark')
    
    # Vectorized compression calculations
    df_design_load['StepE[Dmin+SDL+LL] (kN)'] = (
        df_design_load[['StepE_Dmin (kN)', 'StepE_SDL (kN)', 'StepE_LL (kN)']].sum(axis=1)
    )
    df_design_load['StepE[Dmin+SDL+LL+SL] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL] (kN)'] + df_design_load['StepE_SL (kN)']
    )
    
    # With wind
    df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL] (kN)'] + df_design_load['StepE_WL[Max Compression] (kN)']
    )
    df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]+SL] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]] (kN)'] + df_design_load['StepE_SL (kN)']
    )
    
    # With NSF
    df_design_load['StepE[Dmin+SDL+LL+NSF] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL] (kN)'] + df_design_load['NSF (kN)']
    )
    df_design_load['StepE[Dmin+SDL+LL+NSF+SL] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL+NSF] (kN)'] + df_design_load['StepE_SL (kN)']
    )
    
    # With NSF & Wind
    df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]+NSF] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]] (kN)'] + df_design_load['NSF (kN)']
    )
    df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]+NSF+SL] (kN)'] = (
        df_design_load['StepE[Dmin+SDL+LL+WL[Max Compression]+NSF] (kN)'] + df_design_load['StepE_SL (kN)']
    )
    
    # Calculate max compressions
    compression_cols_wo_wl = [
        'StepE[Dmin+SDL+LL] (kN)', 'StepE[Dmin+SDL+LL+NSF] (kN)', 
        'StepE[Dmin+SDL+LL+SL] (kN)', 'StepE[Dmin+SDL+LL+NSF+SL] (kN)'
    ]
    df_design_load['Max Compression [w/o WL] (kN)'] = df_design_load[compression_cols_wo_wl].max(axis=1).clip(lower=0)
    
    compression_cols_w_wl = [
        'StepE[Dmin+SDL+LL+WL[Max Compression]] (kN)',
        'StepE[Dmin+SDL+LL+WL[Max Compression]+NSF] (kN)',
        'StepE[Dmin+SDL+LL+WL[Max Compression]+SL] (kN)',
        'StepE[Dmin+SDL+LL+WL[Max Compression]+NSF+SL] (kN)'
    ]
    df_design_load['Max Compression [w/ WL] (kN)'] = df_design_load[compression_cols_w_wl].max(axis=1).clip(lower=0)
    
    # Add capacity checks
    df_design_load = df_design_load.merge(
        df_pile_capacity[['Pile Mark', 'Compression Capacity [w/o WL] (kN)', 'Compression Capacity [w/ WL] (kN)']], 
        how='left', on='Pile Mark'
    )
    
    # Compression checks
    df_design_load['Max Compression [w/o WL] Check'] = np.where(
        df_design_load['Max Compression [w/o WL] (kN)'] <= df_design_load['Compression Capacity [w/o WL] (kN)'],
        'OK', 'NOT OK'
    )
    df_design_load['Max Compression [w/ WL] Check'] = np.where(
        df_design_load['Max Compression [w/ WL] (kN)'] <= df_design_load['Compression Capacity [w/ WL] (kN)'],
        'OK', 'NOT OK'
    )
    
    # Stability calculations - vectorized
    df_design_load['Dmin+2Ia+1.5Ua+1.5WL[Max Tension] (kN)'] = (
        df_design_load['Dmin (kN)'] + 2 * df_design_load['Ia (kN)'] + 
        1.5 * df_design_load['Ua (kN)'] + 1.5 * df_design_load['WL[Max Tension] (kN)']
    )
    df_design_load['Dmin+2Ia+1.1Up+1.5WL[Max Tension] (kN)'] = (
        df_design_load['Dmin (kN)'] + 2 * df_design_load['Ia (kN)'] + 
        1.1 * df_design_load['Up (kN)'] + 1.5 * df_design_load['WL[Max Tension] (kN)']
    )
    
    # Ru Transient calculations - only process negative values
    min_values = np.minimum(
        df_design_load['Dmin+2Ia+1.5Ua+1.5WL[Max Tension] (kN)'],
        df_design_load['Dmin+2Ia+1.1Up+1.5WL[Max Tension] (kN)']
    )
    df_design_load['Required Ru,T (kN)'] = np.where(
        min_values < 0,
        np.abs(min_values / 0.9),
        0
    )
    
    df_design_load['Provided Ru,T (kN)'] = np.where(
        df_design_load['Required Ru,T (kN)'] > 0,
        df_design_load['Ru [Transient] (kN)'], 0
    )
    df_design_load['Ru [Transient] Check'] = np.where(
        df_design_load['Provided Ru,T (kN)'] >= df_design_load['Required Ru,T (kN)'],
        'OK', 'NOT OK'
    )
    
    # Ru Permanent calculations
    df_design_load['Dmin+2Ia+1.5Ua (kN)'] = (
        df_design_load['Dmin (kN)'] + 2 * df_design_load['Ia (kN)'] + 1.5 * df_design_load['Ua (kN)']
    )
    df_design_load['Dmin+2Ia+1.1Up (kN)'] = (
        df_design_load['Dmin (kN)'] + 2 * df_design_load['Ia (kN)'] + 1.1 * df_design_load['Up (kN)']
    )


    # Ru Transient calculations - only process negative values
    min_values = np.minimum(
        df_design_load['Dmin+2Ia+1.5Ua (kN)'],
        df_design_load['Dmin+2Ia+1.1Up (kN)']
    )
    df_design_load['Required Ru,P (kN)'] = np.where(
        min_values < 0,
        np.abs(min_values / 0.9),
        0
    )
    
    df_design_load['Provided Ru,P (kN)'] = np.where(
        df_design_load['Required Ru,P (kN)'] > 0,
        df_design_load['Ru [Permanent] (kN)'], 0
    )
    df_design_load['Ru [Permanent] Check'] = np.where(
        df_design_load['Provided Ru,P (kN)'] >= df_design_load['Required Ru,P (kN)'],
        'OK', 'NOT OK'
    )
    
    # Ra Transient calculations
    df_design_load['Dmin+Ia+Ua+WL[Max Tension] (kN)'] = (
        df_design_load['Dmin (kN)'] + df_design_load['Ia (kN)'] +
        df_design_load['Ua (kN)'] + df_design_load['WL[Max Tension] (kN)']
    )
    df_design_load['Dmin+Ia+Up+WL[Max Tension] (kN)'] = (
        df_design_load['Dmin (kN)'] + df_design_load['Ia (kN)'] +
        df_design_load['Up (kN)'] + df_design_load['WL[Max Tension] (kN)']
    )
    
    # Ra Transient calculations - only process negative values
    min_values = np.minimum(
        df_design_load['Dmin+Ia+Ua+WL[Max Tension] (kN)'],
        df_design_load['Dmin+Ia+Up+WL[Max Tension] (kN)']
    )
    df_design_load['Required Ra,T (kN)'] = np.where(
        min_values < 0,
        np.abs(min_values),
        0
    )
    
    df_design_load['Provided Ra,T (kN)'] = np.where(
        df_design_load['Required Ra,T (kN)'] > 0,
        df_design_load['Ra [Transient] (kN)'], 0
    )
    df_design_load['Ra [Transient] Check'] = np.where(
        df_design_load['Provided Ra,T (kN)'] >= df_design_load['Required Ra,T (kN)'],
        'OK', 'NOT OK'
    )
    
    # Ra Permanent calculations
    df_design_load['Dmin+Ia+Ua (kN)'] = (
        df_design_load['Dmin (kN)'] + df_design_load['Ia (kN)'] + df_design_load['Ua (kN)']
    )
    df_design_load['Dmin+Ia+Up (kN)'] = (
        df_design_load['Dmin (kN)'] + df_design_load['Ia (kN)'] + df_design_load['Up (kN)']
    )

    min_values = np.minimum(
        df_design_load['Dmin+Ia+Ua (kN)'],
        df_design_load['Dmin+Ia+Up (kN)']
    )
    df_design_load['Required Ra,P (kN)'] = np.where(
        min_values < 0,
        np.abs(min_values),
        0
    )
    
    df_design_load['Provided Ra,P (kN)'] = np.where(
        df_design_load['Required Ra,P (kN)'] > 0,
        df_design_load['Ra [Permanent] (kN)'], 0
    )
    df_design_load['Ra [Permanent] Check'] = np.where(
        df_design_load['Provided Ra,P (kN)'] >= df_design_load['Required Ra,P (kN)'],
        'OK', 'NOT OK'
    )
    
    # Tension pile analysis
    df_design_load['Tension Pile (Y/N)'] = np.where(
        (df_design_load['Dmin+Ia+Ua (kN)'] >= 0) & (df_design_load['Dmin+Ia+Up (kN)'] >= 0),
        'N', 'Y'
    )
    
    # Simplified Max Tension calculation using numpy
    df_design_load['Max Tension [Permanent] (kN)'] = np.abs(
        np.minimum(df_design_load['Dmin+Ia+Ua (kN)'], df_design_load['Dmin+Ia+Up (kN)'])
    ).clip(lower=0)
    
    df_design_load['1/2 Compression Capacity [w/o WL] (kN)'] = df_design_load['Compression Capacity [w/o WL] (kN)'] / 2
    df_design_load['Need Tension Pile Test (Y/N)'] = np.where(
        df_design_load['Max Tension [Permanent] (kN)'] > df_design_load['1/2 Compression Capacity [w/o WL] (kN)'],
        'Y', 'N'
    )
    
    # Utilization calculations - vectorized with division by zero handling
    with np.errstate(divide='ignore', invalid='ignore'):
        df_design_load['Utilization (Compression [w/o WL])'] = np.where(
            df_design_load['Compression Capacity [w/o WL] (kN)'] != 0,
            df_design_load['Max Compression [w/o WL] (kN)'] / df_design_load['Compression Capacity [w/o WL] (kN)'],
            0
        )
        df_design_load['Utilization (Compression [w/ WL])'] = np.where(
            df_design_load['Compression Capacity [w/ WL] (kN)'] != 0,
            df_design_load['Max Compression [w/ WL] (kN)'] / df_design_load['Compression Capacity [w/ WL] (kN)'],
            0
        )
        
        # Conditional utilization calculations
        for util_type, req_col, prov_col in [
            ('Ra,T', 'Required Ra,T (kN)', 'Provided Ra,T (kN)'),
            ('Ra,P', 'Required Ra,P (kN)', 'Provided Ra,P (kN)'),
            ('Ru,T', 'Required Ru,T (kN)', 'Provided Ru,T (kN)'),
            ('Ru,P', 'Required Ru,P (kN)', 'Provided Ru,P (kN)')
        ]:
            mask = df_design_load[req_col] > 0
            df_design_load[f'Utilization ({util_type})'] = 0
            df_design_load.loc[mask, f'Utilization ({util_type})'] = (
                df_design_load.loc[mask, req_col] / df_design_load.loc[mask, prov_col]
            )
      # MAX Utilization
    df_design_load['MAX Utilization'] = df_design_load[[
        'Utilization (Compression [w/o WL])', 'Utilization (Compression [w/ WL])'
    ]].max(axis=1)
    
    # Overall check
    check_columns = ['Ra [Permanent] Check', 'Ra [Transient] Check', 
                     'Ru [Permanent] Check', 'Ru [Transient] Check',
                     'Max Compression [w/o WL] Check', 'Max Compression [w/ WL] Check']
    df_design_load['Overall Check'] = np.where(
        (df_design_load[check_columns] == 'OK').all(axis=1),
        'OK', 'NOT OK'
    )
      # Write to Excel
    df_design_load.to_excel(path_output_piling_schedule, sheet_name=SHEET_PILING_SCHEDULE, index=False)
    
    design_results.PilingSchedule = df_design_load
    if log_callback:
        log_callback('✅ Generated comprehensive piling schedule with capacity verification!')
    return design_results
