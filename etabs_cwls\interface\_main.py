"""
Main Processing Module for ETABS CWLS (Refactored)

This module serves as the main entry point and coordinator for the ETABS Core
Wall Loading Schedule system. It has been refactored into focused, maintainable
modules following professional software engineering practices.

The refactored architecture includes:
- console_interface: Console-based user interface (deprecated)
- configuration_manager: ETABS version-specific configuration
- file_path_manager: File path validation and management
- initialization_processor: Design parameter initialization
- pier_force_processor: Pier force data processing
- joint_reaction_processor: Joint reaction data processing
- coordinate_transformer: Mathematical coordinate transformations
- schedule_generator: Load schedule generation and combination

This module now provides a clean API while delegating specific functionality
to specialized modules, improving maintainability and testability.

Key Benefits of Refactoring:
- Single Responsibility Principle: Each module has a focused purpose
- Improved Maintainability: Smaller, focused modules are easier to maintain
- Better Testability: Individual modules can be tested in isolation
- Enhanced Readability: Clear separation of concerns
- Reduced Complexity: Large monolithic file broken into manageable pieces
- Professional Architecture: Follows industry best practices

Version: 5.6.9 - Refactored Professional Edition
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import sys
import warnings
from typing import Optional, Callable

# Import refactored modules
from etabs_cwls.interface.console_interface import main as console_main
from etabs_cwls.config.configuration_manager import assign_ETABS_converter
from etabs_cwls.io.file_path_manager import filepath_selection
from etabs_cwls.processing.initialization_processor import initialization
from etabs_cwls.processing.pier_force_processor import create_schedule_pier_force
from etabs_cwls.processing.joint_reaction_processor import create_schedule_joint_reaction
from etabs_cwls.processing.coordinate_transformer import create_raw_schedule, integer_round
from etabs_cwls.processing.schedule_generator import create_final_schedule
from etabs_cwls.config.logging_config import enhanced_log, get_logger

# Re-export all functions for backward compatibility
# This ensures that imports like "from etabs_cwls._main import create_schedule_pier_force" work
__all__ = [
    'main',
    'assign_ETABS_converter',
    'filepath_selection',
    'initialization',
    'create_schedule_pier_force',
    'create_schedule_joint_reaction',
    'create_raw_schedule',
    'create_final_schedule',
    'integer_round'
]


def main(log_callback: Optional[Callable] = None) -> None:
    """
    Console-based main entry point for ETABS CWLS processing.
    
    This function delegates to the refactored console_interface module.
    It maintains backward compatibility while using the new modular architecture.
    
    Args:
        log_callback: Optional logging callback function
        
    Raises:
        ConfigurationError: If system configuration fails
        DataValidationError: If user input validation fails
        
    Example:
        >>> from etabs_cwls._main import main
        >>> main()  # Launches console interface (deprecated)
    """
    # Delegate to the refactored console interface
    console_main(log_callback)


# Functions are already exported above


if __name__ == "__main__":
    """
    Main execution block for console interface.
    
    This block sets up the environment and launches the refactored console
    interface. For production use, the GUI interface is recommended.
    """
    try:
        # Suppress pandas warnings for cleaner console output
        warnings.simplefilter(action='ignore', category=UserWarning)
        warnings.simplefilter(action='ignore', category=FutureWarning)
        
        # Display refactoring notice
        print("=" * 60)
        print("ETABS CWLS - Refactored Professional Edition")
        print("=" * 60)
        print("✓ Modular Architecture: Code split into focused modules")
        print("✓ Enhanced Maintainability: Easier to update and extend")
        print("✓ Better Error Handling: Comprehensive exception management")
        print("✓ Professional Standards: Industry best practices applied")
        print("=" * 60)
        
        # Launch main console interface
        main()
        
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user. Goodbye!")
        sys.exit(0)
        
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        print("Please check the log file for detailed error information.")
        sys.exit(1)
