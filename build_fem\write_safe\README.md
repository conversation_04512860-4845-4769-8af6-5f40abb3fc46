# SAFE Model Writing Package

This package provides functionality for writing various components of SAFE finite element models, serving as a core component of the Foundation Automation system's FEM building capabilities.

## Package Structure

The package is organized into several key modules and subpackages:

### Core Writing Modules

1. `write_geometry.py`
   - Handles geometric definitions and assignments
   - Functions for writing points, beams, columns, walls, slabs, and load paths
   - Manages group assignments and line load assignments

2. `write_load.py`
   - Manages load definitions and applications
   - Functions for writing point loads, line loads, pile loads, slab loads, and wall loads
   - Supports corewall load definitions

3. `write_load_comb.py`
   - Manages load combinations and analysis cases
   - Functions for writing load patterns, load cases, and load combinations

4. `write_material.py`
   - Defines material properties and characteristics
   - Functions for writing material definitions

5. `write_str_prop.py`
   - Manages structural properties and definitions
   - Functions for writing beam, column, and slab properties
   - Supports circular and H-section column properties

6. `write_bp_shp_dhp.py`
   - Handles beam properties, shapes, and dimensions
   - Functions for writing beam section properties

7. `write_design_strip.py`
   - Manages design strip definitions
   - Functions for writing design strip configurations

8. `write_load_output.py`
   - Manages load output definitions
   - Functions for writing load output configurations

9. `write_mp.py`
   - Handles model property definitions
   - Functions for writing model properties

10. `write_soil.py`
    - Manages soil property definitions
    - Functions for writing soil properties

### Subpackages

1. `safe16/`
   - SAFE 16 specific implementation
   - Contains SAFE 16 specific data structures and functionality
   - Uses pandas DataFrames for data management

2. `safe22/`
   - SAFE 22 specific implementation
   - Contains SAFE 22 specific data structures and functionality
   - Supports advanced analysis features

## Key Features

### Model Component Writing
- Comprehensive support for all SAFE model components
- Standardized writing functions for consistent model creation
- Support for both SAFE 16 and SAFE 22 formats

### Data Management
- Structured data handling for all model components
- Consistent function signatures across modules
- Error handling and validation

### Integration
- Seamless integration with SAFE software
- Compatibility with both SAFE 16 and SAFE 22
- Support for modern analysis features

## Usage

The package exposes all writing functions through its `__init__.py`:

```python
from write_safe import write_point_assign, write_beam_load, write_material

# Example usage
write_point_assign(...)
write_beam_load(...)
write_material(...)
```

## Available Functions

### Geometry
- `write_point_assign`: Point assignments
- `write_beam_assign`: Beam assignments
- `write_column_assign`: Column assignments
- `write_wall_assign`: Wall assignments
- `write_slab_assign`: Slab assignments
- `write_lkp_assign`: Look-up path assignments
- `write_slab_group`: Slab group definitions
- `write_line_load_assign`: Line load assignments

### Loads
- `write_point_load`: Point load definitions
- `write_line_load`: Line load definitions
- `write_pile_load`: Pile load definitions
- `write_slab_load`: Slab load definitions
- `write_lkp_load`: Look-up path load definitions
- `write_beam_load`: Beam load definitions
- `write_column_load`: Column load definitions
- `write_wall_load`: Wall load definitions
- `write_corewall_load`: Corewall load definitions

### Load Combinations
- `write_load_pattern`: Load pattern definitions
- `write_load_case`: Load case definitions
- `write_load_combination`: Load combination definitions

### Materials
- `write_material`: Material property definitions

### Structural Properties
- `write_beam_prop`: Beam property definitions
- `write_column_prop_circular`: Circular column properties
- `write_column_prop_H`: H-section column properties
- `write_column_prop`: General column properties
- `write_slab_prop`: Slab property definitions

### Beam Properties
- `write_bp_shp_dhp`: Beam section properties

### Design Strips
- `write_design_strip`: Design strip configurations

### Load Outputs
- `write_load_output`: Load output configurations

### Model Properties
- `write_mp`: Model property definitions

### Soil Properties
- `write_soil`: Soil property definitions

## Best Practices

1. Use the standardized function signatures across all modules
2. Validate input data before writing
3. Follow SAFE naming conventions
4. Use appropriate subpackage (SAFE16 or SAFE22) based on requirements
5. Handle errors and exceptions appropriately

## Error Handling

The package includes error handling for:
- Invalid input data
- Missing required parameters
- Data consistency violations
- SAFE format specific validations

## Integration Points

This package integrates with:
- Foundation Automation system's FEM building capabilities
- SAFE 16 and SAFE 22 finite element analysis software
- Other structural analysis modules in the system

## Compatibility

This package supports:
- SAFE 16 format through safe16/ subpackage
- SAFE 22 format through safe22/ subpackage
- Modern analysis features and capabilities
