"""
Reinforcement Design Module for Bored Pile Foundation Analysis

This module provides comprehensive reinforcement design and optimization capabilities
for bored pile foundations in Ultimate Limit State (ULS) analysis. It implements
advanced algorithms for determining optimal longitudinal reinforcement configurations
while considering constructability constraints, code compliance requirements, and
structural performance criteria for circular reinforced concrete pile sections.

The module performs reinforcement design including:
- Multi-layer reinforcement configuration optimization with constructability constraints
- Steel ratio optimization using binary search algorithms for efficient design
- Geometric constraint handling for circular section reinforcement placement
- Integration with moment-axial (M-N) interaction analysis for design verification
- Pre-calculated curve database utilization for computational efficiency enhancement

Key Features:
- Intelligent reinforcement layout optimization for circular sections
- Multi-layer (up to 3 layers) reinforcement configuration with spacing controls
- Binary search optimization for minimum steel ratio determination
- Constructability-driven design with standard reinforcement sizes and spacing
- Integration with pre-calculated M-N curve databases for performance optimization
- Comprehensive geometric validation for reinforcement placement feasibility

Author: Foundation Design Automation Team
Version: 1.0
"""

from math import pi, acos, ceil
from copy import copy
from typing import Tuple, Dict, Optional, List, Any
# Use direct imports to avoid circular import
from .circular_column import CircularColumn
from .nm_curve_calc import calculate_nm_curve
from .nm_validation import check_nm_points
import numpy as np
import pandas as pd

# Pre-defined standard reinforcement sizes commonly available in construction
# These sizes reflect market availability and construction industry standards
REBAR_SIZES = [20, 25, 32, 40, 50]  # Standard bar diameters in millimeters


def update_rebar_configuration(column: CircularColumn, steel_ratio: float) -> CircularColumn:
    """
    Update multi-layer reinforcement configuration to achieve target steel ratio.
    
    This function implements an intelligent optimization algorithm to determine the
    most efficient reinforcement configuration that meets the target steel ratio
    while satisfying constructability constraints and geometric limitations for
    circular pile sections. The optimization considers standard bar sizes and
    spacing requirements to ensure practical construction feasibility.
    
    The optimization process systematically evaluates:
    - Standard reinforcement bar sizes for each layer configuration
    - Geometric constraints for multi-layer reinforcement placement
    - Spacing requirements for concrete consolidation and quality control
    - Constructability limitations including cover and clearance requirements
    """
    # Extract initial reinforcement diameters with minimum standard size
    rebar_dia1 = column.layer1_rebar_dia or REBAR_SIZES[0]
    rebar_dia2 = column.layer2_rebar_dia or REBAR_SIZES[0]
    rebar_dia3 = column.layer3_rebar_dia or REBAR_SIZES[0]
    
    # Find current positions in standard rebar size list for optimization starting points
    pos1 = REBAR_SIZES.index(rebar_dia1) if rebar_dia1 in REBAR_SIZES else 0
    pos2 = REBAR_SIZES.index(rebar_dia2) if rebar_dia2 in REBAR_SIZES else 0
    pos3 = REBAR_SIZES.index(rebar_dia3) if rebar_dia3 in REBAR_SIZES else 0
    
    # Calculate target reinforcement area based on steel ratio and column geometry
    as_target = steel_ratio * column.area
    
    # Iterative optimization loop with safety limit to prevent infinite loops
    for iteration in range(100):  # Safety limit for optimization convergence
        # Get current reinforcement diameters from position indices
        rebar_dia1 = REBAR_SIZES[pos1]
        rebar_dia2 = REBAR_SIZES[pos2]
        rebar_dia3 = REBAR_SIZES[pos3]
        
        # Calculate geometric parameters for current bar diameter configuration
        layers = _calculate_layer_geometry(column, rebar_dia1, rebar_dia2, rebar_dia3)
        
        if layers is None:
            # Geometry is invalid - increase bar sizes to improve fit
            if pos1 < len(REBAR_SIZES) - 1: pos1 += 1
            if pos2 < len(REBAR_SIZES) - 1: pos2 += 1
            if pos3 < len(REBAR_SIZES) - 1: pos3 += 1
            continue
        
        # Calculate required number of rebars for each layer to achieve target area
        rebar_nums = _calculate_rebar_numbers(layers, as_target)
        
        if rebar_nums is None:
            # Cannot achieve required area with current configuration - increase bar sizes
            if pos1 < len(REBAR_SIZES) - 1: pos1 += 1
            if pos2 < len(REBAR_SIZES) - 1: pos2 += 1
            if pos3 < len(REBAR_SIZES) - 1: pos3 += 1
            continue
        
        # Verify that spacing requirements are satisfied for constructability
        if _check_spacing_requirements(column, rebar_nums[0]):
            # Configuration is valid - update column object and return
            column.layer1_rebar_num = rebar_nums[0]
            column.layer2_rebar_num = rebar_nums[1]
            column.layer3_rebar_num = rebar_nums[2]
            column.layer1_rebar_dia = rebar_dia1
            column.layer2_rebar_dia = rebar_dia2
            column.layer3_rebar_dia = rebar_dia3
            return column
        
        # Spacing requirements not met - adjust bar sizes based on spacing analysis
        spacing = pi * column.diameter / rebar_nums[0] if rebar_nums[0] > 0 else 400
        if spacing < 150:  # Too congested - increase bar sizes
            if pos1 < len(REBAR_SIZES) - 1: pos1 += 1
            if pos2 < len(REBAR_SIZES) - 1: pos2 += 1
            if pos3 < len(REBAR_SIZES) - 1: pos3 += 1
        elif spacing > 400 or rebar_nums[0] < 6:  # Too sparse - decrease bar sizes
            if pos1 > 0: pos1 -= 1
            if pos2 > 0: pos2 -= 1
            if pos3 > 0: pos3 -= 1
    
    # Return original column if optimization fails to converge
    return column


def _calculate_layer_geometry(
    column: CircularColumn, 
    d1: float, 
    d2: float, 
    d3: float
) -> Optional[Dict[str, Any]]:
    """
    Calculate geometric parameters for multi-layer reinforcement arrangement.
    
    This function determines the geometric feasibility and capacity parameters
    for a three-layer reinforcement configuration in a circular pile section.
    It calculates the radial positions, maximum reinforcement quantities, and
    geometric constraints for each reinforcement layer while ensuring adequate
    concrete cover and bar spacing requirements.
    """
    # Extract geometric parameters from column configuration
    cover = column.cover                    # Concrete cover to reinforcement
    spacing = column.rebar_clear_spacing   # Clear spacing between bars
    radius = column.radius                 # Pile radius
    
    # Calculate radial positions for each reinforcement layer
    # Layer 1 (outermost): radius minus cover minus half bar diameter
    r1 = radius - cover - d1/2
    
    # Layer 2 (middle): layer 1 position minus bar clearances and spacing
    r2 = r1 - d1/2 - spacing - d2/2
    
    # Layer 3 (innermost): layer 2 position minus bar clearances and spacing
    r3 = r2 - d2/2 - spacing - d3/2
    
    # Validate that all layers can fit within the section geometry
    if r1 <= 0 or r2 <= 0 or r3 <= 0:
        return None  # Configuration is geometrically infeasible
    
    # Calculate minimum spacing requirements for each layer
    # Minimum spacing considers bar diameter plus clear spacing requirements
    e1 = max(spacing + d1, 150)  # Minimum 150mm for concrete consolidation
    e2 = max(spacing + d2, 150)
    e3 = max(spacing + d3, 150)
    
    # Calculate maximum number of rebars per layer using trigonometric analysis
    try:
        # Angular spacing calculation using law of cosines for circular arrangement
        angle1 = acos((2*r1*r1 - e1*e1)/(2*r1*r1)) if r1 > 0 else pi
        angle2 = acos((2*r2*r2 - e2*e2)/(2*r2*r2)) if r2 > 0 else pi
        angle3 = acos((2*r3*r3 - e3*e3)/(2*r3*r3)) if r3 > 0 else pi
    except ValueError:
        # Mathematical domain error indicates invalid geometry
        return None
    
    # Calculate maximum number of bars based on angular spacing requirements
    n1_max = max(1, int(2*pi*r1 / (r1*angle1)))
    n2_max = max(1, int(2*pi*r2 / (r2*angle2)))
    n3_max = max(1, int(2*pi*r3 / (r3*angle3)))
    
    # Calculate individual bar areas for steel area calculations
    a1 = pi/4 * d1**2
    a2 = pi/4 * d2**2
    a3 = pi/4 * d3**2
    
    # Return comprehensive geometry data for optimization algorithms
    return {
        'n1_max': n1_max, 'n2_max': n2_max, 'n3_max': n3_max,  # Maximum bar counts
        'a1': a1, 'a2': a2, 'a3': a3,                          # Individual bar areas
        'as1_max': n1_max * a1,                                # Maximum layer 1 area
        'as2_max': n2_max * a2,                                # Maximum layer 2 area
        'as3_max': n3_max * a3                                 # Maximum layer 3 area
    }


def _calculate_rebar_numbers(layers: Dict[str, Any], as_target: float) -> Optional[Tuple[int, int, int]]:
    """
    Calculate required reinforcement quantities for each layer to achieve target area.
    
    This function determines the optimal distribution of reinforcement bars across
    the three available layers to achieve the target steel area while maximizing
    the use of outer layers for structural efficiency. The algorithm prioritizes
    outer layer utilization as these bars provide greater moment resistance due
    to their larger lever arm from the section centroid.
    """
    # Extract maximum capacities from layer geometry analysis
    as1_max = layers['as1_max']  # Maximum steel area in layer 1 (outermost)
    as2_max = layers['as2_max']  # Maximum steel area in layer 2 (middle)
    as3_max = layers['as3_max']  # Maximum steel area in layer 3 (innermost)
    
    # Calculate total available capacity across all layers
    total_max = as1_max + as2_max + as3_max
    
    # Check if target area is achievable with given geometric constraints
    if as_target > total_max:
        return None  # Target exceeds available capacity
    
    # Progressive layer filling strategy for optimal reinforcement distribution
    if as_target <= as1_max:
        # Target achievable with layer 1 only - most efficient configuration
        n1 = ceil(as_target / layers['a1'])
        return (n1, 0, 0)  # Only layer 1 reinforcement required
        
    elif as_target <= as1_max + as2_max:
        # Target requires layers 1 and 2 - fill layer 1 completely, partial layer 2
        n1 = layers['n1_max']  # Use all available layer 1 bars
        n2 = ceil((as_target - as1_max) / layers['a2'])  # Remaining area in layer 2
        return (n1, n2, 0)  # Layers 1 and 2 reinforcement
        
    else:
        # Target requires all three layers - fill layers 1 and 2, partial layer 3
        n1 = layers['n1_max']  # Use all available layer 1 bars
        n2 = layers['n2_max']  # Use all available layer 2 bars
        n3 = ceil((as_target - as1_max - as2_max) / layers['a3'])  # Remaining in layer 3
        return (n1, n2, n3)  # All three layers reinforcement


def _check_spacing_requirements(column: CircularColumn, n1: int) -> bool:
    """
    Verify that reinforcement spacing requirements are satisfied for construction.
    
    This function validates that the proposed reinforcement configuration meets
    minimum spacing requirements for concrete consolidation and construction
    quality control. The verification ensures adequate spacing for concrete
    placement, vibration access, and construction practicality in circular sections.
    """
    # Handle zero reinforcement case (no spacing constraints)
    if n1 == 0:
        return True
    
    # Calculate circumferential spacing between adjacent bars
    spacing = pi * column.diameter / n1
    
    # Verify spacing meets construction requirements:
    # - Minimum 150mm for concrete consolidation and vibration access
    # - Maximum 400mm for crack control and structural distribution
    # - Minimum 6 bars for adequate circumferential reinforcement
    return 150 <= spacing <= 400 and n1 >= 6


def design_optimal_rebar(
    column: CircularColumn, 
    df_load_nm: pd.DataFrame, 
    scale: float,
    steel_ratio_interval: float, 
    nm_curves_db: Optional[Dict] = None
) -> Tuple[CircularColumn, pd.DataFrame]:
    """
    Design optimal reinforcement configuration using binary search optimization.
    
    This function implements a sophisticated optimization algorithm to determine the
    minimum reinforcement configuration that satisfies ultimate limit state design
    requirements while maintaining structural adequacy and construction feasibility.
    The optimization uses binary search techniques for computational efficiency and
    integrates with pre-calculated M-N curve databases for performance enhancement.
    
    The optimization process includes:
    1. Maximum steel ratio validation to ensure design adequacy is achievable
    2. Minimum steel ratio calculation based on load requirements and code provisions
    3. Binary search optimization to find the minimum adequate reinforcement
    4. Integration with pre-calculated curves for computational efficiency
    5. Design verification against all critical load combinations
    """
    # Test maximum allowable steel ratio first to ensure design adequacy is achievable
    steel_ratio_max = 0.04  # 4% maximum steel ratio per code limitations
    column_test = copy(column)
    column_test = update_rebar_configuration(column_test, steel_ratio_max)
    
    # Generate M-N curve for maximum reinforcement configuration
    # Use database if available, otherwise calculate in real-time
    if nm_curves_db and column.generate_key() in nm_curves_db:
        df_nm_curve = _get_precalc_curve(nm_curves_db, column, steel_ratio_max)
    else:
        df_nm_curve = calculate_nm_curve(column_test)
    
    # Check if maximum reinforcement configuration fails design requirements
    if not check_nm_points(df_nm_curve, df_load_nm, scale):
        # Design cannot be satisfied even with maximum reinforcement
        return column_test, df_nm_curve  # Return maximum configuration as best attempt
    
    # Calculate minimum required steel ratio based on applied loads and code requirements
    steel_ratio_min = _calculate_min_steel_ratio(column, df_load_nm, scale)
    
    # Initialize binary search parameters for optimization convergence
    low, high = steel_ratio_min, steel_ratio_max
    best_column = column_test      # Conservative fallback (maximum reinforcement)
    best_curve = df_nm_curve      # Corresponding M-N curve
    
    # Binary search optimization loop for minimum adequate reinforcement
    while high - low > steel_ratio_interval / 2:
        # Calculate midpoint for binary search iteration
        mid = (high + low) / 2
        
        # Generate column configuration for current test ratio
        column_test = copy(column)
        column_test = update_rebar_configuration(column_test, mid)
        
        # Generate or retrieve M-N curve for current configuration
        if nm_curves_db and column.generate_key() in nm_curves_db:
            df_nm_curve = _get_precalc_curve(nm_curves_db, column, mid)
        else:
            df_nm_curve = calculate_nm_curve(column_test)
        
        # Test design adequacy for current reinforcement level
        if check_nm_points(df_nm_curve, df_load_nm, scale):
            # Current configuration is adequate - can potentially reduce reinforcement
            high = mid
            best_column = copy(column_test)  # Update best solution
            best_curve = df_nm_curve.copy()
        else:
            # Current configuration inadequate - must increase reinforcement
            low = mid
    
    # Return optimized design with minimum adequate reinforcement
    return best_column, best_curve


def _calculate_min_steel_ratio(column: CircularColumn, df_load_nm: pd.DataFrame, scale: float) -> float:
    """
    Calculate minimum required steel ratio based on applied loads and code requirements.
    
    This function determines the theoretical minimum steel reinforcement ratio required
    to satisfy both applied load conditions and code minimum requirements. The calculation
    considers axial force demands, concrete capacity limitations, and prescriptive code
    minimums to establish a rational lower bound for the optimization process.
    """
    # Extract critical axial forces from load combinations
    n_max = abs(df_load_nm['N (kN)'].max())  # Maximum compression force
    n_min = abs(df_load_nm.loc[df_load_nm['N (kN)'] < 0, 'N (kN)'].min() if any(df_load_nm['N (kN)'] < 0) else 0)  # Maximum tension
    
    # Calculate concrete compression capacity
    # Based on reduced concrete strength with appropriate safety factors
    f_c = 0.67 * column.fcu * pi / 1.5 * column.radius ** 2 / 1000  # kN
    
    # Calculate required steel areas for compression and tension conditions
    # Compression: steel required to carry load exceeding concrete capacity
    a_s_c_req = max(0, (n_max - f_c) * 1000 / (0.87 * 500))  # mm²
    
    # Tension: steel required to carry full tensile force
    a_s_t_req = n_min * 1000 / (0.87 * 500)  # mm²
    
    # Select governing steel area requirement
    a_s_req = max(a_s_c_req, a_s_t_req)
    
    # Convert to steel ratio with utilization factor consideration
    steel_ratio_req = (a_s_req / column.area) / scale
    
    # Apply code minimum requirements based on section size
    if column.area < 0.5e6:        # Small sections (< 0.5 m²)
        min_steel = 0.005          # 0.5% minimum
    elif column.area < 1e6:        # Medium sections (0.5-1.0 m²)
        min_steel = 2500 / column.area  # Graduated minimum
    else:                          # Large sections (> 1.0 m²)
        min_steel = 0.0025         # 0.25% minimum
    
    # Return the governing requirement (load-based or code minimum)
    return max(steel_ratio_req, min_steel)


def _get_precalc_curve(nm_curves_db: Dict, column: CircularColumn, target_ratio: float) -> pd.DataFrame:
    """
    Retrieve pre-calculated M-N curve closest to target steel ratio from database.
    
    This function efficiently retrieves the most appropriate pre-calculated M-N
    interaction curve from the database based on column configuration and target
    steel ratio. The selection prioritizes curves with steel ratios equal to or
    greater than the target to ensure conservative design adequacy.
    """
    # Generate database key for column configuration
    key = column.generate_key()
    
    # Extract available steel ratios for this column configuration
    available_ratios = sorted(nm_curves_db[key].keys())
    
    # Find closest ratio >= target for conservative design approach
    valid_ratios = [r for r in available_ratios if r >= target_ratio]
    closest_ratio = min(valid_ratios) if valid_ratios else max(available_ratios)
    
    # Retrieve and prepare curve data for design verification
    df_curve = nm_curves_db[key][closest_ratio].copy()
    df_curve['Pile Mark'] = column.segment_mark  # Update identification for verification
    
    return df_curve
