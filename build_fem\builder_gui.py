"""SAFE Model Builder GUI Module for Interactive Structural Analysis

This module provides a comprehensive graphical user interface (GUI) for the SAFE Model Builder
application, enabling interactive generation of SAFE16 and SAFE22 finite element models from
Excel input data. The GUI implements a user-friendly workflow with real-time progress tracking,
comprehensive error handling, and multi-threaded operations to maintain responsive user interaction.

The application serves as the primary interface for structural engineers to process building
data through the Foundation Automation system, providing visual feedback and control over
the model generation process while maintaining professional-grade error handling and logging.

Key Features:
- Interactive file and folder selection with path validation
- Real-time progress tracking with visual progress bars
- Comprehensive logging with auto-save and manual export capabilities
- Multi-threaded operations preventing GUI freezing during long operations
- User type-based feature access control (Base vs Ultimate users)
- Automated email notifications for usage tracking
- Robust error handling with detailed diagnostic information
- Professional UI design using tkinter and ttk widgets

GUI Workflow:
The application implements a structured 3-step workflow for SAFE model generation:
1. **Initialize Setup**: File path configuration and Excel data reading
2. **Generate Lateral Soil Spring**: Foundation analysis and soil-structure interaction
3. **Generate SAFE Model**: Complete SAFE16/SAFE22 model generation and export

User Interface Components:
- **File Selection Panel**: Input folder selection and path management
- **Design Options Panel**: Configuration options for Ultimate users
- **Actions Panel**: Primary operation buttons with sequential workflow
- **Progress Panel**: Real-time progress tracking with percentage completion
- **Status Panel**: Comprehensive logging with scrollable text display
- **Control Panel**: Log management and application control buttons

Threading Architecture:
The application uses a sophisticated threading model to maintain GUI responsiveness:
- **Main Thread**: GUI operations, user interaction, and UI updates
- **Worker Threads**: Long-running operations (file processing, model generation)
- **Thread Communication**: Safe inter-thread communication using tkinter.after()
- **Error Isolation**: Thread-specific error handling preventing cascade failures

User Access Control:
- **Base Users**: Access to initialization and soil spring generation
- **Ultimate Users**: Full access including SAFE model generation and design options
- **Feature Gating**: UI elements dynamically shown/hidden based on user type
- **Usage Tracking**: Automated logging of feature usage for analytics

Error Handling System:
- **Graceful Degradation**: Operations continue despite individual step failures
- **Comprehensive Logging**: Detailed error messages with full traceback information
- **User-Friendly Messages**: Technical errors translated to actionable user guidance
- **Auto-Save Logging**: Automatic log preservation for troubleshooting
- **Thread-Safe Error Display**: Safe error presentation across thread boundaries

Technical Specifications:
- **GUI Framework**: tkinter with ttk for modern widget styling
- **Threading Model**: Python threading with daemon threads for background operations
- **File Handling**: pathlib for cross-platform path management
- **Progress Tracking**: Real-time percentage-based progress with descriptive status
- **Memory Management**: Efficient handling of large structural datasets

Dependencies:
- tkinter: GUI framework and widget management
- threading: Multi-threaded operation support
- pathlib: Cross-platform file path handling
- build_fem.builder_main: Core model generation functionality
- email_notifications: Usage tracking and notification system
- build_fem.write_safe: SAFE16/SAFE22 data structure management

Usage:
    # Launch GUI application
    from build_fem.builder_gui import safe_model_builder
    safe_model_builder()
    
    # Or create GUI instance with user configuration
    from build_fem.builder_gui import SafeModelBuilderGUI
    import tkinter as tk
    
    root = tk.Tk()
    app = SafeModelBuilderGUI(
        parent=root,
        user_type="Ultimate",
        user_email="<EMAIL>",
        username="john.doe"
    )
    root.mainloop()

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

# Standard library imports for system and GUI functionality
import os                    # Operating system interface for file and path operations
import sys                   # System-specific parameters and functions
import threading            # Threading support for multi-threaded GUI operations
import tkinter as tk        # Core GUI framework for application interface
from datetime import datetime  # Date and time utilities for logging and timestamps
from pathlib import Path    # Object-oriented filesystem path handling

# tkinter GUI components and utilities
from tkinter import filedialog, Tk, Toplevel, StringVar, IntVar, messagebox  # GUI dialogs and variables
from tkinter import ttk     # Modern themed tkinter widgets for professional appearance

# Foundation Automation core modules - use __init__.py imports for clean namespace
from build_fem.write_safe.safe16 import Safe16DataFrames as _class_safe16  # SAFE16 data structures
from build_fem.write_safe.safe22 import Safe22DataFrames as _class_safe22  # SAFE22 data structures
from build_fem import builder_main as build_fem_main  # Core model generation functionality

# Notification and tracking modules
import email_notifications.notification as _notification  # Usage tracking and email notifications

class SafeModelBuilderGUI:
    """GUI application for interactive SAFE model generation and structural analysis.
    
    This class provides a comprehensive graphical user interface for the SAFE Model Builder
    application, enabling structural engineers to interactively generate SAFE16 and SAFE22
    finite element models from Excel input data. The GUI implements a professional workflow
    with real-time progress tracking, comprehensive error handling, and user access control.
    
    The application uses a multi-threaded architecture to maintain GUI responsiveness during
    long-running operations, with safe inter-thread communication and robust error isolation.
    It supports different user types with appropriate feature access control and automated
    usage tracking for analytics purposes.
    
    Attributes:
        window (Toplevel): Main GUI window with professional styling
        safe16_dfs (Safe16DataFrames): SAFE16 data structure container
        safe22_dfs (Safe22DataFrames): SAFE22 data structure container
        file_paths (object): File path configuration container
        excel_inputs (object): Excel input data container
        excel_outputs (object): Excel output data container
        user_type (str): User access level ("Base" or "Ultimate")
        user_email (str): User email for notifications and tracking
        username (str): System username for logging purposes
        
    GUI Components:
        status_var (StringVar): Status message display variable
        design_strip_var (IntVar): Design strip option selection (0/1)
        progress_var (StringVar): Progress description variable
        folder_path_var (StringVar): Input folder path variable
        existing_folder_path_var (StringVar): Existing folder path variable
        progress_bar (Progressbar): Visual progress indicator
        log_text (Text): Scrollable logging text widget
    """
    
    def __init__(self, parent, user_type="Base", user_email=None, username=None):
        """Initialize the SAFE Model Builder GUI application.
        
        Creates and configures the main GUI window with all necessary components,
        initializes data structures, and sets up user-specific access controls.
        The GUI is designed to be responsive and professional, with appropriate
        styling and layout for structural engineering workflows.
                
        Notes:
            - Window is configured as resizable with professional 800x800 default size
            - Application icon is set if AIS.ico file is available
            - All GUI variables are initialized with appropriate default values
            - User access control is configured based on user_type parameter
            - Threading support is prepared for responsive long-running operations
            
        GUI Initialization:
            - Creates toplevel window with professional styling
            - Initializes all tkinter variables for GUI state management
            - Configures user-specific access controls and feature visibility
            - Sets up data structure containers for SAFE16/SAFE22 processing
            - Prepares logging and progress tracking systems
        """
        # ============================================================================
        # WINDOW INITIALIZATION AND CONFIGURATION
        # Create and configure the main GUI window with professional styling
        # ============================================================================
        
        # Initialize the toplevel window as child of parent
        self.window = Toplevel(parent)
        self.window.title("SAFE Model Builder")
        self.window.geometry("800x800")  # Professional size for structural engineering workflows
        self.window.resizable(True, True)  # Allow resizing for different screen sizes
        
        # Set application icon if available
        try:
            self.window.iconbitmap('AIS.ico')
        except:
            pass  # Continue without icon if file not found
        
        # ============================================================================
        # DATA STRUCTURE INITIALIZATION
        # Initialize SAFE data containers and processing variables
        # ============================================================================
        
        # Initialize SAFE16 and SAFE22 data structure containers
        self.safe16_dfs = _class_safe16()  # Container for SAFE16 DataFrames
        self.safe22_dfs = _class_safe22()  # Container for SAFE22 DataFrames
        
        # Initialize file and data processing containers (set during workflow)
        self.file_paths = None      # File path configuration container
        self.excel_inputs = None    # Excel input data container
        self.excel_outputs = None   # Excel output data container
        
        # ============================================================================
        # GUI STATE VARIABLES
        # Initialize tkinter variables for GUI state management
        # ============================================================================
        
        # Status and progress tracking variables
        self.status_var = StringVar()
        self.status_var.set("Please select input folder to begin")
        self.progress_var = StringVar()
        self.progress_var.set("Ready")
        
        # File path selection variables
        self.folder_path_var = StringVar()          # Primary input folder path
        self.existing_folder_path_var = StringVar() # Existing input folder path
        
        # Design configuration variables (Ultimate users only)
        self.design_strip_var = IntVar(value=1)     # Default to Yes/1 for design strip option
        
        # ============================================================================
        # USER ACCESS CONTROL AND CONFIGURATION
        # Configure user-specific settings and access permissions
        # ============================================================================
        
        # Store user access level and contact information
        self.user_type = user_type      # Access level: "Base" or "Ultimate"
        self.user_email = user_email    # Email address for notifications and tracking
        
        # Configure username for logging (system username if not provided)
        self.username = username if username else self.get_username()
        
        # ============================================================================
        # SYSTEM INTEGRATION SETUP
        # Prepare system integration and output redirection
        # ============================================================================
        
        # Store original stdout for potential restoration (currently unused)
        self.old_stdout = sys.stdout
        
        # ============================================================================
        # GUI CREATION AND LAYOUT
        # Create all GUI components and establish layout
        # ============================================================================
        
        # Create and configure all GUI elements
        self.create_ui()

    def get_username(self):
        """Get system username for logging and tracking purposes.
        
        Attempts to retrieve the current system username for use in logging,
        usage tracking, and email notifications.
        if username cannot be determined from the operating system.
        
        Returns:
            str: System username or "unknown_user" if unavailable.
            
        Notes:
            - Primarily uses os.getlogin() for Windows compatibility
            - Used for usage analytics and troubleshooting identification
            
        Example:
            >>> username = self.get_username()
            >>> print(f"Current user: {username}")
            Current user: john.doe
        """
        # Try to get Windows username first (most common deployment environment)
        try:
            return os.getlogin()
        except:
            # If Windows username is not available, use a placeholder
            # This ensures the application continues to function even if
            # username cannot be determined from the operating system
            return "unknown_user"

    def create_ui(self):
        """Create and configure the complete user interface layout.
        
        Constructs the entire GUI layout with professional styling, logical organization,
        and user-friendly design. The interface is organized into distinct functional
        sections with appropriate spacing, padding, and visual hierarchy.
        
        The UI layout follows a top-to-bottom workflow pattern:
        1. File Selection: Input folder configuration
        2. Design Options: Configuration options (Ultimate users only)
        3. Actions: Primary operation buttons with sequential workflow
        4. Progress: Real-time progress tracking and status
        5. Status: Comprehensive logging with scrollable display
        6. Controls: Log management and application control
        
        GUI Components Created:
            - File selection panel with folder browsing capabilities
            - Design strip options (conditional on user type)
            - Action buttons for the 3-step workflow
            - Progress bar with percentage and description display
            - Scrollable text widget for comprehensive logging
            - Control buttons for log management and application exit
            
        Layout Features:
            - Professional ttk styling with consistent padding
            - Responsive layout that adapts to window resizing
            - Logical grouping of related functionality
            - Clear visual hierarchy with labeled frames
            - Appropriate spacing and alignment for readability
            
        Notes:
            - User type controls visibility of certain UI elements
            - All widgets are properly configured with appropriate variables
            - Layout uses pack geometry manager for reliable cross-platform behavior
            - Professional color scheme and font sizing for engineering applications
        """
        # ============================================================================
        # MAIN CONTAINER FRAME
        # Create the primary container with professional padding
        # ============================================================================
        
        # Create main container frame with consistent padding
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # ============================================================================
        # FILE SELECTION PANEL
        # Input folder selection and path management interface
        # ============================================================================
        
        # File selection frame with labeled border for clear organization
        file_frame = ttk.LabelFrame(
            main_frame, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=5)

        # Primary input folder selection row with label, entry, and browse button
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill=tk.X, pady=5)
        ttk.Label(folder_frame, text="Input Folder:").pack(
            side=tk.LEFT, padx=(0, 5))
        folder_entry = ttk.Entry(
            folder_frame, textvariable=self.folder_path_var, width=50)
        folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(folder_frame, text="Browse...",
                   command=self.browse_folder).pack(side=tk.LEFT)

        # Existing input folder path selection (for template or reference data)
        existing_folder_frame = ttk.Frame(file_frame)
        existing_folder_frame.pack(fill=tk.X, pady=5)
        ttk.Label(existing_folder_frame, text="Existing Input Folder:").pack(
            side=tk.LEFT, padx=(0, 5))
        existing_folder_entry = ttk.Entry(
            existing_folder_frame, textvariable=self.existing_folder_path_var, width=50)
        existing_folder_entry.pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(existing_folder_frame, text="Browse...",
                   command=self.browse_existing_folder).pack(side=tk.LEFT)

        # Step 1: Initialize setup button - first step in the workflow
        ttk.Button(file_frame, text="1. Initialize",
                   command=self.initialize_setup, width=40).pack(pady=5)

        # ============================================================================
        # DESIGN OPTIONS PANEL (Ultimate Users Only)
        # Configuration options for advanced model generation features
        # ============================================================================
        
        # Design strip options frame - only visible for Ultimate users
        if self.user_type == "Ultimate":
            design_frame = ttk.LabelFrame(
                main_frame, text="Design Strip Options", padding="10")
            design_frame.pack(fill=tk.X, pady=5)

            # Radio button options for design strip generation
            ttk.Radiobutton(design_frame, text="Generate Design Strip",
                            variable=self.design_strip_var, value=1).pack(anchor=tk.W, pady=2)
            ttk.Radiobutton(design_frame, text="Skip Design Strip",
                            variable=self.design_strip_var, value=0).pack(anchor=tk.W, pady=2)

        # ============================================================================
        # ACTIONS PANEL
        # Primary operation buttons with sequential workflow
        # ============================================================================
        
        # Actions frame containing the main workflow buttons
        actions_frame = ttk.LabelFrame(
            main_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=5)

        # Step 2: Generate lateral soil spring - foundation analysis step
        ttk.Button(actions_frame, text="2. Generate Lateral Soil Spring",
                   command=self.gen_soil_spring, width=40).pack(pady=5)

        # Step 3: Generate SAFE model - only available for Ultimate users
        if self.user_type == "Ultimate":
            # Full SAFE model generation with export capabilities
            ttk.Button(actions_frame, text="3. Generate SAFE Model",
                       command=self.generate_safe_model, width=40).pack(pady=5)

        # ============================================================================
        # PROGRESS TRACKING PANEL
        # Real-time progress visualization and status updates
        # ============================================================================
        
        # Progress frame with visual progress bar and status text
        progress_frame = ttk.LabelFrame(
            main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=5)

        # Horizontal progress bar for visual progress indication
        self.progress_bar = ttk.Progressbar(
            progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Status label showing current operation and percentage completion
        ttk.Label(progress_frame, textvariable=self.status_var).pack(fill=tk.X)

        # ============================================================================
        # STATUS AND LOGGING PANEL
        # Comprehensive logging display with scrollable text widget
        # ============================================================================
        
        # Status frame with expandable text widget for detailed logging
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Scrollable text widget for comprehensive logging with word wrapping
        self.log_text = tk.Text(
            status_frame, wrap=tk.WORD, width=80, height=10)
        scrollbar = ttk.Scrollbar(status_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ============================================================================
        # CONTROL PANEL
        # Application control and log management buttons
        # ============================================================================
        
        # Button frame at the bottom for log management and application control
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        # Log management buttons on the left side
        ttk.Button(button_frame, text="Clear Log",
                   command=self.clear_log, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Log",
                   command=self.save_log, width=15).pack(side=tk.LEFT, padx=5)
        
        # Application control button on the right side
        ttk.Button(button_frame, text="Close",
                   command=self.window.destroy, width=15).pack(side=tk.RIGHT, padx=5)

    def browse_folder(self):
        """Open file dialog to select primary input folder for model data.
        
        Displays a directory selection dialog allowing users to choose the primary
        input folder containing Excel files with structural data. The selected path
        is validated, normalized, and stored for use in the model generation workflow.
        
        The primary input folder should contain the main Excel files:
        - Geometry data (structural layout and dimensions)
        - Loading data (loads, load patterns, combinations)
        - Property data (materials, sections, design parameters)
        - Geology data (soil properties, foundation conditions)
        
        Updates:
            - folder_path_var: StringVar containing the selected folder path
            - status_var: Status message indicating next required action
            - log_text: Logs the folder selection for user reference
            
        Notes:
            - Uses pathlib for cross-platform path handling
            - Gracefully handles dialog cancellation (no folder selected)
            - Provides clear guidance on next steps in the workflow
            - Path normalization ensures consistent handling across platforms
        """
        # Open directory selection dialog with descriptive title
        folder_path = filedialog.askdirectory(title="Select Input Folder")
        
        if folder_path:
            # Convert to Path object and back to string for consistent cross-platform path handling
            folder_path = str(Path(folder_path))
            
            # Update GUI variables with selected path
            self.folder_path_var.set(folder_path)
            self.log_to_console(f"Selected input folder: {folder_path}")
            self.status_var.set(
                "Folder selected. Click '1. Initialize' to continue.")

    def browse_existing_folder(self):
        """Open file dialog to select existing input folder for template or reference data.
        
        Displays a directory selection dialog for selecting an existing input folder
        that contains template data, reference configurations, or previously processed
        structural data. This folder can be used as a baseline or reference for the
        current model generation process.
        
        Common use cases for existing folder:
        - Template projects with standard configurations
        - Reference data from similar structural analyses
        - Previously processed data for comparison or validation
        - Baseline configurations for parametric studies
        
        Updates:
            - existing_folder_path_var: StringVar containing the selected folder path
            - status_var: Status message indicating next required action
            - log_text: Logs the folder selection for user reference
            
        Notes:
            - Optional parameter - model generation can proceed without existing folder
            - Uses pathlib for cross-platform path handling
            - Provides same path normalization as primary folder selection
            - Supports workflow flexibility for various project types
        """
        # Open directory selection dialog with descriptive title for existing folder
        folder_path = filedialog.askdirectory(
            title="Select Existing Input Folder")
        
        if folder_path:
            # Convert to Path object and back to string for consistent cross-platform path handling
            folder_path = str(Path(folder_path))
            
            # Update GUI variables with selected existing folder path
            self.existing_folder_path_var.set(folder_path)
            self.log_to_console(f"Selected existing input folder: {folder_path}")
            self.status_var.set(
                "Existing folder selected. Click '1. Initialize' to continue.")

    def log_to_console(self, message):
        """Add timestamped message to both GUI log display and terminal output.
        
        Provides comprehensive logging functionality that simultaneously updates
        the GUI log display and prints to the terminal console. Messages are
        automatically timestamped and the GUI display auto-scrolls to show
        the latest entries.
        
        This method is thread-safe and can be called from worker threads during
        long-running operations to provide real-time status updates to users.
        """
        # Create timestamped message for consistent formatting
        timestamp = datetime.now().strftime("%H:%M:%S")
        terminal_message = f"[{timestamp}] {message}"
        
        # Log to terminal first for debugging and monitoring
        print(terminal_message)
        
        # Update GUI log display with thread-safe operations
        self.log_text.configure(state="normal")  # Enable editing temporarily
        self.log_text.insert("end", f"{terminal_message}\n")  # Append message
        self.log_text.see("end")  # Auto-scroll to show latest entry
        self.log_text.configure(state="disabled")  # Return to read-only state

        # Force immediate GUI update for real-time user feedback
        self.window.update_idletasks()

    def clear_log(self):
        """Clear all content from the GUI log display widget.
        
        Removes all logged messages from the scrollable text widget, providing
        users with a clean slate for new operations. The widget is temporarily
        enabled for editing, cleared, and then returned to read-only state.
        
        This method is useful for:
        - Starting fresh analysis sessions
        - Reducing visual clutter during long operations
        - Preparing for new model generation workflows
        - Troubleshooting by isolating current operation logs
        
        GUI Updates:
            - Temporarily enables text widget for editing
            - Removes all content from the log display
            - Returns widget to disabled (read-only) state
            
        Notes:
            - Does not affect terminal output or saved log files
            - Provides immediate visual feedback of cleared state
            - Safe to call at any time during application execution
        """
        # Temporarily enable text widget for content modification
        self.log_text.configure(state="normal")
        
        # Remove all content from the log display
        self.log_text.delete(1.0, "end")
        
        # Return to read-only state to prevent user editing
        self.log_text.configure(state="disabled")

    def _save_log_to_path(self, file_path):
        """Save the contents of the log text widget to a specific file path."""
        log_content = self.log_text.get(1.0, "end-1c") # Get content, excluding the final newline
        try:            # Ensure the directory exists
            path_obj = Path(file_path)
            path_obj.parent.mkdir(parents=True, exist_ok=True)
            with open(path_obj, 'w', encoding='utf-8') as file:
                file.write(log_content)
            self.log_to_console(f"Log successfully saved to: {file_path}")
        except Exception as e:
            error_message = f"Failed to save log to {file_path}: {str(e)}"
            self.log_to_console(error_message) # Log error to console
            # Show error to user, this is on main thread if save_log calls it
            # or scheduled on main thread if _auto_save_log_action calls it via window.after
            messagebox.showerror("Save Log Error", error_message) 

    def _auto_save_log_action(self):
        """Action to automatically save the log."""
        if hasattr(self.file_paths, 'Log') and self.file_paths.Log:
            self._save_log_to_path(self.file_paths.Log)
        else:
            self.log_to_console("Log file path not defined in file_paths. Auto-save skipped.")

    def save_log(self):
        """Save the contents of the log text widget to a file chosen by the user."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt"), ("Log files", "*.log"), ("All Files", "*.*")],
            title="Save Log As"
        )
        if filename:
            self._save_log_to_path(filename) # Use the new method

    def update_progress(self, percent, description=""):
        """Update visual progress indicator and status description.
        
        Provides real-time progress feedback to users during long-running operations
        by updating both the visual progress bar and descriptive status text. This
        method is designed to be called from worker threads to maintain responsive
        user interface during model generation processes.
        """
        # Update visual progress bar with current percentage
        self.progress_bar["value"] = percent
        
        # Update status text with description and percentage
        self.status_var.set(f"{description} ({percent}%)")
        
        # Force immediate GUI update for real-time user feedback
        self.window.update_idletasks()

    def initialize_setup(self):
        """Initialize file paths and read Excel input data - Step 1 of workflow.
        
        Performs the first step in the SAFE model generation workflow by validating
        selected folder paths and initiating the file path initialization process.
        This method validates user input, updates the GUI status, and launches a
        worker thread to perform the actual initialization without freezing the GUI.
        
        The initialization process includes:
        - File path configuration and validation
        - Excel input file reading and validation
        - Data structure preparation for subsequent operations
        - Usage tracking and logging
        
        Workflow Requirements:
            - Primary input folder must be selected
            - Existing folder is optional but can provide template data
            - Input folder must contain required Excel files
            
        GUI Behavior:
            - Validates folder selection before proceeding
            - Updates status to indicate initialization in progress
            - Launches worker thread to maintain GUI responsiveness
            - Provides error messages for invalid configurations
            
        Thread Safety:
            - Main thread handles validation and GUI updates
            - Worker thread performs file operations and data processing
            - Error handling spans both main and worker threads
            
        Success Criteria:
            - File paths successfully configured
            - Excel input data successfully read and validated
            - Data structures prepared for subsequent workflow steps
            - Usage tracking notification sent (if configured)
            
        Notes:
            - First step in the 3-step SAFE model generation workflow
            - Required before soil spring generation or SAFE model creation
            - Supports both new projects and template-based workflows
        """
        # ============================================================================
        # INPUT VALIDATION AND PREPARATION
        # Validate user selections and prepare for initialization
        # ============================================================================
        
        # Get and validate folder path selections
        folder_path = self.folder_path_var.get().strip()
        existing_folder_path = self.existing_folder_path_var.get().strip()

        # Validate that primary input folder has been selected
        if not folder_path:
            messagebox.showerror(
                "Error", "Please select an input folder first")
            return

        # ============================================================================
        # INITIALIZATION PROCESS LAUNCH
        # Start the initialization process with proper error handling
        # ============================================================================
        
        try:
            # Update GUI status to indicate initialization is starting
            self.status_var.set("Initializing file paths...")
            self.log_to_console("Starting file path initialization...")
            self.log_to_console(f"Using input folder: {folder_path}")
            
            # Log existing folder if provided
            if existing_folder_path:
                self.log_to_console(f"Using existing folder: {existing_folder_path}")

            # Start initialization operation in separate worker thread to avoid GUI freezing
            # Use daemon thread to ensure proper application shutdown
            threading.Thread(
                target=lambda: self.initialize_setup_thread(folder_path, existing_folder_path),
                daemon=True
            ).start()

        except Exception as e:
            # Handle errors in thread creation or initial setup
            # This is unlikely to occur but provides safety for edge cases
            self.status_var.set(f"Error during setup initiation: {str(e)}")
            self._show_error(f"Failed to initiate file path initialization: {str(e)}", e)

    def initialize_setup_thread(self, folder_path, existing_folder_path):
        try:
            # Use the new automated function
            self.file_paths, self.excel_inputs, self.excel_outputs = build_fem_main.automated_initialize_setup(
                folder_path,
                existing_folder_path,
                self.log_to_console
            )
            
            if self.file_paths and self.excel_inputs and self.excel_outputs:
                # Log usage of this function
                try:
                    if self.user_email:
                        _notification.send_email_log(self.username, "SAFEModelBuilder: 1.Initialize Setup", self.user_type,
                                                     self.user_email)
                    else:
                        _notification.send_email_log(
                            self.username, "SAFEModelBuilder: 1.Initialize Setup", self.user_type)
                except Exception as e:
                    self.log_to_console(f"Note: Could not log usage ({str(e)})")

                # Update UI from the main thread
                self.window.after(0, lambda: self.status_var.set(
                    "Excel file initialized successfully!"))
                self.window.after(0, lambda: messagebox.showinfo(
                    "Success", "Excel file initialized successfully!"))
            else:
                self.window.after(0, lambda: self.status_var.set(
                    "Excel file initialization canceled or failed"))

        except Exception as e:
            # Error in thread, use window.after to call _show_error
            self.window.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
            self.window.after(0, lambda exc=e: self._show_error(f"Failed to initialize file paths: {str(exc)}", exc))
            self.window.after(0, self._auto_save_log_action)

    def gen_soil_spring(self):
        # Refresh inputs in case files have changed
        if not self.file_paths or not self.excel_inputs or not self.excel_outputs:
            messagebox.showerror(
                "Error", "Please initialize file paths and read input files first")
            return

        self.status_var.set("Generating lateral soil spring...")
        self.log_to_console("Starting lateral soil spring generation...")

        # Start operation in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._gen_soil_spring_thread,
                         daemon=True).start()

    def _gen_soil_spring_thread(self):
        try:
            # Reset progress bar at the start
            self.window.after(0, lambda: self.update_progress(
                0, "Starting soil spring generation"))

            # Use the new automated function
            success = build_fem_main.automated_generate_soil_spring(
                self.file_paths,
                self.excel_inputs,
                self.log_to_console,
                lambda percent, msg: self.window.after(0, lambda: self.update_progress(percent, msg))
            )

            if success:
                # Log usage of this function
                try:
                    if self.user_email:
                        _notification.send_email_log(self.username, "SAFEModelBuilder 2. Generate Lateral Soil Spring",
                                                     self.user_type,
                                                     self.user_email)
                    else:
                        _notification.send_email_log(self.username, "SAFEModelBuilder 2. Generate Lateral Soil Spring",
                                                     self.user_type)
                except Exception as e:
                    self.log_to_console(f"Note: Could not log usage ({str(e)})")

                self.window.after(0, lambda: self.status_var.set(
                    "Lateral soil spring generated successfully"))
                self.window.after(0, lambda: messagebox.showinfo(
                    "Success", "Lateral soil spring generated successfully!"))
            else:
                self.window.after(0, lambda: self.status_var.set(
                    "Failed to generate lateral soil spring"))
                
        except Exception as e:
            # Reset progress bar on error
            self.window.after(
                0, lambda: self.update_progress(0, "Error occurred"))
            self.window.after(0, lambda: self.status_var.set(
                f"Error generating soil spring: {str(e)}"))
            self.window.after(0, lambda exc=e: self._show_error(f"Failed to generate soil spring: {str(exc)}", exc))
            self.window.after(0, self._auto_save_log_action)

    def generate_safe_model(self):
        if not self.file_paths or not self.excel_inputs or not self.excel_outputs:
            messagebox.showerror(
                "Error", "Please initialize file paths and read input files first")
            return

        self.status_var.set("Generating SAFE model...")
        self.log_to_console("Starting SAFE model generation...")
        self.update_progress(0, "Starting SAFE model generation")

        # Start operation in a separate thread to avoid freezing the GUI
        threading.Thread(
            target=self._generate_safe_model_thread, daemon=True).start()

    def _generate_safe_model_thread(self):
        try:
            # Reset progress bar at the start
            self.window.after(0, lambda: self.update_progress(
                0, "Starting SAFE model generation"))            # Reinitialize safe_dfs to avoid column mismatch errors on subsequent runs
            self.safe16_dfs = _class_safe16()
            self.safe22_dfs = _class_safe22()

            # Check for empty LateralSoilSpring data
            if self.excel_inputs.LateralSoilSpring.empty:
                self.log_to_console("WARNING: LateralSoilSpring data is empty")
                # For automation, we'll proceed anyway rather than showing a dialog
                self.log_to_console("Proceeding with empty LateralSoilSpring data...")

            # Use the radio button selection
            option_value = self.design_strip_var.get()
            design_strip_text = "Yes" if option_value == 1 else "No"
            self.log_to_console(f"Design Strip option: {design_strip_text}")

            # Use the new automated function
            success = build_fem_main.automated_generate_safe_model(
                self.file_paths,
                self.excel_inputs,
                self.excel_outputs,
                self.safe16_dfs,
                self.safe22_dfs,
                option_value,
                self.log_to_console,
                lambda percent, msg: self.window.after(0, lambda: self.update_progress(percent, msg))
            )

            if success:
                # Log usage of this function
                try:
                    if self.user_email:
                        _notification.send_email_log(self.username, "SAFEModelBuilder 3. Generate SAFE Model",
                                                     self.user_type,
                                                     self.user_email)
                    else:
                        _notification.send_email_log(self.username, "SAFEModelBuilder 3. Generate SAFE Model",
                                                     self.user_type)
                except Exception as e_notify:
                    self.log_to_console(f"Note: Could not log usage ({str(e_notify)})")

                self.window.after(0, lambda: self.status_var.set(
                    "SAFE model generated successfully"))
                self.window.after(0, lambda: messagebox.showinfo("Success",
                                                                 f"SAFE model generated successfully!\\n\\nFiles saved to:\\n{os.path.dirname(self.file_paths.ExcelSAFE16Model)}"))
                # Add automatic log saving after successful completion
                self.window.after(0, self._auto_save_log_action)
            else:
                self.window.after(0, lambda: self.status_var.set(
                    "Failed to generate SAFE model"))

        except Exception as e:
            # Error in thread, use window.after to call _show_error
            self.window.after(0, lambda: self.update_progress(0, "Error occurred"))
            self.window.after(0, lambda: self.status_var.set(f"Error generating SAFE model: {str(e)}"))
            self.window.after(0, lambda exc=e: self._show_error(f"Failed to generate SAFE model: {str(exc)}", exc))
            self.window.after(0, self._auto_save_log_action)

    def _show_error(self, message, exception_instance=None):
        """Display an error message in a messagebox and log detailed traceback to the console."""
        import traceback  # Import traceback here or at the class/module level if preferred
        
        exc_type, exc_value, exc_tb = sys.exc_info()
        
        # Prioritize traceback from the passed exception_instance if available and seems more relevant
        if exception_instance and hasattr(exception_instance, '__traceback__'):
            if exc_tb is None or exc_tb is not exception_instance.__traceback__:
                exc_tb = exception_instance.__traceback__
                if exc_type is None or exc_type is not type(exception_instance): # Align type and value
                    exc_type = type(exception_instance)
                if exc_value is None or exc_value is not exception_instance:
                    exc_value = exception_instance
        
        detailed_message_for_box = f"{message}\n\n"
        # Use a list for log messages to avoid repeated string concatenations in a loop
        log_messages = [f"ERROR: {message}"]

        if exc_tb:
            extracted_tb = traceback.extract_tb(exc_tb)
            
            # Log the full traceback first
            full_traceback = "\n".join(traceback.format_exception(exc_type, exc_value, exc_tb))
            self.log_to_console(f"--- Full Error Traceback ---\n{full_traceback}") # log_to_console handles UI thread

            # Prepare a more user-friendly summary
            if extracted_tb:
                error_origin_summary = "Error originated in:\n"
                for frame_summary in reversed(extracted_tb):  # Start from the most recent call
                    filename = os.path.basename(frame_summary.filename)
                    error_origin_summary += f"  File \"{filename}\", line {frame_summary.lineno}, in {frame_summary.name}\n"
                    if frame_summary.line:
                        error_origin_summary += f"    Code: {frame_summary.line.strip()}\n"
                
                detailed_message_for_box += error_origin_summary
                log_messages.append("--- Error Summary ---")
                log_messages.append(error_origin_summary.strip())
            else:
                summary_unavailable = "Traceback available but could not extract frames."
                detailed_message_for_box += summary_unavailable + "\n"
                log_messages.append(summary_unavailable)
        else:
            no_traceback_info = "No detailed traceback information available for this error."
            detailed_message_for_box += no_traceback_info + "\n"
            log_messages.append(no_traceback_info)

        # Log all collected messages (summary or basic error)
        # log_to_console is already thread-safe via window.update_idletasks
        # No need to re-log the initial basic message if full traceback was logged
        if not exc_tb: # Only log the basic error if no traceback info was processed
            self.log_to_console(log_messages[0]) # Log the initial "ERROR: message"
        elif len(log_messages) > 1 : # Log summary if available
             self.log_to_console("\n".join(log_messages[1:]))


        # Show the error message box (must be on main thread)
        # If _show_error is called by window.after, this is fine.
        # If called directly from a worker thread, messagebox would error.
        # The calling code in threads should use window.after for _show_error.
        messagebox.showerror("Error", detailed_message_for_box)


# ============================================================================
# APPLICATION LAUNCHER FUNCTION
# Main entry point for the SAFE Model Builder GUI application
# ============================================================================

def safe_model_builder():
    """Launch the SAFE Model Builder GUI application.
    
    Main entry point function that creates and runs the SAFE Model Builder GUI
    application. This function sets up the tkinter root window, initializes the
    GUI application with default settings, and starts the main event loop.
    
    The application provides a comprehensive interface for structural engineers
    to generate SAFE16 and SAFE22 finite element models from Excel input data
    through an intuitive 3-step workflow.
    
    Application Features:
        - Interactive file and folder selection
        - Real-time progress tracking with visual feedback
        - Comprehensive logging with save/export capabilities
        - Multi-threaded operations for responsive user experience
        - Professional UI design optimized for engineering workflows
        
    Default Configuration:
        - User type: "Base" (limited feature access)
        - No email notifications configured
        - System username automatically detected
        - Standard 800x800 window size
        
    Usage:
        >>> from build_fem.builder_gui import safe_model_builder
        >>> safe_model_builder()  # Launches the GUI application
        
    Notes:
        - Root window is hidden to provide clean application appearance
        - Application runs in main thread with worker threads for operations
        - Graceful shutdown handling for proper resource cleanup
        - Compatible with Windows, macOS, and Linux platforms
        
    GUI Workflow:
        1. Select input folder containing Excel structural data
        2. Initialize file paths and read input data
        3. Generate lateral soil spring for foundation analysis
        4. Generate complete SAFE model (Ultimate users only)
        
    For programmatic usage with custom configuration, use SafeModelBuilderGUI directly:
        >>> import tkinter as tk
        >>> from build_fem.builder_gui import SafeModelBuilderGUI
        >>> root = tk.Tk()
        >>> app = SafeModelBuilderGUI(root, user_type="Ultimate", user_email="<EMAIL>")
        >>> root.mainloop()
    """
    # Create tkinter root window for the application
    root = Tk()
    root.withdraw()  # Hide the root window for clean application appearance
    
    # Initialize the SAFE Model Builder GUI with default settings
    app = SafeModelBuilderGUI(root)
    
    # Start the main GUI event loop
    root.mainloop()
