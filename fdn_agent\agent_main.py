"""
Foundation Agent Main Module

This module serves as the primary backend orchestrator for the Foundation Automation Agent,
responsible for initializing the system, running core analyses like pile estimation, and
managing the overall workflow. It provides the main entry points for automated foundation
design and analysis operations.

Key Responsibilities:
    - System initialization and setup coordination
    - Pile estimation workflow orchestration with AI optimization
    - Multi-type pile optimization with NSGA-III algorithms
    - Integration with Excel data sources and outputs
    - Enhanced logging and error handling throughout all operations
    - Pile type pre-selection using AI-driven criteria

Main Functions:
    - automated_initialize_setup: Complete system initialization
    - run_pile_estimation_with_multi_type_optimization: AI-driven pile optimization
    - process_selected_pile_types_for_optimization: GUI parameter processing
    - run_ai_pile_type_preselection: Intelligent pile type selection

Dependencies:
    - initialization: System setup and data operations
    - pile_estimation: Core pile analysis and optimization modules
    - enhanced logging: Comprehensive logging with multiple levels
    - pile_type_selection: AI-driven pile type optimization

Author: Foundation Automation System
Date: June 2025
Version: 5.6.9
"""

from typing import Optional, Callable, Dict, List, Any, Tuple, Union
import os
import pandas as pd

# Core initialization modules
from initialization.data_operations import (
    init_input,
    init_output,
    update_excel_geology,
    update_excel_loading,
    update_excel_property
)
from initialization.error_handling import get_error_details
from initialization.file_operations import init_file_paths, init_existing_file_paths

# Pile estimation workflow coordination
from fdn_agent.pile_estimation.pile_workflow_coordinator import (
    coordinate_pile_estimation_workflow,
    coordinate_pile_estimation_with_multi_type_optimization
)

# Enhanced logging utilities with comprehensive level support
from fdn_agent.pile_estimation.utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)

# AI-driven pile type pre-selection system
from fdn_agent.pile_estimation.pile_type_selection import (
    IntegratedPileEstimationEngine,
    PileTypePreselector,
    PileTypePreselectionCriteria,
    PileTypeCandidate,
    PileTypePreselectionResult,
    PileType,
    create_pile_type_candidates_from_gui,
    integrate_with_existing_capacity_calculation
)

# Core analysis utilities
from fdn_agent.pile_estimation.clustering import create_element_groups_with_auto_threshold
from fdn_agent.pile_estimation.utils import resolve_coordinates_from_excel
from fdn_agent.pile_estimation.load_calculator import calculate_group_loads


def automated_initialize_setup(
    input_folder: str,
    existing_folder: Optional[str] = None,
    log_callback: Optional[Callable] = None
) -> Tuple[Optional[Any], Optional[Any], Optional[Any]]:
    """
    Automated initialization of file paths and input data for Foundation Agent operations.

    This function serves as the primary entry point for setting up the Foundation Automation
    system. It orchestrates the complete initialization workflow including file path setup,
    Excel data loading, property updates, and output preparation. The function implements
    comprehensive error handling and logging throughout the initialization process.

    Initialization Workflow:
        1. Validate input folder and create file path structure
        2. Initialize existing file paths if provided for data continuity
        3. Load Excel input data (geometry, property, geology, loading)
        4. Update property data with latest configurations
        5. Merge existing data if available (geology, loading)
        6. Initialize output data structures for analysis results
        7. Comprehensive validation and error reporting

    Args:
        input_folder (str): Path to the primary input folder containing Excel files.
                           Must contain the required input data structure for foundation analysis.
                           Expected to include geometry, property, geology, and loading data files.

        existing_folder (Optional[str]): Path to existing input folder for data continuity.
                                       If provided, existing geology and loading data will be
                                       merged with new input data to maintain project history.
                                       Defaults to None for new projects.

        log_callback (Optional[Callable]): Callback function for logging messages throughout
                                          the initialization process. Should accept message
                                          strings and optionally log levels. If None, logging
                                          is skipped. Supports both legacy and enhanced logging.

    Returns:
        Tuple[Optional[Any], Optional[Any], Optional[Any]]: A tuple containing:
            - file_paths: Initialized file path structure for the project
            - excel_inputs: Loaded and validated Excel input data
            - excel_outputs: Initialized output data structures

            Returns (None, None, None) if initialization fails at any stage.

    Raises:
        ValueError: If input_folder is None or empty
        FileNotFoundError: If required input files are missing
        PermissionError: If file access is denied
        KeyError: If required data columns are missing from Excel files
        Exception: For any other initialization errors with detailed context

    Example:
        >>> # Basic initialization for new project
        >>> file_paths, inputs, outputs = automated_initialize_setup(
        ...     input_folder="./project_data",
        ...     log_callback=print
        ... )

        >>> # Initialization with existing data continuity
        >>> file_paths, inputs, outputs = automated_initialize_setup(
        ...     input_folder="./new_project",
        ...     existing_folder="./previous_project",
        ...     log_callback=logger.info
        ... )

    Technical Notes:
        - Function implements comprehensive validation at each initialization step
        - Enhanced logging provides detailed progress tracking and error diagnostics
        - Existing data merging preserves project continuity while updating configurations
        - Error handling includes detailed context for troubleshooting
        - Performance metrics are logged for initialization timing analysis
        - Supports both new project setup and project data migration scenarios

    Integration Points:
        - Used by Foundation Agent GUI for project initialization
        - Integrates with pile estimation workflows for data preparation
        - Supports batch processing scenarios for multiple projects
        - Compatible with automated testing and validation frameworks

    Performance Considerations:
        - Initialization time scales with input data size and complexity
        - File I/O operations are optimized for typical project sizes
        - Memory usage is managed through efficient data loading strategies
        - Error recovery mechanisms prevent partial initialization states
    """
    log_function_entry(log_callback, "automated_initialize_setup",
                      input_folder=input_folder,
                      has_existing_folder=existing_folder is not None)

    with create_timed_logger(log_callback, "automated_initialize_setup"):
        enhanced_log(log_callback, "Starting automated initialization of file paths and input data", 'INFO')

        try:
            # Validate input folder
            log_algorithm_step(log_callback, "Agent Initialization", "Validating input folder")
            if not input_folder:
                error_msg = "Error: No input folder specified"
                log_validation_result(log_callback, "Input folder validation", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_function_exit(log_callback, "automated_initialize_setup", "Failed - No input folder")
                return None, None, None
            else:
                log_validation_result(log_callback, "Input folder validation", True, f"Folder: {input_folder}")
                enhanced_log(log_callback, f"Input folder validated: {input_folder}", 'DEBUG')

            # Initialize file paths
            log_algorithm_step(log_callback, "Agent Initialization", "Initializing file paths")
            file_paths = init_file_paths(input_folder, log_callback)
            if not file_paths:
                error_msg = "Failed to initialize file paths"
                log_validation_result(log_callback, "File paths initialization", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_function_exit(log_callback, "automated_initialize_setup", "Failed - File paths")
                return None, None, None
            else:
                log_validation_result(log_callback, "File paths initialization", True, "File paths created successfully")
                enhanced_log(log_callback, "File paths initialized successfully", 'DEBUG')

            # Initialize existing file paths if provided
            if existing_folder:
                log_algorithm_step(log_callback, "Agent Initialization", "Processing existing file paths")
                enhanced_log(log_callback, f"Processing existing folder: {existing_folder}", 'INFO')
                try:
                    file_paths = init_existing_file_paths(file_paths, existing_folder, log_callback)
                    log_validation_result(log_callback, "Existing file paths initialization", True, "Successfully processed")
                    enhanced_log(log_callback, "Existing file paths processed successfully", 'DEBUG')
                except Exception as e:
                    error_details = get_error_details(e, 'init_existing_file_paths')
                    log_validation_result(log_callback, "Existing file paths initialization", False, error_details['error_message'])
                    enhanced_log(log_callback, f"Warning: Failed to initialize existing file paths: {error_details['error_message']}", 'WARNING')
                    enhanced_log(log_callback, "Continuing without existing files...", 'WARNING')
                    log_error_with_context(log_callback, e, "init_existing_file_paths")
            else:
                enhanced_log(log_callback, "No existing folder provided - skipping existing file processing", 'DEBUG')

            # Initialize inputs
            log_algorithm_step(log_callback, "Agent Initialization", "Initializing Excel inputs")
            try:
                excel_inputs = init_input(file_paths, log_callback)
                log_validation_result(log_callback, "Excel inputs initialization", True, "Inputs loaded successfully")
                enhanced_log(log_callback, "Excel inputs initialized successfully", 'DEBUG')
            except Exception as e:
                error_details = get_error_details(e, 'init_input')
                error_msg = f"Failed to initialize inputs: {error_details['error_message']}"
                log_validation_result(log_callback, "Excel inputs initialization", False, error_details['error_message'])
                enhanced_log(log_callback, error_msg, 'ERROR')
                enhanced_log(log_callback, f"Location: {error_details['location']}", 'ERROR')
                log_error_with_context(log_callback, e, "init_input")
                log_function_exit(log_callback, "automated_initialize_setup", "Failed - Excel inputs")
                return None, None, None

            # Update Excel property data
            log_algorithm_step(log_callback, "Agent Initialization", "Updating Excel property data")
            try:
                excel_inputs = update_excel_property(file_paths, excel_inputs, log_callback)
                log_validation_result(log_callback, "Excel property update", True, "Property data updated successfully")
                enhanced_log(log_callback, "Excel property data updated successfully", 'DEBUG')
            except Exception as e:
                error_details = get_error_details(e, 'update_excel_property')
                log_validation_result(log_callback, "Excel property update", False, error_details['error_message'])
                enhanced_log(log_callback, f"Warning: Failed to update Excel property: {error_details['error_message']}", 'WARNING')
                enhanced_log(log_callback, "Continuing with existing property data...", 'WARNING')
                log_error_with_context(log_callback, e, "update_excel_property")

            # Update from existing files if provided
            if existing_folder:
                log_algorithm_step(log_callback, "Agent Initialization", "Updating data from existing files")
                enhanced_log(log_callback, "Processing updates from existing files", 'INFO')

                # Update geology data
                try:
                    excel_inputs = update_excel_geology(file_paths, excel_inputs, log_callback)
                    log_validation_result(log_callback, "Geology data update", True, "Geology updated from existing files")
                    enhanced_log(log_callback, "Geology data updated from existing files", 'DEBUG')
                except Exception as e:
                    error_details = get_error_details(e, 'update_excel_geology')
                    log_validation_result(log_callback, "Geology data update", False, error_details['error_message'])
                    enhanced_log(log_callback, f"Warning: Failed to update geology from existing: {error_details['error_message']}", 'WARNING')
                    log_error_with_context(log_callback, e, "update_excel_geology")

                # Update loading data
                try:
                    excel_inputs = update_excel_loading(file_paths, excel_inputs, log_callback)
                    log_validation_result(log_callback, "Loading data update", True, "Loading updated from existing files")
                    enhanced_log(log_callback, "Loading data updated from existing files", 'DEBUG')
                except Exception as e:
                    error_details = get_error_details(e, 'update_excel_loading')
                    log_validation_result(log_callback, "Loading data update", False, error_details['error_message'])
                    enhanced_log(log_callback, f"Warning: Failed to update loading from existing: {error_details['error_message']}", 'WARNING')
                    log_error_with_context(log_callback, e, "update_excel_loading")
            else:
                enhanced_log(log_callback, "No existing folder - skipping data updates from existing files", 'DEBUG')

            # Initialize outputs
            log_algorithm_step(log_callback, "Agent Initialization", "Initializing Excel outputs")
            try:
                excel_outputs = init_output(excel_inputs, log_callback)
                log_validation_result(log_callback, "Excel outputs initialization", True, "Outputs initialized successfully")
                enhanced_log(log_callback, "Excel outputs initialized successfully", 'DEBUG')
            except Exception as e:
                error_details = get_error_details(e, 'init_output')
                error_msg = f"Failed to initialize outputs: {error_details['error_message']}"
                log_validation_result(log_callback, "Excel outputs initialization", False, error_details['error_message'])
                enhanced_log(log_callback, error_msg, 'ERROR')
                enhanced_log(log_callback, f"Location: {error_details['location']}", 'ERROR')
                log_error_with_context(log_callback, e, "init_output")
                log_function_exit(log_callback, "automated_initialize_setup", "Failed - Excel outputs")
                return None, None, None

            # Log successful completion
            enhanced_log(log_callback, "✓ Initialization completed successfully", 'INFO')
            log_algorithm_step(log_callback, "Agent Initialization", "Initialization completed successfully")
            log_performance_metric(log_callback, "Initialization success rate", 1.0, "ratio")

            log_function_exit(log_callback, "automated_initialize_setup", "Success")
            return file_paths, excel_inputs, excel_outputs

        except Exception as e:
            error_details = get_error_details(e, 'automated_initialize_setup')
            error_msg = (f"Critical error during initialization: {error_details['error_type']}\n"
                        f"Message: {error_details['error_message']}\n"
                        f"Location: {error_details['location']}")

            log_validation_result(log_callback, "Agent initialization", False, error_details['error_message'])
            enhanced_log(log_callback, error_msg, 'ERROR')
            enhanced_log(log_callback, "Full traceback:", 'ERROR')
            enhanced_log(log_callback, error_details['full_traceback'], 'ERROR')
            log_error_with_context(log_callback, e, "automated_initialize_setup")
            log_function_exit(log_callback, "automated_initialize_setup", "Failed - Critical error")
            return None, None, None


def run_pile_estimation_with_multi_type_optimization(
    excel_inputs: Any,
    selected_pile_types: List[Dict[str, Any]],
    edge_dist: float,
    optimization_method: str = "cost_efficiency",
    output_dir: Optional[str] = None,
    log_callback: Optional[Callable] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Execute comprehensive pile estimation analysis with AI-driven pile type pre-selection and NSGA-III optimization.

    This function represents the core of the Foundation Agent's intelligent pile design capabilities.
    It orchestrates a sophisticated workflow that combines artificial intelligence for pile type
    pre-selection with advanced multi-objective optimization using the NSGA-III algorithm to
    generate optimal pile layouts with professional AutoCAD DXF visualization.

    Optimization Workflow:
        1. Input validation and parameter verification
        2. AI-driven pile type pre-selection based on structural requirements
        3. Structural element grouping and load analysis
        4. Multi-objective optimization using NSGA-III algorithm
        5. Layout generation with constraint satisfaction
        6. Professional DXF visualization and documentation
        7. Results compilation and quality assurance

    AI Integration Features:
        - Intelligent pile type selection based on load patterns and soil conditions
        - Automated constraint checking and validation
        - Performance-based optimization with multiple objectives
        - Real-time progress tracking and logging
        - Comprehensive error handling and recovery

    Args:
        excel_inputs (Any): Comprehensive Excel input data structure containing:
                           - Structural element definitions (columns, walls)
                           - Load data for all structural elements
                           - Geometric constraints and site boundaries
                           - Material properties and design parameters
                           Must be properly initialized through automated_initialize_setup.

        selected_pile_types (List[Dict[str, Any]]): List of pile type configurations for optimization.
                                                   Each dictionary must contain:
                                                   - 'type': Pile type identifier ('DHP', 'SHP', 'BP')
                                                   - 'capacity': Pile capacity in kN (must be > 0)
                                                   - 'diameter': Pile diameter in meters (for BP) or None
                                                   - 'section': Steel section identifier (for DHP/SHP)
                                                   - 'min_spacing': Minimum spacing in meters (must be > 0)

        edge_dist (float): Edge distance parameter in meters for pile cap design.
                          Defines minimum distance from pile centerline to pile cap edge.
                          Must be positive value, typically 0.3-0.5m for standard designs.

        optimization_method (str, optional): Optimization strategy for NSGA-III algorithm.
                                           Supported methods:
                                           - 'cost_efficiency': Minimize cost while maximizing efficiency
                                           - 'performance': Prioritize structural performance
                                           - 'balanced': Balance between cost and performance
                                           Defaults to 'cost_efficiency'.

        output_dir (Optional[str]): Directory path for output files and results.
                                   If None, uses default output directory structure.
                                   Directory will be created if it doesn't exist.

        log_callback (Optional[Callable]): Enhanced logging callback function.
                                          Supports both legacy (message only) and enhanced
                                          (message + level) logging interfaces.
                                          Used for progress tracking and debugging.

    Returns:
        Dict[str, Any]: Comprehensive results dictionary containing:
            - 'success': Boolean indicating overall operation success
            - 'summary': Summary statistics including total groups and piles
            - 'optimization': NSGA-III optimization results and metrics
            - 'dxf_file': Path to generated AutoCAD DXF visualization file
            - 'output_dir': Directory containing all output files
            - 'pile_type_usage': Distribution of pile types in final design
            - 'performance_metrics': Optimization performance statistics
            - 'validation_results': Design validation and compliance checks

    Raises:
        ValueError: If selected_pile_types is empty or contains invalid configurations
        ValueError: If edge_dist is not positive
        ValueError: If pile type configurations have invalid capacity or spacing values
        FileNotFoundError: If required input data files are missing
        RuntimeError: If NSGA-III optimization fails to converge
        Exception: For any other errors during the optimization process

    Example:
        >>> # Define pile types for optimization
        >>> pile_types = [
        ...     {
        ...         'type': 'DHP',
        ...         'capacity': 3663.0,
        ...         'section': 'UBP_305x305x223',
        ...         'diameter': None,
        ...         'min_spacing': 1.2
        ...     },
        ...     {
        ...         'type': 'BP',
        ...         'capacity': 5000.0,
        ...         'section': None,
        ...         'diameter': 0.6,
        ...         'min_spacing': 1.8
        ...     }
        ... ]

        >>> # Run optimization
        >>> results = run_pile_estimation_with_multi_type_optimization(
        ...     excel_inputs=project_inputs,
        ...     selected_pile_types=pile_types,
        ...     edge_dist=0.4,
        ...     optimization_method="cost_efficiency",
        ...     output_dir="./results",
        ...     log_callback=logger.info
        ... )

        >>> if results['success']:
        ...     print(f"Optimization completed: {results['summary']['total_piles']} piles")
        ...     print(f"DXF file: {results['dxf_file']}")

    Technical Implementation:
        - Utilizes NSGA-III (Non-dominated Sorting Genetic Algorithm III) for multi-objective optimization
        - Implements constraint handling for structural and geometric requirements
        - Generates professional AutoCAD DXF files with detailed annotations
        - Provides comprehensive validation of all design parameters
        - Supports parallel processing for large-scale optimization problems

    Performance Considerations:
        - Optimization time scales with problem complexity and number of pile types
        - Memory usage is optimized for typical foundation design scenarios
        - Progress tracking provides real-time feedback for long-running optimizations
        - Results caching improves performance for similar design scenarios

    Integration Notes:
        - Called by Foundation Agent GUI for interactive pile design
        - Integrates with coordinate_pile_estimation_with_multi_type_optimization workflow
        - Compatible with batch processing for multiple projects
        - Supports automated testing and validation frameworks
    """
    log_function_entry(log_callback, "run_pile_estimation_with_multi_type_optimization",
                      num_pile_types=len(selected_pile_types),
                      edge_dist=edge_dist,
                      optimization_method=optimization_method,
                      has_output_dir=output_dir is not None)

    with create_timed_logger(log_callback, "pile_estimation_with_multi_type_optimization"):
        try:
            enhanced_log(log_callback, "Starting AI pile layout optimization with AutoCAD DXF visualization", 'INFO')
            enhanced_log(log_callback, "Using improved wall polyline buffering for accurate pile cap geometry", 'INFO')

            # Validate inputs
            log_algorithm_step(log_callback, "Multi-Type Optimization", "Validating input parameters")
            if not selected_pile_types:
                error_msg = "No pile types selected for optimization"
                log_validation_result(log_callback, "Pile types validation", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)
            else:
                log_validation_result(log_callback, "Pile types validation", True, f"{len(selected_pile_types)} pile types selected")

            if edge_dist <= 0:
                error_msg = f"Invalid edge distance: {edge_dist} (must be positive)"
                log_validation_result(log_callback, "Edge distance validation", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)
            else:
                log_validation_result(log_callback, "Edge distance validation", True, f"{edge_dist:.2f} m")

            # Log optimization parameters
            log_calculation_result(log_callback, "Number of pile types", len(selected_pile_types))
            log_calculation_result(log_callback, "Edge distance", f"{edge_dist:.2f} m")
            log_calculation_result(log_callback, "Optimization method", optimization_method)
            log_performance_metric(log_callback, "Pile types for optimization", len(selected_pile_types), "types")

            enhanced_log(log_callback, f"AI Agent will optimize layout using {len(selected_pile_types)} pile type(s):", 'INFO')

            # Log selected pile types with detailed validation
            log_algorithm_step(log_callback, "Multi-Type Optimization", "Processing selected pile types")
            for i, pile_type in enumerate(selected_pile_types):
                # Validate pile type data
                if not pile_type.get('capacity') or pile_type['capacity'] <= 0:
                    error_msg = f"Invalid capacity for pile type {i+1}: {pile_type.get('capacity')}"
                    log_validation_result(log_callback, f"Pile type {i+1} capacity", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise ValueError(error_msg)

                if pile_type.get('diameter'):
                    pile_info = f"{pile_type['type']}: Capacity={pile_type['capacity']}kN, Diameter={pile_type['diameter']}m, Min Spacing={pile_type['min_spacing']}m"
                    enhanced_log(log_callback, f"   - {pile_info}", 'INFO')
                    log_calculation_result(log_callback, f"Pile type {i+1} diameter", f"{pile_type['diameter']:.2f} m")
                else:
                    pile_info = f"{pile_type['type']}: Capacity={pile_type['capacity']}kN, Section={pile_type['section']}, Min Spacing={pile_type['min_spacing']}m"
                    enhanced_log(log_callback, f"   - {pile_info}", 'INFO')
                    log_calculation_result(log_callback, f"Pile type {i+1} section", pile_type['section'])

                log_calculation_result(log_callback, f"Pile type {i+1} capacity", f"{pile_type['capacity']:.1f} kN")
                log_calculation_result(log_callback, f"Pile type {i+1} min spacing", f"{pile_type['min_spacing']:.2f} m")
                log_validation_result(log_callback, f"Pile type {i+1} validation", True, pile_info)

            # Call the pile estimation with AI pile type pre-selection and NSGA-III optimization
            log_algorithm_step(log_callback, "Multi-Type Optimization", "Executing pile estimation workflow")
            enhanced_log(log_callback, "Calling pile estimation with AI pile type pre-selection and NSGA-III optimization", 'INFO')

            results = coordinate_pile_estimation_with_multi_type_optimization(
                excel_inputs,
                selected_pile_types=selected_pile_types,
                edge_dist=edge_dist,
                optimization_method=optimization_method,
                output_dir=output_dir,  # Pass output directory
                log_callback=log_callback,
                **kwargs  # Pass through additional parameters like grouping thresholds
            )

            # Process and validate results
            log_algorithm_step(log_callback, "Multi-Type Optimization", "Processing optimization results")
            if results and results.get('success'):
                log_validation_result(log_callback, "Pile estimation execution", True, "Optimization completed successfully")
                enhanced_log(log_callback, "✓ AI pile layout optimization with AutoCAD DXF visualization completed successfully!", 'INFO')

                # Log result metrics
                summary = results.get('summary', {})
                optimization_results = results.get('optimization', {})

                if summary:
                    log_calculation_result(log_callback, "Total groups processed", summary.get('total_groups', 0))
                    log_calculation_result(log_callback, "Total piles required", summary.get('total_piles', 0))
                    log_performance_metric(log_callback, "Groups processed", summary.get('total_groups', 0), "groups")
                    log_performance_metric(log_callback, "Piles generated", summary.get('total_piles', 0), "piles")

                if optimization_results:
                    optimized_layout = optimization_results.get('optimized_layout', 'AI-optimized mixed pile types')
                    pile_type_usage = optimization_results.get('pile_type_usage', {})
                    log_calculation_result(log_callback, "Optimized layout type", optimized_layout)
                    log_performance_metric(log_callback, "Pile types used", len(pile_type_usage), "types")

                # Log file outputs
                if results.get('dxf_file'):
                    log_calculation_result(log_callback, "DXF file generated", results['dxf_file'])
                if results.get('output_dir'):
                    log_calculation_result(log_callback, "Output directory", results['output_dir'])

                log_performance_metric(log_callback, "Optimization success rate", 1.0, "ratio")
            else:
                error_msg = "AI pile layout optimization failed: No results returned"
                log_validation_result(log_callback, "Pile estimation execution", False, error_msg)
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_performance_metric(log_callback, "Optimization success rate", 0.0, "ratio")

            log_function_exit(log_callback, "run_pile_estimation_with_multi_type_optimization",
                            "Success" if results and results.get('success') else "Failed")
            return results

        except Exception as e:
            error_msg = f"AI pile layout optimization with AutoCAD DXF visualization failed: {str(e)}"
            log_validation_result(log_callback, "Multi-type optimization", False, str(e))
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_error_with_context(log_callback, e, "run_pile_estimation_with_multi_type_optimization")
            log_function_exit(log_callback, "run_pile_estimation_with_multi_type_optimization", f"Failed - {str(e)}")
            raise Exception(error_msg)


def process_selected_pile_types_for_optimization(
    dhp_selected: bool,
    dhp_params: Dict[str, Any],
    shp_selected: bool,
    shp_params: Dict[str, Any],
    bp_selected: bool,
    bp_types_data: List[Dict[str, Any]],
    log_callback: Optional[Callable] = None
) -> List[Dict[str, Any]]:
    """
    Process GUI selections and parameters to create standardized pile types list for optimization.

    This function serves as the interface between the Foundation Agent GUI and the optimization
    engine, converting user selections and parameters into a standardized format required by
    the NSGA-III optimization algorithm. It handles validation, default value assignment, and
    error recovery for all supported pile types.

    Supported Pile Types:
        - DHP (Driven H-Pile): Steel H-sections driven into the ground
        - SHP (Socket H-Pile): Steel H-sections socketed into rock
        - BP (Bored Pile): Reinforced concrete piles with various diameters

    Processing Workflow:
        1. Validate GUI selections and parameter completeness
        2. Process DHP parameters with capacity and section validation
        3. Process SHP parameters with enhanced capacity calculations
        4. Process multiple BP types with diameter and capacity variations
        5. Apply default values for missing or invalid parameters
        6. Comprehensive validation of all pile type configurations
        7. Return standardized list for optimization engine

    Args:
        dhp_selected (bool): User selection flag for Driven H-Pile inclusion.
                            If True, DHP will be included in optimization with
                            parameters from dhp_params dictionary.

        dhp_params (Dict[str, Any]): DHP configuration parameters from GUI:
                                    - 'capacity': Pile capacity in kN (default: 3663)
                                    - 'section': Steel section identifier (default: 'UBP_305x305x223')
                                    - 'min_spacing': Minimum spacing in meters (default: 1.2)
                                    Missing or invalid values trigger default assignment.

        shp_selected (bool): User selection flag for Socket H-Pile inclusion.
                            If True, SHP will be included in optimization with
                            parameters from shp_params dictionary.

        shp_params (Dict[str, Any]): SHP configuration parameters from GUI:
                                    - 'capacity': Pile capacity in kN (default: 6106)
                                    - 'section': Steel section identifier (default: 'UBP_305x305x223')
                                    - 'min_spacing': Minimum spacing in meters (default: 1.85)
                                    Missing or invalid values trigger default assignment.

        bp_selected (bool): User selection flag for Bored Pile inclusion.
                           If True, BP types will be included based on bp_types_data.

        bp_types_data (List[Dict[str, Any]]): List of BP configurations from GUI:
                                             Each dictionary contains:
                                             - 'capacity': Pile capacity in kN
                                             - 'diameter': Pile diameter in meters
                                             - 'min_spacing': Minimum spacing in meters
                                             Multiple BP types with different diameters supported.

        log_callback (Optional[Callable]): Enhanced logging callback for progress tracking.
                                          Logs parameter processing, validation results,
                                          and error recovery operations.

    Returns:
        List[Dict[str, Any]]: Standardized pile type configurations for optimization.
                             Each dictionary contains:
                             - 'type': Pile type identifier ('DHP', 'SHP', 'BP')
                             - 'capacity': Validated pile capacity in kN
                             - 'section': Steel section (for DHP/SHP) or None (for BP)
                             - 'diameter': Pile diameter in meters (for BP) or None (for DHP/SHP)
                             - 'min_spacing': Validated minimum spacing in meters

                             Empty list returned if no pile types are selected.

    Raises:
        ValueError: If pile type parameters contain invalid values that cannot be corrected
        TypeError: If parameter dictionaries have incorrect structure
        KeyError: If required parameter keys are missing from input dictionaries

    Example:
        >>> # GUI parameter example
        >>> dhp_params = {
        ...     'capacity': 3663.0,
        ...     'section': 'UBP_305x305x223',
        ...     'min_spacing': 1.2
        ... }
        >>> shp_params = {
        ...     'capacity': 6106.0,
        ...     'section': 'UBP_305x305x223',
        ...     'min_spacing': 1.85
        ... }
        >>> bp_types = [
        ...     {'capacity': 5000.0, 'diameter': 0.6, 'min_spacing': 1.8},
        ...     {'capacity': 7500.0, 'diameter': 0.8, 'min_spacing': 2.4}
        ... ]

        >>> # Process selections
        >>> pile_types = process_selected_pile_types_for_optimization(
        ...     dhp_selected=True,
        ...     dhp_params=dhp_params,
        ...     shp_selected=False,
        ...     shp_params=shp_params,
        ...     bp_selected=True,
        ...     bp_types_data=bp_types,
        ...     log_callback=logger.info
        ... )

        >>> print(f"Processed {len(pile_types)} pile types for optimization")

    Default Values:
        - DHP: capacity=3663 kN, section='UBP_305x305x223', min_spacing=1.2m
        - SHP: capacity=6106 kN, section='UBP_305x305x223', min_spacing=1.85m
        - BP: No defaults - all parameters must be provided in bp_types_data

    Validation Rules:
        - Capacity must be positive (> 0 kN)
        - Minimum spacing must be positive (> 0 m)
        - Steel sections must be valid identifiers
        - BP diameters must be positive (> 0 m)
        - At least one pile type must be selected for optimization

    Technical Notes:
        - Function implements comprehensive error recovery with default values
        - Logging provides detailed parameter validation and processing status
        - Supports both individual pile type selection and multiple BP configurations
        - Compatible with GUI parameter structures and optimization engine requirements
        - Maintains backward compatibility with legacy parameter formats
    """
    log_function_entry(log_callback, "process_selected_pile_types_for_optimization",
                      dhp_selected=dhp_selected,
                      shp_selected=shp_selected,
                      bp_selected=bp_selected,
                      num_bp_types=len(bp_types_data) if bp_types_data else 0)

    with create_timed_logger(log_callback, "process_selected_pile_types"):
        enhanced_log(log_callback, "Processing GUI selections and parameters for pile type optimization", 'INFO')

        selected_types = []

        # Log selection summary
        log_calculation_result(log_callback, "DHP selected", dhp_selected)
        log_calculation_result(log_callback, "SHP selected", shp_selected)
        log_calculation_result(log_callback, "BP selected", bp_selected)
        if bp_types_data:
            log_calculation_result(log_callback, "BP types count", len(bp_types_data))

        total_selections = sum([dhp_selected, shp_selected, bp_selected])
        log_performance_metric(log_callback, "Total pile type selections", total_selections, "selections")

        # Add DHP if selected
        if dhp_selected:
            log_algorithm_step(log_callback, "Pile Type Processing", "Processing DHP selection")
            enhanced_log(log_callback, "Processing DHP (Driven H-Pile) parameters", 'INFO')
            try:
                dhp_data = {
                    'type': 'DHP',
                    'capacity': dhp_params.get('capacity', 3663),
                    'section': dhp_params.get('section', 'UBP_305x305x223'),
                    'diameter': None,  # H-piles don't have diameter
                    'min_spacing': dhp_params.get('min_spacing', 1.2)
                }

                # Validate DHP parameters
                if dhp_data['capacity'] <= 0:
                    error_msg = f"Invalid DHP capacity: {dhp_data['capacity']} kN"
                    log_validation_result(log_callback, "DHP capacity validation", False, error_msg)
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, "DHP capacity validation", True, f"{dhp_data['capacity']:.1f} kN")

                if dhp_data['min_spacing'] <= 0:
                    error_msg = f"Invalid DHP min spacing: {dhp_data['min_spacing']} m"
                    log_validation_result(log_callback, "DHP spacing validation", False, error_msg)
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, "DHP spacing validation", True, f"{dhp_data['min_spacing']:.2f} m")

                selected_types.append(dhp_data)

                # Log DHP parameters
                log_calculation_result(log_callback, "DHP capacity", f"{dhp_data['capacity']:.1f} kN")
                log_calculation_result(log_callback, "DHP section", dhp_data['section'])
                log_calculation_result(log_callback, "DHP min spacing", f"{dhp_data['min_spacing']:.2f} m")
                enhanced_log(log_callback, f"✓ DHP added: {dhp_data['capacity']:.1f} kN, {dhp_data['section']}, {dhp_data['min_spacing']:.2f} m spacing", 'DEBUG')

            except Exception as e:
                log_validation_result(log_callback, "DHP parameter processing", False, str(e))
                enhanced_log(log_callback, "Warning: Could not read DHP parameters, using defaults", 'WARNING')
                log_error_with_context(log_callback, e, "DHP parameter processing")

                # Use default DHP parameters
                default_dhp = {
                    'type': 'DHP',
                    'capacity': 3663,
                    'section': 'UBP_305x305x223',
                    'diameter': None,
                    'min_spacing': 1.2
                }
                selected_types.append(default_dhp)

                log_calculation_result(log_callback, "DHP default capacity", f"{default_dhp['capacity']:.1f} kN")
                log_calculation_result(log_callback, "DHP default section", default_dhp['section'])
                log_calculation_result(log_callback, "DHP default min spacing", f"{default_dhp['min_spacing']:.2f} m")
                enhanced_log(log_callback, "✓ DHP added with default parameters", 'DEBUG')
        else:
            enhanced_log(log_callback, "DHP not selected - skipping", 'DEBUG')

        # Add SHP if selected
        if shp_selected:
            log_algorithm_step(log_callback, "Pile Type Processing", "Processing SHP selection")
            enhanced_log(log_callback, "Processing SHP (Socket H-Pile) parameters", 'INFO')
            try:
                shp_data = {
                    'type': 'SHP',
                    'capacity': shp_params.get('capacity', 6106),
                    'section': shp_params.get('section', 'UBP_305x305x223'),
                    'diameter': None,  # H-piles don't have diameter
                    'min_spacing': shp_params.get('min_spacing', 1.85)
                }

                # Validate SHP parameters
                if shp_data['capacity'] <= 0:
                    error_msg = f"Invalid SHP capacity: {shp_data['capacity']} kN"
                    log_validation_result(log_callback, "SHP capacity validation", False, error_msg)
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, "SHP capacity validation", True, f"{shp_data['capacity']:.1f} kN")

                if shp_data['min_spacing'] <= 0:
                    error_msg = f"Invalid SHP min spacing: {shp_data['min_spacing']} m"
                    log_validation_result(log_callback, "SHP spacing validation", False, error_msg)
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, "SHP spacing validation", True, f"{shp_data['min_spacing']:.2f} m")

                selected_types.append(shp_data)

                # Log SHP parameters
                log_calculation_result(log_callback, "SHP capacity", f"{shp_data['capacity']:.1f} kN")
                log_calculation_result(log_callback, "SHP section", shp_data['section'])
                log_calculation_result(log_callback, "SHP min spacing", f"{shp_data['min_spacing']:.2f} m")
                enhanced_log(log_callback, f"✓ SHP added: {shp_data['capacity']:.1f} kN, {shp_data['section']}, {shp_data['min_spacing']:.2f} m spacing", 'DEBUG')

            except Exception as e:
                log_validation_result(log_callback, "SHP parameter processing", False, str(e))
                enhanced_log(log_callback, "Warning: Could not read SHP parameters, using defaults", 'WARNING')
                log_error_with_context(log_callback, e, "SHP parameter processing")

                # Use default SHP parameters
                default_shp = {
                    'type': 'SHP',
                    'capacity': 6106,
                    'section': 'UBP_305x305x223',
                    'diameter': None,
                    'min_spacing': 1.85
                }
                selected_types.append(default_shp)

                log_calculation_result(log_callback, "SHP default capacity", f"{default_shp['capacity']:.1f} kN")
                log_calculation_result(log_callback, "SHP default section", default_shp['section'])
                log_calculation_result(log_callback, "SHP default min spacing", f"{default_shp['min_spacing']:.2f} m")
                enhanced_log(log_callback, "✓ SHP added with default parameters", 'DEBUG')
        else:
            enhanced_log(log_callback, "SHP not selected - skipping", 'DEBUG')

        # Add BP types if selected
        if bp_selected and bp_types_data:
            log_algorithm_step(log_callback, "Pile Type Processing", "Processing BP selections")
            enhanced_log(log_callback, f"Processing {len(bp_types_data)} BP (Bored Pile) types", 'INFO')

            for i, bp_data in enumerate(bp_types_data):
                log_algorithm_step(log_callback, "BP Processing", f"Processing BP type {i+1}")
                try:
                    bp_entry = {
                        'type': 'BP',
                        'subtype': f'BP-{i+1}',
                        'capacity': bp_data.get('capacity', 41155),
                        'section': None,  # Bored piles use diameter
                        'diameter': bp_data.get('diameter', 2.0),
                        'min_spacing': bp_data.get('min_spacing', 6.0)
                    }

                    # Validate BP parameters
                    if bp_entry['capacity'] <= 0:
                        error_msg = f"Invalid BP-{i+1} capacity: {bp_entry['capacity']} kN"
                        log_validation_result(log_callback, f"BP-{i+1} capacity validation", False, error_msg)
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, f"BP-{i+1} capacity validation", True, f"{bp_entry['capacity']:.1f} kN")

                    if bp_entry['diameter'] <= 0:
                        error_msg = f"Invalid BP-{i+1} diameter: {bp_entry['diameter']} m"
                        log_validation_result(log_callback, f"BP-{i+1} diameter validation", False, error_msg)
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, f"BP-{i+1} diameter validation", True, f"{bp_entry['diameter']:.2f} m")

                    if bp_entry['min_spacing'] <= 0:
                        error_msg = f"Invalid BP-{i+1} min spacing: {bp_entry['min_spacing']} m"
                        log_validation_result(log_callback, f"BP-{i+1} spacing validation", False, error_msg)
                        raise ValueError(error_msg)
                    else:
                        log_validation_result(log_callback, f"BP-{i+1} spacing validation", True, f"{bp_entry['min_spacing']:.2f} m")

                    selected_types.append(bp_entry)

                    # Log BP parameters
                    log_calculation_result(log_callback, f"BP-{i+1} capacity", f"{bp_entry['capacity']:.1f} kN")
                    log_calculation_result(log_callback, f"BP-{i+1} diameter", f"{bp_entry['diameter']:.2f} m")
                    log_calculation_result(log_callback, f"BP-{i+1} min spacing", f"{bp_entry['min_spacing']:.2f} m")
                    enhanced_log(log_callback, f"✓ BP-{i+1} added: {bp_entry['capacity']:.1f} kN, {bp_entry['diameter']:.2f} m diameter, {bp_entry['min_spacing']:.2f} m spacing", 'DEBUG')

                except Exception as e:
                    log_validation_result(log_callback, f"BP-{i+1} parameter processing", False, str(e))
                    enhanced_log(log_callback, f"Warning: Could not process BP-{i+1} parameters: {str(e)}", 'WARNING')
                    log_error_with_context(log_callback, e, f"BP-{i+1} parameter processing")
                    continue
        elif bp_selected and not bp_types_data:
            enhanced_log(log_callback, "BP selected but no BP types data provided", 'WARNING')
            log_validation_result(log_callback, "BP types data", False, "No BP types data provided")
        else:
            enhanced_log(log_callback, "BP not selected - skipping", 'DEBUG')

        # Log final results
        log_calculation_result(log_callback, "Total pile types processed", len(selected_types))
        log_performance_metric(log_callback, "Pile type processing success rate",
                             len(selected_types) / total_selections if total_selections > 0 else 0, "ratio")

        if selected_types:
            enhanced_log(log_callback, f"✓ Successfully processed {len(selected_types)} pile types for optimization", 'INFO')
            for pile_type in selected_types:
                enhanced_log(log_callback, f"  - {pile_type.get('subtype', pile_type['type'])}: {pile_type['capacity']:.1f} kN", 'DEBUG')
        else:
            enhanced_log(log_callback, "⚠️ No pile types were successfully processed", 'WARNING')
            log_validation_result(log_callback, "Pile type processing", False, "No pile types processed")

        log_function_exit(log_callback, "process_selected_pile_types_for_optimization", f"{len(selected_types)} pile types")
        return selected_types


def send_usage_notification(username, user_type, user_email=None, log_callback=None):
    """
    Send usage notification email.

    Args:
        username (str): Username for logging
        user_type (str): Type of user
        user_email (str): User email (optional)
        log_callback (callable): Function to log messages
    """
    log_function_entry(log_callback, "send_usage_notification",
                      username=username,
                      user_type=user_type,
                      has_email=user_email is not None)

    with create_timed_logger(log_callback, "send_usage_notification"):
        enhanced_log(log_callback, f"Sending usage notification for user: {username}", 'INFO')

        # Validate inputs
        if not username:
            error_msg = "Username is required for usage notification"
            log_validation_result(log_callback, "Username validation", False, error_msg)
            enhanced_log(log_callback, error_msg, 'WARNING')
            log_function_exit(log_callback, "send_usage_notification", "Failed - No username")
            return
        else:
            log_validation_result(log_callback, "Username validation", True, username)

        if not user_type:
            error_msg = "User type is required for usage notification"
            log_validation_result(log_callback, "User type validation", False, error_msg)
            enhanced_log(log_callback, error_msg, 'WARNING')
            log_function_exit(log_callback, "send_usage_notification", "Failed - No user type")
            return
        else:
            log_validation_result(log_callback, "User type validation", True, user_type)

        # Log notification parameters
        log_calculation_result(log_callback, "Username", username)
        log_calculation_result(log_callback, "User type", user_type)
        log_calculation_result(log_callback, "Has email", user_email is not None)
        if user_email:
            log_calculation_result(log_callback, "User email", user_email)

        try:
            log_algorithm_step(log_callback, "Usage Notification", "Importing notification module")
            import email_notifications.notification as _notification

            log_algorithm_step(log_callback, "Usage Notification", "Sending email notification")
            notification_subject = "FdnAgent: AI Pile Layout Optimization with AutoCAD DXF Visualization"

            if user_email:
                enhanced_log(log_callback, f"Sending notification with email to {user_email}", 'DEBUG')
                _notification.send_email_log(username, notification_subject, user_type, user_email)
                log_calculation_result(log_callback, "Notification method", "Email with address")
            else:
                enhanced_log(log_callback, "Sending notification without email address", 'DEBUG')
                _notification.send_email_log(username, notification_subject, user_type)
                log_calculation_result(log_callback, "Notification method", "Email without address")

            log_validation_result(log_callback, "Usage notification", True, "Notification sent successfully")
            enhanced_log(log_callback, "✓ Usage notification sent successfully", 'INFO')
            log_performance_metric(log_callback, "Notification success rate", 1.0, "ratio")

        except Exception as e:
            error_msg = f"Could not log usage ({str(e)})"
            log_validation_result(log_callback, "Usage notification", False, str(e))
            enhanced_log(log_callback, f"Note: {error_msg}", 'WARNING')
            log_error_with_context(log_callback, e, "send_usage_notification")
            log_performance_metric(log_callback, "Notification success rate", 0.0, "ratio")

        log_function_exit(log_callback, "send_usage_notification", "Completed")


def format_optimization_results_message(results: Optional[Dict[str, Any]]) -> str:
    """
    Format pile optimization results into a comprehensive, user-friendly message for display.

    This function transforms technical optimization results from the NSGA-III algorithm and
    AI pile type pre-selection into a well-formatted, informative message suitable for
    GUI display or reporting. It includes optimization statistics, file paths, usage
    instructions, and visual formatting for enhanced user experience.

    Message Components:
        - Optimization success confirmation with emoji indicators
        - Statistical summary (groups processed, total piles required)
        - Pile type distribution and usage information
        - File paths for generated DXF visualizations
        - Layer information for CAD software usage
        - Professional formatting with clear sections

    Args:
        results (Optional[Dict[str, Any]]): Comprehensive results dictionary from pile optimization.
                                          Expected structure:
                                          - 'success': Boolean indicating operation success
                                          - 'summary': Dict with 'total_groups' and 'total_piles'
                                          - 'optimization': Dict with layout and pile type usage
                                          - 'dxf_file': Path to final optimized layout DXF
                                          - 'consolidated_preselection_dxf_path': Path to preselection DXF
                                          - 'output_dir': Directory containing all output files

                                          If None or empty, returns error message.

    Returns:
        str: Formatted message string ready for display containing:
             - Success confirmation with visual indicators
             - Optimization statistics and metrics
             - File paths and directory information
             - CAD software usage instructions
             - Layer visibility guidelines for DXF files
             - Professional formatting with emojis and structure

             Returns error message if results are invalid or missing.

    Example:
        >>> results = {
        ...     'success': True,
        ...     'summary': {'total_groups': 5, 'total_piles': 23},
        ...     'optimization': {
        ...         'optimized_layout': 'Mixed pile types with cost optimization',
        ...         'pile_type_usage': {'DHP': 10, 'BP': 13}
        ...     },
        ...     'dxf_file': './output/optimized_layout.dxf',
        ...     'output_dir': './output'
        ... }

        >>> message = format_optimization_results_message(results)
        >>> print(message)
        # 🤖 AI Pile Layout Optimization with AutoCAD DXF visualization completed!
        #
        # 📊 Groups optimized: 5
        #  Total piles required: 23
        # 🏆 Optimized layout: Mixed pile types with cost optimization
        # 📈 Pile types used: DHP, BP
        #  Advanced AI optimization applied
        #
        # 📄 Final Optimized Layout DXF: ./output/optimized_layout.dxf
        # 💾 Results saved to: ./output
        #
        # 🎨 DXF files can be opened in AutoCAD, DraftSight, FreeCAD, or any CAD software.
        # ...

    Technical Features:
        - Comprehensive error handling for missing or invalid results
        - Detailed logging of message formatting process
        - Performance metrics tracking for message generation
        - Support for both complete and partial result sets
        - Professional formatting with consistent visual indicators

    Message Sections:
        1. Success Header: Confirmation with emoji and title
        2. Statistics: Groups, piles, and optimization metrics
        3. File Information: DXF paths and output directories
        4. Usage Instructions: CAD software guidance and layer information
        5. Layer Guide: Color coding and visibility instructions

    Integration Notes:
        - Used by Foundation Agent GUI for result display
        - Compatible with both console and GUI output formats
        - Supports internationalization through emoji usage
        - Maintains consistent formatting across different result types
    """
    log_function_entry(None, "format_optimization_results_message",
                      has_results=results is not None)

    with create_timed_logger(None, "format_optimization_results_message"):
        enhanced_log(None, "Formatting optimization results into user-friendly message", 'INFO')

        if not results:
            error_msg = "AI pile layout optimization failed: No results returned"
            log_validation_result(None, "Results validation", False, "No results provided")
            enhanced_log(None, error_msg, 'ERROR')
            log_function_exit(None, "format_optimization_results_message", "Failed - No results")
            return error_msg
        else:
            log_validation_result(None, "Results validation", True, "Results provided")

        # Extract result data with logging
        log_algorithm_step(None, "Message Formatting", "Extracting result data")
        summary = results.get('summary', {})
        optimization_results = results.get('optimization', {})
        total_groups = summary.get('total_groups', 0)
        total_piles = summary.get('total_piles', 0)
        optimized_layout = optimization_results.get('optimized_layout', 'AI-optimized mixed pile types')
        pile_type_usage = optimization_results.get('pile_type_usage', {})
        dxf_file = results.get('dxf_file', '')
        consolidated_preselection_dxf = results.get('consolidated_preselection_dxf_path', '')
        output_dir = results.get('output_dir', '')

        # Log extracted data
        log_calculation_result(None, "Total groups", total_groups)
        log_calculation_result(None, "Total piles", total_piles)
        log_calculation_result(None, "Optimized layout", optimized_layout)
        log_calculation_result(None, "Pile types used", len(pile_type_usage))
        log_calculation_result(None, "Has DXF file", bool(dxf_file))
        log_calculation_result(None, "Has preselection DXF", bool(consolidated_preselection_dxf))
        log_calculation_result(None, "Has output directory", bool(output_dir))

        # Build success message
        log_algorithm_step(None, "Message Formatting", "Building success message")
        success_msg = f"🤖 AI Pile Layout Optimization with AutoCAD DXF visualization completed!\n\n"
        success_msg += f"📊 Groups optimized: {total_groups}\n"
        success_msg += f" Total piles required: {total_piles}\n"
        success_msg += f"🏆 Optimized layout: {optimized_layout}\n"
        success_msg += f"📈 Pile types used: {', '.join(pile_type_usage.keys()) if pile_type_usage else 'Mixed types'}\n"
        success_msg += f" Advanced AI optimization applied\n"

        # DXF file information
        if dxf_file:
            success_msg += f"\n📄 Final Optimized Layout DXF: {dxf_file}"
            enhanced_log(None, f"DXF file included in message: {dxf_file}", 'DEBUG')

        if consolidated_preselection_dxf:
            success_msg += f"\n Consolidated Preselection DXF: {consolidated_preselection_dxf}"
            success_msg += f"\n    Shows all pile type options (DHP, SHP, BP) for all groups in one file"
            success_msg += f"\n   🎨 Use layer visibility to view specific pile types and groups"
            enhanced_log(None, f"Preselection DXF included in message: {consolidated_preselection_dxf}", 'DEBUG')

        if output_dir:
            success_msg += f"\n💾 Results saved to: {output_dir}"
            enhanced_log(None, f"Output directory included in message: {output_dir}", 'DEBUG')

        # Add usage instructions
        log_algorithm_step(None, "Message Formatting", "Adding usage instructions")
        success_msg += f"\n\n🎨 DXF files can be opened in AutoCAD, DraftSight, FreeCAD, or any CAD software."
        success_msg += f"\n Pile caps use improved geometry with enhanced wall processing."
        success_msg += f"\n🤖 AI Agent has optimized the pile layout using your selected pile types."
        success_msg += f"\n Pre-selection DXF shows possible positions for all pile types in layers:"
        success_msg += f"\n   • Red layer: DHP (Driven H-Pile) positions"
        success_msg += f"\n   • Green layer: SHP (Socket H-Pile) positions"
        success_msg += f"\n   • Magenta layer: BP (Bored Pile) positions"
        success_msg += f"\n   • Yellow layer: Selected pile type (highlighted)"

        # Log message statistics
        message_length = len(success_msg)
        log_calculation_result(None, "Message length", f"{message_length} characters")
        log_performance_metric(None, "Message formatting success rate", 1.0, "ratio")

        enhanced_log(None, f"✓ Successfully formatted optimization results message ({message_length} characters)", 'INFO')
        log_function_exit(None, "format_optimization_results_message", f"Success - {message_length} chars")
        return success_msg

