import tkinter as tk
from datetime import datetime
from tkinter import messagebox

import numpy as np
import pandas as pd

from build_fem import build_fem_config as config
from build_fem.functions import cal_wall_length, cal_corewall_length


def get_num_wind_cases():
    """Get the number of wind cases from a dialog box"""
    root = tk.Tk()
    root.withdraw()  # Hide the root window

    # Create a custom dialog window
    dialog = tk.Toplevel(root)
    dialog.title("Input")
    dialog.geometry("350x150")
    dialog.attributes('-topmost', True)

    # Set the icon
    try:
        dialog.iconbitmap("AIS.ico")
    except:
        pass  # Ignore if icon file not found

    # Center the dialog on screen
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (200)
    y = (dialog.winfo_screenheight() // 2) - (75)
    dialog.geometry(f"+{x}+{y}")

    # Create dialog content
    tk.Label(dialog, text="Number of Wind Cases:", font=("Arial", 10)).pack(pady=20)

    entry = tk.Entry(dialog, width=20, font=("Arial", 10))
    entry.pack(pady=5)
    entry.insert(0, "1")
    entry.focus_set()

    result = [None]

    def ok_click():
        try:
            value = int(entry.get())
            if value >= 1:
                result[0] = value
                dialog.destroy()
            else:
                messagebox.showerror("Error", "Please enter a number >= 1", parent=dialog)
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer", parent=dialog)

    def cancel_click():
        dialog.destroy()

    # Create buttons
    button_frame = tk.Frame(dialog)
    button_frame.pack(pady=10)

    tk.Button(button_frame, text="OK", command=ok_click, width=10).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Cancel", command=cancel_click, width=10).pack(side=tk.LEFT, padx=5)

    # Bind Enter key to OK
    dialog.bind('<Return>', lambda e: ok_click())

    # Wait for dialog to close
    dialog.wait_window()

    # Destroy root
    root.destroy()

    # Default to 1 if canceled
    return 1 if result[0] is None else result[0]


def ask_consider_nsf():
    """Ask user if Negative Skin Friction (NSF) should be considered."""
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    root.attributes('-topmost', True)

    # Set the icon
    try:
        root.iconbitmap("AIS.ico")
    except:
        pass  # Ignore if icon file not found

    response = messagebox.askyesno("Input", "Consider Negative Skin Friction (NSF) in load combinations?", parent=root)

    root.destroy()
    return response  # True for Yes, False for No


def get_other_load_patterns():
    """Get additional custom load pattern names from the user."""
    root = tk.Tk()
    root.withdraw()  # Hide the root window

    # Create a custom dialog window
    dialog = tk.Toplevel(root)
    dialog.title("Input")
    dialog.geometry("400x300")
    dialog.attributes('-topmost', True)

    # Set the icon
    try:
        dialog.iconbitmap("AIS.ico")
    except:
        pass  # Ignore if icon file not found

    # Center the dialog on screen
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (200)
    y = (dialog.winfo_screenheight() // 2) - (150)
    dialog.geometry(f"+{x}+{y}")

    # Create dialog content
    tk.Label(dialog, text="Additional Load Patterns:", font=("Arial", 10)).pack(pady=10)
    tk.Label(dialog, text="(Enter one per line)", font=("Arial", 8)).pack()

    # Create text widget for multiple entries
    text_frame = tk.Frame(dialog)
    text_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

    text_widget = tk.Text(text_frame, width=40, height=8, font=("Arial", 10))
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # Add scrollbar
    scrollbar = tk.Scrollbar(text_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    text_widget.config(yscrollcommand=scrollbar.set)
    scrollbar.config(command=text_widget.yview)

    text_widget.focus_set()

    result = [None]

    def ok_click():
        content = text_widget.get(1.0, tk.END).strip()
        if content:
            # Split by newlines and filter out empty lines
            patterns = [line.strip().upper() for line in content.split('\n') if line.strip()]
            result[0] = patterns
        else:
            result[0] = []
        dialog.destroy()

    def cancel_click():
        dialog.destroy()

    # Create buttons
    button_frame = tk.Frame(dialog)
    button_frame.pack(pady=10)

    tk.Button(button_frame, text="OK", command=ok_click, width=10).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Cancel", command=cancel_click, width=10).pack(side=tk.LEFT, padx=5)

    # Bind Enter key to OK (Ctrl+Enter since Enter is needed for new lines)
    dialog.bind('<Control-Return>', lambda e: ok_click())

    # Wait for dialog to close
    dialog.wait_window()

    # Destroy root
    root.destroy()

    # Return empty list if cancelled
    return [] if result[0] is None else result[0]


def add_load_pattern(add_wind):
    base_load_patterns = [
        ['SELF WEIGHT', 'SW'],
        ['DEAD', 'DL'],
        ['SUPERIMPOSED DEAD', 'SDL'],
        ['LIVE', 'LL'],
        ['SOIL', 'SL'],
        ['ANTICIPATED UPLIFT', 'Ua'],
        ['POSSIBLE UPLIFT', 'Up']  # NSF removed from here initially
    ]

    df_load_pattern = pd.DataFrame(base_load_patterns, columns=['Load Type', 'LoadPat (Text)'])

    consider_nsf = ask_consider_nsf()
    if consider_nsf:
        nsf_row = pd.DataFrame([['NEGATIVE SKIN FRICTION', 'NSF']], columns=df_load_pattern.columns)
        df_load_pattern = pd.concat([df_load_pattern, nsf_row], ignore_index=True)

    if add_wind:
        num_wind = get_num_wind_cases()
    else:
        num_wind = 1

    wind_rows_data = [['WIND', f'W{str(i).zfill(2)}'] for i in range(1, num_wind + 1)]
    if wind_rows_data:  # Only concat if there are wind rows
        wind_rows_df = pd.DataFrame(wind_rows_data, columns=df_load_pattern.columns)
        df_load_pattern = pd.concat([df_load_pattern, wind_rows_df], ignore_index=True)

    other_custom_patterns = get_other_load_patterns()
    if other_custom_patterns:
        other_rows_data = [['OTHER', name] for name in other_custom_patterns]
        other_rows_df = pd.DataFrame(other_rows_data, columns=df_load_pattern.columns)
        df_load_pattern = pd.concat([df_load_pattern, other_rows_df], ignore_index=True)

    return df_load_pattern, consider_nsf


def add_load_case(df_load_pattern):
    load_cases = df_load_pattern['LoadPat (Text)'].values
    factor_load_case = [1 for _ in load_cases]
    factor_load_case = np.diag(factor_load_case)

    df_load_case = pd.DataFrame(factor_load_case, columns=load_cases)
    df_load_case.insert(0, 'LoadCase (Text)', load_cases)
    return df_load_case, load_cases


def add_wind_envelope(df_load_comb, is_wl):
    factors_load_comb = np.where(is_wl, 1, 0)
    row = df_load_comb.index.size
    load_comb = np.append(['SLS', '_Envelope_WL', 'Envelope'], factors_load_comb)
    df_load_comb.loc[row] = load_comb
    return df_load_comb


def _add_single_load_combination(df_load_comb, combo_type, combo_name_template, factors, counter_obj,
                                 wind_case_num=None):
    """Helper to add a single load combination row to the DataFrame."""
    counter_obj[0] += 1
    count_str = str(counter_obj[0]).zfill(3)

    if wind_case_num is not None:
        combo_name = f"_{combo_type}{count_str}_{combo_name_template}{str(wind_case_num).zfill(2)}"
    else:
        combo_name = f"_{combo_type}{count_str}_{combo_name_template}"

    if combo_type == 'SETT':
        combo_type = 'SLS'  # Use SLS for settlement combinations
        
    load_comb_row = np.append([combo_type, combo_name, 'Linear Add'], factors)
    df_load_comb.loc[df_load_comb.index.size] = load_comb_row


def _add_load_combinations_with_wind(df_load_comb, combo_type, combo_name_template, base_factors, wind_factor_array,
                                     counter_obj):
    """Helper to add load combinations that iterate through wind cases."""
    num_wind_iter = 0
    for wind_factors_row in wind_factor_array:
        num_wind_iter += 1
        combined_factors = base_factors + wind_factors_row
        _add_single_load_combination(df_load_comb, combo_type, combo_name_template, combined_factors, counter_obj,
                                     wind_case_num=num_wind_iter)


def add_load_comb_uls(df_load_comb, num_uls, is_dl, is_sdl, is_ll, is_sl, is_ua, is_up, is_nsf, is_wl, consider_nsf):
    uls_counter = [num_uls]  # list to pass by reference

    # Loads for Ultimate Limit State (ULS) Case 1 Compression
    # ULS_1_C: 1.4(DL+SDL)+1.6LL+1.4NSF
    name_parts = ["1.4(DL+SDL)+1.6LL"]
    if consider_nsf:
        name_parts.append("+1.4NSF")
    name_template = "".join(name_parts)
    factors = np.where(is_dl, 1.4, 0) + np.where(is_sdl, 1.4, 0) + np.where(is_ll, 1.6, 0) + \
              np.where(is_sl, 0, 0) + np.where(is_nsf, 1.4, 0)  # is_nsf handles the factor correctly
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    name_parts = ["1.4(DL+SDL)+1.6LL+1.4SL"]
    if consider_nsf:
        name_parts.append("+1.4NSF")
    name_template = "".join(name_parts)
    factors = np.where(is_dl, 1.4, 0) + np.where(is_sdl, 1.4, 0) + np.where(is_ll, 1.6, 0) + \
              np.where(is_sl, 1.4, 0) + np.where(is_nsf, 1.4, 0)
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    # Loads for Ultimate Limit State (ULS) Case 2 Compression
    # ULS_2_C: 1.4(DL+SDL)+1.4SL+1.4NSF+1.4WL
    name_parts_c2_base = ["1.4(DL+SDL)+1.4SL"]
    if consider_nsf:
        name_parts_c2_base.append("+1.4NSF")
    name_template = "".join(name_parts_c2_base) + "+1.4W"
    base_factors_c2 = np.where(is_dl, 1.4, 0) + np.where(is_sdl, 1.4, 0) + np.where(is_ll, 0, 0) + \
                      np.where(is_sl, 1.4, 0) + np.where(is_nsf, 1.4, 0)
    wind_factors_diag_c2 = np.diag(np.where(is_wl, 1.4, 0))[~np.all(np.diag(np.where(is_wl, 1.4, 0)) == 0, axis=1)]
    if wind_factors_diag_c2.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_c2, wind_factors_diag_c2,
                                         uls_counter)

    # ULS_2_C: 1.4(DL+SDL)+1.4NSF+1.4WL
    name_parts_c2_no_sl_base = ["1.4(DL+SDL)"]
    if consider_nsf:
        name_parts_c2_no_sl_base.append("+1.4NSF")
    name_template = "".join(name_parts_c2_no_sl_base) + "+1.4W"
    base_factors_c2_no_sl = np.where(is_dl, 1.4, 0) + np.where(is_sdl, 1.4, 0) + np.where(is_ll, 0, 0) + \
                            np.where(is_sl, 0, 0) + np.where(is_nsf, 1.4, 0)
    if wind_factors_diag_c2.size > 0:  # wind_factors_diag_c2 is the same as above
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_c2_no_sl,
                                         wind_factors_diag_c2, uls_counter)

    # Loads for Ultimate Limit State (ULS) Case 3 Compression
    # ULS_3_C: 1.2(DL+SDL+LL+SL+NSF+WL)
    name_core = "DL+SDL+LL+SL"
    if consider_nsf:
        name_core += "+NSF"
    name_template = f"1.2({name_core})+1.2W"

    base_factors_c3 = np.where(is_dl, 1.2, 0) + np.where(is_sdl, 1.2, 0) + np.where(is_ll, 1.2, 0) + \
                      np.where(is_sl, 1.2, 0) + np.where(is_nsf, 1.2, 0)
    wind_factors_diag_c3 = np.diag(np.where(is_wl, 1.2, 0))[~np.all(np.diag(np.where(is_wl, 1.2, 0)) == 0, axis=1)]
    if wind_factors_diag_c3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_c3, wind_factors_diag_c3,
                                         uls_counter)

    name_core_no_sl = "DL+SDL+LL"
    if consider_nsf:
        name_core_no_sl += "+NSF"
    name_template = f"1.2({name_core_no_sl})+1.2W"

    base_factors_c3_no_sl = np.where(is_dl, 1.2, 0) + np.where(is_sdl, 1.2, 0) + np.where(is_ll, 1.2, 0) + \
                            np.where(is_sl, 0, 0) + np.where(is_nsf, 1.2, 0)
    if wind_factors_diag_c3.size > 0:  # wind_factors_diag_c3 is the same as above
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_c3_no_sl,
                                         wind_factors_diag_c3, uls_counter)

    # Loads for Ultimate Limit State (ULS) Case 1 Tension - NSF not typically in tension uplift without soil interaction
    # ULS_1_T: 1DL+1.4Ua (NSF not present)
    name_template = '1DL+1.4Ua'
    factors = np.where(is_dl, 1, 0) + np.where(is_ua, 1.4, 0)
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    name_template = '1DL+1.4Up'
    factors = np.where(is_dl, 1, 0) + np.where(is_up, 1.4, 0)
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    # ULS_1_T: 1DL+1.4SL+1.4Ua
    name_template = '1DL+1.4SL+1.4Ua'
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 0, 0) + np.where(is_ll, 0, 0) + \
              np.where(is_sl, 1.4, 0) + np.where(is_ua, 1.4, 0)
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    name_template = '1DL+1.4SL+1.4Up'
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 0, 0) + np.where(is_ll, 0, 0) + \
              np.where(is_sl, 1.4, 0) + np.where(is_up, 1.4, 0)
    _add_single_load_combination(df_load_comb, 'ULS', name_template, factors, uls_counter)

    # Loads for Ultimate Limit State (ULS) Case 2 Tension
    # ULS_2_T: 1DL+1.4Ua+1.4WL
    name_template = '1DL+1.4Ua+1.4W'
    base_factors_t2_ua = np.where(is_dl, 1, 0) + np.where(is_ua, 1.4, 0)
    # wind_factors_diag_c2 is the same (1.4*WL)
    if wind_factors_diag_c2.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_t2_ua, wind_factors_diag_c2,
                                         uls_counter)

    name_template = '1DL+1.4Up+1.4W'
    base_factors_t2_up = np.where(is_dl, 1, 0) + np.where(is_up, 1.4, 0)
    # wind_factors_diag_c2 is the same (1.4*WL)
    if wind_factors_diag_c2.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_t2_up, wind_factors_diag_c2,
                                         uls_counter)

    # ULS_2_T: 1DL+1.4SL+1.4Ua+1.4WL
    name_template = '1DL+1.4SL+1.4Ua+1.4W'
    base_factors_t2_sl_ua = np.where(is_dl, 1, 0) + np.where(is_sl, 1.4, 0) + np.where(is_ua, 1.4, 0)
    # wind_factors_diag_c2 is the same (1.4*WL)
    if wind_factors_diag_c2.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_t2_sl_ua,
                                         wind_factors_diag_c2, uls_counter)

    name_template = '1DL+1.4SL+1.4Up+1.4W'
    base_factors_t2_sl_up = np.where(is_dl, 1, 0) + np.where(is_sl, 1.4, 0) + np.where(is_up, 1.4, 0)
    # wind_factors_diag_c2 is the same (1.4*WL)
    if wind_factors_diag_c2.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'ULS', name_template, base_factors_t2_sl_up,
                                         wind_factors_diag_c2, uls_counter)

    num_uls = uls_counter[0]  # update the original counter
    return df_load_comb


def add_load_comb_sls(df_load_comb, num_sls, is_dl, is_sdl, is_ll, is_sl, is_ua, is_up, is_nsf, is_wl, consider_nsf):
    sls_counter = [num_sls]  # list to pass by reference
    sett_counter = [0]  # separate counter for settlement

    # Settlement Case
    # Settlement: 0.5DL+SDL+LL+NSF
    name_parts_sett = ["0.5DL+SDL+LL"]
    if consider_nsf:
        name_parts_sett.append("+NSF")  # Factor 1.0 for NSF in settlement
    name_template = "".join(name_parts_sett)

    factors = np.where(is_dl, 0.5, 0) + np.where(is_sdl, 1, 0) + np.where(is_ll, 1, 0) + \
              np.where(is_nsf, 1, 0)  # Factor is 1 for NSF in settlement
    _add_single_load_combination(df_load_comb, 'SETT', name_template, factors, sett_counter)

    # SLS Case
    # SLS1: DL+SDL+LL+SL+NSF
    name_parts_sls1 = ["DL+SDL+LL+SL"]
    if consider_nsf:
        name_parts_sls1.append("+NSF")  # Factor 1.0 for NSF
    name_template = "".join(name_parts_sls1)
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 1, 0) + np.where(is_ll, 1, 0) + \
              np.where(is_sl, 1, 0) + np.where(is_nsf, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    # SLS2: DL+SDL+LL+SL+NSF (This is identical to SLS1 as corrected, was this intended to be different?)
    # Assuming it is, as per previous structure. If it should be different, the base name or factors need to change.
    name_parts_sls2 = ["DL+SDL+LL+SL"]
    if consider_nsf:
        name_parts_sls2.append("+NSF")
    name_template = "".join(name_parts_sls2)  # Same name as SLS1 if NSF is included.
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 1, 0) + np.where(is_ll, 1, 0) + \
              np.where(is_sl, 1, 0) + np.where(is_nsf, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    # SLS_3_C: DL+SDL+LL+SL+NSF+WL
    name_parts_s3_base = ["DL+SDL+LL+SL"]
    if consider_nsf:
        name_parts_s3_base.append("+NSF")
    name_template = "".join(name_parts_s3_base) + "+W"

    base_factors_s3 = np.where(is_dl, 1, 0) + np.where(is_sdl, 1, 0) + np.where(is_ll, 1, 0) + \
                      np.where(is_sl, 1, 0) + np.where(is_nsf, 1, 0)
    wind_factors_diag_s3 = np.diag(np.where(is_wl, 1, 0))[~np.all(np.diag(np.where(is_wl, 1, 0)) == 0, axis=1)]
    if wind_factors_diag_s3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_s3, wind_factors_diag_s3,
                                         sls_counter)

    name_parts_s3_no_sl_base = ["DL+SDL+LL"]
    if consider_nsf:
        name_parts_s3_no_sl_base.append("+NSF")
    name_template = "".join(name_parts_s3_no_sl_base) + "+W"

    base_factors_s3_no_sl = np.where(is_dl, 1, 0) + np.where(is_sdl, 1, 0) + np.where(is_ll, 1, 0) + \
                            np.where(is_sl, 0, 0) + np.where(is_nsf, 1, 0)
    if wind_factors_diag_s3.size > 0:  # wind_factors_diag_s3 is the same as above
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_s3_no_sl,
                                         wind_factors_diag_s3, sls_counter)

    # Loads for SLS Case 1 Tension - NSF not typically in tension uplift
    # SLS_1_T: DL+Ua
    name_template = 'DL+Ua'
    factors = np.where(is_dl, 1, 0) + np.where(is_ua, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    name_template = 'DL+Up'
    factors = np.where(is_dl, 1, 0) + np.where(is_up, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    # SLS_1_T: DL+SL+Ua
    name_template = 'DL+SL+Ua'
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 0, 0) + np.where(is_ll, 0, 0) + \
              np.where(is_sl, 1, 0) + np.where(is_ua, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    name_template = 'DL+SL+Up'
    factors = np.where(is_dl, 1, 0) + np.where(is_sdl, 0, 0) + np.where(is_ll, 0, 0) + \
              np.where(is_sl, 1, 0) + np.where(is_up, 1, 0)
    _add_single_load_combination(df_load_comb, 'SLS', name_template, factors, sls_counter)

    # SLS_2_T: DL+Ua+WL
    name_template = 'DL+Ua+W'
    base_factors_st2_ua = np.where(is_dl, 1, 0) + np.where(is_ua, 1, 0)
    # wind_factors_diag_s3 is the same (1.0*WL)
    if wind_factors_diag_s3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_st2_ua, wind_factors_diag_s3,
                                         sls_counter)

    name_template = 'DL+Up+W'
    base_factors_st2_up = np.where(is_dl, 1, 0) + np.where(is_up, 1, 0)
    # wind_factors_diag_s3 is the same (1.0*WL)
    if wind_factors_diag_s3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_st2_up, wind_factors_diag_s3,
                                         sls_counter)

    # SLS_2_T: DL+SL+Ua+WL
    name_template = 'DL+SL+Ua+W'
    base_factors_st2_sl_ua = np.where(is_dl, 1, 0) + np.where(is_sl, 1, 0) + np.where(is_ua, 1, 0)
    # wind_factors_diag_s3 is the same (1.0*WL)
    if wind_factors_diag_s3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_st2_sl_ua,
                                         wind_factors_diag_s3, sls_counter)

    name_template = 'DL+SL+Up+W'
    base_factors_st2_sl_up = np.where(is_dl, 1, 0) + np.where(is_sl, 1, 0) + np.where(is_up, 1, 0)
    # wind_factors_diag_s3 is the same (1.0*WL)
    if wind_factors_diag_s3.size > 0:
        _add_load_combinations_with_wind(df_load_comb, 'SLS', name_template, base_factors_st2_sl_up,
                                         wind_factors_diag_s3, sls_counter)

    num_sls = sls_counter[0]  # update the original counter
    return df_load_comb


def add_input_load_pile(excel_inputs, load_cases):
    multi_cols = [('Pile Data', 'Pile Mark')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN)'))
        multi_cols.append((load_case, 'Vy (kN)'))
        multi_cols.append((load_case, 'Axial (kN)'))
        multi_cols.append((load_case, 'Mx (kNm)'))
        multi_cols.append((load_case, 'My (kNm)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_pile = pd.DataFrame(columns=multi_cols)
    # add pile marks to loading schedule
    df_pile = excel_inputs.Pile
    if not df_pile.empty and 'Pile Mark' in df_pile.columns:
        df_input_load_pile['Pile Data', 'Pile Mark'] = df_pile['Pile Mark']
    return df_input_load_pile


def add_input_load_point(excel_inputs, load_cases):
    multi_cols = [('Point Data', 'Point Load'), ('Point Data', 'Cap Thickness (m)')]

    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN)'))
        multi_cols.append((load_case, 'Vy (kN)'))
        multi_cols.append((load_case, 'Axial (kN)'))
        multi_cols.append((load_case, 'Mx (kNm)'))
        multi_cols.append((load_case, 'My (kNm)'))
        multi_cols.append((load_case, 'Mz (kNm)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_point = pd.DataFrame(columns=multi_cols)

    df_pointload = excel_inputs.PointLoad.copy()
    df_input_load_point['Point Data', 'Point Load'] = df_pointload['Point Load']

    return df_input_load_point


def add_input_load_line(excel_inputs, load_cases):
    multi_cols = [('Line Data', 'Line Load'), ('Line Data', 'Cap Thickness (m)')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'V1 (kN/m)'))
        multi_cols.append((load_case, 'V3 (kN/m)'))
        multi_cols.append((load_case, 'Axial (kN/m)'))
        multi_cols.append((load_case, 'M1 (kNm/m)'))
        multi_cols.append((load_case, 'M3 (kNm/m)'))
        multi_cols.append((load_case, 'Mz (kNm/m)'))
    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_line = pd.DataFrame(columns=multi_cols)

    df_lineload = excel_inputs.LineLoad.copy()
    df_input_load_line['Line Data', 'Line Load'] = df_lineload['Line Load']

    return df_input_load_line


def add_input_load_lkp(excel_inputs, load_cases):
    multi_cols = [('LKP Data', 'LKP Type')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN/m2)'))
        multi_cols.append((load_case, 'Vy (kN/m2)'))
        multi_cols.append((load_case, 'Axial (kN/m2)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_lkp = pd.DataFrame(columns=multi_cols)
    # add slab marks to loading schedule
    df_lkp = excel_inputs.LKP
    df = pd.DataFrame()
    df = df_lkp['Load Group'].drop_duplicates()
    df = df.dropna()
    df = df.reset_index(drop=True)

    df_input_load_lkp['LKP Data', 'LKP Type'] = df
    return df_input_load_lkp


def add_input_load_slab(excel_inputs, load_cases):
    multi_cols = [('Slab Data', 'Slab Mark')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN/m2)'))
        multi_cols.append((load_case, 'Vy (kN/m2)'))
        multi_cols.append((load_case, 'Axial (kN/m2)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_slab = pd.DataFrame(columns=multi_cols)
    # add slab marks to loading schedule
    df_slab = excel_inputs.Slab
    condition = pd.isna(df_slab['Slab Prop'])
    df_input_load_slab['Slab Data', 'Slab Mark'] = df_slab.loc[~condition, 'Slab']
    return df_input_load_slab


def add_input_load_beam(excel_inputs, load_cases):
    multi_cols = [('Beam Data', 'Beam Mark'), ('Beam Data', 'Cap Thickness (m)')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'V1 (kN/m)'))
        multi_cols.append((load_case, 'V3 (kN/m)'))
        multi_cols.append((load_case, 'Axial (kN/m)'))
        multi_cols.append((load_case, 'M1 (kNm/m)'))
        multi_cols.append((load_case, 'M3 (kNm/m)'))
        multi_cols.append((load_case, 'Mz (kNm/m)'))
    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_beam = pd.DataFrame(columns=multi_cols)

    df_beam = excel_inputs.Beam.copy()
    df_input_load_beam['Beam Data', 'Beam Mark'] = df_beam['Beam']

    return df_input_load_beam


def add_input_load_column(excel_inputs, load_cases):
    multi_cols = [('Column Data', 'Column Mark'), ('Column Data', 'Cap Thickness (m)'),
                  ('Column Data', 'Area/Point Load (A/P)')]

    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN)'))
        multi_cols.append((load_case, 'Vy (kN)'))
        multi_cols.append((load_case, 'Axial (kN)'))
        multi_cols.append((load_case, 'Mx (kNm)'))
        multi_cols.append((load_case, 'My (kNm)'))
        multi_cols.append((load_case, 'Mz (kNm)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_column = pd.DataFrame(columns=multi_cols)
    # add column marks to loading schedule
    df_input_load_column['Column Data', 'Column Mark'] = excel_inputs.Column['Column']
    # df_input_load_column['Column Data', 'Area (m2)'] = df_column['Area (m2)']
    # df_input_load_column['Column Data', 'Center Point (Text)'] = df_column['Center Point (Text)']
    df_input_load_column['Column Data', 'Area/Point Load (A/P)'] = 'P'
    return df_input_load_column


def add_input_load_wall(excel_inputs, load_cases):
    df_wall_length = cal_wall_length(excel_inputs)
    multi_cols = [('Wall Data', 'Wall Mark'), ('Wall Data', 'Cap Thickness (m)')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN)'))
        multi_cols.append((load_case, 'Vy (kN)'))
        multi_cols.append((load_case, 'Axial (kN)'))
        multi_cols.append((load_case, 'Mx (kNm)'))
        multi_cols.append((load_case, 'My (kNm)'))
        multi_cols.append((load_case, 'Mz (kNm)'))
    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_wall = pd.DataFrame(columns=multi_cols)
    # add wall marks to loading schedule
    df_input_load_wall['Wall Data', 'Wall Mark'] = df_wall_length['Wall Name']

    return df_input_load_wall


def add_input_load_corewall(excel_inputs, load_cases):
    df_wall_length = cal_wall_length(excel_inputs)
    df_corewall_length = cal_corewall_length(excel_inputs, df_wall_length)
    multi_cols = [('CoreWall Data', 'CoreWall Mark'), ('CoreWall Data', 'Cap Thickness (m)')]
    for load_case in load_cases:
        multi_cols.append((load_case, 'Vx (kN)'))
        multi_cols.append((load_case, 'Vy (kN)'))
        multi_cols.append((load_case, 'Axial (kN)'))
        multi_cols.append((load_case, 'Mx (kNm)'))
        multi_cols.append((load_case, 'My (kNm)'))
        multi_cols.append((load_case, 'Mz (kNm)'))

    multi_cols = pd.MultiIndex.from_tuples(multi_cols)
    df_input_load_corewall = pd.DataFrame(columns=multi_cols)
    # add corewall marks to loading schedule
    df_input_load_corewall['CoreWall Data', 'CoreWall Mark'] = df_corewall_length['CoreWall Name']
    # df_input_load_corewall['CoreWall Data', 'CoreWall Length (m)'] = df_corewall_length['CoreWall Length (m)']
    return df_input_load_corewall


def init_input_loading(excel_inputs, file_paths):
    path_excel_output = file_paths.ExcelLoading
    # add load pattern
    if file_paths.ExistingExcelLoading:
        add_wind = False
    else:
        add_wind = True
    df_load_pattern, consider_nsf = add_load_pattern(add_wind)

    # add load case
    df_load_case, load_cases = add_load_case(df_load_pattern)
    # check load type
    is_dl = df_load_pattern['Load Type'] == 'DEAD'
    is_sdl = df_load_pattern['Load Type'] == 'SUPERIMPOSED DEAD'
    is_ll = df_load_pattern['Load Type'] == 'LIVE'
    is_sl = df_load_pattern['Load Type'] == 'SOIL'
    is_ua = df_load_pattern['Load Type'] == 'ANTICIPATED UPLIFT'
    is_up = df_load_pattern['Load Type'] == 'POSSIBLE UPLIFT'
    is_nsf = df_load_pattern['Load Type'] == 'NEGATIVE SKIN FRICTION'
    is_wl = df_load_pattern['Load Type'] == 'WIND'

    # add load comb
    df_load_comb = pd.DataFrame(columns=np.append(['ULS/SLS', 'Combo (Text)', 'Type (Text)'], load_cases))
    # WL Envelope
    df_load_comb = add_wind_envelope(df_load_comb, is_wl)
    # ULS Cases
    num_uls = 0
    df_load_comb = add_load_comb_uls(df_load_comb, num_uls, is_dl, is_sdl, is_ll, is_sl, is_ua, is_up, is_nsf, is_wl,
                                     consider_nsf)
    # SLS Cases
    num_sls = 0
    df_load_comb = add_load_comb_sls(df_load_comb, num_sls, is_dl, is_sdl, is_ll, is_sl, is_ua, is_up, is_nsf, is_wl,
                                     consider_nsf)

    # Point Load Input Load Schedule
    df_input_load_point = add_input_load_point(excel_inputs, load_cases)
    # Line Load Input Load Schedule
    df_input_load_line = add_input_load_line(excel_inputs, load_cases)

    # Pile Input Load Schedule
    df_input_load_pile = add_input_load_pile(excel_inputs, load_cases)
    # Slab Load Schedule
    df_input_load_slab = add_input_load_slab(excel_inputs, load_cases)
    # Loading Key Plan
    df_input_load_lkp = add_input_load_lkp(excel_inputs, load_cases)
    # Beam Load Schedule
    df_input_load_beam = add_input_load_beam(excel_inputs, load_cases)
    # Column Load Schedule
    df_input_load_column = add_input_load_column(excel_inputs, load_cases)
    # Wall Load Schedule
    df_input_load_wall = add_input_load_wall(excel_inputs, load_cases)    # Corewall Load Schedule
    df_input_load_corewall = add_input_load_corewall(excel_inputs, load_cases)

    with pd.ExcelWriter(path_excel_output) as writer:
        df_load_pattern.to_excel(writer, sheet_name=config.SHEET_LOAD_PAT, index=False)
        df_load_case.to_excel(writer, sheet_name=config.SHEET_LOAD_CASE, index=False)
        df_load_comb.to_excel(writer, sheet_name=config.SHEET_LOAD_COMB, index=False)

        _write_multilevel_df_to_excel(writer, df_input_load_point, config.SHEET_INPUT_LOAD_POINT_LOAD)
        _write_multilevel_df_to_excel(writer, df_input_load_line, config.SHEET_INPUT_LOAD_LINE_LOAD)
        _write_multilevel_df_to_excel(writer, df_input_load_lkp, config.SHEET_INPUT_LOAD_LKP)
        _write_multilevel_df_to_excel(writer, df_input_load_pile, config.SHEET_INPUT_LOAD_PILE)
        _write_multilevel_df_to_excel(writer, df_input_load_slab, config.SHEET_INPUT_LOAD_SLAB)
        _write_multilevel_df_to_excel(writer, df_input_load_beam, config.SHEET_INPUT_LOAD_BEAM)
        _write_multilevel_df_to_excel(writer, df_input_load_column, config.SHEET_INPUT_LOAD_COLUMN)
        _write_multilevel_df_to_excel(writer, df_input_load_wall, config.SHEET_INPUT_LOAD_WALL)
        _write_multilevel_df_to_excel(writer, df_input_load_corewall, config.SHEET_INPUT_LOAD_CORE_WALL)

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Initialized Excel Input (Loading)!')
    return excel_inputs


def _write_multilevel_df_to_excel(writer, df, sheet_name):
    """Helper to write a DataFrame with a multi-level header to an Excel sheet."""
    df_title = pd.DataFrame(columns=df.droplevel(1, axis=1).columns)
    df_value = df.droplevel(0, axis=1)
    df_title.to_excel(writer, sheet_name=sheet_name, index=False)
    df_value.to_excel(writer, sheet_name=sheet_name, startrow=1, index=False)
