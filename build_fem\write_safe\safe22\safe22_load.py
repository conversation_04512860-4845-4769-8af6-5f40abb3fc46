"""
SAFE 22 Load Handling Module

This module provides functions for handling load definitions and applications
in SAFE 22 finite element models. It manages the conversion of load data from
Excel input formats to SAFE 22 DataFrame structures.

The module handles:
- Point loads (concentrated forces and moments)
- Line loads (distributed loads on beams and walls)
- Area loads (uniform loads on slabs and areas)
- Pile loads (foundation loads)
- Load groups and assignments
- Load pattern definitions
- Load transformations and coordinate conversions

Functions:
    Point Loads:
        - point_load_GroupDefinitions_safe22: Create point load groups
        - point_load_GroupAssignments_safe22: Assign points to load groups
        - point_load_JointAssignsFloorMeshOpt_safe22: Set mesh options for loaded points
        - point_load_JointLoadsForce_safe22: Apply point forces and moments

    Line Loads:
        - line_load_GroupDefinitions_safe22: Create line load groups
        - line_load_GroupAssignments_safe22: Assign lines to load groups
        - line_load_JointAssignsFloorMeshOpt_safe22: Set mesh options for loaded lines
        - line_load_FrameLoadsDistributed_safe22: Apply distributed line loads

    Area Loads:
        - slab_load_AreaLoadsUniform_safe22: Apply uniform slab loads
        - lkp_load_AreaLoadsUniform_safe22: Apply loads to load groups

    Pile Loads:
        - pile_load_GroupDefinitions_safe22: Create pile load groups
        - pile_load_GroupAssignments_safe22: Assign piles to load groups
        - pile_load_JointLoadsForce_safe22: Apply pile forces and moments

    Utility Functions:
        - _write_line_load_safe22: Process all line load operations
        - _write_beam_load_safe22: Process beam-specific loads
        - _write_column_load_safe22: Process column-specific loads
        - _write_wall_load_safe22: Process wall-specific loads
        - _write_corewall_load_safe22: Process core wall loads

"""

import pandas as pd
from typing import Tuple, Any, Optional, Dict, List

from build_fem import functions


def point_load_GroupDefinitions_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create group definitions for point loads in SAFE 22 model.

    Defines a load group specifically for point loads with standard properties.
    This group will contain all points that have applied loads and helps with
    visualization and organization in the SAFE 22 model.

    """
    # Define point load group with standard properties
    s22_group_definitions_rows = [['A.Load_Point', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)

    # Add group definition to existing data
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def point_load_GroupAssignments_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Assign points with loads to the point load group in SAFE 22 model.

    Maps point load identifiers to actual point names and assigns them to the
    'A.Load_Point' group for visualization and organization purposes. This function
    establishes the relationship between load definitions and geometric points.

    """
    df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()
    df_point_load = excel_inputs.PointLoad.copy()

    target_columns = safe22_dfs.GroupAssignments.columns.tolist()
    new_rows = []

    # Process each point load definition
    for i, df_row in df_input_load_point.iterrows():
        center = df_row[('Point Data', 'Point Load')]

        # Map point load identifier to actual point name
        condition = df_point_load['Point Load'] == center
        matching_points = df_point_load.loc[condition, 'Point'].values

        # Validate point load mapping
        if len(matching_points) == 0:
            print(f"Warning: Point not found for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue
        point = matching_points[0]
        if point is None:
            print(f"Warning: Point value is None for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue

        # Add point to load group
        new_rows.append(['A.Load_Point', 'Point', point])

    # Create group assignment DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add assignments to existing group data
    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat(
                [safe22_dfs.GroupAssignments, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def point_load_JointAssignsFloorMeshOpt_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Configure floor mesh optimization for points with applied loads in SAFE 22 model.

    Ensures that points with applied loads are included in the floor mesh generation
    process. This is critical for accurate load transfer and stress distribution
    in the finite element analysis.

    """
    df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()
    df_point_load = excel_inputs.PointLoad.copy()

    target_columns = safe22_dfs.JointAssignsFloorMeshOpt.columns.tolist()
    new_rows = []

    # Process each point load definition for mesh optimization
    for i, df_row in df_input_load_point.iterrows():
        center = df_row[('Point Data', 'Point Load')]

        # Map point load identifier to actual point name
        condition = df_point_load['Point Load'] == center
        matching_points = df_point_load.loc[condition, 'Point'].values

        # Validate point load mapping
        if len(matching_points) == 0:
            print(f"Warning: Point not found for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue
        point = matching_points[0]
        if point is None:
            print(f"Warning: Point value is None for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue

        # Include point in floor mesh generation
        new_rows.append([point, 'Yes'])

    # Create mesh optimization DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add mesh optimization settings to existing data
    if not df_append.empty:
        if safe22_dfs.JointAssignsFloorMeshOpt.empty:
            safe22_dfs.JointAssignsFloorMeshOpt = df_append.copy()
        else:
            safe22_dfs.JointAssignsFloorMeshOpt = pd.concat(
                [safe22_dfs.JointAssignsFloorMeshOpt, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def point_load_JointLoadsForce_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Apply point forces and moments to joints in SAFE 22 model.

    Converts point load data into SAFE 22 joint force assignments, including
    direct loads and additional moments due to load eccentricity. Handles
    coordinate system conversions and load transformations for proper analysis.

    """
    df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()
    df_point_load = excel_inputs.PointLoad.copy()

    target_columns = safe22_dfs.JointLoadsForce.columns.tolist()

    # Extract load case column names (exclude 'Point Data' columns)
    load_cases = [col for col in df_input_load_point.columns.get_level_values(0).unique() if 'Point Data' not in col]

    new_rows = []

    # Process each point load definition
    for i, df_row in df_input_load_point.iterrows():
        center = df_row[('Point Data', 'Point Load')]

        # Map point load identifier to actual point name
        condition = df_point_load['Point Load'] == center
        matching_points = df_point_load.loc[condition, 'Point'].values

        # Validate point load mapping
        if len(matching_points) == 0:
            print(f"Warning: Point not found for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue
        point = matching_points[0]
        if point is None:
            print(f"Warning: Point value is None for point load '{center}' in excel_inputs.PointLoad. Skipping this point load.")
            continue

        # Get cap thickness for eccentricity calculations
        t = df_row[('Point Data', 'Cap Thickness (m)')]

        # Process each load case
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x = load_data['Vx (kN)']
            v_y = load_data['Vy (kN)']
            f_z = load_data['Axial (kN)']
            m_x = load_data['Mx (kNm)']
            m_y = load_data['My (kNm)']
            m_z = load_data['Mz (kNm)']

            # Apply direct moments if non-zero
            if m_x != 0 or m_y != 0 or m_z != 0:
                new_rows.append([point, load_case, 0, 0, 0, m_x, m_y, m_z, 0, 0, None])

            # Calculate and apply additional moments due to shear force eccentricity
            # These moments account for load application above slab centerline
            m_x_add = -v_y * t  # Moment about X-axis due to Y-direction shear
            m_y_add = v_x * t   # Moment about Y-axis due to X-direction shear

            if m_x_add != 0 or m_y_add != 0:
                new_rows.append([point, load_case, 0, 0, 0, m_x_add, m_y_add, 0, 0, 0, None])

            # Apply point forces if non-zero
            if v_x != 0 or v_y != 0 or f_z != 0:
                # Note: f_z sign is reversed for SAFE coordinate convention
                new_rows.append([point, load_case, v_x, v_y, -f_z, 0, 0, 0, 0, 0, None])

    # Create joint loads DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add joint loads to existing data
    if not df_append.empty:
        if safe22_dfs.JointLoadsForce.empty:
            safe22_dfs.JointLoadsForce = df_append.copy()
        else:
            safe22_dfs.JointLoadsForce = pd.concat(
                [safe22_dfs.JointLoadsForce, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def line_load_GroupDefinitions_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create group definitions for line loads in SAFE 22 model.

    Defines a load group specifically for line loads (beams, walls) with standard
    properties. This group will contain all line elements that have applied loads
    and helps with visualization and organization in the SAFE 22 model.

    """
    # Define line load group with standard properties
    s22_group_definitions_rows = [['A.Load_Line', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)

    # Add group definition to existing data
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def line_load_GroupAssignments_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Assign line elements and their endpoints to the line load group in SAFE 22 model.

    Maps line load identifiers to actual line elements and their endpoint nodes,
    then assigns them to the 'A.Load_Line' group for visualization and organization.
    This includes both the line elements themselves and their connecting points.

    """
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()
    df_lineload = excel_inputs.LineLoad.copy()

    # Parse point connectivity from semicolon-separated format
    # Example: 'P1;P2' becomes PointI='P1', PointJ='P2'
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    target_columns = safe22_dfs.GroupAssignments.columns.tolist()
    new_rows = []

    # Process each line load definition
    for i, df_row in df_input_load_line.iterrows():
        name = df_row[('Line Data', 'Line Load')]

        # Look up line geometry information
        line_assign_row = df_lineload[df_lineload['Line Load'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        # Assign line element and its endpoints to load group
        new_rows.append(['A.Load_Line', 'Line', name])    # Line element
        new_rows.append(['A.Load_Line', 'Point', pt_i])   # Start point
        new_rows.append(['A.Load_Line', 'Point', pt_j])   # End point

    # Create group assignment DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add assignments to existing group data
    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat(
                [safe22_dfs.GroupAssignments, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def line_load_JointAssignsFloorMeshOpt_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Configure floor mesh optimization for endpoints of loaded line elements in SAFE 22 model.

    Ensures that endpoints of line elements with applied loads are included in the
    floor mesh generation process. This is critical for accurate load transfer from
    line elements to the supporting slab and proper stress distribution.

    """
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()
    df_lineload = excel_inputs.LineLoad.copy()

    # Parse point connectivity from semicolon-separated format
    # Example: 'P1;P2' becomes PointI='P1', PointJ='P2'
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    target_columns = safe22_dfs.JointAssignsFloorMeshOpt.columns.tolist()
    new_rows = []

    # Process each line load definition for mesh optimization
    for i, df_row in df_input_load_line.iterrows():
        name = df_row[('Line Data', 'Line Load')]

        # Look up line geometry information
        line_assign_row = df_lineload[df_lineload['Line Load'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        # Include both endpoints in floor mesh generation
        new_rows.append([pt_i, 'Yes'])  # Start point
        new_rows.append([pt_j, 'Yes'])  # End point

    # Create mesh optimization DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add mesh optimization settings to existing data
    if not df_append.empty:
        if safe22_dfs.JointAssignsFloorMeshOpt.empty:
            safe22_dfs.JointAssignsFloorMeshOpt = df_append.copy()
        else:
            safe22_dfs.JointAssignsFloorMeshOpt = pd.concat(
                [safe22_dfs.JointAssignsFloorMeshOpt, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def line_load_FrameLoadsDistributed_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Apply distributed loads to line elements (frames) in SAFE 22 model.

    Converts line load data into SAFE 22 distributed frame load assignments,
    including coordinate transformations, load combinations, and additional
    loads due to eccentricity effects. Handles both forces and moments in
    local and global coordinate systems.

    """
    # Calculate line geometry properties (length and orientation)
    df_line_length_data = functions.cal_line_length(excel_inputs)
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()

    # Extract load case column names (exclude 'Line Data' columns)
    all_columns = df_input_load_line.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Line Data' not in col]

    new_rows = []

    # Process each line load definition
    for i, df_row in df_input_load_line.iterrows():
        name = df_row[('Line Data', 'Line Load')]

        # Look up line geometric properties
        condition_length = df_line_length_data['Line Load'] == name
        length = df_line_length_data.loc[condition_length, 'Line Length (m)'].item()
        deg = df_line_length_data.loc[condition_length, 'Line Theta (deg)'].item()
        t = df_row[('Line Data', 'Cap Thickness (m)')]

        # Process each load case for this line element
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components in local coordinate system
            v_1 = load_data['V1 (kN/m)']          # Local 1-direction force per unit length
            v_3 = load_data['V3 (kN/m)']          # Local 3-direction force per unit length
            f_z = load_data['Axial (kN/m)']       # Axial force per unit length
            m_1_input = load_data['M1 (kNm/m)']   # Moment about local 1-axis per unit length
            m_3_input = load_data['M3 (kNm/m)']   # Moment about local 3-axis per unit length
            m_z = load_data['Mz (kNm/m)']         # Torsional moment per unit length

            # Transform loads from local to global coordinates and calculate additional effects
            # This function handles coordinate transformations, eccentricity effects, and
            # additional loads due to shear-moment coupling
            m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_line_load_local(
                    t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            # Apply transformed loads to SAFE 22 model (only non-zero components)

            # Local 1-direction force (along element axis)
            if s_1 != 0:
                new_rows.append([
                    name, load_case, 'Force', 'Local-1', 'Relative', 0, 1, 0, length, s_1, s_1, None, None, None])

            # Local 3-direction force (perpendicular to element)
            if s_3 != 0:
                new_rows.append([
                    name, load_case, 'Force', 'Local-3', 'Relative', 0, 1, 0, length, s_3, s_3, None, None, None])

            # Gravity force (vertical loads, may vary along length)
            if s_z_start != 0 or s_z_end != 0:
                new_rows.append([
                    name, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start, s_z_end, None, None,
                    None])

            # Moment about local 1-axis
            if s_m1 != 0:
                new_rows.append([
                    name, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1, s_m1, None])

            # Torsional moment (about gravity axis, sign convention applied)
            if s_mz != 0:
                new_rows.append([
                    name, load_case, 'Moment', 'Gravity', 'Relative', 0, 1, 0, length, None, None, -s_mz, -s_mz, None])

            # Additional loads due to shear-eccentricity coupling

            # Additional gravity force due to shear eccentricity
            if s_z_start_add != 0 or s_z_end_add != 0:
                new_rows.append([
                    name, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None, None])

            # Additional moment about local 1-axis due to shear eccentricity
            if s_m1_add != 0:
                new_rows.append([
                    name, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1_add, s_m1_add,
                    None])

    # Create distributed loads DataFrame with proper column structure
    df_append = pd.DataFrame(new_rows, columns=safe22_dfs.FrameLoadsDistributed.columns)

    # Add distributed loads to existing data
    if not df_append.empty:
        if safe22_dfs.FrameLoadsDistributed.empty:
            safe22_dfs.FrameLoadsDistributed = df_append.copy()
        else:
            safe22_dfs.FrameLoadsDistributed = pd.concat(
                [safe22_dfs.FrameLoadsDistributed, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def _write_line_load_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Process all line load operations for SAFE 22 model.

    Orchestrates the complete line load processing workflow including group
    definitions, group assignments, mesh optimization, and distributed load
    applications. This function ensures all line load data is properly
    converted and applied to the SAFE 22 model.

    """
    # Create line load group definition
    safe22_dfs, df_append = line_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)

    # Assign line elements and endpoints to load group
    safe22_dfs, df_append = line_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)

    # Configure mesh optimization for loaded line endpoints
    safe22_dfs, df_append = line_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)

    # Apply distributed loads to line elements
    safe22_dfs, df_append = line_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)

    return safe22_dfs


def pile_load_GroupDefinitions_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create group definitions for pile loads in SAFE 22 model.

    Defines a load group specifically for pile loads with standard properties.
    This group will contain all pile elements that have applied loads and helps
    with visualization and organization in the SAFE 22 model.

    """
    # Define pile load group with standard properties
    s22_group_definitions_rows = [['A.Load_Pile', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)

    # Add group definition to existing data
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def pile_load_GroupAssignments_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Assign pile top points to the pile load group in SAFE 22 model.

    Maps pile load identifiers to pile top points and assigns them to the
    'A.Load_Pile' group for visualization and organization. Pile loads are
    typically applied at the pile head (top point) for load transfer analysis.

    """
    df_input_load_pile = excel_inputs.InputLoadPile.fillna(0).copy()

    group_assignments_22_list = []

    # Process each pile load definition
    for _, df_row in df_input_load_pile.iterrows():
        pile_mark = df_row[('Pile Data', 'Pile Mark')]

        # Convert pile mark to top point identifier
        # Pile loads are applied at pile head (top point)
        point = f"{pile_mark}_T"

        # Assign pile top point to load group
        group_assignments_22_list.append(['A.Load_Pile', 'Point', point])

    # Create group assignment DataFrame with proper column structure
    df_append = pd.DataFrame(group_assignments_22_list, columns=safe22_dfs.GroupAssignments.columns)

    # Add assignments to existing group data
    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat(
                [safe22_dfs.GroupAssignments, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def pile_load_JointLoadsForce_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Apply pile forces and moments to pile head joints in SAFE 22 model.

    Converts pile load data into SAFE 22 joint force assignments at pile heads.
    Handles foundation loads transferred from superstructure to pile foundations,
    including both forces and moments with proper coordinate system conventions.

    """
    df_input_load_pile = excel_inputs.InputLoadPile.fillna(0).copy()

    # Extract load case column names (exclude 'Pile Data' columns)
    all_columns = df_input_load_pile.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Pile Data' not in col]

    # Prepare list for batch DataFrame creation (performance optimization)
    joint_loads_force_22_list = []

    # Process each pile load definition
    for _, df_row in df_input_load_pile.iterrows():
        pile_mark = df_row[('Pile Data', 'Pile Mark')]

        # Convert pile mark to top point identifier (load application point)
        point = f"{pile_mark}_T"

        # Process each load case for this pile
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components
            v_x = load_data['Vx (kN)']      # Horizontal force in X-direction
            v_y = load_data['Vy (kN)']      # Horizontal force in Y-direction
            f_z = load_data['Axial (kN)']   # Vertical force (axial)
            m_x = load_data['Mx (kNm)']     # Moment about X-axis
            m_y = load_data['My (kNm)']     # Moment about Y-axis

            # Apply loads only if at least one component is non-zero
            if v_x != 0 or v_y != 0 or f_z != 0 or m_x != 0 or m_y != 0:
                # Note: f_z sign is reversed for SAFE coordinate convention
                # Mz is set to 0 (not typically applied to pile heads)
                joint_loads_force_22_list.append([point, load_case, v_x, v_y, -f_z, m_x, m_y, 0, 0, 0, None])

    # Create joint loads DataFrame with proper column structure
    df_append = pd.DataFrame(joint_loads_force_22_list, columns=safe22_dfs.JointLoadsForce.columns)

    # Add joint loads to existing data
    if not df_append.empty:
        if safe22_dfs.JointLoadsForce.empty:
            safe22_dfs.JointLoadsForce = df_append.copy()
        else:
            safe22_dfs.JointLoadsForce = pd.concat(
                [safe22_dfs.JointLoadsForce, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def slab_load_AreaLoadsUniform_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Apply uniform area loads to slab elements in SAFE 22 model.

    Converts slab load data into SAFE 22 uniform area load assignments.
    Handles distributed loads on slab areas including dead loads, live loads,
    and other uniform pressure loads with proper coordinate system applications.

    """
    df_input_load_slab = excel_inputs.InputLoadSlab.fillna(0).copy()

    # Extract load case column names (exclude 'Slab Data' columns)
    all_columns = df_input_load_slab.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Slab Data' not in col]

    # Prepare list for batch DataFrame creation (performance optimization)
    area_loads_uniform_22_list = []

    # Process each slab load definition
    for _, df_row in df_input_load_slab.iterrows():
        slab_mark = df_row[('Slab Data', 'Slab Mark')]

        # Process each load case for this slab
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components (pressure values)
            v_x = load_data['Vx (kN/m2)']      # Horizontal pressure in X-direction
            v_y = load_data['Vy (kN/m2)']      # Horizontal pressure in Y-direction
            f_z = load_data['Axial (kN/m2)']   # Vertical pressure (gravity)

            # Apply each non-zero load component separately
            if v_x != 0:
                area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-X', v_x, None])

            if v_y != 0:
                area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-Y', v_y, None])

            if f_z != 0:
                area_loads_uniform_22_list.append([slab_mark, load_case, 'Gravity', f_z, None])

    # Create area loads DataFrame with proper column structure
    df_append = pd.DataFrame(area_loads_uniform_22_list, columns=safe22_dfs.AreaLoadsUniform.columns)

    # Add area loads to existing data
    if not df_append.empty:
        if safe22_dfs.AreaLoadsUniform.empty:
            safe22_dfs.AreaLoadsUniform = df_append.copy()
        else:
            safe22_dfs.AreaLoadsUniform = pd.concat(
                [safe22_dfs.AreaLoadsUniform, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def lkp_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()
    df_lkp = excel_inputs.LKP.copy()
    df_input_load_lkp = excel_inputs.InputLoadLKP.fillna(0).copy()

    df_slab['Load Group'] = df_slab['Load Group'].astype(str)
    df_lkp['Load Group'] = df_lkp['Load Group'].astype(str)

    # Pre-extract column names for load cases from df_input_load_lkp
    all_lkp_columns = df_input_load_lkp.columns.get_level_values(0).unique()
    load_cases_lkp = [col for col in all_lkp_columns if 'LKP Data' not in col]

    # Prepare lists for batch DataFrame appends
    area_loads_uniform_22_list = []

    for _, df_row_lkp in df_input_load_lkp.iterrows():  # Use _ if index is not needed
        lkp_type = df_row_lkp[('LKP Data', 'LKP Type')]

        if isinstance(lkp_type, float):
            lkp_type = str(int(lkp_type))

        # Filter df_slab once per lkp_type
        df_load_group = df_slab[df_slab['Load Group'] == lkp_type]

        for _, df_row_group in df_load_group.iterrows():  # Use _ if index is not needed
            slab_mark = df_row_group['Slab']

            for load_case in load_cases_lkp:
                load_data = df_row_lkp[load_case]
                v_x = load_data['Vx (kN/m2)']
                v_y = load_data['Vy (kN/m2)']
                f_z = load_data['Axial (kN/m2)']

                if v_x != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-X', v_x, None])

                if v_y != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-Y', v_y, None])

                if f_z != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Gravity', f_z, None])

    df_append_1 = pd.DataFrame(area_loads_uniform_22_list, columns=safe22_dfs.AreaLoadsUniform.columns)

    # Prepare lists for batch DataFrame appends
    area_loads_uniform_22_list = []

    for _, df_row_lkp in df_input_load_lkp.iterrows():  # Use _ if index is not needed
        lkp_type = df_row_lkp[('LKP Data', 'LKP Type')]

        if isinstance(lkp_type, float):
            lkp_type = str(int(lkp_type))

        # Filter df_slab once per lkp_type
        df_load_group = df_lkp[df_lkp['Load Group'] == lkp_type]

        for _, df_row_group in df_load_group.iterrows():  # Use _ if index is not needed
            slab_mark = df_row_group['LKP']

            for load_case in load_cases_lkp:
                load_data = df_row_lkp[load_case]
                v_x = load_data['Vx (kN/m2)']
                v_y = load_data['Vy (kN/m2)']
                f_z = load_data['Axial (kN/m2)']

                if v_x != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-X', v_x, None])

                if v_y != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Global-Y', v_y, None])

                if f_z != 0:
                    area_loads_uniform_22_list.append([slab_mark, load_case, 'Gravity', f_z, None])

    df_append_2 = pd.DataFrame(area_loads_uniform_22_list, columns=safe22_dfs.AreaLoadsUniform.columns)

    # concat df_append_1 and df_append_2
    df_append = pd.concat([df_append_1, df_append_2], ignore_index=True)
    # Batch append to DataFrames
    if not df_append.empty:
        if safe22_dfs.AreaLoadsUniform.empty:
            safe22_dfs.AreaLoadsUniform = df_append.copy()
        else:
            safe22_dfs.AreaLoadsUniform = pd.concat(
                [safe22_dfs.AreaLoadsUniform, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def beam_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs):
    s22_group_definitions_rows = [['A.Load_Beam', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def beam_load_GroupAssignments_safe22(excel_inputs, safe22_dfs):
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()
    df_beam = excel_inputs.Beam.copy()

    # rename df_beam 'Beam' to 'Line (Text)' for consistency
    df_beam.rename(columns={'Beam': 'Line (Text)'}, inplace=True)
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(
        ';', expand=True)

    target_columns = safe22_dfs.GroupAssignments.columns.tolist()
    new_rows = []

    for i, df_row in df_input_load_beam.iterrows():
        name = df_row[('Beam Data', 'Beam Mark')]

        line_assign_row = df_beam[df_beam['Line (Text)'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        new_rows.append(['A.Load_Beam', 'Line', name])
        new_rows.append(['A.Load_Beam', 'Point', pt_i])
        new_rows.append(['A.Load_Beam', 'Point', pt_j])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat(
                [safe22_dfs.GroupAssignments, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def beam_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs):
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()
    df_beam = excel_inputs.Beam.copy()

    # rename df_beam 'Beam' to 'Line (Text)' for consistency
    df_beam.rename(columns={'Beam': 'Line (Text)'}, inplace=True)
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(
        ';', expand=True)

    target_columns = safe22_dfs.JointAssignsFloorMeshOpt.columns.tolist()
    new_rows = []

    for i, df_row in df_input_load_beam.iterrows():
        name = df_row[('Beam Data', 'Beam Mark')]

        # ASSIGN WALL POINT AND LINE TO GROUP
        # Look up PointI and PointJ
        line_assign_row = df_beam[df_beam['Line (Text)'] == name]
        pt_i = line_assign_row['PointI (Text)'].item()
        pt_j = line_assign_row['PointJ (Text)'].item()

        # INCLUDE COLUMN POINT FOR MESH IN SAFE
        new_rows.append([pt_i, 'Yes'])
        new_rows.append([pt_j, 'Yes'])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    if not df_append.empty:
        if safe22_dfs.JointAssignsFloorMeshOpt.empty:
            safe22_dfs.JointAssignsFloorMeshOpt = df_append.copy()
        else:
            safe22_dfs.JointAssignsFloorMeshOpt = pd.concat(
                [safe22_dfs.JointAssignsFloorMeshOpt, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def beam_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs):
    df_beam_length_data = functions.cal_beam_length(excel_inputs)
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()
    all_columns = df_input_load_beam.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Beam Data' not in col]
    
    # Prepare lists for batch DataFrame appends to improve performance
    new_rows = []

    for _, df_row in df_input_load_beam.iterrows():  # Use _ if index is not needed
        beam_mark = df_row[('Beam Data', 'Beam Mark')]
        t = df_row[('Beam Data', 'Cap Thickness (m)')]

        # Look up beam length and angle
        condition_length = df_beam_length_data['Beam Mark'] == beam_mark
        length = df_beam_length_data.loc[condition_length, 'Beam Length (m)'].item()
        deg = df_beam_length_data.loc[condition_length, 'Beam Theta (deg)'].item()

        for load_case in load_cases:
            load_data = df_row[load_case]
            v_1 = load_data['V1 (kN/m)']
            v_3 = load_data['V3 (kN/m)']
            f_z = load_data['Axial (kN/m)']
            m_1_input = load_data['M1 (kNm/m)']  # Renamed to avoid conflict
            m_3_input = load_data['M3 (kNm/m)']  # Renamed to avoid conflict
            m_z = load_data['Mz (kNm/m)']

            m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_line_load_local(
                    t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            if s_1 != 0:
                new_rows.append([
                    beam_mark, load_case, 'Force', 'Local-1', 'Relative', 0, 1, 0, length, s_1, s_1, None, None, None])

            if s_3 != 0:
                new_rows.append([
                    beam_mark, load_case, 'Force', 'Local-3', 'Relative', 0, 1, 0, length, s_3, s_3, None, None, None])

            if s_z_start != 0 or s_z_end != 0:
                new_rows.append([
                    beam_mark, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start, s_z_end, None, None,
                    None])

            if s_m1 != 0:
                new_rows.append([
                    beam_mark, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1, s_m1, None])

            if s_mz != 0:
                new_rows.append([
                    beam_mark, load_case, 'Moment', 'Gravity', 'Relative', 0, 1, 0, length, None, None, -s_mz, -s_mz, None])

            if s_z_start_add != 0 or s_z_end_add != 0:
                # Additional Load due to Shear
                new_rows.append([
                    beam_mark, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None, None])

            if s_m1_add != 0:
                new_rows.append([
                    beam_mark, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1_add, s_m1_add,
                    None])

    df_append = pd.DataFrame(new_rows, columns=safe22_dfs.FrameLoadsDistributed.columns)
    if not df_append.empty:
        if safe22_dfs.FrameLoadsDistributed.empty:
            safe22_dfs.FrameLoadsDistributed = df_append.copy()
        else:
            safe22_dfs.FrameLoadsDistributed = pd.concat(
                [safe22_dfs.FrameLoadsDistributed, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def column_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs):
    s22_group_definitions_rows = [['A.Load_Column', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def column_load_GroupAssignments_safe22(excel_inputs, safe22_dfs):
    df_column = excel_inputs.Column.fillna(0).copy()
    s22_group_assignments_rows = []
    for i, df_row in df_column.iterrows():
        name = df_row['Column']
        center = df_row['Center Point']

        # DEFINE COLUMN POSITION TO SAFE & ASSIGN TO GROUP
        s22_group_assignments_rows.append(['A.Load_Column', 'Point', center])
        s22_group_assignments_rows.append(['A.Load_Column', 'Area', name])

    df_append = pd.DataFrame(s22_group_assignments_rows, columns=safe22_dfs.GroupAssignments.columns)

    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat([safe22_dfs.GroupAssignments, df_append], ignore_index=True)

    return safe22_dfs, df_append


def column_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs):
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()
    df_column = excel_inputs.Column.copy()

    df = pd.DataFrame()
    df['Column'] = df_input_load_column['Column Data', 'Column Mark']
    merged_df = pd.merge(df, df_column, on='Column', how='left')
    df_input_load_column['Column Data', 'Center Point (Text)'] = merged_df['Center Point']

    s22_joint_assigns_floor_mesh_opt_rows = []

    for i, df_row in df_input_load_column.iterrows():
        center = df_row[('Column Data', 'Center Point (Text)')]

        s22_joint_assigns_floor_mesh_opt_rows.append([center, 'Yes'])

    df_append = pd.DataFrame(s22_joint_assigns_floor_mesh_opt_rows, columns=safe22_dfs.JointAssignsFloorMeshOpt.columns)

    if not df_append.empty:
        if safe22_dfs.JointAssignsFloorMeshOpt.empty:
            safe22_dfs.JointAssignsFloorMeshOpt = df_append.copy()
        else:
            safe22_dfs.JointAssignsFloorMeshOpt = pd.concat([safe22_dfs.JointAssignsFloorMeshOpt, df_append],
                                                            ignore_index=True)
    return safe22_dfs, df_append


def column_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs):
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()
    df_column = excel_inputs.Column.copy()

    df = pd.DataFrame()
    df['Column'] = df_input_load_column['Column Data', 'Column Mark']
    merged_df = pd.merge(df, df_column, on='Column', how='left')
    df_input_load_column['Column Data', 'Center Point (Text)'] = merged_df['Center Point']

    s22_joint_loads_force_rows = []

    for i, df_row in df_input_load_column.iterrows():
        center = df_row[('Column Data', 'Center Point (Text)')]
        t = df_row[('Column Data', 'Cap Thickness (m)')]
        load_type = df_row[('Column Data', 'Area/Point Load (A/P)')]

        # WRITE COLUMN Load TO SAFE
        cols = df_input_load_column.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Column Data')
        load_cases = cols[condition]
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            # WRITE COLUMN CENTER Load (Moments) TO SAFE
            if (m_x != 0) or (m_y != 0) or (m_z != 0):
                s22_joint_loads_force_rows.append([center, load_case, 0, 0, 0, m_x, m_y, m_z, 0, 0, None])

            # WRITE COLUMN ADDITIONAL CENTER Load DUE TO SHEAR TO SAFE
            m_x_add = 0 - v_y * t
            m_y_add = 0 + v_x * t
            if (m_x_add != 0) or (m_y_add != 0):
                s22_joint_loads_force_rows.append([center, load_case, 0, 0, 0, m_x_add, m_y_add, 0, 0, 0, None])

            # WRITE COLUMN AREA/POINT Load TO SAFE
            if load_type == 'P':
                if (v_x != 0) or (v_y != 0) or (f_z != 0):
                    s22_joint_loads_force_rows.append([center, load_case, v_x, v_y, -f_z, 0, 0, 0, 0, 0, None])

    # Concatenate collected rows to the respective DataFrames
    df_append = pd.DataFrame(s22_joint_loads_force_rows, columns=safe22_dfs.JointLoadsForce.columns)

    if not df_append.empty:
        if safe22_dfs.JointLoadsForce.empty:
            safe22_dfs.JointLoadsForce = df_append.copy()
        else:
            safe22_dfs.JointLoadsForce = pd.concat([safe22_dfs.JointLoadsForce, df_append], ignore_index=True)

    return safe22_dfs, df_append


def column_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs):
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()

    s22_area_loads_uniform_rows = []

    for i, df_row in df_input_load_column.iterrows():
        name = df_row[('Column Data', 'Column Mark')]
        area = df_row[('Column Data', 'Area (m2)')]
        center = df_row[('Column Data', 'Center Point (Text)')]
        t = df_row[('Column Data', 'Cap Thickness (m)')]
        load_type = df_row[('Column Data', 'Area/Point Load (A/P)')]

        # WRITE COLUMN Load TO SAFE
        cols = df_input_load_column.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Column Data')
        load_cases = cols[condition]
        for load_case in load_cases:
            load_data = df_row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            # WRITE COLUMN AREA/POINT Load TO SAFE
            if load_type == 'A':
                if area == 0:  # Avoid division by zero if area is 0 for an area load
                    # Potentially log a warning or handle as an error
                    # For now, skip adding these loads if area is zero
                    pass
                else:
                    if v_x != 0:
                        s22_area_loads_uniform_rows.append([name, load_case, 'Global-X', v_x / area, None])
                    if v_y != 0:
                        s22_area_loads_uniform_rows.append([name, load_case, 'Global-Y', v_y / area, None])
                    if f_z != 0:
                        s22_area_loads_uniform_rows.append([name, load_case, 'Gravity', f_z / area, None])

    # Concatenate collected rows to the respective DataFrames
    df_append = pd.DataFrame(s22_area_loads_uniform_rows, columns=safe22_dfs.AreaLoadsUniform.columns)
    if not df_append.empty:
        if safe22_dfs.AreaLoadsUniform.empty:
            safe22_dfs.AreaLoadsUniform = df_append.copy()
        else:
            safe22_dfs.AreaLoadsUniform = pd.concat([safe22_dfs.AreaLoadsUniform, df_append], ignore_index=True)

    return safe22_dfs, df_append


def _write_column_load_safe22(excel_inputs, safe22_dfs):
    safe22_dfs, df_append = column_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)
    return safe22_dfs


def wall_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs):
    s22_group_definitions_rows = [['A.Load_Wall', 'Red', 'No', 'No', 'No', None]]

    df_append = pd.DataFrame(s22_group_definitions_rows, columns=safe22_dfs.GroupDefinitions.columns)
    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat(
                [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def wall_load_GroupAssignments_safe22(excel_inputs, safe22_dfs):
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()
    df_wall = excel_inputs.Wall.copy()

    target_columns = safe22_dfs.GroupAssignments.columns.tolist()
    new_rows = []

    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)

    for i, df_row in df_input_load_wall.iterrows():
        name = df_row[('Wall Data', 'Wall Mark')]

        index_target = df_wall.index[df_wall['Wall'] == name]
        pt_i = df_wall.loc[index_target, 'PointI (Text)'].values[0]
        pt_j = df_wall.loc[index_target, 'PointJ (Text)'].values[0]
        center = df_wall.loc[index_target, 'Center Point'].values[0]

        new_rows.append(['A.Load_Wall', 'Line', name])
        new_rows.append(['A.Load_Wall', 'Point', pt_i])
        new_rows.append(['A.Load_Wall', 'Point', pt_j])
        new_rows.append(['A.Load_Wall', 'Point', center])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:
            safe22_dfs.GroupAssignments = pd.concat(
                [safe22_dfs.GroupAssignments, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def wall_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs):
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()
    df_wall = excel_inputs.Wall.copy()

    target_columns = safe22_dfs.JointAssignsFloorMeshOpt.columns.tolist()
    new_rows = []

    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)

    for i, df_row in df_input_load_wall.iterrows():
        name = df_row[('Wall Data', 'Wall Mark')]
        index_target = df_wall.index[df_wall['Wall'] == name]
        pt_i = df_wall.loc[index_target, 'PointI (Text)'].values[0]
        pt_j = df_wall.loc[index_target, 'PointJ (Text)'].values[0]
        center = df_wall.loc[index_target, 'Center Point'].values[0]

        # INCLUDE COLUMN POINT FOR MESH IN SAFE
        new_rows.append([pt_i, 'Yes'])
        new_rows.append([pt_j, 'Yes'])
        new_rows.append([center, 'Yes'])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    if not df_append.empty:
        if safe22_dfs.JointAssignsFloorMeshOpt.empty:
            safe22_dfs.JointAssignsFloorMeshOpt = df_append.copy()
        else:
            safe22_dfs.JointAssignsFloorMeshOpt = pd.concat(
                [safe22_dfs.JointAssignsFloorMeshOpt, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def wall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs):
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()
    df_wall_length = functions.cal_wall_length(excel_inputs)

    target_columns = safe22_dfs.FrameLoadsDistributed.columns.tolist()
    new_rows = []

    for i, df_row in df_input_load_wall.iterrows():
        name = df_row[('Wall Data', 'Wall Mark')]
        condition = df_wall_length['Wall Name'] == name
        length = df_wall_length.loc[condition, 'Wall Length (m)'].values[0]
        deg = df_wall_length.loc[condition, 'Wall Theta (deg)'].values[0]
        t = df_row[('Wall Data', 'Cap Thickness (m)')]

        # WRITE WALL Load TO SAFE
        cols = df_input_load_wall.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Wall Data')
        load_cases = cols[condition]
        for load_case in load_cases:
            # load data of each wall under each load case
            load_data = df_row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            mx_add, my_add, m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add, s_x, s_y, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz = \
                functions.transform_wall_load_global(
                    t, length, deg, v_x, v_y, f_z, m_x, m_y, m_z)

            not_zero = (s_x != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Force', 'Global-X', 'Relative', 0, 1, 0, length, s_x, s_x, None, None, None])

            not_zero = (s_y != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Force', 'Global-Y', 'Relative', 0, 1, 0, length, s_y, s_y, None, None, None])

            not_zero = (s_z_start != 0) or (s_z_end != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start, s_z_end, None, None,
                    None])

            not_zero = (s_m1 != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1, s_m1, None])

            not_zero = (s_mz != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Moment', 'Gravity', 'Relative', 0, 1, 0, length, None, None, -s_mz, -s_mz, None])

            # Additional Load due to Shear
            not_zero = (s_z_start_add != 0) or (s_z_end_add != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Force', 'Gravity', 'Relative', 0, 1, 0, length, s_z_start_add, s_z_end_add, None,
                    None, None])

            not_zero = (s_m1_add != 0)
            if not_zero:
                new_rows.append([
                    name, load_case, 'Moment', 'Local-1', 'Relative', 0, 1, 0, length, None, None, s_m1_add, s_m1_add,
                    None])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    if not df_append.empty:
        if safe22_dfs.FrameLoadsDistributed.empty:
            safe22_dfs.FrameLoadsDistributed = df_append.copy()
        else:
            # Ensure consistent dtypes before concatenation
            for col in df_append.columns:
                if col in safe22_dfs.FrameLoadsDistributed.columns:
                    if df_append[col].isna().all() and not safe22_dfs.FrameLoadsDistributed[col].isna().all():
                        df_append[col] = df_append[col].astype(safe22_dfs.FrameLoadsDistributed[col].dtype)
                    elif safe22_dfs.FrameLoadsDistributed[col].isna().all() and not df_append[col].isna().all():
                        safe22_dfs.FrameLoadsDistributed[col] = safe22_dfs.FrameLoadsDistributed[col].astype(df_append[col].dtype)

            safe22_dfs.FrameLoadsDistributed = pd.concat(
                [safe22_dfs.FrameLoadsDistributed, df_append], ignore_index=True
            )
    return safe22_dfs, df_append


def _write_wall_load_safe22(excel_inputs, safe22_dfs):
    safe22_dfs, df_append = wall_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)
    return safe22_dfs


def corewall_group_GroupDefinitions_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()  # Changed from CoreWallGroup

    new_group_defs_22_rows = []
    # Store original column names
    group_defs_22_cols = safe22_dfs.GroupDefinitions.columns

    # Get unique 'Wall Group' names from df_wall
    if 'Wall Group' in df_wall.columns:
        unique_core_wall_names = df_wall['Wall Group'].dropna().unique()
        # Filter out potential empty strings or other invalid group names if necessary
        unique_core_wall_names = [name for name in unique_core_wall_names if isinstance(name, str) and name.strip() != '']

        for core_wall_name in unique_core_wall_names:
            new_group_defs_22_rows.append([core_wall_name, core_wall_name, 'No', 'No', 'No', None])
    else:
        # Optionally, print a warning or log if 'Wall Group' column is missing
        print("Warning: 'Wall Group' column not found in excel_inputs.Wall. Cannot create corewall group definitions.")

    df_append = pd.DataFrame(new_group_defs_22_rows, columns=group_defs_22_cols)

    if not df_append.empty:
        if safe22_dfs.GroupDefinitions.empty:
            safe22_dfs.GroupDefinitions = df_append.copy()
        else:
            safe22_dfs.GroupDefinitions = pd.concat([safe22_dfs.GroupDefinitions, df_append], ignore_index=True)

    return safe22_dfs, df_append


def corewall_group_GroupAssignments_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()  # Changed from CoreWallGroup

    new_group_assign_22_rows = []

    # Store original column names
    group_assign_22_cols = safe22_dfs.GroupAssignments.columns

    for _, df_row in df_wall.iterrows():
        core_wall_name = df_row.get('Wall Group')  # Get the group name for the wall
        wall_name = df_row.get('Wall')  # Get the wall name

        # Ensure both core_wall_name and wall_name are valid before appending
        if pd.notna(core_wall_name) and isinstance(core_wall_name, str) and core_wall_name.strip() != '' and pd.notna(wall_name):
            new_group_assign_22_rows.append([core_wall_name, 'Line', wall_name])

    df_append = pd.DataFrame(new_group_assign_22_rows, columns=group_assign_22_cols)

    if not df_append.empty:
        if safe22_dfs.GroupAssignments.empty:
            safe22_dfs.GroupAssignments = df_append.copy()
        else:  # This part was missing in the provided excerpt, adding it for completeness
            safe22_dfs.GroupAssignments = pd.concat([safe22_dfs.GroupAssignments, df_append], ignore_index=True)

    return safe22_dfs, df_append


def corewall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs):
    df_wall_definitions = excel_inputs.Wall.copy()  # Use Wall for wall-to-group mapping

    df_input_load_corewall = excel_inputs.InputLoadCoreWall.fillna(0).copy()
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()

    df_wall_length_calc = functions.cal_wall_length(excel_inputs)  # External function
    df_corewall_length_calc = functions.cal_corewall_length(excel_inputs, df_wall_length_calc)  # External function

    # Prepare for faster lookups
    df_corewall_length_indexed = df_corewall_length_calc.set_index('CoreWall Name')

    wall_mark_col = ('Wall Data', 'Wall Mark')
    wall_len_col = ('Wall Data', 'Wall Length (m)')
    # Create a mapping from Wall Mark to Wall Length, assuming Wall Marks are unique
    if wall_mark_col in df_input_load_wall.columns and wall_len_col in df_input_load_wall.columns:
        wall_length_map = pd.Series(
            df_input_load_wall[wall_len_col].values,
            index=df_input_load_wall[wall_mark_col]
        ).to_dict()
    else:
        wall_length_map = {}
        print(f"Warning: Columns for wall length mapping ('{wall_mark_col}', '{wall_len_col}') not found in df_input_load_wall.")

    new_frame_loads_dist_22_rows = []

    # Store original column names
    frame_loads_dist_22_cols = safe22_dfs.FrameLoadsDistributed.columns

    for i, df_corewall_row in df_input_load_corewall.iterrows():
        corewall_name = df_corewall_row[('CoreWall Data', 'CoreWall Mark')]

        try:
            corewall_len = df_corewall_length_indexed.at[corewall_name, 'CoreWall Length (m)']
        except KeyError:
            print(f"Warning: Corewall '{corewall_name}' not found in length data. Skipping this corewall.")
            continue

        cap_thickness = df_corewall_row[('CoreWall Data', 'Cap Thickness (m)')]

        # Determine walls for this corewall_name using df_wall_definitions
        if 'Wall Group' in df_wall_definitions.columns and 'Wall' in df_wall_definitions.columns:
            associated_walls_df = df_wall_definitions[df_wall_definitions['Wall Group'] == corewall_name]
            if associated_walls_df.empty:
                print(f"Warning: No walls found assigned to corewall group '{corewall_name}' in excel_inputs.Wall. Loads for this group will not be distributed to individual walls.")
                walls_for_corewall = []
            else:
                walls_for_corewall = associated_walls_df['Wall'].tolist()
        else:
            print(f"Warning: 'Wall Group' or 'Wall' column not found in excel_inputs.Wall. Cannot determine walls for corewall group '{corewall_name}'.")
            walls_for_corewall = []

        # Identify load case columns (level 0 of MultiIndex, excluding 'CoreWall Data')
        all_level0_cols = df_input_load_corewall.columns.get_level_values(0).unique()
        load_cases_level0 = [col_l0 for col_l0 in all_level0_cols if col_l0 != 'CoreWall Data']

        for load_case_name in load_cases_level0:
            load_data_subset = df_corewall_row[load_case_name]

            v_x = load_data_subset.get('Vx (kN)', 0.0)
            v_y = load_data_subset.get('Vy (kN)', 0.0)
            f_z = load_data_subset.get('Axial (kN)', 0.0)
            m_x = load_data_subset.get('Mx (kNm)', 0.0)
            m_y = load_data_subset.get('My (kNm)', 0.0)
            m_z = load_data_subset.get('Mz (kNm)', 0.0)

            mx_add = - (v_y * cap_thickness)
            my_add = + (v_x * cap_thickness)

            s_x, s_y, s_z, s_m_x, s_m_y, s_m_z, s_m_x_add, s_m_y_add = (0.0,) * 8
            if corewall_len != 0:
                s_x = v_x / corewall_len
                s_y = v_y / corewall_len
                s_z = f_z / corewall_len
                s_m_x = m_x / corewall_len
                s_m_y = m_y / corewall_len
                s_m_z = m_z / corewall_len
                s_m_x_add = mx_add / corewall_len
                s_m_y_add = my_add / corewall_len

            for wall_name_str in walls_for_corewall:
                wall_len = wall_length_map.get(wall_name_str)
                if wall_len is None:
                    continue

                # Append rows to lists
                if s_x != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Global-X', 'Relative', 0, 1, 0, wall_len,
                        s_x, s_x, None, None, None])
                if s_y != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Global-Y', 'Relative', 0, 1, 0, wall_len,
                        s_y, s_y, None, None, None])
                if s_z != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Force', 'Gravity', 'Relative', 0, 1, 0, wall_len,
                        s_z, s_z, None, None, None])
                if s_m_x != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global-X', 'Relative', 0, 1, 0, wall_len, None, None,
                        s_m_x, s_m_x, None])
                if s_m_y != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global-Y', 'Relative', 0, 1, 0, wall_len, None, None,
                        s_m_y, s_m_y, None])
                if s_m_z != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Gravity', 'Relative', 0, 1, 0, wall_len, None, None,
                        -s_m_z, -s_m_z, None])  # Note: original code uses -s_m_z
                if s_m_x_add != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global-X', 'Relative', 0, 1, 0, wall_len, None, None,
                        s_m_x_add, s_m_x_add, None])
                if s_m_y_add != 0:
                    new_frame_loads_dist_22_rows.append([
                        wall_name_str, load_case_name, 'Moment', 'Global-Y', 'Relative', 0, 1, 0, wall_len, None, None,
                        s_m_y_add, s_m_y_add, None])

    # Concatenate all collected rows at once
    df_append = pd.DataFrame(new_frame_loads_dist_22_rows, columns=frame_loads_dist_22_cols)
    if not df_append.empty:
        if safe22_dfs.FrameLoadsDistributed.empty:
            safe22_dfs.FrameLoadsDistributed = df_append.copy()
        else:
            safe22_dfs.FrameLoadsDistributed = pd.concat(
                [safe22_dfs.FrameLoadsDistributed, df_append], ignore_index=True
            )

    return safe22_dfs, df_append


def _write_corewall_load_safe22(excel_inputs, safe22_dfs):
    safe22_dfs, df_append = corewall_group_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = corewall_group_GroupAssignments_safe22(excel_inputs, safe22_dfs)
    safe22_dfs, df_append = corewall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)

    return safe22_dfs


def _write_beam_load_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Process all beam load operations for SAFE 22 model.

    Orchestrates the complete beam load processing workflow including group
    definitions, group assignments, mesh optimization, and distributed load
    applications. This function ensures all beam load data is properly
    converted and applied to the SAFE 22 model.

    """
    # Create beam load group definition
    safe22_dfs, df_append = beam_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)

    # Assign beam elements and endpoints to load group
    safe22_dfs, df_append = beam_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)

    # Configure mesh optimization for loaded beam endpoints
    safe22_dfs, df_append = beam_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)

    # Apply distributed loads to beam elements
    safe22_dfs, df_append = beam_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)

    return safe22_dfs
