from datetime import datetime

import numpy as np
import pandas as pd

from read.functions import _load_sheet_data


def _identity_process(df):
    """Default processing function that returns the DataFrame as is."""
    return df


def _process_pile_bp_data(df):
    df['Pile Type'] = 'BP'
    df['Pile Local Axis (Deg)'] = 0
    return df


def _process_pile_shp_data(df):
    df['Pile Type'] = 'SHP'
    return df


def _process_pile_dhp_data(df):
    df['Pile Type'] = 'DHP'
    if 'Sleeve Length (m)' not in df.columns:
        df['Sleeve Length (m)'] = 0
    return df


def _process_pile_mp_data(df):
    df['Pile Type'] = 'MP'
    df['Pile Local Axis (Deg)'] = 0
    return df


def read_input_geometry(excel_inputs, file_paths):
    path_excel_input = file_paths.ExcelGeometry
    geometry_attributes = [
        'SiteBoundary','Borehole', 'BP', 'SHP', 'DHP', 'MP', 'Pile',
        'Point', 'Slab', 'Beam', 'Wall', 'Column',
        'PointLoad', 'LineLoad', 'LKP', 'Ru'
    ]
    header_single = [0]

    try:
        _load_sheet_data(path_excel_input, 'SiteBoundary', excel_inputs, 'SiteBoundary', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Borehole', excel_inputs, 'Borehole', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PileBP', excel_inputs, 'BP', _process_pile_bp_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PileSHP', excel_inputs, 'SHP', _process_pile_shp_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PileDHP', excel_inputs, 'DHP', _process_pile_dhp_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PileMP', excel_inputs, 'MP', _process_pile_mp_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Point', excel_inputs, 'Point', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Beam', excel_inputs, 'Beam', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Column', excel_inputs, 'Column', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Wall', excel_inputs, 'Wall', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Slab', excel_inputs, 'Slab', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Opening', excel_inputs, 'Opening', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PointLoad', excel_inputs, 'PointLoad', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'LineLoad', excel_inputs, 'LineLoad', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'LKP', excel_inputs, 'LKP', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Ru', excel_inputs, 'Ru', _identity_process,
                         header=header_single)

        # Consolidate Pile data
        all_pile_dfs = []
        for pile_attr in ['BP', 'SHP', 'DHP', 'MP']:
            df = getattr(excel_inputs, pile_attr, pd.DataFrame())
            if isinstance(df, pd.DataFrame) and not df.empty:
                all_pile_dfs.append(df)

        if all_pile_dfs:
            df_pile = pd.concat(all_pile_dfs, ignore_index=True)

            if 'Y (m)' in df_pile.columns:
                y_col_idx = df_pile.columns.get_loc('Y (m)')                # Ensure the column index is not out of bounds if 'Y (m)' is the last column
                # df_pile.insert expects to insert at an index, if index is len(columns), it appends
                df_pile.insert(min(y_col_idx + 1, len(df_pile.columns)), 'Z (m)', 0)
            elif not df_pile.empty:
                df_pile['Z (m)'] = 0

            if 'Pile Local Axis (Deg)' not in df_pile.columns and not df_pile.empty:
                df_pile['Pile Local Axis (Deg)'] = np.nan

            if 'Pile Local Axis (Deg)' in df_pile.columns:
                df_pile['Pile Local Axis (Deg)'] = df_pile['Pile Local Axis (Deg)'].fillna(0)

            # Replace empty strings with NaN - using explicit approach to avoid future warnings
            df_pile = df_pile.replace(r'^\s*$', np.nan, regex=True)
            excel_inputs.Pile = df_pile
        else:
            excel_inputs.Pile = pd.DataFrame()

    except FileNotFoundError:
        print(
            f"Halting geometry reading: Excel file at '{path_excel_input}' not found. All geometry attributes will be empty.")
        for attr_name in geometry_attributes:
            setattr(excel_inputs, attr_name, pd.DataFrame())
        # Ensure individual pile components are also empty if they were attributes (already covered by geometry_attributes)
        return excel_inputs

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Read Excel Input (Geometry)!')
    return excel_inputs
