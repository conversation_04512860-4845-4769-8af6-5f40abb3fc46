﻿"""
Visualization Utilities

Helper functions for visualization operations including safe data access
and site boundary extraction.
"""

from typing import Any, Optional, Dict, List, Tuple, Callable
from shapely.geometry import Polygon, Point
import ezdxf
from ezdxf import colors
from ezdxf.math import Vec3
import numpy as np

from ..data_types import PileGroupResult
from ..utils.geometry_utils import _create_continuous_centerline
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def safe_get(obj, key, default=None, log_callback: Optional[Callable] = None):
    """
    Safely get a value from an object, handling both dictionary and object attribute access.
    
    This function provides a unified interface for accessing data regardless of whether
    it's stored as dictionary keys or object attributes.
    
    Args:
        obj: The object to get the value from (dict or object with attributes)
        key: The key/attribute name to access
        default: Value to return if key is not found
        log_callback: Optional callback for logging data access operations
        
    Returns:
        The value if found, otherwise the default value
        
    Raises:
        ValueError: If obj is None or key is invalid
    """
    log_function_entry(log_callback, "safe_get", 
                      obj_type=type(obj).__name__, key=key, default=default)
    
    with create_timed_logger(log_callback, "safe_data_access"):
        try:
            enhanced_log(log_callback, f"Accessing key '{key}' from {type(obj).__name__}", 'DEBUG')
            
            if obj is None:
                enhanced_log(log_callback, f"Object is None, returning default: {default}", 'DEBUG')
                log_validation_result(log_callback, "Object Existence", False, "Object is None")
                log_function_exit(log_callback, "safe_get", default)
                return default
            
            log_validation_result(log_callback, "Object Existence", True, f"Object type: {type(obj).__name__}")
            
            if not isinstance(key, str) or not key:
                error_msg = "Key must be a non-empty string"
                enhanced_log(log_callback, f"Invalid key: {key} (type: {type(key)})", 'ERROR')
                log_validation_result(log_callback, "Key Validity", False, error_msg)
                log_error_with_context(log_callback, ValueError(error_msg), "safe_get key validation")
                raise ValueError(error_msg)
            
            log_validation_result(log_callback, "Key Validity", True, f"Valid key: '{key}'")
            log_constraint_check(log_callback, "Non-empty string key", key, "is string and non-empty", True)
            
            # Handle dictionary access
            if isinstance(obj, dict):
                enhanced_log(log_callback, f"Using dictionary access for key '{key}'", 'DEBUG')
                result = obj.get(key, default)
                
                if result is not default:
                    enhanced_log(log_callback, f"Found key '{key}' in dictionary", 'DEBUG')
                    log_calculation_result(log_callback, f"Dictionary_access_{key}", str(result) if result is not None else "None")
                else:
                    enhanced_log(log_callback, f"Key '{key}' not found in dictionary, using default", 'DEBUG')
                
                log_function_exit(log_callback, "safe_get", result)
                return result
            
            # Handle object attribute access
            enhanced_log(log_callback, f"Using attribute access for key '{key}'", 'DEBUG')
            try:
                result = getattr(obj, key, default)
                
                if result is not default:
                    enhanced_log(log_callback, f"Found attribute '{key}' in object", 'DEBUG')
                    log_calculation_result(log_callback, f"Attribute_access_{key}", str(result) if result is not None else "None")
                else:
                    enhanced_log(log_callback, f"Attribute '{key}' not found in object, using default", 'DEBUG')
                
                log_function_exit(log_callback, "safe_get", result)
                return result
                
            except AttributeError as e:
                enhanced_log(log_callback, f"AttributeError accessing '{key}': {e}, using default", 'DEBUG')
                log_error_with_context(log_callback, e, f"safe_get attribute access {key}")
                log_function_exit(log_callback, "safe_get", default)
                return default
                
        except ValueError:
            # Re-raise ValueError as-is (for invalid key)
            log_function_exit(log_callback, "safe_get", "ValueError raised")
            raise
        except Exception as e:
            enhanced_log(log_callback, f"Unexpected error in safe_get: {str(e)}", 'ERROR')
            log_error_with_context(log_callback, e, "safe_get unexpected error")
            log_function_exit(log_callback, "safe_get", default)
            return default


def extract_site_boundary_from_excel(excel_inputs: Any, 
                                    log_callback: Optional[Callable] = None) -> Optional[Polygon]:
    """
    Extract site boundary from Excel inputs SiteBoundary data.
    
    Args:
        excel_inputs: Excel inputs object containing SiteBoundary dataframe with 'X (m)' and 'Y (m)' columns
        log_callback: Optional callback for logging site boundary extraction progress
        
    Returns:
        Shapely Polygon representing the site boundary, or None if extraction fails
    """
    log_function_entry(log_callback, "extract_site_boundary_from_excel", 
                      excel_inputs_type=type(excel_inputs).__name__)
    
    with create_timed_logger(log_callback, "site_boundary_extraction"):
        try:
            enhanced_log(log_callback, "Starting site boundary extraction from Excel inputs", 'INFO')
            
            # Validate excel_inputs existence
            if not excel_inputs or not hasattr(excel_inputs, 'SiteBoundary'):
                error_msg = "Excel inputs missing SiteBoundary data"
                enhanced_log(log_callback, error_msg, 'WARNING')
                log_validation_result(log_callback, "Excel Inputs SiteBoundary", False, error_msg)
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(" Excel inputs missing SiteBoundary data")
                    
                log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
                return None
            
            log_validation_result(log_callback, "Excel Inputs SiteBoundary", True, "SiteBoundary attribute found")
            enhanced_log(log_callback, "Excel inputs contain SiteBoundary data", 'DEBUG')
            
            # Extract and validate dataframe
            df_boundary = excel_inputs.SiteBoundary.copy()
            enhanced_log(log_callback, f"Copied SiteBoundary dataframe with {len(df_boundary)} rows", 'DEBUG')
            log_calculation_result(log_callback, "SiteBoundary_dataframe_rows", str(len(df_boundary)))
            
            if df_boundary.empty:
                error_msg = "SiteBoundary dataframe is empty"
                enhanced_log(log_callback, error_msg, 'WARNING')
                log_validation_result(log_callback, "SiteBoundary DataFrame Empty Check", False, error_msg)
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(" SiteBoundary dataframe is empty")
                    
                log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
                return None
            
            log_validation_result(log_callback, "SiteBoundary DataFrame Empty Check", True, 
                                f"{len(df_boundary)} rows available")
            
            # Check required columns
            required_columns = ['X (m)', 'Y (m)']
            enhanced_log(log_callback, f"Checking for required columns: {required_columns}", 'DEBUG')
            missing_columns = [col for col in required_columns if col not in df_boundary.columns]
            
            if missing_columns:
                error_msg = f"SiteBoundary dataframe missing columns: {missing_columns}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Required Columns Present", False, error_msg)
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f" SiteBoundary dataframe missing columns: {missing_columns}")
                    
                log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
                return None
            
            log_validation_result(log_callback, "Required Columns Present", True, 
                                f"All required columns found: {required_columns}")
            enhanced_log(log_callback, f"All required columns present: {list(df_boundary.columns)}", 'DEBUG')
            
            # Extract coordinates
            coords = []
            invalid_coords_count = 0
            
            enhanced_log(log_callback, f"Extracting coordinates from {len(df_boundary)} data points", 'INFO')
            
            # Maintain backward compatibility
            if log_callback:
                log_callback(f" Extracting site boundary from {len(df_boundary)} coordinate points")
            
            for idx, row in df_boundary.iterrows():
                try:                
                    x = float(row['X (m)'])
                    y = float(row['Y (m)'])
                    coords.append((x, y))
                    enhanced_log(log_callback, f"Extracted coordinate {len(coords)}: ({x:.3f}, {y:.3f})", 'DEBUG')
                    
                except (ValueError, TypeError) as e:
                    invalid_coords_count += 1
                    error_msg = f"Invalid coordinates at row {idx}: {e}"
                    enhanced_log(log_callback, error_msg, 'WARNING')
                    log_error_with_context(log_callback, e, f"coordinate extraction row {idx}")
                    
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback(f" Invalid coordinates at row {idx}: {e}")
                    continue
            
            # Log coordinate extraction results
            valid_coords_count = len(coords)
            log_performance_metric(log_callback, "Valid_coordinates_extracted", valid_coords_count, "coordinates")
            log_performance_metric(log_callback, "Invalid_coordinates_skipped", invalid_coords_count, "coordinates")
            log_calculation_result(log_callback, "Total_coordinates_processed", str(len(df_boundary)))
            
            enhanced_log(log_callback, f"Coordinate extraction complete: {valid_coords_count} valid, {invalid_coords_count} invalid", 'INFO')
            
            # Validate sufficient coordinates for polygon
            if len(coords) >= 3:
                log_constraint_check(log_callback, "Sufficient Coordinates for Polygon", len(coords), ">= 3", True)
                enhanced_log(log_callback, f"Sufficient coordinates ({len(coords)}) for polygon creation", 'DEBUG')
                
                # Create polygon
                enhanced_log(log_callback, "Creating site boundary polygon from coordinates", 'DEBUG')
                site_polygon = Polygon(coords)
                
                # Validate and fix polygon if needed
                if not site_polygon.is_valid:
                    enhanced_log(log_callback, "Site boundary polygon is invalid, attempting to fix...", 'WARNING')
                    log_validation_result(log_callback, "Initial Polygon Validity", False, "Polygon is invalid")
                    
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback(" Site boundary polygon is invalid, attempting to fix...")
                    
                    # Attempt to fix the polygon
                    site_polygon = site_polygon.buffer(0)
                    enhanced_log(log_callback, f"Applied buffer(0) fix, polygon now valid: {site_polygon.is_valid}", 'INFO')
                else:
                    log_validation_result(log_callback, "Initial Polygon Validity", True, "Polygon is valid")
                    enhanced_log(log_callback, "Site boundary polygon is initially valid", 'DEBUG')
                
                # Final validation
                if site_polygon.is_valid and not site_polygon.is_empty:
                    area = site_polygon.area
                    bounds = site_polygon.bounds
                    
                    log_calculation_result(log_callback, "Site_boundary_area", f"{area:.2f}")
                    log_calculation_result(log_callback, "Site_boundary_bounds", str(bounds))
                    log_validation_result(log_callback, "Final Polygon Validation", True, 
                                        f"Valid polygon with area {area:.2f} m²")
                    
                    enhanced_log(log_callback, f"Site boundary extraction SUCCESSFUL: Area = {area:.2f} m², Bounds = {bounds}", 'INFO')
                    
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback(f"Site boundary extracted: Area = {area:.2f} m²")
                        
                    log_function_exit(log_callback, "extract_site_boundary_from_excel", site_polygon)
                    return site_polygon
                    
                else:
                    error_msg = "Failed to create valid site boundary polygon"
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_validation_result(log_callback, "Final Polygon Validation", False, error_msg)
                    
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback("Failed to create valid site boundary polygon")
                        
                    log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
                    return None
                    
            else:
                error_msg = f"Insufficient points for site boundary: {len(coords)} (need at least 3)"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_constraint_check(log_callback, "Sufficient Coordinates for Polygon", len(coords), ">= 3", False)
                log_validation_result(log_callback, "Sufficient Coordinates", False, error_msg)
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"Insufficient points for site boundary: {len(coords)} (need at least 3)")
                    
                log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
                return None
                
        except Exception as e:
            error_msg = f"Error extracting site boundary from Excel: {e}"
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_error_with_context(log_callback, e, "extract_site_boundary_from_excel")
            
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"Error extracting site boundary from Excel: {e}")
                
            log_function_exit(log_callback, "extract_site_boundary_from_excel", None)
            return None


def get_text_position_outside_polygon(polygon: Polygon, centroid: tuple, 
                                     log_callback: Optional[Callable] = None) -> tuple:
    """
    Calculate text position outside the polygon boundary.
    
    Args:
        polygon: The polygon to position text outside of
        centroid: The centroid coordinates as (x, y) tuple
        log_callback: Optional callback for logging text positioning operations
        
    Returns:
        Tuple of (x, y) coordinates for text position outside polygon
        
    Raises:
        Exception: If text position calculation fails
    """
    log_function_entry(log_callback, "get_text_position_outside_polygon", 
                      polygon_type=type(polygon).__name__, centroid=centroid)
    
    with create_timed_logger(log_callback, "text_position_calculation"):
        try:
            from shapely.geometry import Point
            import numpy as np
            
            enhanced_log(log_callback, f"Calculating text position outside polygon for centroid {centroid}", 'DEBUG')
            
            # Validate inputs
            if not isinstance(polygon, Polygon):
                error_msg = f"Expected Polygon, got {type(polygon)}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Polygon Type", False, error_msg)
                raise TypeError(error_msg)
            
            log_validation_result(log_callback, "Polygon Type", True, "Valid Polygon provided")
            
            if not isinstance(centroid, (tuple, list)) or len(centroid) < 2:
                error_msg = f"Invalid centroid format: {centroid}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Centroid Format", False, error_msg)
                raise ValueError(error_msg)
            
            log_validation_result(log_callback, "Centroid Format", True, f"Valid centroid: {centroid}")
            
            center_point = Point(centroid)
            enhanced_log(log_callback, f"Created center point: {center_point}", 'DEBUG')
            
            # Get polygon bounds to determine offset direction
            bounds = polygon.bounds
            width = bounds[2] - bounds[0]
            height = bounds[3] - bounds[1]
            
            log_calculation_result(log_callback, "Polygon_width", f"{width:.3f}")
            log_calculation_result(log_callback, "Polygon_height", f"{height:.3f}")
            log_calculation_result(log_callback, "Polygon_bounds", str(bounds))
            
            enhanced_log(log_callback, f"Polygon dimensions: width={width:.3f}, height={height:.3f}", 'DEBUG')
            
            # Calculate offset distance (slightly larger than typical polygon size)
            offset_distance = max(width, height) * 0.3 + 0.2
            log_calculation_result(log_callback, "Offset_distance", f"{offset_distance:.3f}")
            enhanced_log(log_callback, f"Calculated offset distance: {offset_distance:.3f}", 'DEBUG')
            
            # Try multiple directions to find one that places text outside polygon
            angles = [0, 45, 90, 135, 180, 225, 270, 315]  # degrees
            enhanced_log(log_callback, f"Testing {len(angles)} angle directions: {angles}", 'DEBUG')
            log_performance_metric(log_callback, "Angles_to_test", len(angles), "angles")
            
            successful_positions = 0
            
            for i, angle in enumerate(angles):
                enhanced_log(log_callback, f"Testing angle {i+1}/{len(angles)}: {angle}°", 'DEBUG')
                
                rad = np.radians(angle)
                offset_x = offset_distance * np.cos(rad)
                offset_y = offset_distance * np.sin(rad)
                
                text_x = centroid[0] + offset_x
                text_y = centroid[1] + offset_y
                text_point = Point(text_x, text_y)
                
                enhanced_log(log_callback, f"Angle {angle}°: calculated position ({text_x:.3f}, {text_y:.3f})", 'DEBUG')
                
                # Check if this position is outside the polygon
                is_contained = polygon.contains(text_point)
                is_touching = polygon.touches(text_point)
                is_outside = not is_contained and not is_touching
                
                enhanced_log(log_callback, f"Angle {angle}°: contained={is_contained}, touching={is_touching}, outside={is_outside}", 'DEBUG')
                
                if is_outside:
                    successful_positions += 1
                    result_position = (text_x, text_y)
                    
                    enhanced_log(log_callback, f"Found suitable text position at angle {angle}°: ({text_x:.3f}, {text_y:.3f})", 'INFO')
                    log_calculation_result(log_callback, "Selected_text_position", f"({text_x:.3f}, {text_y:.3f})")
                    log_calculation_result(log_callback, "Selected_angle", f"{angle}°")
                    log_performance_metric(log_callback, "Angles_tested_before_success", i + 1, "angles")
                    
                    log_function_exit(log_callback, "get_text_position_outside_polygon", result_position)
                    return result_position
            
        except Exception as e:
            error_msg = f"Failed to calculate text position: {e}"
            enhanced_log(log_callback, f"Error in text position calculation: {str(e)}", 'ERROR')
            log_error_with_context(log_callback, e, "get_text_position_outside_polygon")
            
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"Error: Failed to calculate text position: {e}")
                
            log_function_exit(log_callback, "get_text_position_outside_polygon", "Exception raised")
            raise


def create_sharp_corner_wall_polygon(group_data: Dict[str, Any], wall_polygons: Dict[str, Dict[str, Any]], 
                                    log_callback: Optional[Callable] = None) -> Optional[Polygon]:
    """
    Create a wall polygon with sharp corners by extending and joining individual wall segments.
    
    Args:
        group_data: Dictionary containing wall group data with 'wall_names' key
        wall_polygons: Dictionary mapping wall names to wall polygon data
        log_callback: Optional callback for logging wall polygon creation progress
        
    Returns:
        Shapely Polygon with sharp corners, or None if creation fails
        
    Raises:
        Exception: If polygon creation fails
    """
    log_function_entry(log_callback, "create_sharp_corner_wall_polygon", 
                      group_data_keys=list(group_data.keys()) if isinstance(group_data, dict) else "invalid",
                      wall_polygons_count=len(wall_polygons) if wall_polygons else 0)
    
    with create_timed_logger(log_callback, "sharp_corner_wall_polygon_creation"):
        try:
            from shapely.ops import unary_union
            from shapely.geometry import LineString, Polygon as ShapelyPolygon
            
            enhanced_log(log_callback, "Starting sharp corner wall polygon creation", 'INFO')
            
            # Validate group_data
            if not isinstance(group_data, dict) or 'wall_names' not in group_data:
                error_msg = "Invalid group_data: missing 'wall_names' key"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Group Data Validity", False, error_msg)
                raise ValueError(error_msg)
            
            log_validation_result(log_callback, "Group Data Validity", True, "Valid group_data with wall_names")
            
            wall_names_in_group = group_data['wall_names']
            if not isinstance(wall_names_in_group, (list, tuple)):
                error_msg = f"wall_names must be a list or tuple, got {type(wall_names_in_group)}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Wall Names Type", False, error_msg)
                raise ValueError(error_msg)
            
            log_validation_result(log_callback, "Wall Names Type", True, f"Valid wall_names list with {len(wall_names_in_group)} walls")
            enhanced_log(log_callback, f"Processing {len(wall_names_in_group)} walls: {wall_names_in_group}", 'INFO')
            log_calculation_result(log_callback, "Walls_in_group", str(len(wall_names_in_group)))
            
            # Handle single wall case
            if len(wall_names_in_group) == 1:
                wall_name = wall_names_in_group[0]
                enhanced_log(log_callback, f"Single wall case: {wall_name}", 'DEBUG')
                
                if wall_name in wall_polygons:
                    result_polygon = wall_polygons[wall_name]['polygon']
                    enhanced_log(log_callback, f"Returning existing polygon for single wall: {wall_name}", 'INFO')
                    log_calculation_result(log_callback, "Single_wall_polygon_area", f"{result_polygon.area:.3f}" if hasattr(result_polygon, 'area') else "unknown")
                    log_function_exit(log_callback, "create_sharp_corner_wall_polygon", result_polygon)
                    return result_polygon
                else:
                    error_msg = f"Wall '{wall_name}' not found in wall_polygons"
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_validation_result(log_callback, f"Wall {wall_name} Exists", False, error_msg)
                    log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                    return None
            
            # Multi-wall case: collect centerlines and thickness
            enhanced_log(log_callback, f"Multi-wall case: processing {len(wall_names_in_group)} walls", 'DEBUG')
            centerlines = []
            thicknesses = []
            missing_walls = []
            
            for i, wall_name in enumerate(wall_names_in_group):
                enhanced_log(log_callback, f"Processing wall {i+1}/{len(wall_names_in_group)}: {wall_name}", 'DEBUG')
                
                if wall_name in wall_polygons:
                    wall_data = wall_polygons[wall_name]
                    
                    # Extract centerline
                    if 'centerline' in wall_data:
                        centerlines.append(wall_data['centerline'])
                        enhanced_log(log_callback, f"Wall {wall_name}: centerline extracted", 'DEBUG')
                    else:
                        enhanced_log(log_callback, f"Wall {wall_name}: missing centerline data", 'WARNING')
                    
                    # Extract thickness
                    if 'thickness_m' in wall_data:
                        thickness = wall_data['thickness_m']
                        thicknesses.append(thickness)
                        enhanced_log(log_callback, f"Wall {wall_name}: thickness = {thickness:.3f} m", 'DEBUG')
                        log_calculation_result(log_callback, f"Wall_{wall_name}_thickness", f"{thickness:.3f}")
                    else:
                        enhanced_log(log_callback, f"Wall {wall_name}: missing thickness_m data", 'WARNING')
                else:
                    missing_walls.append(wall_name)
                    enhanced_log(log_callback, f"Wall {wall_name}: not found in wall_polygons", 'WARNING')
            
            # Log collection results
            log_performance_metric(log_callback, "Centerlines_collected", len(centerlines), "centerlines")
            log_performance_metric(log_callback, "Thicknesses_collected", len(thicknesses), "thicknesses")
            log_performance_metric(log_callback, "Missing_walls", len(missing_walls), "walls")
            
            if missing_walls:
                enhanced_log(log_callback, f"Missing walls in wall_polygons: {missing_walls}", 'WARNING')
            
            if not centerlines:
                error_msg = "No centerlines collected from wall data"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Centerlines Available", False, error_msg)
                log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                return None
            
            log_validation_result(log_callback, "Centerlines Available", True, f"{len(centerlines)} centerlines collected")
            
            if not thicknesses:
                error_msg = "No thickness data collected from wall data"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Thickness Data Available", False, error_msg)
                log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                return None
            
            log_validation_result(log_callback, "Thickness Data Available", True, f"{len(thicknesses)} thickness values collected")
            
            # Create continuous centerline
            enhanced_log(log_callback, f"Creating continuous centerline from {len(centerlines)} segments", 'DEBUG')
            continuous_centerline = _create_continuous_centerline(centerlines)
            
            if continuous_centerline is None:
                error_msg = "Failed to create continuous centerline"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Continuous Centerline Creation", False, error_msg)
                log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                return None
            
            log_validation_result(log_callback, "Continuous Centerline Creation", True, "Successfully created continuous centerline")
            enhanced_log(log_callback, "Continuous centerline created successfully", 'DEBUG')
            
            # Calculate average thickness
            avg_thickness = sum(thicknesses) / len(thicknesses)
            min_thickness = min(thicknesses)
            max_thickness = max(thicknesses)
            
            log_calculation_result(log_callback, "Average_thickness", f"{avg_thickness:.3f}")
            log_calculation_result(log_callback, "Min_thickness", f"{min_thickness:.3f}")
            log_calculation_result(log_callback, "Max_thickness", f"{max_thickness:.3f}")
            
            enhanced_log(log_callback, f"Thickness statistics: avg={avg_thickness:.3f}m, min={min_thickness:.3f}m, max={max_thickness:.3f}m", 'DEBUG')
            
            # Create polygon with sharp corners (using join_style=2 for mitered joins)
            enhanced_log(log_callback, "Creating polygon with sharp corners using mitered joins", 'DEBUG')
            buffer_distance = avg_thickness / 2.0
            log_calculation_result(log_callback, "Buffer_distance", f"{buffer_distance:.3f}")
            
            sharp_polygon = continuous_centerline.buffer(
                buffer_distance, 
                cap_style=2,  # Flat cap
                join_style=2  # Mitered join for sharp corners
            )
            
            # Validate result polygon
            if sharp_polygon is None:
                error_msg = "Buffer operation returned None"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Sharp Polygon Creation", False, error_msg)
                log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                return None
            
            if sharp_polygon.is_empty:
                error_msg = "Created polygon is empty"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Sharp Polygon Non-Empty", False, error_msg)
                log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                return None
            
            if not sharp_polygon.is_valid:
                enhanced_log(log_callback, "Created polygon is invalid, attempting to fix...", 'WARNING')
                sharp_polygon = sharp_polygon.buffer(0)
                
                if not sharp_polygon.is_valid:
                    error_msg = "Failed to fix invalid polygon"
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    log_validation_result(log_callback, "Sharp Polygon Validity", False, error_msg)
                    log_function_exit(log_callback, "create_sharp_corner_wall_polygon", None)
                    return None
                else:
                    enhanced_log(log_callback, "Successfully fixed invalid polygon", 'INFO')
            
            # Log final polygon properties
            polygon_area = sharp_polygon.area
            polygon_bounds = sharp_polygon.bounds
            
            log_calculation_result(log_callback, "Sharp_polygon_area", f"{polygon_area:.3f}")
            log_calculation_result(log_callback, "Sharp_polygon_bounds", str(polygon_bounds))
            log_validation_result(log_callback, "Sharp Polygon Creation", True, 
                                f"Successfully created polygon with area {polygon_area:.3f} m²")
            
            enhanced_log(log_callback, f"Sharp corner wall polygon creation SUCCESSFUL: Area = {polygon_area:.3f} m²", 'INFO')
            
            log_function_exit(log_callback, "create_sharp_corner_wall_polygon", sharp_polygon)
            return sharp_polygon
            
        except Exception as e:
            error_msg = f"Failed to create sharp corner polygon: {e}"
            enhanced_log(log_callback, f"Error in sharp corner wall polygon creation: {str(e)}", 'ERROR')
            log_error_with_context(log_callback, e, "create_sharp_corner_wall_polygon")
            
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"Error: Failed to create sharp corner polygon: {e}")
                
            log_function_exit(log_callback, "create_sharp_corner_wall_polygon", "Exception raised")
            raise

