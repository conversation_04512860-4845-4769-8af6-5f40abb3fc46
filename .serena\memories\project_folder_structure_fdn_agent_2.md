# Foundation Agent Advanced Systems (`fdn_agent/pile_estimation/` continued)

## 🏗️ Layout Generation (`layout_generation/`)
```
layout_generation/
├── 📄 case_1_layouts.py         # Point loads & isolated footings
├── 📄 case_2_layouts.py         # Wall foundations & linear structures
├── 📄 case_4_layouts.py         # Complex geometries & constraints
├── 📄 genetic_fitness.py        # Genetic algorithm optimization
├── 📄 layout_common.py          # Common layout functions
├── 📄 layout_engine.py          # Main layout generation engine
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 📐 Load Calculator (`load_calculator/`)
```
load_calculator/
├── 📄 basic_calculations.py     # Basic load calculations
├── 📄 centroid_calculations.py  # Load centroid calculations
├── 📄 pile_calculations.py      # Pile requirement calculations
├── 📄 utils.py                  # Load calculation utilities
├── 📄 README.md                 # Load calculator documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🎯 Multi-Objective Optimization (`optimization/`)
```
optimization/
├── 📄 enhanced_nsga3_optimizer.py # Enhanced NSGA-III implementation
├── 📄 nsga3_optimizer.py        # Standard DEAP-based NSGA-III
├── 📄 README.md                 # Optimization documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🏛️ Pile Cap Geometry (`pile_cap_geometry/`)
```
pile_cap_geometry/
├── 📄 pile_cap_geometry.py      # Pile cap creation algorithms
├── 📄 README.md                 # Pile cap documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🧠 Intelligent Pile Selection (`pile_type_selection/`)
```
pile_type_selection/
├── 📄 core.py                   # Integrated estimation engine
├── 📄 pile_type_preselection.py # Advanced preselection algorithms
├── 📄 utils.py                  # Selection utility functions
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🛠️ Utilities System (`utils/`)
```
utils/
├── 📄 coordinate_utils.py       # Coordinate system utilities
├── 📄 excel_processor.py        # Enhanced Excel processing
├── 📄 geometry_utils.py         # Geometric utility functions
├── 📄 layout_utils.py           # Layout utility functions
├── 📄 logging_utils.py          # Advanced logging utilities
├── 📄 math_utils.py             # Mathematical utility functions
├── 📄 pile_geometry_utils.py    # Pile geometry utilities
├── 📄 pile_preselection_utils.py # Preselection utilities
├── 📄 validation_utils.py       # Comprehensive validation
├── 📄 README.md                 # Utils documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🎨 Visualization System (`visualizer/`)
```
visualizer/
├── 📄 core.py                   # Main DXF creation engine
├── 📄 analysis_plotters.py      # Analysis visualization
├── 📄 base_plotters.py          # Base plotting functions
├── 📄 dimension_plotters.py     # Dimension plotting
├── 📄 dxf_setup.py              # DXF setup and configuration
├── 📄 pile_drawer.py            # Pile drawing functions
├── 📄 pile_plotters.py          # Specialized pile plotting
├── 📄 plotters.py               # General plotting interface
├── 📄 structure_plotters.py     # Structure visualization
├── 📄 text_manager.py           # Text placement management
├── 📄 utils.py                  # Visualization utilities
├── 📄 validation.py             # Visualization validation
├── 📄 README.md                 # Visualization documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```