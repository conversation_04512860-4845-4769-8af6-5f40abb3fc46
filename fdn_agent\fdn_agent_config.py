# filepath: c:\Users\<USER>\CursorProjects\Foundation-Automation\build_fem\build_fem_config.py

"""
Configuration file for build_fem module.
Contains file path constants and Excel sheet name constants.
"""

# =============================================================================
# FILE NAME CONSTANTS
# =============================================================================

# Input Excel Files
EXCEL_PROPERTY_FILENAME = 'A.SAFEInput_Property.xlsx'
EXCEL_GEOMETRY_FILENAME = 'A.SAFEInput_Geometry.xlsx'
EXCEL_GEOLOGY_FILENAME = 'A.SAFEInput_Geology.xlsx'
EXCEL_LOADING_FILENAME = 'A.SAFEInput_Loading.xlsx'

# Output Excel Files
EXCEL_SAFE16_MODEL_FILENAME = 'B.SAFE16_Model.xlsx'
EXCEL_SAFE22_MODEL_FILENAME = 'B.SAFE22_Model.xlsx'

# Output F2K Files
F2K_SAFE16_MODEL_FILENAME = 'B.SAFE16_Model.f2k'
F2K_SAFE22_MODEL_FILENAME = 'B.SAFE22_Model.f2k'

# Output CSV Files
EXCEL_OUTPUT_MP_FILENAME = 'MiniPile_Spring.csv'

# Output Loading Files
EXCEL_OUTPUT_LOADING_FILENAME = 'Output_Loading.xlsx'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - PROPERTY
# =============================================================================

SHEET_CONCRETE = 'Concrete'
SHEET_STEEL = 'Steel'
SHEET_REBAR = 'Rebar'
SHEET_TENDON = 'Tendon'
SHEET_BEAM_PROP = 'Beam Prop'
SHEET_BP_PROP = 'BP Prop'
SHEET_HP_PROP = 'HP Prop'
SHEET_SLAB_PROP = 'Slab Prop'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - GEOMETRY
# =============================================================================

SHEET_BOREHOLE = 'Borehole'
SHEET_PILE_BP = 'PileBP'
SHEET_PILE_SHP = 'PileSHP'
SHEET_PILE_DHP = 'PileDHP'
SHEET_PILE_MP = 'PileMP'
SHEET_POINT = 'Point'
SHEET_BEAM = 'Beam'
SHEET_COLUMN = 'Column'
SHEET_WALL = 'Wall'
SHEET_SLAB = 'Slab'
SHEET_POINT_LOAD = 'PointLoad'
SHEET_LINE_LOAD = 'LineLoad'
SHEET_LKP = 'LKP'
SHEET_RU = 'Ru'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - GEOLOGY
# =============================================================================

SHEET_BOREHOLE_SPT = 'BoreholeSPT'
SHEET_PILE_SOIL_SPRING_SETTING = 'PileSoilSpringSetting'
SHEET_LATERAL_SOIL_SPRING = 'LateralSoilSpring'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - LOADING
# =============================================================================

SHEET_LOAD_PAT = 'LoadPat'
SHEET_LOAD_CASE = 'LoadCase'
SHEET_LOAD_COMB = 'LoadComb'

# Input Load Sheets
SHEET_INPUT_LOAD_POINT_LOAD = 'InputLoad_PointLoad'
SHEET_INPUT_LOAD_LINE_LOAD = 'InputLoad_LineLoad'
SHEET_INPUT_LOAD_PILE = 'InputLoad_Pile'
SHEET_INPUT_LOAD_LKP = 'InputLoad_LKP'
SHEET_INPUT_LOAD_SLAB = 'InputLoad_Slab'
SHEET_INPUT_LOAD_BEAM = 'InputLoad_Beam'
SHEET_INPUT_LOAD_COLUMN = 'InputLoad_Column'
SHEET_INPUT_LOAD_WALL = 'InputLoad_Wall'
SHEET_INPUT_LOAD_CORE_WALL = 'InputLoad_CoreWall'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - OUTPUT LOADING
# =============================================================================

SHEET_OUTPUT_LOAD_POINT_LOAD = 'OutputLoad_PointLoad'
SHEET_OUTPUT_LOAD_LINE_LOAD = 'OutputLoad_LineLoad'
SHEET_OUTPUT_LOAD_BEAM = 'OutputLoad_Beam'
SHEET_OUTPUT_LOAD_COLUMN = 'OutputLoad_Column'
SHEET_OUTPUT_LOAD_WALL = 'OutputLoad_Wall'
SHEET_OUTPUT_LOAD_CORE_WALL = 'OutputLoad_CoreWall'

# =============================================================================
# FOLDER NAME CONSTANTS
# =============================================================================

OUTPUT_FOLDER = 'Output'
ANALYTICAL_RESULTS_FOLDER = 'Analytical_Results'
ULS_FOLDER = 'ULS'
SLS_FOLDER = 'SLS'