"""SAFE 22 Load Output Processing Module.

Processes and transforms structural loads for SAFE 22 finite element analysis software."""

from typing import Any, Tuple
import pandas as pd
from build_fem import functions


def _write_point_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """Process point load data and write to Excel output format for SAFE 22 analysis.
    Calculates additional moments due to pile cap thickness effects."""
    df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()

    # Extract load case column names (exclude 'Point Data' metadata columns)
    load_cases = [col for col in df_input_load_point.columns.get_level_values(0).unique()
                  if 'Point Data' not in col]

    for i, df_row in df_input_load_point.iterrows():
        # Extract point identification data
        name = df_row[('Point Data', 'Point Load')]
        center = df_row[('Point Data', 'Point (Text)')]
        t = df_row[('Point Data', 'Cap Thickness (m)')]  # Pile cap thickness

        # Save identification data to design record
        excel_outputs.PointLoad.loc[i, ('Point Data', 'Point Load')] = name
        excel_outputs.PointLoad.loc[i, ('Point Data', 'Point (Text)')] = center
        excel_outputs.PointLoad.loc[i, ('Point Data', 'Cap Thickness (m)')] = t

        # Process each load case for this point
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components
            v_x = load_data['Vx (kN)']      # Shear force in X direction
            v_y = load_data['Vy (kN)']      # Shear force in Y direction
            f_z = load_data['Axial (kN)']   # Axial force in Z direction
            m_x = load_data['Mx (kNm)']     # Moment about X axis
            m_y = load_data['My (kNm)']     # Moment about Y axis
            m_z = load_data['Mz (kNm)']     # Moment about Z axis (torsion)

            # Calculate additional moments due to pile cap shear effects
            # These account for the eccentricity when loads are applied at cap top
            m_x_add = -v_y * t  # Additional moment about X due to Y shear
            m_y_add = v_x * t   # Additional moment about Y due to X shear

            # Save all load data to design record
            excel_outputs.PointLoad.loc[i, (load_case, 'Vx (kN)')] = v_x
            excel_outputs.PointLoad.loc[i, (load_case, 'Vy (kN)')] = v_y
            excel_outputs.PointLoad.loc[i, (load_case, 'Fz (kN)')] = f_z
            excel_outputs.PointLoad.loc[i, (load_case, 'Mx (kNm)')] = m_x
            excel_outputs.PointLoad.loc[i, (load_case, 'My (kNm)')] = m_y
            excel_outputs.PointLoad.loc[i, (load_case, 'Mz (kNm)')] = m_z
            excel_outputs.PointLoad.loc[i, (load_case, 'Mx_Add (kNm)')] = m_x_add
            excel_outputs.PointLoad.loc[i, (load_case, 'My_Add (kNm)')] = m_y_add

    return excel_outputs


def _write_line_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """
    Process line load data and write to Excel output format for SAFE 22 analysis.

    This function processes distributed loads applied along linear elements, performing
    local coordinate transformations and calculating additional effects due to pile cap
    thickness. Line loads are specified in local coordinates (1-3 system) and include
    distributed forces and moments along the element length.
    """
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()

    # Calculate line geometry data (length and orientation angle)
    df_line_length_data = functions.cal_line_length(excel_inputs)
    df_lineload = excel_inputs.LineLoad.copy()

    # Extract load case column names (exclude 'Line Data' metadata columns)
    all_columns = df_input_load_line.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Line Data' not in col]

    for i, df_row in df_input_load_line.iterrows():
        name = df_row[('Line Data', 'Line Load')]

        # Look up geometric properties for this line load
        condition_length = df_line_length_data['Line Load'] == name
        length = df_line_length_data.loc[condition_length, 'Line Length (m)'].item()
        deg = df_line_length_data.loc[condition_length, 'Line Theta (deg)'].item()
        t = df_row[('Line Data', 'Cap Thickness (m)')]  # Pile cap thickness

        # Save geometric data to design record
        excel_outputs.LineLoad.loc[i, ('Line Data', 'Line Load')] = name
        excel_outputs.LineLoad.loc[i, ('Line Data', 'Line Length (m)')] = length
        excel_outputs.LineLoad.loc[i, ('Line Data', 'Line Theta (deg)')] = deg
        excel_outputs.LineLoad.loc[i, ('Line Data', 'Cap Thickness (m)')] = t

        # Process each load case for this line element
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components in local coordinates
            v_1 = load_data['V1 (kN/m)']        # Distributed force along line (local 1-axis)
            v_3 = load_data['V3 (kN/m)']        # Distributed force perpendicular to line (local 3-axis)
            f_z = load_data['Axial (kN/m)']     # Distributed axial force (vertical)
            m_1_input = load_data['M1 (kNm/m)'] # Distributed moment about local 1-axis
            m_3_input = load_data['M3 (kNm/m)'] # Distributed moment about local 3-axis
            m_z = load_data['Mz (kNm/m)']       # Distributed torsional moment

            # Transform loads using local coordinate system and calculate additional effects
            # This function handles pile cap thickness effects and load distribution
            (m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add,
             s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add,
             s_m1_add, s_mz) = functions.transform_line_load_local(
                t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            # Save original load components to design record
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V1 (kN/m)')] = v_1
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V3 (kN/m)')] = v_3
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz (kN/m)')] = f_z
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1 (kNm/m)')] = m_1_input
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M3 (kNm/m)')] = m_3_input
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Mz (kNm/m)')] = m_z

            # Save transformed loads and additional effects
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_M3 (kN/m)')] = fz_m3
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1_Add (kNm)')] = m1_add
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M3_Add (kNm)')] = m3_add
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_M3_Add (kN/m)')] = fz_m3_add

            # Save start and end values for SAFE 22 distributed load application
            # These values account for moment distribution effects along the line
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V1_Start (kN/m)')] = s_1
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V1_End (kN/m)')] = s_1
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V3_Start (kN/m)')] = s_3
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'V3_End (kN/m)')] = s_3
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_Start (kN/m)')] = s_z_start
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_End (kN/m)')] = s_z_end
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1_Start (kNm/m)')] = s_m1
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1_End (kNm/m)')] = s_m1

            # Save additional load effects due to pile cap thickness
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_Add_Start (kN/m)')] = s_z_start_add
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'Fz_Add_End (kN/m)')] = s_z_end_add
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1_Add_Start (kNm/m)')] = s_m1_add
            excel_outputs.LineLoad.loc[i, (f"{load_case}", 'M1_Add_End (kNm/m)')] = s_m1_add

    return excel_outputs


def _write_beam_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """
    Process beam load data and write to Excel output format for SAFE 22 analysis.

    This function processes distributed loads applied to beam elements, which are
    structural frame members that carry loads primarily through bending. The function
    performs local coordinate transformations similar to line loads but specifically
    handles beam-specific properties and load distributions for SAFE 22 modeling.
    """
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()

    # Calculate beam geometry data (length and orientation angle)
    df_beam_length_data = functions.cal_beam_length(excel_inputs)

    # Extract load case column names (exclude 'Beam Data' metadata columns)
    all_columns = df_input_load_beam.columns.get_level_values(0).unique()
    load_cases = [col for col in all_columns if 'Beam Data' not in col]

    for i, df_row in df_input_load_beam.iterrows():
        name = df_row[('Beam Data', 'Beam Mark')]

        # Look up geometric properties for this beam
        condition_length = df_beam_length_data['Beam Mark'] == name
        length = df_beam_length_data.loc[condition_length, 'Beam Length (m)'].item()
        deg = df_beam_length_data.loc[condition_length, 'Beam Theta (deg)'].item()
        t = df_row[('Beam Data', 'Cap Thickness (m)')]  # Pile cap thickness

        # Save geometric data to design record
        excel_outputs.BeamLoad.loc[i, ('Beam Data', 'Beam Mark')] = name
        excel_outputs.BeamLoad.loc[i, ('Beam Data', 'Beam Length (m)')] = length
        excel_outputs.BeamLoad.loc[i, ('Beam Data', 'Beam Theta (deg)')] = deg
        excel_outputs.BeamLoad.loc[i, ('Beam Data', 'Cap Thickness (m)')] = t

        # Process each load case for this beam element
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components in local coordinates
            v_1 = load_data['V1 (kN/m)']        # Distributed force along beam (local 1-axis)
            v_3 = load_data['V3 (kN/m)']        # Distributed force perpendicular to beam (local 3-axis)
            f_z = load_data['Axial (kN/m)']     # Distributed axial force (vertical)
            m_1_input = load_data['M1 (kNm/m)'] # Distributed moment about local 1-axis
            m_3_input = load_data['M3 (kNm/m)'] # Distributed moment about local 3-axis
            m_z = load_data['Mz (kNm/m)']       # Distributed torsional moment

            # Transform loads using local coordinate system (same as line loads)
            # This function handles pile cap thickness effects and load distribution
            (m_1_transformed, m_3_transformed, fz_m3, m1_add, m3_add, fz_m3_add,
             s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add,
             s_m1_add, s_mz) = functions.transform_line_load_local(
                t, length, v_1, v_3, f_z, m_1_input, m_3_input, m_z)

            # Save original load components to design record
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V1 (kN/m)')] = v_1
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V3 (kN/m)')] = v_3
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz (kN/m)')] = f_z
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1 (kNm/m)')] = m_1_input
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M3 (kNm/m)')] = m_3_input
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Mz (kNm/m)')] = m_z

            # Save transformed loads and additional effects
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_M3 (kN/m)')] = fz_m3
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1_Add (kNm)')] = m1_add
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M3_Add (kNm)')] = m3_add
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_M3_Add (kN/m)')] = fz_m3_add

            # Save start and end values for SAFE 22 distributed load application
            # These values account for moment distribution effects along the beam
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V1_Start (kN/m)')] = s_1
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V1_End (kN/m)')] = s_1
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V3_Start (kN/m)')] = s_3
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'V3_End (kN/m)')] = s_3
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_Start (kN/m)')] = s_z_start
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_End (kN/m)')] = s_z_end
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1_Start (kNm/m)')] = s_m1
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1_End (kNm/m)')] = s_m1

            # Save additional load effects due to pile cap thickness
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_Add_Start (kN/m)')] = s_z_start_add
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'Fz_Add_End (kN/m)')] = s_z_end_add
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1_Add_Start (kNm/m)')] = s_m1_add
            excel_outputs.BeamLoad.loc[i, (f"{load_case}", 'M1_Add_End (kNm/m)')] = s_m1_add

    return excel_outputs


def _write_column_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """
    Process column load data and write to Excel output format for SAFE 22 analysis.

    This function processes loads applied to column elements, which can be treated
    as either point loads or area loads depending on the column configuration.
    The function calculates both force-based (kN) and pressure-based (kPa) values
    and includes additional moments due to pile cap thickness effects.
    """
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()

    for i, df_row in df_input_load_column.iterrows():
        # Extract column identification data
        name = df_row[('Column Data', 'Column Mark')]
        area = df_row[('Column Data', 'Area (m2)')]
        center = df_row[('Column Data', 'Center Point (Text)')]
        t = df_row[('Column Data', 'Cap Thickness (m)')]  # Pile cap thickness
        load_type = df_row[('Column Data', 'Area/Point Load (A/P)')]

        # Save identification data to design record
        excel_outputs.ColumnLoad.loc[i, ('Column Data', 'Column Mark')] = name
        excel_outputs.ColumnLoad.loc[i, ('Column Data', 'Area (m2)')] = area
        excel_outputs.ColumnLoad.loc[i, ('Column Data', 'Center Point (Text)')] = center
        excel_outputs.ColumnLoad.loc[i, ('Column Data', 'Cap Thickness (m)')] = t
        excel_outputs.ColumnLoad.loc[i, ('Column Data', 'Area/Point Load (A/P)')] = load_type

        # Extract load case column names (exclude 'Column Data' metadata columns)
        cols = df_input_load_column.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Column Data')
        load_cases = cols[condition]

        # Process each load case for this column
        for load_case in load_cases:
            load_data = df_row[load_case]

            # Extract load components in global coordinates
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            # Calculate additional moments due to pile cap shear effects
            # These account for the eccentricity when loads are applied at cap top
            m_x_add = 0 - v_y * t  # Additional moment about X due to Y shear
            m_y_add = 0 + v_x * t  # Additional moment about Y due to X shear

            # Save force-based load data to design record
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Vx (kN)')] = v_x
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Vy (kN)')] = v_y
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Fz (kN)')] = f_z
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Mx (kNm)')] = m_x
            excel_outputs.ColumnLoad.loc[i, (load_case, 'My (kNm)')] = m_y
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Mz (kNm)')] = m_z

            # Calculate and save pressure-based values (kPa) when area is available
            if area != 0:
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Vx (kPa)')] = v_x / area
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Vy (kPa)')] = v_y / area
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Fz (kPa)')] = f_z / area
            else:  # Handle case where area is 0 to avoid division by zero
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Vx (kPa)')] = 0
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Vy (kPa)')] = 0
                excel_outputs.ColumnLoad.loc[i, (load_case, 'Fz (kPa)')] = 0

            # Save additional moments due to pile cap effects
            excel_outputs.ColumnLoad.loc[i, (load_case, 'Mx_Add (kNm)')] = m_x_add
            excel_outputs.ColumnLoad.loc[i, (load_case, 'My_Add (kNm)')] = m_y_add

    return excel_outputs


def _write_wall_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """
    Process wall load data and write to Excel output format for SAFE 22 analysis.

    This function processes loads applied to wall elements, performing global coordinate
    transformations to convert from global X-Y coordinates to local wall coordinates
    (1-3 system). Wall loads include both concentrated loads and distributed effects
    along the wall length, with proper handling of pile cap thickness effects.
    """
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()

    # Calculate wall geometry data (length and orientation angle)
    df_wall_length = functions.cal_wall_length(excel_inputs)

    for i, df_row in df_input_load_wall.iterrows():
        name = df_row[('Wall Data', 'Wall Mark')]

        # Look up geometric properties for this wall
        condition = df_wall_length['Wall Name'] == name
        length = df_wall_length.loc[condition, 'Wall Length (m)'].values[0]
        deg = df_wall_length.loc[condition, 'Wall Theta (deg)'].values[0]
        t = df_row[('Wall Data', 'Cap Thickness (m)')]  # Pile cap thickness

        # Save geometric data to design record
        excel_outputs.WallLoad.loc[i, ('Wall Data', 'Wall Mark')] = name
        excel_outputs.WallLoad.loc[i, ('Wall Data', 'Wall Length (m)')] = length
        excel_outputs.WallLoad.loc[i, ('Wall Data', 'Wall Theta (deg)')] = deg
        excel_outputs.WallLoad.loc[i, ('Wall Data', 'Cap Thickness (m)')] = t

        # Extract load case column names (exclude 'Wall Data' metadata columns)
        cols = df_input_load_wall.columns.get_level_values(0).unique()
        condition = ~cols.str.contains('Wall Data')
        load_cases = cols[condition]

        # Process each load case for this wall element
        for load_case in load_cases:
            # Extract load data for this wall under the current load case
            load_data = df_row[load_case]
            v_x, v_y, f_z, m_x, m_y, m_z = load_data[
                ['Vx (kN)', 'Vy (kN)', 'Axial (kN)', 'Mx (kNm)', 'My (kNm)', 'Mz (kNm)']]

            # Transform loads from global to local coordinates and calculate additional effects
            # This function handles pile cap thickness effects and coordinate transformation
            (mx_add, my_add, m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add,
             s_x, s_y, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add,
             s_m1_add, s_mz) = functions.transform_wall_load_global(
                t, length, deg, v_x, v_y, f_z, m_x, m_y, m_z)

            # Organize all load data into a comprehensive record dictionary
            # This includes original loads, transformed loads, and distributed values
            record_data = {
                # Original load components in global coordinates
                'Vx (kN)': v_x,
                'Vy (kN)': v_y,
                'Fz (kN)': f_z,
                'Mx (kNm)': m_x,
                'My (kNm)': m_y,
                'Mz (kNm)': m_z,

                # Additional moments due to pile cap thickness effects
                'Mx_Add (kNm)': mx_add,
                'My_Add (kNm)': my_add,

                # Transformed loads in local wall coordinates
                'M1 (kNm)': m_1,      # Moment about local 1-axis (along wall)
                'M3 (kNm)': m_3,      # Moment about local 3-axis (perpendicular to wall)
                'Fz_M3 (kN/m)': fz_m3,  # Distributed load due to M3 moment

                # Additional effects in local coordinates
                'M1_Add (kNm)': m1_add,
                'M3_Add (kNm)': m3_add,
                'Fz_M3_Add (kN/m)': fz_m3_add,

                # Distributed load values for SAFE 22 application
                # Start and end values account for moment distribution effects
                'Vx_Start (kN/m)': s_x,
                'Vx_End (kN/m)': s_x,
                'Vy_Start (kN/m)': s_y,
                'Vy_End (kN/m)': s_y,
                'Fz_Start (kN/m)': s_z_start,
                'Fz_End (kN/m)': s_z_end,
                'M1_Start (kNm/m)': s_m1,
                'M1_End (kNm/m)': s_m1,

                # Additional distributed effects due to pile cap
                'Fz_Add_Start (kN/m)': s_z_start_add,
                'Fz_Add_End (kN/m)': s_z_end_add,
                'M1_Add_Start (kNm/m)': s_m1_add,
                'M1_Add_End (kNm/m)': s_m1_add
            }

            # Save the complete record data for this load case
            for key, value in record_data.items():
                excel_outputs.WallLoad.loc[i, (load_case, key)] = value

    return excel_outputs


def _write_corewall_load_output(excel_inputs: Any, excel_outputs: Any) -> Any:
    """
    Process core wall load data and write to Excel output format for SAFE 22 analysis.

    This function processes loads applied to core wall systems, which are assemblies
    of multiple wall elements that work together as a structural unit. Core walls
    typically consist of multiple individual wall segments that are grouped together
    for load distribution and analysis purposes. The function calculates both total
    loads and distributed loads per unit length for the entire core wall system.
    """
    df_input_load_corewall = excel_inputs.InputLoadCoreWall.fillna(0).copy()

    # Calculate individual wall lengths and then core wall total lengths
    df_wall_length_calc = functions.cal_wall_length(excel_inputs)
    df_corewall_length_calc = functions.cal_corewall_length(excel_inputs, df_wall_length_calc)

    # Create indexed DataFrame for faster core wall length lookups
    df_corewall_length_indexed = df_corewall_length_calc.set_index('CoreWall Name')

    for i, df_corewall_row in df_input_load_corewall.iterrows():
        corewall_name = df_corewall_row[('CoreWall Data', 'CoreWall Mark')]

        # Look up total length for this core wall system
        try:
            corewall_len = df_corewall_length_indexed.at[corewall_name, 'CoreWall Length (m)']
        except KeyError:
            print(f"Warning: Corewall '{corewall_name}' not found in length data. Skipping this corewall.")
            continue

        cap_thickness = df_corewall_row[('CoreWall Data', 'Cap Thickness (m)')]

        # Save identification data to design record
        excel_outputs.CoreWallLoad.loc[i, ('CoreWall Data', 'CoreWall Mark')] = corewall_name
        excel_outputs.CoreWallLoad.loc[i, ('CoreWall Data', 'CoreWall Length (m)')] = corewall_len
        excel_outputs.CoreWallLoad.loc[i, ('CoreWall Data', 'Cap Thickness (m)')] = cap_thickness

        # Extract load case column names (exclude 'CoreWall Data' metadata columns)
        all_level0_cols = df_input_load_corewall.columns.get_level_values(0).unique()
        load_cases_level0 = [col_l0 for col_l0 in all_level0_cols if col_l0 != 'CoreWall Data']

        # Process each load case for this core wall system
        for load_case_name in load_cases_level0:
            load_data_subset = df_corewall_row[load_case_name]

            # Extract load components with safe defaults for missing data
            v_x = load_data_subset.get('Vx (kN)', 0.0)      # Shear force in X direction
            v_y = load_data_subset.get('Vy (kN)', 0.0)      # Shear force in Y direction
            f_z = load_data_subset.get('Axial (kN)', 0.0)   # Axial force in Z direction
            m_x = load_data_subset.get('Mx (kNm)', 0.0)     # Moment about X axis
            m_y = load_data_subset.get('My (kNm)', 0.0)     # Moment about Y axis
            m_z = load_data_subset.get('Mz (kNm)', 0.0)     # Moment about Z axis (torsion)

            # Calculate additional moments due to pile cap shear effects
            mx_add = - (v_y * cap_thickness)  # Additional moment about X due to Y shear
            my_add = + (v_x * cap_thickness)  # Additional moment about Y due to X shear

            # Initialize distributed load values (per unit length)
            s_x, s_y, s_z, s_m_x, s_m_y, s_m_z, s_m_x_add, s_m_y_add = (0.0,) * 8

            # Calculate distributed loads only if core wall length is non-zero
            if corewall_len != 0:
                s_x = v_x / corewall_len          # Distributed shear force X (kN/m)
                s_y = v_y / corewall_len          # Distributed shear force Y (kN/m)
                s_z = f_z / corewall_len          # Distributed axial force Z (kN/m)
                s_m_x = m_x / corewall_len        # Distributed moment about X (kNm/m)
                s_m_y = m_y / corewall_len        # Distributed moment about Y (kNm/m)
                s_m_z = m_z / corewall_len        # Distributed moment about Z (kNm/m)
                s_m_x_add = mx_add / corewall_len # Distributed additional moment X (kNm/m)
                s_m_y_add = my_add / corewall_len # Distributed additional moment Y (kNm/m)

            # Save total load components to design record
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Vx (kN)')] = v_x
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Vy (kN)')] = v_y
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Fz (kN)')] = f_z
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mx (kNm)')] = m_x
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'My (kNm)')] = m_y
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mz (kNm)')] = m_z
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mx_Add (kNm)')] = mx_add
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'My_Add (kNm)')] = my_add

            # Save distributed load components (per unit length) to design record
            # These values are used for SAFE 22 distributed load application
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Vx (kN/m)')] = s_x
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Vy (kN/m)')] = s_y
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Fz (kN/m)')] = s_z
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mx (kNm/m)')] = s_m_x
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'My (kNm/m)')] = s_m_y
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mz (kNm/m)')] = s_m_z
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'Mx_Add (kNm/m)')] = s_m_x_add
            excel_outputs.CoreWallLoad.loc[i, (f"{load_case_name}", 'My_Add (kNm/m)')] = s_m_y_add

    return excel_outputs
