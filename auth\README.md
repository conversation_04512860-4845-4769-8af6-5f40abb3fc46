# Foundation Automation - Auth Package

The auth package provides security and authentication functionality for the Foundation Automation system. It includes essential components for user authentication, session management, and security logging.

## Components

### SecurityManager
The main security management class that handles:

- User authentication
- Session token generation
- Login attempt tracking
- Account lockout management
- Security logging

## Key Features

### Security Features
- Secure password hashing with salt
- Session token generation
- Account lockout mechanism
- Failed login attempt tracking
- System-wide lockout protection

### Configuration
The security manager uses configuration values from `app_config.py`:
- `MAX_LOGIN_ATTEMPTS`: Maximum number of failed login attempts before lockout
- `LOGIN_TIMEOUT_MINUTES`: Duration of lockout period
- `SESSION_TOKEN_BYTES`: Size of session tokens
- `SECURITY_LOG_PATH`: Path to security log file
- `EMAIL_DOMAIN`: Domain for email-based security features

## Usage

### Authentication Flow
1. User attempts to log in
2. SecurityManager checks for lockout status
3. If not locked out, attempts to validate credentials
4. On success, generates a session token
5. On failure, logs the attempt and checks for lockout conditions

### Session Management
- Secure session tokens are generated using cryptographically secure random bytes
- Tokens are used to maintain user sessions
- Session tokens are invalidated on logout or timeout

### Security Logging
- All authentication attempts are logged
- Failed attempts are tracked per user
- System-wide lockout events are logged
- Security logs are stored in a JSON file

## Security Considerations

- All passwords are hashed using SHA-256 with salt
- Session tokens are generated using cryptographically secure random numbers
- Failed login attempts are tracked to prevent brute force attacks
- System implements automatic lockout after multiple failed attempts
- All security events are logged for auditing purposes

## Error Handling

The SecurityManager includes robust error handling for:
- File operations (reading/writing security logs)
- JSON parsing errors
- Missing configuration values
- Invalid input data

## Integration

The auth package integrates with other components of the Foundation Automation system to provide secure access to foundation design and analysis features.
