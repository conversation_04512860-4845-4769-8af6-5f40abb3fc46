﻿"""
Case 4 Pile Layouts: Complex Multi-Element Layouts (Updated according to Pile_Layout_Rules.md)

This module implements enhanced multi-objective optimization for complex pile layouts using NSGA-III.
Updated to follow Rules 3.1-3.5 from Pile_Layout_Rules.md with improved integration.

Rules Implementation (Case 4):
- Rule 3.1: Find Local Axis of Pile Cap (same as Case 1.1)
- Rule 3.2: Find Possible Pile Layout for each Pile Type (same as Case 2.2)
- Rule 3.3: Find Ideal Pile Type for each Column Wall Cluster
- Rule 3.4: Use NSGA-III for optimal pile layout generation
- Rule 3.5: Professional DXF plotting with comprehensive visualization

Enhanced Features:
- Multi-objective optimization (4 objectives: safety, geometric, spacing, loading)
- Comprehensive pile type evaluation before optimization
- Site boundary integration and filtering
- Better preselection integration
- Enhanced constraint handling and repair operators

Multi-objective optimization considers 4 key objectives:
1. Safety & Structural Performance - pile utilization and load distribution
2. Geometric Optimization - centroid alignment and pile count
3. Spacing & Distribution - spacing violations and distribution quality  
4. Loading Optimization - load-based placement optimization

Author: Foundation Automation System
Date: 2024
"""

from typing import List, Dict, Any, Optional, Tuple
from math import sqrt, ceil, pi, cos, sin, atan2
import numpy as np

from ..data_types import Point2D
from ..pile_cap_geometry.pile_cap_geometry import LocalCoordinateSystem
from ..pile_cap_geometry.pile_cap_geometry import create_pile_cap_polygon
from ..utils.coordinate_utils import find_minimum_area_bounding_rectangle, local_to_global_coordinates
from ..optimization import (
    GeneticFitnessEvaluator,
    DEAPNSGA3Optimizer,
    DEAPNSGA3Config,
    collect_structural_sites
)
from ..layout_generation.genetic_fitness import (
    GeneticAlgorithmConfig
)
from .layout_common import (
    calculate_overall_load_center
)
from ..optimization.nsga3_optimizer import (
    DEAPNSGA3Optimizer,
    DEAPNSGA3Config,
    PileRepairOperators
)
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    create_timed_logger,
    log_algorithm_step,
    log_calculation_result,
    log_constraint_check
)





def handle_case_4(sub_clusters: Dict[str, Dict[str, Any]],
                  pile_distribution: Dict[str, int],
                  min_spacing: float,
                  edge_dist: float,
                  initial_local_system: Optional[LocalCoordinateSystem],
                  excel_inputs: Optional[Any],
                  pile_diameter: float,
                  site_boundary: Optional[Any] = None,
                  config_overrides: Optional[Dict[str, Any]] = None,
                  log_callback=None) -> List[Point2D]:
    """
    Enhanced Case 4 pile layout handler for complex multi-element arrangements.
    
    Implements Rules 3.1-3.5 from Pile_Layout_Rules.md:
    - Rule 3.1: Find Local Axis of Pile Cap (same as Case 1.1)
    - Rule 3.2: Find Possible Pile Layout for each Pile Type (same as Case 2.2)
    - Rule 3.3: Find Ideal Pile Type for each Column Wall Cluster
    - Rule 3.4: Use NSGA-III for optimal pile layout generation
    - Rule 3.5: Professional DXF plotting with comprehensive visualization
    
    This function coordinates multi-objective genetic algorithm (NSGA-III) optimization
    for complex layouts with multiple structural elements and constraints.

    Args:
        sub_clusters: Dictionary of sub-cluster data containing load points and structural elements
        pile_distribution: Dictionary indicating the target number of piles for each sub-cluster
        min_spacing: Minimum allowable spacing between any two piles in the layout
        edge_dist: Minimum edge distance for pile cap creation, used in defining the candidate grid
        initial_local_system: Optional local coordinate system to influence the orientation
        excel_inputs: Optional Excel input data for pile cap polygon creation
        pile_diameter: Diameter of the piles
        site_boundary: Optional site boundary for filtering grid positions
        config_overrides: Optional dictionary to override default optimization parameters
        log_callback: Optional callback function for logging debug and information messages

    Returns:
        List of 2D points representing the optimized pile positions for the entire foundation
    """
    log_function_entry(log_callback, "handle_case_4", 
                      clusters=len(sub_clusters), 
                      total_piles=sum(pile_distribution.values()),
                      min_spacing=min_spacing,
                      edge_dist=edge_dist,
                      pile_diameter=pile_diameter,
                      has_site_boundary=site_boundary is not None,
                      has_config_overrides=config_overrides is not None)
    
    try:
        # Log input validation and parameters
        enhanced_log(log_callback, "Starting Case 4 pile layout optimization", 'INFO')
        enhanced_log(log_callback, f"Input parameters:", 'INFO')
        enhanced_log(log_callback, f"  - Sub-clusters: {len(sub_clusters)}", 'INFO')
        enhanced_log(log_callback, f"  - Pile distribution: {pile_distribution}", 'INFO')
        enhanced_log(log_callback, f"  - Total target piles: {sum(pile_distribution.values())}", 'INFO')
        enhanced_log(log_callback, f"  - Min spacing: {min_spacing:.3f}m", 'INFO')
        enhanced_log(log_callback, f"  - Edge distance: {edge_dist:.3f}m", 'INFO')
        enhanced_log(log_callback, f"  - Pile diameter: {pile_diameter:.3f}m", 'INFO')
        enhanced_log(log_callback, f"  - Has site boundary: {site_boundary is not None}", 'DEBUG')
        enhanced_log(log_callback, f"  - Has config overrides: {config_overrides is not None}", 'DEBUG')
        
        # Validate inputs
        if not sub_clusters:
            enhanced_log(log_callback, "No sub-clusters provided for Case 4 layout", 'ERROR')
            raise ValueError("Case 4 requires at least one sub-cluster")
        
        if not pile_distribution or sum(pile_distribution.values()) <= 0:
            enhanced_log(log_callback, "Invalid pile distribution for Case 4 layout", 'ERROR')
            raise ValueError("Case 4 requires valid pile distribution")
        
        log_validation_result(log_callback, "input_validation", True, "All inputs validated successfully")
        
        # Create performance timer for the entire Case 4 process
        with create_timed_logger(log_callback, f"Case_4_optimization_{len(sub_clusters)}_clusters_{sum(pile_distribution.values())}_piles"):
            enhanced_log(log_callback, "Calling internal Case 4 handler", 'DEBUG')
            
            # Call internal function and only return the selected positions
            selected_positions, _ = _handle_case_4_internal(
                sub_clusters=sub_clusters,
                pile_distribution=pile_distribution,
                min_spacing=min_spacing,
                edge_dist=edge_dist,
                initial_local_system=initial_local_system,
                excel_inputs=excel_inputs,
                pile_diameter=pile_diameter,
                site_boundary=site_boundary,
                config_overrides=config_overrides,
                log_callback=log_callback,
                return_grid=False  # Don't need the grid for normal case
            )
            
            # Validate results
            if not selected_positions:
                enhanced_log(log_callback, "Case 4 optimization returned no positions", 'ERROR')
                raise ValueError("Case 4 optimization failed to generate any pile positions")
            
            enhanced_log(log_callback, f"Case 4 optimization completed successfully", 'INFO')
            enhanced_log(log_callback, f"Generated {len(selected_positions)} optimized pile positions", 'INFO')
            
            # Log position summary
            if selected_positions:
                x_coords = [pos[0] for pos in selected_positions]
                y_coords = [pos[1] for pos in selected_positions]
                enhanced_log(log_callback, f"Position bounds: X[{min(x_coords):.3f}, {max(x_coords):.3f}], Y[{min(y_coords):.3f}, {max(y_coords):.3f}]", 'DEBUG')
                
                # Calculate pile centroid
                pile_center_x = sum(x_coords) / len(x_coords)
                pile_center_y = sum(y_coords) / len(y_coords)
                enhanced_log(log_callback, f"Pile layout centroid: ({pile_center_x:.3f}, {pile_center_y:.3f})", 'DEBUG')
            
            log_validation_result(log_callback, "case_4_result_validation", len(selected_positions) > 0,
                                f"Generated {len(selected_positions)} positions")
            
            log_function_exit(log_callback, "handle_case_4", f"{len(selected_positions)} positions")
            return selected_positions
    
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 4 pile layout: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_4", "error")
        raise


def _handle_case_4_internal(sub_clusters: Dict[str, Dict[str, Any]],
                           pile_distribution: Dict[str, int],
                           min_spacing: float,
                           edge_dist: float,
                           initial_local_system: Optional[LocalCoordinateSystem],
                           excel_inputs: Optional[Any],
                           pile_diameter: float,
                           site_boundary: Optional[Any] = None,
                           config_overrides: Optional[Dict[str, Any]] = None,
                           log_callback=None,
                           return_grid: bool = False) -> Tuple[List[Point2D], Optional[List[Point2D]]]:
    """
    Unified function for handling Case 4 pile layouts (single or multiple sub-clusters).
    
    This function coordinates the process of generating pile positions using a multi-objective genetic algorithm
    (NSGA-III). It accounts for various objectives such as centroid mismatch, structural utilization,
    pile count, and spacing constraints. Now handles both single and multiple sub-clusters.

    Args:
        sub_clusters: Dictionary of sub-cluster data containing load points and structural elements
        pile_distribution: Dictionary indicating the target number of piles for each sub-cluster
        min_spacing: Minimum allowable spacing between any two piles in the layout
        edge_dist: Minimum edge distance for pile cap creation, used in defining the candidate grid
        initial_local_system: Optional local coordinate system to influence the orientation
        excel_inputs: Optional Excel input data for pile cap polygon creation
        pile_diameter: Diameter of the piles
        site_boundary: Optional site boundary for filtering grid positions
        config_overrides: Optional dictionary to override default optimization parameters
        log_callback: Optional callback function for logging debug and information messages
        return_grid: Whether to return the optimization grid along with selected positions

    Returns:
        Tuple containing:
        - List of 2D points representing the optimized pile positions
        - Optional list of grid positions used for optimization (if return_grid=True)
    """
    log_function_entry(log_callback, "_handle_case_4_internal", 
                      clusters=len(sub_clusters), 
                      total_piles=sum(pile_distribution.values()),
                      return_grid=return_grid)
    
    try:
        # Start optimization process with enhanced logging
        enhanced_log(log_callback, "CASE 4 INTERNAL - Starting optimization process", 'INFO')

        is_single_cluster = len(sub_clusters) == 1
        total_target_piles = sum(pile_distribution.values())

        # Track processing with detailed logging
        if is_single_cluster:
            enhanced_log(log_callback, f"CASE 4 (Single Cluster) - Starting optimization with 1 sub-cluster", 'INFO')
            log_algorithm_step(log_callback, "Case_4_Internal", "single_cluster_mode", "Processing single cluster layout")
        else:
            enhanced_log(log_callback, f"CASE 4 (Multiple Clusters) - Starting optimization with {len(sub_clusters)} sub-clusters", 'INFO')
            log_algorithm_step(log_callback, "Case_4_Internal", "multi_cluster_mode", f"Processing {len(sub_clusters)} clusters")

        enhanced_log(log_callback, f"CASE 4 - Pile distribution: {pile_distribution}", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Total target piles: {total_target_piles}", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Min spacing: {min_spacing}m, Edge dist: {edge_dist}m", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Pile diameter: {pile_diameter}m", 'DEBUG')

        # Handle single pile case
        if total_target_piles == 1:
            enhanced_log(log_callback, "CASE 4 - Single pile case detected", 'INFO')
            load_center = calculate_overall_load_center(sub_clusters)
            enhanced_log(log_callback, f"CASE 4 - Single pile at load center: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'INFO')
            log_algorithm_step(log_callback, "Case_4_Internal", "single_pile_solution", f"Load center: ({load_center[0]:.3f}, {load_center[1]:.3f})")
            
            result_positions = [load_center]
            result_grid = [load_center] if return_grid else None
            
            log_function_exit(log_callback, "_handle_case_4_internal", "single_pile_solution")
            return result_positions, result_grid

        # Configuration setup with detailed logging
        enhanced_log(log_callback, "CASE 4 - Setting up optimization configuration", 'INFO')
        
        # Use simplified configuration for speed
        optimization_config = GeneticAlgorithmConfig()
        enhanced_log(log_callback, "CASE 4 - Using fast optimization configuration", 'INFO')
        
        if is_single_cluster:
            # Optimized for single cluster
            grid_spacing = max(min_spacing * 1.2, 1.5)  # Coarser grid for speed
            expansion_factor = 2.0  # Reasonable coverage
            enhanced_log(log_callback, f"Single cluster config: grid_spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'DEBUG')
        else:
            # Optimized for multiple clusters
            grid_spacing = max(min_spacing * 1.3, 1.8)  # Even coarser for speed
            expansion_factor = 1.8  # Smaller coverage for speed
            enhanced_log(log_callback, f"Multi cluster config: grid_spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'DEBUG')
        
        # Create fast NSGA-III configuration with enhanced logging
        enhanced_log(log_callback, "Creating DEAPNSGA3Config for Case 4 optimization", 'INFO')
        try:
            nsga3_config = DEAPNSGA3Config(
                population_size=15,  # Small for speed
                generations=20       # Few for speed
                # Note: use_multiprocessing is not a valid parameter for DEAPNSGA3Config
                # Valid parameters: population_size, generations, ref_points_p, crossover_prob, mutation_prob, mutation_indpb
            )
            enhanced_log(log_callback, f"✅ DEAPNSGA3Config created successfully", 'INFO')
            enhanced_log(log_callback, f"Base NSGA-III config: pop={nsga3_config.population_size}, gen={nsga3_config.generations}", 'DEBUG')
            enhanced_log(log_callback, f"Genetic operators: crossover_prob={nsga3_config.crossover_prob}, mutation_prob={nsga3_config.mutation_prob}", 'DEBUG')
            enhanced_log(log_callback, f"Reference points precision: {nsga3_config.ref_points_p}", 'DEBUG')
        except TypeError as e:
            error_msg = f"DEAPNSGA3Config creation failed with TypeError: {e}"
            enhanced_log(log_callback, f"❌ {error_msg}", 'ERROR')
            enhanced_log(log_callback, "This error typically occurs when invalid parameters are passed to DEAPNSGA3Config", 'ERROR')
            enhanced_log(log_callback, "Valid parameters: population_size, generations, ref_points_p, crossover_prob, mutation_prob, mutation_indpb", 'ERROR')
            raise TypeError(f"Case 4 optimization configuration error: {error_msg}")
        except Exception as e:
            error_msg = f"DEAPNSGA3Config creation failed: {e}"
            enhanced_log(log_callback, f"❌ {error_msg}", 'ERROR')
            raise
        
        # Apply configuration overrides if provided
        if config_overrides:
            enhanced_log(log_callback, "Applying configuration overrides", 'DEBUG')
            original_pop = nsga3_config.population_size
            original_gen = nsga3_config.generations
            original_expansion = expansion_factor
            original_spacing = grid_spacing
            
            if 'population_size' in config_overrides:
                nsga3_config.population_size = config_overrides['population_size']
                enhanced_log(log_callback, f"Override population_size: {original_pop} -> {nsga3_config.population_size}", 'DEBUG')
            if 'generations' in config_overrides:
                nsga3_config.generations = config_overrides['generations']
                enhanced_log(log_callback, f"Override generations: {original_gen} -> {nsga3_config.generations}", 'DEBUG')
            if 'grid_expansion_factor' in config_overrides:
                expansion_factor = config_overrides['grid_expansion_factor']
                enhanced_log(log_callback, f"Override expansion_factor: {original_expansion} -> {expansion_factor}", 'DEBUG')
            if 'grid_spacing' in config_overrides and config_overrides['grid_spacing']:
                grid_spacing = max(config_overrides['grid_spacing'], min_spacing)
                enhanced_log(log_callback, f"Override grid_spacing: {original_spacing:.3f} -> {grid_spacing:.3f}m", 'DEBUG')
            
            enhanced_log(log_callback, f"CASE 4 - Config overrides applied: pop={nsga3_config.population_size}, gen={nsga3_config.generations}", 'INFO')

        # For large problems, apply additional speed optimizations
        if len(sub_clusters) >= 5 or sum(pile_distribution.values()) >= 50:
            enhanced_log(log_callback, f"Large problem detected ({len(sub_clusters)} clusters, {sum(pile_distribution.values())} piles)", 'WARNING')
            enhanced_log(log_callback, "Applying speed optimizations...", 'INFO')

            # Store original values for logging
            orig_pop = nsga3_config.population_size
            orig_gen = nsga3_config.generations
            orig_expansion = expansion_factor
            orig_spacing = grid_spacing

            # Further reduce for speed
            nsga3_config.population_size = min(nsga3_config.population_size, 10)
            nsga3_config.generations = min(nsga3_config.generations, 15)
            expansion_factor = min(expansion_factor, 1.5)
            grid_spacing = max(grid_spacing * 1.2, min_spacing * 1.4)

            enhanced_log(log_callback, f"Speed optimizations applied:", 'INFO')
            enhanced_log(log_callback, f"  Population: {orig_pop} -> {nsga3_config.population_size}", 'INFO')
            enhanced_log(log_callback, f"  Generations: {orig_gen} -> {nsga3_config.generations}", 'INFO')
            enhanced_log(log_callback, f"  Expansion: {orig_expansion:.2f} -> {expansion_factor:.2f}", 'INFO')
            enhanced_log(log_callback, f"  Grid spacing: {orig_spacing:.3f} -> {grid_spacing:.3f}m", 'INFO')

        log_calculation_result(log_callback, "optimization_config", f"pop={nsga3_config.population_size}, gen={nsga3_config.generations}", "final")

        # Grid creation with detailed logging
        enhanced_log(log_callback, "CASE 4 - Starting grid creation process", 'INFO')
        
        # Create extended pile cap and grid
        if is_single_cluster:
            enhanced_log(log_callback, "Creating pile cap aligned grid for single cluster", 'DEBUG')
            # For single clusters, use pile cap aligned grid (former Case 3 approach)
            extended_grid = _create_pile_cap_aligned_grid(
                list(sub_clusters.values())[0], excel_inputs, edge_dist, grid_spacing, 
                expansion_factor, initial_local_system, log_callback
            )
        else:
            enhanced_log(log_callback, "Creating extended grid for multiple clusters", 'DEBUG')
            # For multiple clusters, use extended grid approach
            extended_grid = _create_extended_grid(
                sub_clusters, excel_inputs, edge_dist, grid_spacing, 
                expansion_factor, initial_local_system, log_callback
            )
        
        # Validate grid creation
        if not extended_grid:
            enhanced_log(log_callback, "CASE 4 - Failed to create grid for pile placement", 'ERROR')
            raise ValueError("CASE 4 - Failed to create grid for pile placement")

        enhanced_log(log_callback, f"CASE 4 - Initial grid created with {len(extended_grid)} candidate positions", 'INFO')
        
        # Apply site boundary filtering to grid BEFORE optimization
        if site_boundary and extended_grid:
            enhanced_log(log_callback, "Applying site boundary filtering to grid", 'INFO')
            original_grid_size = len(extended_grid)
            filtered_grid = _filter_grid_by_site_boundary(extended_grid, site_boundary, log_callback)
            
            if filtered_grid:
                extended_grid = filtered_grid
                removed_count = original_grid_size - len(extended_grid)
                enhanced_log(log_callback, f"Site boundary filter removed {removed_count} grid positions outside boundary", 'INFO')
                enhanced_log(log_callback, f"Grid reduced from {original_grid_size} to {len(extended_grid)} valid positions", 'INFO')
                log_validation_result(log_callback, "site_boundary_filtering", True, f"Retained {len(extended_grid)}/{original_grid_size} positions")
            else:
                enhanced_log(log_callback, "Site boundary filtering removed ALL grid positions! Using original grid.", 'WARNING')
                log_validation_result(log_callback, "site_boundary_filtering", False, "All positions filtered out")

        enhanced_log(log_callback, f"CASE 4 - Final grid with {len(extended_grid)} candidate positions", 'INFO')
        if extended_grid:
            grid_bounds = {
                'min_x': min(pos[0] for pos in extended_grid),
                'max_x': max(pos[0] for pos in extended_grid),
                'min_y': min(pos[1] for pos in extended_grid),
                'max_y': max(pos[1] for pos in extended_grid)
            }
            enhanced_log(log_callback, f"CASE 4 - Grid bounds: X[{grid_bounds['min_x']:.2f}, {grid_bounds['max_x']:.2f}], Y[{grid_bounds['min_y']:.2f}, {grid_bounds['max_y']:.2f}]", 'DEBUG')
            log_calculation_result(log_callback, "grid_bounds", grid_bounds, "spatial")

        # Prepare evaluation parameters with detailed logging
        enhanced_log(log_callback, "CASE 4 - Preparing evaluation parameters", 'INFO')
        struct_sites = collect_structural_sites(sub_clusters, log_callback)
        total_load = sum(load for cluster in sub_clusters.values() for *_, load in cluster.get("load_points", []))
        pile_capacity = total_load / max(1, total_target_piles)
        
        enhanced_log(log_callback, f"CASE 4 - Collected {len(struct_sites)} structural sites", 'INFO')
        if struct_sites:
            enhanced_log(log_callback, f"CASE 4 - First structural site: {struct_sites[0]}", 'DEBUG')
        else:
            enhanced_log(log_callback, "CASE 4 - No structural sites found!", 'ERROR')
            # Debug the sub_clusters structure
            for cluster_name, cluster_data in sub_clusters.items():
                enhanced_log(log_callback, f"CASE 4 - Cluster {cluster_name}:", 'DEBUG')
                elements = cluster_data.get('elements', {})
                enhanced_log(log_callback, f"  Elements keys: {list(elements.keys())}", 'DEBUG')
                columns = elements.get('columns', [])
                walls = elements.get('walls', [])
                enhanced_log(log_callback, f"  Columns count: {len(columns)}", 'DEBUG')
                enhanced_log(log_callback, f"  Walls count: {len(walls)}", 'DEBUG')
                if columns:
                    enhanced_log(log_callback, f"  First column: {columns[0]}", 'DEBUG')
                if walls:
                    enhanced_log(log_callback, f"  First wall: {walls[0]}", 'DEBUG')

        enhanced_log(log_callback, f"CASE 4 - Total load: {total_load:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Calculated pile capacity: {pile_capacity:.2f} kN/pile", 'INFO')
        
        log_calculation_result(log_callback, "load_analysis", f"{total_load:.2f} kN total, {pile_capacity:.2f} kN/pile", "structural")

        # Check if structural sites are available
        if not struct_sites:
            enhanced_log(log_callback, "CASE 4 - No structural sites found from elements", 'ERROR')
            raise ValueError("CASE 4 - No structural sites or load points available")

        # Create evaluator with logging
        enhanced_log(log_callback, "Creating genetic fitness evaluator", 'INFO')
        evaluator = GeneticFitnessEvaluator(
            grid_positions=extended_grid,
            sub_clusters=sub_clusters,
            target_pile_count=total_target_piles,
            min_spacing=min_spacing,
            struct_sites=struct_sites,
            pile_capacity=pile_capacity,
            config=optimization_config
        )

        # Store the final grid for possible return (AFTER all modifications including site boundary filtering)
        optimization_grid = extended_grid.copy()

        enhanced_log(log_callback, "Fast evaluator created successfully", 'INFO')
        enhanced_log(log_callback, "Creating fast NSGA-III optimizer...", 'INFO')

        optimizer = DEAPNSGA3Optimizer(
            config=nsga3_config,
            n_variables=len(extended_grid),
            n_objectives=4  # 4-objective optimization
        )

        enhanced_log(log_callback, "Optimizer created successfully", 'INFO')
        enhanced_log(log_callback, "Ready to start optimization process", 'INFO')
        
        log_algorithm_step(log_callback, "Case_4_Internal", "optimization_setup_complete", f"Grid: {len(extended_grid)}, Variables: {len(extended_grid)}, Objectives: 4")

        # Run optimization with comprehensive logging and error handling
        enhanced_log(log_callback, "Starting fast NSGA-III optimization...", 'INFO')
        enhanced_log(log_callback, f"Population size: {nsga3_config.population_size}", 'INFO')
        enhanced_log(log_callback, f"Generations: {nsga3_config.generations}", 'INFO')
        enhanced_log(log_callback, f"Grid positions: {len(extended_grid)}", 'INFO')
        enhanced_log(log_callback, "Optimized for production speed...", 'INFO')

        # Run fast optimization with timing
        import time
        start_time = time.time()

        best_individual, pareto_front, optimization_metrics = optimizer.optimize(
            evaluation_func=evaluator,
            eval_kwargs={},  # Evaluator is already configured
            grid_positions=extended_grid,
            min_spacing=min_spacing,
            target_count=total_target_piles,
            log_callback=log_callback
        )

        elapsed_time = time.time() - start_time

        enhanced_log(log_callback, f"Fast optimization completed in {elapsed_time:.1f} seconds!", 'INFO')
        log_performance_metric(log_callback, "optimization_time", elapsed_time, "seconds")

        # Log optimization metrics if available
        if optimization_metrics:
            enhanced_log(log_callback, f"Optimization metrics: {len(optimization_metrics)} metrics collected", 'DEBUG')
            if 'convergence_history' in optimization_metrics and optimization_metrics['convergence_history']:
                enhanced_log(log_callback, f"Convergence history: {len(optimization_metrics['convergence_history'])} generations", 'DEBUG')
            if 'diversity_history' in optimization_metrics and optimization_metrics['diversity_history']:
                enhanced_log(log_callback, f"Diversity history: {len(optimization_metrics['diversity_history'])} generations", 'DEBUG')

        enhanced_log(log_callback, "Analyzing results...", 'INFO')
        
        # Enhanced result analysis with detailed logging
        enhanced_log(log_callback, "CASE 4 - Starting result analysis", 'INFO')
        
        # Filter Pareto front to include solutions with target or close to target pile count
        if pareto_front:
            enhanced_log(log_callback, f"Found {len(pareto_front)} solutions in Pareto front", 'INFO')
            log_calculation_result(log_callback, "pareto_front_size", len(pareto_front), "solutions")
            
            correct_count_solutions = []
            acceptable_range = max(1, int(total_target_piles * 0.15))  # Allow ±15% flexibility
            enhanced_log(log_callback, f"Acceptable pile count range: {total_target_piles} ±{acceptable_range} piles", 'DEBUG')
            
            for individual in pareto_front:
                pile_count = sum(individual)
                # Accept solutions within reasonable range of target
                if abs(pile_count - total_target_piles) <= acceptable_range:
                    correct_count_solutions.append(individual)
            
            enhanced_log(log_callback, f"Found {len(correct_count_solutions)} solutions with acceptable pile count", 'INFO')
            log_validation_result(log_callback, "acceptable_solutions", len(correct_count_solutions) > 0,
                                f"Found {len(correct_count_solutions)} acceptable solutions")
            
            if correct_count_solutions:
                enhanced_log(log_callback, f"Target: {total_target_piles} ±{acceptable_range} piles", 'DEBUG')
                enhanced_log(log_callback, "Selecting best solution from acceptable candidates...", 'INFO')
                
                # Re-evaluate to find the best among acceptable count solutions
                best_fitness = float('inf')
                best_individual = None
                
                for i, individual in enumerate(correct_count_solutions):
                    # Calculate overall fitness (sum of normalized objectives)
                    objectives = evaluator(individual)
                    overall_fitness = sum(objectives)  # Simple sum for now
                    
                    # Add small penalty for deviation from exact target count
                    count_deviation = abs(sum(individual) - total_target_piles)
                    overall_fitness += count_deviation * 0.01  # Small penalty weight
                    
                    if overall_fitness < best_fitness:
                        best_fitness = overall_fitness
                        best_individual = individual
                        enhanced_log(log_callback, f"New best solution {i+1}: {sum(individual)} piles, fitness {overall_fitness:.3f}", 'DEBUG')
                
                enhanced_log(log_callback, f"Selected best solution with {sum(best_individual)} piles and fitness {best_fitness:.3f}", 'INFO')
                log_calculation_result(log_callback, "best_solution", f"{sum(best_individual)} piles, fitness {best_fitness:.3f}", "optimization")
            else:
                enhanced_log(log_callback, f"WARNING: No solutions with acceptable pile count found", 'WARNING')
                enhanced_log(log_callback, "Using best solution from entire Pareto front", 'WARNING')
                # Use the best individual from the entire Pareto front if no acceptable count solutions
                if pareto_front:
                    best_individual = pareto_front[0]  # First individual from sorted Pareto front
                    enhanced_log(log_callback, f"Fallback solution: {sum(best_individual)} piles", 'WARNING')
        
        if not best_individual:
            enhanced_log(log_callback, "ERROR: CASE 4 - Optimization returned no valid individual", 'ERROR')
            raise ValueError("CASE 4 - Optimization returned no valid individual")
        
        enhanced_log(log_callback, "CASE 4 - Extracting final positions from best individual", 'INFO')
        
        # Extract final positions
        final_positions = [
            pos for pos, selected in zip(extended_grid, best_individual) if selected
        ]
        
        enhanced_log(log_callback, f"Extracted {len(final_positions)} positions from optimization result", 'DEBUG')
        
        # Apply final site boundary check as safety net
        if site_boundary and final_positions:
            enhanced_log(log_callback, "Applying final site boundary check as safety net", 'DEBUG')
            original_count = len(final_positions)
            final_positions = _filter_grid_by_site_boundary(final_positions, site_boundary, log_callback)
            
            if len(final_positions) < original_count:
                removed_count = original_count - len(final_positions)
                enhanced_log(log_callback, f"WARNING: Final site boundary check removed {removed_count} positions outside boundary", 'WARNING')
                enhanced_log(log_callback, f"Final positions: {len(final_positions)} (down from {original_count})", 'WARNING')
            
            if not final_positions:
                enhanced_log(log_callback, "ERROR: All final positions are outside site boundary!", 'ERROR')
                raise ValueError("CASE 4 - All optimized positions are outside site boundary")

        # Validate that we got positions
        if not final_positions:
            enhanced_log(log_callback, "ERROR: CASE 4 - No positions extracted from optimization result", 'ERROR')
            raise ValueError("CASE 4 - No positions extracted from optimization result")
        
        # Final logging and validation
        enhanced_log(log_callback, f"Pile layout optimization completed successfully!", 'INFO')
        enhanced_log(log_callback, f"Generated {len(final_positions)} optimized pile positions", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Best individual has {sum(best_individual)} selected positions", 'INFO')
        
        if final_positions:
            enhanced_log(log_callback, f"CASE 4 - First pile position: ({final_positions[0][0]:.3f}, {final_positions[0][1]:.3f})", 'DEBUG')
            enhanced_log(log_callback, f"CASE 4 - Last pile position: ({final_positions[-1][0]:.3f}, {final_positions[-1][1]:.3f})", 'DEBUG')
            
            # Calculate pile center
            pile_center_x = sum(pos[0] for pos in final_positions) / len(final_positions)
            pile_center_y = sum(pos[1] for pos in final_positions) / len(final_positions)
            enhanced_log(log_callback, f"CASE 4 - Pile center: ({pile_center_x:.3f}, {pile_center_y:.3f})", 'DEBUG')
            
            # Calculate offset from load center
            overall_load_center = evaluator.overall_load_center
            offset = sqrt((pile_center_x - overall_load_center[0])**2 + 
                         (pile_center_y - overall_load_center[1])**2)
            enhanced_log(log_callback, f"CASE 4 - Offset from load center: {offset:.3f}m", 'DEBUG')
            
            log_calculation_result(log_callback, "final_analysis", f"center: ({pile_center_x:.3f}, {pile_center_y:.3f}), offset: {offset:.3f}m", "geometric")
            
        enhanced_log(log_callback, "Case 4 optimization process complete!", 'INFO')

        # Return both final positions and optimization grid if requested
        result_grid = optimization_grid if return_grid else None
        
        log_function_exit(log_callback, "_handle_case_4_internal", f"{len(final_positions)} positions, grid_returned={return_grid}")
        return final_positions, result_grid
        
    except Exception as e:
        enhanced_log(log_callback, f"CASE 4 - Optimization failed: {str(e)}", 'ERROR')
        import traceback
        enhanced_log(log_callback, f"CASE 4 - Traceback: {traceback.format_exc()}", 'ERROR')
        log_function_exit(log_callback, "_handle_case_4_internal", "error")
        raise


def handle_case_4_with_possible_positions(sub_clusters: Dict[str, Dict[str, Any]],
                                        pile_distribution: Dict[str, int],
                                        min_spacing: float,
                                        edge_dist: float,
                                        initial_local_system: Optional[LocalCoordinateSystem],
                                        excel_inputs: Optional[Any],
                                        pile_diameter: float,
                                        site_boundary: Optional[Any] = None,
                                        config_overrides: Optional[Dict[str, Any]] = None,
                                        log_callback=None) -> Tuple[List[Point2D], List[Point2D]]:
    """
    Unified function for Case 4 that also returns all possible pile positions.
    This function provides a comprehensive view of selected piles and the entire set of valid
    candidate locations that could accommodate piles without clashing.

    This function now calls handle_case_4_internal to ensure the EXACT same grid is used
    for both optimization and possible positions display.

    Args:
        sub_clusters: Dictionary of sub-cluster data containing load points and structural elements
        pile_distribution: Dictionary indicating the target number of piles for each sub-cluster
        min_spacing: Minimum allowable spacing between any two piles in the layout
        edge_dist: Minimum edge distance for pile cap creation
        initial_local_system: Optional local coordinate system to influence the orientation
        excel_inputs: Optional Excel input data for pile cap polygon creation
        pile_diameter: Diameter of the piles
        site_boundary: Optional site boundary for filtering grid positions
        config_overrides: Optional dictionary to override default optimization parameters
        log_callback: Optional callback function for logging debug and information messages

    Returns:
        Tuple[List[Point2D], List[Point2D]]: A tuple containing two lists of 2D points:
                                             - The first list represents the actual selected pile positions.
                                             - The second list represents all valid possible pile positions
                                               that could accommodate piles, filtered for clashes.
    """
    log_function_entry(log_callback, "handle_case_4_with_possible_positions", 
                      clusters=len(sub_clusters), total_piles=sum(pile_distribution.values()),
                      has_site_boundary=site_boundary is not None)
    
    try:
        enhanced_log(log_callback, "CASE 4 POSSIBLE - Starting optimization with shared grid approach", 'INFO')
        enhanced_log(log_callback, f"CASE 4 POSSIBLE - Input parameters:", 'INFO')
        enhanced_log(log_callback, f"  - Sub-clusters: {len(sub_clusters)}", 'INFO')
        enhanced_log(log_callback, f"  - Total target piles: {sum(pile_distribution.values())}", 'INFO')
        enhanced_log(log_callback, f"  - Min spacing: {min_spacing:.3f}m", 'INFO')
        enhanced_log(log_callback, f"  - Edge distance: {edge_dist:.3f}m", 'INFO')
        enhanced_log(log_callback, f"  - Pile diameter: {pile_diameter:.3f}m", 'INFO')
        
        # Call the internal function that returns both selected positions and the grid used
        selected_positions, optimization_grid = _handle_case_4_internal(
            sub_clusters=sub_clusters,
            pile_distribution=pile_distribution,
            min_spacing=min_spacing,
            edge_dist=edge_dist,
            initial_local_system=initial_local_system,
            excel_inputs=excel_inputs,
            pile_diameter=pile_diameter,
            site_boundary=site_boundary,
            config_overrides=config_overrides,
            log_callback=log_callback,
            return_grid=True  # Request to return the grid used
        )
        
        if not selected_positions:
            enhanced_log(log_callback, "ERROR: CASE 4 POSSIBLE - No selected positions returned", 'ERROR')
            raise ValueError("CASE 4 POSSIBLE - Optimization failed to generate positions")
        
        if not optimization_grid:
            enhanced_log(log_callback, "ERROR: CASE 4 POSSIBLE - No optimization grid returned", 'ERROR')
            raise ValueError("CASE 4 POSSIBLE - No optimization grid available")
        
        # The optimization_grid is exactly the same grid that was used for optimization
        possible_positions = optimization_grid.copy()
        
        enhanced_log(log_callback, f"CASE 4 POSSIBLE - Selected {len(selected_positions)} positions from {len(possible_positions)} possible positions", 'INFO')
        enhanced_log(log_callback, "CASE 4 POSSIBLE - Grid alignment guaranteed - same grid used for optimization and display", 'INFO')
        
        log_validation_result(log_callback, "possible_positions_generation", len(possible_positions) > 0,
                            f"Generated {len(possible_positions)} possible positions")
        log_validation_result(log_callback, "selected_positions_subset", len(selected_positions) <= len(possible_positions),
                            f"Selected {len(selected_positions)} from {len(possible_positions)} possible")
        
        # Log grid bounds for debugging
        if selected_positions:
            sel_min_x = min(pos[0] for pos in selected_positions)
            sel_max_x = max(pos[0] for pos in selected_positions)
            sel_min_y = min(pos[1] for pos in selected_positions)
            sel_max_y = max(pos[1] for pos in selected_positions)
            enhanced_log(log_callback, f"CASE 4 POSSIBLE - Selected bounds: X[{sel_min_x:.3f}, {sel_max_x:.3f}], Y[{sel_min_y:.3f}, {sel_max_y:.3f}]", 'DEBUG')
        
        if possible_positions:
            poss_min_x = min(pos[0] for pos in possible_positions)
            poss_max_x = max(pos[0] for pos in possible_positions)
            poss_min_y = min(pos[1] for pos in possible_positions)
            poss_max_y = max(pos[1] for pos in possible_positions)
            enhanced_log(log_callback, f"CASE 4 POSSIBLE - Possible bounds: X[{poss_min_x:.3f}, {poss_max_x:.3f}], Y[{poss_min_y:.3f}, {poss_max_y:.3f}]", 'DEBUG')
        
        # Additional debugging: verify grid alignment
        if selected_positions and possible_positions:
            enhanced_log(log_callback, "CASE 4 POSSIBLE - Verifying grid alignment...", 'DEBUG')
            alignment_errors = 0
            misaligned_positions = []
            
            for i, sel_pos in enumerate(selected_positions):
                # Check if selected position exists in possible positions
                found = False
                closest_distance = float('inf')
                closest_pos = None
                
                for poss_pos in possible_positions:
                    distance = abs(sel_pos[0] - poss_pos[0]) + abs(sel_pos[1] - poss_pos[1])
                    if distance < 0.001:  # Very tight tolerance
                        found = True
                        break
                    elif distance < closest_distance:
                        closest_distance = distance
                        closest_pos = poss_pos
                
                if not found:
                    alignment_errors += 1
                    misaligned_positions.append((i, sel_pos, closest_pos, closest_distance))
            
            if alignment_errors == 0:
                enhanced_log(log_callback, "CASE 4 POSSIBLE - Perfect grid alignment - all selected positions found in possible positions", 'INFO')
                log_validation_result(log_callback, "grid_alignment", True, "Perfect alignment between selected and possible positions")
            else:
                enhanced_log(log_callback, f"WARNING: CASE 4 POSSIBLE - {alignment_errors} selected positions NOT found in possible positions grid!", 'WARNING')
                enhanced_log(log_callback, "WARNING: This indicates a grid generation mismatch - possible DXF display issue", 'WARNING')
                log_validation_result(log_callback, "grid_alignment", False, f"{alignment_errors} misaligned positions")
                
                # Log details of first few misaligned positions
                for i, (idx, sel_pos, closest_pos, distance) in enumerate(misaligned_positions[:3]):
                    enhanced_log(log_callback, f"  Misaligned #{idx}: Selected=({sel_pos[0]:.6f}, {sel_pos[1]:.6f}), Closest=({closest_pos[0]:.6f}, {closest_pos[1]:.6f}), Distance={distance:.6f}", 'WARNING')
                
                if len(misaligned_positions) > 3:
                    enhanced_log(log_callback, f"  ... and {len(misaligned_positions) - 3} more misaligned positions", 'WARNING')
        
        enhanced_log(log_callback, "CASE 4 POSSIBLE - Grid alignment verification complete", 'DEBUG')
        
        log_function_exit(log_callback, "handle_case_4_with_possible_positions", f"selected={len(selected_positions)}, possible={len(possible_positions)}")
        return selected_positions, possible_positions
    
    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 4 with possible positions: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_4_with_possible_positions", "error")
        raise


def _create_pile_cap_aligned_grid(cluster_data: dict,
                                 excel_inputs: Optional[Any],
                                 edge_dist: float, grid_spacing: float,
                                 expansion_factor: float,
                                 initial_local_system: Optional[LocalCoordinateSystem],
                                 log_callback=None) -> List[Point2D]:
    """Create a grid aligned with the pile cap's local coordinate system (for single clusters)."""
    
    log_function_entry(log_callback, "_create_pile_cap_aligned_grid", 
                      edge_dist=edge_dist, grid_spacing=grid_spacing, expansion_factor=expansion_factor)
    
    try:
        enhanced_log(log_callback, f"CASE 4 - Creating pile cap aligned grid with spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'INFO')
        
        # Extract elements from cluster data
        elements = cluster_data.get('elements', {})
        
        enhanced_log(log_callback, f"CASE 4 - Elements: {len(elements.get('columns', []))} columns, {len(elements.get('walls', []))} walls", 'INFO')
        log_algorithm_step(log_callback, "Grid_Creation", "extract_elements", f"columns: {len(elements.get('columns', []))}, walls: {len(elements.get('walls', []))}")
        
        if excel_inputs and elements:
            enhanced_log(log_callback, "CASE 4 - Creating pile cap from cluster elements", 'DEBUG')
            # Create pile cap from cluster elements
            pile_cap_result = create_pile_cap_polygon(
                group_elements=elements,
                excel_inputs=excel_inputs,
                site_poly=None,
                edge_dist=edge_dist
            )
            
            enhanced_log(log_callback, f"CASE 4 - Pile cap creation result: valid={pile_cap_result['is_valid']}", 'INFO')
            log_validation_result(log_callback, "pile_cap_creation", pile_cap_result['is_valid'], "Pile cap polygon generation")
            
            if pile_cap_result['is_valid'] and pile_cap_result['polygon']:
                enhanced_log(log_callback, "CASE 4 - Finding minimum area bounding rectangle", 'DEBUG')
                local_system_result = find_minimum_area_bounding_rectangle(pile_cap_result['polygon'])
                local_system = local_system_result.local_system
                
                # Use provided initial local system rotation if available
                if local_system and initial_local_system:
                    enhanced_log(log_callback, f"CASE 4 - Applying initial rotation angle: {initial_local_system.rotation_angle:.3f} rad", 'DEBUG')
                    # Create a new coordinate system with the modified rotation angle
                    # since OptimalCoordinateSystem is immutable (NamedTuple)
                    local_system = local_system._replace(rotation_angle=initial_local_system.rotation_angle)
                
                if local_system:
                    enhanced_log(log_callback, f"CASE 4 - Using pile cap based local system for grid generation", 'INFO')
                    enhanced_log(log_callback, f"CASE 4 - Local system dimensions: {local_system.long_axis_length:.3f}m × {local_system.short_axis_length:.3f}m", 'DEBUG')
                    
                    grid = _generate_aligned_grid(local_system, grid_spacing, expansion_factor, log_callback)
                    enhanced_log(log_callback, f"CASE 4 - Generated pile cap aligned grid with {len(grid)} positions", 'INFO')
                    
                    log_function_exit(log_callback, "_create_pile_cap_aligned_grid", f"{len(grid)} positions")
                    return grid
        
        # No pile cap geometry available
        enhanced_log(log_callback, "ERROR: CASE 4 - No pile cap geometry available for grid creation", 'ERROR')
        log_function_exit(log_callback, "_create_pile_cap_aligned_grid", "error")
        raise ValueError("CASE 4 - No pile cap geometry available for grid creation")
        
    except Exception as e:
        enhanced_log(log_callback, f"ERROR: CASE 4 - Exception in grid creation: {e}", 'ERROR')
        log_function_exit(log_callback, "_create_pile_cap_aligned_grid", "error")
        raise


def _generate_aligned_grid(local_system: LocalCoordinateSystem,
                          grid_spacing: float,
                          expansion_factor: float,
                          log_callback=None) -> List[Point2D]:
    """Generate a rectangular grid aligned with the local coordinate system, prioritizing the longer axis."""
    
    # Expand dimensions
    expanded_width = local_system.long_axis_length * expansion_factor
    expanded_height = local_system.short_axis_length * expansion_factor
    
    # Calculate grid dimensions with improved alignment logic
    # Prioritize the longer axis (should be along local X-axis)
    min_cols = max(2, int(expanded_width / grid_spacing))
    min_rows = max(2, int(expanded_height / grid_spacing))
    
    # Limit grid size but ensure good coverage
    cols = max(3, min(60, min_cols + 1))
    rows = max(3, min(60, min_rows + 1))
    
    if log_callback:
        log_callback(f"CASE 4 - Grid dimensions: {rows}x{cols} (expanded: {expanded_width:.2f}m x {expanded_height:.2f}m)")
        log_callback(f"CASE 4 - Long axis: {expanded_width:.2f}m ({cols} cols), Short axis: {expanded_height:.2f}m ({rows} rows)")
    
    # Generate grid positions in local coordinates with better spacing
    local_positions = []
    
    # Use actual spacing that respects the grid_spacing parameter
    actual_spacing_x = max(grid_spacing, expanded_width / max(1, cols - 1))
    actual_spacing_y = max(grid_spacing, expanded_height / max(1, rows - 1))
    
    # Center the grid
    start_x = -expanded_width / 2
    start_y = -expanded_height / 2
    
    # Generate grid with priority for alignment along longer axis
    for row in range(rows):
        for col in range(cols):
            local_x = start_x + col * actual_spacing_x
            local_y = start_y + row * actual_spacing_y
            local_positions.append((local_x, local_y))
    
    # Convert to global coordinates
    global_positions = []
    for local_pos in local_positions:
        global_pos = local_to_global_coordinates(local_pos, local_system)
        global_positions.append(global_pos)
    
    if log_callback:
        log_callback(f"CASE 4 - Generated {len(global_positions)} grid positions")
        log_callback(f"CASE 4 - Actual spacing: X={actual_spacing_x:.3f}m, Y={actual_spacing_y:.3f}m")
    
    return global_positions



def _create_extended_grid(sub_clusters: Dict[str, Dict[str, Any]], 
                         excel_inputs: Optional[Any],
                         edge_dist: float, grid_spacing: float,
                         expansion_factor: float,
                         initial_local_system: Optional[LocalCoordinateSystem],
                         log_callback=None) -> List[Point2D]:
    """
    Create extended grid for multiple sub-clusters in Case 4.
    """
    log_function_entry(log_callback, "_create_extended_grid", 
                      clusters=len(sub_clusters), edge_dist=edge_dist, 
                      grid_spacing=grid_spacing, expansion_factor=expansion_factor)
    
    try:
        enhanced_log(log_callback, "CASE 4 - Creating extended grid for multiple clusters", 'INFO')
        
        # Collect all elements from all sub-clusters
        all_elements = {
            'columns': [],
            'walls': []
        }
        
        for cluster_name, cluster_data in sub_clusters.items():
            elements = cluster_data.get('elements', {})
            columns = elements.get('columns', [])
            walls = elements.get('walls', [])
            all_elements['columns'].extend(columns)
            all_elements['walls'].extend(walls)
            enhanced_log(log_callback, f"CASE 4 - Cluster '{cluster_name}': {len(columns)} columns, {len(walls)} walls", 'DEBUG')
        
        enhanced_log(log_callback, f"CASE 4 - Collected {len(all_elements['columns'])} columns and {len(all_elements['walls'])} walls", 'INFO')
        log_algorithm_step(log_callback, "Extended_Grid", "collect_elements", f"Total: {len(all_elements['columns'])} columns, {len(all_elements['walls'])} walls")
        
        # Try to create pile cap from collected elements
        local_system = None
        
        if excel_inputs and (all_elements['columns'] or all_elements['walls']):
            try:
                enhanced_log(log_callback, "CASE 4 - Attempting pile cap creation from all elements", 'DEBUG')
                # Create pile cap from all elements
                pile_cap_result = create_pile_cap_polygon(
                    group_elements=all_elements,
                    excel_inputs=excel_inputs,
                    site_poly=None,
                    edge_dist=edge_dist
                )
                
                enhanced_log(log_callback, f"CASE 4 - Pile cap creation result: valid={pile_cap_result['is_valid']}", 'INFO')
                log_validation_result(log_callback, "extended_pile_cap_creation", pile_cap_result['is_valid'], "Extended pile cap generation")
                
                if pile_cap_result['is_valid'] and pile_cap_result['polygon']:
                    enhanced_log(log_callback, "CASE 4 - Finding local system from extended pile cap", 'DEBUG')
                    local_system_result = find_minimum_area_bounding_rectangle(pile_cap_result['polygon'])
                    local_system = local_system_result.local_system
                    
                    # Use provided initial local system rotation if available
                    if local_system and initial_local_system:
                        enhanced_log(log_callback, f"CASE 4 - Applying initial rotation angle: {initial_local_system.rotation_angle:.3f} rad", 'DEBUG')
                        # Create a new coordinate system with the modified rotation angle
                        # since OptimalCoordinateSystem is immutable (NamedTuple)
                        local_system = local_system._replace(rotation_angle=initial_local_system.rotation_angle)
                    
                    if local_system:
                        enhanced_log(log_callback, f"CASE 4 - Using pile cap based local system for grid generation", 'INFO')
                        enhanced_log(log_callback, f"CASE 4 - Extended local system dimensions: {local_system.long_axis_length:.3f}m × {local_system.short_axis_length:.3f}m", 'DEBUG')
                        
                        grid = _generate_expanded_grid(local_system, grid_spacing, expansion_factor)
                        enhanced_log(log_callback, f"CASE 4 - Generated pile cap based grid with {len(grid)} positions", 'INFO')
                        
                        log_function_exit(log_callback, "_create_extended_grid", f"{len(grid)} positions (pile_cap_based)")
                        return grid
            except Exception as e:
                enhanced_log(log_callback, f"CASE 4 - Pile cap creation failed: {e}", 'WARNING')
                log_algorithm_step(log_callback, "Extended_Grid", "pile_cap_failed", str(e))
        
        # Fallback: Create grid from load points if pile cap creation fails
        enhanced_log(log_callback, "CASE 4 - Using fallback grid creation from load points", 'INFO')
        log_algorithm_step(log_callback, "Extended_Grid", "fallback_mode", "Creating grid from load points")
        
        # Collect all load points from sub-clusters
        all_load_points = []
        for cluster_name, cluster_data in sub_clusters.items():
            load_points = cluster_data.get('load_points', [])
            for x, y, load in load_points:
                all_load_points.append((x, y))
            enhanced_log(log_callback, f"CASE 4 - Cluster '{cluster_name}': {len(load_points)} load points", 'DEBUG')
        
        if not all_load_points:
            enhanced_log(log_callback, "ERROR: CASE 4 - No load points found for fallback grid creation", 'ERROR')
            log_function_exit(log_callback, "_create_extended_grid", "error")
            raise ValueError("CASE 4 - No load points available for grid creation")
        
        enhanced_log(log_callback, f"CASE 4 - Found {len(all_load_points)} load points for fallback grid", 'INFO')
        
        # Create a simple bounding box from load points
        x_coords = [pt[0] for pt in all_load_points]
        y_coords = [pt[1] for pt in all_load_points]
        
        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)
        
        enhanced_log(log_callback, f"CASE 4 - Load point bounds: X[{min_x:.3f}, {max_x:.3f}], Y[{min_y:.3f}, {max_y:.3f}]", 'DEBUG')
        
        # Add some padding
        padding = max(edge_dist * 2, grid_spacing * 2)
        min_x -= padding
        max_x += padding
        min_y -= padding
        max_y += padding
        
        enhanced_log(log_callback, f"CASE 4 - Padded bounds: X[{min_x:.3f}, {max_x:.3f}], Y[{min_y:.3f}, {max_y:.3f}], padding={padding:.3f}m", 'DEBUG')
        
        # Create grid positions
        grid_positions = []
        rows = 0
        cols = 0
        x = min_x
        while x <= max_x:
            y = min_y
            col_count = 0
            while y <= max_y:
                grid_positions.append((x, y))
                y += grid_spacing
                col_count += 1
            if rows == 0:
                cols = col_count
            x += grid_spacing
            rows += 1
        
        enhanced_log(log_callback, f"CASE 4 - Generated fallback grid with {len(grid_positions)} positions ({rows}×{cols})", 'INFO')
        enhanced_log(log_callback, f"CASE 4 - Grid bounds: X[{min_x:.2f}, {max_x:.2f}], Y[{min_y:.2f}, {max_y:.2f}]", 'INFO')
        
        log_calculation_result(log_callback, "fallback_grid", f"{len(grid_positions)} positions, {rows}×{cols}", "spatial")
        
        log_function_exit(log_callback, "_create_extended_grid", f"{len(grid_positions)} positions (fallback)")
        return grid_positions
        
    except Exception as e:
        enhanced_log(log_callback, f"ERROR: CASE 4 - Fallback grid creation also failed: {e}", 'ERROR')
        
        # Final fallback: Create a simple default grid
        enhanced_log(log_callback, "CASE 4 - Using final fallback: default grid", 'WARNING')
        default_grid = []
        for i in range(10):
            for j in range(10):
                default_grid.append((i * grid_spacing, j * grid_spacing))
        
        enhanced_log(log_callback, f"CASE 4 - Using default grid with {len(default_grid)} positions", 'WARNING')
        log_algorithm_step(log_callback, "Extended_Grid", "default_grid", f"{len(default_grid)} positions")
        
        log_function_exit(log_callback, "_create_extended_grid", f"{len(default_grid)} positions (default)")
        return default_grid


def _generate_expanded_grid(local_system: LocalCoordinateSystem,
                           grid_spacing: float,
                           expansion_factor: float) -> List[Point2D]:
    """Generate an expanded rectangular grid based on a local coordinate system."""
    
    # Expand dimensions
    expanded_width = local_system.long_axis_length * expansion_factor
    expanded_height = local_system.short_axis_length * expansion_factor
    
    # Calculate grid dimensions with reasonable limits
    cols = max(3, min(70, int(expanded_width / grid_spacing) + 1))  # Increased limit to 70x70 grid
    rows = max(3, min(70, int(expanded_height / grid_spacing) + 1))
    
    # Generate grid positions in local coordinates
    local_positions = []
    start_x = -expanded_width / 2
    start_y = -expanded_height / 2
    
    spacing_x = expanded_width / (cols - 1) if cols > 1 else 0
    spacing_y = expanded_height / (rows - 1) if rows > 1 else 0
    
    for row in range(rows):
        for col in range(cols):
            local_x = start_x + col * spacing_x
            local_y = start_y + row * spacing_y
            local_positions.append((local_x, local_y))
    
    # Convert to global coordinates
    global_positions = []
    for local_pos in local_positions:
        global_pos = local_to_global_coordinates(local_pos, local_system)
        global_positions.append(global_pos)
    
    return global_positions

def _filter_grid_by_site_boundary(grid_positions: List[Point2D], 
                                   site_boundary, 
                                   log_callback=None) -> List[Point2D]:
    """
    Filter grid positions to only include those within the site boundary.
    
    Args:
        grid_positions: List of 2D points representing grid positions
        site_boundary: Shapely Polygon or similar object representing site boundary
        log_callback: Optional logging callback
        
    Returns:
        List of filtered grid positions within the site boundary
    """
    log_function_entry(log_callback, "_filter_grid_by_site_boundary", 
                      grid_size=len(grid_positions), has_boundary=site_boundary is not None)
    
    try:
        if not site_boundary or not grid_positions:
            enhanced_log(log_callback, "No site boundary or grid positions provided - returning original grid", 'DEBUG')
            log_function_exit(log_callback, "_filter_grid_by_site_boundary", f"{len(grid_positions)} positions (no filtering)")
            return grid_positions
        
        # Check if site_boundary has the necessary methods
        if not hasattr(site_boundary, 'contains') or not hasattr(site_boundary, 'touches'):
            enhanced_log(log_callback, "WARNING: Site boundary object doesn't support contains/touches methods", 'WARNING')
            log_validation_result(log_callback, "boundary_method_check", False, "Missing contains/touches methods")
            log_function_exit(log_callback, "_filter_grid_by_site_boundary", f"{len(grid_positions)} positions (unsupported boundary)")
            return grid_positions
        
        enhanced_log(log_callback, f"Filtering {len(grid_positions)} positions against site boundary", 'DEBUG')
        
        from shapely.geometry import Point
        
        filtered_positions = []
        boundary_check_count = 0
        inside_count = 0
        error_count = 0
        
        for pos in grid_positions:
            try:
                # Create point from position
                point = Point(pos[0], pos[1])
                
                # Check if point is within or touches the boundary
                # Use explicit boolean checks to avoid array ambiguity
                contains_result = site_boundary.contains(point)
                touches_result = site_boundary.touches(point)
                
                # Handle potential numpy array returns by checking type
                if hasattr(contains_result, '__len__') and len(contains_result) > 1:
                    # If it's an array, use any() to get a boolean
                    contains_bool = bool(contains_result.any()) if hasattr(contains_result, 'any') else bool(contains_result)
                else:
                    contains_bool = bool(contains_result)
                
                if hasattr(touches_result, '__len__') and len(touches_result) > 1:
                    # If it's an array, use any() to get a boolean
                    touches_bool = bool(touches_result.any()) if hasattr(touches_result, 'any') else bool(touches_result)
                else:
                    touches_bool = bool(touches_result)
                
                if contains_bool or touches_bool:
                    filtered_positions.append(pos)
                    inside_count += 1
                    
            except Exception as e:
                enhanced_log(log_callback, f"WARNING: Error checking point {pos} against site boundary: {e}", 'WARNING')
                # In case of error, include the point (fail-safe approach)
                filtered_positions.append(pos)
                error_count += 1
            
            boundary_check_count += 1
            
            # Progress logging for large grids
            if log_callback and boundary_check_count % 500 == 0:
                enhanced_log(log_callback, f"Site boundary check progress: {boundary_check_count}/{len(grid_positions)} positions", 'DEBUG')
        
        removed_count = len(grid_positions) - len(filtered_positions)
        enhanced_log(log_callback, f"Site boundary filtering completed: {len(filtered_positions)}/{len(grid_positions)} positions retained", 'INFO')
        enhanced_log(log_callback, f"Filtering stats: {inside_count} inside, {removed_count} removed, {error_count} errors", 'DEBUG')
        
        log_validation_result(log_callback, "site_boundary_filtering", len(filtered_positions) > 0,
                            f"Retained {len(filtered_positions)} positions, removed {removed_count}")
        
        log_function_exit(log_callback, "_filter_grid_by_site_boundary", f"{len(filtered_positions)} positions")
        return filtered_positions
    
    except Exception as e:
        enhanced_log(log_callback, f"ERROR: Exception in site boundary filtering: {e}", 'ERROR')
        log_function_exit(log_callback, "_filter_grid_by_site_boundary", "error")
        # Return original grid in case of error (fail-safe)
        return grid_positions


def generate_case_4_possible_pile_grid(sub_clusters: Dict[str, Dict[str, Any]],
                                     min_spacing: float,
                                     edge_dist: float,
                                     initial_local_system: Optional[LocalCoordinateSystem],
                                     excel_inputs: Optional[Any],
                                     pile_diameter: float,
                                     max_pile_cap_boundary: Optional[List[Point2D]] = None,
                                     site_boundary: Optional[Any] = None,
                                     config_overrides: Optional[Dict[str, Any]] = None,
                                     log_callback=None) -> List[Point2D]:
    """
    Generate possible pile grid positions for Case 4 DXF visualization according to Rules 3.2 & 3.5.
    
    This function generates the complete grid for complex multi-element layouts,
    which is used for pre-selection visualization in DXF files.
    
    Implements Rules 3.2 & 3.5:
    - Grid generation same as Case 2.2 (grid shifting, local coordinate alignment)
    - ALL pile types (DHP, SHP, BP) must show possible pile positions as cross markers
    - Maximum Pile Cap boundaries in same layer as pile type pre-selection positions
    - Complex layout visualization for multi-cluster arrangements
    
    Args:
        sub_clusters: Dictionary of sub-cluster data containing load points and structural elements
        min_spacing: Minimum spacing for grid generation
        edge_dist: Edge distance for pile cap creation
        initial_local_system: Optional local coordinate system
        excel_inputs: Excel input data for pile cap polygon creation
        pile_diameter: Diameter of piles
        max_pile_cap_boundary: Optional maximum pile cap boundary for filtering
        site_boundary: Optional site boundary for filtering
        config_overrides: Optional configuration overrides
        log_callback: Optional logging callback
        
    Returns:
        List[Point2D]: All possible pile grid positions for Case 4 visualization
    """
    log_function_entry(log_callback, "generate_case_4_possible_pile_grid", 
                      clusters=len(sub_clusters), min_spacing=min_spacing, edge_dist=edge_dist,
                      has_max_pile_cap=max_pile_cap_boundary is not None,
                      has_site_boundary=site_boundary is not None)
    
    try:
        enhanced_log(log_callback, "CASE 4 GRID - Generating possible pile grid for DXF visualization", 'INFO')
        enhanced_log(log_callback, f"CASE 4 GRID - Clusters: {len(sub_clusters)}, Min spacing: {min_spacing:.3f}m", 'INFO')
        enhanced_log(log_callback, f"CASE 4 GRID - Edge distance: {edge_dist:.3f}m, Pile diameter: {pile_diameter:.3f}m", 'INFO')
        enhanced_log(log_callback, f"CASE 4 GRID - Has max pile cap boundary: {max_pile_cap_boundary is not None}", 'DEBUG')
        enhanced_log(log_callback, f"CASE 4 GRID - Has site boundary: {site_boundary is not None}", 'DEBUG')
        
        # Validate inputs
        if not sub_clusters:
            enhanced_log(log_callback, "ERROR: CASE 4 GRID - No sub-clusters provided", 'ERROR')
            log_function_exit(log_callback, "generate_case_4_possible_pile_grid", "error")
            return []
        
        if min_spacing <= 0:
            enhanced_log(log_callback, f"ERROR: CASE 4 GRID - Invalid min_spacing: {min_spacing}", 'ERROR')
            log_function_exit(log_callback, "generate_case_4_possible_pile_grid", "error")
            return []
        
        log_validation_result(log_callback, "input_validation", True, "All inputs validated successfully")
        
        # Use the same grid generation logic as the optimization process
        is_single_cluster = len(sub_clusters) == 1
        
        enhanced_log(log_callback, f"CASE 4 GRID - Processing mode: {'single cluster' if is_single_cluster else 'multiple clusters'}", 'INFO')
        log_algorithm_step(log_callback, "Case_4_Grid", "mode_selection", f"{'single' if is_single_cluster else 'multi'}_cluster")
        
        # Use similar configuration as optimization but for visualization
        if is_single_cluster:
            grid_spacing = max(min_spacing * 1.2, 1.5)
            expansion_factor = 2.0
            enhanced_log(log_callback, f"Single cluster grid config: spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'DEBUG')
        else:
            grid_spacing = max(min_spacing * 1.3, 1.8)
            expansion_factor = 1.8
            enhanced_log(log_callback, f"Multi cluster grid config: spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'DEBUG')
        
        # Apply configuration overrides if provided
        if config_overrides:
            enhanced_log(log_callback, "CASE 4 GRID - Applying configuration overrides", 'DEBUG')
            original_expansion = expansion_factor
            original_spacing = grid_spacing
            
            if 'grid_expansion_factor' in config_overrides:
                expansion_factor = config_overrides['grid_expansion_factor']
                enhanced_log(log_callback, f"Override expansion_factor: {original_expansion} -> {expansion_factor}", 'DEBUG')
            if 'grid_spacing' in config_overrides and config_overrides['grid_spacing']:
                grid_spacing = max(config_overrides['grid_spacing'], min_spacing)
                enhanced_log(log_callback, f"Override grid_spacing: {original_spacing:.3f} -> {grid_spacing:.3f}m", 'DEBUG')
        
        enhanced_log(log_callback, f"CASE 4 GRID - Final grid config: spacing={grid_spacing:.3f}m, expansion={expansion_factor}", 'INFO')
        log_calculation_result(log_callback, "grid_configuration", f"spacing={grid_spacing:.3f}m, expansion={expansion_factor}", "parameters")
        
        # Create grid using the same approach as optimization
        enhanced_log(log_callback, "CASE 4 GRID - Starting grid creation", 'INFO')
        
        if is_single_cluster:
            enhanced_log(log_callback, "Creating single cluster grid for visualization", 'DEBUG')
            possible_grid = _create_pile_cap_aligned_grid(
                list(sub_clusters.values())[0], excel_inputs, edge_dist, grid_spacing, 
                expansion_factor, initial_local_system, log_callback
            )
        else:
            enhanced_log(log_callback, "Creating extended grid for multiple clusters", 'DEBUG')
            possible_grid = _create_extended_grid(
                sub_clusters, excel_inputs, edge_dist, grid_spacing, 
                expansion_factor, initial_local_system, log_callback
            )
        
        if not possible_grid:
            enhanced_log(log_callback, "ERROR: CASE 4 GRID - Failed to create possible pile grid", 'ERROR')
            log_validation_result(log_callback, "grid_creation", False, "Grid creation failed")
            log_function_exit(log_callback, "generate_case_4_possible_pile_grid", "error")
            return []
        
        enhanced_log(log_callback, f"CASE 4 GRID - Initial grid created with {len(possible_grid)} positions", 'INFO')
        
        # Apply max pile cap boundary filtering if provided
        if max_pile_cap_boundary:
            enhanced_log(log_callback, "CASE 4 GRID - Applying max pile cap boundary filtering", 'INFO')
            original_count = len(possible_grid)
            possible_grid = _filter_grid_by_max_pile_cap(possible_grid, max_pile_cap_boundary, log_callback)
            removed_count = original_count - len(possible_grid)
            enhanced_log(log_callback, f"CASE 4 GRID - Max pile cap filter: {len(possible_grid)}/{original_count} positions retained", 'INFO')
            log_validation_result(log_callback, "max_pile_cap_filtering", len(possible_grid) > 0, 
                                f"Retained {len(possible_grid)} positions, removed {removed_count}")
        
        # Apply site boundary filtering if provided  
        if site_boundary:
            enhanced_log(log_callback, "CASE 4 GRID - Applying site boundary filtering", 'INFO')
            original_count = len(possible_grid)
            possible_grid = _filter_grid_by_site_boundary(possible_grid, site_boundary, log_callback)
            removed_count = original_count - len(possible_grid)
            enhanced_log(log_callback, f"CASE 4 GRID - Site boundary filter: {len(possible_grid)}/{original_count} positions retained", 'INFO')
            log_validation_result(log_callback, "site_boundary_filtering", len(possible_grid) > 0,
                                f"Retained {len(possible_grid)} positions, removed {removed_count}")
        
        # Final validation and logging
        if possible_grid:
            # Calculate grid bounds
            min_x = min(pos[0] for pos in possible_grid)
            max_x = max(pos[0] for pos in possible_grid)
            min_y = min(pos[1] for pos in possible_grid)
            max_y = max(pos[1] for pos in possible_grid)
            
            enhanced_log(log_callback, f"CASE 4 GRID - Final grid bounds: X[{min_x:.3f}, {max_x:.3f}], Y[{min_y:.3f}, {max_y:.3f}]", 'DEBUG')
            
            # Calculate grid density
            grid_area = (max_x - min_x) * (max_y - min_y)
            density = len(possible_grid) / max(grid_area, 1.0)
            enhanced_log(log_callback, f"CASE 4 GRID - Grid density: {density:.6f} positions/m²", 'DEBUG')
            
            log_calculation_result(log_callback, "final_grid_stats", 
                                 f"{len(possible_grid)} positions, area={grid_area:.2f}m², density={density:.6f}/m²", 
                                 "spatial")
        
        enhanced_log(log_callback, f"CASE 4 GRID - Generated {len(possible_grid)} possible pile positions for DXF visualization", 'INFO')
        log_validation_result(log_callback, "final_grid_generation", len(possible_grid) > 0,
                            f"Successfully generated {len(possible_grid)} positions")
        
        log_function_exit(log_callback, "generate_case_4_possible_pile_grid", f"{len(possible_grid)} positions")
        return possible_grid
        
    except Exception as e:
        enhanced_log(log_callback, f"ERROR: CASE 4 GRID - Exception in grid generation: {e}", 'ERROR')
        import traceback
        enhanced_log(log_callback, f"CASE 4 GRID - Traceback: {traceback.format_exc()}", 'ERROR')
        log_function_exit(log_callback, "generate_case_4_possible_pile_grid", "error")
        return []


def _filter_grid_by_max_pile_cap(grid_positions: List[Point2D], 
                                 max_pile_cap_boundary: List[Point2D],
                                 log_callback=None) -> List[Point2D]:
    """
    Filter grid positions to only include those within the maximum pile cap boundary.
    
    Args:
        grid_positions: List of 2D points representing grid positions
        max_pile_cap_boundary: List of 2D points representing maximum pile cap boundary
        log_callback: Optional logging callback
        
    Returns:
        List of filtered grid positions within the maximum pile cap boundary
    """
    if not max_pile_cap_boundary or len(max_pile_cap_boundary) < 3:
        return grid_positions
    
    filtered_positions = []
    
    for pos in grid_positions:
        if _point_in_polygon_case_4(pos, max_pile_cap_boundary):
            filtered_positions.append(pos)
    
    if log_callback:
        log_callback(f"CASE 4 GRID - Max pile cap filtering: {len(filtered_positions)}/{len(grid_positions)} positions retained")
    
    return filtered_positions


def _point_in_polygon_case_4(point: Point2D, polygon: List[Point2D]) -> bool:
    """
    Check if a point is inside a polygon using ray casting algorithm for Case 4.
    
    Args:
        point (Point2D): Point to check
        polygon (List[Point2D]): Polygon vertices
        
    Returns:
        bool: True if point is inside polygon
    """
    if not polygon or len(polygon) < 3:
        return True  # No boundary constraint
    
    x, y = point
    n = len(polygon)
    inside = False
    
    p1x, p1y = polygon[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside


def should_show_preselection_in_dxf_case_4(pile_type_name: str, required_piles: int) -> bool:
    """
    Determine if pre-selection visualization should be shown in DXF for Case 4.
    
    Implements Rule 3.5:
    ALL pile types (DHP, SHP, BP) must show possible pile positions as cross markers
    in DXF pre-selection visualization for Case 4 complex layouts.
    
    Args:
        pile_type_name (str): Name of pile type ('DHP', 'SHP', 'BP')
        required_piles (int): Number of required piles
        
    Returns:
        bool: Always True for Case 4 - all pile types show pre-selection
    """
    # Case 4 always shows pre-selection for all pile types as per Rule 3.5
    return True
