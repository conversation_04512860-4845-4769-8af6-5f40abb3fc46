"""
SAFE 22 Load Combinations Module

This module provides functions for handling load pattern definitions, load cases,
and load combinations in SAFE 22 finite element models. It manages the conversion
of load combination data from Excel input formats to SAFE 22 DataFrame structures.

The module handles:
- Load pattern definitions and properties
- Linear static load case definitions
- Load combination definitions and scaling factors
- Envelope combinations for ULS/SLS analysis
- Wind-specific and non-wind envelope combinations
- Settlement envelope combinations
- Design combination data for concrete frame and slab design

Functions:
    Load Patterns:
        - load_pattern_LoadPatternDefinitions_safe22: Create load pattern definitions

    Load Cases:
        - load_case_LoadCasesLinearStatic_safe22: Create linear static load cases

    Load Combinations:
        - load_comb_LoadCombinationDefinitions_safe22: Create load combinations
        - _write_primary_load_combinations_safe22: Process primary combinations
        - _create_uls_envelope_safe22: Create ULS envelope combinations
        - _create_sls_envelope_safe22: Create SLS envelope combinations
        - _create_wind_envelopes_safe22: Create wind-specific envelopes
        - _create_settlement_envelope_safe22: Create settlement envelopes

    Design Combinations:
        - load_comb_ConcFrameDesignComboData_safe22: Set frame design combinations
        - load_comb_ConcSlabDesignComboData_safe22: Set slab design combinations

"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Tuple, Any, Optional, Dict, List

from build_fem import functions


def load_pattern_LoadPatternDefinitions_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create load pattern definitions for SAFE 22 model.

    Processes load pattern data from Excel inputs and creates SAFE 22 compatible
    load pattern definitions. Each load pattern defines a specific type of loading
    (dead, live, wind, etc.) with associated properties.


    """    # Access the load pattern input data from Excel
    load_pat_input_df = excel_inputs.LoadPat

    # Extract relevant columns for load pattern definition
    # These columns contain the essential load pattern properties
    relevant_columns = ['LoadPat (Text)', 'Type (Text)', 'SelfWtMult (Unitless)']
    source_data_df = load_pat_input_df[relevant_columns].copy()

    # Create SAFE 22 format data with required fields
    intermediate_data_for_safe22 = pd.DataFrame({
        'Name': source_data_df['LoadPat (Text)'],                    # Load pattern identifier
        'Is Auto Load': 'No',                                       # User-defined load patterns (not auto-generated)
        'Type': source_data_df['Type (Text)'],                      # Load type (Dead, Live, Wind, etc.)
        'Self Weight Multiplier': source_data_df['SelfWtMult (Unitless)'],  # Self-weight scaling factor
        'Auto Load': None,                                          # Not applicable for user-defined patterns
        'GUID': None                                                # Will be populated if needed by SAFE
    }, index=source_data_df.index)  # Ensure index alignment with source data

    # Convert to target DataFrame format with proper column structure
    df_append = pd.DataFrame(
        intermediate_data_for_safe22.values,
        columns=safe22_dfs.LoadPatternDefinitions.columns,
        index=intermediate_data_for_safe22.index
    )

    # Append to existing load pattern definitions in SAFE 22 model
    safe22_dfs.LoadPatternDefinitions = pd.concat(
        [safe22_dfs.LoadPatternDefinitions, df_append], ignore_index=True
    )

    return safe22_dfs, df_append


def load_case_LoadCasesLinearStatic_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """
    Create linear static load case definitions for SAFE 22 model.

    Processes load case data from Excel inputs and creates SAFE 22 compatible
    linear static load case definitions. Each load case defines combinations of
    load patterns with specific scale factors for structural analysis.

    """
    # Create working copies to avoid modifying original data
    load_pattern_df = excel_inputs.LoadPat.copy()
    load_case_df = excel_inputs.LoadCase.copy()    # Get list of load pattern names for column identification
    load_patterns = load_pattern_df['LoadPat (Text)'].values

    search_columns = load_patterns
    
    # Find the starting column index for load pattern data
    # Create a boolean series indicating which columns match load pattern names
    matches = [col in search_columns for col in load_case_df.columns]

    # Use idxmax() to find the index of the first matching column
    column_start = pd.Series(matches).idxmax()

    # Reshape load case names for broadcasting with load pattern data
    load_cases = load_case_df['LoadCase (Text)'].values.reshape(-1, 1)
    
    # Extract load pattern scale factor data from the DataFrame
    data = load_case_df.iloc[:, column_start:]
    columns = data.columns
    
    # Create temporary DataFrame with load case-pattern combinations
    # Uses numpy broadcasting to create all combinations efficiently
    df_temp = pd.DataFrame({
        'LoadCase': np.repeat(load_cases, data.shape[1]).flatten(),  # Repeat each load case name
        'LoadPat': np.tile(columns, data.shape[0]),                 # Tile pattern names for each case
        'SF': data.values.flatten()                                 # Flatten scale factor matrix
    })
    
    # Filter out zero scale factors to keep only active load combinations
    condition = df_temp['SF'] != 0
    df_temp = df_temp[condition]

    # Create SAFE 22 format data with all required fields
    safe22_data = pd.DataFrame({
        'Name': df_temp['LoadCase'],                    # Load case identifier
        'Exclude Group': 'None',                       # No exclusion groups
        'Mass Source': 'MsSrc1',                       # Default mass source
        'Initial Condition': 'Nonlinear Case',         # Analysis starting condition
        'Nonlinear Case': 'None',                      # No initial nonlinear case
        'Load Type': 'Load',                           # Type of loading
        'Load Name': df_temp['LoadPat'],               # Referenced load pattern
        'Load SF': df_temp['SF'],                      # Scale factor for the pattern
        'Design Type': 'Program Determined',          # Let SAFE determine design type
        'GUID': None,                                  # Will be generated if needed
        'Notes': None                                  # No additional notes
    })    # Get column structure from target SAFE 22 DataFrame
    target_columns = safe22_dfs.LoadCasesLinearStatic.columns.tolist()

    # Create new DataFrame with appropriate multi-level column structure for SAFE 22
    df_append = pd.DataFrame(
        safe22_data.values,
        columns=pd.MultiIndex.from_tuples(target_columns)
    )

    # Append to existing SAFE 22 load cases
    safe22_dfs.LoadCasesLinearStatic = pd.concat(
        [safe22_dfs.LoadCasesLinearStatic, df_append],
        ignore_index=True
    )

    return safe22_dfs, df_append


def _write_primary_load_combinations_safe22(load_comb_df, load_case_df, target_columns, safe22_dfs):
    """
    Process primary load combinations and convert to SAFE 22 format.

    Takes load combination definitions from Excel and converts them to SAFE 22
    load combination format. Each combination references multiple load cases
    with specific scale factors for structural analysis.

    """
    # Get load case names for column identification
    load_case_names = load_case_df['LoadCase (Text)'].values

    # Find the index where load case columns start in the combination data
    matches = [col in load_case_names for col in load_comb_df.columns]
    if not any(matches):
        # Return empty DataFrame if no load case columns found
        return pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    column_start = pd.Series(matches).idxmax()    # Process each load combination row to create individual load case entries
    new_data = []
    for i in range(load_comb_df.shape[0]):
        # Extract combination properties from the current row
        load_comb = load_comb_df.loc[i, 'Combo (Text)']
        load_type = load_comb_df.loc[i, 'Type (Text)']

        # Process each load case column for this combination
        for j in range(column_start, load_comb_df.columns.size):
            load = load_comb_df.columns[j]  # Load case name
            scale_factor = load_comb_df.iloc[i, j]  # Scale factor for this load case

            # Skip zero scale factors to keep combinations clean
            if scale_factor != 0:
                # Create a new combination entry with all required properties
                # Format: [Name, Type, Is Auto, Load Name, SF, GUID, Notes]
                new_data.append([load_comb, load_type, 'No', load, scale_factor, None, None])

    # Convert processed data to SAFE 22 DataFrame format
    safe_load_combs = pd.DataFrame(new_data, columns=pd.MultiIndex.from_tuples(target_columns))

    # Add to SAFE 22 load combination definitions
    if not safe_load_combs.empty:
        if safe22_dfs.LoadCombinationDefinitions.empty:
            safe22_dfs.LoadCombinationDefinitions = safe_load_combs.copy()
        else:
            safe22_dfs.LoadCombinationDefinitions = pd.concat(
                [safe22_dfs.LoadCombinationDefinitions, safe_load_combs], ignore_index=True
            )
    return safe22_dfs, safe_load_combs


def _create_uls_envelope_safe22(safe_load_combs, safe22_dfs):
    """
    Create ULS (Ultimate Limit State) envelope combination.

    Creates an envelope combination that includes all ULS load combinations
    for comprehensive strength analysis. The envelope combination allows SAFE
    to find maximum and minimum values across all ULS combinations.

    """
    # Find all ULS combinations using string pattern matching
    condition = safe_load_combs[('TABLE:  Load Combination Definitions', 'Name', '')].str.contains('_ULS')
    if not condition.any():
        return    # Extract unique ULS combination names
    uls_combos = safe_load_combs.loc[
        condition, ('TABLE:  Load Combination Definitions', 'Name', '')].drop_duplicates().tolist()

    if not uls_combos:
        return

    # Create envelope data structure with all required SAFE 22 fields
    uls_envelope_data = {
        ('TABLE:  Load Combination Definitions', 'Name', ''): '_Envelope_ULS',        # Envelope identifier
        ('TABLE:  Load Combination Definitions', 'Type', ''): 'Envelope',            # SAFE envelope type
        ('TABLE:  Load Combination Definitions', 'Is Auto', ''): 'No',               # User-defined envelope
        ('TABLE:  Load Combination Definitions', 'Load Name', ''): uls_combos,       # Referenced combinations
        ('TABLE:  Load Combination Definitions', 'SF', ''): 1,                       # Unity scale factor
        ('TABLE:  Load Combination Definitions', 'GUID', ''): None,                  # No GUID needed
        ('TABLE:  Load Combination Definitions', 'Notes', ''): None,                 # No additional notes
    }

    # Expand scalar values to match the number of referenced combinations
    num_combos = len(uls_combos)
    for key, value in uls_envelope_data.items():
        if not isinstance(value, list):
            uls_envelope_data[key] = [value] * num_combos

    # Create the envelope DataFrame and add to SAFE 22 definitions
    uls_envelope = pd.DataFrame(uls_envelope_data)

    safe22_dfs.LoadCombinationDefinitions = pd.concat(
        [safe22_dfs.LoadCombinationDefinitions, uls_envelope], ignore_index=True
    )
    return safe22_dfs, uls_envelope


def _create_sls_envelope_safe22(safe_load_combs, safe22_dfs):
    """
    Create SLS (Serviceability Limit State) envelope combination.

    Creates an envelope combination that includes all SLS load combinations
    for comprehensive serviceability analysis. The envelope combination allows
    SAFE to find maximum and minimum values across all SLS combinations.

    """
    # Find all SLS combinations using string pattern matching
    condition = safe_load_combs[('TABLE:  Load Combination Definitions', 'Name', '')].str.contains('_SLS')
    if not condition.any():
        return    # Extract unique SLS combination names
    sls_combos = safe_load_combs.loc[
        condition, ('TABLE:  Load Combination Definitions', 'Name', '')].drop_duplicates().tolist()

    if not sls_combos:
        return

    # Create envelope data structure with all required SAFE 22 fields
    sls_envelope_data = {
        ('TABLE:  Load Combination Definitions', 'Name', ''): '_Envelope_SLS',        # Envelope identifier
        ('TABLE:  Load Combination Definitions', 'Type', ''): 'Envelope',            # SAFE envelope type
        ('TABLE:  Load Combination Definitions', 'Is Auto', ''): 'No',               # User-defined envelope
        ('TABLE:  Load Combination Definitions', 'Load Name', ''): sls_combos,       # Referenced combinations
        ('TABLE:  Load Combination Definitions', 'SF', ''): 1,                       # Unity scale factor
        ('TABLE:  Load Combination Definitions', 'GUID', ''): None,                  # No GUID needed
        ('TABLE:  Load Combination Definitions', 'Notes', ''): None,                 # No additional notes
    }

    # Expand scalar values to match the number of referenced combinations
    num_combos = len(sls_combos)
    for key, value in sls_envelope_data.items():
        if not isinstance(value, list):
            sls_envelope_data[key] = [value] * num_combos

    # Create the envelope DataFrame and add to SAFE 22 definitions
    sls_envelope = pd.DataFrame(sls_envelope_data)

    safe22_dfs.LoadCombinationDefinitions = pd.concat(
        [safe22_dfs.LoadCombinationDefinitions, sls_envelope], ignore_index=True
    )
    return safe22_dfs, sls_envelope


def _create_wind_envelopes_safe22(safe_load_combs, empty_template, wind_patterns, safe22_dfs):
    """
    Create wind-specific and non-wind envelope combinations.

    Creates four separate envelope combinations to analyze wind effects:
    1. ULS with wind loads only
    2. ULS without wind loads
    3. SLS with wind loads only
    4. SLS without wind loads

    This separation allows engineers to evaluate wind effects independently
    and compare with non-wind loading conditions.

    """
    # Create ULS wind envelopes (combinations that include wind loads)
    safe22_dfs, envelope_uls_wind = _create_wind_specific_envelope_safe22(
        safe_load_combs, empty_template, wind_patterns,
        '_ULS', '_Envelope_ULS_Wind', True, safe22_dfs)

    # Create ULS non-wind envelopes (combinations without wind loads)
    safe22_dfs, envelope_uls_no_wind = _create_non_wind_envelope_safe22(
        safe_load_combs, empty_template, wind_patterns,
        '_ULS', '_Envelope_ULS_NoWind', True, safe22_dfs)

    # Create SLS wind envelopes (combinations that include wind loads)
    safe22_dfs, envelope_sls_wind = _create_wind_specific_envelope_safe22(
        safe_load_combs, empty_template, wind_patterns,
        '_SLS', '_Envelope_SLS_Wind', False, safe22_dfs)

    # Create SLS non-wind envelopes (combinations without wind loads)
    safe22_dfs, envelope_sls_no_wind = _create_non_wind_envelope_safe22(
        safe_load_combs, empty_template, wind_patterns,
        '_SLS', '_Envelope_SLS_NoWind', False, safe22_dfs)

    # Combine all wind-related envelopes into a single DataFrame
    wind_envelope = pd.concat(
        [envelope_uls_wind, envelope_uls_no_wind, envelope_sls_wind, envelope_sls_no_wind], 
        ignore_index=True
    )
    return safe22_dfs, wind_envelope


def _create_wind_specific_envelope_safe22(safe_load_combs, empty_template, wind_patterns,
                                          suffix, envelope_name, is_uls, safe22_dfs):
    """
    Create envelope that includes only combinations with wind loads.

    Filters load combinations to include only those that reference wind load
    patterns, creating an envelope for wind-specific analysis. This allows
    separate evaluation of wind effects on the structure.

    """
    # Filter combinations that contain wind loads AND match the suffix (ULS/SLS)
    condition = (
            safe_load_combs[('TABLE:  Load Combination Definitions', 'Load Name', '')].isin(wind_patterns) &
            safe_load_combs[('TABLE:  Load Combination Definitions', 'Name', '')].str.contains(suffix)
    )

    if not condition.any():
        return

    # Extract unique combination names that meet the wind criteria
    wind_combos = safe_load_combs.loc[
        condition, [('TABLE:  Load Combination Definitions', 'Name', '')]
    ].drop_duplicates()[('TABLE:  Load Combination Definitions', 'Name', '')].tolist()

    if not wind_combos:
        return

    # Create envelope data structure for wind-specific combinations
    envelope_data = {
        ('TABLE:  Load Combination Definitions', 'Name', ''): envelope_name,      # Wind envelope identifier
        ('TABLE:  Load Combination Definitions', 'Type', ''): 'Envelope',        # SAFE envelope type
        ('TABLE:  Load Combination Definitions', 'Is Auto', ''): 'No',           # User-defined envelope
        ('TABLE:  Load Combination Definitions', 'Load Name', ''): wind_combos,  # Wind combinations only
        ('TABLE:  Load Combination Definitions', 'SF', ''): 1,                   # Unity scale factor
        ('TABLE:  Load Combination Definitions', 'GUID', ''): None,              # No GUID needed
        ('TABLE:  Load Combination Definitions', 'Notes', ''): None,             # No additional notes
    }    # Expand scalar values to match the number of referenced combinations
    num_combos = len(wind_combos)
    for key, value in envelope_data.items():
        if not isinstance(value, list):
            envelope_data[key] = [value] * num_combos

    # Create the wind envelope DataFrame and add to SAFE 22 definitions
    envelope = pd.DataFrame(envelope_data)

    safe22_dfs.LoadCombinationDefinitions = pd.concat(
        [safe22_dfs.LoadCombinationDefinitions, envelope], ignore_index=True
    )
    return safe22_dfs, envelope


def _create_non_wind_envelope_safe22(safe_load_combs, empty_template, wind_patterns,
                                     suffix, envelope_name, is_uls, safe22_dfs):
    """
    Create envelope that includes only combinations without wind loads.

    Filters load combinations to exclude those that reference wind load
    patterns, creating an envelope for non-wind analysis. This allows
    evaluation of structural behavior under non-wind loading conditions.

    """
    # Filter combinations that do NOT contain wind loads AND match the suffix (ULS/SLS)
    condition = (
            ~safe_load_combs[('TABLE:  Load Combination Definitions', 'Load Name', '')].isin(wind_patterns) &
            safe_load_combs[('TABLE:  Load Combination Definitions', 'Name', '')].str.contains(suffix)
    )

    if not condition.any():
        return

    # Extract unique combination names that meet the non-wind criteria
    non_wind_combos = safe_load_combs.loc[
        condition, [('TABLE:  Load Combination Definitions', 'Name', '')]
    ].drop_duplicates()[('TABLE:  Load Combination Definitions', 'Name', '')].tolist()

    if not non_wind_combos:
        return

    # Create envelope data structure for non-wind combinations
    envelope_data = {
        ('TABLE:  Load Combination Definitions', 'Name', ''): envelope_name,          # Non-wind envelope identifier
        ('TABLE:  Load Combination Definitions', 'Type', ''): 'Envelope',            # SAFE envelope type
        ('TABLE:  Load Combination Definitions', 'Is Auto', ''): 'No',               # User-defined envelope
        ('TABLE:  Load Combination Definitions', 'Load Name', ''): non_wind_combos,  # Non-wind combinations only
        ('TABLE:  Load Combination Definitions', 'SF', ''): 1,                       # Unity scale factor
        ('TABLE:  Load Combination Definitions', 'GUID', ''): None,                  # No GUID needed
        ('TABLE:  Load Combination Definitions', 'Notes', ''): None,                 # No additional notes
    }    # Expand scalar values to match the number of referenced combinations
    num_combos = len(non_wind_combos)
    for key, value in envelope_data.items():
        if not isinstance(value, list):
            envelope_data[key] = [value] * num_combos

    # Create the non-wind envelope DataFrame and add to SAFE 22 definitions
    envelope = pd.DataFrame(envelope_data)

    safe22_dfs.LoadCombinationDefinitions = pd.concat(
        [safe22_dfs.LoadCombinationDefinitions, envelope], ignore_index=True
    )

    return safe22_dfs, envelope


def _create_settlement_envelope_safe22(load_comb_df, empty_template, safe22_dfs):
    """
    Create settlement envelope combination.

    Creates an envelope combination that includes all settlement load combinations
    for comprehensive settlement analysis. Settlement combinations typically model
    differential foundation movements and their effects on the structure.

    """
    # Check if there are any settlement combinations in the original data
    condition = load_comb_df['Combo (Text)'].str.contains('_SETT')
    if not any(condition):
        return

    # Create envelope using the empty template structure
    settlement_envelope = empty_template.copy()
    
    # Populate envelope with settlement combination data
    settlement_envelope[('TABLE:  Load Combination Definitions', 'Load Name', '')] = load_comb_df.loc[
        condition, 'Combo (Text)']  # Reference all settlement combinations
    settlement_envelope[('TABLE:  Load Combination Definitions', 'Name', '')] = '_Envelope_SETT'  # Envelope identifier
    settlement_envelope[('TABLE:  Load Combination Definitions', 'Type', '')] = 'Envelope'        # SAFE envelope type
    settlement_envelope[('TABLE:  Load Combination Definitions', 'Is Auto', '')] = 'No'            # User-defined envelope
    settlement_envelope[('TABLE:  Load Combination Definitions', 'SF', '')] = 1                    # Unity scale factor
    settlement_envelope[('TABLE:  Load Combination Definitions', 'GUID', '')] = None               # No GUID needed
    settlement_envelope[('TABLE:  Load Combination Definitions', 'Notes', '')] = None              # No additional notes

    # Add settlement envelope to SAFE 22 definitions
    safe22_dfs.LoadCombinationDefinitions = pd.concat(
        [safe22_dfs.LoadCombinationDefinitions, settlement_envelope], ignore_index=True
    )
    return safe22_dfs, settlement_envelope


def load_comb_LoadCombinationDefinitions_safe22(excel_inputs, safe22_dfs):
    """
    Create comprehensive load combination definitions for SAFE 22 model.

    Processes load combination data from Excel inputs and creates a complete set
    of SAFE 22 load combinations including primary combinations, envelope combinations,
    wind-specific envelopes, and settlement envelopes. This provides a comprehensive
    analysis framework for structural design.

    """
    # Create deep copies to avoid modifying original input data
    load_pattern_df = excel_inputs.LoadPat.copy()
    load_case_df = excel_inputs.LoadCase.copy()
    load_comb_df = excel_inputs.LoadComb.copy()    # Get target column structure for SAFE 22 load combinations
    target_columns = safe22_dfs.LoadCombinationDefinitions.columns.tolist()
    empty_load_comb_df = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    # Process primary load combinations from Excel input
    safe22_dfs, safe_load_combs = _write_primary_load_combinations_safe22(
        load_comb_df, load_case_df, target_columns, safe22_dfs)

    # Identify wind load patterns for specialized envelope creation
    wind_load_patterns = functions._get_wind_load_patterns(load_pattern_df)

    # Create standard ULS and SLS envelope combinations
    safe22_dfs, uls_envelope = _create_uls_envelope_safe22(safe_load_combs, safe22_dfs)
    safe22_dfs, sls_envelope = _create_sls_envelope_safe22(safe_load_combs, safe22_dfs)    # Create wind-specific envelopes if wind patterns are present
    if len(wind_load_patterns) > 0:
        safe22_dfs, wind_envelope = _create_wind_envelopes_safe22(safe_load_combs, empty_load_comb_df,
                                                                  wind_load_patterns, safe22_dfs)
    else:
        # Create empty wind envelope if no wind patterns found
        wind_envelope = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    # Create settlement envelope for differential movement analysis
    safe22_dfs, settlement_envelope = _create_settlement_envelope_safe22(load_comb_df, empty_load_comb_df, safe22_dfs)

    # Combine all envelope types into a single output DataFrame
    df_append = pd.concat(
        [safe_load_combs, uls_envelope, sls_envelope, wind_envelope, settlement_envelope],
        ignore_index=True
    )
    return safe22_dfs, df_append


def load_comb_ConcFrameDesignComboData_safe22(excel_inputs, safe22_dfs):
    """
    Set concrete frame design combination data for SAFE 22 model.

    Configures load combinations to be used specifically for concrete frame
    design calculations. Only Ultimate Limit State (ULS) combinations are
    used for frame strength design, mapped to 'Strength' type in SAFE.

    """
    # Create working copy to avoid modifying original data
    df_load_comb = excel_inputs.LoadComb.copy()

    # Initialize DataFrame for SAFE 22 frame design combination data
    df_22 = pd.DataFrame(
        columns=['Combo Type', 'Combo Name']
    )
    
    # Filter to include only Ultimate Limit State combinations for frame design
    condition = df_load_comb['ULS/SLS'] == 'ULS'
    df_22['Combo Name'] = df_load_comb.loc[condition, 'Combo (Text)']  # Combination identifier
    df_22['Combo Type'] = df_load_comb.loc[condition, 'ULS/SLS']      # Initial type assignment
    df_22['Combo Type'] = 'Strength'                                  # Map ULS to SAFE strength type    # Get target column structure for SAFE 22 frame design combinations
    target_columns = safe22_dfs.ConcFrameDesignComboData.columns.tolist()

    # Create DataFrame with proper multi-level column structure
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )

    # Add frame design combinations to SAFE 22 model
    safe22_dfs.ConcFrameDesignComboData = pd.concat(
        [safe22_dfs.ConcFrameDesignComboData, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def load_comb_ConcSlabDesignComboData_safe22(excel_inputs, safe22_dfs):
    """
    Set concrete slab design combination data for SAFE 22 model.

    Configures load combinations to be used specifically for concrete slab
    design calculations. Both Ultimate Limit State (ULS) and Serviceability
    Limit State (SLS) combinations are used for comprehensive slab design.

    """
    # Create working copy to avoid modifying original data
    df_load_comb = excel_inputs.LoadComb.copy()

    # Initialize DataFrame for SAFE 22 slab design combination data
    df_22 = pd.DataFrame(
        columns=['Combo Type', 'Combo Name']
    )
    
    # Filter to include both ULS and SLS combinations for comprehensive slab design
    condition = (df_load_comb['ULS/SLS'] == 'ULS') | (df_load_comb['ULS/SLS'] == 'SLS')
    df_22['Combo Name'] = df_load_comb.loc[condition, 'Combo (Text)']  # Combination identifier
    df_22['Combo Type'] = df_load_comb.loc[condition, 'ULS/SLS']      # Initial type assignment
    
    # Map combination types to SAFE design categories
    df_22['Combo Type'] = df_22['Combo Type'].replace({
        'ULS': 'Strength',        # Ultimate limit state for strength design
        'SLS': 'Service Initial'  # Serviceability limit state for deflection/crack control
    })    # Get target column structure for SAFE 22 slab design combinations
    target_columns = safe22_dfs.ConcSlabDesignComboData.columns.tolist()

    # Create DataFrame with proper multi-level column structure
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Add slab design combinations to SAFE 22 model
    safe22_dfs.ConcSlabDesignComboData = pd.concat(
        [safe22_dfs.ConcSlabDesignComboData, df_append], ignore_index=True
    )
    return safe22_dfs, df_append
