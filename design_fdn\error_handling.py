import sys
import traceback
from typing import Dict, Any


def get_error_details(e: Exception, function_name: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Get detailed error information including traceback
    """
    exc_type, exc_value, exc_traceback = sys.exc_info()

    # Get the specific line where error occurred
    tb_lines = traceback.format_tb(exc_traceback)
    error_location = tb_lines[-1].strip() if tb_lines else "Unknown location"

    # Format error details
    error_details = {
        'function': function_name,
        'error_type': exc_type.__name__ if exc_type else 'Unknown',
        'error_message': str(e),
        'location': error_location,
        'full_traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    }
    
    # Add context if provided
    if context:
        error_details['context'] = context

    return error_details


def log_error(log_callback, error_details: Dict[str, Any], critical: bool = False):
    """Log error details using the provided callback
    """
    if log_callback:
        error_msg = f"ERROR in {error_details['function']}: {error_details['error_type']} - {error_details['error_message']}"
        log_callback(error_msg)
        
        if critical:
            log_callback(f"Critical error at: {error_details['location']}")
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
