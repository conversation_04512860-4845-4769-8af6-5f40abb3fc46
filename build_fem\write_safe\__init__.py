"""
SAFE File Writing Module
This module contains functionality for writing various SAFE model components including:
- Building properties and shapes
- Design strips
- Geometry
- Loads and load combinations
- Load outputs
- Materials
- Model properties
- Soil properties
"""

# Import direct modules first (no internal dependencies)
from .write_load_comb import (
    write_load_pattern,
    write_load_case,
    write_load_combination
)
from .write_material import write_material
from .write_str_prop import (
    write_beam_prop,
    write_column_prop_circular,
    write_column_prop_H,
    write_column_prop,
    write_slab_prop
)
from .write_bp_shp_dhp import write_bp_shp_dhp
from .write_design_strip import write_design_strip
from .write_soil import write_soil_prop

# Import load functions 
from .write_load import (
    write_point_load,
    write_line_load,
    write_pile_load,
    write_slab_load,
    write_lkp_load,
    write_beam_load,
    write_column_load,
    write_wall_load,
    write_corewall_load
)

# Import geometry functions separately to handle dependencies
def _import_geometry_functions():
    """Lazy import of geometry functions to avoid circular imports"""
    from .write_geometry import (
        write_point_assign,
        write_beam_assign,
        write_column_assign,
        write_wall_assign,
        write_slab_assign,
        write_lkp_assign,
        write_slab_group,
        write_line_load_assign
    )
    return (
        write_point_assign,
        write_beam_assign,
        write_column_assign,
        write_wall_assign,
        write_slab_assign,
        write_lkp_assign,
        write_slab_group,
        write_line_load_assign
    )

# Import geometry functions when needed
try:
    (write_point_assign, write_beam_assign, write_column_assign, write_wall_assign,
     write_slab_assign, write_lkp_assign, write_slab_group, write_line_load_assign) = _import_geometry_functions()
except ImportError:
    # If there are import issues, define placeholder functions
    def write_point_assign(*args, **kwargs):
        from .write_geometry import write_point_assign as _func
        return _func(*args, **kwargs)
    
    def write_beam_assign(*args, **kwargs):
        from .write_geometry import write_beam_assign as _func
        return _func(*args, **kwargs)
    
    def write_column_assign(*args, **kwargs):
        from .write_geometry import write_column_assign as _func
        return _func(*args, **kwargs)
    
    def write_wall_assign(*args, **kwargs):
        from .write_geometry import write_wall_assign as _func
        return _func(*args, **kwargs)
    
    def write_slab_assign(*args, **kwargs):
        from .write_geometry import write_slab_assign as _func
        return _func(*args, **kwargs)
    
    def write_lkp_assign(*args, **kwargs):
        from .write_geometry import write_lkp_assign as _func
        return _func(*args, **kwargs)
    
    def write_slab_group(*args, **kwargs):
        from .write_geometry import write_slab_group as _func
        return _func(*args, **kwargs)
    
    def write_line_load_assign(*args, **kwargs):
        from .write_geometry import write_line_load_assign as _func
        return _func(*args, **kwargs)

__all__ = [
    'write_point_assign',
    'write_beam_assign',
    'write_column_assign',
    'write_wall_assign',
    'write_slab_assign',
    'write_lkp_assign',
    'write_slab_group',
    'write_line_load_assign',
    'write_point_load',
    'write_line_load',
    'write_pile_load',
    'write_slab_load',
    'write_lkp_load',
    'write_beam_load',
    'write_column_load',
    'write_wall_load',
    'write_corewall_load',
    'write_load_pattern',
    'write_load_case', 
    'write_load_combination',
    'write_material',
    'write_beam_prop',
    'write_column_prop_circular',
    'write_column_prop_H',
    'write_column_prop',
    'write_slab_prop',
    'write_bp_shp_dhp',
    'write_design_strip',
    'write_soil_prop'
]