"""
SAFE Connection Management Module

This module handles establishing connections with the SAFE application,
including both local and remote connections, and instance attachment.
"""

import sys
import os

# Add the parent directory (workspace root) to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from .safe_environment import get_safe_environment


def connect_to_safe(AttachToInstance, Remote, RemoteComputer, ProgramPath, SpecifyPath, helper_obj):
    """
    Establishes connection with SAFE application.
    
    Args:
        AttachToInstance (bool): Whether to attach to existing instance
        Remote (bool): Whether to use remote connection
        RemoteComputer (str): Remote computer hostname
        ProgramPath (str): Path to SAFE program
        SpecifyPath (bool): Whether to specify program path
        helper_obj: SAFE helper object
        
    Returns:
        SAFE object or None if connection failed
    """
    # Get SAFEv1 module from environment
    _, _, SAFEv1, _, _, _, _, _, _ = get_safe_environment()
    
    safe_object = None
    if AttachToInstance:
        try:
            if Remote:
                safe_object = SAFEv1.cOAPI(helper_obj.GetObjectHost(
                    RemoteComputer, "CSI.SAFE.API.ETABSObject"))
            else:
                safe_object = SAFEv1.cOAPI(helper_obj.GetObject(
                    "CSI.SAFE.API.ETABSObject"))
        except Exception as e:
            print(f"Error attaching to SAFE instance: {e}")
            print("No running instance of the program found or failed to attach.")
            sys.exit(-1)
    else:
        if SpecifyPath:
            try:
                if Remote:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectHost(
                        RemoteComputer, ProgramPath))
                else:
                    safe_object = SAFEv1.cOAPI(
                        helper_obj.CreateObject(ProgramPath))
            except Exception as e:
                print(f"Error creating SAFE instance from path: {e}")
                print(
                    f"Cannot start a new instance of the program from {ProgramPath}")
                sys.exit(-1)
        else:
            try:
                if Remote:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectProgIDHost(
                        RemoteComputer, "CSI.SAFE.API.ETABSObject"))
                else:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectProgID(
                        "CSI.SAFE.API.ETABSObject"))
            except Exception as e:
                print(f"Error creating SAFE instance: {e}")
                print("Cannot start a new instance of the program.")
                sys.exit(-1)

        if safe_object:
            app_start_ret = safe_object.ApplicationStart()
            print(
                f"mySAFEObject.ApplicationStart() returned: {app_start_ret}")
        else:
            print("Failed to create SAFE object.")
            sys.exit(-1)
    return safe_object


def setup_safe_connection():
    """
    Setup SAFE connection using environment configuration.
    
    Returns:
        SAFE object
    """
    (clr, Marshal, SAFEv1, Remote, RemoteComputer, 
     AttachToInstance, SpecifyPath, ProgramPath, helper) = get_safe_environment()
    
    safe_object = connect_to_safe(
        AttachToInstance, Remote, RemoteComputer, ProgramPath, SpecifyPath, helper)
    
    if not safe_object:
        print("Failed to connect to SAFE. Exiting.")
        return None
        
    print("SAFE connection established.")
    return safe_object
