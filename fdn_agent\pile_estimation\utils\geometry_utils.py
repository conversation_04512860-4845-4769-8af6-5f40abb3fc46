﻿"""
Geometry Utilities for Pile Estimation

Provides common geometric operations used across multiple modules.
"""

from typing import List, Tuple, Union, Sequence, Dict, Optional, Any, Callable
import numpy as np
import pandas as pd
from shapely.geometry import Point, LineString, Polygon, MultiPolygon
from shapely.ops import unary_union, linemerge
from math import sqrt

from ..data_types import GroupElements
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)

# Type aliases
Point2D = Tuple[float, float]
Point3D = Tuple[float, float, float]


def extract_all_structural_points(group_elements: GroupElements, excel_inputs: Optional[Any],
                                 warnings_list: List[str], log_callback: Optional[Callable] = None) -> List[Tuple[float, float]]:
    """
    Extract all coordinate points from structural elements (columns and walls).

    Args:
        group_elements: Dictionary containing 'columns' and 'walls' lists
        excel_inputs: Excel inputs containing Column and Point dataframes
        warnings_list: List to append warning messages
        log_callback: Optional logging callback function

    Returns:
        List of (x, y) coordinate tuples for all structural points
    """
    log_function_entry(log_callback, "extract_all_structural_points",
                      num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])),
                      has_excel_inputs=excel_inputs is not None)

    # Input validation
    log_constraint_check(log_callback, "group_elements_provided", 1 if group_elements else 0, 1, bool(group_elements))
    if not group_elements:
        enhanced_log(log_callback, "No group elements provided", 'WARNING')
        log_function_exit(log_callback, "extract_all_structural_points", "empty_input")
        return []

    all_points = []
    initial_warnings_count = len(warnings_list)

    # Extract column points
    enhanced_log(log_callback, f"Extracting points from {len(group_elements.get('columns', []))} columns", 'DEBUG')
    with create_timed_logger(log_callback, "column_point_extraction") as timer:
        column_points = _extract_column_points(group_elements, excel_inputs, warnings_list, log_callback)
    all_points.extend(column_points)

    # Extract wall points
    enhanced_log(log_callback, f"Extracting points from {len(group_elements.get('walls', []))} walls", 'DEBUG')
    with create_timed_logger(log_callback, "wall_point_extraction") as timer:
        wall_points = _extract_wall_points(group_elements, excel_inputs, warnings_list, log_callback)
    all_points.extend(wall_points)

    # Log results
    new_warnings_count = len(warnings_list) - initial_warnings_count
    log_calculation_result(log_callback, "total_structural_points", len(all_points), "points")
    log_calculation_result(log_callback, "column_points_extracted", len(column_points), "points")
    log_calculation_result(log_callback, "wall_points_extracted", len(wall_points), "points")
    log_performance_metric(log_callback, "extraction_warnings", new_warnings_count, "warnings")

    enhanced_log(log_callback, f"Extracted {len(all_points)} total structural points ({len(column_points)} from columns, {len(wall_points)} from walls)", 'INFO')

    if new_warnings_count > 0:
        enhanced_log(log_callback, f"Point extraction completed with {new_warnings_count} warnings", 'WARNING')

    log_validation_result(log_callback, "structural_point_extraction", len(all_points) > 0,
                        f"Extracted {len(all_points)} structural points")
    log_function_exit(log_callback, "extract_all_structural_points", f"{len(all_points)} points")

    return all_points


def extract_wall_segments_from_excel(group_elements: GroupElements, excel_inputs: Optional[Any],
                                    warnings_list: List[str], log_callback: Optional[Callable] = None) -> List[Dict]:
    """
    Extract wall segments with coordinates and properties from Excel inputs.
    """
    log_function_entry(log_callback, "extract_wall_segments_from_excel",
                      num_walls=len(group_elements.get('walls', [])),
                      has_excel_inputs=excel_inputs is not None)

    wall_segments = []
    seen_walls = set()
    processed_count = 0
    skipped_count = 0
    initial_warnings_count = len(warnings_list)

    walls_list = group_elements.get('walls', [])
    enhanced_log(log_callback, f"Processing {len(walls_list)} walls for segment extraction", 'DEBUG')

    for i, wall in enumerate(walls_list):
        if not wall or len(wall) < 1:
            skipped_count += 1
            enhanced_log(log_callback, f"Skipping invalid wall at index {i}", 'WARNING')
            continue

        wall_name = wall[0]
        if wall_name in seen_walls:
            skipped_count += 1
            enhanced_log(log_callback, f"Skipping duplicate wall '{wall_name}'", 'DEBUG')
            continue
        seen_walls.add(wall_name)

        enhanced_log(log_callback, f"Processing wall {i+1}/{len(walls_list)}: '{wall_name}'", 'DEBUG')
        wall_data = _extract_wall_data_from_excel(wall_name, excel_inputs, warnings_list, log_callback)
        if wall_data:
            wall_segments.append(wall_data)
            processed_count += 1
            enhanced_log(log_callback, f"Successfully extracted data for wall '{wall_name}'", 'DEBUG')
        else:
            skipped_count += 1
            enhanced_log(log_callback, f"Failed to extract data for wall '{wall_name}'", 'WARNING')

    # Log results
    new_warnings_count = len(warnings_list) - initial_warnings_count
    log_calculation_result(log_callback, "wall_segments_extracted", len(wall_segments), "segments")
    log_performance_metric(log_callback, "walls_processed", processed_count, "walls")
    log_performance_metric(log_callback, "walls_skipped", skipped_count, "walls")
    log_performance_metric(log_callback, "wall_extraction_warnings", new_warnings_count, "warnings")

    success_rate = (processed_count / len(walls_list) * 100) if walls_list else 0
    log_performance_metric(log_callback, "wall_extraction_success_rate", success_rate, "%")

    enhanced_log(log_callback, f"Wall segment extraction completed: {len(wall_segments)} segments from {processed_count}/{len(walls_list)} walls ({success_rate:.1f}% success rate)", 'INFO')

    if new_warnings_count > 0:
        enhanced_log(log_callback, f"Wall extraction completed with {new_warnings_count} warnings", 'WARNING')

    log_validation_result(log_callback, "wall_segment_extraction", len(wall_segments) > 0,
                        f"Extracted {len(wall_segments)} wall segments")
    log_function_exit(log_callback, "extract_wall_segments_from_excel", f"{len(wall_segments)} segments")

    return wall_segments


def group_continuous_walls(wall_segments: List[Dict], log_callback: Optional[Callable] = None) -> List[List[Dict]]:
    """
    Group wall segments that are continuous (share endpoints).
    """
    log_function_entry(log_callback, "group_continuous_walls",
                      num_segments=len(wall_segments))

    if not wall_segments:
        enhanced_log(log_callback, "No wall segments provided for grouping", 'DEBUG')
        log_function_exit(log_callback, "group_continuous_walls", "empty_input")
        return []

    enhanced_log(log_callback, f"Grouping {len(wall_segments)} wall segments by connectivity", 'DEBUG')

    # Create a list to track which segments have been grouped
    ungrouped = list(range(len(wall_segments)))
    groups = []
    connections_found = 0

    with create_timed_logger(log_callback, "wall_grouping") as timer:
        while ungrouped:
            # Start a new group with the first ungrouped segment
            first_idx = ungrouped[0]
            current_group = [wall_segments[first_idx]]
            current_endpoints = set([
                tuple(wall_segments[first_idx]['coords'][0]),
                tuple(wall_segments[first_idx]['coords'][-1])
            ])
            ungrouped.remove(first_idx)

            enhanced_log(log_callback, f"Starting new group {len(groups)+1} with segment '{wall_segments[first_idx].get('name', 'unknown')}'", 'DEBUG')

            # Keep looking for connected segments
            found_connection = True
            while found_connection and ungrouped:
                found_connection = False
                for i in range(len(ungrouped)):
                    idx = ungrouped[i]
                    segment = wall_segments[idx]
                    segment_endpoints = set([
                        tuple(segment['coords'][0]),
                        tuple(segment['coords'][-1])
                    ])

                    # Check if this segment connects to any endpoint in the current group
                    if current_endpoints.intersection(segment_endpoints):
                        current_group.append(segment)
                        current_endpoints.update(segment_endpoints)
                        ungrouped.remove(idx)
                        found_connection = True
                        connections_found += 1
                        enhanced_log(log_callback, f"Connected segment '{segment.get('name', 'unknown')}' to group {len(groups)+1}", 'DEBUG')
                        break

            groups.append(current_group)
            enhanced_log(log_callback, f"Group {len(groups)} completed with {len(current_group)} segments", 'DEBUG')

    # Log results
    log_calculation_result(log_callback, "wall_groups_created", len(groups), "groups")
    log_performance_metric(log_callback, "connections_found", connections_found, "connections")

    # Calculate group size statistics
    group_sizes = [len(group) for group in groups]
    avg_group_size = sum(group_sizes) / len(group_sizes) if group_sizes else 0
    max_group_size = max(group_sizes) if group_sizes else 0

    log_performance_metric(log_callback, "average_group_size", avg_group_size, "segments")
    log_performance_metric(log_callback, "largest_group_size", max_group_size, "segments")

    enhanced_log(log_callback, f"Wall grouping completed: {len(groups)} groups created from {len(wall_segments)} segments", 'INFO')
    enhanced_log(log_callback, f"Group statistics: avg size {avg_group_size:.1f}, largest group {max_group_size} segments", 'DEBUG')

    log_validation_result(log_callback, "wall_grouping", len(groups) > 0,
                        f"Created {len(groups)} wall groups")
    log_function_exit(log_callback, "group_continuous_walls", f"{len(groups)} groups")

    return groups


def create_continuous_path(wall_group: List[Dict], log_callback: Optional[Callable] = None) -> List[Tuple[float, float]]:
    """
    Create a continuous path from a group of wall segments.

    This function takes a group of wall segments and attempts to create a single
    continuous path by connecting the segments in the correct order. It handles
    both simple cases (single segment) and complex cases (multiple segments that
    need to be merged).

    Args:
        wall_group: List of wall segment dictionaries containing 'coords' key
        log_callback: Optional callback function for logging

    Returns:
        List of (x, y) coordinate tuples representing the continuous path

    Raises:
        ValueError: If no valid wall coordinates found or path cannot be created
        GeometryError: If there are issues with the geometric operations
    """
    log_function_entry(log_callback, "create_continuous_path",
                      num_segments=len(wall_group))

    with create_timed_logger(log_callback, "continuous_path_creation") as timer:
        try:
            # Input validation
            if not wall_group:
                enhanced_log(log_callback, "Empty wall group provided", 'ERROR')
                raise ValueError("Wall group cannot be empty")

            log_validation_result(log_callback, "wall_group_provided", True,
                                f"Processing {len(wall_group)} wall segments")

            # Simple case: single segment
            if len(wall_group) == 1:
                enhanced_log(log_callback, "Single wall segment, returning coordinates directly", 'DEBUG')
                coords = wall_group[0].get('coords', [])
                if not coords:
                    enhanced_log(log_callback, "Single wall segment has no coordinates", 'ERROR')
                    raise ValueError("Wall segment has no coordinates")

                log_calculation_result(log_callback, "single_segment_path", len(coords), "points")
                log_function_exit(log_callback, "create_continuous_path", f"{len(coords)} points")
                return coords

            # Multiple segments: need to merge
            enhanced_log(log_callback, f"Processing {len(wall_group)} wall segments for merging", 'INFO')

            # Create line segments from wall coordinates
            lines = []
            valid_segments = 0
            invalid_segments = 0

            for i, wall in enumerate(wall_group):
                if 'coords' not in wall:
                    enhanced_log(log_callback, f"Wall segment {i} missing 'coords' key", 'WARNING')
                    invalid_segments += 1
                    continue

                coords = wall['coords']
                if not coords or len(coords) < 2:
                    enhanced_log(log_callback, f"Wall segment {i} has insufficient coordinates ({len(coords)} points)", 'WARNING')
                    invalid_segments += 1
                    continue

                try:
                    line = LineString(coords)
                    if line.is_valid and not line.is_empty:
                        lines.append(line)
                        valid_segments += 1
                        enhanced_log(log_callback, f"Wall segment {i}: {len(coords)} points, length {line.length:.3f}m", 'DEBUG')
                    else:
                        enhanced_log(log_callback, f"Wall segment {i} created invalid LineString", 'WARNING')
                        invalid_segments += 1
                except Exception as e:
                    enhanced_log(log_callback, f"Error creating LineString for segment {i}: {e}", 'WARNING')
                    invalid_segments += 1

            log_calculation_result(log_callback, "valid_segments", valid_segments, "segments")
            log_calculation_result(log_callback, "invalid_segments", invalid_segments, "segments")

            if not lines:
                enhanced_log(log_callback, "No valid wall coordinates found in any segment", 'ERROR')
                raise ValueError("No valid wall coordinates found")

            log_validation_result(log_callback, "valid_line_segments", True,
                                f"Created {len(lines)} valid line segments")

            # Try to merge the lines into a continuous path
            enhanced_log(log_callback, "Attempting to merge line segments into continuous path", 'DEBUG')

            try:
                merged = linemerge(lines)

                if hasattr(merged, 'coords'):
                    # Successfully merged into a single line
                    path = list(merged.coords)
                    enhanced_log(log_callback, f"Successfully merged into single continuous path with {len(path)} points", 'INFO')
                    log_calculation_result(log_callback, "merged_path_length", len(path), "points")

                elif hasattr(merged, 'geoms'):
                    # Multiple lines - need manual connection
                    enhanced_log(log_callback, f"Merge resulted in {len(list(merged.geoms))} separate lines, attempting manual connection", 'INFO')
                    path = _manually_connect_lines(list(merged.geoms), log_callback)
                    log_calculation_result(log_callback, "manually_connected_path_length", len(path), "points")

                else:
                    enhanced_log(log_callback, "Merge operation produced unexpected result type", 'ERROR')
                    raise ValueError("Unable to create continuous path from wall segments")

                # Validate the resulting path
                if len(path) < 2:
                    enhanced_log(log_callback, f"Resulting path too short: {len(path)} points", 'ERROR')
                    raise ValueError("Resulting path must have at least 2 points")

                log_validation_result(log_callback, "continuous_path_creation", True,
                                    f"Created path with {len(path)} points")

                log_function_exit(log_callback, "create_continuous_path", f"{len(path)} points")
                return path

            except Exception as e:
                enhanced_log(log_callback, f"Error during line merging: {e}", 'ERROR')
                raise ValueError(f"Unable to create continuous path: {str(e)}")

        except Exception as e:
            log_error_with_context(log_callback, e, "create_continuous_path")
            raise


def _extract_column_points(group_elements: GroupElements, excel_inputs: Optional[Any],
                          warnings_list: List[str], log_callback: Optional[Callable] = None) -> List[Tuple[float, float]]:
    """Extract points from columns using Excel data."""
    log_function_entry(log_callback, "_extract_column_points",
                      num_columns=len(group_elements.get('columns', [])),
                      has_excel_inputs=excel_inputs is not None)

    points = []
    processed_count = 0
    skipped_count = 0

    columns_list = group_elements.get('columns', [])
    enhanced_log(log_callback, f"Extracting points from {len(columns_list)} columns", 'DEBUG')

    for i, column in enumerate(columns_list):
        if not column or len(column) < 1:
            skipped_count += 1
            enhanced_log(log_callback, f"Skipping invalid column at index {i}", 'WARNING')
            continue

        column_name = column[0]
        enhanced_log(log_callback, f"Processing column {i+1}/{len(columns_list)}: '{column_name}'", 'DEBUG')

        if excel_inputs is not None:
            # Extract from Excel
            try:
                df_column = excel_inputs.Column
                df_point = excel_inputs.Point

                column_row = df_column[df_column['Column'] == column_name]
                if not column_row.empty and 'Points' in df_column.columns:
                    points_field = column_row['Points'].iloc[0]
                    if pd.notna(points_field) and points_field:
                        point_names = [p.strip() for p in points_field.split(';') if p.strip()]
                        enhanced_log(log_callback, f"Column '{column_name}' has {len(point_names)} point references", 'DEBUG')

                        for point_name in point_names:
                            point_row = df_point[df_point['Point'] == point_name]
                            if not point_row.empty:
                                x = float(point_row['X (m)'].iloc[0])
                                y = float(point_row['Y (m)'].iloc[0])
                                points.append((x, y))
                                enhanced_log(log_callback, f"Added point '{point_name}' at ({x:.2f}, {y:.2f})", 'DEBUG')
                            else:
                                warnings_list.append(f"Point '{point_name}' referenced by column '{column_name}' not found")
                                enhanced_log(log_callback, f"Point '{point_name}' not found for column '{column_name}'", 'WARNING')
                    else:
                        warnings_list.append(f"Column '{column_name}' has no valid points field")
                        enhanced_log(log_callback, f"Column '{column_name}' has no valid points field", 'WARNING')
                else:
                    warnings_list.append(f"Column '{column_name}' not found in Excel data")
                    enhanced_log(log_callback, f"Column '{column_name}' not found in Excel data", 'WARNING')

                processed_count += 1

            except Exception as e:
                skipped_count += 1
                error_msg = f"Error processing column '{column_name}': {str(e)}"
                warnings_list.append(error_msg)
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
        else:
            # Use column object attributes
            try:
                if hasattr(column, 'x') and hasattr(column, 'y'):
                    points.append((column.x, column.y))
                    enhanced_log(log_callback, f"Added column '{column_name}' point from object attributes: ({column.x:.2f}, {column.y:.2f})", 'DEBUG')
                elif len(column) >= 3:
                    x, y = float(column[1]), float(column[2])
                    points.append((x, y))
                    enhanced_log(log_callback, f"Added column '{column_name}' point from list: ({x:.2f}, {y:.2f})", 'DEBUG')
                else:
                    warnings_list.append(f"Column '{column_name}' has insufficient coordinate data")
                    enhanced_log(log_callback, f"Column '{column_name}' has insufficient coordinate data", 'WARNING')

                processed_count += 1

            except Exception as e:
                skipped_count += 1
                error_msg = f"Error extracting coordinates for column '{column_name}': {str(e)}"
                warnings_list.append(error_msg)
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')

    # Log results
    log_calculation_result(log_callback, "column_points_extracted", len(points), "points")
    log_performance_metric(log_callback, "columns_processed", processed_count, "columns")
    log_performance_metric(log_callback, "columns_skipped", skipped_count, "columns")

    success_rate = (processed_count / len(columns_list) * 100) if columns_list else 0
    log_performance_metric(log_callback, "column_processing_success_rate", success_rate, "%")

    enhanced_log(log_callback, f"Column point extraction completed: {len(points)} points from {processed_count}/{len(columns_list)} columns", 'DEBUG')

    log_function_exit(log_callback, "_extract_column_points", f"{len(points)} points")
    return points


def _extract_wall_points(group_elements: GroupElements, excel_inputs: Optional[Any],
                        warnings_list: List[str], log_callback: Optional[Callable] = None) -> List[Tuple[float, float]]:
    """Extract points from walls using Excel data."""
    log_function_entry(log_callback, "_extract_wall_points",
                      num_walls=len(group_elements.get('walls', [])),
                      has_excel_inputs=excel_inputs is not None)

    points = []
    processed_count = 0
    skipped_count = 0

    walls_list = group_elements.get('walls', [])
    enhanced_log(log_callback, f"Extracting points from {len(walls_list)} walls", 'DEBUG')

    for i, wall in enumerate(walls_list):
        if not wall or len(wall) < 1:
            skipped_count += 1
            enhanced_log(log_callback, f"Skipping invalid wall at index {i}", 'WARNING')
            continue

        wall_name = wall[0]
        enhanced_log(log_callback, f"Processing wall {i+1}/{len(walls_list)}: '{wall_name}'", 'DEBUG')

        if excel_inputs is not None:
            # Extract from Excel
            try:
                df_wall = excel_inputs.Wall
                df_point = excel_inputs.Point

                wall_row = df_wall[df_wall['Wall'] == wall_name]
                if not wall_row.empty and 'Points' in df_wall.columns:
                    points_field = wall_row['Points'].iloc[0]
                    if pd.notna(points_field) and points_field:
                        point_names = [p.strip() for p in points_field.split(';') if p.strip()]
                        enhanced_log(log_callback, f"Wall '{wall_name}' has {len(point_names)} point references", 'DEBUG')

                        for point_name in point_names:
                            point_row = df_point[df_point['Point'] == point_name]
                            if not point_row.empty:
                                x = float(point_row['X (m)'].iloc[0])
                                y = float(point_row['Y (m)'].iloc[0])
                                points.append((x, y))
                                enhanced_log(log_callback, f"Added point '{point_name}' at ({x:.2f}, {y:.2f})", 'DEBUG')
                            else:
                                warnings_list.append(f"Point '{point_name}' referenced by wall '{wall_name}' not found")
                                enhanced_log(log_callback, f"Point '{point_name}' not found for wall '{wall_name}'", 'WARNING')
                    else:
                        warnings_list.append(f"Wall '{wall_name}' has no valid points field")
                        enhanced_log(log_callback, f"Wall '{wall_name}' has no valid points field", 'WARNING')
                else:
                    warnings_list.append(f"Wall '{wall_name}' not found in Excel data")
                    enhanced_log(log_callback, f"Wall '{wall_name}' not found in Excel data", 'WARNING')

                processed_count += 1

            except Exception as e:
                skipped_count += 1
                error_msg = f"Error processing wall '{wall_name}': {str(e)}"
                warnings_list.append(error_msg)
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
        else:
            # Use wall object attributes
            try:
                coords = _extract_wall_coords(wall, log_callback)
                if coords:
                    points.extend(coords)
                    enhanced_log(log_callback, f"Added {len(coords)} points from wall '{wall_name}' object", 'DEBUG')
                else:
                    warnings_list.append(f"Wall '{wall_name}' has no extractable coordinates")
                    enhanced_log(log_callback, f"Wall '{wall_name}' has no extractable coordinates", 'WARNING')

                processed_count += 1

            except Exception as e:
                skipped_count += 1
                error_msg = f"Error extracting coordinates for wall '{wall_name}': {str(e)}"
                warnings_list.append(error_msg)
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')

    # Log results
    log_calculation_result(log_callback, "wall_points_extracted", len(points), "points")
    log_performance_metric(log_callback, "walls_processed", processed_count, "walls")
    log_performance_metric(log_callback, "walls_skipped", skipped_count, "walls")

    success_rate = (processed_count / len(walls_list) * 100) if walls_list else 0
    log_performance_metric(log_callback, "wall_processing_success_rate", success_rate, "%")

    enhanced_log(log_callback, f"Wall point extraction completed: {len(points)} points from {processed_count}/{len(walls_list)} walls", 'DEBUG')

    log_function_exit(log_callback, "_extract_wall_points", f"{len(points)} points")
    return points


def _extract_wall_data_from_excel(wall_name: str, excel_inputs: Any, warnings_list: List[str],
                                 log_callback: Optional[Callable] = None) -> Optional[Dict]:
    """
    Extract wall coordinates and thickness from Excel data.
    """
    log_function_entry(log_callback, "_extract_wall_data_from_excel",
                      wall_name=wall_name,
                      has_excel_inputs=excel_inputs is not None)

    # Input validation
    log_constraint_check(log_callback, "excel_inputs_has_wall_data",
                        1 if (excel_inputs and hasattr(excel_inputs, 'Wall') and hasattr(excel_inputs, 'Point')) else 0,
                        1,
                        excel_inputs and hasattr(excel_inputs, 'Wall') and hasattr(excel_inputs, 'Point'))

    if not hasattr(excel_inputs, 'Wall') or not hasattr(excel_inputs, 'Point'):
        enhanced_log(log_callback, f"Excel inputs missing Wall or Point dataframes for wall '{wall_name}'", 'ERROR')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "missing_dataframes")
        return None

    df_wall = excel_inputs.Wall
    df_point = excel_inputs.Point

    enhanced_log(log_callback, f"Searching for wall '{wall_name}' in {len(df_wall)} wall records", 'DEBUG')

    wall_row = df_wall[df_wall['Wall'] == wall_name]
    if wall_row.empty:
        warning_msg = f"Wall {wall_name} not found in Wall data"
        warnings_list.append(warning_msg)
        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "wall_not_found")
        return None

    # Check required columns
    required_columns = ['Points', 'Thickness (mm)']
    missing_columns = [col for col in required_columns if col not in df_wall.columns]
    if missing_columns:
        warning_msg = f"Missing required columns {missing_columns} in Wall data for {wall_name}"
        warnings_list.append(warning_msg)
        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "missing_columns")
        return None

    points_field = wall_row['Points'].iloc[0]
    thickness_mm = wall_row['Thickness (mm)'].iloc[0]

    enhanced_log(log_callback, f"Wall '{wall_name}' points field: '{points_field}', thickness: {thickness_mm} mm", 'DEBUG')

    if pd.isna(points_field) or not points_field or pd.isna(thickness_mm):
        warning_msg = f"Missing points or thickness data for wall {wall_name}"
        warnings_list.append(warning_msg)
        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "missing_data")
        return None

    # Extract point coordinates
    point_names = [p.strip() for p in points_field.split(';') if p.strip()]
    enhanced_log(log_callback, f"Wall '{wall_name}' references {len(point_names)} points: {point_names}", 'DEBUG')

    coords = []
    missing_points = []

    for point_name in point_names:
        point_row = df_point[df_point['Point'] == point_name]
        if not point_row.empty:
            x = float(point_row['X (m)'].iloc[0])
            y = float(point_row['Y (m)'].iloc[0])
            coords.append((x, y))
            enhanced_log(log_callback, f"Point '{point_name}': ({x:.2f}, {y:.2f})", 'DEBUG')
        else:
            missing_points.append(point_name)
            enhanced_log(log_callback, f"Point '{point_name}' not found in Point data", 'WARNING')

    if missing_points:
        warning_msg = f"Points {missing_points} referenced by wall '{wall_name}' not found"
        warnings_list.append(warning_msg)
        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')

    log_constraint_check(log_callback, "sufficient_coordinates", len(coords), 2, len(coords) >= 2)
    if len(coords) >= 2:
        wall_data = {
            'name': wall_name,
            'coords': coords,
            'thickness_mm': float(thickness_mm)
        }

        log_calculation_result(log_callback, f"wall_{wall_name}_coordinate_count", len(coords), "coordinates")
        log_calculation_result(log_callback, f"wall_{wall_name}_thickness", float(thickness_mm), "mm")

        enhanced_log(log_callback, f"Successfully extracted wall '{wall_name}' data: {len(coords)} coordinates, {thickness_mm} mm thickness", 'DEBUG')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "success")
        return wall_data
    else:
        warning_msg = f"Wall '{wall_name}' has insufficient coordinates ({len(coords)} < 2)"
        warnings_list.append(warning_msg)
        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
        log_function_exit(log_callback, "_extract_wall_data_from_excel", "insufficient_coordinates")
        return None


def _extract_wall_coords(wall, log_callback: Optional[Callable] = None) -> Optional[List[Tuple[float, float]]]:
    """
    Extract coordinates from wall object.
    """
    log_function_entry(log_callback, "_extract_wall_coords",
                      wall_type=type(wall).__name__,
                      wall_length=len(wall) if hasattr(wall, '__len__') else 'N/A')

    try:
        # Try object format (MockWall)
        if hasattr(wall, 'start_x') and hasattr(wall, 'start_y') and hasattr(wall, 'end_x') and hasattr(wall, 'end_y'):
            coords = [(float(wall.start_x), float(wall.start_y)), (float(wall.end_x), float(wall.end_y))]
            enhanced_log(log_callback, f"Extracted coordinates from wall object: {coords}", 'DEBUG')
            log_calculation_result(log_callback, "wall_coords_from_object", len(coords), "coordinates")
            log_function_exit(log_callback, "_extract_wall_coords", f"{len(coords)} coordinates")
            return coords
        elif isinstance(wall, (list, tuple)) and len(wall) >= 5:
            # Format: [name, start_x, start_y, end_x, end_y]
            coords = [(float(wall[1]), float(wall[2])), (float(wall[3]), float(wall[4]))]
            enhanced_log(log_callback, f"Extracted coordinates from wall list: {coords}", 'DEBUG')
            log_calculation_result(log_callback, "wall_coords_from_list", len(coords), "coordinates")
            log_function_exit(log_callback, "_extract_wall_coords", f"{len(coords)} coordinates")
            return coords
        else:
            enhanced_log(log_callback, f"Wall object format not recognized: {type(wall)}", 'WARNING')
            log_function_exit(log_callback, "_extract_wall_coords", "unrecognized_format")
            return None

    except (ValueError, TypeError, IndexError) as e:
        enhanced_log(log_callback, f"Error extracting wall coordinates: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_extract_wall_coords", "error")
        return None


def _manually_connect_lines(lines: List[LineString], log_callback: Optional[Callable] = None) -> List[Tuple[float, float]]:
    """
    Manually connect lines by finding shared endpoints.

    This function attempts to create a continuous path from multiple LineString
    objects by finding shared endpoints and connecting them in the correct order.
    It handles cases where automatic line merging fails.

    Args:
        lines: List of LineString objects to connect
        log_callback: Optional callback function for logging

    Returns:
        List of (x, y) coordinate tuples representing the connected path

    Raises:
        ValueError: If no lines provided or connection fails
    """
    log_function_entry(log_callback, "_manually_connect_lines",
                      num_lines=len(lines))

    with create_timed_logger(log_callback, "manual_line_connection") as timer:
        try:
            # Input validation
            if not lines:
                enhanced_log(log_callback, "No lines provided for connection", 'ERROR')
                raise ValueError("No lines to connect")

            log_validation_result(log_callback, "lines_provided", True,
                                f"Processing {len(lines)} lines")

            # Start with the first line
            path = list(lines[0].coords)
            remaining_lines = lines[1:]
            connections_made = 0

            enhanced_log(log_callback, f"Starting with line 0: {len(path)} points", 'DEBUG')

            # Keep adding connected lines
            while remaining_lines:
                found_connection = False
                for i, line in enumerate(remaining_lines):
                    line_coords = list(line.coords)

                    # Check if this line connects to the end of our path
                    if _points_close(path[-1], line_coords[0]):
                        enhanced_log(log_callback, f"Connecting line {i} forward to end", 'DEBUG')
                        path.extend(line_coords[1:])
                        remaining_lines.pop(i)
                        found_connection = True
                        connections_made += 1
                        break
                    elif _points_close(path[-1], line_coords[-1]):
                        enhanced_log(log_callback, f"Connecting line {i} reversed to end", 'DEBUG')
                        path.extend(reversed(line_coords[:-1]))
                        remaining_lines.pop(i)
                        found_connection = True
                        connections_made += 1
                        break
                    # Check if this line connects to the start of our path
                    elif _points_close(path[0], line_coords[-1]):
                        enhanced_log(log_callback, f"Connecting line {i} to start", 'DEBUG')
                        path = line_coords[:-1] + path
                        remaining_lines.pop(i)
                        found_connection = True
                        connections_made += 1
                        break
                    elif _points_close(path[0], line_coords[0]):
                        enhanced_log(log_callback, f"Connecting line {i} reversed to start", 'DEBUG')
                        path = list(reversed(line_coords[1:])) + path
                        remaining_lines.pop(i)
                        found_connection = True
                        connections_made += 1
                        break

                if not found_connection:
                    enhanced_log(log_callback, f"No connection found for remaining {len(remaining_lines)} lines", 'WARNING')
                    break

            log_calculation_result(log_callback, "connections_made", connections_made, "connections")
            log_calculation_result(log_callback, "final_path_points", len(path), "points")

            if remaining_lines:
                enhanced_log(log_callback, f"Manual connection completed with {len(remaining_lines)} unconnected lines", 'WARNING')
            else:
                enhanced_log(log_callback, "Manual connection completed successfully", 'INFO')

            log_function_exit(log_callback, "_manually_connect_lines", f"{len(path)} points")
            return path

        except Exception as e:
            log_error_with_context(log_callback, e, "_manually_connect_lines")
            raise


def _points_close(p1: Tuple[float, float], p2: Tuple[float, float],
                 tolerance: float = 1e-6, log_callback: Optional[Callable] = None) -> bool:
    """
    Check if two points are close enough to be considered the same.

    Args:
        p1: First point (x, y)
        p2: Second point (x, y)
        tolerance: Distance tolerance for considering points equal
        log_callback: Optional callback function for logging

    Returns:
        True if points are within tolerance distance, False otherwise
    """
    try:
        distance = sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        is_close = distance < tolerance

        if log_callback:
            enhanced_log(log_callback, f"Point distance check: {distance:.8f} < {tolerance} = {is_close}", 'DEBUG')

        return is_close

    except Exception as e:
        if log_callback:
            enhanced_log(log_callback, f"Error in point distance calculation: {e}", 'ERROR')
        return False


def distance_2d(p1: Point2D, p2: Point2D, log_callback: Optional[Callable] = None) -> float:
    """Calculate Euclidean distance between two 2D points."""
    try:
        distance = np.hypot(p2[0] - p1[0], p2[1] - p1[1])
        if log_callback and hasattr(log_callback, '__call__'):
            enhanced_log(log_callback, f"2D distance between {p1} and {p2}: {distance:.6f}", 'DEBUG')
        return distance
    except Exception as e:
        if log_callback:
            enhanced_log(log_callback, f"Error calculating 2D distance: {str(e)}", 'ERROR')
        raise


def distance_3d(p1: Point3D, p2: Point3D, log_callback: Optional[Callable] = None) -> float:
    """Calculate Euclidean distance between two 3D points."""
    try:
        distance = np.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2 + (p2[2]-p1[2])**2)
        if log_callback and hasattr(log_callback, '__call__'):
            enhanced_log(log_callback, f"3D distance between {p1} and {p2}: {distance:.6f}", 'DEBUG')
        return distance
    except Exception as e:
        if log_callback:
            enhanced_log(log_callback, f"Error calculating 3D distance: {str(e)}", 'ERROR')
        raise


def closest_point_on_line(point: Point2D, line_start: Point2D, line_end: Point2D) -> Point2D:
    """
    Find the closest point on a line segment to a given point.
    
    Args:
        point: The point to find closest position for
        line_start: Start point of the line segment
        line_end: End point of the line segment
        
    Returns:
        Closest point on the line segment
    """
    line_vec = np.array(line_end) - np.array(line_start)
    point_vec = np.array(point) - np.array(line_start)
    
    line_len = np.linalg.norm(line_vec)
    if line_len == 0:
        return line_start
        
    line_unit_vec = line_vec / line_len
    proj_length = np.dot(point_vec, line_unit_vec)
    
    if proj_length <= 0:
        return line_start
    elif proj_length >= line_len:
        return line_end
    else:
        return tuple(np.array(line_start) + proj_length * line_unit_vec)


def point_to_line_distance(point: Point2D, line_start: Point2D, line_end: Point2D) -> float:
    """
    Calculate the shortest distance from a point to a line segment.
    
    Args:
        point: The point
        line_start: Start point of the line segment
        line_end: End point of the line segment
        
    Returns:
        Shortest distance from point to line segment
    """
    closest = closest_point_on_line(point, line_start, line_end)
    return distance_2d(point, closest)


def polygon_centroid(points: List[Point2D], log_callback: Optional[Callable] = None) -> Point2D:
    """
    Calculate the centroid of a polygon defined by a list of points.

    Args:
        points: List of (x,y) points defining the polygon
        log_callback: Optional logging callback function

    Returns:
        (x, y) coordinates of the centroid
    """
    log_function_entry(log_callback, "polygon_centroid", num_points=len(points))

    log_constraint_check(log_callback, "points_not_empty", len(points), 0, len(points) > 0)
    if not points:
        error_msg = "Cannot calculate centroid of empty point list"
        enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
        log_function_exit(log_callback, "polygon_centroid", "error")
        raise ValueError(error_msg)

    if len(points) == 1:
        enhanced_log(log_callback, f"Single point centroid: {points[0]}", 'DEBUG')
        log_function_exit(log_callback, "polygon_centroid", f"single_point_{points[0]}")
        return points[0]

    # Use Shapely for robust centroid calculation
    if len(points) == 2:
        # For a line, return the midpoint
        centroid = ((points[0][0] + points[1][0]) / 2,
                   (points[0][1] + points[1][1]) / 2)
        enhanced_log(log_callback, f"Line midpoint centroid: {centroid}", 'DEBUG')
        log_calculation_result(log_callback, "line_centroid", centroid, "coordinates")
        log_function_exit(log_callback, "polygon_centroid", f"line_centroid_{centroid}")
        return centroid

    try:
        poly = Polygon(points)
        if not poly.is_valid:
            enhanced_log(log_callback, "Invalid polygon, using mean of points", 'WARNING')
            # Use mean of points
            x = sum(p[0] for p in points) / len(points)
            y = sum(p[1] for p in points) / len(points)
            centroid = (x, y)
            enhanced_log(log_callback, f"Mean centroid: {centroid}", 'DEBUG')
        else:
            centroid_point = poly.centroid
            centroid = (centroid_point.x, centroid_point.y)
            enhanced_log(log_callback, f"Polygon centroid: {centroid}", 'DEBUG')

        log_calculation_result(log_callback, "polygon_centroid", centroid, "coordinates")
        log_function_exit(log_callback, "polygon_centroid", f"centroid_{centroid}")
        return centroid

    except Exception as e:
        enhanced_log(log_callback, f"Error calculating polygon centroid: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "polygon_centroid", "error")
        raise


def is_point_in_polygon(point: Point2D, polygon: List[Point2D]) -> bool:
    """
    Check if a point is inside a polygon.
    
    Args:
        point: The point to check
        polygon: List of points defining the polygon
        
    Returns:
        True if point is inside the polygon, False otherwise
    """
    if len(polygon) < 3:
        return False
        
    shapely_point = Point(point)
    shapely_poly = Polygon(polygon)
    
    return shapely_poly.contains(shapely_point) or shapely_poly.touches(shapely_point)


def calculate_polygon_area(points: List[Point2D]) -> float:
    """
    Calculate the area of a polygon defined by points.
    
    Args:
        points: List of (x,y) points defining the polygon
        
    Returns:
        Area of the polygon
    """
    if len(points) < 3:
        return 0.0
        
    poly = Polygon(points)
    return poly.area if poly.is_valid else 0.0


def get_polygon_bounds(points: List[Point2D]) -> Tuple[float, float, float, float]:
    """
    Get the bounding box of a polygon.
    
    Args:
        points: List of (x,y) points defining the polygon
        
    Returns:
        Tuple of (min_x, min_y, max_x, max_y)
    """
    if not points:
        return (0.0, 0.0, 0.0, 0.0)
        
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    
    return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))


def offset_polygon(points: List[Point2D], distance: float) -> List[Point2D]:
    """
    Create an offset (buffer) of a polygon.
    
    Args:
        points: List of (x,y) points defining the polygon
        distance: Offset distance (positive for outward, negative for inward)
        
    Returns:
        List of points defining the offset polygon
    """
    if len(points) < 3:
        return points
        
    poly = Polygon(points)
    if not poly.is_valid:
        poly = poly.buffer(0)  # Try to fix invalid geometry
        
    buffered = poly.buffer(distance)
    
    if buffered.is_empty:
        return []
    elif hasattr(buffered, 'exterior'):
        return list(buffered.exterior.coords[:-1])  # Remove duplicate last point
    else:
        raise ValueError("Failed to create offset polygon")


def normalize_angle(angle: float) -> float:
    """
    Normalize an angle to the range [0, 2).
    
    Args:
        angle: Angle in radians
        
    Returns:
        Normalized angle in radians
    """
    return angle % (2 * np.pi)


def angle_between_points(p1: Point2D, p2: Point2D) -> float:
    """
    Calculate the angle from p1 to p2.
    
    Args:
        p1: First point
        p2: Second point
        
    Returns:
        Angle in radians
    """
    return np.arctan2(p2[1] - p1[1], p2[0] - p1[0]) 


def create_column_polygons_from_excel(excel_inputs: Any,
                                    column_names: Optional[List[str]] = None,
                                    log_callback: Optional[Callable] = None) -> Dict[str, Dict[str, Any]]:
    """
    Create column polygons from Excel inputs with column marks stored.

    Args:
        excel_inputs: Excel inputs object containing Column and Point dataframes
        column_names: Optional list of specific column names to process. If None, processes all columns.
        log_callback: Optional logging callback function

    Returns:
        Dictionary with column names as keys and values containing:
        - 'polygon': Shapely Polygon representing the column
        - 'mark': Column mark/name
        - 'centroid': (x, y) centroid coordinates
        - 'area': Column area in square meters
        - 'points': List of (x, y) coordinates used to create the polygon

    Raises:
        ValueError: If excel_inputs is invalid or required dataframes are missing
    """
    log_function_entry(log_callback, "create_column_polygons_from_excel",
                      has_excel_inputs=excel_inputs is not None,
                      specific_columns=len(column_names) if column_names else 'all')

    # Input validation
    log_constraint_check(log_callback, "excel_inputs_valid",
                        1 if (excel_inputs and hasattr(excel_inputs, 'Column') and hasattr(excel_inputs, 'Point')) else 0,
                        1,
                        excel_inputs and hasattr(excel_inputs, 'Column') and hasattr(excel_inputs, 'Point'))

    if not excel_inputs or not hasattr(excel_inputs, 'Column') or not hasattr(excel_inputs, 'Point'):
        error_msg = "excel_inputs must contain Column and Point dataframes"
        enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
        log_function_exit(log_callback, "create_column_polygons_from_excel", "error")
        raise ValueError(error_msg)

    df_column = excel_inputs.Column
    df_point = excel_inputs.Point

    enhanced_log(log_callback, f"Processing column data: {len(df_column)} column records, {len(df_point)} point records", 'DEBUG')

    if df_column.empty or df_point.empty:
        enhanced_log(log_callback, "Empty dataframes provided", 'WARNING')
        log_function_exit(log_callback, "create_column_polygons_from_excel", "empty_dataframes")
        return {}

    column_polygons = {}
    processed_count = 0
    skipped_count = 0

    # Process specific columns or all columns
    columns_to_process = column_names if column_names else df_column['Column'].tolist()
    enhanced_log(log_callback, f"Processing {len(columns_to_process)} columns", 'INFO')

    with create_timed_logger(log_callback, "column_polygon_creation") as timer:
        for i, column_name in enumerate(columns_to_process):
            enhanced_log(log_callback, f"Processing column {i+1}/{len(columns_to_process)}: '{column_name}'", 'DEBUG')

            try:
                column_row = df_column[df_column['Column'] == column_name]
                if column_row.empty:
                    skipped_count += 1
                    enhanced_log(log_callback, f"Column '{column_name}' not found in data", 'WARNING')
                    continue

                # Check if Points column exists
                if 'Points' not in df_column.columns:
                    skipped_count += 1
                    enhanced_log(log_callback, f"Points column missing for column '{column_name}'", 'WARNING')
                    continue

                points_field = column_row['Points'].iloc[0]
                if pd.isna(points_field) or not points_field:
                    skipped_count += 1
                    enhanced_log(log_callback, f"No points data for column '{column_name}'", 'WARNING')
                    continue

                # Extract point names and coordinates
                point_names = [p.strip() for p in str(points_field).split(';') if p.strip()]
                enhanced_log(log_callback, f"Column '{column_name}' references {len(point_names)} points", 'DEBUG')

                coords = []
                missing_points = []

                for point_name in point_names:
                    point_row = df_point[df_point['Point'] == point_name]
                    if not point_row.empty:
                        x = float(point_row['X (m)'].iloc[0])
                        y = float(point_row['Y (m)'].iloc[0])
                        coords.append((x, y))
                    else:
                        missing_points.append(point_name)

                if missing_points:
                    enhanced_log(log_callback, f"Missing points for column '{column_name}': {missing_points}", 'WARNING')

                # Create polygon if we have enough points
                if len(coords) >= 3:
                    poly = Polygon(coords)

                    # Validate and fix polygon if needed
                    if not poly.is_valid:
                        enhanced_log(log_callback, f"Invalid polygon for column '{column_name}', attempting to fix", 'WARNING')
                        poly = poly.buffer(0)

                    if poly.is_valid and not poly.is_empty:
                        centroid = poly.centroid
                        column_polygons[column_name] = {
                            'polygon': poly,
                            'mark': column_name,
                            'centroid': (centroid.x, centroid.y),
                            'area': poly.area,
                            'points': coords
                        }
                        processed_count += 1
                        enhanced_log(log_callback, f"Created polygon for column '{column_name}': area {poly.area:.2f} m²", 'DEBUG')
                    else:
                        skipped_count += 1
                        enhanced_log(log_callback, f"Failed to create valid polygon for column '{column_name}'", 'WARNING')
                else:
                    skipped_count += 1
                    enhanced_log(log_callback, f"Insufficient points ({len(coords)}) for column '{column_name}' polygon", 'WARNING')

            except Exception as e:
                skipped_count += 1
                enhanced_log(log_callback, f"Error processing column '{column_name}': {str(e)}", 'ERROR')

    # Log results
    log_calculation_result(log_callback, "column_polygons_created", len(column_polygons), "polygons")
    log_performance_metric(log_callback, "columns_processed", processed_count, "columns")
    log_performance_metric(log_callback, "columns_skipped", skipped_count, "columns")

    success_rate = (processed_count / len(columns_to_process) * 100) if columns_to_process else 0
    log_performance_metric(log_callback, "column_polygon_success_rate", success_rate, "%")

    enhanced_log(log_callback, f"Column polygon creation completed: {len(column_polygons)} polygons from {processed_count}/{len(columns_to_process)} columns ({success_rate:.1f}% success rate)", 'INFO')

    log_validation_result(log_callback, "column_polygon_creation", len(column_polygons) > 0,
                        f"Created {len(column_polygons)} column polygons")
    log_function_exit(log_callback, "create_column_polygons_from_excel", f"{len(column_polygons)} polygons")

    return column_polygons


def create_wall_polygons_from_excel(excel_inputs: Any, 
                                   wall_names: Optional[List[str]] = None,
                                   log_callback=None) -> Dict[str, Dict[str, Any]]:
    """
    Create wall polygons from Excel inputs with wall marks stored.
    
    Args:
        excel_inputs: Excel inputs object containing Wall and Point dataframes
        wall_names: Optional list of specific wall names to process. If None, processes all walls.
        
    Returns:
        Dictionary with wall names as keys and values containing:
        - 'polygon': Shapely Polygon representing the wall with thickness
        - 'mark': Wall mark/name
        - 'centerline': LineString representing the wall centerline
        - 'thickness_m': Wall thickness in meters
        - 'length': Wall length in meters
        - 'area': Wall area in square meters
        - 'points': List of (x, y) coordinates of the centerline
        
    Raises:
        ValueError: If excel_inputs is invalid or required dataframes are missing
    """
    if not excel_inputs or not hasattr(excel_inputs, 'Wall') or not hasattr(excel_inputs, 'Point'):
        raise ValueError("excel_inputs must contain Wall and Point dataframes")
    
    df_wall = excel_inputs.Wall
    df_point = excel_inputs.Point
    
    if df_wall.empty or df_point.empty:
        return {}
    
    wall_polygons = {}
    
    # Process specific walls or all walls
    walls_to_process = wall_names if wall_names else df_wall['Wall'].tolist()
    
    for wall_name in walls_to_process:
        wall_row = df_wall[df_wall['Wall'] == wall_name]
        if wall_row.empty:
            continue
        
        # Check required columns
        if 'Points' not in df_wall.columns or 'Thickness (mm)' not in df_wall.columns:
            continue
        
        points_field = wall_row['Points'].iloc[0]
        thickness_mm = wall_row['Thickness (mm)'].iloc[0]
        
        if pd.isna(points_field) or not points_field or pd.isna(thickness_mm):
            continue
        
        # Extract point coordinates
        point_names = [p.strip() for p in str(points_field).split(';') if p.strip()]
        coords = []
        
        for point_name in point_names:
            point_row = df_point[df_point['Point'] == point_name]
            if not point_row.empty:
                x = float(point_row['X (m)'].iloc[0])
                y = float(point_row['Y (m)'].iloc[0])
                coords.append((x, y))
        
        # Create wall polygon if we have enough points
        if len(coords) >= 2:
            thickness_m = float(thickness_mm) / 1000.0
            centerline = LineString(coords)
            
            # Create polygon by buffering the centerline
            wall_polygon = centerline.buffer(thickness_m / 2.0, cap_style=2, join_style=2)
            if wall_polygon.is_valid and not wall_polygon.is_empty:
                wall_polygons[wall_name] = {
                    'polygon': wall_polygon,
                    'mark': wall_name,
                    'centerline': centerline,
                    'thickness_m': thickness_m,
                    'length': centerline.length,
                    'area': wall_polygon.area,
                    'points': coords
                }
    
    return wall_polygons


def group_continuous_walls_from_excel(excel_inputs: Any, 
                                     wall_names: Optional[List[str]] = None,
                                     tolerance: float = 1e-6,
                                     log_callback=None) -> List[Dict[str, Any]]:
    """
    Group continuous walls from Excel inputs and create unified polygons with sharp corners.
    
    Args:
        excel_inputs: Excel inputs object containing Wall and Point dataframes
        wall_names: Optional list of specific wall names to process. If None, processes all walls.
        tolerance: Distance tolerance for connecting wall endpoints
        
    Returns:
        List of dictionaries, each containing:
        - 'polygon': Unified Shapely Polygon for the continuous wall group
        - 'marks': Combined wall marks (e.g., 'W1;W2;W3')
        - 'centerline': Continuous LineString for the wall group
        - 'total_length': Total length of all walls in the group
        - 'total_area': Total area of the unified polygon
        - 'wall_names': List of individual wall names in the group
        - 'average_thickness_m': Average thickness of walls in the group
        
    Raises:
        ValueError: If excel_inputs is invalid or required dataframes are missing
    """
    if not excel_inputs or not hasattr(excel_inputs, 'Wall') or not hasattr(excel_inputs, 'Point'):
        raise ValueError("excel_inputs must contain Wall and Point dataframes")
    
    # First get individual wall polygons
    wall_polygons = create_wall_polygons_from_excel(excel_inputs, wall_names)
    
    if not wall_polygons:
        return []
    
    # Extract wall segments for grouping
    wall_segments = []
    for wall_name, wall_data in wall_polygons.items():
        wall_segments.append({
            'name': wall_name,
            'centerline': wall_data['centerline'],
            'thickness_m': wall_data['thickness_m'],
            'polygon': wall_data['polygon'],
            'coords': wall_data['points']
        })
    
    # Group continuous segments
    wall_groups = _group_wall_segments_by_connectivity(wall_segments, tolerance)
    
    # Create unified polygons for each group
    grouped_walls = []
    for group in wall_groups:
        unified_data = _create_unified_wall_group(group, log_callback)
        if unified_data:
            grouped_walls.append(unified_data)
    
    return grouped_walls


def _group_wall_segments_by_connectivity(wall_segments: List[Dict], tolerance: float) -> List[List[Dict]]:
    """
    Group wall segments that are connected (share endpoints within tolerance).
    """
    if not wall_segments:
        return []
    
    ungrouped = list(range(len(wall_segments)))
    groups = []
    
    while ungrouped:
        # Start new group with first ungrouped segment
        current_group = [wall_segments[ungrouped[0]]]
        current_endpoints = set([
            tuple(wall_segments[ungrouped[0]]['coords'][0]),   # Start point
            tuple(wall_segments[ungrouped[0]]['coords'][-1])   # End point
        ])
        ungrouped.remove(ungrouped[0])
        
        # Find connected segments
        found_connection = True
        while found_connection and ungrouped:
            found_connection = False
            
            for i in list(ungrouped):
                segment = wall_segments[i]
                segment_start = tuple(segment['coords'][0])
                segment_end = tuple(segment['coords'][-1])
                
                # Check if this segment connects to any endpoint in current group
                connected = False
                for endpoint in current_endpoints:
                    if (_points_close(segment_start, endpoint, tolerance) or 
                        _points_close(segment_end, endpoint, tolerance)):
                        connected = True
                        break
                
                if connected:
                    current_group.append(segment)
                    current_endpoints.add(segment_start)
                    current_endpoints.add(segment_end)
                    ungrouped.remove(i)
                    found_connection = True
                    break
        
        groups.append(current_group)
    
    return groups


def _create_unified_wall_group(wall_group: List[Dict], log_callback=None) -> Dict[str, Any]:
    """
    Create a unified polygon and data for a group of connected walls.
    """
    if not wall_group:
        raise ValueError("Empty wall group")
    
    # Collect all wall polygons and union them
    wall_polygons = [wall['polygon'] for wall in wall_group]
    unified_polygon = unary_union(wall_polygons)
    
    # Handle MultiPolygon case - create convex hull to connect all parts
    if isinstance(unified_polygon, MultiPolygon):
        # Extract all coordinates from all polygons
        all_coords = []
        for geom in unified_polygon.geoms:
            if isinstance(geom, Polygon):
                all_coords.extend(list(geom.exterior.coords[:-1]))
        
        if len(all_coords) >= 3:
            from shapely.geometry import MultiPoint
            multipoint = MultiPoint(all_coords)
            unified_polygon = multipoint.convex_hull
    
    # Create continuous centerline
    centerlines = [wall['centerline'] for wall in wall_group]
    continuous_centerline = _create_continuous_centerline(centerlines)
    
    # Calculate combined properties
    wall_names = [wall['name'] for wall in wall_group]
    marks = ';'.join(wall_names)
    total_length = sum(wall['centerline'].length for wall in wall_group)
    average_thickness = sum(wall['thickness_m'] for wall in wall_group) / len(wall_group)
    
    return {
        'polygon': unified_polygon,
        'marks': marks,
        'centerline': continuous_centerline,
        'total_length': total_length,
        'total_area': unified_polygon.area,
        'wall_names': wall_names,
        'average_thickness_m': average_thickness
    }


def _create_continuous_centerline(centerlines: List[LineString]) -> LineString:
    """
    Create a continuous centerline from multiple LineString segments.
    """
    if not centerlines:
        return LineString()
    
    if len(centerlines) == 1:
        return centerlines[0]
    
    # Try to merge lines using Shapely's linemerge
    merged = linemerge(centerlines)
    
    if isinstance(merged, LineString):
        return merged
    elif hasattr(merged, 'geoms'):
        # If multiple disconnected lines, connect them manually
        return _manually_connect_centerlines(list(merged.geoms))
    else:
        raise ValueError("Unable to create continuous centerline")


def _manually_connect_centerlines(centerlines: List[LineString]) -> LineString:
    """
    Manually connect centerlines by finding shared endpoints.
    """
    if not centerlines:
        return LineString()
    
    # Start with first line
    coords = list(centerlines[0].coords)
    remaining = centerlines[1:]
    
    while remaining:
        current_start = coords[0]
        current_end = coords[-1]
        
        connected = False
        for i, line in enumerate(remaining):
            line_coords = list(line.coords)
            line_start = line_coords[0]
            line_end = line_coords[-1]
            
            # Check connections
            if _points_close(current_end, line_start):
                coords.extend(line_coords[1:])  # Skip duplicate point
                remaining.pop(i)
                connected = True
                break
            elif _points_close(current_end, line_end):
                coords.extend(reversed(line_coords[:-1]))  # Reverse and skip duplicate
                remaining.pop(i)
                connected = True
                break
            elif _points_close(current_start, line_start):
                coords = list(reversed(line_coords[1:])) + coords  # Prepend reversed
                remaining.pop(i)
                connected = True
                break
            elif _points_close(current_start, line_end):
                coords = line_coords[:-1] + coords  # Prepend
                remaining.pop(i)
                connected = True
                break
        
        if not connected:
            break  # Cannot connect remaining lines
    
    return LineString(coords)

