"""
Serviceability Limit State (SLS) Deflection Analysis for Foundation Design

This module provides comprehensive deflection analysis capabilities for foundation elements
as part of serviceability limit state design verification. The module handles various types
of deflection calculations including pile local deflections, lateral displacements, and
deflection limit checking to ensure structural performance meets design requirements.

Key Features:
- Pile local deflection analysis with coordinate transformation capabilities
- Lateral displacement calculation for foundation elements under service loads
- Deflection limit checking against serviceability criteria (25mm limit)
- P-Delta effect identification for large deflection cases
- Comprehensive load combination analysis including wind and gravity loads
- Unit conversion and data processing for engineering analysis results

Author: Foundation-Automation Development Team
Compatible with: Foundation design workflows and SAFE 16/22 analysis systems
"""

import sqlite3

import numpy as np
import pandas as pd


def cal_lateral_displacement(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Calculate comprehensive lateral displacement analysis for foundation serviceability assessment.
    
    This function processes nodal displacement results from SAFE analysis to generate
    comprehensive lateral displacement data for serviceability limit state verification.
    The analysis includes individual load cases and realistic load combinations that
    represent actual service conditions for foundation elements.

    The analysis workflow includes:
    - Load pattern identification and extraction from input definitions
    - Individual load case displacement processing
    - Load combination generation for realistic service conditions
    - Unit conversion and data formatting for engineering analysis
    - Database storage for result management and design integration
    """    
    # Extract complete nodal displacement results from SAFE analysis
    # This contains displacement data for all analysis points and load cases
    df_nodal_displacements = safe_mdbs.NodalDisplacements.copy()
    
    # Extract load pattern definitions for identifying specific load types
    df_load_pat = excel_inputs.LoadPat

    # Initialize dictionary to store load pattern mappings for different load types
    # This enables systematic processing of various load categories
    load_patterns = {}
    
    # Define fundamental load types for comprehensive foundation analysis
    load_types = ['SOIL']
    
    # Extract single load pattern for each fundamental load type
    for load_type in load_types:
        mask = df_load_pat['Load Type'] == load_type
        if mask.any():
            # Take first matching pattern for each load type (assumes single pattern per type)
            load_patterns[load_type] = df_load_pat.loc[mask, 'LoadPat (Text)'].values[0]

    # Extract wind load patterns (multiple patterns possible for different directions)
    # Wind loads typically include multiple directions (±X, ±Y) for comprehensive analysis
    wind_mask = df_load_pat['Load Type'] == 'WIND'
    load_patterns['WIND'] = df_load_pat.loc[wind_mask, 'LoadPat (Text)'].tolist()

    # Create list of all required load cases for displacement extraction
    # Flatten the load pattern structure to handle both single patterns and lists
    required_cases = list(load_patterns.values())
    required_cases = [item for sublist in (required_cases if isinstance(required_cases, list) else [required_cases])
                     for item in (sublist if isinstance(sublist, list) else [sublist])]

    # Filter displacement data to include only required load cases
    # This reduces data volume and focuses on relevant analysis cases
    df_lateral_displacement = df_nodal_displacements[
        df_nodal_displacements['OutputCase'].isin(required_cases)
    ].copy()

    # Prepare base load cases for load combination calculations
    # Separate wind loads from other loads for combination processing
    base_loads = {k: v for k, v in load_patterns.items() if k != 'WIND'}
    base_dfs = {}

    # Extract displacement data for each base load type
    # Create separate DataFrames for efficient combination calculations
    for load_type, load_case in base_loads.items():
        mask = df_nodal_displacements['OutputCase'] == load_case
        # Extract only essential displacement columns for combination calculations
        base_dfs[load_type] = df_nodal_displacements.loc[mask, ['Point', 'Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz']]

    # Process wind load combinations with base loads
    # Generate realistic service load combinations including wind effects
    combined_results = []
    for wind in load_patterns['WIND']:
        # Extract wind displacement data for current wind direction/case
        wind_df = df_nodal_displacements[df_nodal_displacements['OutputCase'] == wind][
            ['Point', 'Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz']
        ]

        # Combine all base loads with current wind load case
        # This creates realistic service load combinations for analysis
        all_dfs = list(base_dfs.values()) + [wind_df]
        df_concat = pd.concat(all_dfs, ignore_index=True)
        
        # Sum displacement components by point to create load combination effects
        # This represents algebraic addition of displacement effects
        df_sum = df_concat.groupby('Point', as_index=False).sum()

        # Add metadata to identify the load combination case
        df_sum['Node'] = df_sum['Point']  # Maintain compatibility with analysis results format
        
        # Create descriptive load combination name for tracking and reporting
        df_sum['OutputCase'] = '+'.join([base_loads[k] for k in ['SOIL']] + [wind])
        df_sum['CaseType'] = 'LinStatic'  # Identify as linear static analysis combination
        combined_results.append(df_sum)

    # Combine individual load cases with load combination results
    # This creates comprehensive displacement dataset for serviceability analysis
    df_lateral_displacement = pd.concat([df_lateral_displacement] + combined_results, ignore_index=True)

    # Apply standard unit conversions and precision rounding for engineering analysis
    # This ensures consistent units and appropriate precision for design calculations
    _apply_unit_conversions_and_rounding(df_lateral_displacement)

    # Store processed results in excel_outputs for downstream analysis
    excel_outputs.LateralDisplacement = df_lateral_displacement.copy()

    # Save comprehensive displacement results to SQLite database
    # This enables efficient retrieval and integration with other analysis modules
    with sqlite3.connect(file_paths.ExcelOutputLateralDisplacement) as conn:
        excel_outputs.LateralDisplacement.to_sql('ExcelOutputLateralDisplacement', conn, if_exists='replace', index=False)

    # Log successful completion of lateral displacement calculations
    if log_callback:
        log_callback("")
        log_callback("✅ Lateral displacement calculations completed successfully")
        log_callback("   • Foundation displacement analysis finalized for serviceability verification")
        log_callback("   • Results prepared for deflection compliance assessment")
    return excel_outputs


def check_pile_deflection(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Perform comprehensive pile deflection limit checking for serviceability verification.
    
    This function analyzes pile top deflections against established serviceability limits
    to verify foundation performance under service loads. The analysis identifies piles
    requiring P-Delta analysis consideration and provides comprehensive reporting for
    design decision-making and iteration.

    The checking workflow includes:
    - Pile top point identification and displacement extraction
    - Combined lateral deflection calculation (resultant of X and Y components)
    - Deflection limit checking against 25mm serviceability criterion
    - P-Delta effect identification for large deflection cases
    - Comprehensive failure reporting and design guidance
    """    
    # Filter lateral displacement data to focus on pile top points only
    # Pile top points are identified by '_T' suffix and represent critical deflection locations
    df_lateral_displacement = excel_outputs.LateralDisplacement[
        excel_outputs.LateralDisplacement['Point'].str.contains('_T', na=False)
    ].copy()

    # Calculate combined lateral deflection magnitude using Pythagorean theorem
    # This represents the resultant lateral displacement considering both X and Y components
    df_lateral_displacement['Uxy (mm)'] = np.sqrt(
        df_lateral_displacement['Ux (mm)']**2 + df_lateral_displacement['Uy (mm)']**2
    )

    # Apply serviceability deflection limit check (25mm criterion)
    # This limit ensures acceptable foundation performance under service loads
    df_lateral_displacement['Check (<=25mm)'] = np.where(
        df_lateral_displacement['Uxy (mm)'] <= 25, 'OK', 'NOT OK'
    )

    # Calculate maximum combined deflection for overall performance assessment
    # This provides global measure of foundation deflection performance
    uxy_max = df_lateral_displacement['Uxy (mm)'].max()
    if log_callback:
        log_callback("")
        log_callback("🔍 SERVICEABILITY VERIFICATION - Pile Deflection Analysis")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   Evaluating pile lateral deflection compliance with serviceability limits")
        log_callback(f"   • Maximum lateral deflection detected: {uxy_max:.2f} mm")
        log_callback("   • Serviceability limit: ≤25.0 mm (standard deflection criteria)")
        log_callback("   • P-Delta threshold: >25.0 mm requires secondary moment analysis")

    # Identify pile points that exceed the deflection limit (failures)
    # These points require design attention and potential P-Delta analysis consideration
    df_fail = df_lateral_displacement[df_lateral_displacement['Uxy (mm)'] > 25].copy()

    # Report overall deflection check results with appropriate engineering guidance
    if len(df_fail) > 0:
        if log_callback:
            log_callback("")
            log_callback("❌ FAIL: Pile deflection exceeds serviceability limits")
            log_callback(f"   • Detected deflection {uxy_max:.2f} mm > 25.0 mm limit")
            log_callback("   • CRITICAL: P-Delta effects must be included in structural analysis")
            log_callback("   • DESIGN ACTION REQUIRED: Enhanced foundation stiffness or P-Delta analysis")
            log_callback("   • Consider: Increased pile diameter, additional piles, or rigorous P-Delta evaluation")
    else:
        if log_callback:
            log_callback("")
            log_callback("✅ PASS: Pile deflection complies with serviceability requirements")
            log_callback(f"   • Detected deflection {uxy_max:.2f} mm ≤ 25.0 mm limit")
            log_callback("   • Foundation provides adequate lateral stiffness under service loads")
            log_callback("   • P-Delta effects negligible - linear analysis remains valid")

    # Export comprehensive deflection analysis results to CSV files
    # Complete results file contains all pile top deflections with pass/fail status
    df_lateral_displacement.to_csv(file_paths.ResultPileDeflection, index=False)
    
    # Failure summary file focuses on deflection limit violations for design review
    df_fail.to_csv(file_paths.ResultPileDeflectionFail, index=False)

    # Update design results container with deflection analysis outcomes
    design_results.PileDeflection = df_lateral_displacement.copy()
    design_results.PileDeflectionFail = df_fail.copy()

    # Process piles requiring P-Delta analysis consideration (for large deflections)
    if len(df_fail) > 0:
        # Extract maximum deflection case for each pile to identify worst-case scenarios
        # This focuses on the most critical deflection condition for each pile element
        max_indices = df_fail.groupby('Point')['Uxy (mm)'].idxmax()
        df_pile_fail = df_fail.loc[max_indices].copy()
        
        # Convert point identifier to pile identifier for design reference
        # Remove '_T' suffix to get base pile identifier for design team use
        df_pile_fail['Pile'] = df_pile_fail['Point'].str.replace('_T', '', regex=True)
        df_pile_fail.drop('Point', axis=1, inplace=True)  # Remove point column to avoid confusion
        
        # Store piles requiring P-Delta analysis in design results
        design_results.PileNeedsPDelta = df_pile_fail
        
        # Export P-Delta requirement list for design team reference and action
        df_pile_fail.to_csv(file_paths.ResultPileNeedsPDelta, index=False)

    return design_results


def _apply_unit_conversions_and_rounding(df):
    """
    Apply standardized unit conversions and precision rounding to displacement DataFrames.
    
    This utility function provides consistent unit conversion and rounding procedures
    for displacement analysis results. The function converts displacement values to
    engineering units (millimeters) and applies appropriate precision rounding for
    different types of data to ensure consistent presentation across all analysis results.

    Unit Conversions Applied:
    - Displacement components (Ux, Uy, Uz): Converted from meters to millimeters (×1000)
    - Rotation components (Rx, Ry, Rz): Maintained in radians with descriptive column names
    
    Precision Rounding Applied:
    - Displacement values: 2 decimal places (0.01 mm precision suitable for engineering)
    - Rotation values: 6 decimal places (high precision for small angular values)
    """    
    # Convert displacement components from meters to millimeters for engineering analysis
    # Standard engineering practice uses millimeters for structural displacement reporting
    for col in ['Ux', 'Uy', 'Uz']:
        if col in df.columns:
            df[f'{col} (mm)'] = df[col] * 1000  # Convert m to mm
            df.drop(col, axis=1, inplace=True)  # Remove original column to avoid confusion

    # Rename rotation columns to include units for clear identification
    # Rotations remain in radians as this is standard for structural analysis
    for col in ['Rx', 'Ry', 'Rz']:
        if col in df.columns:
            df.rename(columns={col: f'{col} (Rad)'}, inplace=True)

    # Apply appropriate precision rounding for different data types
    # Displacement precision: 0.01 mm suitable for structural engineering applications
    mm_cols = [col for col in df.columns if '(mm)' in col]
    
    # Rotation precision: 6 decimal places appropriate for small angular measurements
    rad_cols = [col for col in df.columns if '(Rad)' in col]

    # Execute rounding operations with appropriate precision levels
    if mm_cols:
        df[mm_cols] = df[mm_cols].round(2)  # 2 decimal places for displacement values
    if rad_cols:
        df[rad_cols] = df[rad_cols].round(6)  # 6 decimal places for rotation values
