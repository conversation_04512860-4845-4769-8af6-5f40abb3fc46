﻿"""
Visualizer Module

Functions for creating DXF visualizations of pile estimation results.
"""

from .core import (
    create_pile_estimation_dxf, 
    create_pile_estimation_dxf_with_possible_positions,
    create_comprehensive_pile_preselection_dxf
)
from .validation import (
    ValidationError,
    validate_pile_estimation_results,
    debug_group_results,
    validate_pile_cap_polygon
)
from .text_manager import TextPlacementManager
from .pile_drawer import (
    draw_dhp_pile,
    draw_shp_pile, 
    draw_bp_pile,
    draw_pile_by_type,
    get_pile_drawing_bounds
)

__all__ = [
    'create_pile_estimation_dxf',
    'create_pile_estimation_dxf_with_possible_positions',
    'create_comprehensive_pile_preselection_dxf',
    'ValidationError',
    'validate_pile_estimation_results',
    'debug_group_results', 
    'validate_pile_cap_polygon',
    'TextPlacementManager',
    'draw_dhp_pile',
    'draw_shp_pile',
    'draw_bp_pile', 
    'draw_pile_by_type',
    'get_pile_drawing_bounds'
]

