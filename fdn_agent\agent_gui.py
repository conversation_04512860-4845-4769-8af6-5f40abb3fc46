import os
import sys
import threading
import tkinter as tk
from datetime import datetime
from pathlib import Path  # Import pathlib for better path handling
from tkinter import filedialog, Tk, Toplevel, StringVar, IntVar, messagebox
from tkinter import ttk  # Import ttk for improved widgets

import email_notifications.notification as _notification  # Import Notification module
import fdn_agent.agent_main as fdn_agent_main

# Import new components
from .components.base_gui import BaseGUI
from .components.file_management import FileManagementFrame
from .components.pile_types import PileTypesManager

class FdnAgentGUI(BaseGUI):
    def __init__(self, parent, user_type="Base", user_email=None, username=None):
        # Initialize base GUI
        super().__init__(parent, "AI Agent - Pile Layout Optimization", "700x500")
        
        # Initialize application-specific variables
        self.file_paths = None
        self.excel_inputs = None
        self.excel_outputs = None
        self.user_type = user_type  # Store user type
        self.user_email = user_email  # Store the user's email address
        self.progress_var = StringVar()
        self.progress_var.set("Ready")
        
        # Path variables
        self.folder_path_var = StringVar()
        self.existing_folder_path_var = StringVar()
        self.output_dir_var = StringVar()
        self.edge_dist_var = tk.DoubleVar(value=0.4)
        
        # Variables for pile estimation functionality (kept for compatibility)
        self.pile_capacity_var = None
        self.pile_diameter_var = None
        self.min_spacing_var = None
        self.selected_pile_types = []  # Will store user-selected pile types for optimization

        # Use passed username if available, otherwise get it from system
        self.username = username if username else self.get_username()        # Create GUI elements
        self.create_ui()
        
    def create_ui(self):
        # Create main container with scrollbar    def create_ui(self):
        """Create the main user interface using modular components"""
        # Create main container with scrollbar
        main_container = ttk.Frame(self.window)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create canvas and scrollbar
        canvas = tk.Canvas(main_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Create main frame inside scrollable area
        main_frame = ttk.Frame(scrollable_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create file management component
        self.file_manager = FileManagementFrame(
            main_frame, 
            self.folder_path_var, 
            self.existing_folder_path_var, 
            self.output_dir_var, 
            self.log_to_console,
            self.initialize_setup
        )
        
        # Actions frame with better styling
        actions_frame = ttk.LabelFrame(
            main_frame, text="Configuration & Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create pile types manager component
        self.pile_types_manager = PileTypesManager(actions_frame, self.log_to_console)
        self.pile_types_manager.create_ui()
        
        # Create optimization section
        self._create_optimization_section(actions_frame)
        
        # Create progress section
        self._create_progress_section(main_frame)
        
        # Create log section
        self._create_log_section(main_frame)
        
        # Create controls section
        self._create_controls_section(main_frame)
    
    def _create_optimization_section(self, parent):
        """Create AI optimization section"""
        # Pile Estimation Options frame with better styling
        pile_frame = ttk.LabelFrame(
            parent, text="AI Pile Layout Optimization", padding="8")
        pile_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Info section with better formatting
        info_frame = ttk.Frame(pile_frame)
        info_frame.pack(fill=tk.X, pady=(0, 8))
        
        info_label = ttk.Label(info_frame, 
            text="🤖 AI Agent will:\n"
                 "1. Analyze each column/wall cluster for optimal pile type (DHP → SHP → BP priority)\n"
                 "2. Create enlarged pile cap with 3m expansion for possible pile grid\n"
                 "3. Pre-select best pile type based on capacity requirements\n"
                 "4. Run NSGA-III optimization with pre-selected pile type and grid\n"
                 "5. Generate optimized pile layout with AutoCAD DXF visualization", 
            font=("Arial", 9), foreground="blue", wraplength=600, justify=tk.LEFT)
        info_label.pack(anchor=tk.W)

        # Edge Distance parameter in a neat frame
        params_frame = ttk.LabelFrame(pile_frame, text="Parameters", padding="5")
        params_frame.pack(fill=tk.X, pady=(0, 8))
        
        edge_frame = ttk.Frame(params_frame)
        edge_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(edge_frame, text="Edge Distance (m):", width=18, anchor="w").pack(side=tk.LEFT, padx=(0, 5))
        edge_dist_entry = ttk.Entry(edge_frame, textvariable=self.edge_dist_var, width=12)
        edge_dist_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(edge_frame, 
            text="(Distance from structural elements to pile cap edge)", 
            font=("Arial", 8), foreground="gray").pack(side=tk.LEFT)

        # Output directory with improved layout
        output_frame = ttk.LabelFrame(pile_frame, text="Output Settings", padding="5")
        output_frame.pack(fill=tk.X, pady=(0, 8))
        
        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(output_dir_frame, text="Output Directory:", width=18, anchor="w").pack(side=tk.LEFT, padx=(0, 5))
        self.output_dir_var.set("Select input folder to auto-set output directory")
        output_entry = ttk.Entry(output_dir_frame, textvariable=self.output_dir_var, font=("Arial", 9))
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(output_dir_frame, text="Browse...", width=12, 
                  command=self.file_manager.browse_output_directory).pack(side=tk.LEFT)
        
        # Action button with better styling
        action_frame = ttk.Frame(pile_frame)
        action_frame.pack(fill=tk.X, pady=(5, 0))
        
        action_btn = ttk.Button(action_frame, 
                               text="2. Run AI Pile Type Pre-Selection + NSGA-III Optimization with AutoCAD DXF",
                               command=self.run_pile_estimation_with_visualization)
        action_btn.pack(pady=8)
    
    def _create_progress_section(self, parent):
        """Create progress monitoring section"""
        # Progress frame with better styling
        progress_frame = ttk.LabelFrame(
            parent, text="Progress Monitor", padding="8")
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        # Progress bar
        self.progress_bar = ttk.Progressbar(
            progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate', style="TProgressbar")
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        # Status label with better styling
        self.status_var.set("Please select input folder to begin")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var, 
                                font=("Arial", 9), foreground="navy")
        status_label.pack(fill=tk.X)
    
    def _create_log_section(self, parent):
        """Create log display section with dual-output logging information"""
        # Status frame with text widget - make it more compact
        status_frame = ttk.LabelFrame(parent, text="Status Log - Milestone & Error Messages", padding="5")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Add information about dual-output logging
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X, pady=(0, 5))

        info_label = ttk.Label(info_frame,
            text="ℹ️ This window shows milestone progress and error messages. Complete logs are saved to text files.",
            font=("Arial", 8), foreground="blue", wraplength=600)
        info_label.pack(anchor=tk.W)

        # Status text widget with scrollbar
        status_text_frame = ttk.Frame(status_frame)
        status_text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(
            status_text_frame, wrap=tk.WORD, width=80, height=8, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(status_text_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_controls_section(self, parent):
        """Create control buttons section"""
        # Control buttons frame with better layout
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="8")
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        # Left side buttons
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="Clear Log", 
                   command=self.clear_log, width=15).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Save Log", 
                   command=self.save_log, width=15).pack(side=tk.LEFT, padx=(0, 5))
        
        # Right side button
        ttk.Button(button_frame, text="Close",                   command=self.window.destroy, width=15).pack(side=tk.RIGHT)
    
    def initialize_setup(self):
        folder_path = self.folder_path_var.get().strip()
        existing_folder_path = self.existing_folder_path_var.get().strip()

        if not folder_path:
            messagebox.showerror(
                "Error", "Please select an input folder first")
            return

        try:
            self.status_var.set("Initializing file paths...")
            self.log_to_console("Starting file path initialization...")
            self.log_to_console(f"Using input folder: {folder_path}")

            # Start operation in a separate thread to avoid freezing the GUI
            threading.Thread(target=lambda: self.initialize_setup_thread(folder_path, existing_folder_path),
                             daemon=True).start()

        except Exception as e:
            # This part is unlikely to be hit if the thread is spawned correctly,
            # but if it is, it's on the main thread.
            self.status_var.set(f"Error during setup initiation: {str(e)}") # Keep this simple status
            self._show_error(f"Failed to initiate file path initialization: {str(e)}", e)

    def log_to_console(self, message, log_level=None):
        """
        Enhanced dual-output logging system.

        This method implements a dual-output approach:
        1. GUI Status Window: Shows only WARNING, ERROR, CRITICAL levels for better UX
        2. Text File Logging: Writes all log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL) to file
        3. Terminal Output: Shows only ERROR, WARNING, CRITICAL levels

        Maintains backward compatibility with existing log_callback functions.
        """
        # Auto-detect log level if not provided (backward compatibility)
        if log_level is None:
            log_level = self._auto_detect_log_level(message)

        # Validate log level
        valid_levels = {self.LOG_DEBUG, self.LOG_INFO, self.LOG_WARNING, self.LOG_ERROR, self.LOG_CRITICAL}
        if log_level not in valid_levels:
            log_level = self.LOG_INFO  # Default to INFO for invalid levels

        # Create formatted messages
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_log_message = f"[{timestamp}] [{log_level}] {message}"

        # 1. Terminal Output: Only ERROR/WARNING/CRITICAL messages
        should_print_to_terminal = log_level in self.TERMINAL_LOG_LEVELS
        if should_print_to_terminal:
            print(full_log_message)

        # 2. File Logging: Write ALL messages to log file (real-time logging)
        self._write_to_log_file(full_log_message)

        # 3. GUI Status Window: Show milestone messages and error messages
        # Include INFO level messages that are milestones (major progress updates)
        gui_display_levels = {self.LOG_WARNING, self.LOG_ERROR, self.LOG_CRITICAL}
        is_milestone_message = self._is_milestone_message(message, log_level)
        should_display_in_gui = log_level in gui_display_levels or is_milestone_message

        def _update_gui_log():
            if should_display_in_gui:
                self.log_text.configure(state="normal")
                self.log_text.insert("end", f"{full_log_message}\n")
                self.log_text.see("end")  # Auto-scroll to the end
                self.log_text.configure(state="disabled")
                # Update the window to show the new text immediately
                self.window.update_idletasks()

        # Check if we're on the main thread - MUST be on main thread
        if threading.current_thread() == threading.main_thread():
            _update_gui_log()
        else:
            # Schedule the update on the main thread
            self.window.after(0, _update_gui_log)

    def _write_to_log_file(self, formatted_message):
        """
        Write log message to file in real-time.

        This method provides comprehensive file logging for all messages,
        ensuring complete audit trails and debugging information are preserved.

        Args:
            formatted_message (str): The fully formatted log message with timestamp and level
        """
        try:
            # Only write to file if we have a valid log file path
            if hasattr(self, 'file_paths') and self.file_paths and hasattr(self.file_paths, 'Log') and self.file_paths.Log:
                log_file_path = Path(self.file_paths.Log)

                # Ensure the directory exists
                log_file_path.parent.mkdir(parents=True, exist_ok=True)

                # Append the message to the log file with UTF-8 encoding
                with open(log_file_path, 'a', encoding='utf-8') as log_file:
                    log_file.write(f"{formatted_message}\n")
                    log_file.flush()  # Ensure immediate write to disk

        except Exception as e:
            # If file logging fails, don't disrupt the application
            # Just print to terminal as fallback (avoid infinite recursion)
            try:
                error_msg = f"[{datetime.now().strftime('%H:%M:%S')}] [ERROR] Failed to write to log file: {str(e)}"
                print(error_msg)
            except:
                # Ultimate fallback - silently continue if even terminal output fails
                pass

    def _is_milestone_message(self, message, log_level):
        """
        Determine if a message represents a critical milestone that should be displayed in GUI.

        This method identifies important progress updates and major workflow steps
        that users should see in the status window for better user experience.

        Args:
            message (str): The log message content
            log_level (str): The log level of the message

        Returns:
            bool: True if message should be displayed in GUI status window
        """
        # Always show WARNING, ERROR, CRITICAL regardless of content
        if log_level in {self.LOG_WARNING, self.LOG_ERROR, self.LOG_CRITICAL}:
            return True

        # For INFO level messages, check if they are milestone messages
        if log_level == self.LOG_INFO:
            message_lower = message.lower()

            # Major workflow milestones
            milestone_keywords = [
                'starting', 'completed', 'finished', 'success', 'failed',
                'initialized', 'optimization', 'analysis', 'generation',
                'processing', 'validation', 'results', 'summary',
                'ai agent', 'pile estimation', 'nsga-iii', 'dxf',
                'excel', 'workflow', 'coordinate', 'preselection'
            ]

            # Check for milestone indicators
            if any(keyword in message_lower for keyword in milestone_keywords):
                return True

            # Check for progress indicators (percentages, counts)
            if any(indicator in message_lower for indicator in ['%', 'step', 'phase', 'stage']):
                return True

            # Check for file operations that users care about
            if any(file_op in message_lower for file_op in ['saved', 'created', 'loaded', 'exported']):
                return True

        # DEBUG level messages are generally not milestones for GUI display
        return False

    def _auto_detect_log_level(self, message):
        """
        Auto-detect log level from message content for backward compatibility.

        This method analyzes message content to determine the appropriate log level
        when no explicit level is provided.

        Args:
            message (str): The log message to analyze

        Returns:
            str: Detected log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        message_lower = message.lower()

        # Critical level: fatal errors, system failures
        critical_keywords = ['critical', 'fatal', 'system failure', 'critical failure']
        if any(keyword in message_lower for keyword in critical_keywords):
            return self.LOG_CRITICAL

        # Error level: errors, failures, exceptions
        error_keywords = ['error', 'failed', 'failure', 'exception', '❌']
        if any(keyword in message_lower for keyword in error_keywords):
            return self.LOG_ERROR

        # Warning level: warnings, cautions, important notices
        warning_keywords = ['warning', 'caution', 'attention', 'important', 'note:']
        if any(keyword in message_lower for keyword in warning_keywords):
            return self.LOG_WARNING

        # Debug level: debug info, detailed progress, processing details
        debug_keywords = ['debug:', 'processing', '...', 'analyzing', 'calculating']
        if any(keyword in message_lower for keyword in debug_keywords):
            return self.LOG_DEBUG

        # Default to INFO level for general messages
        return self.LOG_INFO

    def clear_log(self):
        """Clear the log text widget"""
        self.log_text.configure(state="normal")
        self.log_text.delete(1.0, "end")
        self.log_text.configure(state="disabled")

    def _save_log_to_path(self, file_path):
        """Save the contents of the log text widget to a specific file path."""
        log_content = self.log_text.get(1.0, "end-1c") # Get content, excluding the final newline
        try:
            # Ensure the directory exists
            path_obj = Path(file_path)
            path_obj.parent.mkdir(parents=True, exist_ok=True)
            with open(path_obj, 'w', encoding='utf-8') as file:
                file.write(log_content)
            self.log_to_console(f"Log successfully saved to: {file_path}", self.LOG_INFO)
        except Exception as e:
            error_message = f"Failed to save log to {file_path}: {str(e)}"
            self.log_to_console(error_message, self.LOG_ERROR) # Log error to console
            # Show error to user, this is on main thread if save_log calls it
            # or scheduled on main thread if _auto_save_log_action calls it via window.after
            messagebox.showerror("Save Log Error", error_message)

    def _auto_save_log_action(self):
        """Action to automatically save the log."""
        if hasattr(self.file_paths, 'Log') and self.file_paths.Log:
            self._save_log_to_path(self.file_paths.Log)
        else:
            self.log_to_console("Log file path not defined in file_paths. Auto-save skipped.", self.LOG_WARNING)

    def save_log(self):
        """Save the contents of the log text widget to a file chosen by the user."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt"), ("Log files", "*.log"), ("All Files", "*.*")],
            title="Save Log As"
        )
        if filename:
            self._save_log_to_path(filename) # Use the new method

    def update_progress(self, percent, description=""):
        """Update the progress bar and progress description (thread-safe)"""
        def _update_progress():
            self.progress_bar["value"] = percent
            self.status_var.set(f"{description} ({percent}%)")
            self.window.update_idletasks()  # Force UI update
        
        # Check if we're on the main thread - MUST be on main thread
        if threading.current_thread() == threading.main_thread():
            _update_progress()
        else:
            # Schedule the update on the main thread
            self.window.after(0, _update_progress)

    def initialize_setup_thread(self, folder_path, existing_folder_path):
        """Initialize setup in background thread with proper error handling."""
        try:
            # Call the main program function directly - NO FALLBACKS ALLOWED
            self.file_paths, self.excel_inputs, self.excel_outputs = fdn_agent_main.automated_initialize_setup(
                folder_path,
                existing_folder_path,
                self.log_to_console
            )
            
            if self.file_paths and self.excel_inputs and self.excel_outputs:
                # Update UI from the main thread
                self.window.after(0, lambda: self.status_var.set(
                    "Excel file initialized successfully!"))
                self.window.after(0, lambda: messagebox.showinfo(
                    "Success", "Excel file initialized successfully!"))
            else:
                self.window.after(0, lambda: self.status_var.set(
                    "Excel file initialization failed - invalid results"))
                self.window.after(0, lambda: messagebox.showerror(
                    "Error", "Excel file initialization failed - no valid results returned"))

        except Exception as e:
            # STRICT ERROR HANDLING - NO FALLBACKS ALLOWED
            error_msg = f"Failed to initialize file paths: {str(e)}"
            self.window.after(0, lambda: self.status_var.set(f"FAILED: {str(e)}"))
            self.window.after(0, lambda: self._show_error(error_msg, e))
            # Re-raise to ensure complete failure
            raise
          # Always auto-save log
        self.window.after(0, self._auto_save_log_action)
    
    def get_selected_pile_types_for_optimization(self):
        """Get selected pile types data for optimization using the pile types manager"""
        return self.pile_types_manager.get_selected_pile_types_for_optimization()
    
    def run_pile_estimation_with_visualization(self):
        """
        Run pile estimation analysis with enhanced AutoCAD DXF visualization using AI-optimized pile layout.
        
        This method uses the improved pile cap geometry system that:
        - Groups continuous wall segments into polylines
        - Creates more accurate pile cap boundaries by buffering wall polylines
        - Provides better handling of complex structural geometries
        - Generates professional AutoCAD DXF visualization files
        - Uses AI Agent to optimize pile layout with user-selected pile types
        """
        if not self.excel_inputs:
            messagebox.showerror("Error", "Please initialize input data first")
            return
        
        # Get selected pile types for optimization
        selected_pile_types = self.get_selected_pile_types_for_optimization()
        
        if not selected_pile_types:
            messagebox.showerror("Error", "Please select at least one pile type for optimization")
            return
            
        try:
            # Get edge distance
            edge_dist = float(self.edge_dist_var.get())
            output_dir = self.output_dir_var.get().strip()
            
            # Validate parameters
            if edge_dist <= 0:
                messagebox.showerror("Error", "Edge distance must be greater than 0")
                return
            
            # Validate output directory
            if not output_dir:
                messagebox.showerror("Error", "Please select an output directory")
                return
            
            # Test write permissions for the output directory
            try:
                output_path = Path(output_dir)
                # Create the directory if it doesn't exist
                output_path.mkdir(parents=True, exist_ok=True)
                
                # Test write permission by creating a temporary file
                test_file = output_path / "test_write_permission.tmp"
                test_file.write_text("test")
                test_file.unlink()  # Remove the test file
                
                self.log_to_console(f" Output directory validated: {output_dir}")
                
            except PermissionError as e:
                error_msg = f"Permission denied for selected directory: {output_dir}"
                self.log_to_console(f"❌ {error_msg}")
                messagebox.showerror("Directory Access Error", error_msg)
                raise PermissionError(error_msg) from e
                
            except Exception as e:
                error_msg = f"Cannot access output directory: {str(e)}"
                self.log_to_console(f"❌ {error_msg}")
                messagebox.showerror("Directory Error", error_msg)
                return
            
            self.status_var.set("Running AI pile layout optimization with AutoCAD DXF visualization...")
            self.log_to_console("Starting AI pile layout optimization with AutoCAD DXF visualization...")
            self.log_to_console("Using improved wall polyline buffering for accurate pile cap geometry...")
            self.log_to_console(f"AI Agent will optimize layout using {len(selected_pile_types)} pile type(s):")
              # Run pile estimation with visualization in a separate thread
            # to avoid blocking the GUI
            threading.Thread(target=lambda: self.run_pile_estimation_with_visualization_thread(
                selected_pile_types, edge_dist, output_dir
            ), daemon=True).start()
                
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numeric values for edge distance")
        except Exception as e:
            self._show_error(f"Failed to start AI pile layout optimization with AutoCAD DXF visualization: {str(e)}", e)

    def run_pile_estimation_with_visualization_thread(self, selected_pile_types, edge_dist, output_dir):
        """Run pile estimation with AI optimization in background thread with proper error handling."""
        try:
            # Call the main program function directly - NO FALLBACKS ALLOWED
            results = fdn_agent_main.run_pile_estimation_with_multi_type_optimization(
                self.excel_inputs,
                selected_pile_types=selected_pile_types,
                edge_dist=edge_dist,
                optimization_method="cost_efficiency",  # Direct method specification
                output_dir=output_dir,
                log_callback=self.log_to_console
            )

            if results and results.get('success'):
                # Format results message using the main program function directly
                success_msg = fdn_agent_main.format_optimization_results_message(results)
                
                self.window.after(0, lambda: self.status_var.set("AI pile layout optimization with AutoCAD DXF visualization completed successfully"))
                self.window.after(0, lambda: messagebox.showinfo("🤖 AI Pile Layout Optimization Complete", success_msg))
                
                # Send usage notification directly
                fdn_agent_main.send_usage_notification(self.username, self.user_type, self.user_email, self.log_to_console)
            else:
                error_msg = "AI pile layout optimization failed: " + results.get('error', 'No results returned')
                self.window.after(0, lambda: self.status_var.set("AI pile layout optimization failed"))
                self.window.after(0, lambda: messagebox.showerror("Error", error_msg))
                
        except Exception as e:
            # STRICT ERROR HANDLING - NO FALLBACKS ALLOWED
            error_msg = f"AI pile layout optimization with AutoCAD DXF visualization failed: {str(e)}"
            self.window.after(0, lambda: self.status_var.set("SYSTEM FAILURE"))
            self.log_to_console(f"CRITICAL FAILURE: {error_msg}", self.LOG_CRITICAL)
            self.window.after(0, lambda: messagebox.showerror("System Failure", error_msg))
            # Re-raise to ensure complete failure
            raise

    def browse_output_directory(self):
        """Opens file dialog to select output directory for grouping test results"""
        # Default to input folder if available
        initial_dir = self.folder_path_var.get().strip() if self.folder_path_var.get().strip() else None
        
        directory_path = filedialog.askdirectory(
            title="Select Output Directory (should be within Input Folder)",
            initialdir=initial_dir
        )
        if directory_path:
            # Convert to Path object and then back to string for consistent path handling
            directory_path = str(Path(directory_path))
            
            # Check if the selected directory is within the input folder
            input_folder = self.folder_path_var.get().strip()
            if input_folder:
                input_path = Path(input_folder)
                output_path = Path(directory_path)
                
                # Check if output directory is within input directory
                try:
                    output_path.relative_to(input_path)
                    self.output_dir_var.set(directory_path)
                    self.log_to_console(f"Selected output directory: {directory_path}")
                except ValueError:
                    # Output directory is not within input directory
                    messagebox.showwarning(
                        "Invalid Directory", 
                        f"Output directory must be within the input folder:\n{input_folder}\n\nPlease select a directory inside the input folder."
                    )
                    self.log_to_console(f"Warning: Selected directory {directory_path} is not within input folder {input_folder}", self.LOG_WARNING)
            else:
                # No input folder selected yet, allow any directory
                self.output_dir_var.set(directory_path)
                self.log_to_console(f"Selected output directory: {directory_path}", self.LOG_INFO)
                self.log_to_console("Note: Please select an input folder first to ensure output is within the correct location.", self.LOG_WARNING)

    def _show_error(self, message, exception_instance=None):
        """Show error message to user and log it"""
        self.log_to_console(f"ERROR: {message}", self.LOG_ERROR)
        if exception_instance:
            self.log_to_console(f"Exception details: {str(exception_instance)}", self.LOG_ERROR)

        # Show error dialog to user
        messagebox.showerror("Error", message)

def fdn_agent():
    root = Tk()
    root.withdraw()  # Hide the root window
    app = FdnAgentGUI(root)
    root.mainloop()


if __name__ == "__main__":
    # Run the GUI application
    fdn_agent()
    # Note: The GUI will handle its own mainloop, so no need to call root.mainloop() here.
    # The script can be run directly to launch the GUI.