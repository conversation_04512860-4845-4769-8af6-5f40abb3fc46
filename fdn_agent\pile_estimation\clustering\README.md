# Clustering Package for Pile Estimation

This package provides core functionalities for analyzing structural elements within pile foundation design, focusing on 3D grouping and load-based clustering.

## Overview

The `clustering` package is responsible for:
- Grouping structural elements (columns and walls) in 3D space.
- Performing load-based sub-clustering within these groups to identify distinct load patterns and areas.
- Providing helper utilities for geometry and clustering operations.

## Key Modules

- `core_grouping.py`: Contains the main functions for 3D grouping of structural elements using hierarchical clustering, including methods for fixed and auto-threshold grouping.
- `load_clustering.py`: Implements load-based sub-clustering using K-Means, distributing piles among sub-clusters based on load ratios.
- `element.py`: Defines the internal `_Element` class used for representing structural elements and provides utilities like 3D centroid calculation.
- `helpers.py`: Provides helper functions for preprocessing elements, computing distance matrices, finding optimal clustering thresholds, and forming element groups.
- `__init__.py`: Exports key functions from the sub-modules for easy access. 