"""
Unit tests for VolumeCalculator module.
"""

import unittest
import pandas as pd
import numpy as np
import trimesh

from soil_rock_cone.volume_calculator import VolumeCalculator, VolumeResult
from soil_rock_cone.geometry_engine import GeometryEngine, PileGeometry


class TestVolumeCalculator(unittest.TestCase):
    """Test cases for VolumeCalculator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.calculator = VolumeCalculator()
        self.geometry_engine = GeometryEngine(mesh_resolution=16)
        
        # Create test geometry
        self.pile_params = {
            'pile_id': 'TEST_P1',
            'x_coord': 10.0,
            'y_coord': 20.0,
            'top_elevation': 100.0,
            'bottom_elevation': 80.0,
            'radius': 0.5,
            'pile_length': 20.0,
            'projection_angle': 15.0,
            'frustum_base_radius': 1.5
        }
        
        self.test_geometry = self.geometry_engine.create_pile_geometry(self.pile_params)
        
    def test_initialization(self):
        """Test VolumeCalculator initialization."""
        calculator = VolumeCalculator()
        self.assertIsInstance(calculator, VolumeCalculator)
        
    def test_calculate_mesh_volume(self):
        """Test mesh volume calculation."""
        # Create a simple cylinder
        cylinder = self.geometry_engine._create_cylinder((0, 0, 0), 1.0, 10.0)
        volume = self.calculator._calculate_mesh_volume(cylinder)
        
        # Expected volume: π * r² * h = π * 1² * 10 ≈ 31.416
        expected_volume = np.pi * 1.0**2 * 10.0
        self.assertAlmostEqual(volume, expected_volume, places=1)
        
    def test_calculate_mesh_volume_empty(self):
        """Test volume calculation with empty mesh."""
        volume = self.calculator._calculate_mesh_volume(None)
        self.assertEqual(volume, 0.0)
        
    def test_calculate_pile_volume(self):
        """Test pile volume calculation."""
        result = self.calculator.calculate_pile_volume(self.test_geometry)
        
        self.assertIsInstance(result, VolumeResult)
        self.assertEqual(result.pile_id, 'TEST_P1')
        self.assertGreater(result.part1_volume, 0)
        self.assertGreater(result.part2_volume, 0)
        self.assertGreater(result.part3_volume, 0)
        self.assertGreater(result.total_volume, 0)
        self.assertEqual(result.overlap_volume, 0.0)  # Initial value
        
        # Total should be sum of parts (approximately)
        calculated_total = result.part1_volume + result.part2_volume + result.part3_volume
        self.assertAlmostEqual(result.total_volume, calculated_total, places=1)
        
    def test_calculate_all_volumes(self):
        """Test volume calculation for multiple geometries."""
        # Create second geometry
        pile_params2 = self.pile_params.copy()
        pile_params2['pile_id'] = 'TEST_P2'
        pile_params2['x_coord'] = 30.0
        geometry2 = self.geometry_engine.create_pile_geometry(pile_params2)
        
        geometries = {
            'TEST_P1': self.test_geometry,
            'TEST_P2': geometry2
        }
        
        results = self.calculator.calculate_all_volumes(geometries)
        
        self.assertEqual(len(results), 2)
        self.assertIn('TEST_P1', results)
        self.assertIn('TEST_P2', results)
        
        for pile_id, result in results.items():
            self.assertIsInstance(result, VolumeResult)
            self.assertEqual(result.pile_id, pile_id)
            self.assertGreater(result.total_volume, 0)
            
    def test_distribute_overlap_volumes(self):
        """Test overlap volume distribution."""
        # Create initial volume results
        volume_results = {
            'P1': VolumeResult('P1', 10, 20, 30, 60, 60, 0, 60),
            'P2': VolumeResult('P2', 15, 25, 35, 75, 75, 0, 75)
        }
        
        # Define overlap
        overlap_data = {('P1', 'P2'): 10.0}
        
        updated_results = self.calculator.distribute_overlap_volumes(
            volume_results, overlap_data
        )
        
        # Check that overlap was distributed proportionally
        total_volume = 60 + 75  # 135
        expected_overlap_p1 = 10.0 * (60 / 135)  # ≈ 4.44
        expected_overlap_p2 = 10.0 * (75 / 135)  # ≈ 5.56
        
        self.assertAlmostEqual(updated_results['P1'].overlap_volume, expected_overlap_p1, places=1)
        self.assertAlmostEqual(updated_results['P2'].overlap_volume, expected_overlap_p2, places=1)
        
        # Final volumes should be reduced by overlap
        self.assertAlmostEqual(updated_results['P1'].final_volume, 60 - expected_overlap_p1, places=1)
        self.assertAlmostEqual(updated_results['P2'].final_volume, 75 - expected_overlap_p2, places=1)
        
    def test_calculate_theoretical_volumes(self):
        """Test theoretical volume calculations."""
        pile_data = pd.DataFrame([{
            'pile_id': 'P1',
            'radius': 0.5,
            'pile_length': 20.0,
            'projection_angle': 15.0,
            'frustum_base_radius': 1.5
        }])
        
        result = self.calculator.calculate_theoretical_volumes(pile_data)
        
        self.assertIn('part1_theoretical', result.columns)
        self.assertIn('part2_theoretical', result.columns)
        self.assertIn('part3_theoretical', result.columns)
        self.assertIn('total_theoretical', result.columns)
        
        # Check that volumes are positive
        self.assertGreater(result['part1_theoretical'].iloc[0], 0)
        self.assertGreater(result['part2_theoretical'].iloc[0], 0)
        self.assertGreater(result['part3_theoretical'].iloc[0], 0)
        self.assertGreater(result['total_theoretical'].iloc[0], 0)
        
        # Part 1 should be cylinder volume: π * r² * h
        expected_part1 = np.pi * 0.5**2 * 20.0
        self.assertAlmostEqual(result['part1_theoretical'].iloc[0], expected_part1, places=2)
        
    def test_validate_volume_calculations(self):
        """Test volume validation."""
        # Create volume results
        volume_results = {
            'P1': VolumeResult('P1', 10, 20, 30, 60, 60, 0, 60)
        }
        
        # Create theoretical volumes
        theoretical_volumes = pd.DataFrame([{
            'pile_id': 'P1',
            'total_theoretical': 58.0  # Close to calculated
        }])
        
        validation = self.calculator.validate_volume_calculations(
            volume_results, theoretical_volumes, tolerance=0.1
        )
        
        self.assertIn('P1', validation)
        self.assertTrue(validation['P1'])  # Should pass with 10% tolerance
        
        # Test with strict tolerance
        validation_strict = self.calculator.validate_volume_calculations(
            volume_results, theoretical_volumes, tolerance=0.01
        )
        
        self.assertFalse(validation_strict['P1'])  # Should fail with 1% tolerance
        
    def test_get_volume_summary(self):
        """Test volume summary generation."""
        volume_results = {
            'P1': VolumeResult('P1', 10, 20, 30, 60, 55, 5, 50),
            'P2': VolumeResult('P2', 15, 25, 35, 75, 70, 10, 60)
        }
        
        summary = self.calculator.get_volume_summary(volume_results)
        
        self.assertEqual(summary['total_piles'], 2)
        self.assertEqual(summary['total_volume_sum'], 135)  # 60 + 75
        self.assertEqual(summary['final_volume_sum'], 110)  # 50 + 60
        self.assertEqual(summary['total_overlap_volume'], 15)  # 5 + 10
        self.assertAlmostEqual(summary['volume_efficiency'], 110/135, places=3)
        
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Test with empty results
        summary = self.calculator.get_volume_summary({})
        self.assertEqual(summary, {})
        
        # Test validation with missing pile
        volume_results = {'P1': VolumeResult('P1', 10, 20, 30, 60, 60, 0, 60)}
        theoretical_volumes = pd.DataFrame([{'pile_id': 'P2', 'total_theoretical': 60}])
        
        validation = self.calculator.validate_volume_calculations(
            volume_results, theoretical_volumes
        )
        self.assertEqual(len(validation), 0)  # No matching piles


if __name__ == '__main__':
    unittest.main()
