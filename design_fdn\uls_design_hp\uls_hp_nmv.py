"""
Ultimate Limit State (ULS) Design for H-Pile Foundation Systems - N-M-V Interaction Analysis

This module provides comprehensive Ultimate Limit State (ULS) design verification for 
H-pile (HP) steel foundation systems, implementing advanced N-M-V (Axial-Moment-Shear) 
interaction analysis according to structural steel design codes and foundation engineering 
principles. It performs both Ultimate Limit State (ULS) and Allowable Stress Design (ASD) 
verification procedures for socket H-piles (SHP) and driven H-piles (DHP).

The module performs comprehensive H-pile design verification including:
- Axial-Moment interaction analysis: Combined compression/tension and biaxial bending
- Shear capacity verification: Web and flange shear resistance calculations
- Load combination processing: ULS and SLS load case evaluation
- Steel stress verification: Yield strength-based capacity checking
- Wind load classification: Separate treatment of gravity and wind load combinations
- Multi-directional analysis: X and Y direction force and moment evaluation

Key Features:
- Advanced N-M-V interaction analysis following steel design codes
- Vectorized force processing for computational efficiency
- Dual design methodology support (ULS and ASD)
- Comprehensive load combination handling with wind load identification
- Automated steel section property integration
- Multi-format output generation (Excel and SQLite)
- Detailed design check reporting with pass/fail status
- Integration with SAFE structural analysis results

Author: Foundation Design Automation Team
Version: 1.0
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable, Tuple, Any, Union
# Use __init__.py imports for design_fdn modules
from .steel_design import design_strength
import sqlite3

# Import configuration constants
from design_fdn.design_fdn_config import (
    SHEET_SHP_DESIGN_MXY_BD, SHEET_SHP_DESIGN_VX_BD, SHEET_SHP_DESIGN_VY_BD,
    SHEET_SHP_DESIGN_MXY_ASD_GRAVITY, SHEET_SHP_DESIGN_VX_ASD_GRAVITY, SHEET_SHP_DESIGN_VY_ASD_GRAVITY,
    SHEET_SHP_DESIGN_MXY_ASD_WIND, SHEET_SHP_DESIGN_VX_ASD_WIND, SHEET_SHP_DESIGN_VY_ASD_WIND,
    SHEET_DHP_DESIGN_MXY_BD, SHEET_DHP_DESIGN_VX_BD, SHEET_DHP_DESIGN_VY_BD,
    SHEET_DHP_DESIGN_MXY_ASD_GRAVITY, SHEET_DHP_DESIGN_VX_ASD_GRAVITY, SHEET_DHP_DESIGN_VY_ASD_GRAVITY,
    SHEET_DHP_DESIGN_MXY_ASD_WIND, SHEET_DHP_DESIGN_VX_ASD_WIND, SHEET_DHP_DESIGN_VY_ASD_WIND,
    TABLE_STEEL_HPILE_CHECK_BD, TABLE_STEEL_HPILE_CHECK_ASD
)


def get_wind_load_combs(excel_inputs: Any) -> List[str]:
    """
    Extract wind load combinations from structural analysis input data.
    """
    df_loadpat = excel_inputs.LoadPat
    df_loadcase = excel_inputs.LoadCase
    df_loadcomb = excel_inputs.LoadComb
    
    # Identify wind load patterns from load type classification
    # Wind loads are typically classified as environmental/temporary loads
    wind_load_pats = df_loadpat[df_loadpat['Load Type'] == 'WIND']['LoadPat (Text)'].tolist()
    
    # Find load cases containing wind patterns using vectorized operations
    # Check for non-zero contributions from identified wind load patterns
    wind_cols = [col for col in df_loadcase.columns if col in wind_load_pats]
    if wind_cols:
        # Create boolean mask for load cases with wind pattern contributions
        wind_mask = (df_loadcase[wind_cols] != 0).any(axis=1)
        wind_load_cases = df_loadcase.loc[wind_mask, 'LoadCase (Text)'].tolist()
    else:
        wind_load_cases = []
    
    # Find load combinations containing wind load cases using vectorized operations
    # Check for non-zero contributions from identified wind load cases
    wind_case_cols = [col for col in df_loadcomb.columns if col in wind_load_cases]
    if wind_case_cols:
        # Create boolean mask for load combinations with wind case contributions
        wind_comb_mask = (df_loadcomb[wind_case_cols] != 0).any(axis=1)
        wind_load_combs = df_loadcomb.loc[wind_comb_mask, 'Combo (Text)'].tolist()
    else:
        wind_load_combs = []
    
    return wind_load_combs


def prepare_pile_data(
    df_pile: pd.DataFrame, 
    pile_type: str, 
    excel_inputs: Any
) -> pd.DataFrame:
    """
    Prepare H-pile data with calculated design strength properties for ULS analysis.
    """
    # Filter pile data for specified pile type (SHP or DHP)
    df_h = df_pile[df_pile['Pile Type'] == pile_type].copy()
    
    # Calculate characteristic yield strength for each pile using vectorized operation
    # This accounts for steel grade specifications and section thickness effects
    df_h['py1 (N/mm2)'] = df_h.apply(
        lambda row: design_strength(excel_inputs, row['Pile Section'], row['Steel Grade']), 
        axis=1
    )
    
    return df_h


def prepare_forces_data(
    df_forces: pd.DataFrame,
    df_h: pd.DataFrame,
    df_steel_section: pd.DataFrame,
    wind_load_combs: List[str],
    is_sls: bool = False
) -> pd.DataFrame:
    """
    Prepare comprehensive force data with calculated stress components for N-M-V analysis.
    """
    # Sort force data for consistent processing and efficient operations
    mdb_forces = df_forces.sort_values(['Line', 'Station', 'OutputCase'])
    
    # Add load classification and force transformations using vectorized operations
    # Wind load classification enables appropriate ASD allowable stress factors
    mdb_forces['Is Wind'] = mdb_forces['OutputCase'].isin(wind_load_combs)
    
    # Extract pile mark from SAFE element naming convention (e.g., 'P1_T' -> 'P1')
    mdb_forces['Pile Mark'] = mdb_forces['Line'].str.split('_').str[0]
    
    # Transform forces from SAFE coordinate system to foundation design conventions
    # SAFE uses compression-positive convention, foundation design uses tension-positive
    mdb_forces['P (kN)'] = -mdb_forces['P']      # Axial force: tension positive
    mdb_forces['Vx (kN)'] = mdb_forces['V3']     # Shear force in X-direction
    mdb_forces['Vy (kN)'] = mdb_forces['V2']     # Shear force in Y-direction
    mdb_forces['Mx (kNm)'] = mdb_forces['M3']    # Moment about X-axis
    mdb_forces['My (kNm)'] = mdb_forces['M2']    # Moment about Y-axis
    
    # Merge pile design data including calculated yield strengths
    # This enriches force data with material properties and design parameters
    mdb_forces = mdb_forces.merge(
        df_h[['Pile Mark', 'Pile Section', 'Steel Grade', 'py1 (N/mm2)']], 
        how='left', 
        on='Pile Mark'
    ).dropna(subset=['Steel Grade'])  # Remove entries without valid steel grade data
    
    # Prepare for steel section property merge by standardizing column names
    mdb_forces['Steel Section'] = mdb_forces['Pile Section']
    
    # Merge comprehensive steel section geometric properties
    # These properties are essential for stress and capacity calculations
    steel_cols = ['Steel Section', 'D (mm)', 'B (mm)', 't (mm)', 'T (mm)', 
                  'A (cm2)', 'Ix (cm4)', 'Iy (cm4)']
    mdb_forces = mdb_forces.merge(df_steel_section[steel_cols], how='left', on='Steel Section')
    
    # Calculate shear areas for capacity verification using steel design code provisions
    # Shear area calculations follow AISC/Eurocode methods for H-sections
    mdb_forces['Shear Ax (mm2)'] = 0.9 * 2 * mdb_forces['T (mm)'] * mdb_forces['B (mm)']  # Flange shear area
    mdb_forces['Shear Ay (mm2)'] = mdb_forces['t (mm)'] * mdb_forces['D (mm)']              # Web shear area
    
    # Calculate fundamental stress components using section properties
    # Direct axial stress from compression/tension forces
    area_m2 = mdb_forces['A (cm2)'] * 1e-4  # Convert cm² to m² for consistent units
    mdb_forces['P/A (N/mm2)'] = mdb_forces['P (kN)'] / area_m2 * 1e-3  # Axial stress
    
    # Bending stresses from moments about principal axes
    # Convert section moduli to consistent units for stress calculations
    ix_m4 = mdb_forces['Ix (cm4)'] * 1e-8  # Convert cm⁴ to m⁴
    iy_m4 = mdb_forces['Iy (cm4)'] * 1e-8  # Convert cm⁴ to m⁴
    
    # Bending stress about X-axis (strong axis bending)
    mdb_forces['MxY/Ix (N/mm2)'] = mdb_forces['Mx (kNm)'] * (mdb_forces['D (mm)'] * 0.5e-3) / ix_m4 * 1e-3
    
    # Bending stress about Y-axis (weak axis bending)
    mdb_forces['MyX/Iy (N/mm2)'] = mdb_forces['My (kNm)'] * (mdb_forces['B (mm)'] * 0.5e-3) / iy_m4 * 1e-3
    
    # Combined stress state using linear superposition for elastic analysis
    # This represents the maximum combined stress under biaxial bending and axial loading
    mdb_forces['abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)'] = (
        np.abs(mdb_forces['P/A (N/mm2)']) + 
        np.abs(mdb_forces['MxY/Ix (N/mm2)']) + 
        np.abs(mdb_forces['MyX/Iy (N/mm2)'])
    )
    
    # Calculate absolute shear forces for capacity verification
    mdb_forces['abs Vx (kN)'] = np.abs(mdb_forces['Vx (kN)'])  # X-direction shear
    mdb_forces['abs Vy (kN)'] = np.abs(mdb_forces['Vy (kN)'])  # Y-direction shear
    
    # Calculate ASD allowable stresses for Serviceability Limit State (SLS) analysis
    if is_sls:
        # Basic allowable stress for gravity loads (Factor of Safety = 2.0)
        mdb_forces['py_asd_g (N/mm2)'] = mdb_forces['py1 (N/mm2)'] / 2
        
        # Enhanced allowable stress for wind loads (25% increase per steel codes)
        # Steel design codes allow higher stresses for temporary environmental loads
        mdb_forces['py_asd_w (N/mm2)'] = mdb_forces['py_asd_g (N/mm2)'] * 1.25
    
    return mdb_forces


def perform_checks(
    mdb_forces: pd.DataFrame, 
    check_type: str = 'BD'
) -> pd.DataFrame:
    """
    Perform comprehensive stress and shear capacity checks for H-pile design verification.
    
    This function implements Ultimate Limit State (ULS) and Allowable Stress Design (ASD)
    verification procedures for H-pile steel sections under combined axial, bending, and
    shear loading. It evaluates both stress-based capacity limits and shear resistance
    according to structural steel design code provisions.
    
    The function performs two categories of design checks:
    1. Combined stress verification: Axial + biaxial bending stress vs. yield strength
    2. Shear capacity verification: Applied shear forces vs. section shear resistance
    """
    if check_type == 'BD':
        # Ultimate Limit State (ULS) design checks using characteristic yield strength
        stress_limit = mdb_forces['py1 (N/mm2)']  # Unfactored yield strength for ULS
        
        # Combined stress check: Verify that combined axial and bending stresses
        # do not exceed the characteristic yield strength of the steel section
        mdb_forces['ULS Stress Check (BD)'] = np.where(
            mdb_forces['abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)'] <= stress_limit, 
            'OK', 'NOT OK'
        )
        
        # Shear capacity calculations using von Mises yield criterion
        # Factor of 1/√3 ≈ 0.577 converts normal stress to shear stress capacity
        # Factor of 1/1000 converts mm² to kN units for force calculations
        shear_factor = 1 / np.sqrt(3) / 1000
        
        # Calculate shear capacities for both principal directions
        mdb_forces['Vcx (kN)'] = stress_limit * mdb_forces['Shear Ax (mm2)'] * shear_factor  # X-direction capacity
        mdb_forces['Vcy (kN)'] = stress_limit * mdb_forces['Shear Ay (mm2)'] * shear_factor  # Y-direction capacity
        
        # Shear resistance verification for both principal directions
        mdb_forces['ULS Vx Check (BD)'] = np.where(
            mdb_forces['abs Vx (kN)'] <= mdb_forces['Vcx (kN)'], 'OK', 'NOT OK'
        )
        mdb_forces['ULS Vy Check (BD)'] = np.where(
            mdb_forces['abs Vy (kN)'] <= mdb_forces['Vcy (kN)'], 'OK', 'NOT OK'
        )
    
    else:  # Allowable Stress Design (ASD) checks for SLS verification
        # Load combination classification for appropriate allowable stress application
        is_wind = mdb_forces['Is Wind']
        
        # Shear capacity conversion factor (von Mises criterion + unit conversion)
        shear_factor = 1 / np.sqrt(3) / 1000
        
        # Calculate allowable capacities for gravity load combinations
        # Uses basic allowable stress with standard factor of safety
        stress_limit_g = mdb_forces['py_asd_g (N/mm2)']
        mdb_forces['Vcx_asd_g (kN)'] = stress_limit_g * mdb_forces['Shear Ax (mm2)'] * shear_factor
        mdb_forces['Vcy_asd_g (kN)'] = stress_limit_g * mdb_forces['Shear Ay (mm2)'] * shear_factor
        
        # Calculate allowable capacities for wind load combinations
        # Uses enhanced allowable stress (1.25x) for temporary environmental loads
        stress_limit_w = mdb_forces['py_asd_w (N/mm2)']
        mdb_forces['Vcx_asd_w (kN)'] = stress_limit_w * mdb_forces['Shear Ax (mm2)'] * shear_factor
        mdb_forces['Vcy_asd_w (kN)'] = stress_limit_w * mdb_forces['Shear Ay (mm2)'] * shear_factor
        
        # Extract combined stress for comparison with allowable limits
        total_stress = mdb_forces['abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)']
        
        # Combined stress verification with load-specific allowable stresses
        # Gravity load combinations use standard allowable stress
        mdb_forces['ULS Stress Check (ASD Gravity)'] = np.where(
            is_wind, '-', np.where(total_stress <= stress_limit_g, 'OK', 'NOT OK')
        )
        # Wind load combinations use enhanced allowable stress
        mdb_forces['ULS Stress Check (ASD Wind)'] = np.where(
            ~is_wind, '-', np.where(total_stress <= stress_limit_w, 'OK', 'NOT OK')
        )
        
        # X-direction shear verification with load-specific capacities
        mdb_forces['ULS Vx Check (ASD Gravity)'] = np.where(
            is_wind, '-', np.where(mdb_forces['abs Vx (kN)'] <= mdb_forces['Vcx_asd_g (kN)'], 'OK', 'NOT OK')
        )
        mdb_forces['ULS Vx Check (ASD Wind)'] = np.where(
            ~is_wind, '-', np.where(mdb_forces['abs Vx (kN)'] <= mdb_forces['Vcx_asd_w (kN)'], 'OK', 'NOT OK')
        )
        
        # Y-direction shear verification with load-specific capacities
        mdb_forces['ULS Vy Check (ASD Gravity)'] = np.where(
            is_wind, '-', np.where(mdb_forces['abs Vy (kN)'] <= mdb_forces['Vcy_asd_g (kN)'], 'OK', 'NOT OK')
        )
        mdb_forces['ULS Vy Check (ASD Wind)'] = np.where(
            ~is_wind, '-', np.where(mdb_forces['abs Vy (kN)'] <= mdb_forces['Vcy_asd_w (kN)'], 'OK', 'NOT OK')
        )
    
    return mdb_forces


def extract_max_values(
    mdb_forces: pd.DataFrame,
    pile_type: str = 'SHP',
    check_type: str = 'BD',
    is_wind: Optional[bool] = None
) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Extract critical design values representing maximum stress and shear conditions for each pile.
    
    This function identifies the most critical loading conditions for each pile mark by
    extracting the maximum combined stress and maximum shear force conditions across all
    load combinations. These critical values form the basis for design verification and
    capacity assessment reporting.
    
    The function performs grouped analysis to find:
    - Maximum combined stress condition (axial + biaxial bending)
    - Maximum X-direction shear force condition
    - Maximum Y-direction shear force condition
    """
    # Filter by wind condition if specified
    if is_wind is not None:
        mdb_forces = mdb_forces[mdb_forces['Is Wind'] == is_wind]
    
    if mdb_forces.empty:
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    grouped = mdb_forces.groupby('Pile Mark')
    
    # Define base columns
    base_cols = ['Pile Mark', 'Steel Section', 'D (mm)', 'B (mm)', 't (mm)', 'T (mm)', 
                 'A (cm2)', 'Ix (cm4)', 'Iy (cm4)', 'OutputCase', 'Line', 
                 'P (kN)', 'Vx (kN)', 'Vy (kN)', 'Mx (kNm)', 'My (kNm)']
    
    # Extract max stress rows
    idx_max_stress = grouped['abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)'].idxmax()
    stress_cols = base_cols + ['P/A (N/mm2)', 'MxY/Ix (N/mm2)', 'MyX/Iy (N/mm2)', 
                               'abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)']
    
    # Extract max shear rows
    idx_max_vx = grouped['abs Vx (kN)'].idxmax()
    idx_max_vy = grouped['abs Vy (kN)'].idxmax()
    
    if check_type == 'BD':
        stress_cols += ['py1 (N/mm2)', 'ULS Stress Check (BD)']
        vx_cols = base_cols + ['abs Vx (kN)', 'py1 (N/mm2)', 'Shear Ax (mm2)', 'Vcx (kN)', 'ULS Vx Check (BD)']
        vy_cols = base_cols + ['abs Vy (kN)', 'py1 (N/mm2)', 'Shear Ay (mm2)', 'Vcy (kN)', 'ULS Vy Check (BD)']
    else:
        suffix = 'g' if is_wind is False else 'w'
        check_suffix = 'Gravity' if is_wind is False else 'Wind'
        
        stress_cols += [f'py_asd_{suffix} (N/mm2)', f'ULS Stress Check (ASD {check_suffix})']
        vx_cols = base_cols + ['abs Vx (kN)', f'py_asd_{suffix} (N/mm2)', 'Shear Ax (mm2)', 
                               f'Vcx_asd_{suffix} (kN)', f'ULS Vx Check (ASD {check_suffix})']
        vy_cols = base_cols + ['abs Vy (kN)', f'py_asd_{suffix} (N/mm2)', 'Shear Ay (mm2)', 
                               f'Vcy_asd_{suffix} (kN)', f'ULS Vy Check (ASD {check_suffix})']
    
    df_stress = mdb_forces.loc[idx_max_stress, stress_cols].reset_index(drop=True)
    df_vx = mdb_forces.loc[idx_max_vx, vx_cols].reset_index(drop=True)
    df_vy = mdb_forces.loc[idx_max_vy, vy_cols].reset_index(drop=True)
    
    return df_stress, df_vx, df_vy


def check_steel_h(
    file_paths: Any,
    safe_mdbs: Any,
    excel_inputs: Any,
    excel_outputs: Any,
    design_results: Dict[str, Any],
    pile_type: str = 'SHP',
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Perform comprehensive Ultimate Limit State (ULS) design verification for H-pile foundations.
    
    This is the main orchestrating function that coordinates the complete H-pile design
    verification workflow, including Ultimate Limit State (ULS) and Allowable Stress Design
    (ASD) analysis for both socket H-piles (SHP) and driven H-piles (DHP). It processes
    structural analysis results, performs N-M-V interaction checks, and generates
    comprehensive design verification reports.
    
    The function implements a multi-stage verification process:
    1. Wind load combination identification and classification
    2. Pile data preparation with material property calculations
    3. ULS force processing and design check evaluation
    4. SLS force processing with ASD verification procedures
    5. Critical design value extraction and reporting
    6. Multi-format result export (Excel and SQLite)
    7. Design results integration for downstream processes
    
    """
    # Step 1: Identify wind load combinations for proper ASD factor application
    # This classification enables appropriate allowable stress increases for wind loads
    wind_load_combs = get_wind_load_combs(excel_inputs)
    
    # Step 2: Prepare pile data with calculated material properties
    # Filter for specified pile type and calculate characteristic yield strengths
    df_h = prepare_pile_data(excel_inputs.Pile, pile_type, excel_inputs)
    
    # Step 3: Process Ultimate Limit State (ULS) forces for BD design verification
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"🔄 ULS FORCE PROCESSING - {pile_type} H-PILE ANALYSIS")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Pile Type: {'Socket H-Pile (SHP)' if pile_type == 'SHP' else 'Driven H-Pile (DHP)'}")
        log_callback(f"   • Analysis Method: Ultimate Limit State (ULS) with BD verification")
        log_callback(f"   • Force Components: Axial (P), Shear (Vx, Vy), Moments (Mx, My)")
        log_callback(f"   • Processing: Material properties and stress calculations")
        log_callback("")
    
    # Enrich ULS force data with section properties and calculated stress components
    mdb_forces_uls = prepare_forces_data(
        excel_outputs.PileULS,          # Factored force combinations from SAFE
        df_h,                           # Pile data with yield strengths
        excel_inputs.SteelSectionH,     # Steel section property database
        wind_load_combs,                # Wind load combination identifiers
        is_sls=False                    # ULS analysis flag
    )
    
    # Perform Ultimate Limit State design checks using characteristic yield strength
    mdb_forces_uls = perform_checks(mdb_forces_uls, check_type='BD')
    
    # Extract critical design values for ULS verification reporting
    if log_callback:
        log_callback("")
        log_callback("🔍 CRITICAL DESIGN VALUE EXTRACTION - BD VERIFICATION")
        log_callback("   • Extracting maximum stress conditions for each pile mark")
        log_callback("   • Identifying critical X-direction and Y-direction shear forces")
        log_callback("   • Compiling governing ULS design cases for verification")
        log_callback("   • Method: BD (Characteristic yield strength verification)")
    
    # Find maximum stress and shear conditions for each pile mark
    df_design_mxy_bd, df_design_vx_bd, df_design_vy_bd = extract_max_values(
        mdb_forces_uls, pile_type, check_type='BD'
    )
    
    # Step 4: Process Serviceability Limit State (SLS) forces for ASD verification
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"🔄 SLS FORCE PROCESSING - {pile_type} ALLOWABLE STRESS DESIGN")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Pile Type: {'Socket H-Pile (SHP)' if pile_type == 'SHP' else 'Driven H-Pile (DHP)'}")
        log_callback(f"   • Analysis Method: Serviceability Limit State with ASD verification")
        log_callback(f"   • Allowable Stress Calculations: Gravity and wind load combinations")
        log_callback(f"   • Wind Load Enhancement: 1.25x allowable stress for wind cases")
        log_callback("")
    
    # Enrich SLS force data with allowable stress calculations for ASD method
    mdb_forces_sls = prepare_forces_data(
        excel_outputs.PileSLS,          # Working load combinations from SAFE
        df_h,                           # Pile data with yield strengths
        excel_inputs.SteelSectionH,     # Steel section property database
        wind_load_combs,                # Wind load combination identifiers
        is_sls=True                     # SLS analysis flag (enables ASD calculations)
    )
    
    # Perform Allowable Stress Design checks with load-specific allowable stresses
    mdb_forces_sls = perform_checks(mdb_forces_sls, check_type='ASD')
    
    # Step 5: Extract critical design values for ASD verification reporting
    if log_callback:
        log_callback("")
        log_callback("🔍 CRITICAL DESIGN VALUE EXTRACTION - ASD VERIFICATION")
        log_callback("   • Processing gravity load combinations (standard allowable stress)")
        log_callback("   • Processing wind load combinations (enhanced allowable stress)")
        log_callback("   • Extracting maximum stress and shear conditions per load type")
        log_callback("   • Method: ASD (Allowable Stress Design with load-specific factors)")
    
    # Extract maximum conditions for gravity load combinations (standard allowable stress)
    df_design_mxy_asdg, df_design_vx_asdg, df_design_vy_asdg = extract_max_values(
        mdb_forces_sls, pile_type, check_type='ASD', is_wind=False
    )
    
    # Extract maximum conditions for wind load combinations (enhanced allowable stress)
    df_design_mxy_asdw, df_design_vx_asdw, df_design_vy_asdw = extract_max_values(
        mdb_forces_sls, pile_type, check_type='ASD', is_wind=True
    )
    
    # Step 6: Export comprehensive design verification results to multiple formats
    # Generate Excel reports for design review and SQLite databases for data analysis
    export_results(
        file_paths, pile_type, mdb_forces_uls, mdb_forces_sls,
        df_design_mxy_bd, df_design_vx_bd, df_design_vy_bd,
        df_design_mxy_asdg, df_design_vx_asdg, df_design_vy_asdg,
        df_design_mxy_asdw, df_design_vx_asdw, df_design_vy_asdw,
        log_callback
    )
    
    # Step 7: Update design results object for integration with downstream processes
    # Store critical design values and verification outcomes for overall design coordination
    update_design_results(
        design_results, pile_type,
        df_design_mxy_bd, df_design_vx_bd, df_design_vy_bd,
        df_design_mxy_asdg, df_design_vx_asdg, df_design_vy_asdg,
        df_design_mxy_asdw, df_design_vx_asdw, df_design_vy_asdw
    )
    
    # Completion logging with pile type identification
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"✅ {'SOCKET' if pile_type == 'SHP' else 'DRIVEN'} H-PILE DESIGN VERIFICATION COMPLETED")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Pile Type: {'Socket H-Pile (SHP)' if pile_type == 'SHP' else 'Driven H-Pile (DHP)'}")
        log_callback(f"   • ULS Analysis: BD verification with characteristic yield strength")
        log_callback(f"   • SLS Analysis: ASD verification with gravity and wind load factors")
        log_callback(f"   • Output Generated: Excel reports and SQLite databases")
        log_callback(f"   • Design Integration: Results updated for workflow coordination")
        log_callback("")
    
    return design_results


def export_results(
    file_paths: Any,
    pile_type: str,
    mdb_forces_uls: pd.DataFrame,
    mdb_forces_sls: pd.DataFrame,
    df_design_mxy_bd: pd.DataFrame,
    df_design_vx_bd: pd.DataFrame,
    df_design_vy_bd: pd.DataFrame,
    df_design_mxy_asdg: pd.DataFrame,
    df_design_vx_asdg: pd.DataFrame,
    df_design_vy_asdg: pd.DataFrame,
    df_design_mxy_asdw: pd.DataFrame,
    df_design_vx_asdw: pd.DataFrame,
    df_design_vy_asdw: pd.DataFrame,
    log_callback: Optional[Callable[[str], None]]
) -> None:
    """
    Export comprehensive H-pile design verification results to multiple output formats.
    
    This function generates multi-format output files containing detailed design verification
    results for both Ultimate Limit State (ULS) and Allowable Stress Design (ASD) analysis.
    It creates Excel reports for design review and SQLite databases for data analysis and
    integration with other design tools.
    """    
    # Determine output path and sheet names
    path_excel_output = file_paths.ResultSHPCheck if pile_type == 'SHP' else file_paths.ResultDHPCheck
    
    # Use config constants for sheet names
    if pile_type == 'SHP':
        sheet_mapping = {
            SHEET_SHP_DESIGN_MXY_BD: df_design_mxy_bd,
            SHEET_SHP_DESIGN_VX_BD: df_design_vx_bd,
            SHEET_SHP_DESIGN_VY_BD: df_design_vy_bd,
            SHEET_SHP_DESIGN_MXY_ASD_GRAVITY: df_design_mxy_asdg,
            SHEET_SHP_DESIGN_VX_ASD_GRAVITY: df_design_vx_asdg,
            SHEET_SHP_DESIGN_VY_ASD_GRAVITY: df_design_vy_asdg,
            SHEET_SHP_DESIGN_MXY_ASD_WIND: df_design_mxy_asdw,
            SHEET_SHP_DESIGN_VX_ASD_WIND: df_design_vx_asdw,
            SHEET_SHP_DESIGN_VY_ASD_WIND: df_design_vy_asdw,
        }
    else:  # DHP
        sheet_mapping = {
            SHEET_DHP_DESIGN_MXY_BD: df_design_mxy_bd,
            SHEET_DHP_DESIGN_VX_BD: df_design_vx_bd,
            SHEET_DHP_DESIGN_VY_BD: df_design_vy_bd,
            SHEET_DHP_DESIGN_MXY_ASD_GRAVITY: df_design_mxy_asdg,
            SHEET_DHP_DESIGN_VX_ASD_GRAVITY: df_design_vx_asdg,
            SHEET_DHP_DESIGN_VY_ASD_GRAVITY: df_design_vy_asdg,
            SHEET_DHP_DESIGN_MXY_ASD_WIND: df_design_mxy_asdw,
            SHEET_DHP_DESIGN_VX_ASD_WIND: df_design_vx_asdw,
            SHEET_DHP_DESIGN_VY_ASD_WIND: df_design_vy_asdw,
        }
    
    # Export to Excel
    with pd.ExcelWriter(path_excel_output) as writer:
        for sheet_name, df in sheet_mapping.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    # Round columns for SQLite export
    round_cols_uls = ['Shear Ax (mm2)', 'Shear Ay (mm2)', 'P/A (N/mm2)', 
                      'MxY/Ix (N/mm2)', 'MyX/Iy (N/mm2)', 
                      'abs(P/A)+abs(MxY/Ix)+abs(MyX/Iy) (N/mm2)', 'Vcx (kN)', 'Vcy (kN)']
    
    round_cols_sls = round_cols_uls[:-2] + ['Vcx_asd_g (kN)', 'Vcx_asd_w (kN)', 
                                             'Vcy_asd_g (kN)', 'Vcy_asd_w (kN)']
    
    mdb_forces_uls[round_cols_uls] = mdb_forces_uls[round_cols_uls].round(2)
    mdb_forces_sls[round_cols_sls] = mdb_forces_sls[round_cols_sls].round(2)
    
    # Rename columns for SQLite
    rename_cols = {'t (mm)': 'Web_Thickness_t (mm)', 'T (mm)': 'Flange_Thickness_T (mm)'}
    mdb_forces_uls_sqlite = mdb_forces_uls.rename(columns=rename_cols)
    mdb_forces_sls_sqlite = mdb_forces_sls.rename(columns=rename_cols)
      # Save to SQLite
    with sqlite3.connect(file_paths.ResultSteelHPileCheckBD) as conn:
        mdb_forces_uls_sqlite.to_sql(TABLE_STEEL_HPILE_CHECK_BD, conn, if_exists='replace', index=False)
    
    with sqlite3.connect(file_paths.ResultSteelHPileCheckASD) as conn:
        mdb_forces_sls_sqlite.to_sql(TABLE_STEEL_HPILE_CHECK_ASD, conn, if_exists='replace', index=False)
    
    if log_callback:
        log_callback("")
        log_callback("✅ MULTI-FORMAT EXPORT COMPLETED SUCCESSFULLY")
        log_callback("   • Excel Reports: Design verification sheets with critical values")
        log_callback("   • SQLite Databases: BD and ASD analysis results for data integration")
        log_callback("   • Data Format: Rounded precision for database storage efficiency")
        log_callback("   • Column Mapping: Renamed columns for SQLite compatibility")
        log_callback("")


def update_design_results(
    design_results: Dict[str, Any],
    pile_type: str,
    df_design_mxy_bd: pd.DataFrame,
    df_design_vx_bd: pd.DataFrame,
    df_design_vy_bd: pd.DataFrame,
    df_design_mxy_asdg: pd.DataFrame,
    df_design_vx_asdg: pd.DataFrame,
    df_design_vy_asdg: pd.DataFrame,
    df_design_mxy_asdw: pd.DataFrame,
    df_design_vx_asdw: pd.DataFrame,
    df_design_vy_asdw: pd.DataFrame
) -> None:
    """
    Update design results object with H-pile verification outcomes for workflow integration.
    
    This function integrates H-pile design verification results into the comprehensive
    design results object, enabling coordination with other foundation design modules
    and overall project design workflow. Results are organized by pile type and
    verification method for systematic access by downstream processes.
    """
    prefix = 'SHP' if pile_type == 'SHP' else 'DHP'
    
    setattr(design_results, f'{prefix}ULSStressCheckBD', df_design_mxy_bd)
    setattr(design_results, f'{prefix}ULSVxCheckBD', df_design_vx_bd)
    setattr(design_results, f'{prefix}ULSVyCheckBD', df_design_vy_bd)
    setattr(design_results, f'{prefix}ULSStressCheckASDG', df_design_mxy_asdg)
    setattr(design_results, f'{prefix}ULSVxCheckASDG', df_design_vx_asdg)
    setattr(design_results, f'{prefix}ULSVyCheckASDG', df_design_vy_asdg)
    setattr(design_results, f'{prefix}ULSStressCheckASDW', df_design_mxy_asdw)
    setattr(design_results, f'{prefix}ULSVxCheckASDW', df_design_vx_asdw)
    setattr(design_results, f'{prefix}ULSVyCheckASDW', df_design_vy_asdw)
