"""Version selection frame UI component."""

import tkinter as tk
from typing import Callable

from ui.components import <PERSON>Frame, HeaderFrame, setup_window_icon, create_menu_bar, show_about


class VersionSelectionFrame(BaseFrame):
    """Version selection interface frame."""
    
    def __init__(self, parent, username: str, on_version_selected: Callable):
        super().__init__(parent)
        
        # Clear window and add header
        self.clear_window()
        HeaderFrame(parent, show_user=False)
        
        # Create modal window for version selection
        self.choice_window = tk.Toplevel(parent)
        self.choice_window.title("Select Version")
        self.choice_window.geometry("400x200")
        self.choice_window.transient(parent)
        self.choice_window.grab_set()  # Make window modal
        
        # Setup window icon
        setup_window_icon(self.choice_window)
        
        # Welcome message
        tk.Label(
            self.choice_window,
            text=f"Welcome, {username}!\nYou have access to both versions.",
            font=("Arial", 12)
        ).pack(pady=10)
        
        tk.Label(
            self.choice_window,
            text="Please select which version you would like to use:",
            font=("Arial", 10)
        ).pack(pady=10)
        
        # Button frame
        button_frame = tk.Frame(self.choice_window)
        button_frame.pack(pady=20)
        
        # Version selection buttons
        tk.Button(
            button_frame,
            text="Base Version",
            command=lambda: self._select_version("Base", on_version_selected),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            button_frame,
            text="Ultimate Version",
            command=lambda: self._select_version("Ultimate", on_version_selected),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
    
    def _select_version(self, version: str, callback: Callable):
        """Handle version selection."""
        self.choice_window.destroy()
        callback(version)
