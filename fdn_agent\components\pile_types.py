"""Pile types configuration components"""
import tkinter as tk
from tkinter import messagebox, Toplevel, StringVar
from tkinter import ttk


class PileTypesManager:
    """Manages pile types configuration interface"""
    
    def __init__(self, parent, log_callback):
        self.parent = parent
        self.log_callback = log_callback
        self.pile_types_data = self._get_default_pile_data()
        self.selected_pile_types = []
        self._init_variables()
    
    def _get_default_pile_data(self):
        """Get default pile types data"""
        return {
            'DHP': [{'capacity': 3663, 'section': 'UBP_305x305x223', 'diameter': None, 'min_spacing': 1.2}],
            'SHP': [{'capacity': 6106, 'section': 'UBP_305x305x223', 'diameter': None, 'min_spacing': 1.85}],
            'BP': [
                {'capacity': 41155, 'section': None, 'diameter': 2.0, 'min_spacing': 6.0},
                {'capacity': 72064, 'section': None, 'diameter': 2.0, 'min_spacing': 6.0},
                {'capacity': 64304, 'section': None, 'diameter': 2.5, 'min_spacing': 7.5},
                {'capacity': 112875, 'section': None, 'diameter': 2.5, 'min_spacing': 7.5},
                {'capacity': 92598, 'section': None, 'diameter': 3, 'min_spacing': 9},
                {'capacity': 162804, 'section': None, 'diameter': 3, 'min_spacing': 9}
            ]
        }
    
    def _init_variables(self):
        """Initialize GUI variables"""
        # DHP variables
        self.dhp_var = tk.BooleanVar(value=True)
        self.dhp_capacity_var = tk.DoubleVar(value=3663)
        self.dhp_section_var = tk.StringVar(value="UBP_305x305x223")
        self.dhp_spacing_var = tk.DoubleVar(value=1.2)
        
        # SHP variables
        self.shp_var = tk.BooleanVar(value=True)
        self.shp_capacity_var = tk.DoubleVar(value=6106)
        self.shp_section_var = tk.StringVar(value="UBP_305x305x223")
        self.shp_spacing_var = tk.DoubleVar(value=1.85)
        
        # BP variables
        self.bp_var = tk.BooleanVar(value=True)
    
    def create_ui(self):
        """Create pile types configuration interface"""
        pile_config_frame = ttk.LabelFrame(
            self.parent, text="Pile Types Configuration (AI Agent Input)", padding="8")
        pile_config_frame.pack(fill=tk.X, pady=(0, 8))
        
        # Create notebook for different pile types
        style = ttk.Style()
        style.configure('TNotebook.Tab', padding=[8, 4])
        
        notebook = ttk.Notebook(pile_config_frame, style='TNotebook')
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 8))
        
        # Create tabs
        self._create_dhp_tab(notebook)
        self._create_shp_tab(notebook)
        self._create_bp_tab(notebook)
        
        # Selected pile types display
        self._create_selected_display(pile_config_frame)
        
        return pile_config_frame
    
    def _create_dhp_tab(self, notebook):
        """Create DHP configuration tab"""
        dhp_frame = ttk.Frame(notebook, padding="10")
        notebook.add(dhp_frame, text="DHP (Driven H-Pile)")
        
        # Header and selection
        ttk.Label(dhp_frame, text="Driven H-Pile (DHP) Configuration", 
                 font=("Arial", 10, "bold")).pack(pady=5)
        
        dhp_select_frame = ttk.Frame(dhp_frame)
        dhp_select_frame.pack(fill=tk.X, pady=5)
        
        dhp_check = ttk.Checkbutton(dhp_select_frame, text="Include DHP in optimization", 
                                   variable=self.dhp_var, command=self.update_selected_piles_display)
        dhp_check.pack(side=tk.LEFT)
        
        # Parameters
        dhp_params_frame = ttk.LabelFrame(dhp_frame, text="DHP Parameters", padding="5")
        dhp_params_frame.pack(fill=tk.X, pady=5)
        
        self._create_parameter_row(dhp_params_frame, "Pile Capacity (kN):", self.dhp_capacity_var, 15)
        self._create_parameter_row(dhp_params_frame, "Pile Section:", self.dhp_section_var, 20)
        self._create_parameter_row(dhp_params_frame, "Min Spacing (m):", self.dhp_spacing_var, 15)
    
    def _create_shp_tab(self, notebook):
        """Create SHP configuration tab"""
        shp_frame = ttk.Frame(notebook, padding="10")
        notebook.add(shp_frame, text="SHP (Socket H-Pile)")
        
        # Header and selection
        ttk.Label(shp_frame, text="Socket H-Pile (SHP) Configuration", 
                 font=("Arial", 10, "bold")).pack(pady=5)
        
        shp_select_frame = ttk.Frame(shp_frame)
        shp_select_frame.pack(fill=tk.X, pady=5)
        
        shp_check = ttk.Checkbutton(shp_select_frame, text="Include SHP in optimization", 
                                   variable=self.shp_var, command=self.update_selected_piles_display)
        shp_check.pack(side=tk.LEFT)
        
        # Parameters
        shp_params_frame = ttk.LabelFrame(shp_frame, text="SHP Parameters", padding="5")
        shp_params_frame.pack(fill=tk.X, pady=5)
        
        self._create_parameter_row(shp_params_frame, "Pile Capacity (kN):", self.shp_capacity_var, 15)
        self._create_parameter_row(shp_params_frame, "Pile Section:", self.shp_section_var, 20)
        self._create_parameter_row(shp_params_frame, "Min Spacing (m):", self.shp_spacing_var, 15)
    
    def _create_bp_tab(self, notebook):
        """Create BP configuration tab"""
        bp_frame = ttk.Frame(notebook, padding="10")
        notebook.add(bp_frame, text="BP (Bored Pile)")
        
        # Header and selection
        header_label = ttk.Label(bp_frame, text="Bored Pile (BP) Configuration", 
                                font=("Arial", 10, "bold"))
        header_label.pack(pady=5)
        
        bp_select_frame = ttk.Frame(bp_frame)
        bp_select_frame.pack(fill=tk.X, pady=5)
        
        bp_check = ttk.Checkbutton(bp_select_frame, text="Include BP in optimization", 
                                  variable=self.bp_var, command=self.update_selected_piles_display)
        bp_check.pack(side=tk.LEFT)
        
        # BP list management
        self._create_bp_management(bp_frame)
    
    def _create_parameter_row(self, parent, label_text, variable, width):
        """Create a parameter input row"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2)
        ttk.Label(frame, text=label_text).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(frame, textvariable=variable, width=width).pack(side=tk.LEFT)
    
    def _create_bp_management(self, parent):
        """Create BP management interface"""
        # BP types frame
        bp_types_frame = ttk.LabelFrame(parent, text="BP Types", padding="5")
        bp_types_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Treeview for BP types
        tree_frame = ttk.Frame(bp_types_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.bp_tree = ttk.Treeview(tree_frame, columns=('Capacity', 'Diameter', 'Min Spacing'), 
                                   show='tree headings', height=6)
        
        # Configure columns
        self.bp_tree.heading('#0', text='Type')
        self.bp_tree.heading('Capacity', text='Capacity (kN)')
        self.bp_tree.heading('Diameter', text='Diameter (m)')
        self.bp_tree.heading('Min Spacing', text='Min Spacing (m)')
        
        self.bp_tree.column('#0', width=80, minwidth=50)
        self.bp_tree.column('Capacity', width=100, minwidth=80)
        self.bp_tree.column('Diameter', width=100, minwidth=80)
        self.bp_tree.column('Min Spacing', width=100, minwidth=80)
        
        # Scrollbar for treeview
        bp_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.bp_tree.yview)
        self.bp_tree.configure(yscrollcommand=bp_scrollbar.set)
        
        self.bp_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        bp_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons for BP management
        bp_button_frame = ttk.Frame(bp_types_frame)
        bp_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(bp_button_frame, text="Add BP Type", command=self.add_bp_type).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bp_button_frame, text="Edit Selected", command=self.edit_bp_type).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bp_button_frame, text="Remove Selected", command=self.remove_bp_type).pack(side=tk.LEFT)
        
        # Populate the tree with default BP data
        self.populate_bp_tree()
    
    def populate_bp_tree(self):
        """Populate BP tree with current data"""
        # Clear existing items
        for item in self.bp_tree.get_children():
            self.bp_tree.delete(item)
        
        # Add BP types
        for i, bp_data in enumerate(self.pile_types_data['BP']):
            item_text = f"BP_{i+1}"
            self.bp_tree.insert('', 'end', text=item_text,
                               values=(bp_data['capacity'], bp_data['diameter'], bp_data['min_spacing']))
    
    def add_bp_type(self):
        """Add new BP type"""
        self.show_bp_dialog(edit_mode=False)
    
    def edit_bp_type(self):
        """Edit selected BP type"""
        selected = self.bp_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a BP type to edit.")
            return
        
        item = selected[0]
        values = self.bp_tree.item(item, 'values')
        current_values = {
            'capacity': float(values[0]),
            'diameter': float(values[1]),
            'min_spacing': float(values[2])
        }
        self.show_bp_dialog(edit_mode=True, current_values=current_values, item=item)
    
    def remove_bp_type(self):
        """Remove selected BP type"""
        selected = self.bp_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a BP type to remove.")
            return
        
        if messagebox.askyesno("Confirm Removal", "Are you sure you want to remove the selected BP type?"):
            item = selected[0]
            index = self.bp_tree.index(item)
            
            # Remove from data
            del self.pile_types_data['BP'][index]
            
            # Refresh tree
            self.populate_bp_tree()
            self.update_selected_piles_display()
            self.log_callback("BP type removed successfully.")
    
    def show_bp_dialog(self, edit_mode=False, current_values=None, item=None):
        """Show dialog for adding/editing BP type"""
        dialog = Toplevel(self.parent)
        dialog.title("Edit BP Type" if edit_mode else "Add BP Type")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.grab_set()
        
        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # Variables
        capacity_var = tk.DoubleVar(value=current_values['capacity'] if current_values else 50000)
        diameter_var = tk.DoubleVar(value=current_values['diameter'] if current_values else 2.0)
        spacing_var = tk.DoubleVar(value=current_values['min_spacing'] if current_values else 6.0)
        
        # Create form
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Capacity
        ttk.Label(main_frame, text="Pile Capacity (kN):").grid(row=0, column=0, sticky="w", pady=2)
        capacity_entry = ttk.Entry(main_frame, textvariable=capacity_var, width=15)
        capacity_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        
        # Diameter
        ttk.Label(main_frame, text="Diameter (m):").grid(row=1, column=0, sticky="w", pady=2)
        diameter_entry = ttk.Entry(main_frame, textvariable=diameter_var, width=15)
        diameter_entry.grid(row=1, column=1, pady=2, padx=(5, 0))
        
        # Min Spacing
        ttk.Label(main_frame, text="Min Spacing (m):").grid(row=2, column=0, sticky="w", pady=2)
        spacing_entry = ttk.Entry(main_frame, textvariable=spacing_var, width=15)
        spacing_entry.grid(row=2, column=1, pady=2, padx=(5, 0))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        def save_bp():
            try:
                new_bp = {
                    'capacity': capacity_var.get(),
                    'section': None,
                    'diameter': diameter_var.get(),
                    'min_spacing': spacing_var.get()
                }
                
                if edit_mode and item:
                    # Update existing
                    index = self.bp_tree.index(item)
                    self.pile_types_data['BP'][index] = new_bp
                    self.log_callback("BP type updated successfully.")
                else:
                    # Add new
                    self.pile_types_data['BP'].append(new_bp)
                    self.log_callback("New BP type added successfully.")
                
                self.populate_bp_tree()
                self.update_selected_piles_display()
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save BP type: {str(e)}")
        
        ttk.Button(button_frame, text="Save", command=save_bp).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)
        
        # Focus on first entry
        capacity_entry.focus()
    
    def _create_selected_display(self, parent):
        """Create selected pile types display"""
        selected_frame = ttk.LabelFrame(parent, text="Selected Pile Types for AI Optimization", padding="8")
        selected_frame.pack(fill=tk.X, pady=(0, 5))
        
        text_container = ttk.Frame(selected_frame)
        text_container.pack(fill=tk.X)
        
        self.selected_piles_text = tk.Text(text_container, height=4, wrap=tk.WORD, 
                                          font=("Arial", 9), relief="solid", borderwidth=1)
        selected_scrollbar = ttk.Scrollbar(text_container, command=self.selected_piles_text.yview)
        self.selected_piles_text.configure(yscrollcommand=selected_scrollbar.set)
        self.selected_piles_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.update_selected_piles_display()
    
    def update_selected_piles_display(self):
        """Update the display of selected pile types"""
        # Clear the text widget
        self.selected_piles_text.configure(state="normal")
        self.selected_piles_text.delete(1.0, "end")
        
        display_text = []
        
        # DHP
        if self.dhp_var.get():
            dhp_text = f"DHP: Capacity={self.dhp_capacity_var.get()}kN, Section={self.dhp_section_var.get()}, Min Spacing={self.dhp_spacing_var.get()}m"
            display_text.append(dhp_text)
        
        # SHP
        if self.shp_var.get():
            shp_text = f"SHP: Capacity={self.shp_capacity_var.get()}kN, Section={self.shp_section_var.get()}, Min Spacing={self.shp_spacing_var.get()}m"
            display_text.append(shp_text)
        
        # BP
        if self.bp_var.get():
            for i, bp in enumerate(self.pile_types_data['BP']):
                bp_text = f"BP_{i+1}: Capacity={bp['capacity']}kN, Diameter={bp['diameter']}m, Min Spacing={bp['min_spacing']}m"
                display_text.append(bp_text)
        
        if not display_text:
            display_text.append("No pile types selected for optimization.")
        
        self.selected_piles_text.insert("end", "\n".join(display_text))
        self.selected_piles_text.configure(state="disabled")
    
    def get_selected_pile_types_for_optimization(self):
        """Get selected pile types data for optimization"""
        selected_types = []
        
        # Add DHP if selected
        if self.dhp_var.get():
            dhp_data = {
                'type': 'DHP',
                'capacity': self.dhp_capacity_var.get(),
                'section': self.dhp_section_var.get(),
                'diameter': None,
                'min_spacing': self.dhp_spacing_var.get()
            }
            selected_types.append(dhp_data)
        
        # Add SHP if selected
        if self.shp_var.get():
            shp_data = {
                'type': 'SHP',
                'capacity': self.shp_capacity_var.get(),
                'section': self.shp_section_var.get(),
                'diameter': None,
                'min_spacing': self.shp_spacing_var.get()
            }
            selected_types.append(shp_data)
        
        # Add BP types if selected
        if self.bp_var.get():
            for i, bp in enumerate(self.pile_types_data['BP']):
                bp_data = {
                    'type': 'BP',
                    'capacity': bp['capacity'],
                    'section': None,
                    'diameter': bp['diameter'],
                    'min_spacing': bp['min_spacing']
                }
                selected_types.append(bp_data)
        
        return selected_types
