"""Design Strip Generation Module for SAFE16/SAFE22 Slab Design

This module provides comprehensive functionality for generating design strips for reinforced 
concrete slab design in both SAFE16 and SAFE22 finite element analysis software. Design strips 
are critical components in slab design methodology, providing organized regions for reinforcement 
design, analysis result interpretation, and design verification.

The module automatically generates orthogonal design strip patterns based on slab geometry and 
grouping information from Excel inputs, creating both X-direction and Y-direction strips that 
conform to structural design standards and software requirements.

Key Functionality:
- Automatic design strip generation based on slab boundaries and grouping
- Orthogonal strip pattern creation (X-direction and Y-direction strips)
- Point coordinate generation for strip boundary definition
- Strip connectivity establishment for finite element analysis
- Design overwrite configuration for reinforcement design parameters
- Dual SAFE16/SAFE22 compatibility with format-specific adaptations

Design Strip Concepts:
- **Column Strips**: Strips aligned with column centerlines for primary load transfer
- **Middle Strips**: Strips between column strips for secondary reinforcement
- **X-Direction Strips (Layer A)**: Horizontal strips for reinforcement parallel to X-axis
- **Y-Direction Strips (Layer B)**: Vertical strips for reinforcement parallel to Y-axis
- **Strip Width**: Typically based on slab thickness or structural requirements
- **Strip Boundaries**: Defined by slab group extents and geometric constraints

SAFE16 vs SAFE22 Implementation:
- **SAFE16**: Uses ObjGeomDesignStrips with point-based strip definitions
- **SAFE22**: Uses StripObjectConnectivity with enhanced strip modeling capabilities
- **Common Features**: Point generation, design overwrites, material assignments
- **Format Differences**: Column structures, parameter naming, design integration

Structural Design Integration:
- Strip-based reinforcement design approach following ACI/BS/EC standards
- Moment distribution analysis across strip widths
- Reinforcement calculation and detailing within strip boundaries
- Design verification and optimization within strip regions
- Load path analysis through strip connectivity

Technical Specifications:
- Strip width calculation based on structural thickness and design requirements
- Automatic boundary detection from slab group geometry
- Point offset management for proper strip definition
- Design layer assignment (A/B) for orthogonal reinforcement directions
- Material property integration for reinforcement design

Performance Considerations:
- Efficient processing of large slab systems with multiple groups
- Memory-optimized data structures for complex strip patterns
- Automatic validation and error checking throughout generation process
- Safety limits for strip generation to prevent infinite loops
- Optimized DataFrame operations for large-scale slab models

Dependencies:
- pandas: DataFrame operations and data manipulation
- datetime: Timestamp generation for process tracking
- Excel input data with slab geometry, properties, and grouping information

Usage:
    from build_fem.write_safe.write_design_strip import write_design_strip
    
    # Generate design strips for slab design
    safe16_dfs, safe22_dfs = write_design_strip(excel_inputs, safe16_dfs, safe22_dfs)
    
    # Verify strip generation
    print(f"Generated design strips for {len(slab_groups)} slab groups")

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

from datetime import datetime

import pandas as pd

# =============================================================================
# DESIGN STRIP GENERATION CONSTANTS
# =============================================================================
# Configuration parameters for SAFE16 and SAFE22 design strip generation,
# covering geometric definitions, material properties, and design parameters.

# --- Geometric Configuration ---
STRIP_POINT_OFFSET = 0.1                    # Point offset beyond slab boundaries (m)
                                            # Ensures strip points are outside slab geometry
                                            # for proper strip definition and visualization

# --- SAFE16 Point Configuration ---
SAFE16_POINT_IS_SPECIAL = 'Yes'            # Marks points as special for SAFE16 processing
                                            # Special points receive different treatment in analysis

# --- SAFE22 Point Configuration ---
SAFE22_POINT_IS_UNIQUE_POINT = 'No'        # Point uniqueness flag for SAFE22
SAFE22_POINT_HAS_RESTRAINT = 'Yes'         # Restraint presence flag for strip points
SAFE22_POINT_SPRING_PROPERTY = ''          # Spring property assignment (empty = no springs)

# --- SAFE16 Strip Definition Parameters ---  
SAFE16_STRIP_ANGLE = None                   # Strip angle (None = automatic orientation)
# SAFE16 ObjGeomDesignStrips column structure:
# [Name, Point, Angle, FEMWidthLeft, FEMWidthRight, DesignWidthLeft, DesignWidthRight, DesignGroup]
# - Name: Strip identifier
# - Point: Associated point name
# - Angle: Strip orientation angle (degrees)
# - FEMWidthLeft/Right: Finite element mesh widths on each side
# - DesignWidthLeft/Right: Design widths for reinforcement calculations
# - DesignGroup: Group assignment for design organization

# --- SAFE22 Strip Definition Parameters ---
SAFE22_STRIP_OBJECT_NAME = None             # Object name association (None = automatic)
SAFE22_STRIP_AUTO_GENERATE = 'No'          # Automatic strip generation flag
SAFE22_STRIP_LAYER_A = 'A'                 # Layer A designation (X-direction strips)
SAFE22_STRIP_LAYER_B = 'B'                 # Layer B designation (Y-direction strips)
SAFE22_STRIP_DESIGN_GROUP_NAME = None      # Design group name (None = automatic)

# --- Material and Design Configuration ---
COMMON_REBAR_MATERIAL = 'REBAR13'          # Standard rebar material designation
                                            # Typically Grade 60 (420 MPa) reinforcing steel

# --- Cover Preferences ---
SAFE16_COVER_PREFERENCES = 'Preferences'    # SAFE16 cover preference source
SAFE22_COVER_PREFERENCES = 'From Preferences'  # SAFE22 cover preference source

# --- SAFE16 Slab Design Parameters ---
SAFE16_SLAB_DESIGN_TYPE = 'Column'         # Design strip type (Column/Middle strip)
SAFE16_SLAB_DESIGN_NUM_BARS = 1            # Number of bar sizes for design
SAFE16_SLAB_DESIGN_IS_PT = 'Yes'           # Post-tensioning consideration flag

# --- SAFE22 Slab Design Parameters ---
SAFE22_SLAB_DESIGN_TYPE = 'Column Strip'   # Design strip type specification
SAFE22_SLAB_DESIGN_IS_PT_YES = 'Yes'       # Post-tensioning flag (Is PT?)
SAFE22_SLAB_DESIGN_IS_PT_NO = 'No'         # Minimum reinforcement flag (UseMinReinf?)
SAFE22_SLAB_DESIGN_NUM_BARS = 1            # Number of bar sizes for design

# --- Safety and Performance Limits ---
MAX_STRIP_SEGMENTS_PER_GROUP = 1000        # Maximum strip segments per slab group
                                            # Safety limit to prevent infinite loops in
                                            # strip generation algorithms


# =============================================================================
# HELPER FUNCTIONS FOR DESIGN STRIP GENERATION
# =============================================================================

def _create_strip_point(safe16_dfs, safe22_dfs, point_name, x_coord, y_coord, z_coord=0.0):
    """Create a design strip point in both SAFE16 and SAFE22 data structures.

    Generates point coordinates for design strip boundaries in both SAFE16 and SAFE22 formats.
    These points define the geometric boundaries of design strips and are essential for 
    establishing strip connectivity and finite element mesh generation.

    Strip points are typically located slightly outside slab boundaries to ensure proper 
    strip definition and to avoid conflicts with slab geometry. The function handles 
    format-specific requirements for point creation in both SAFE versions.
    """
    # Create SAFE16 point coordinate entry
    target_columns = safe16_dfs.ObjGeomPointCoordinates.columns.tolist()
    new_rows = []

    # Add point data: [Name, X, Y, Z, IsSpecial]
    new_rows.append([
        point_name, x_coord, y_coord, z_coord, SAFE16_POINT_IS_SPECIAL
    ])

    # Create DataFrame with proper MultiIndex column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    
    # Append to existing SAFE16 point coordinates
    safe16_dfs.ObjGeomPointCoordinates = pd.concat(
        [safe16_dfs.ObjGeomPointCoordinates, df_append], ignore_index=True
    )

    # Create SAFE22 point connectivity entry
    target_columns = safe22_dfs.PointObjectConnectivity.columns.tolist()
    new_rows = []

    # Add point data: [Name, IsUniquePoint, HasRestraint, X, Y, Z, SpringProperty]
    new_rows.append([
        point_name, SAFE22_POINT_IS_UNIQUE_POINT, SAFE22_POINT_HAS_RESTRAINT,
        x_coord, y_coord, z_coord, SAFE22_POINT_SPRING_PROPERTY
    ])

    # Create DataFrame with proper MultiIndex column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    
    # Append to existing SAFE22 point connectivity
    safe22_dfs.PointObjectConnectivity = pd.concat(
        [safe22_dfs.PointObjectConnectivity, df_append], ignore_index=True
    )


def _add_safe16_design_strip_entry(safe16_dfs, strip_name, point_name, width, is_start_entry):
    """Add a design strip entry to SAFE16 ObjGeomDesignStrips DataFrame.

    Creates design strip definitions in SAFE16 format by associating strip properties with 
    start and end points. SAFE16 uses a point-based approach where each strip is defined 
    by entries at both its start and end points, with different width parameters assigned 
    to each end to define the strip geometry.

    This function handles the SAFE16-specific requirements for strip definition, including 
    proper width assignment, angle specification, and design group management. The strip 
    width is distributed between design and FEM (finite element mesh) parameters according 
    to SAFE16 conventions.
    """
    # Extract target column structure from existing DataFrame
    # Columns: Name, Point, Angle, FEMWidthLeft, FEMWidthRight, DesignWidthLeft, DesignWidthRight, DesignGroup
    target_columns = safe16_dfs.ObjGeomDesignStrips.columns.tolist()
    new_rows = []
    
    # Create strip entry based on position (start vs end point)
    if is_start_entry:
        # Start point entry: Defines strip beginning with design group assignment
        # DesignWidthRight=0 (no width on right side at start)
        # DesignGroup=width (assigns total width to design group)
        new_rows.append([
            strip_name, point_name, SAFE16_STRIP_ANGLE, None, None,
            None, 0, width])
    else:
        # End point entry: Defines strip ending with design width assignment
        # FEMWidthRight=0 (no FEM width on right side at end)
        # DesignWidthLeft=width (assigns total width to left design width)
        new_rows.append([
            strip_name, point_name, SAFE16_STRIP_ANGLE, None, 0,
            width, None, None])

    # Create DataFrame with proper MultiIndex column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Ensure numeric columns are properly typed to prevent dtype conflicts
    # Convert None values to np.nan and set float dtype for numeric columns
    # Columns structure: Name(0), Point(1), Angle(2), FEMWidthLeft(3), FEMWidthRight(4), 
    #                   DesignWidthLeft(5), DesignWidthRight(6), DesignGroup(7)
    # Indices 0-1 are strings, indices 2-7 should be numeric (float)
    for col_idx, col_name_tuple in enumerate(df_append.columns):
        if col_idx >= 2:  # Skip Name and Point columns (string types)
            df_append[col_name_tuple] = df_append[col_name_tuple].astype(float)

    # Append to existing design strips DataFrame
    if safe16_dfs.ObjGeomDesignStrips.empty:
        # Initialize DataFrame if empty
        safe16_dfs.ObjGeomDesignStrips = df_append
    else:
        # Concatenate with existing data (dtypes are now aligned)
        safe16_dfs.ObjGeomDesignStrips = pd.concat(
            [safe16_dfs.ObjGeomDesignStrips, df_append], ignore_index=True
        )



def _define_safe22_strip_connectivity(safe22_dfs, strip_name, start_point_name, end_point_name, width, layer):
    """Define design strip connectivity in SAFE22 StripObjectConnectivity DataFrame.

    Creates design strip connectivity definitions in SAFE22 format by establishing relationships 
    between strip start and end points. SAFE22 uses a connectivity-based approach where each 
    strip is defined by a single entry that connects two points and specifies width parameters 
    along the strip length.

    This function handles SAFE22-specific requirements for strip connectivity, including proper 
    width distribution, layer assignment, and design group management. The strip width is 
    distributed between left and right sides with consistent values at both ends.
    """
    # Extract target column structure from existing DataFrame
    # Columns: Name, ObjectName, Point1, Point2, WidthLeft, WidthRight, WidthLeftEnd, WidthRightEnd, AutoGenerate, DesignLayer, DesignGroupName
    target_columns = safe22_dfs.StripObjectConnectivity.columns.tolist()
    new_rows = []

    # Create strip connectivity entry with width distribution
    # WidthLeft=0, WidthRight=width: Assigns full width to right side
    # WidthLeftEnd=0, WidthRightEnd=width: Maintains consistent width at strip end
    new_rows.append([
        strip_name, SAFE22_STRIP_OBJECT_NAME, start_point_name, end_point_name,
        0, width, 0, width,  # Width distribution: Left=0, Right=full_width
        SAFE22_STRIP_AUTO_GENERATE, layer, SAFE22_STRIP_DESIGN_GROUP_NAME
    ])

    # Create DataFrame with proper MultiIndex column structure
    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))

    # Ensure numeric width columns are properly typed to prevent dtype conflicts
    # Column indices: Name(0), ObjectName(1), Point1(2), Point2(3), 
    #                WidthLeft(4), WidthRight(5), WidthLeftEnd(6), WidthRightEnd(7),
    #                AutoGenerate(8), DesignLayer(9), DesignGroupName(10)
    width_column_indices = [4, 5, 6, 7]  # Width-related numeric columns
    for col_idx, col_name_tuple in enumerate(df_append.columns):
        if col_idx in width_column_indices:
            df_append[col_name_tuple] = df_append[col_name_tuple].astype(float)

    # Append to existing strip connectivity DataFrame
    if safe22_dfs.StripObjectConnectivity.empty:
        # Initialize DataFrame if empty
        safe22_dfs.StripObjectConnectivity = df_append
    else:
        # Concatenate with existing data
        safe22_dfs.StripObjectConnectivity = pd.concat(
            [safe22_dfs.StripObjectConnectivity, df_append], ignore_index=True
        )


def _add_strip_design_overwrites(safe16_dfs, safe22_dfs, strip_name, layer):
    """Add slab design overwrites for design strips in both SAFE16 and SAFE22 formats.

    Creates design overwrite entries that specify reinforcement design parameters for design 
    strips in both SAFE16 and SAFE22 formats. These overwrites control how the software 
    performs reinforcement design calculations within each strip, including material 
    specifications, design type, and post-tensioning considerations.

    Design overwrites provide fine-grained control over the design process, allowing 
    engineers to specify different design approaches for different strips based on 
    structural requirements and design philosophy.
    """
    # SAFE16: SlabDesignOverwrites01Str
    target_columns = safe16_dfs.SlabDesignOverwrites01Str.columns.tolist()
    new_rows = []

    new_rows.append([
        strip_name, layer, SAFE16_SLAB_DESIGN_TYPE, SAFE16_SLAB_DESIGN_NUM_BARS,
        SAFE16_SLAB_DESIGN_IS_PT, COMMON_REBAR_MATERIAL, SAFE16_COVER_PREFERENCES
    ])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    safe16_dfs.SlabDesignOverwrites01Str = pd.concat(
        [safe16_dfs.SlabDesignOverwrites01Str, df_append], ignore_index=True
    )

    # SAFE22: ConcSlbOverStripBased
    target_columns = safe22_dfs.ConcSlbOverStripBased.columns.tolist()
    new_rows = []

    new_rows.append([
        strip_name, layer, SAFE22_SLAB_DESIGN_TYPE, SAFE22_SLAB_DESIGN_IS_PT_YES,
        SAFE22_SLAB_DESIGN_IS_PT_NO, COMMON_REBAR_MATERIAL, SAFE22_COVER_PREFERENCES,
        SAFE22_SLAB_DESIGN_NUM_BARS
    ])

    df_append = pd.DataFrame(new_rows, columns=pd.MultiIndex.from_tuples(target_columns))
    safe22_dfs.ConcSlbOverStripBased = pd.concat(
        [safe22_dfs.ConcSlbOverStripBased, df_append], ignore_index=True
    )



def _get_strip_segment_details(current_coord, boundary_coord, base_segment_width, is_approaching_from_larger_side):
    """
    Calculates the actual width for the current strip segment and the coordinate for the next iteration.
    """

    if is_approaching_from_larger_side:  # e.g., y decreasing from y_max towards y_min
        remaining_distance = current_coord - boundary_coord
        if remaining_distance <= 0:  # Already at or past boundary
            return 0, boundary_coord

        if current_coord - 1.5 * base_segment_width <= boundary_coord:  # Covers remaining < 1.5 * base_width
            actual_width = remaining_distance
            next_coord = boundary_coord
        # elif current_coord - base_segment_width <= boundary_coord: # This case is covered by the one above if remaining_distance is positive
        #     actual_width = remaining_distance
        #     next_coord = boundary_coord
        else:
            actual_width = base_segment_width
            next_coord = current_coord - base_segment_width
    else:  # e.g., x increasing from x_min towards x_max
        remaining_distance = boundary_coord - current_coord
        if remaining_distance <= 0:  # Already at or past boundary
            return 0, boundary_coord

        if current_coord + 1.5 * base_segment_width >= boundary_coord:  # Covers remaining < 1.5 * base_width
            actual_width = remaining_distance
            next_coord = boundary_coord
        # elif current_coord + base_segment_width >= boundary_coord:
        #     actual_width = remaining_distance
        #     next_coord = boundary_coord
        else:
            actual_width = base_segment_width
            next_coord = current_coord + base_segment_width

    # Ensure actual_width is not negative due to floating point issues if very close to boundary
    return max(0, actual_width), next_coord


def write_design_strip(excel_inputs, safe16_dfs, safe22_dfs):
    """Generate comprehensive design strips for SAFE16 and SAFE22 slab design analysis.

    Orchestrates the complete design strip generation workflow from Excel inputs to both 
    SAFE16 and SAFE22 finite element model formats. This function serves as the primary 
    interface for creating orthogonal design strip patterns essential for reinforced 
    concrete slab design according to structural design standards.

    The function processes slab geometry, grouping, and property information to automatically 
    generate design strips in both X and Y directions, creating the framework necessary for 
    strip-based reinforcement design, moment distribution analysis, and design verification.

    Design Strip Generation Process:
    1. Data extraction and validation from Excel inputs
    2. Slab group boundary detection and validation
    3. X-direction strip generation (Layer A) from top to bottom
    4. Y-direction strip generation (Layer B) from left to right
    5. Point coordinate creation for strip boundaries
    6. Strip connectivity establishment for finite element analysis
    7. Design overwrite configuration for reinforcement parameters
    """
    # Extract and prepare input data from Excel inputs
    df_point_assign = excel_inputs.Point.copy()        # Point coordinate data
    df_slab_assign = excel_inputs.Slab.copy()          # Slab assignment and grouping data
    df_slab_prop = excel_inputs.SlabProp.copy()        # Slab property definitions

    # =============================================================================
    # DATA PREPARATION AND VALIDATION
    # =============================================================================
    
    # Prepare point coordinate data for strip boundary calculations
    df_points = df_point_assign[['Point', 'X (m)', 'Y (m)']]
    df_points = df_points.rename(
        columns={'Point': 'Point', 'X (m)': 'X', 'Y (m)': 'Y'})

    # Standardize column names for consistent processing
    df_slab_assign.rename(columns={
        'Slab': 'Area (Text)',                          # Slab identifier
        'Soil Prop': 'SoilProp (Text)',                # Soil property reference
        'Slab Prop': 'SlabProp (Text)',                # Slab property reference
        'Load Group': 'LoadGroup (Text)',              # Load group assignment
        'Slab Group': 'SlabGroup (Text)'               # Critical: Slab group for strip generation
    }, inplace=True)

    # Validate essential slab group data availability
    # Design strip generation requires valid slab group information
    if 'SlabGroup (Text)' not in df_slab_assign.columns or df_slab_assign['SlabGroup (Text)'].dropna().empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} "Slab Group" data is missing or empty in the "Slab" input sheet. Cannot generate design strips.')
        return safe16_dfs, safe22_dfs

    df_areas = pd.DataFrame(columns=['Area', 'Slab Prop', 'Point'])
    matches = ['Point' in col for col in df_slab_assign.columns]
    column_start = pd.Series(matches).idxmax()
    column_area = df_slab_assign.columns.get_loc('Area (Text)')
    column_slab_prop = df_slab_assign.columns.get_loc('SlabProp (Text)')

    for i in range(df_slab_assign.index.size):
        area = df_slab_assign.iloc[i, column_area]
        slab_prop = df_slab_assign.iloc[i, column_slab_prop]
        for j in range(column_start, df_slab_assign.columns.size):
            point = df_slab_assign.iloc[i, j]
            if not pd.isna(point):
                # Using pd.concat for potentially better performance with many appends
                new_row = pd.DataFrame([[area, slab_prop, point]], columns=['Area', 'Slab Prop', 'Point'])
                df_areas = pd.concat([df_areas, new_row], ignore_index=True)

    df_slabprop = df_slab_prop.rename(columns={'Slab (Text)': 'Slab Prop'})
    df_slabprop = df_slabprop[['Slab Prop', 'Thickness (m)']]
    df_areas = pd.merge(df_areas, df_slabprop, on=['Slab Prop'], how='left')
    # drop the rows in df_areas if df_areas['Thickness (m)'].isnull()
    df_areas = df_areas[~df_areas['Thickness (m)'].isnull()].reset_index(drop=True)
    # Use left merge to keep all areas
    areas_points = pd.merge(df_areas, df_points, on=['Point'], how='left')  # Use left merge

    # NEW LOGIC for area_groups:
    # Extract Group and Area information directly from df_slab_assign (which is excel_inputs.Slab)
    # 'Area (Text)' is the slab identifier (original 'Slab' column from input)
    # 'SlabGroup (Text)' is the group identifier (original 'Slab Group' column from input)
    area_groups = df_slab_assign[['SlabGroup (Text)', 'Area (Text)']].copy()
    area_groups.rename(columns={'SlabGroup (Text)': 'Group', 'Area (Text)': 'Area'}, inplace=True)
    area_groups.dropna(subset=['Group'], inplace=True)  # Ensure all entries have a group
    area_groups.drop_duplicates(inplace=True)  # Ensure unique Group-Area pairings

    group_areas_points = pd.merge(area_groups, areas_points, on=['Area'], how='left')

    # Drop groups where essential data (like coordinates or thickness) might be missing for all points
    group_areas_points.dropna(subset=['X', 'Y', 'Thickness (m)'], inplace=True)

    x_min_df = group_areas_points.groupby('Group')['X'].min().reset_index().rename(columns={'X': 'Xmin (m)'})
    x_max_df = group_areas_points.groupby('Group')['X'].max().reset_index().rename(columns={'X': 'Xmax (m)'})
    y_min_df = group_areas_points.groupby('Group')['Y'].min().reset_index().rename(columns={'Y': 'Ymin (m)'})
    y_max_df = group_areas_points.groupby('Group')['Y'].max().reset_index().rename(columns={'Y': 'Ymax (m)'})

    group_boundary = group_areas_points.groupby('Group')['Thickness (m)'].min().reset_index()
    group_boundary = pd.merge(group_boundary, x_min_df, how='left', on=['Group'])
    group_boundary = pd.merge(group_boundary, x_max_df, how='left', on=['Group'])
    group_boundary = pd.merge(group_boundary, y_min_df, how='left', on=['Group'])
    group_boundary = pd.merge(group_boundary, y_max_df, how='left', on=['Group'])

    group_boundary.dropna(subset=['Xmin (m)', 'Xmax (m)', 'Ymin (m)', 'Ymax (m)', 'Thickness (m)'], inplace=True)

    # =============================================================================
    # DESIGN STRIP GENERATION
    # =============================================================================
    # Generate orthogonal design strip patterns for each slab group
    
    for i in range(group_boundary.index.size):
        # Extract slab group information for strip generation
        group_data = group_boundary.loc[i]
        group_name = group_data['Group']
        
        # Set strip width based on design requirements
        # Note: Currently hardcoded to 1.0m for consistent strip sizing
        # thickness_val = group_data['Thickness (m)']  # Alternative: use actual slab thickness
        thickness_val = 1.0  # Standard strip width for design strip generation

        # Extract slab group boundaries for strip generation limits
        x_min_bound = group_data['Xmin (m)']            # Left boundary of slab group
        x_max_bound = group_data['Xmax (m)']            # Right boundary of slab group
        y_min_bound = group_data['Ymin (m)']            # Bottom boundary of slab group
        y_max_bound = group_data['Ymax (m)']            # Top boundary of slab group

        # Define base strip widths for both directions
        base_width_x = thickness_val                    # Strip width for X-direction strips (Layer A)
        base_width_y = thickness_val                    # Strip width for Y-direction strips (Layer B)

        # =============================================================================
        # X-DIRECTION DESIGN STRIPS (LAYER A)
        # =============================================================================
        # Generate horizontal strips from top (Y-max) to bottom (Y-min)
        # These strips are used for reinforcement parallel to X-axis
        
        current_y = y_max_bound                         # Start from top boundary
        strip_num_x = 0                                 # Strip counter for naming
        iterations_x = 0                                # Safety counter to prevent infinite loops
        
        while current_y > y_min_bound and iterations_x < MAX_STRIP_SEGMENTS_PER_GROUP:
            iterations_x += 1                           # Increment safety counter
            strip_num_x += 1                            # Increment strip number
            
            # Calculate actual strip width and next position
            # Handles boundary conditions and ensures complete coverage
            actual_segment_width, next_y = _get_strip_segment_details(
                current_y, y_min_bound, base_width_x, is_approaching_from_larger_side=True
            )
            
            # Generate unique names for strip and its boundary points
            start_point_name = f"DSX_{group_name}_{strip_num_x}_Start"  # Left boundary point
            end_point_name = f"DSX_{group_name}_{strip_num_x}_End"      # Right boundary point
            strip_name = f"DSX_{group_name}_{strip_num_x}"              # Strip identifier
            
            # Create boundary points with offset beyond slab edges
            # Start point: Left side of slab group (X-min - offset)
            _create_strip_point(safe16_dfs, safe22_dfs, start_point_name, 
                               x_min_bound - STRIP_POINT_OFFSET, current_y)
            # End point: Right side of slab group (X-max + offset)
            _create_strip_point(safe16_dfs, safe22_dfs, end_point_name, 
                               x_max_bound + STRIP_POINT_OFFSET, current_y)
            
            # Create SAFE16 design strip entries (requires start and end point entries)
            _add_safe16_design_strip_entry(safe16_dfs, strip_name, start_point_name, 
                                          actual_segment_width, is_start_entry=True)
            _add_safe16_design_strip_entry(safe16_dfs, strip_name, end_point_name, 
                                          actual_segment_width, is_start_entry=False)
            
            # Create SAFE22 strip connectivity (single entry connects both points)
            _define_safe22_strip_connectivity(safe22_dfs, strip_name, start_point_name, end_point_name,
                                             actual_segment_width, SAFE22_STRIP_LAYER_A)
            
            # Add design overwrites for reinforcement design control
            _add_strip_design_overwrites(safe16_dfs, safe22_dfs, strip_name, SAFE22_STRIP_LAYER_A)
            
            # Move to next strip position
            current_y = next_y

        # =============================================================================
        # Y-DIRECTION DESIGN STRIPS (LAYER B)
        # =============================================================================
        # Generate vertical strips from left (X-min) to right (X-max)
        # These strips are used for reinforcement parallel to Y-axis
        
        current_x = x_min_bound                         # Start from left boundary
        strip_num_y = 0                                 # Strip counter for naming
        iterations_y = 0                                # Safety counter to prevent infinite loops
        
        while current_x < x_max_bound and iterations_y < MAX_STRIP_SEGMENTS_PER_GROUP:
            iterations_y += 1                           # Increment safety counter
            strip_num_y += 1                            # Increment strip number
            
            # Calculate actual strip width and next position
            # Handles boundary conditions and ensures complete coverage
            actual_segment_width, next_x = _get_strip_segment_details(
                current_x, x_max_bound, base_width_y, is_approaching_from_larger_side=False
            )
            
            # Generate unique names for strip and its boundary points
            start_point_name = f"DSY_{group_name}_{strip_num_y}_Start"  # Bottom boundary point
            end_point_name = f"DSY_{group_name}_{strip_num_y}_End"      # Top boundary point
            strip_name = f"DSY_{group_name}_{strip_num_y}"              # Strip identifier
            
            # Create boundary points with offset beyond slab edges
            # Start point: Bottom side of slab group (Y-min - offset)
            _create_strip_point(safe16_dfs, safe22_dfs, start_point_name, 
                               current_x, y_min_bound - STRIP_POINT_OFFSET)
            # End point: Top side of slab group (Y-max + offset)
            _create_strip_point(safe16_dfs, safe22_dfs, end_point_name, 
                               current_x, y_max_bound + STRIP_POINT_OFFSET)
            
            # Create SAFE16 design strip entries (requires start and end point entries)
            _add_safe16_design_strip_entry(safe16_dfs, strip_name, start_point_name, 
                                          actual_segment_width, is_start_entry=True)
            _add_safe16_design_strip_entry(safe16_dfs, strip_name, end_point_name, 
                                          actual_segment_width, is_start_entry=False)
            
            # Create SAFE22 strip connectivity (single entry connects both points)
            _define_safe22_strip_connectivity(safe22_dfs, strip_name, start_point_name, end_point_name,
                                             actual_segment_width, SAFE22_STRIP_LAYER_B)
            
            # Add design overwrites for reinforcement design control
            _add_strip_design_overwrites(safe16_dfs, safe22_dfs, strip_name, SAFE22_STRIP_LAYER_B)
            
            # Move to next strip position
            current_x = next_x

    # Print confirmation message with timestamp indicating successful processing
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Generated design strip to SAFE format!')
    
    return safe16_dfs, safe22_dfs
