"""
Pier Force Processor Module for ETABS CWLS

This module handles the processing of pier force data from ETABS to create
comprehensive load schedules. It manages both gravity and lateral load models
with proper coordinate transformations and schedule generation.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional pier force processing algorithms

Key Functions:
    create_schedule_pier_force: Main pier force processing workflow
    process_pier_forces: Process gravity and lateral pier forces
    filter_and_process_pier_forces: Filter and process pier force data
    separate_column_wall_schedules: Separate schedules for columns and walls

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import pandas as pd
import math
import time
from typing import Optional, Callable, List

from etabs_cwls.core import _class
from etabs_cwls.io._read import read_excel, read_mdbs
from etabs_cwls.io._write import write_excel, format_load_schedule_excel
from etabs_cwls.processing.coordinate_transformer import create_raw_schedule
from etabs_cwls.processing.schedule_generator import create_final_schedule, merge_schedules
from etabs_cwls.config.exceptions import DataValidationError, ETABSConnectionError, FileOperationError, CalculationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger
)


def create_schedule_pier_force(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> _class.design_data:
    """
    Create comprehensive load schedule from pier force data.
    
    This function processes pier force data from ETABS to create detailed load
    schedules including raw schedules, final schedules with load factors, and
    separate schedules for columns and walls. It handles both gravity and
    lateral load models with proper coordinate transformations.
    
    Args:
        file_path: File path object with configured paths
        design_data: Design data object to populate
        ETABS_converter: Configured ETABS converter object
        log_callback: Optional logging callback function
        
    Returns:
        Updated design data object with pier force schedules
        
    Raises:
        DataValidationError: If input parameters are invalid
        ETABSConnectionError: If pier force data access fails
        FileOperationError: If Excel operations fail
        CalculationError: If schedule calculation fails
        
    Example:
        >>> design_data = create_schedule_pier_force(file_path, design_data, converter)
    """
    log_function_entry(log_callback, "create_schedule_pier_force")
    
    try:
        # Input validation
        if not isinstance(file_path, _class.file_path):
            raise DataValidationError(
                f"file_path must be a file_path instance, got {type(file_path).__name__}",
                field_name="file_path",
                invalid_value=type(file_path).__name__,
                expected_type="file_path",
                error_code="INVALID_FILE_PATH_TYPE"
            )
        
        if not isinstance(design_data, _class.design_data):
            raise DataValidationError(
                f"design_data must be a design_data instance, got {type(design_data).__name__}",
                field_name="design_data",
                invalid_value=type(design_data).__name__,
                expected_type="design_data",
                error_code="INVALID_DESIGN_DATA_TYPE"
            )
        
        enhanced_log(log_callback, "Starting pier force schedule creation", 'INFO')
        
        with create_timed_logger(log_callback, "pier_force_schedule_creation") as timer:
            # Read configuration data
            read_configuration_data(file_path, design_data, log_callback)
            
            # Read pier assignment and section data
            read_pier_data(file_path, design_data, ETABS_converter, log_callback)
            
            # Process gravity and lateral pier forces
            process_pier_forces(file_path, design_data, ETABS_converter, log_callback)
            
            # Create raw and final schedules
            loadcase_list = create_pier_force_schedules(design_data, log_callback)
            
            # Separate schedules for columns and walls
            separate_column_wall_schedules(design_data, log_callback)
            
            # Write all schedules to Excel
            write_pier_force_schedules(file_path, design_data, loadcase_list, log_callback)
        
        enhanced_log(log_callback, "Pier force schedule creation completed successfully", 'INFO')
        log_validation_result(log_callback, "pier_force_schedule_creation", True,
                            f"Successfully created schedules for {len(loadcase_list)} load cases")
        log_function_exit(log_callback, "create_schedule_pier_force", "Success")
        
        return design_data
        
    except Exception as e:
        log_error_with_context(log_callback, e, "create_schedule_pier_force")
        log_validation_result(log_callback, "pier_force_schedule_creation", False, str(e))
        raise


def read_configuration_data(
    file_path: _class.file_path,
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> None:
    """Read configuration data from Excel files."""
    log_function_entry(log_callback, "read_configuration_data")
    
    try:
        # Read load mapping
        enhanced_log(log_callback, "Reading load mapping configuration", 'DEBUG')
        design_data.df_mapping = read_excel(file_path.design_excel_path, file_path.mapping, log_callback)
        log_calculation_result(log_callback, "mapping_entries", len(design_data.df_mapping), "entries")
        
        # Read global rotation parameter
        enhanced_log(log_callback, "Reading global rotation parameter", 'DEBUG')
        parameter_df = read_excel(file_path.design_excel_path, file_path.parameter, log_callback)
        global_rotation_deg = parameter_df.loc[0, 'Global Rotation']
        design_data.df_addrotation = math.radians(global_rotation_deg)
        
        log_calculation_result(log_callback, "global_rotation_degrees", global_rotation_deg, "degrees")
        log_calculation_result(log_callback, "global_rotation_radians", design_data.df_addrotation, "radians")
        
        log_function_exit(log_callback, "read_configuration_data", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "read_configuration_data")
        raise


def read_pier_data(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> None:
    """Read pier assignment and section data."""
    log_function_entry(log_callback, "read_pier_data")
    
    try:
        # Read frame pier assignments (optional)
        try:
            table_name = ETABS_converter.TN_FAPier
            enhanced_log(log_callback, f"Reading frame pier assignments: {table_name}", 'DEBUG')
            design_data.df_framepier = read_mdbs(file_path.accessfile1, table_name, log_callback)
            
            # Standardize column name
            if ETABS_converter.CN_FPierName == "Pier Label":
                design_data.df_framepier.rename(columns={'Pier Label': 'Pier'}, inplace=True)
            
            log_calculation_result(log_callback, "frame_pier_assignments", len(design_data.df_framepier), "assignments")
            
        except Exception as e:
            enhanced_log(log_callback, f"Frame pier assignments not available: {e}", 'WARNING')
            design_data.df_framepier = None
        
        # Read shell pier assignments (optional)
        try:
            table_name = ETABS_converter.TN_SAPier
            enhanced_log(log_callback, f"Reading shell pier assignments: {table_name}", 'DEBUG')
            design_data.df_shellpier = read_mdbs(file_path.accessfile1, table_name, log_callback)
            
            # Standardize column name
            if ETABS_converter.CN_SPierName == "Pier Name":
                design_data.df_shellpier.rename(columns={'Pier Name': 'Pier'}, inplace=True)
            
            log_calculation_result(log_callback, "shell_pier_assignments", len(design_data.df_shellpier), "assignments")
            
        except Exception as e:
            enhanced_log(log_callback, f"Shell pier assignments not available: {e}", 'WARNING')
            design_data.df_shellpier = None
        
        # Read pier section properties (required)
        try:
            table_name = ETABS_converter.TN_PSProperties
            enhanced_log(log_callback, f"Reading pier section properties: {table_name}", 'DEBUG')
            design_data.df_piersection = read_mdbs(file_path.accessfile1, table_name, log_callback)
            
            # Extract only required columns and remove duplicates
            design_data.df_piersection = design_data.df_piersection[['Pier', 'AxisAngle']].drop_duplicates()
            
            log_calculation_result(log_callback, "pier_sections", len(design_data.df_piersection), "sections")
            
        except Exception as e:
            raise ETABSConnectionError(
                f"Failed to read pier section properties: {str(e)}",
                database_path=file_path.accessfile1,
                table_name=table_name,
                error_code="PIER_SECTION_READ_FAILED"
            ) from e
        
        log_function_exit(log_callback, "read_pier_data", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "read_pier_data")
        raise


def process_pier_forces(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> None:
    """Process gravity and lateral pier forces."""
    log_function_entry(log_callback, "process_pier_forces")
    
    try:
        # Read story data for elevation information
        base_df = read_excel(file_path.design_excel_path, file_path.storylist, log_callback)
        
        # Process gravity loads
        process_gravity_pier_forces(file_path, design_data, ETABS_converter, base_df, log_callback)
        
        # Process lateral loads if secondary file exists
        if file_path.accessfile2:
            process_lateral_pier_forces(file_path, design_data, ETABS_converter, base_df, log_callback)
        else:
            enhanced_log(log_callback, "No secondary file - skipping lateral pier forces", 'INFO')
        
        log_function_exit(log_callback, "process_pier_forces", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "process_pier_forces")
        raise


def process_gravity_pier_forces(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    base_df: pd.DataFrame,
    log_callback: Optional[Callable] = None
) -> None:
    """Process gravity pier forces from primary file."""
    log_function_entry(log_callback, "process_gravity_pier_forces")

    try:
        # Get gravity load names
        GL_list = design_data.df_mapping['Load Name(Gravity)'].tolist()
        GL_list = [name for name in GL_list if pd.notnull(name)]

        log_calculation_result(log_callback, "gravity_load_names", len(GL_list), "loads")
        enhanced_log(log_callback, f"Gravity loads: {', '.join(GL_list[:5])}{'...' if len(GL_list) > 5 else ''}", 'DEBUG')

        # Read pier force data if not already loaded
        if design_data.df_pierforceG is None:
            try:
                table_name = ETABS_converter.TN_PierForce
                enhanced_log(log_callback, f"Reading gravity pier forces: {table_name}", 'DEBUG')
                design_data.df_pierforceG = read_mdbs(file_path.accessfile1, table_name, log_callback)

                # Standardize column name for ETABS 2021
                if ETABS_converter.CN_CaseCombo == "Output Case":
                    design_data.df_pierforceG.rename(columns={'Output Case': 'CaseCombo'}, inplace=True)

                log_calculation_result(log_callback, "gravity_pier_force_records", len(design_data.df_pierforceG), "records")

            except Exception as e:
                raise ETABSConnectionError(
                    f"Failed to read gravity pier forces: {str(e)}",
                    database_path=file_path.accessfile1,
                    table_name=table_name,
                    error_code="GRAVITY_PIER_FORCE_READ_FAILED"
                ) from e

        # Filter and process gravity pier forces
        design_data.df_pierforceG = filter_and_process_pier_forces(
            design_data.df_pierforceG, GL_list, base_df, design_data.df_piersection,
            "gravity", log_callback
        )

        log_function_exit(log_callback, "process_gravity_pier_forces", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "process_gravity_pier_forces")
        raise


def process_lateral_pier_forces(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    base_df: pd.DataFrame,
    log_callback: Optional[Callable] = None
) -> None:
    """Process lateral pier forces from secondary file."""
    log_function_entry(log_callback, "process_lateral_pier_forces")

    try:
        # Get lateral load names
        LL_list = design_data.df_mapping['Load Name(Wind)'].tolist()
        LL_list = [name for name in LL_list if pd.notnull(name)]

        log_calculation_result(log_callback, "lateral_load_names", len(LL_list), "loads")
        enhanced_log(log_callback, f"Lateral loads: {', '.join(LL_list[:5])}{'...' if len(LL_list) > 5 else ''}", 'DEBUG')

        # Read pier force data if not already loaded
        if design_data.df_pierforceL is None:
            try:
                table_name = ETABS_converter.TN_PierForce
                enhanced_log(log_callback, f"Reading lateral pier forces: {table_name}", 'DEBUG')
                design_data.df_pierforceL = read_mdbs(file_path.accessfile2, table_name, log_callback)

                # Standardize column name for ETABS 2021
                if ETABS_converter.CN_CaseCombo == "Output Case":
                    design_data.df_pierforceL.rename(columns={'Output Case': 'CaseCombo'}, inplace=True)

                log_calculation_result(log_callback, "lateral_pier_force_records", len(design_data.df_pierforceL), "records")

            except Exception as e:
                raise ETABSConnectionError(
                    f"Failed to read lateral pier forces: {str(e)}",
                    database_path=file_path.accessfile2,
                    table_name=table_name,
                    error_code="LATERAL_PIER_FORCE_READ_FAILED"
                ) from e

        # Filter and process lateral pier forces
        design_data.df_pierforceL = filter_and_process_pier_forces(
            design_data.df_pierforceL, LL_list, base_df, design_data.df_piersection,
            "lateral", log_callback
        )

        log_function_exit(log_callback, "process_lateral_pier_forces", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "process_lateral_pier_forces")
        raise


def filter_and_process_pier_forces(
    df_pierforce: pd.DataFrame,
    load_list: List[str],
    base_df: pd.DataFrame,
    df_piersection: pd.DataFrame,
    force_type: str,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """Filter and process pier force data."""
    log_function_entry(log_callback, "filter_and_process_pier_forces",
                      force_type=force_type, load_count=len(load_list))

    try:
        # Filter by load cases
        df_filtered = df_pierforce[df_pierforce['CaseCombo'].isin(load_list)]
        log_calculation_result(log_callback, f"{force_type}_filtered_records", len(df_filtered), "records")

        # Remove top location records (keep base reactions only)
        df_filtered = df_filtered.drop(df_filtered[df_filtered['Location'] == 'Top'].index)
        df_filtered = df_filtered.reset_index(drop=True)
        log_calculation_result(log_callback, f"{force_type}_base_records", len(df_filtered), "records")

        # Select required columns
        required_columns = ['Pier', 'Story', 'CaseCombo', 'P', 'V2', 'V3', 'M2', 'M3', 'T']
        df_filtered = df_filtered[required_columns]

        # Merge with story data for elevation
        df_filtered = pd.merge(df_filtered, base_df, left_on='Story', right_on='Name')

        # Merge with pier section data for axis angle
        df_filtered = pd.merge(df_filtered, df_piersection, left_on='Pier', right_on='Pier')

        # Keep only minimum elevation for each pier
        min_elevations = df_filtered.groupby('Pier')['Elevation'].min()
        df_filtered = df_filtered.merge(
            min_elevations.reset_index().rename(columns={'Elevation': 'min_elevation'}),
            on='Pier'
        )
        df_filtered = df_filtered[df_filtered['Elevation'] == df_filtered['min_elevation']]
        df_filtered = df_filtered.drop('min_elevation', axis=1).reset_index(drop=True)

        log_calculation_result(log_callback, f"{force_type}_final_records", len(df_filtered), "records")
        log_calculation_result(log_callback, f"{force_type}_unique_piers", df_filtered['Pier'].nunique(), "piers")

        log_function_exit(log_callback, "filter_and_process_pier_forces", f"{len(df_filtered)} records")
        return df_filtered

    except Exception as e:
        log_error_with_context(log_callback, e, f"filter_and_process_pier_forces({force_type})")
        raise


def create_pier_force_schedules(
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> List[str]:
    """Create raw and final pier force schedules."""
    log_function_entry(log_callback, "create_pier_force_schedules")

    try:
        enhanced_log(log_callback, "Creating pier force schedules", 'INFO')

        # Step 1: Create raw schedules for each load case
        raw_schedules = {}
        loadcase_list = []

        for index, row in design_data.df_mapping.iterrows():
            loadcase = row['Load Case']

            # Check gravity loads first
            loadname = row['Load Name(Gravity)']
            if pd.isna(loadname):
                # Check wind loads
                loadname = row['Load Name(Wind)']
                if pd.isna(loadname):
                    continue  # Skip if no load name specified
                else:
                    # Use lateral pier forces
                    if design_data.df_pierforceL is not None:
                        loadcase_list.append(loadcase)
                        df_pierforce = design_data.df_pierforceL
                        raw_schedules[loadcase] = create_raw_schedule(
                            df_pierforce, loadname, loadcase, design_data.df_addrotation,
                            consider_torsion=design_data.consider_torsion, log_callback=log_callback
                        )
            else:
                # Use gravity pier forces
                if design_data.df_pierforceG is not None:
                    loadcase_list.append(loadcase)
                    df_pierforce = design_data.df_pierforceG
                    raw_schedules[loadcase] = create_raw_schedule(
                        df_pierforce, loadname, loadcase, design_data.df_addrotation,
                        consider_torsion=design_data.consider_torsion, log_callback=log_callback
                    )

        design_data.raw_schedules = raw_schedules
        log_calculation_result(log_callback, "raw_schedules_created", len(raw_schedules), "schedules")

        # Step 2: Merge all raw schedules into a single DataFrame
        design_data.df_schedule_raw = merge_schedules(raw_schedules, log_callback)
        log_calculation_result(log_callback, "raw_schedule_rows", len(design_data.df_schedule_raw), "rows")

        # Step 3: Apply load factors to create final schedules
        final_schedules = {}
        for loadcase in loadcase_list:
            final_schedules[loadcase] = create_final_schedule(
                loadcase, design_data, raw_schedules,
                consider_torsion=design_data.consider_torsion, log_callback=log_callback
            )

        # Merge final schedules
        design_data.df_schedule_final = merge_schedules(final_schedules, log_callback)
        log_calculation_result(log_callback, "final_schedule_rows", len(design_data.df_schedule_final), "rows")

        enhanced_log(log_callback, f"Created schedules for {len(loadcase_list)} load cases", 'INFO')
        log_function_exit(log_callback, "create_pier_force_schedules", f"{len(loadcase_list)} cases")

        return loadcase_list

    except Exception as e:
        log_error_with_context(log_callback, e, "create_pier_force_schedules")
        raise


def separate_column_wall_schedules(
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> None:
    """Separate schedules for columns and walls."""
    log_function_entry(log_callback, "separate_column_wall_schedules")

    try:
        # Separate column schedule (frame piers)
        if design_data.df_framepier is not None:
            mask_C = design_data.df_schedule_final['Pier'].isin(design_data.df_framepier['Pier'])
            design_data.df_schedule_C = design_data.df_schedule_final[mask_C].reset_index(drop=True)
            log_calculation_result(log_callback, "column_schedule_rows", len(design_data.df_schedule_C), "rows")
        else:
            enhanced_log(log_callback, "No frame pier data - skipping column schedule", 'WARNING')
            design_data.df_schedule_C = None

        # Separate wall schedule (shell piers)
        if design_data.df_shellpier is not None:
            mask_W = design_data.df_schedule_final['Pier'].isin(design_data.df_shellpier['Pier'])
            design_data.df_schedule_W = design_data.df_schedule_final[mask_W].reset_index(drop=True)
            log_calculation_result(log_callback, "wall_schedule_rows", len(design_data.df_schedule_W), "rows")
        else:
            enhanced_log(log_callback, "No shell pier data - skipping wall schedule", 'WARNING')
            design_data.df_schedule_W = None

        log_function_exit(log_callback, "separate_column_wall_schedules", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "separate_column_wall_schedules")
        raise


def write_pier_force_schedules(
    file_path: _class.file_path,
    design_data: _class.design_data,
    loadcase_list: List[str],
    log_callback: Optional[Callable] = None
) -> None:
    """Write all pier force schedules to Excel."""
    log_function_entry(log_callback, "write_pier_force_schedules",
                      schedule_count=4, loadcase_count=len(loadcase_list))

    try:
        enhanced_log(log_callback, "Writing pier force schedules to Excel", 'INFO')

        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Write final schedule
                write_excel(design_data.df_schedule_final, file_path.design_excel_path,
                          file_path.loadschedule, log_callback)
                format_load_schedule_excel(file_path.design_excel_path, file_path.loadschedule,
                                         loadcase_list, consider_torsion=design_data.consider_torsion,
                                         log_callback=log_callback)

                # Write raw schedule
                write_excel(design_data.df_schedule_raw, file_path.design_excel_path,
                          file_path.loadschedule_raw, log_callback)
                format_load_schedule_excel(file_path.design_excel_path, file_path.loadschedule_raw,
                                         loadcase_list, consider_torsion=design_data.consider_torsion,
                                         log_callback=log_callback)

                # Write column schedule if available
                if design_data.df_schedule_C is not None:
                    write_excel(design_data.df_schedule_C, file_path.design_excel_path,
                              file_path.loadschedule_C, log_callback)
                    format_load_schedule_excel(file_path.design_excel_path, file_path.loadschedule_C,
                                             loadcase_list, consider_torsion=design_data.consider_torsion,
                                             log_callback=log_callback)

                # Write wall schedule if available
                if design_data.df_schedule_W is not None:
                    write_excel(design_data.df_schedule_W, file_path.design_excel_path,
                              file_path.loadschedule_W, log_callback)
                    format_load_schedule_excel(file_path.design_excel_path, file_path.loadschedule_W,
                                             loadcase_list, consider_torsion=design_data.consider_torsion,
                                             log_callback=log_callback)

                enhanced_log(log_callback, "All pier force schedules written successfully", 'INFO')

                # Clear file paths to prevent reuse
                file_path.accessfile1 = None
                file_path.accessfile2 = None

                break  # Success - exit retry loop

            except PermissionError as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise FileOperationError(
                        f"Cannot write Excel file after {max_retries} attempts. "
                        f"Please close the Excel file: {file_path.design_excel_path}",
                        file_path=file_path.design_excel_path,
                        operation="write",
                        error_code="EXCEL_FILE_LOCKED"
                    ) from e

                enhanced_log(log_callback, f"Excel file locked, retry {retry_count}/{max_retries}", 'WARNING')
                print(f"⚠️  Excel file is locked. Retrying in 3 seconds... ({retry_count}/{max_retries})")
                time.sleep(3)

            except Exception as e:
                raise FileOperationError(
                    f"Failed to write pier force schedules: {str(e)}",
                    file_path=file_path.design_excel_path,
                    operation="write",
                    error_code="PIER_FORCE_SCHEDULE_WRITE_FAILED"
                ) from e

        log_function_exit(log_callback, "write_pier_force_schedules", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "write_pier_force_schedules")
        raise


# Export all functions
__all__ = [
    'create_schedule_pier_force',
    'read_configuration_data',
    'read_pier_data',
    'process_pier_forces',
    'process_gravity_pier_forces',
    'process_lateral_pier_forces',
    'filter_and_process_pier_forces',
    'create_pier_force_schedules',
    'separate_column_wall_schedules',
    'write_pier_force_schedules'
]
