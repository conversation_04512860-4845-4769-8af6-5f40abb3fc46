﻿"""
Basic geometric type definitions
"""

from typing import Tuple, NamedTuple
import pandas as pd

# Basic geometric types
Point2D = Tuple[float, float]
Point3D = Tuple[float, float, float]

class BoundingBox(NamedTuple):
    """Bounding box coordinates."""
    min_x: float
    min_y: float
    max_x: float
    max_y: float
    
    @property
    def width(self) -> float:
        return self.max_x - self.min_x
    
    @property  
    def height(self) -> float:
        return self.max_y - self.min_y
    
    @property
    def center(self) -> Point2D:
        return ((self.min_x + self.max_x) / 2, (self.min_y + self.max_y) / 2) 


class ExcelInputs:
    """Excel input data structure for pile estimation."""
    def __init__(self):
        # Initialize empty DataFrames for all required data
        self.Point = pd.DataFrame(columns=['Point', 'X (m)', 'Y (m)'])
        self.Column = pd.DataFrame(columns=['Column', 'Center Point', 'Base Level', 'Points'])
        self.Wall = pd.DataFrame(columns=['Wall', 'Center Point', 'Points', 'Thickness (mm)', 'Base Level', 'Wall Group'])
        self.SiteBoundary = pd.DataFrame(columns=['X (m)', 'Y (m)'])
        
        # Load data
        self.InputLoadColumn = pd.DataFrame()
        self.InputLoadWall = pd.DataFrame()
        
        # Other required DataFrames can be added as needed
        self.Material = pd.DataFrame()
        self.ColumnProp = pd.DataFrame()
        self.LoadPat = pd.DataFrame()
        self.LoadCase = pd.DataFrame()
        self.LoadComb = pd.DataFrame() 

