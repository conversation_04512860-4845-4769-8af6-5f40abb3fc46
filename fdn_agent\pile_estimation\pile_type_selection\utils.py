﻿"""
Pile Type Selection Utility Functions

This module contains utility functions extracted from the pile type selection workflow
to improve code organization and reusability.

Author: Foundation Automation System
Date: June 14, 2025
"""

from typing import List, Dict, Any, Optional
import logging
from math import sqrt

from ..data_types.pile_preselection_types import (
    PileTypePreselectionCriteria,
    PileTypeCandidate
)
from ..utils.pile_preselection_utils import (
    create_pile_type_candidates_from_gui,
    integrate_with_existing_capacity_calculation
)
from ..layout_generation.case_1_layouts import handle_case_1_with_preselection
from ..layout_generation.case_2_layouts import handle_case_2_with_preselection
from ..layout_generation.case_4_layouts import handle_case_4
from ..data_types import Point2D, GroupElements, LocalCoordinateSystem

# Import enhanced logging utilities
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)


class PileTypeSelectionUtils:
    """
    Utility class for pile type selection operations.
    
    Contains methods for preparing candidates, running optimization,
    and combining results.
    """
    
    def __init__(self, log_callback: Optional[callable] = None):
        """Initialize with optional logging callback."""
        log_function_entry(log_callback, "PileTypeSelectionUtils.__init__")

        enhanced_log(log_callback, "Initializing Pile Type Selection Utilities with enhanced logging", 'INFO')

        self.log_callback = log_callback or (lambda msg: logging.info(msg))

        log_function_exit(log_callback, "PileTypeSelectionUtils.__init__")
    
    def prepare_pile_type_candidates(self,
                                   selected_pile_types: List[Dict[str, Any]],
                                   excel_inputs: Any) -> List[PileTypeCandidate]:
        """Prepare pile type candidates with GUI capacities as primary source."""

        log_function_entry(self.log_callback, "prepare_pile_type_candidates",
                          num_selected_types=len(selected_pile_types),
                          has_excel_inputs=excel_inputs is not None)

        with create_timed_logger(self.log_callback, "prepare_pile_type_candidates"):
            enhanced_log(self.log_callback, "Preparing pile type candidates from GUI data", 'INFO')
            log_performance_metric(self.log_callback, "Input pile types", len(selected_pile_types), "types")

            # Validate input
            if not selected_pile_types:
                error_msg = "No pile types selected from GUI"
                log_validation_result(self.log_callback, "GUI pile types validation", False, error_msg)
                enhanced_log(self.log_callback, error_msg, 'WARNING')
                log_function_exit(self.log_callback, "prepare_pile_type_candidates", "Empty list")
                return []
            else:
                log_validation_result(self.log_callback, "GUI pile types validation", True,
                                    f"{len(selected_pile_types)} pile types provided")

            # Create base candidates from GUI data
            log_algorithm_step(self.log_callback, "Candidate Preparation", "Creating base candidates from GUI data")
            candidates = create_pile_type_candidates_from_gui(selected_pile_types, self.log_callback)
            log_calculation_result(self.log_callback, "Base candidates created", len(candidates))

            # Use GUI capacities directly - no enhancement needed during pre-selection
            enhanced_candidates = []
            log_algorithm_step(self.log_callback, "Candidate Preparation", "Processing GUI capacities")

            for i, candidate in enumerate(candidates):
                # Use GUI capacity as primary and final source during pre-selection
                gui_capacity = candidate.capacity_per_pile

                # Validate candidate capacity
                if gui_capacity <= 0:
                    error_msg = f"Invalid capacity for {candidate.display_name}: {gui_capacity} kN"
                    log_validation_result(self.log_callback, f"Candidate {i+1} capacity validation", False, error_msg)
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    continue
                else:
                    log_validation_result(self.log_callback, f"Candidate {i+1} capacity validation", True,
                                        f"{candidate.display_name}: {gui_capacity:.1f} kN")

                log_calculation_result(self.log_callback, f"{candidate.display_name} capacity", f"{gui_capacity:.1f} kN")
                enhanced_log(self.log_callback, f"   {candidate.display_name}: {gui_capacity:.1f} kN capacity", 'DEBUG')

                # SKIP capacity enhancement during pre-selection phase
                # The GUI-provided capacities are the user's intended values
                # Attempting to enhance them requires existing pile data which doesn't exist yet
                enhanced_log(self.log_callback, f"Using GUI capacity directly for {candidate.display_name} (no enhancement needed)", 'DEBUG')

                enhanced_candidates.append(candidate)

            log_calculation_result(self.log_callback, "Final enhanced candidates", len(enhanced_candidates))
            log_performance_metric(self.log_callback, "Candidate preparation success rate",
                                 len(enhanced_candidates) / len(candidates) if candidates else 0, "ratio")

            enhanced_log(self.log_callback, f"Successfully prepared {len(enhanced_candidates)} pile type candidates", 'INFO')
            log_function_exit(self.log_callback, "prepare_pile_type_candidates", f"{len(enhanced_candidates)} candidates")
            return enhanced_candidates
    
    def perform_preselection(self,
                           group_elements: GroupElements,
                           required_load: float,
                           excel_inputs: Any,
                           pile_candidates: List[PileTypeCandidate],
                           user_edge_distance: float,
                           site_boundary: Optional[Any],
                           create_preselection_dxf: bool,
                           output_dir: Optional[str],
                           preselector: Any) -> Any:
        """Perform pile type pre-selection with DXF visualization."""

        log_function_entry(self.log_callback, "perform_preselection",
                          required_load=required_load,
                          num_candidates=len(pile_candidates),
                          user_edge_distance=user_edge_distance,
                          create_dxf=create_preselection_dxf,
                          has_site_boundary=site_boundary is not None)

        with create_timed_logger(self.log_callback, "perform_preselection"):
            enhanced_log(self.log_callback, "Performing pile type pre-selection with DXF visualization", 'INFO')

            # Log input parameters
            log_calculation_result(self.log_callback, "Required load", f"{required_load:.1f} kN")
            log_calculation_result(self.log_callback, "User edge distance", f"{user_edge_distance:.2f} m")
            log_performance_metric(self.log_callback, "Pile candidates available", len(pile_candidates), "candidates")
            log_calculation_result(self.log_callback, "Create DXF", create_preselection_dxf)

            # Validate inputs
            if required_load <= 0:
                error_msg = f"Invalid required load: {required_load} kN (must be positive)"
                log_validation_result(self.log_callback, "Required load validation", False, error_msg)
                enhanced_log(self.log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)
            else:
                log_validation_result(self.log_callback, "Required load validation", True, f"{required_load:.1f} kN")

            if not pile_candidates:
                error_msg = "No pile candidates provided for preselection"
                log_validation_result(self.log_callback, "Pile candidates validation", False, error_msg)
                enhanced_log(self.log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)
            else:
                log_validation_result(self.log_callback, "Pile candidates validation", True, f"{len(pile_candidates)} candidates")

            # Create preselection criteria
            log_algorithm_step(self.log_callback, "Preselection Setup", "Creating preselection criteria")
            criteria = PileTypePreselectionCriteria(
                required_total_load=required_load,
                group_elements=group_elements,
                excel_inputs=excel_inputs,
                user_edge_distance=user_edge_distance,
                site_boundary=site_boundary
            )

            enhanced_log(self.log_callback, "Preselection criteria created successfully", 'DEBUG')

            # Perform preselection
            log_algorithm_step(self.log_callback, "Preselection Execution", "Running pile type preselection")
            result = preselector.preselect_pile_type(criteria, pile_candidates, create_preselection_dxf, output_dir)

            if result is None:
                error_msg = "Preselection failed to return results"
                log_validation_result(self.log_callback, "Preselection execution", False, error_msg)
                enhanced_log(self.log_callback, error_msg, 'ERROR')
                log_function_exit(self.log_callback, "perform_preselection", "Failed - No results")
                raise RuntimeError(error_msg)
            else:
                log_validation_result(self.log_callback, "Preselection execution", True,
                                    f"Selected: {result.selected_pile_type.display_name}")
                enhanced_log(self.log_callback, f"✓ Preselection completed successfully: {result.selected_pile_type.display_name}", 'INFO')
                log_function_exit(self.log_callback, "perform_preselection", f"Success - {result.selected_pile_type.display_name}")
                return result
    
    def prepare_optimization_input(self,
                                 group_elements: GroupElements,
                                 preselection_result: Any,
                                 min_spacing: float,
                                 pile_diameter: float) -> Dict[str, Any]:
        """Prepare input for NSGA-III optimization."""

        log_function_entry(self.log_callback, "prepare_optimization_input",
                          min_spacing=min_spacing,
                          pile_diameter=pile_diameter,
                          has_preselection_result=preselection_result is not None)

        with create_timed_logger(self.log_callback, "prepare_optimization_input"):
            enhanced_log(self.log_callback, "Preparing NSGA-III optimization input from preselection results", 'INFO')

            # Validate preselection result
            if preselection_result is None:
                error_msg = "No preselection result provided for optimization input preparation"
                log_validation_result(self.log_callback, "Preselection result validation", False, error_msg)
                enhanced_log(self.log_callback, error_msg, 'ERROR')
                raise ValueError(error_msg)
            else:
                log_validation_result(self.log_callback, "Preselection result validation", True,
                                    f"Selected: {preselection_result.selected_pile_type.display_name}")

            # Log preselection result details
            log_calculation_result(self.log_callback, "Selected pile type", preselection_result.selected_pile_type.display_name)
            log_calculation_result(self.log_callback, "Total possible piles", preselection_result.total_possible_piles)
            log_calculation_result(self.log_callback, "Viable grid positions", len(preselection_result.viable_grid_positions))
            log_performance_metric(self.log_callback, "Preselection viable positions", len(preselection_result.viable_grid_positions), "positions")

            # Create sub-clusters format expected by case_4_layouts
            log_algorithm_step(self.log_callback, "Optimization Input Preparation", "Creating sub-clusters format")

            # Sample load points for calculation (limit to 5 for performance)
            sample_positions = preselection_result.viable_grid_positions[:5]
            load_points = [(pos[0], pos[1], preselection_result.selected_pile_type.capacity_per_pile)
                          for pos in sample_positions]

            sub_clusters = {
                "cluster_1": {
                    "elements": group_elements,
                    "load_points": load_points
                }
            }

            log_calculation_result(self.log_callback, "Sub-clusters created", len(sub_clusters))
            log_calculation_result(self.log_callback, "Load points sampled", len(load_points))

            # Create pile distribution (simplified for single cluster)
            log_algorithm_step(self.log_callback, "Optimization Input Preparation", "Setting up pile distribution")
            pile_distribution = {"cluster_1": preselection_result.total_possible_piles}
            log_calculation_result(self.log_callback, "Pile distribution target", preselection_result.total_possible_piles)

            # Use the selected pile type's spacing
            log_algorithm_step(self.log_callback, "Optimization Input Preparation", "Determining optimal spacing")
            selected_min_spacing = max(min_spacing, preselection_result.selected_pile_type.min_spacing)

            if selected_min_spacing != min_spacing:
                enhanced_log(self.log_callback, f"Using pile type minimum spacing: {selected_min_spacing:.2f} m (was {min_spacing:.2f} m)", 'INFO')
            else:
                enhanced_log(self.log_callback, f"Using input minimum spacing: {selected_min_spacing:.2f} m", 'DEBUG')

            log_calculation_result(self.log_callback, "Selected minimum spacing", f"{selected_min_spacing:.2f} m")
            log_constraint_check(self.log_callback, "Minimum spacing constraint", selected_min_spacing, min_spacing,
                               selected_min_spacing >= min_spacing)

            # Use the pile diameter from selected type if available
            log_algorithm_step(self.log_callback, "Optimization Input Preparation", "Determining pile diameter")
            selected_pile_diameter = preselection_result.selected_pile_type.diameter or pile_diameter

            if preselection_result.selected_pile_type.diameter:
                enhanced_log(self.log_callback, f"Using pile type diameter: {selected_pile_diameter:.2f} m", 'DEBUG')
            else:
                enhanced_log(self.log_callback, f"Using fallback diameter: {selected_pile_diameter:.2f} m", 'DEBUG')

            log_calculation_result(self.log_callback, "Selected pile diameter", f"{selected_pile_diameter:.2f} m")

            # Create optimization input dictionary
            optimization_input = {
                "sub_clusters": sub_clusters,
                "pile_distribution": pile_distribution,
                "min_spacing": selected_min_spacing,
                "pile_diameter": selected_pile_diameter,
                "edge_dist": 0.3,  # Standard edge distance for optimization grid
                "initial_local_system": preselection_result.local_coordinate_system,
                "selected_pile_type": preselection_result.selected_pile_type,
                "viable_grid": preselection_result.viable_grid_positions,
                "preselection_result": preselection_result  # Include full preselection result
            }

            # Log final optimization input summary
            log_calculation_result(self.log_callback, "Edge distance", f"{optimization_input['edge_dist']:.1f} m")
            log_performance_metric(self.log_callback, "Optimization input parameters", len(optimization_input), "parameters")
            enhanced_log(self.log_callback, "✓ Optimization input prepared successfully", 'INFO')

            log_function_exit(self.log_callback, "prepare_optimization_input", f"Success - {len(optimization_input)} parameters")
            return optimization_input
    
    def run_optimization(self,
                       optimization_input: Dict[str, Any],
                       excel_inputs: Any,
                       site_boundary: Optional[Any],
                       config_overrides: Optional[Dict[str, Any]],
                       log_callback: callable) -> List[Point2D]:
        """Run layout method using the official layout engine only - ZERO FALLBACKS ALLOWED."""

        log_function_entry(log_callback, "run_optimization",
                          has_site_boundary=site_boundary is not None,
                          has_config_overrides=config_overrides is not None)

        with create_timed_logger(log_callback, "run_optimization"):
            # Import the official layout engine
            from ..layout_generation.layout_engine import generate_pile_layout

            enhanced_log(log_callback, "Using official layout engine for consistent case routing", 'INFO')
            log_algorithm_step(log_callback, "Layout Optimization", "Initializing official layout engine")

            # Extract parameters for layout engine
            sub_clusters = optimization_input["sub_clusters"]
            log_calculation_result(log_callback, "Sub-clusters available", len(sub_clusters))

            # Calculate pile distribution from preselection result
            log_algorithm_step(log_callback, "Layout Optimization", "Determining pile distribution strategy")
            preselection_result = optimization_input.get("preselection_result")

            if preselection_result and hasattr(preselection_result, 'viable_grid_positions'):
                # Use the number of viable positions as the target
                target_piles = len(preselection_result.viable_grid_positions)
                pile_distribution = {cluster_name: target_piles for cluster_name in sub_clusters.keys()}

                log_calculation_result(log_callback, "Preselection target piles", target_piles)
                enhanced_log(log_callback, f"✓ Using preselection target: {target_piles} piles", 'INFO')
                log_performance_metric(log_callback, "Target piles from preselection", target_piles, "piles")
            else:
                # Use original distribution
                pile_distribution = optimization_input["pile_distribution"]
                enhanced_log(log_callback, f"📊 Using original distribution: {pile_distribution}", 'INFO')
                log_validation_result(log_callback, "Preselection target availability", False, "Using fallback distribution")

            # Extract pile capacity from preselection result
            log_algorithm_step(log_callback, "Layout Optimization", "Determining pile capacity")
            pile_capacity = 3000.0  # Default

            if preselection_result and hasattr(preselection_result, 'selected_pile_type'):
                pile_capacity = preselection_result.selected_pile_type.capacity_per_pile
                log_calculation_result(log_callback, "Preselected pile capacity", f"{pile_capacity:.1f} kN")
                enhanced_log(log_callback, f"✓ Using preselected pile capacity: {pile_capacity:.1f} kN", 'INFO')
                log_validation_result(log_callback, "Preselected capacity availability", True, f"{pile_capacity:.1f} kN")
            else:
                log_calculation_result(log_callback, "Default pile capacity", f"{pile_capacity:.1f} kN")
                enhanced_log(log_callback, f"⚠️ Using default pile capacity: {pile_capacity:.1f} kN", 'WARNING')
                log_validation_result(log_callback, "Preselected capacity availability", False, "Using default capacity")

            # Validate optimization input parameters
            log_algorithm_step(log_callback, "Layout Optimization", "Validating optimization parameters")
            required_params = ["pile_diameter", "min_spacing", "edge_dist", "initial_local_system"]
            for param in required_params:
                if param not in optimization_input:
                    error_msg = f"Missing required optimization parameter: {param}"
                    log_validation_result(log_callback, f"Parameter {param} validation", False, error_msg)
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise ValueError(error_msg)
                else:
                    log_validation_result(log_callback, f"Parameter {param} validation", True, "Present")

            # Log optimization parameters
            log_calculation_result(log_callback, "Pile diameter", f"{optimization_input['pile_diameter']:.2f} m")
            log_calculation_result(log_callback, "Minimum spacing", f"{optimization_input['min_spacing']:.2f} m")
            log_calculation_result(log_callback, "Edge distance", f"{optimization_input['edge_dist']:.2f} m")

            # Use the layout engine with proper parameters
            log_algorithm_step(log_callback, "Layout Optimization", "Executing layout engine")
            enhanced_log(log_callback, "Running layout engine with validated parameters", 'INFO')

            pile_locations, total_piles, warnings = generate_pile_layout(
                sub_clusters=sub_clusters,
                pile_distribution=pile_distribution,
                pile_capacity=pile_capacity,
                pile_diameter=optimization_input["pile_diameter"],
                min_spacing=optimization_input["min_spacing"],
                edge_dist=optimization_input["edge_dist"],
                site_boundary=site_boundary,
                initial_local_system=optimization_input["initial_local_system"],
                excel_inputs=excel_inputs,
                config_overrides=config_overrides,
                log_callback=log_callback
            )

            # Log results from layout engine
            log_algorithm_step(log_callback, "Layout Optimization", "Processing layout engine results")
            log_calculation_result(log_callback, "Layout engine pile count", total_piles)
            log_performance_metric(log_callback, "Layout engine generated positions", len(pile_locations), "positions")

            if warnings:
                log_performance_metric(log_callback, "Layout engine warnings", len(warnings), "warnings")
                for i, warning in enumerate(warnings):
                    enhanced_log(log_callback, f"Layout Engine Warning {i+1}: {warning}", 'WARNING')
            else:
                enhanced_log(log_callback, "Layout engine completed without warnings", 'DEBUG')

            enhanced_log(log_callback, f"✓ Layout engine generated {total_piles} pile positions", 'INFO')

            # For preselection consistency, try to use preselected positions if available and reasonable
            log_algorithm_step(log_callback, "Layout Optimization", "Evaluating preselection consistency")

            if (preselection_result and
                hasattr(preselection_result, 'viable_grid_positions') and
                preselection_result.viable_grid_positions and
                len(preselection_result.viable_grid_positions) <= total_piles * 1.5):  # Within reasonable range

                preselected_count = len(preselection_result.viable_grid_positions)
                consistency_ratio = preselected_count / total_piles if total_piles > 0 else 0

                log_calculation_result(log_callback, "Preselected positions", preselected_count)
                log_calculation_result(log_callback, "Layout engine positions", total_piles)
                log_performance_metric(log_callback, "Preselection consistency ratio", consistency_ratio, "ratio")

                log_constraint_check(log_callback, "Preselection consistency", preselected_count, total_piles * 1.5,
                                   preselected_count <= total_piles * 1.5)

                enhanced_log(log_callback, f"✓ Using {preselected_count} preselected positions for consistency", 'INFO')
                log_function_exit(log_callback, "run_optimization", f"Preselected positions - {preselected_count}")
                return preselection_result.viable_grid_positions

            # Otherwise use layout engine results
            enhanced_log(log_callback, f"🏗️ Using {len(pile_locations)} positions from layout engine", 'INFO')
            log_validation_result(log_callback, "Preselection consistency check", False, "Using layout engine results")
            log_function_exit(log_callback, "run_optimization", f"Layout engine positions - {len(pile_locations)}")
            return pile_locations
    

    
    def _run_case_4_optimization(self,
                               optimization_input: Dict[str, Any],
                               excel_inputs: Any,
                               site_boundary: Optional[Any],
                               config_overrides: Optional[Dict[str, Any]],
                               log_callback: callable) -> List[Point2D]:
        """Run Case 4 NSGA-III optimization."""

        log_function_entry(log_callback, "_run_case_4_optimization",
                          has_site_boundary=site_boundary is not None,
                          has_config_overrides=config_overrides is not None)

        with create_timed_logger(log_callback, "case_4_optimization"):
            enhanced_log(log_callback, "Using Case 4 NSGA-III optimization (complex layout)", 'INFO')
            log_algorithm_step(log_callback, "Case 4 Optimization", "Initializing NSGA-III configuration")

            # Enhanced configuration for better optimization results
            enhanced_config = {
                'population_size': 50,  # Increased for better diversity
                'generations': 30,      # More generations for convergence
                'grid_expansion_factor': 1.8,  # Better grid coverage
                'grid_spacing': optimization_input["min_spacing"],
                'crossover_prob': 0.85,  # High crossover for exploration
                'mutation_prob': 0.15,   # Higher mutation for diversity
                'ref_points_p': 6,       # More reference points for NSGA-III
                'use_enhanced_operators': True,  # Enable enhanced genetic operators
                'adaptive_parameters': True,     # Adaptive parameter control
                'diversity_preservation': True  # Enhanced diversity mechanisms
            }

            # Log initial configuration
            log_calculation_result(log_callback, "Initial population size", enhanced_config['population_size'])
            log_calculation_result(log_callback, "Initial generations", enhanced_config['generations'])
            log_calculation_result(log_callback, "Grid expansion factor", enhanced_config['grid_expansion_factor'])
            log_performance_metric(log_callback, "NSGA-III reference points", enhanced_config['ref_points_p'], "points")

            # Merge with user-provided config overrides
            log_algorithm_step(log_callback, "Case 4 Optimization", "Processing configuration overrides")
            if config_overrides:
                enhanced_config.update(config_overrides)
                log_calculation_result(log_callback, "Config overrides applied", len(config_overrides))
                enhanced_log(log_callback, f"✓ Applied config overrides: {list(config_overrides.keys())}", 'INFO')
                log_performance_metric(log_callback, "Configuration parameters overridden", len(config_overrides), "parameters")
            else:
                enhanced_log(log_callback, "No configuration overrides provided", 'DEBUG')

            # Dynamic optimization based on problem size
            log_algorithm_step(log_callback, "Case 4 Optimization", "Analyzing problem size for dynamic optimization")
            num_possible_piles = len(optimization_input["viable_grid"])
            target_piles = optimization_input["pile_distribution"]["cluster_1"]

            log_calculation_result(log_callback, "Possible pile positions", num_possible_piles)
            log_calculation_result(log_callback, "Target pile count", target_piles)
            log_performance_metric(log_callback, "Problem complexity ratio", num_possible_piles / target_piles if target_piles > 0 else 0, "ratio")

            if num_possible_piles > 100 or target_piles > 20:
                # Large problem - optimize for speed while maintaining quality
                log_algorithm_step(log_callback, "Case 4 Optimization", "Applying large problem optimizations")
                original_pop = enhanced_config['population_size']
                original_gen = enhanced_config['generations']

                enhanced_config.update({
                    'population_size': min(enhanced_config['population_size'], 30),
                    'generations': min(enhanced_config['generations'], 25),
                    'grid_expansion_factor': min(enhanced_config['grid_expansion_factor'], 1.5)
                })

                enhanced_log(log_callback, f"🔧 Large problem detected: {num_possible_piles} positions, {target_piles} target piles", 'INFO')
                enhanced_log(log_callback, "Applied speed optimizations while maintaining quality", 'INFO')
                log_calculation_result(log_callback, "Population size adjusted", f"{original_pop} → {enhanced_config['population_size']}")
                log_calculation_result(log_callback, "Generations adjusted", f"{original_gen} → {enhanced_config['generations']}")

            elif num_possible_piles < 30 or target_piles < 5:
                # Small problem - optimize for precision
                log_algorithm_step(log_callback, "Case 4 Optimization", "Applying small problem optimizations")
                original_pop = enhanced_config['population_size']
                original_gen = enhanced_config['generations']

                enhanced_config.update({
                    'population_size': max(enhanced_config['population_size'], 40),
                    'generations': max(enhanced_config['generations'], 35),
                    'grid_expansion_factor': max(enhanced_config['grid_expansion_factor'], 2.0)
                })

                enhanced_log(log_callback, f"🎯 Small problem detected: {num_possible_piles} positions, {target_piles} target piles", 'INFO')
                enhanced_log(log_callback, "Applied precision optimizations for better accuracy", 'INFO')
                log_calculation_result(log_callback, "Population size adjusted", f"{original_pop} → {enhanced_config['population_size']}")
                log_calculation_result(log_callback, "Generations adjusted", f"{original_gen} → {enhanced_config['generations']}")
            else:
                enhanced_log(log_callback, f"📊 Medium problem size: {num_possible_piles} positions, {target_piles} target piles", 'INFO')
                enhanced_log(log_callback, "Using standard optimization configuration", 'DEBUG')

            # Log final configuration
            log_calculation_result(log_callback, "Final population size", enhanced_config['population_size'])
            log_calculation_result(log_callback, "Final generations", enhanced_config['generations'])
            enhanced_log(log_callback, f"✓ Final config: pop={enhanced_config['population_size']}, gen={enhanced_config['generations']}", 'INFO')
            enhanced_log(log_callback, f"🎯 Target: {target_piles} piles from {num_possible_piles} possible positions", 'INFO')

            # Run Case 4 optimization with the enhanced configuration
            log_algorithm_step(log_callback, "Case 4 Optimization", "Executing NSGA-III optimization")
            enhanced_log(log_callback, "Running Case 4 optimization with enhanced configuration", 'INFO')

            optimized_positions = handle_case_4(
                sub_clusters=optimization_input["sub_clusters"],
                pile_distribution=optimization_input["pile_distribution"],
                min_spacing=optimization_input["min_spacing"],
                edge_dist=optimization_input["edge_dist"],
                initial_local_system=optimization_input["initial_local_system"],
                excel_inputs=excel_inputs,
                pile_diameter=optimization_input["pile_diameter"],
                site_boundary=site_boundary,
                config_overrides=enhanced_config,  # Pass enhanced config
                log_callback=log_callback
            )

            # Log optimization results
            log_algorithm_step(log_callback, "Case 4 Optimization", "Processing optimization results")
            log_calculation_result(log_callback, "Optimized positions generated", len(optimized_positions))

            if target_piles > 0:
                achievement_rate = (len(optimized_positions) / target_piles) * 100
                log_performance_metric(log_callback, "Target achievement rate", achievement_rate, "%")
                enhanced_log(log_callback, f"✓ Case 4 NSGA-III optimization completed: {len(optimized_positions)} positions", 'INFO')
                enhanced_log(log_callback, f"🎯 Achieved {len(optimized_positions)}/{target_piles} target piles ({achievement_rate:.1f}%)", 'INFO')

                # Validate achievement rate
                if achievement_rate >= 80:
                    log_validation_result(log_callback, "Target achievement", True, f"{achievement_rate:.1f}% achieved")
                else:
                    log_validation_result(log_callback, "Target achievement", False, f"Only {achievement_rate:.1f}% achieved")
            else:
                enhanced_log(log_callback, f"✓ Case 4 NSGA-III optimization completed: {len(optimized_positions)} positions", 'INFO')
                log_validation_result(log_callback, "Optimization completion", True, "Completed successfully")

            log_function_exit(log_callback, "_run_case_4_optimization", f"{len(optimized_positions)} positions")
            return optimized_positions
    
    def _filter_by_site_boundary(self,
                                positions: List[Point2D],
                                site_boundary: Any,
                                log_callback: callable) -> List[Point2D]:
        """Filter positions by site boundary with comprehensive adjustment logic."""

        log_function_entry(log_callback, "_filter_by_site_boundary",
                          input_positions=len(positions),
                          has_site_boundary=site_boundary is not None)

        with create_timed_logger(log_callback, "filter_by_site_boundary"):
            from shapely.geometry import Point

            enhanced_log(log_callback, f"Filtering {len(positions)} positions by site boundary", 'INFO')
            log_algorithm_step(log_callback, "Site Boundary Filtering", "Initializing boundary filtering")

            filtered_positions = []
            removed_count = 0
            adjusted_count = 0

            # Add a small tolerance for boundary checking
            tolerance = 0.1  # 10cm tolerance
            log_calculation_result(log_callback, "Boundary tolerance", f"{tolerance:.2f} m")

            # Validate site boundary
            if site_boundary is None:
                enhanced_log(log_callback, "No site boundary provided - returning all positions", 'WARNING')
                log_validation_result(log_callback, "Site boundary validation", False, "No boundary provided")
                log_function_exit(log_callback, "_filter_by_site_boundary", f"No filtering - {len(positions)} positions")
                return positions
            else:
                log_validation_result(log_callback, "Site boundary validation", True, "Boundary provided")

            log_algorithm_step(log_callback, "Site Boundary Filtering", "Processing individual positions")

            for i, pos in enumerate(positions):
                point = Point(pos[0], pos[1])

                # Check if point is within boundary with tolerance
                if site_boundary.contains(point) or site_boundary.distance(point) <= tolerance:
                    filtered_positions.append(pos)
                else:
                    # Try to move the point to the closest point on the boundary
                    log_algorithm_step(log_callback, "Position Adjustment", f"Adjusting position {i+1}")

                    # Find closest point on site boundary
                    boundary_coords = list(site_boundary.exterior.coords)
                    min_dist = float('inf')
                    closest_point = pos

                    for coord in boundary_coords:
                        dist = ((coord[0] - pos[0])**2 + (coord[1] - pos[1])**2)**0.5
                        if dist < min_dist:
                            min_dist = dist
                            closest_point = coord

                    log_calculation_result(log_callback, f"Position {i+1} distance to boundary", f"{min_dist:.2f} m")

                    # Use closest boundary point if it's reasonable (within 2m)
                    if min_dist <= 2.0:
                        adjusted_point = Point(closest_point[0], closest_point[1])
                        if site_boundary.contains(adjusted_point) or site_boundary.distance(adjusted_point) <= tolerance:
                            filtered_positions.append(closest_point)
                            adjusted_count += 1
                            enhanced_log(log_callback, f"✓ Adjusted position {i+1} to boundary", 'DEBUG')
                            continue

                    # If adjustment failed, remove the point
                    removed_count += 1
                    enhanced_log(log_callback, f"❌ Removed pile at ({pos[0]:.2f}, {pos[1]:.2f}) - outside site boundary", 'DEBUG')

            # Log filtering results
            log_calculation_result(log_callback, "Input positions", len(positions))
            log_calculation_result(log_callback, "Filtered positions", len(filtered_positions))
            log_calculation_result(log_callback, "Removed positions", removed_count)
            log_calculation_result(log_callback, "Adjusted positions", adjusted_count)

            # Calculate filtering metrics
            retention_rate = (len(filtered_positions) / len(positions)) * 100 if positions else 0
            adjustment_rate = (adjusted_count / len(positions)) * 100 if positions else 0

            log_performance_metric(log_callback, "Position retention rate", retention_rate, "%")
            log_performance_metric(log_callback, "Position adjustment rate", adjustment_rate, "%")

            if removed_count > 0:
                enhanced_log(log_callback, f"🔧 Final boundary filter: removed {removed_count} piles, adjusted {adjusted_count} piles to boundary", 'INFO')
                log_validation_result(log_callback, "Boundary filtering", True, f"Removed {removed_count}, adjusted {adjusted_count}")
            elif adjusted_count > 0:
                enhanced_log(log_callback, f"🔧 Final boundary filter: adjusted {adjusted_count} piles to site boundary", 'INFO')
                log_validation_result(log_callback, "Boundary filtering", True, f"Adjusted {adjusted_count} positions")
            else:
                enhanced_log(log_callback, "✓ All optimized piles within site boundary", 'INFO')
                log_validation_result(log_callback, "Boundary filtering", True, "All positions within boundary")

            log_function_exit(log_callback, "_filter_by_site_boundary", f"{len(filtered_positions)} positions")
            return filtered_positions
    
    def combine_results(self, preselection_result: Any, optimized_positions: List[Point2D]) -> Dict[str, Any]:
        """Combine pre-selection and optimization results."""
        
        final_pile_count = len(optimized_positions)
        final_capacity = final_pile_count * preselection_result.selected_pile_type.capacity_per_pile
        
        return {
            # Pre-selection results
            "selected_pile_type": {
                "type": preselection_result.selected_pile_type.pile_type.name,
                "display_name": preselection_result.selected_pile_type.display_name,
                "capacity_per_pile": preselection_result.selected_pile_type.capacity_per_pile,
                "min_spacing": preselection_result.selected_pile_type.min_spacing,
                "section": preselection_result.selected_pile_type.section,
                "diameter": preselection_result.selected_pile_type.diameter
            },
            
            # Geometry results
            "pile_cap_geometry": {
                "initial_pile_cap": preselection_result.initial_pile_cap,
                "enlarged_pile_cap": preselection_result.enlarged_pile_cap,
                "local_coordinate_system": preselection_result.local_coordinate_system
            },
            
            # Pre-selection analysis - including viable grid positions and CRITICAL edge distance
            "preselection_analysis": {
                "edge_distance": getattr(preselection_result, 'user_edge_distance', 0.4),  # CRITICAL FIX for DXF
                "total_possible_piles": preselection_result.total_possible_piles,
                "total_possible_capacity": preselection_result.total_capacity,
                "preselection_utilization": preselection_result.utilization_ratio,
                "all_pile_type_positions": preselection_result.all_pile_type_positions,
                "all_possible_pile_boundaries": preselection_result.all_possible_pile_boundaries,  # CRITICAL FIX: Add missing boundaries
                "preselection_dxf_path": preselection_result.preselection_dxf_path,
                "initial_pile_cap": preselection_result.initial_pile_cap,
                "enlarged_pile_cap": preselection_result.enlarged_pile_cap,
                "local_coordinate_system": preselection_result.local_coordinate_system,
                "selected_pile_type": preselection_result.selected_pile_type,
                "evaluation_summary": {
                    preselection_result.selected_pile_type.pile_type.name: {
                        "grid_positions": preselection_result.viable_grid_positions,
                        "possible_pile_count": preselection_result.total_possible_piles,
                        "total_capacity": preselection_result.total_capacity,
                        "meets_capacity_requirement": True,
                        "utilization_ratio": preselection_result.utilization_ratio
                    }
                }
            },
            
            # Final optimized results
            "optimized_layout": {
                "pile_positions": optimized_positions,
                "final_pile_count": final_pile_count,
                "final_total_capacity": final_capacity,
                "final_utilization": preselection_result.selected_pile_type.capacity_per_pile * final_pile_count / final_capacity if final_capacity > 0 else 0
            },
            
            # Process metadata
            "process_metadata": {
                "preselection_method": "geometric_capacity_check",
                "optimization_method": self.get_optimization_method_used(optimized_positions),
                "pile_type_order_evaluated": ["DHP", "SHP", "BP"],
                "success": True
            }
        }
    
    def get_optimization_method_used(self, optimized_positions: List[Point2D]) -> str:
        """Determine which optimization method was used based on the last log messages."""
        log_function_entry(self.log_callback, "get_optimization_method_used",
                          num_positions=len(optimized_positions))

        enhanced_log(self.log_callback, "Determining optimization method used", 'DEBUG')

        # This is a simple heuristic - in a real implementation, you'd track this more explicitly
        # For now, we'll return a default that indicates the method was determined automatically
        method = "auto_case_determination"

        log_calculation_result(self.log_callback, "Optimization method determined", method)
        enhanced_log(self.log_callback, f"Optimization method: {method}", 'INFO')
        log_performance_metric(self.log_callback, "Positions analyzed for method determination", len(optimized_positions), "positions")

        log_function_exit(self.log_callback, "get_optimization_method_used", method)
        return method

