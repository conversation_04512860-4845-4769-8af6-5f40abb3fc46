"""
Foundation-Automation SAFE Geometry Writer - Structural Element Geometry Generation

This module provides the main geometry writing functions for converting structural
element data into SAFE 16 and SAFE 22 format specifications. It orchestrates the
complete geometric model definition including points, beams, columns, walls, slabs,
and their associated properties and assignments.

Key Features:
- Dual-format export supporting both SAFE 16 (legacy) and SAFE 22 (current) formats
- Comprehensive structural element support (points, beams, columns, walls, slabs)
- Property assignment coordination for materials and sections
- Group definition and assignment management
- Opening and void region handling
- Line load assignment and distribution
- Geometric validation and error handling

Structural Elements Supported:
- Point coordinates and connectivity definitions
- Beam geometry with property assignments and insertion points
- Column definitions with section properties
- Wall geometry and property assignments
- Slab areas with mesh control and property assignments
- Opening definitions within slab areas
- Line load paths and load application points
- Lookup (LKP) elements for special modeling requirements

Format Compatibility:
- SAFE 16: Legacy format support with traditional data structures
- SAFE 22: Enhanced format with improved connectivity and analysis features
- Automatic format conversion ensuring consistency across versions
- Validation of geometric constraints and modeling requirements

Workflow Integration:
- Processes Excel input data into SAFE-compatible geometric definitions
- Coordinates with property assignment modules for complete model definition
- Integrates with load assignment systems for comprehensive analysis models
- Supports design strip generation and analysis optimization features

Dependencies:
- build_fem.write_safe.safe16: SAFE 16 format-specific geometry writers
- build_fem.write_safe.safe22: SAFE 22 format-specific geometry writers
- Excel input data structures with validated geometric parameters
- Property definition modules for material and section assignments

Technical Notes:
- All geometric coordinates are validated for consistency and accuracy
- Property assignments are cross-referenced with available material libraries
- Group definitions support design optimization and result processing
- Opening definitions are properly integrated with parent slab elements
- Line loads are distributed according to structural engineering principles

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 structural analysis software
"""

from build_fem.write_safe.safe16 import (
    _write_point_coordinates_safe16, _write_beam_geometry_lines_safe16,
    _write_beam_property_assignments_safe16, _write_beam_insertion_points_safe16,
    _write_wall_geometry_lines_safe16, _write_column_assign_safe16,
    _write_slab_assign_ObjGeomAreas01General_safe16, _write_lkp_ObjGeomAreas01General_safe16,
    _write_slab_assign_SoilPropertyAssignments_safe16, _write_slab_assign_SlabPropertyAssignments_safe16,
    _write_slab_group_definitions_safe16, _write_slab_group_assign_safe16, _write_line_load_assign_safe16,
    _write_opening_assign_SlabPropertyAssignments_safe16, _write_opening_ObjGeomAreas01General_safe16
)
from build_fem.write_safe.safe22 import (
    point_PointObjectConnectivity_safe22, _write_beam_geometry_lines_safe22,
    beam_FrameAssignsSectProp_safe22, beam_FrameAssignsInsertionPoint_safe22,
    _write_wall_geometry_lines_safe22, _write_column_assign_safe22, slab_FloorObjectConnectivity_safe22,
    lkp_NullAreaObjectConnectivity_safe22, slab_AreaAssignsSectProp_safe22, lkp_AreaAssignsSectProp_safe22,
    slab_GroupDefinitions_safe22, slab_GroupAssignments_safe22, line_load_NullLineObjectConnectivity_safe22,
    slab_AreaAssignsInsertionPoint_safe22, slab_AreaAssignsEdgeConstraints_safe22,
    slab_AreaAssignsFloorAutoMesh_safe22, slab_ConcSlbOverFEBased_safe22,
    line_load_FrameAssignsEndLenOffsets_safe22, line_load_FrameAssignsFrameAutoMesh_safe22,
    line_load_FrameAssignsInsertionPoint_safe22, line_load_FrameAssignsOutputStations_safe22,
    line_load_FrameAssignsSectProp_safe22
)


def write_point_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write point coordinate definitions and connectivity for structural nodes.
    
    Processes point coordinate data from Excel inputs and generates corresponding
    SAFE 16 and SAFE 22 format point definitions. Points serve as fundamental
    building blocks for all structural elements including beams, columns, and
    area elements.

    The function handles:
    - Point coordinate extraction and validation from Excel geometry data
    - SAFE 16 point coordinate table generation with proper formatting
    - SAFE 22 point object connectivity definitions with enhanced metadata
    - Coordinate system consistency checks and validation
    - Duplicate point detection and consolidation
    """
    safe16_dfs = _write_point_coordinates_safe16(excel_inputs, safe16_dfs)

    safe22_dfs, df_append = point_PointObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs


def write_beam_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write beam geometry definitions, property assignments, and insertion points.
    
    Processes beam element data to generate comprehensive beam definitions including
    geometric lines, section property assignments, and insertion point specifications.
    Beams are linear structural elements that resist axial, shear, and moment forces.

    The function orchestrates three main beam definition phases:
    1. Geometric line definitions connecting structural points
    2. Section property assignments linking beams to material libraries
    3. Insertion point specifications for accurate stress calculations

    Processing includes:
    - Beam geometry line creation with start/end point connectivity
    - Section property validation and assignment from material libraries
    - Insertion point calculation for accurate stress and deflection analysis
    - Cross-referencing with structural point definitions for connectivity
    - Validation of beam orientation and local axis definitions
    """
    # Early return if no beam data is available to process
    if excel_inputs.Beam.empty:
        return safe16_dfs, safe22_dfs
    
    # Part 1: Generate geometric line definitions connecting structural points
    # This establishes the basic connectivity and spatial relationships for beam elements
    safe16_dfs = _write_beam_geometry_lines_safe16(
        excel_inputs, safe16_dfs)
    safe22_dfs = _write_beam_geometry_lines_safe22(
        excel_inputs, safe22_dfs)

    # Part 2: Assign section properties to beam elements from material libraries
    # This links geometric definitions to structural behavior through material properties
    safe16_dfs = _write_beam_property_assignments_safe16(
        excel_inputs, safe16_dfs)
    safe22_dfs, df_append = beam_FrameAssignsSectProp_safe22(
        excel_inputs, safe22_dfs)

    # Part 3: Define insertion points for accurate stress and deflection calculations
    # Insertion points determine where stress calculations are performed within beam cross-sections
    safe16_dfs = _write_beam_insertion_points_safe16(
        excel_inputs, safe16_dfs)
    safe22_dfs, df_append = beam_FrameAssignsInsertionPoint_safe22(
        excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs


def write_column_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write column element definitions and property assignments for vertical load-bearing members.
    
    Processes column data to generate vertical structural element definitions including
    geometric properties, section assignments, and material specifications. Columns
    are primary vertical load-bearing elements that transfer loads from superstructure
    to foundation systems.

    The function handles:
    - Column geometric definition with height and positioning
    - Section property validation and assignment from material libraries
    - Material property integration for concrete, steel, or composite columns
    - Coordinate system alignment for proper load transfer
    - Cross-referencing with foundation and superstructure connections
    """
    # Process column definitions for both SAFE 16 and SAFE 22 formats
    # Column assignments include geometry, properties, and material specifications
    safe16_dfs = _write_column_assign_safe16(excel_inputs, safe16_dfs)
    safe22_dfs = _write_column_assign_safe22(excel_inputs, safe22_dfs)
    return safe16_dfs, safe22_dfs


def write_wall_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write wall element definitions and geometric line assignments for lateral load resistance.
    
    Processes wall element data to generate definitions for vertical planar elements
    that provide lateral load resistance and structural stability. Walls are critical
    for seismic and wind load resistance in building structures.

    The function handles:
    - Wall geometric line definition with proper orientation and connectivity
    - Thickness and material property validation from structural specifications
    - Integration with adjacent structural elements for load path continuity
    - Boundary condition establishment for proper load transfer
    - Coordinate validation for accurate geometric representation
    """
    # Process wall geometric line definitions for both SAFE formats
    # This includes centerline definition and property assignments for lateral load resistance
    safe16_dfs = _write_wall_geometry_lines_safe16(excel_inputs, safe16_dfs)
    safe22_dfs = _write_wall_geometry_lines_safe22(excel_inputs, safe22_dfs)
    return safe16_dfs, safe22_dfs


def write_slab_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write comprehensive slab element definitions including area geometry, properties, and analysis settings.
    
    Processes slab element data to generate complete area element definitions for
    floor systems, foundation slabs, and other planar structural elements. Slabs
    are critical two-dimensional elements that distribute loads and provide
    structural diaphragm action.

    The function orchestrates multiple slab definition components:
    - Area geometry definitions with boundary coordinates
    - Soil property assignments for foundation interaction modeling
    - Slab structural property assignments from material libraries
    - Mesh control and analysis optimization settings
    - Edge constraints and boundary condition specifications
    - Floor connectivity and load transfer characteristics

    Processing includes:
    - General area geometry definition with proper coordinate validation
    - Soil spring assignment for foundation-soil interaction modeling
    - Structural property validation and assignment from design libraries
    - Insertion point calculation for accurate stress analysis locations
    - Edge constraint definition for proper boundary behavior
    - Auto-mesh settings for finite element discretization optimization
    - Concrete slab design parameters for reinforcement calculations
    """
    # SAFE 16 format slab definitions with legacy compatibility
    # Process general area geometry definitions for structural analysis
    safe16_dfs = _write_slab_assign_ObjGeomAreas01General_safe16(
        excel_inputs, safe16_dfs)
    # Assign soil properties for foundation-soil interaction modeling
    safe16_dfs = _write_slab_assign_SoilPropertyAssignments_safe16(
        excel_inputs, safe16_dfs)
    # Assign structural slab properties from material and design libraries
    safe16_dfs = _write_slab_assign_SlabPropertyAssignments_safe16(
        excel_inputs, safe16_dfs)

    # SAFE 22 format slab definitions with enhanced capabilities
    # Define insertion points for accurate stress calculation locations
    safe22_dfs, df_append = slab_AreaAssignsInsertionPoint_safe22(
        excel_inputs, safe22_dfs)
    # Assign edge constraints for proper boundary condition implementation
    safe22_dfs, df_append = slab_AreaAssignsEdgeConstraints_safe22(
        excel_inputs, safe22_dfs)
    # Configure automatic mesh generation for finite element analysis
    safe22_dfs, df_append = slab_AreaAssignsFloorAutoMesh_safe22(
        excel_inputs, safe22_dfs)
    # Assign concrete slab design parameters for reinforcement calculations
    safe22_dfs, df_append = slab_ConcSlbOverFEBased_safe22(
        excel_inputs, safe22_dfs)
    # Establish floor object connectivity for diaphragm and load transfer behavior
    safe22_dfs, df_append = slab_FloorObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)
    # Assign section properties for structural behavior and analysis
    safe22_dfs, df_append = slab_AreaAssignsSectProp_safe22(
        excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs

def write_opening_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write opening element definitions for voids and penetrations within slab areas.
    
    Processes opening data to generate void regions within slab elements for
    architectural and MEP requirements. Openings are negative area elements
    that remove material from parent slab elements while maintaining proper
    load transfer characteristics around the opening perimeter.

    The function handles:
    - Opening geometry definition within parent slab boundaries
    - Property assignment for opening edge reinforcement and detailing
    - Integration with parent slab elements for proper load redistribution
    - Validation of opening size and position within structural limits
    """
    # Process opening area geometry definitions for void regions within slabs
    safe16_dfs = _write_opening_ObjGeomAreas01General_safe16(excel_inputs, safe16_dfs)
    # Assign slab property specifications for opening edge reinforcement and detailing
    safe16_dfs = _write_opening_assign_SlabPropertyAssignments_safe16(excel_inputs, safe16_dfs)
    return safe16_dfs, safe22_dfs

def write_lkp_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write lookup (LKP) element definitions for special modeling requirements and reference elements.
    
    Processes lookup element data to generate specialized area elements used for
    reference purposes, boundary conditions, or special modeling requirements.
    LKP elements provide flexibility for custom modeling situations that don't
    fit standard structural element categories.

    The function handles:
    - LKP area geometry definition for special modeling requirements
    - Null area object connectivity for reference and boundary applications
    - Section property assignments for specialized analysis needs
    - Integration with standard structural elements for comprehensive modeling
    """
    # Process LKP area geometry definitions for specialized modeling requirements
    safe16_dfs = _write_lkp_ObjGeomAreas01General_safe16(excel_inputs, safe16_dfs)

    # SAFE 22 format LKP definitions with enhanced connectivity options
    # Establish null area object connectivity for reference and boundary applications
    safe22_dfs, df_append = lkp_NullAreaObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)
    # Assign section properties for specialized analysis and modeling requirements
    safe22_dfs, df_append = lkp_AreaAssignsSectProp_safe22(
        excel_inputs, safe22_dfs)
    return safe16_dfs, safe22_dfs


def write_slab_group(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write slab group definitions and assignments for design optimization and result processing.
    
    Processes slab grouping data to organize slab elements into logical groups for
    design optimization, result processing, and construction sequencing. Groups
    enable efficient analysis and design of similar slab elements with common
    properties and loading conditions.

    The function handles:
    - Group definition creation with descriptive names and properties
    - Slab element assignment to appropriate design groups
    - Cross-referencing between group definitions and member assignments
    - Validation of group consistency and completeness
    """
    # Early return if no slab data is available for group processing
    if excel_inputs.Slab.empty:
        return safe16_dfs, safe22_dfs

    # SAFE 16 format group processing with legacy compatibility
    # Create group definitions with names, properties, and design criteria
    safe16_dfs = _write_slab_group_definitions_safe16(excel_inputs, safe16_dfs)
    # Assign slab elements to appropriate groups based on design criteria
    safe16_dfs = _write_slab_group_assign_safe16(excel_inputs, safe16_dfs)

    # SAFE 22 format group processing with enhanced capabilities
    # Process group definitions with enhanced metadata and properties
    safe22_dfs, df_append = slab_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    # Establish group assignments with improved tracking and validation
    safe22_dfs, df_append = slab_GroupAssignments_safe22(excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs


def write_line_load_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Write line load element definitions and property assignments for distributed load modeling.
    
    Processes line load data to generate linear elements for modeling distributed
    loads such as wall loads, equipment loads, or other line-distributed forces.
    Line loads provide an efficient method for applying distributed forces to
    structural elements without requiring detailed geometric modeling.

    The function handles comprehensive line load definition including:
    - Line load geometric connectivity with null line object definitions
    - Frame property assignments including end length offsets and insertion points
    - Automatic mesh control for optimal analysis accuracy
    - Output station definitions for detailed result extraction
    - Section property assignments for load distribution characteristics

    Processing includes:
    - Validation of line load data availability in Excel inputs
    - Null line object connectivity for load path definition
    - Frame assignment processing for structural integration
    - Mesh control optimization for analysis accuracy
    - Result extraction station placement for design verification
    """
    # Process line load definitions only if data is available in Excel inputs
    if not excel_inputs.LineLoad.empty:
        # SAFE 16 format line load assignment with legacy compatibility
        safe16_dfs = _write_line_load_assign_safe16(excel_inputs, safe16_dfs)

        # SAFE 22 format line load processing with enhanced capabilities
        # Establish null line object connectivity for load path definition without structural behavior
        safe22_dfs, df_append = line_load_NullLineObjectConnectivity_safe22(
            excel_inputs, safe22_dfs)
        # Assign frame end length offsets for accurate load application points
        safe22_dfs, df_append = line_load_FrameAssignsEndLenOffsets_safe22(
            excel_inputs, safe22_dfs)
        # Configure automatic mesh generation for optimal analysis of distributed loads
        safe22_dfs, df_append = line_load_FrameAssignsFrameAutoMesh_safe22(
            excel_inputs, safe22_dfs)
        # Define insertion points for accurate load application and stress calculations
        safe22_dfs, df_append = line_load_FrameAssignsInsertionPoint_safe22(
            excel_inputs, safe22_dfs)
        # Establish output stations for detailed result extraction and verification
        safe22_dfs, df_append = line_load_FrameAssignsOutputStations_safe22(
            excel_inputs, safe22_dfs)
        # Assign section properties for load distribution and structural integration
        safe22_dfs, df_append = line_load_FrameAssignsSectProp_safe22(
            excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs
