﻿"""
DXF Setup Functions

Functions for setting up DXF documents including layers, units, and other settings.
"""

from typing import Optional, Callable
import ezdxf
from ezdxf import colors
from ezdxf.document import Drawing

from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)


def setup_layers(doc: Drawing, units: str, text_height: float, log_callback: Optional[Callable] = None) -> None:
    """
    Setup DXF layers with appropriate colors and linetypes.
    
    Args:
        doc: DXF document
        units: Drawing units
        text_height: Text height for the drawing
        log_callback: Optional callback function for logging
    """
    log_function_entry(log_callback, "setup_layers", 
                      units=units, text_height=text_height)
    
    try:
        enhanced_log(log_callback, "=== DXF LAYER SETUP ===", 'INFO')
        
        with create_timed_logger(log_callback, "DXF layer creation") as layer_timer:
            # Validate input parameters
            if not isinstance(doc, Drawing):
                enhanced_log(log_callback, "Invalid DXF document provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", False)
                raise ValueError("Invalid DXF document provided")
            
            log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", True)
            enhanced_log(log_callback, f"Setting up DXF layers for units: {units}", 'INFO')
            
            # Define layer configuration
            enhanced_log(log_callback, "Defining layer configuration", 'DEBUG')
            layers = {
                # Site and structure layers
                'SITE_BOUNDARY': {'color': colors.BLACK, 'linetype': 'CONTINUOUS'},
                'COLUMNS': {'color': colors.CYAN, 'linetype': 'CONTINUOUS'},
                'WALLS': {'color': colors.YELLOW, 'linetype': 'CONTINUOUS'},

                # Final optimized pile elements
                'PILE_CAPS': {'color': colors.BLUE, 'linetype': 'CONTINUOUS'},
                'PILES_DHP': {'color': colors.RED, 'linetype': 'CONTINUOUS'},        # Driven H-Pile
                'PILES_SHP': {'color': colors.GREEN, 'linetype': 'CONTINUOUS'},      # Socket H-Pile (consolidated layer)
                'PILES_BP': {'color': colors.MAGENTA, 'linetype': 'CONTINUOUS'},     # Bored Pile
                
                # Preselection analysis layers (lighter colors)
                'INITIAL_PILE_CAPS': {'color': colors.CYAN, 'linetype': 'CONTINUOUS'},     # Original pile cap
                'OPTIMAL_RECTANGLE': {'color': colors.RED, 'linetype': 'DASHDOT'},         # Minimum area rectangle
                'PRESELECTION_DHP': {'color': 1, 'linetype': 'DOTTED'},                    # DHP possible positions
                'PRESELECTION_SHP': {'color': 3, 'linetype': 'DOTTED'},                    # SHP possible positions
                'PRESELECTION_DHP_CAP': {'color': 1, 'linetype': 'DASHED'},                # DHP maximum pile cap
                'PRESELECTION_SHP_CAP': {'color': 3, 'linetype': 'DASHED'},                # SHP maximum pile cap
                
                # Dynamic BP layers are created in create_bp_layers_if_needed()
                
                # Other elements
                'POSSIBLE_POSITIONS': {'color': colors.GRAY, 'linetype': 'DASHED'},
                'LOAD_CENTROIDS': {'color': colors.MAGENTA, 'linetype': 'CONTINUOUS'},
                'SUB_CLUSTER_CENTROIDS': {'color': colors.BLUE, 'linetype': 'CONTINUOUS'},
                'PILE_CENTROIDS': {'color': colors.GREEN, 'linetype': 'CONTINUOUS'},
                'TEXT': {'color': colors.BLACK, 'linetype': 'CONTINUOUS'},
                'DIMENSIONS': {'color': colors.WHITE, 'linetype': 'CONTINUOUS'}
            }
            
            log_calculation_result(log_callback, "Total layers to create", len(layers))
            enhanced_log(log_callback, f"Creating {len(layers)} DXF layers", 'INFO')
            
            # Create layers
            layers_created = 0
            layers_skipped = 0
            
            for layer_name, properties in layers.items():
                try:
                    # Check if layer already exists
                    if layer_name in doc.layers:
                        enhanced_log(log_callback, f"Layer '{layer_name}' already exists, skipping", 'DEBUG')
                        layers_skipped += 1
                        continue
                    
                    # Create new layer
                    layer = doc.layers.new(layer_name)
                    layer.color = properties['color']
                    layer.linetype = properties['linetype']
                    
                    enhanced_log(log_callback, f"Created layer '{layer_name}' with color {properties['color']} and linetype {properties['linetype']}", 'DEBUG')
                    layers_created += 1
                    
                except Exception as layer_error:
                    enhanced_log(log_callback, f"Failed to create layer '{layer_name}': {layer_error}", 'WARNING')
                    log_error_with_context(log_callback, "setup_layers", layer_error,
                                         layer_name=layer_name,
                                         color=properties['color'],
                                         linetype=properties['linetype'])
            
            # Log layer creation results
            log_calculation_result(log_callback, "Layers created", layers_created)
            log_calculation_result(log_callback, "Layers skipped", layers_skipped)
            log_performance_metric(log_callback, "Layer creation success rate", 
                                 (layers_created / len(layers)) * 100, "%")
            
            enhanced_log(log_callback, f"Layer setup completed: {layers_created} created, {layers_skipped} skipped", 'INFO')
            
            # Validate layer setup
            total_layers_after = len(doc.layers)
            log_validation_result(log_callback, "DXF layer setup", True, 
                                f"Successfully setup {layers_created} layers, total layers: {total_layers_after}")
        
        log_function_exit(log_callback, "setup_layers", 
                         layers_created=layers_created,
                         layers_skipped=layers_skipped,
                         total_layers=len(doc.layers))
        
    except Exception as e:
        log_error_with_context(log_callback, "setup_layers", e,
                              units=units, text_height=text_height)
        enhanced_log(log_callback, f"DXF layer setup failed: {e}", 'ERROR')
        raise


def create_bp_layers_if_needed(doc: Drawing, capacity: float, diameter: float, 
                             log_callback: Optional[Callable] = None) -> str:
    """
    Create BP-specific layers based on capacity and diameter if they don't exist.
    
    Args:
        doc: DXF document
        capacity: BP capacity in kN
        diameter: BP diameter in meters
        log_callback: Optional callback function for logging
        
    Returns:
        str: Layer name for BP preselection positions
    """
    log_function_entry(log_callback, "create_bp_layers_if_needed",
                      capacity=capacity, diameter=diameter)
    
    try:
        enhanced_log(log_callback, "=== BP LAYER CREATION ===", 'INFO')
        
        with create_timed_logger(log_callback, "BP layer creation") as bp_timer:
            # Validate input parameters
            if not isinstance(doc, Drawing):
                enhanced_log(log_callback, "Invalid DXF document provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", False)
                raise ValueError("Invalid DXF document provided")
            
            log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", True)
            
            # Validate and sanitize parameters
            if capacity is None or capacity <= 0:
                enhanced_log(log_callback, f"Invalid capacity value: {capacity}, using default 500 kN", 'WARNING')
                capacity = 500.0
                log_constraint_check(log_callback, "Valid capacity", capacity, "> 0", False)
            else:
                log_constraint_check(log_callback, "Valid capacity", capacity, "> 0", True)
            
            # Handle None diameter
            original_diameter = diameter
            if diameter is None:
                diameter = 1.0  # Default diameter
                enhanced_log(log_callback, f"Diameter was None, using default: {diameter} m", 'DEBUG')
                log_constraint_check(log_callback, "Valid diameter", original_diameter, "not None", False)
            else:
                log_constraint_check(log_callback, "Valid diameter", diameter, "> 0", diameter > 0)
            
            enhanced_log(log_callback, f"Creating BP layers for capacity: {capacity} kN, diameter: {diameter} m", 'INFO')
            
            # Create layer names according to PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m format
            log_algorithm_step(log_callback, "BP layer naming", "Generating layer names from parameters")
            capacity_int = int(capacity)
            diameter_str = f"{diameter:.0f}" if diameter == int(diameter) else f"{diameter:.1f}"
            
            position_layer_name = f'PRESELECTION_BP_{capacity_int}kN_{diameter_str}m'
            cap_layer_name = f'PRESELECTION_BP_{capacity_int}kN_{diameter_str}m_CAP'
            
            enhanced_log(log_callback, f"Generated layer names: position='{position_layer_name}', cap='{cap_layer_name}'", 'DEBUG')
            log_calculation_result(log_callback, "Position layer name", position_layer_name)
            log_calculation_result(log_callback, "Cap layer name", cap_layer_name)
            
            layers_created = 0
            layers_existing = 0
            
            # Create position layer if it doesn't exist
            log_algorithm_step(log_callback, "BP layer creation", "Creating position layer")
            if position_layer_name not in doc.layers:
                try:
                    layer = doc.layers.new(position_layer_name)
                    layer.color = 6  # Magenta
                    layer.linetype = 'DOTTED'
                    enhanced_log(log_callback, f"Created position layer '{position_layer_name}' with color 6 (Magenta), linetype DOTTED", 'DEBUG')
                    layers_created += 1
                except Exception as layer_error:
                    enhanced_log(log_callback, f"Failed to create position layer '{position_layer_name}': {layer_error}", 'ERROR')
                    log_error_with_context(log_callback, "create_bp_layers_if_needed", layer_error,
                                         layer_name=position_layer_name, layer_type="position")
                    raise
            else:
                enhanced_log(log_callback, f"Position layer '{position_layer_name}' already exists", 'DEBUG')
                layers_existing += 1
            
            # Create cap layer if it doesn't exist  
            log_algorithm_step(log_callback, "BP layer creation", "Creating cap layer")
            if cap_layer_name not in doc.layers:
                try:
                    layer = doc.layers.new(cap_layer_name)
                    layer.color = 6  # Magenta
                    layer.linetype = 'DASHED'
                    enhanced_log(log_callback, f"Created cap layer '{cap_layer_name}' with color 6 (Magenta), linetype DASHED", 'DEBUG')
                    layers_created += 1
                except Exception as layer_error:
                    enhanced_log(log_callback, f"Failed to create cap layer '{cap_layer_name}': {layer_error}", 'ERROR')
                    log_error_with_context(log_callback, "create_bp_layers_if_needed", layer_error,
                                         layer_name=cap_layer_name, layer_type="cap")
                    raise
            else:
                enhanced_log(log_callback, f"Cap layer '{cap_layer_name}' already exists", 'DEBUG')
                layers_existing += 1
            
            # Log results
            log_calculation_result(log_callback, "BP layers created", layers_created)
            log_calculation_result(log_callback, "BP layers existing", layers_existing)
            log_performance_metric(log_callback, "Total BP layers processed", layers_created + layers_existing)
            
            enhanced_log(log_callback, f"BP layer creation completed: {layers_created} created, {layers_existing} existing", 'INFO')
            
            # Validate results
            position_exists = position_layer_name in doc.layers
            cap_exists = cap_layer_name in doc.layers
            log_validation_result(log_callback, "BP position layer exists", position_exists, position_layer_name)
            log_validation_result(log_callback, "BP cap layer exists", cap_exists, cap_layer_name)
            
            if not position_exists:
                enhanced_log(log_callback, f"Position layer '{position_layer_name}' was not created successfully", 'ERROR')
                raise RuntimeError(f"Failed to create position layer: {position_layer_name}")
        
        log_function_exit(log_callback, "create_bp_layers_if_needed",
                         result_layer=position_layer_name,
                         layers_created=layers_created,
                         layers_existing=layers_existing)
        
        return position_layer_name
        
    except Exception as e:
        log_error_with_context(log_callback, "create_bp_layers_if_needed", e,
                              capacity=capacity, diameter=diameter)
        enhanced_log(log_callback, f"BP layer creation failed: {e}", 'ERROR')
        raise


def setup_units(doc: Drawing, units: str, log_callback: Optional[Callable] = None) -> None:
    """
    Setup drawing units and scale.
    
    Args:
        doc: DXF document
        units: Drawing units ('meters', 'millimeters', 'feet', 'inches')
        log_callback: Optional callback function for logging
    """
    log_function_entry(log_callback, "setup_units", units=units)
    
    try:
        enhanced_log(log_callback, "=== DXF UNITS SETUP ===", 'INFO')
        
        with create_timed_logger(log_callback, "DXF units configuration") as units_timer:
            # Validate input parameters
            if not isinstance(doc, Drawing):
                enhanced_log(log_callback, "Invalid DXF document provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", False)
                raise ValueError("Invalid DXF document provided")
            
            log_constraint_check(log_callback, "Valid DXF document", type(doc).__name__, "Drawing", True)
            
            if not isinstance(units, str):
                enhanced_log(log_callback, f"Invalid units type: {type(units)}, expected string", 'ERROR')
                log_constraint_check(log_callback, "Valid units type", type(units).__name__, "str", False)
                raise ValueError(f"Units must be a string, got {type(units)}")
            
            log_constraint_check(log_callback, "Valid units type", type(units).__name__, "str", True)
            enhanced_log(log_callback, f"Setting up DXF units: {units}", 'INFO')
            
            # Define units mapping
            log_algorithm_step(log_callback, "DXF units setup", "Defining units mapping")
            units_mapping = {
                'meters': 1,
                'millimeters': 2,
                'feet': 8,
                'inches': 1
            }
            
            log_calculation_result(log_callback, "Available units", list(units_mapping.keys()))
            enhanced_log(log_callback, f"Available units: {list(units_mapping.keys())}", 'DEBUG')
            
            # Validate and apply units
            units_lower = units.lower()
            log_algorithm_step(log_callback, "DXF units setup", f"Processing units: {units_lower}")
            
            if units_lower in units_mapping:
                try:
                    header = doc.header
                    old_insunits = header.get('$INSUNITS', 'undefined')
                    new_insunits = units_mapping[units_lower]
                    
                    header['$INSUNITS'] = new_insunits
                    
                    enhanced_log(log_callback, f"Drawing units set to: {units} (INSUNITS={new_insunits})", 'INFO')
                    log_calculation_result(log_callback, "Previous INSUNITS", old_insunits)
                    log_calculation_result(log_callback, "New INSUNITS", new_insunits)
                    log_constraint_check(log_callback, "Supported units", units_lower, "in supported list", True)
                    
                    # Validate the change
                    actual_insunits = header.get('$INSUNITS', None)
                    if actual_insunits == new_insunits:
                        log_validation_result(log_callback, "DXF units setup", True, 
                                            f"Successfully set units to {units} (INSUNITS={new_insunits})")
                    else:
                        enhanced_log(log_callback, f"Units validation failed: expected {new_insunits}, got {actual_insunits}", 'WARNING')
                        log_validation_result(log_callback, "DXF units setup", False, 
                                            f"Units mismatch: expected {new_insunits}, got {actual_insunits}")
                    
                except Exception as units_error:
                    enhanced_log(log_callback, f"Failed to set DXF units: {units_error}", 'ERROR')
                    log_error_with_context(log_callback, "setup_units", units_error,
                                         units=units, insunits_value=units_mapping[units_lower])
                    raise
            else:
                enhanced_log(log_callback, f"Unknown units '{units}', using default", 'WARNING')
                log_constraint_check(log_callback, "Supported units", units_lower, "in supported list", False)
                log_validation_result(log_callback, "DXF units setup", False, 
                                    f"Unknown units '{units}', available options: {list(units_mapping.keys())}")
                
                # Keep existing units or use default
                header = doc.header
                current_insunits = header.get('$INSUNITS', 1)  # Default to meters
                enhanced_log(log_callback, f"Keeping current INSUNITS value: {current_insunits}", 'DEBUG')
                log_calculation_result(log_callback, "Fallback INSUNITS", current_insunits)
            
            # Log final units configuration
            final_insunits = doc.header.get('$INSUNITS', 'undefined')
            enhanced_log(log_callback, f"Final DXF units configuration: INSUNITS={final_insunits}", 'DEBUG')
            log_performance_metric(log_callback, "Units setup success", 1 if units_lower in units_mapping else 0)
        
        log_function_exit(log_callback, "setup_units",
                         final_insunits=doc.header.get('$INSUNITS', 'undefined'),
                         success=units_lower in units_mapping)
        
    except Exception as e:
        log_error_with_context(log_callback, "setup_units", e, units=units)
        enhanced_log(log_callback, f"DXF units setup failed: {e}", 'ERROR')
        raise

