﻿"""
Helper Functions

Contains internal helper functions for element preprocessing, distance calculations,
clustering operations, and validation.
"""

import inspect
from typing import List, Dict, Tuple, Callable, Optional, Any
import numpy as np
from scipy.cluster.hierarchy import fcluster
from sklearn.metrics import silhouette_score

from ..data_types import ColumnData, WallData, GroupElements
from ..exceptions import GroupingError, GeometryError
from ..utils import geometry_utils as geom
from ..utils.logging_utils import (
    enhanced_log, log_function_entry, log_function_exit, log_progress,
    log_performance_metric, log_validation_result, log_algorithm_step,
    log_calculation_result, log_error_with_context, create_timed_logger
)
from .element import _Element


def preprocess_elements(columns: List[ColumnData], walls: List[WallData], log_callback=None) -> List[_Element]:
    """
    Preprocess input elements into a unified format for clustering.
    
    Args:
        columns: List of column data tuples
        walls: List of wall data tuples
        log_callback: Optional callback for logging preprocessing progress
        
    Returns:
        List of _Element objects ready for clustering
        
    Raises:
        GeometryError: If there are issues with the input geometry
    """
    log_function_entry(log_callback, "preprocess_elements", 
                      columns_count=len(columns), walls_count=len(walls))
    
    with create_timed_logger(log_callback, "Element Preprocessing") as timer:
        try:
            elements = []
            
            log_algorithm_step(log_callback, "Element Preprocessing", "Starting element conversion")
            log_performance_metric(log_callback, "Input Columns", len(columns), "elements")
            log_performance_metric(log_callback, "Input Walls", len(walls), "elements")
            
            # Process columns
            log_algorithm_step(log_callback, "Element Preprocessing", "Processing columns")
            columns_processed = 0
            columns_skipped = 0
            
            for i, col in enumerate(columns):
                try:
                    log_progress(log_callback, i + 1, len(columns), "column processing")
                    
                    name, x, y, base_level = col
                    if not all(isinstance(coord, (int, float)) for coord in (x, y, base_level)):
                        raise ValueError(f"Invalid coordinates for column {name}: {x}, {y}, {base_level}")
                    
                    # Create a single point for the column using base_level as z-coordinate
                    point = (float(x), float(y), float(base_level))
                    elements.append(_Element(name, 'column', [point], col, log_callback))
                    columns_processed += 1
                    
                    enhanced_log(log_callback, f"Processed column '{name}' at ({x:.2f}, {y:.2f}, {base_level:.2f})", 'DEBUG')
                    
                except (ValueError, TypeError) as e:
                    columns_skipped += 1
                    log_error_with_context(log_callback, e, f"column preprocessing for {col}")
                    enhanced_log(log_callback, f"Skipped invalid column: {col}, error: {e}", 'WARNING')
                    raise GeometryError(f"Invalid column data: {col}") from e
            
            log_calculation_result(log_callback, "Columns Processed", columns_processed, "elements")
            log_calculation_result(log_callback, "Columns Skipped", columns_skipped, "elements")
            
            # Process walls
            log_algorithm_step(log_callback, "Element Preprocessing", "Processing walls")
            walls_processed = 0
            walls_skipped = 0
            
            for i, wall in enumerate(walls):
                try:
                    log_progress(log_callback, i + 1, len(walls), "wall processing")
                    
                    name, points_list, base_level = wall
                    if not points_list or len(points_list) < 2:
                        raise ValueError(f"Wall {name} must have at least 2 points")
                    
                    # Convert 2D wall points to 3D with base_level as z-coordinate
                    points_3d = []
                    for j, (x, y) in enumerate(points_list):
                        if not (isinstance(x, (int, float)) and isinstance(y, (int, float))):
                            raise ValueError(f"Invalid point coordinates in wall {name}: {x}, {y}")
                        points_3d.append((float(x), float(y), float(base_level)))
                    
                    elements.append(_Element(name, 'wall', points_3d, wall, log_callback))
                    walls_processed += 1
                    
                    wall_length = sum(
                        np.sqrt((points_3d[i+1][0] - points_3d[i][0])**2 + (points_3d[i+1][1] - points_3d[i][1])**2)
                        for i in range(len(points_3d) - 1)
                    )
                    log_calculation_result(log_callback, f"Wall '{name}' Length", wall_length, "meters")
                    enhanced_log(log_callback, f"Processed wall '{name}' with {len(points_3d)} points, length {wall_length:.2f}m at level {base_level:.2f}", 'DEBUG')
                        
                except (ValueError, TypeError) as e:
                    walls_skipped += 1
                    log_error_with_context(log_callback, e, f"wall preprocessing for {wall}")
                    enhanced_log(log_callback, f"Skipped invalid wall: {wall}, error: {e}", 'WARNING')
                    raise GeometryError(f"Invalid wall data: {wall}") from e

            # Log final statistics
            log_calculation_result(log_callback, "Walls Processed", walls_processed, "elements")
            log_calculation_result(log_callback, "Walls Skipped", walls_skipped, "elements")
            log_performance_metric(log_callback, "Total Elements Created", len(elements), "elements")
            log_performance_metric(log_callback, "Processing Success Rate", 
                                 (columns_processed + walls_processed) / (len(columns) + len(walls)) * 100, "%")
            
            enhanced_log(log_callback, f"Preprocessing complete - Processed: {columns_processed} columns, {walls_processed} walls", 'INFO')
            if columns_skipped > 0 or walls_skipped > 0:
                enhanced_log(log_callback, f"Skipped: {columns_skipped} columns, {walls_skipped} walls", 'WARNING')
            
            log_function_exit(log_callback, "preprocess_elements", f"{len(elements)} elements created")
            return elements
            
        except Exception as e:
            log_error_with_context(log_callback, e, "preprocess_elements")
            raise


def compute_distance_matrix(elements: List[_Element], log_callback=None) -> np.ndarray:
    """
    Compute pairwise minimum distance matrix between all elements.
    
    Args:
        elements: List of _Element objects
        log_callback: Optional callback for logging distance computation progress
        
    Returns:
        Symmetric distance matrix
        
    Raises:
        GeometryError: If distance calculation fails
    """
    log_function_entry(log_callback, "compute_distance_matrix", elements_count=len(elements))
    
    with create_timed_logger(log_callback, "Distance Matrix Computation") as timer:
        try:
            n = len(elements)
            if n == 0:
                enhanced_log(log_callback, "No elements provided for distance matrix computation", 'WARNING')
                log_function_exit(log_callback, "compute_distance_matrix", "empty matrix")
                return np.array([])

            log_algorithm_step(log_callback, "Distance Computation", "Initializing distance matrix", f"{n}×{n}")
            log_performance_metric(log_callback, "Matrix Size", n * n, "elements")
                
            distance_matrix = np.zeros((n, n))
            total_pairs = n * (n - 1) // 2
            pairs_computed = 0
            close_pairs = 0  # Pairs requiring exact computation
            
            log_performance_metric(log_callback, "Total Pairs to Compute", total_pairs, "pairs")
            log_algorithm_step(log_callback, "Distance Computation", "Computing pairwise distances")
            
            # Progress tracking for large matrices
            progress_interval = max(1, total_pairs // 20)  # Update every 5%
            
            for i in range(n):
                for j in range(i+1, n):
                    # Use the centroids for initial distance estimate
                    dist = geom.distance_3d(
                        elements[i].centroid,
                        elements[j].centroid
                    )
                    
                    # If centroids are close, compute exact minimum distance
                    if dist < 40.0:  # Conservative threshold
                        exact_dist = min_distance_between_elements(
                            elements[i].points,
                            elements[j].points
                        )
                        distance_matrix[i, j] = exact_dist
                        distance_matrix[j, i] = exact_dist
                        close_pairs += 1
                        enhanced_log(log_callback, f"Exact distance {elements[i].name} ↔ {elements[j].name}: {exact_dist:.2f}m", 'DEBUG')
                    else:
                        distance_matrix[i, j] = dist
                        distance_matrix[j, i] = dist
                    
                    pairs_computed += 1
                    
                    # Progress logging for large datasets
                    if pairs_computed % progress_interval == 0:
                        log_progress(log_callback, pairs_computed, total_pairs, "distance computation")
                    
            # Calculate and log statistics
            nonzero_distances = distance_matrix[distance_matrix > 0]
            if len(nonzero_distances) > 0:
                min_dist = np.min(nonzero_distances)
                max_dist = np.max(distance_matrix)
                avg_dist = np.mean(nonzero_distances)
                std_dist = np.std(nonzero_distances)
                
                log_calculation_result(log_callback, "Distance Matrix Statistics", 
                                     f"Min: {min_dist:.2f}m, Max: {max_dist:.2f}m, Avg: {avg_dist:.2f}m, Std: {std_dist:.2f}m")
                log_performance_metric(log_callback, "Close Pairs Requiring Exact Computation", close_pairs, "pairs")
                log_performance_metric(log_callback, "Exact Computation Percentage", 
                                     close_pairs / total_pairs * 100 if total_pairs > 0 else 0, "%")
            else:
                enhanced_log(log_callback, "All distances are zero", 'WARNING')
                    
            enhanced_log(log_callback, f"Distance matrix computed - {pairs_computed} pairs, {close_pairs} required exact computation", 'INFO')
            log_function_exit(log_callback, "compute_distance_matrix", f"{n}×{n} matrix")
            return distance_matrix
            
        except Exception as e:
            log_error_with_context(log_callback, e, "compute_distance_matrix")
            raise GeometryError(f"Failed to compute distance matrix: {str(e)}") from e


def min_distance_between_elements(points1: List[Tuple[float, float, float]], 
                                  points2: List[Tuple[float, float, float]],
                                  log_callback=None) -> float:
    """
    Calculate minimum distance between any two points from two sets of 3D points.
    
    Args:
        points1: First set of 3D points
        points2: Second set of 3D points
        log_callback: Optional callback for logging distance calculation
        
    Returns:
        Minimum Euclidean distance between any point in points1 and any point in points2
    """
    # Only log for debug purposes since this is called frequently
    enhanced_log(log_callback, f"Computing min distance between {len(points1)} and {len(points2)} points", 'DEBUG')
    
    if not points1 or not points2:
        enhanced_log(log_callback, "Empty point set provided, returning infinite distance", 'DEBUG')
        return float('inf')
        
    min_dist_sq = float('inf')
    
    # Convert to numpy arrays for vectorized operations
    points1_arr = np.array(points1)
    points2_arr = np.array(points2)
    
    # For each point in points1, find minimum distance to points2
    for p1 in points1_arr:
        # Vectorized distance squared calculation
        dists_sq = np.sum((points2_arr - p1) ** 2, axis=1)
        min_dist_sq = min(min_dist_sq, np.min(dists_sq))
        
        if min_dist_sq == 0:  # Can't get any smaller
            enhanced_log(log_callback, "Found zero distance between points", 'DEBUG')
            return 0.0
    
    result = np.sqrt(min_dist_sq)
    enhanced_log(log_callback, f"Minimum distance calculated: {result:.3f}m", 'DEBUG')
    return result


def find_optimal_threshold_internal(elements: List[_Element], 
                                    distance_matrix: np.ndarray,
                                    linkage_matrix: np.ndarray,
                                    min_threshold: float,
                                    max_threshold: float,
                                    step: float,
                                    log_callback=None) -> float:
    """
    Find the optimal distance threshold for hierarchical clustering using silhouette score.
    
    Args:
        elements: List of elements being clustered
        distance_matrix: Precomputed distance matrix
        linkage_matrix: Linkage matrix from hierarchical clustering
        min_threshold: Minimum threshold to evaluate
        max_threshold: Maximum threshold to evaluate
        step: Step size for threshold search
        log_callback: Optional callback for logging threshold search progress
        
    Returns:
        Optimal distance threshold for clustering
        
    Raises:
        GroupingError: If optimal threshold cannot be determined
    """
    log_function_entry(log_callback, "find_optimal_threshold_internal",
                      elements_count=len(elements), min_threshold=min_threshold,
                      max_threshold=max_threshold, step=step)
    
    with create_timed_logger(log_callback, "Threshold Optimization") as timer:
        try:
            if len(elements) <= 1:
                enhanced_log(log_callback, f"Only {len(elements)} element(s), no threshold optimization needed", 'INFO')
                log_function_exit(log_callback, "find_optimal_threshold_internal", min_threshold)
                return min_threshold

            # Validate distance matrix
            if distance_matrix.size == 0:
                enhanced_log(log_callback, "Empty distance matrix, returning min_threshold", 'WARNING')
                log_function_exit(log_callback, "find_optimal_threshold_internal", min_threshold)
                return min_threshold
                
            max_dist_value = np.max(distance_matrix)
            
            # Ensure max_dist_value is a proper scalar float
            if hasattr(max_dist_value, 'item'):
                max_dist_value = max_dist_value.item()
            max_dist_value = float(max_dist_value)
            
            # Ensure max_threshold is also a proper float
            max_threshold = float(max_threshold)
            
            max_threshold = min(max_threshold, max_dist_value * 1.1)
            log_calculation_result(log_callback, "Adjusted Max Threshold", max_threshold, "meters")
            
            thresholds = np.arange(min_threshold, max_threshold, step)
            if not thresholds.size:
                enhanced_log(log_callback, f"No thresholds to evaluate in range {min_threshold}-{max_threshold}", 'WARNING')
                log_function_exit(log_callback, "find_optimal_threshold_internal", min_threshold)
                return min_threshold

            log_algorithm_step(log_callback, "Silhouette Analysis", "Starting threshold evaluation",
                             f"{len(thresholds)} thresholds from {min_threshold:.1f}m to {max_threshold:.1f}m")
            log_performance_metric(log_callback, "Thresholds to Evaluate", len(thresholds), "thresholds")
                
            best_score = -1
            best_threshold = min_threshold
            threshold_results = []
            valid_evaluations = 0
            
            # Evaluate each threshold
            for i, threshold in enumerate(thresholds):
                log_progress(log_callback, i + 1, len(thresholds), "threshold evaluation")
                
                # Get cluster assignments
                clusters = fcluster(linkage_matrix, t=threshold, criterion='distance')
                n_clusters = len(set(clusters))
                
                # Skip if all in one cluster or each point is its own cluster
                if n_clusters == 1 or n_clusters == len(elements):
                    enhanced_log(log_callback, f"Threshold {threshold:.1f}m: {n_clusters} clusters (skipped - trivial)", 'DEBUG')
                    continue

                # Calculate silhouette score (higher is better)
                if len(elements) >= 2:  # Silhouette score requires at least 2 clusters
                    score = silhouette_score(distance_matrix, clusters, metric='precomputed')
                    valid_evaluations += 1
                else:
                    score = 1.0  # Perfect score for 1 cluster

                threshold_results.append((threshold, n_clusters, score))
                log_calculation_result(log_callback, f"Threshold {threshold:.1f}m Evaluation", 
                                     f"{n_clusters} clusters, score {score:.3f}")

                enhanced_log(log_callback, f"Threshold {threshold:.1f}m: {n_clusters} clusters, silhouette score {score:.3f}", 'DEBUG')

                # Update best threshold if this score is better
                if score > best_score:
                    best_score = score
                    best_threshold = threshold
                    enhanced_log(log_callback, f"New best threshold: {threshold:.1f}m (score: {score:.3f})", 'INFO')
            
            # Log comprehensive results
            log_performance_metric(log_callback, "Valid Evaluations", valid_evaluations, "thresholds")
            log_calculation_result(log_callback, "Best Threshold", best_threshold, "meters")
            log_calculation_result(log_callback, "Best Silhouette Score", best_score)
            
            enhanced_log(log_callback, f"Threshold optimization complete", 'INFO')
            enhanced_log(log_callback, f"Best threshold: {best_threshold:.1f}m with silhouette score {best_score:.3f}", 'INFO')
            
            if threshold_results:
                sorted_results = sorted(threshold_results, key=lambda x: x[2], reverse=True)
                enhanced_log(log_callback, f"Top 3 thresholds by score:", 'DEBUG')
                for j, (thresh, n_clust, score) in enumerate(sorted_results[:3]):
                    enhanced_log(log_callback, f"#{j+1}: {thresh:.1f}m ({n_clust} clusters, score {score:.3f})", 'DEBUG')
                    
                # Log score distribution statistics
                scores = [result[2] for result in threshold_results]
                if len(scores) > 1:
                    score_stats = f"Score range: {min(scores):.3f} to {max(scores):.3f}, std: {np.std(scores):.3f}"
                    log_calculation_result(log_callback, "Score Statistics", score_stats)

            final_threshold = best_threshold if best_score > 0 else min_threshold
            log_function_exit(log_callback, "find_optimal_threshold_internal", f"{final_threshold:.2f}m")
            return final_threshold

        except Exception as e:
            log_error_with_context(log_callback, e, "find_optimal_threshold_internal")
            raise GroupingError(f"Failed to find optimal threshold: {str(e)}") from e


def form_groups(elements: List[_Element], cluster_labels: np.ndarray, log_callback=None) -> Dict[str, GroupElements]:
    """
    Form groups of elements based on cluster labels.
    
    Args:
        elements: List of elements to group
        cluster_labels: Array of cluster labels for each element
        log_callback: Optional callback for logging group formation progress
        
    Returns:
        Dictionary mapping group IDs to GroupElements
        
    Raises:
        GroupingError: If group formation fails
    """
    log_function_entry(log_callback, "form_groups", 
                      elements_count=len(elements), unique_labels=len(set(cluster_labels)))
    
    with create_timed_logger(log_callback, "Group Formation") as timer:
        try:
            log_algorithm_step(log_callback, "Group Formation", "Starting group organization")
            log_performance_metric(log_callback, "Input Elements", len(elements), "elements")
            log_performance_metric(log_callback, "Cluster Labels", len(set(cluster_labels)), "unique labels")

            groups: Dict[str, GroupElements] = {}
            elements_processed = 0

            # Group elements by cluster label
            for element, label in zip(elements, cluster_labels):
                group_id = f'group_{int(label)}'
                if group_id not in groups:
                    groups[group_id] = {'columns': [], 'walls': []}
                    enhanced_log(log_callback, f"Created new group: {group_id}", 'DEBUG')

                if element.element_type == 'column':
                    groups[group_id]['columns'].append(element.data)
                else:  # wall
                    groups[group_id]['walls'].append(element.data)
                
                elements_processed += 1
                enhanced_log(log_callback, f"Added {element.element_type} '{element.name}' to {group_id}", 'DEBUG')

            # Log detailed group statistics
            log_algorithm_step(log_callback, "Group Formation", "Computing group statistics")
            log_performance_metric(log_callback, "Groups Created", len(groups), "groups")
            log_performance_metric(log_callback, "Elements Processed", elements_processed, "elements")

            # Calculate and log group size distribution
            group_sizes = []
            mixed_groups = 0
            column_only_groups = 0
            wall_only_groups = 0

            for group_id, group_data in groups.items():
                columns_count = len(group_data['columns'])
                walls_count = len(group_data['walls'])
                total_elements = columns_count + walls_count
                group_sizes.append(total_elements)
                
                log_calculation_result(log_callback, f"{group_id} Composition", 
                                     f"{total_elements} elements ({columns_count} columns, {walls_count} walls)")
                enhanced_log(log_callback, f"{group_id}: {total_elements} elements ({columns_count} columns, {walls_count} walls)", 'DEBUG')
                
                # Track group types
                if columns_count > 0 and walls_count > 0:
                    mixed_groups += 1
                elif columns_count > 0:
                    column_only_groups += 1
                elif walls_count > 0:
                    wall_only_groups += 1

            # Log summary statistics
            if group_sizes:
                avg_group_size = sum(group_sizes) / len(group_sizes)
                min_group_size = min(group_sizes)
                max_group_size = max(group_sizes)
                
                log_performance_metric(log_callback, "Average Group Size", avg_group_size, "elements")
                log_performance_metric(log_callback, "Min Group Size", min_group_size, "elements")
                log_performance_metric(log_callback, "Max Group Size", max_group_size, "elements")
                
                log_performance_metric(log_callback, "Mixed Groups", mixed_groups, "groups")
                log_performance_metric(log_callback, "Column-Only Groups", column_only_groups, "groups")
                log_performance_metric(log_callback, "Wall-Only Groups", wall_only_groups, "groups")
                
                log_calculation_result(log_callback, "Group Size Distribution", str(group_sizes))

            enhanced_log(log_callback, f"Group formation complete - Created {len(groups)} groups", 'INFO')
            log_function_exit(log_callback, "form_groups", f"{len(groups)} groups")
            return groups
            
        except Exception as e:
            log_error_with_context(log_callback, e, "form_groups")
            raise GroupingError(f"Failed to form groups: {str(e)}") from e


def create_single_element_group(element: _Element, log_callback=None) -> Dict[str, GroupElements]:
    """
    Create a group containing a single element.
    
    Args:
        element: The element to create a group for
        log_callback: Optional callback for logging single group creation
        
    Returns:
        Dictionary with a single group containing the element
    """
    log_function_entry(log_callback, "create_single_element_group", 
                      element_name=element.name, element_type=element.element_type)
    
    try:
        group_id = 'group_1'
        
        if element.element_type == 'column':
            result = {group_id: {'columns': [element.data], 'walls': []}}
            enhanced_log(log_callback, f"Created single-element group for column '{element.name}'", 'INFO')
        else:
            result = {group_id: {'columns': [], 'walls': [element.data]}}
            enhanced_log(log_callback, f"Created single-element group for wall '{element.name}'", 'INFO')
        
        log_calculation_result(log_callback, "Single Element Group", f"{group_id} with 1 {element.element_type}")
        log_function_exit(log_callback, "create_single_element_group", f"{group_id} created")
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"create_single_element_group for {element.name}")
        raise


def validate_groups(groups: Dict[str, GroupElements], log_callback=None) -> None:
    """
    Validate that groups meet the required criteria.
    
    Args:
        groups: Dictionary of groups to validate
        log_callback: Optional callback for logging validation progress
        
    Raises:
        GroupingError: If any group is invalid
    """
    log_function_entry(log_callback, "validate_groups", groups_count=len(groups))
    
    with create_timed_logger(log_callback, "Group Validation") as timer:
        try:
            if not groups:
                enhanced_log(log_callback, "No groups to validate", 'INFO')
                log_function_exit(log_callback, "validate_groups", "no groups")
                return

            log_algorithm_step(log_callback, "Group Validation", "Starting validation checks")
            log_performance_metric(log_callback, "Groups to Validate", len(groups), "groups")
                
            min_group_size = 1
            max_group_size = 20
            validation_errors = []
            valid_groups = 0
            total_elements = 0
            
            for group_id, group_data in groups.items():
                columns_count = len(group_data['columns'])
                walls_count = len(group_data['walls'])
                group_size = columns_count + walls_count
                total_elements += group_size
                
                enhanced_log(log_callback, f"Validating {group_id}: {group_size} elements ({columns_count} columns, {walls_count} walls)", 'DEBUG')

                # Size validation
                if group_size < min_group_size:
                    error_msg = (f"Group {group_id} has {group_size} elements, "
                                f"which is below the minimum of {min_group_size}")
                    validation_errors.append(error_msg)
                    log_validation_result(log_callback, f"{group_id} Size Check", False, 
                                        f"{group_size} < {min_group_size}")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                elif group_size > max_group_size:
                    error_msg = (f"Group {group_id} has {group_size} elements, "
                                f"which exceeds the maximum of {max_group_size}")
                    validation_errors.append(error_msg)
                    log_validation_result(log_callback, f"{group_id} Size Check", False, 
                                        f"{group_size} > {max_group_size}")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                else:
                    log_validation_result(log_callback, f"{group_id} Size Check", True, 
                                        f"{group_size} elements within limits")
                    valid_groups += 1

                # Content validation
                if group_size == 0:
                    error_msg = f"Group {group_id} is empty"
                    validation_errors.append(error_msg)
                    log_validation_result(log_callback, f"{group_id} Content Check", False, "empty group")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                else:
                    log_validation_result(log_callback, f"{group_id} Content Check", True, 
                                        f"contains {group_size} elements")
            
            # Overall validation results
            log_performance_metric(log_callback, "Valid Groups", valid_groups, "groups")
            log_performance_metric(log_callback, "Invalid Groups", len(groups) - valid_groups, "groups")
            log_performance_metric(log_callback, "Total Elements Validated", total_elements, "elements")
            log_performance_metric(log_callback, "Validation Success Rate", 
                                 valid_groups / len(groups) * 100 if groups else 100, "%")
            
            if validation_errors:
                combined_error = "; ".join(validation_errors)
                log_validation_result(log_callback, "Overall Group Validation", False, 
                                    f"{len(validation_errors)} errors found")
                enhanced_log(log_callback, f"Group validation failed: {combined_error}", 'ERROR')
                log_function_exit(log_callback, "validate_groups", "validation failed")
                raise GroupingError(f"Group validation failed: {combined_error}")

            log_validation_result(log_callback, "Overall Group Validation", True, 
                                f"All {len(groups)} groups passed validation")
            enhanced_log(log_callback, "All groups passed validation", 'INFO')
            log_function_exit(log_callback, "validate_groups", f"{len(groups)} groups validated")
            
        except GroupingError:
            # Re-raise GroupingError as-is
            raise
        except Exception as e:
            log_error_with_context(log_callback, e, "validate_groups")
            raise GroupingError(f"Group validation failed due to unexpected error: {str(e)}") from e

