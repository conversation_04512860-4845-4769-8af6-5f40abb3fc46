"""
SAFE 22 Structural Property Writing Module

This module provides comprehensive functionality for processing and writing structural
property definitions for SAFE 16 and SAFE 22 finite element analysis software. It handles
the conversion of structural property data from Excel input formats to SAFE-compatible
property definitions, including material assignments, section properties, and geometric
characteristics for various structural elements.

The module processes three main categories of structural properties:
- Beam Properties: Rectangular concrete beam sections with material assignments
- Column Properties: Circular concrete (BP) and H-section steel (HP) column definitions
- Slab Properties: Solid slab definitions with thickness and material specifications

Key Features:
- Dual-format support for both SAFE 16 and SAFE 22 property definitions
- Automatic property validation and error handling with graceful degradation
- Material property linking between structural elements and material libraries
- Section property calculations and geometric parameter conversions
- Color coding and visualization properties for structural element identification
- Comprehensive error logging with timestamp tracking for debugging

Structural Property Types:
- Beam Properties:
  * Rectangular concrete sections with depth and width specifications
  * Material assignments linking to concrete grade definitions
  * General properties including type classification and visual attributes

- Column Properties:
  * Circular sections (BP): Bored pile and drilled shaft configurations
  * H-sections (HP): Steel I-beam and wide flange configurations
  * General properties with rigid zone and drop panel specifications

- Slab Properties:
  * Solid slab definitions with uniform thickness
  * Material assignments for concrete grade specifications
  * Orthotropic behavior flags and modeling type specifications

Usage:
    This module is typically used as part of the larger SAFE model generation workflow:

    >>> from build_fem.write_safe import write_str_prop
    >>> safe16_dfs, safe22_dfs = write_str_prop.write_beam_prop(excel_inputs, safe16_dfs, safe22_dfs)
    >>> safe16_dfs, safe22_dfs = write_str_prop.write_column_prop(excel_inputs, safe16_dfs, safe22_dfs)
    >>> safe16_dfs, safe22_dfs = write_str_prop.write_slab_prop(excel_inputs, safe16_dfs, safe22_dfs)

Dependencies:
    - build_fem.write_safe.safe16: SAFE 16 specific property writing functions
    - build_fem.write_safe.safe22: SAFE 22 specific property writing functions
    - pandas: Used for DataFrame operations and data manipulation
    - datetime: Used for error logging and timestamp generation

Note:
    All functions in this module include comprehensive error handling to ensure
    that property processing failures do not crash the entire model generation
    process. Error messages are logged with timestamps for debugging purposes,
    and original DataFrames are returned in case of errors to allow continued
    processing of other model components.

    The module maintains compatibility with both SAFE 16 and SAFE 22 formats,
    automatically handling the different data structure requirements and property
    definition formats between the two software versions.

Author: Foundation Automation System
Date: 2025
Version: Compatible with SAFE 16/22 and pile foundation automation workflows
"""

from datetime import datetime
from typing import Any, Tuple

import pandas as pd

from build_fem.write_safe.safe16 import (
    _write_beam_rectangular_section_safe16,
    _write_column_circular_section_safe16, _write_column_h_section_safe16,
    _write_slab_general_props_safe16, _write_slab_solid_props_safe16, _write_slab_prop_safe16,
    _write_beam_general_properties_safe16
)
from build_fem.write_safe.safe22 import (
    beam_FrameSecDefConcRect_safe22, bp_FrameSecDefConcCircle_safe22,
    hp_FrameSecDefSteelI_safe22, slab_SlabPropertyDefinitions_safe22
)


def write_beam_prop(excel_inputs: Any, safe16_dfs: Any, safe22_dfs: Any) -> Tuple[Any, Any]:
    """
    Process beam property data and write to SAFE 16 and SAFE 22 data structures.

    This function processes rectangular concrete beam section properties from Excel
    inputs and converts them to both SAFE 16 and SAFE 22 format specifications.
    It handles general beam properties (type, color, identification) and detailed
    section properties (dimensions, materials, reinforcement specifications).
    """
    try:
        # Process general beam properties for SAFE 16 (type, color, identification)
        safe16_dfs = _write_beam_general_properties_safe16(excel_inputs, safe16_dfs)

        # Process rectangular beam section properties for both SAFE versions
        # SAFE 16: Handles basic rectangular section with depth, width, material
        safe16_dfs = _write_beam_rectangular_section_safe16(excel_inputs, safe16_dfs)

        # SAFE 22: Includes advanced parameters like cover, modifiers, reinforcement materials
        safe22_dfs, df_append = beam_FrameSecDefConcRect_safe22(excel_inputs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the entire process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing beam properties: {str(e)}")
        return safe16_dfs, safe22_dfs


def write_column_prop_general_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Process general column property data and write to SAFE 16 data structures.

    This function processes general column properties from both bored pile (BP) and
    H-pile (HP) property definitions, combining them into a unified general column
    property dataset for SAFE 16. It handles basic identification, type classification,
    and visual properties that are common to all column types.
    """
    try:
        # Extract and prepare bored pile (BP) column properties
        df_bp_prop = excel_inputs.BPProp.copy()
        df_hp_prop = excel_inputs.HPProp.copy()

        # Assign standard color coding for visual identification in SAFE
        df_bp_prop['Color'] = 'Red'
        df_hp_prop['Color'] = 'Red'

        # Define columns needed for general column properties
        titles = ['Column', 'Type', 'Color']

        # Extract only the required columns for general properties
        df_bp_prop = df_bp_prop[titles]
        df_hp_prop = df_hp_prop[titles]

        # Combine both column types into a unified general property dataset
        df_column_prop = pd.concat([df_bp_prop, df_hp_prop], ignore_index=True)

        # Standardize column names to SAFE 16 format requirements
        df_column_prop.columns = ['Column (Text)', 'Type (Text)', 'Color (Text)']

        # Ensure proper column ordering for SAFE 16 compatibility
        df_column_prop = df_column_prop[['Column (Text)', 'Type (Text)', 'Color (Text)']].copy()

        # Convert to SAFE 16 MultiIndex format and append to existing data
        target_columns = safe16_dfs.ColumnProperties01General.columns.tolist()
        df_append_16 = pd.DataFrame(
            df_column_prop.values, columns=pd.MultiIndex.from_tuples(target_columns)
        )
        safe16_dfs.ColumnProperties01General = pd.concat(
            [safe16_dfs.ColumnProperties01General, df_append_16], ignore_index=True
        )

        return safe16_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing general column properties: {str(e)}")
        return safe16_dfs


def write_column_prop_circular(excel_inputs: Any, safe16_dfs: Any, safe22_dfs: Any) -> Tuple[Any, Any]:
    """
    Process circular column property data and write to SAFE 16 and SAFE 22 data structures.

    This function processes circular concrete column section properties, typically used
    for bored pile (BP) foundations and drilled shaft applications. It converts the
    properties from Excel input format to both SAFE 16 and SAFE 22 specifications,
    handling diameter specifications, material assignments, and behavioral parameters.
    """
    try:
        # Process circular column properties for SAFE 16 format
        # Handles basic circular section parameters and behavioral flags
        safe16_dfs = _write_column_circular_section_safe16(excel_inputs, safe16_dfs)

        # Process circular column properties for SAFE 22 format
        # Includes advanced parameters, modifiers, and enhanced property definitions
        safe22_dfs, df_append = bp_FrameSecDefConcCircle_safe22(excel_inputs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing circular column properties: {str(e)}")
        return safe16_dfs, safe22_dfs


def write_column_prop_H(excel_inputs: Any, safe16_dfs: Any, safe22_dfs: Any) -> Tuple[Any, Any]:
    """
    Process H-section column property data and write to SAFE 16 and SAFE 22 data structures.

    This function processes steel H-section column properties, typically used for
    structural steel framing and H-pile foundation applications. It converts the
    properties from Excel input format to both SAFE 16 and SAFE 22 specifications,
    handling flange dimensions, web thickness, material assignments, and section moduli.
    """
    try:
        # Process H-section column properties for SAFE 16 format
        # Uses general shape format with calculated section properties
        safe16_dfs = _write_column_h_section_safe16(excel_inputs, safe16_dfs)

        # Process H-section column properties for SAFE 22 format
        # Uses detailed steel I-section format with flange and web specifications
        safe22_dfs, df_append = hp_FrameSecDefSteelI_safe22(excel_inputs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing H-section column properties: {str(e)}")
        return safe16_dfs, safe22_dfs


def write_column_prop(excel_inputs: Any, safe16_dfs: Any, safe22_dfs: Any) -> Tuple[Any, Any]:
    """
    Process all column property data and write to SAFE 16 and SAFE 22 data structures.

    This function serves as the main coordinator for processing all types of column
    properties from Excel inputs, including general properties, circular concrete
    sections (bored piles), and H-section steel columns. It orchestrates the complete
    column property processing workflow to ensure all column types are properly
    defined in both SAFE 16 and SAFE 22 formats.
    """
    try:
        # Step 1: Process general column properties for SAFE 16
        # This creates the basic column identification and classification data
        safe16_dfs = write_column_prop_general_safe16(excel_inputs, safe16_dfs)

        # Step 2: Process circular column properties for both SAFE versions
        # Handles bored pile and circular concrete column sections
        safe16_dfs, safe22_dfs = write_column_prop_circular(excel_inputs, safe16_dfs, safe22_dfs)

        # Step 3: Process H-section column properties for both SAFE versions
        # Handles steel H-section and I-beam column sections
        safe16_dfs, safe22_dfs = write_column_prop_H(excel_inputs, safe16_dfs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the entire process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing column properties: {str(e)}")
        # Return original DataFrames to allow further processing if possible
        return safe16_dfs, safe22_dfs


def write_slab_prop(excel_inputs: Any, safe16_dfs: Any, safe22_dfs: Any) -> Tuple[Any, Any]:
    """
    Process slab property data and write to SAFE 16 and SAFE 22 data structures.

    This function processes solid slab property definitions from Excel inputs and
    converts them to both SAFE 16 and SAFE 22 format specifications. It handles
    slab thickness, material assignments, orthotropic behavior flags, and modeling
    type specifications for concrete slab elements in structural analysis.
    """
    try:
        # Process slab properties for SAFE 16 format
        # Includes both general properties and solid slab specific parameters
        safe16_dfs = _write_slab_prop_safe16(excel_inputs, safe16_dfs)

        # Process slab properties for SAFE 22 format
        # Includes enhanced property definitions with modifiers and advanced parameters
        safe22_dfs, df_append = slab_SlabPropertyDefinitions_safe22(excel_inputs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Log error with timestamp but don't crash the process
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing slab properties: {str(e)}")
        return safe16_dfs, safe22_dfs


