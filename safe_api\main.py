# filepath: c:\Users\<USER>\VSCodeProjects\Foundation-Automation\safe_api\main.py
"""
Main Orchestration Module

This module serves as the main entry point for the SAFE automation script,
orchestrating all the different components and workflows.
"""

import os
import sys
from datetime import datetime
import argparse
import main_class

# Add the parent directory (workspace root) to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import SAFE API modules - use relative imports when possible, absolute when run directly
try:
    from . import config
    from .safe_connection import setup_safe_connection
    from .models import (setup_model_and_inputs, get_sap_model_object, 
                         initialize_model, import_and_run_analysis)
    from .data_processor import (extract_safe22_dataframe,
                                process_point_object_connectivity,
                                process_frame_assignments_local_axes,
                                process_joint_displacements,
                                process_element_forces_columns,
                                setup_load_combinations_for_display)
    from .database_manager import (setup_database_path, process_and_save_dataframe,
                                  save_df_to_database, save_df_to_sqlite)
except ImportError:
    # Fallback to absolute imports when run directly
    import safe_api.config as config
    from safe_api.safe_connection import setup_safe_connection
    from safe_api.models import (setup_model_and_inputs, get_sap_model_object,
                                 initialize_model, import_and_run_analysis)
    from safe_api.data_processor import (extract_safe22_dataframe,
                                         process_point_object_connectivity,
                                         process_frame_assignments_local_axes,
                                         process_joint_displacements,
                                         process_element_forces_columns,
                                         setup_load_combinations_for_display)
    from safe_api.database_manager import (setup_database_path, process_and_save_dataframe,
                                           save_df_to_database, save_df_to_sqlite)

# Import build_fem modules
import build_fem.builder_main as build_fem_main
import build_fem.write_safe.safe16.safe16_class as class_safe16
import build_fem.write_safe.safe22.safe22_class as class_safe22

# Import design_fdn modules
import design_fdn.designer_main as design_fdn_main

# import warnings
# warnings.simplefilter(action='ignore', category=FutureWarning)

class AutomationLogger:
    """Simple logger class for automation"""
    def __init__(self, log_file_path=None):
        self.log_file_path = log_file_path
        
    def log(self, message):
        """Log message to console and optionally to file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        print(formatted_message)
        
        if self.log_file_path:
            try:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(formatted_message + '\n')
            except Exception as e:
                print(f"Failed to write to log file: {e}")


def automate_build_fem(input_folder, existing_folder=None, design_strip_option=1, log_file_path=None):
    """
    Automate the build_fem process including initialization, soil spring generation, and SAFE model generation.
    
    Args:
        input_folder (str): Path to the input folder containing SAFE input files
        existing_folder (str, optional): Path to existing input folder for updating data
        design_strip_option (int): 1 to generate design strips, 0 to skip (default: 1)
        log_file_path (str, optional): Path to save the log file
        
    Returns:
        tuple: (success, file_paths) where success is bool and file_paths is the file paths object
    """
    logger = AutomationLogger(log_file_path)
    logger.log("Starting automated SAFE model building process...")
    logger.log(f"Input folder: {input_folder}")
    if existing_folder:
        logger.log(f"Existing folder: {existing_folder}")
    
    # Initialize variables
    file_paths = None
    excel_inputs = None
    excel_outputs = None
    safe16_dfs = None
    safe22_dfs = None
    
    try:
        # Step 1: Initialize
        logger.log("\n=== Step 1: Initializing file paths and inputs ===")
        file_paths, excel_inputs, excel_outputs = build_fem_main.automated_initialize_setup(
            input_folder, 
            existing_folder, 
            logger.log
        )
        
        if not all([file_paths, excel_inputs, excel_outputs]):
            logger.log("ERROR: Failed to initialize file paths and inputs")
            return False, None
            
        logger.log("Initialization completed successfully")
        
        # Step 2: Generate Lateral Soil Spring
        logger.log("\n=== Step 2: Generating Lateral Soil Springs ===")
        
        def progress_callback(percent, description):
            logger.log(f"Progress: {percent}% - {description}")
            
        success = build_fem_main.automated_generate_soil_spring(
            file_paths,
            excel_inputs,
            logger.log,
            progress_callback
        )
        
        if not success:
            logger.log("ERROR: Failed to generate soil springs")
            return False, file_paths
            
        logger.log("Soil spring generation completed successfully")
        
        # Step 3: Generate SAFE Model
        logger.log("\n=== Step 3: Generating SAFE Model ===")
        
        # Initialize SAFE dataframes
        safe16_dfs = class_safe16.Safe16DataFrames()
        safe22_dfs = class_safe22.Safe22DataFrames()
        
        logger.log(f"Design strip option: {'Yes' if design_strip_option == 1 else 'No'}")
        
        success = build_fem_main.automated_generate_safe_model(
            file_paths,
            excel_inputs,
            excel_outputs,
            safe16_dfs,
            safe22_dfs,
            design_strip_option,
            logger.log,
            progress_callback
        )
        
        if not success:
            logger.log("ERROR: Failed to generate SAFE model")
            return False, file_paths
            
        logger.log("SAFE model generation completed successfully")
        logger.log(f"\nSAFE model files saved to: {os.path.dirname(file_paths.ExcelSAFE16Model)}")
        
        # Save the log
        if file_paths and hasattr(file_paths, 'Log'):
            logger.log(f"\nLog saved to: {file_paths.Log}")
            
        return True, file_paths
        
    except Exception as e:
        logger.log(f"\nERROR: Unexpected error during automation: {str(e)}")
        import traceback
        logger.log(traceback.format_exc())
        return False, file_paths


def automate_foundation_design(mdb_path, excel_folder, use_tgn53=True, log_file_path=None):
    """
    Automate the foundation design process including ULS/SLS calculations and design automation.
    
    Args:
        mdb_path (str): Path to the SAFE result database
        excel_folder (str): Path to the Excel master data folder
        use_tgn53 (bool): Whether to follow GEO TGN 53 (default: True)
        log_file_path (str, optional): Path to save the log file
        
    Returns:
        tuple: (success, design_results) where success is bool and design_results is the design results object
    """
    logger = AutomationLogger(log_file_path)
    logger.log("Starting automated foundation design process...")
    logger.log(f"SAFE Result Database: {mdb_path}")
    logger.log(f"Excel Master Data Folder: {excel_folder}")
    logger.log(f"Follow GEO TGN 53: {'Yes' if use_tgn53 else 'No'}")
    
    # Initialize variables
    file_paths = None
    excel_inputs = None
    safe_mdbs = None
    excel_outputs = None
    design_results = None
    
    try:
        # Step 1: Initialize file paths
        logger.log("\n=== Step 1: Initializing file paths ===")
        file_paths = design_fdn_main.initialize_file_paths(
            mdb_path, 
            excel_folder, 
            logger.log
        )
        
        if not file_paths:
            logger.log("ERROR: Failed to initialize file paths")
            return False, None
            
        logger.log("File paths initialized successfully")
        
        # Step 2: Initialize data structures
        logger.log("\n=== Step 2: Initializing data structures ===")

        excel_inputs = main_class.ExcelInputs()
        excel_outputs = main_class.ExcelOutputs()
        safe_mdbs = main_class.SafeMdbs()
        design_results = main_class.DesignResults()
        
        logger.log("Data structures initialized successfully")
        
        # Step 3: Read input data
        logger.log("\n=== Step 3: Reading input data ===")
        design_fdn_main.read_input_data(
            file_paths,
            excel_inputs,
            safe_mdbs,
            logger.log
        )
        
        logger.log("Input data read successfully")
        
        # Step 4: Run ULS/SLS calculations
        logger.log("\n=== Step 4: Running ULS/SLS calculations ===")
        design_fdn_main.run_uls_sls_calculations(
            file_paths,
            safe_mdbs,
            excel_inputs,
            excel_outputs,
            design_results,
            logger.log
        )
        
        logger.log("ULS/SLS calculations completed successfully")
        
        # Step 5: Run design automation
        logger.log("\n=== Step 5: Running design automation ===")
        design_fdn_main.run_design_automation(
            file_paths,
            safe_mdbs,
            excel_inputs,
            excel_outputs,
            design_results,
            use_tgn53,
            logger.log
        )
        
        logger.log("Design automation completed successfully")
        logger.log(f"\nDesign results saved to: {os.path.dirname(file_paths.Log)}")
        
        # Save the log
        if file_paths and hasattr(file_paths, 'Log'):
            logger.log(f"\nLog saved to: {file_paths.Log}")
            
        return True, design_results
        
    except Exception as e:
        logger.log(f"\nERROR: Unexpected error during foundation design automation: {str(e)}")
        import traceback
        logger.log(traceback.format_exc())
        return False, design_results


def automate_complete_workflow(input_folder, existing_folder=None, design_strip_option=1, 
                              use_tgn53=True, log_file_path=None):
    """
    Complete automation workflow: build_fem -> SAFE analysis -> foundation design.
    
    Args:
        input_folder (str): Path to the input folder containing SAFE input files
        existing_folder (str, optional): Path to existing input folder for updating data
        design_strip_option (int): 1 to generate design strips, 0 to skip (default: 1)
        use_tgn53 (bool): Whether to follow GEO TGN 53 for foundation design (default: True)
        log_file_path (str, optional): Path to save the log file
        
    Returns:
        tuple: (success, file_paths, design_results) where success is bool, 
               file_paths is the file paths object, and design_results is the design results object
    """
    logger = AutomationLogger(log_file_path)
    logger.log("Starting complete automation workflow...")
    logger.log("Phase 1: Build FEM -> Phase 2: SAFE Analysis -> Phase 3: Foundation Design")
    
    # Initialize variables
    file_paths = None
    design_results = None
    
    try:
        # Phase 1: Build FEM
        logger.log("\n" + "="*60)
        logger.log("PHASE 1: BUILD FEM AUTOMATION")
        logger.log("="*60)
        
        success, file_paths = automate_build_fem(
            input_folder, 
            existing_folder, 
            design_strip_option, 
            log_file_path
        )
        
        if not success:
            logger.log("ERROR: Phase 1 (Build FEM) failed. Stopping workflow.")
            return False, None, None
            
        logger.log("Phase 1 (Build FEM) completed successfully!")
        
        # Phase 2: SAFE Analysis
        logger.log("\n" + "="*60)
        logger.log("PHASE 2: SAFE ANALYSIS AND DATABASE GENERATION")
        logger.log("="*60)
        
        # Establish SAFE connection
        logger.log("Establishing SAFE connection...")
        safe_object = setup_safe_connection()
        if not safe_object:
            logger.log("ERROR: Failed to connect to SAFE. Stopping workflow.")
            return False, file_paths, None

        # Get SapModel object
        sap_model_obj = get_sap_model_object(safe_object)

        # Initialize model
        initialize_model(sap_model_obj)
        logger.log("SAFE model initialized successfully.")

        # Setup database path
        dynamic_sqlite_db_path = setup_database_path(config)
        logger.log(f"SQLite database path: {dynamic_sqlite_db_path}")        # Import data, save model, and run analysis
        logger.log("Importing SAFE model and running analysis...")
        model_file = file_paths.ExcelSAFE22Model
        save_file = model_file.replace('.xlsx', '_analyzed.sdb')
        
        db_tables, analyze_obj = import_and_run_analysis(
            sap_model_obj, model_file, save_file)
        logger.log("Data imported, model saved, and analysis run.")

        # Process and save data to SQLite using integrated ref.py workflow
        logger.log("Processing and saving analysis results to SQLite database...")
        success = process_safe_analysis_results(db_tables, dynamic_sqlite_db_path, logger)
        
        if not success:
            logger.log("ERROR: Failed to process SAFE analysis results. Continuing with workflow...")
        else:
            logger.log(f"SQLite database saved to: {dynamic_sqlite_db_path}")
            logger.log("Phase 2 (SAFE Analysis) completed successfully!")
        
        # Phase 3: Foundation Design
        logger.log("\n" + "="*60)
        logger.log("PHASE 3: FOUNDATION DESIGN AUTOMATION")
        logger.log("="*60)
        
        # Use the analyzed SAFE database for foundation design
        safe_result_db = save_file  # The analyzed .sdb file
        excel_master_folder = input_folder  # Use the same input folder
        
        # Check if the result database exists
        if not os.path.exists(safe_result_db):
            logger.log(f"ERROR: SAFE result database not found: {safe_result_db}")
            return False, file_paths, None
            
        logger.log(f"Using SAFE result database: {safe_result_db}")
        logger.log(f"Using Excel master folder: {excel_master_folder}")
        
        # Run foundation design automation
        design_success, design_results = automate_foundation_design(
            safe_result_db,
            excel_master_folder,
            use_tgn53,
            log_file_path
        )
        
        if not design_success:
            logger.log("ERROR: Phase 3 (Foundation Design) failed.")
            return False, file_paths, None
            
        logger.log("Phase 3 (Foundation Design) completed successfully!")
        
        # Workflow completion
        logger.log("\n" + "="*60)
        logger.log("COMPLETE WORKFLOW FINISHED SUCCESSFULLY!")
        logger.log("="*60)
        logger.log("Summary:")
        logger.log("✓ Phase 1: Build FEM - COMPLETED")
        logger.log("✓ Phase 2: SAFE Analysis & SQLite Database - COMPLETED")
        logger.log("✓ Phase 3: Foundation Design - COMPLETED")
        logger.log(f"✓ SAFE model files: {os.path.dirname(file_paths.ExcelSAFE16Model)}")
        logger.log(f"✓ SQLite database: {dynamic_sqlite_db_path}")
        logger.log(f"✓ Foundation design results: {os.path.dirname(file_paths.Log) if file_paths and hasattr(file_paths, 'Log') else 'Check log for details'}")
        
        return True, file_paths, design_results
        
    except Exception as e:
        logger.log(f"\nERROR: Unexpected error during complete workflow: {str(e)}")
        import traceback
        logger.log(traceback.format_exc())
        return False, file_paths, design_results


def process_safe_analysis_results(db_tables, dynamic_sqlite_db_path, logger):
    """
    Complete SAFE analysis results processing workflow.
    This function replicates the data processing logic from ref.py after analysis completion.
    
    Args:
        db_tables: SAFE database tables object
        dynamic_sqlite_db_path (str): Path to SQLite database
        logger: Logger object for logging messages
        
    Returns:
        bool: True if processing completed successfully, False otherwise
    """
    try:
        logger.log("Processing SAFE analysis results...")
        
        # Process Point Object Connectivity
        logger.log("Processing Point Object Connectivity...")
        selected_table = "Point Object Connectivity"
        df_point_object_connectivity = extract_safe22_dataframe(db_tables, selected_table)
        if not df_point_object_connectivity.empty:
            logger.log(f"Point Object Connectivity data shape: {df_point_object_connectivity.shape}")
            logger.log("Saving Point Object Connectivity to database...")
            save_df_to_database(df_point_object_connectivity, selected_table, dynamic_sqlite_db_path)
        else:
            logger.log(f"DataFrame for {selected_table} is empty. Skipping processing.")

        # Process Frame Assignments - Local Axes
        logger.log("Processing Frame Assignments - Local Axes...")
        selected_table = "Frame Assignments - Local Axes"
        df_frame_assigns_local_axes = extract_safe22_dataframe(db_tables, selected_table)
        if not df_frame_assigns_local_axes.empty:
            logger.log(f"Frame Assignments - Local Axes data shape: {df_frame_assigns_local_axes.shape}")
            save_df_to_database(df_frame_assigns_local_axes, selected_table, dynamic_sqlite_db_path)
        else:
            logger.log(f"DataFrame for {selected_table} is empty. Skipping processing.")

        # Set load combinations for display
        logger.log("Setting load combinations for display...")
        ret_load_combo = db_tables.SetLoadCombinationsSelectedForDisplay([])
        logger.log(f"SetLoadCombinationsSelectedForDisplay returned: {ret_load_combo}")

        # Process Joint Displacements
        logger.log("Processing Joint Displacements...")
        selected_table = "Joint Displacements"
        df_joint_displacements = extract_safe22_dataframe(db_tables, selected_table)
        # Rename columns for consistency (from ref.py)
        df_joint_displacements = df_joint_displacements.rename(columns={
            "UniqueName": "Unique Name",
            "OutputCase": "Output Case", 
            "CaseType": "Case Type"
        })
        if not df_joint_displacements.empty:
            logger.log(f"Joint Displacements data shape: {df_joint_displacements.shape}")
            save_df_to_database(df_joint_displacements, selected_table, dynamic_sqlite_db_path)
        else:
            logger.log(f"DataFrame for {selected_table} is empty. Skipping processing.")

        # Process Element Forces - Columns
        logger.log("Processing Element Forces - Columns...")
        selected_table = "Element Forces - Columns"
        df_element_forces_columns = extract_safe22_dataframe(db_tables, selected_table)
        # Rename columns for consistency (from ref.py)
        df_element_forces_columns = df_element_forces_columns.rename(columns={
            "UniqueName": "Unique Name",
            "OutputCase": "Output Case",
            "CaseType": "Case Type",
            "ElemStation": "Elem Station",
        })
        if not df_element_forces_columns.empty:
            logger.log(f"Element Forces - Columns data shape: {df_element_forces_columns.shape}")
            save_df_to_database(df_element_forces_columns, selected_table, dynamic_sqlite_db_path)
        else:
            logger.log(f"DataFrame for {selected_table} is empty. Skipping processing.")
            
        logger.log("SAFE analysis results processing completed successfully!")
        return True
        
    except Exception as e:
        logger.log(f"ERROR: Failed to process SAFE analysis results: {str(e)}")
        import traceback
        logger.log(traceback.format_exc())
        return False


def parse_arguments():
    """Parse command line arguments for the automation workflow."""
    parser = argparse.ArgumentParser(
        description="Automate SAFE model building, analysis, and foundation design",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Complete workflow
  python main.py --complete-workflow --input-folder "C:/path/to/input"
  
  # Build FEM only
  python main.py --build-fem-only --input-folder "C:/path/to/input"
  
  # SAFE analysis only (after build_fem)
  python main.py --run-analysis --input-folder "C:/path/to/input"
  
  # Foundation design only  
  python main.py --foundation-design --safe-result-db "path/to/result.sdb" --excel-master-folder "C:/path/to/excel"
        """
    )
    
    # Input/output arguments
    parser.add_argument('--input-folder', '-i', type=str,
                       help='Path to input folder for build_fem')
    parser.add_argument('--existing-folder', '-e', type=str,
                       help='Path to existing input folder (optional)')
    parser.add_argument('--design-strip', '-d', type=int, choices=[0, 1], default=1,
                       help='Generate design strips: 1=Yes, 0=No (default: 1)')
    parser.add_argument('--log-file', '-l', type=str,
                       help='Path to save log file (optional)')
    parser.add_argument('--use-tgn53', '-t', type=int, choices=[0, 1], default=1,
                       help='Follow GEO TGN 53: 1=Yes, 0=No (default: 1)')
    
    # Workflow selection (mutually exclusive)
    workflow_group = parser.add_mutually_exclusive_group(required=True)
    workflow_group.add_argument('--complete-workflow', '-c', action='store_true',
                               help='Run complete workflow: build_fem -> SAFE analysis -> foundation design')
    workflow_group.add_argument('--build-fem-only', '-b', action='store_true',
                               help='Run build_fem automation only')
    workflow_group.add_argument('--run-analysis', '-a', action='store_true',
                               help='Continue with SAFE API analysis after model generation')
    
    # Foundation design specific arguments
    parser.add_argument('--foundation-design', '-f', action='store_true',
                       help='Run foundation design automation')
    parser.add_argument('--safe-result-db', '-r', type=str,
                       help='Path to SAFE result database (.mdb file)')
    parser.add_argument('--excel-master-folder', '-m', type=str,
                       help='Path to Excel master data folder')
    
    args = parser.parse_args()
    
    # Validation
    if args.foundation_design:
        if not args.safe_result_db or not args.excel_master_folder:
            parser.error("--foundation-design requires --safe-result-db and --excel-master-folder")
    elif args.complete_workflow or args.build_fem_only or args.run_analysis:
        if not args.input_folder:
            parser.error("--input-folder is required for build_fem and analysis workflows")
    
    return args

def main():
    """Main function to run the SAFE automation workflow based on command line arguments."""
    
    # Parse command line arguments
    args = parse_arguments()
    
    # Initialize success flag and results
    success = False
    file_paths = None
    design_results = None
    
    try:
        if args.complete_workflow:
            print("Starting complete SAFE automation workflow...")
            print("Phase 1: Build FEM -> Phase 2: SAFE Analysis -> Phase 3: Foundation Design")
            
            success, file_paths, design_results = automate_complete_workflow(
                args.input_folder,
                args.existing_folder,
                args.design_strip,
                bool(args.use_tgn53),
                args.log_file
            )
            
        elif args.build_fem_only:
            print("Starting Build FEM automation only...")
            
            success, file_paths = automate_build_fem(
                args.input_folder,
                args.existing_folder,
                args.design_strip,
                args.log_file
            )
            
        elif args.run_analysis:
            print("Starting Build FEM + SAFE Analysis...")
            print("Phase 1: Build FEM -> Phase 2: SAFE Analysis")
            
            # Run up to SAFE analysis (without foundation design)
            success, file_paths, _ = automate_complete_workflow(
                args.input_folder,
                args.existing_folder,
                args.design_strip,
                bool(args.use_tgn53),
                args.log_file
            )
            
        elif args.foundation_design:
            print("Starting Foundation Design automation only...")
            
            success, design_results = automate_foundation_design(
                args.safe_result_db,
                args.excel_master_folder,
                bool(args.use_tgn53),
                args.log_file
            )
        
        # Display results
        if success:
            print("\n" + "="*60)
            if args.complete_workflow:
                print("COMPLETE AUTOMATION WORKFLOW FINISHED SUCCESSFULLY!")
                print("="*60)
                print("All phases completed:")
                print("✓ Build FEM: Model generation completed")
                print("✓ SAFE Analysis: Analysis and SQLite database created")  
                print("✓ Foundation Design: Design automation completed")
            elif args.build_fem_only:
                print("BUILD FEM AUTOMATION FINISHED SUCCESSFULLY!")
                print("="*60)
                print("✓ Build FEM: Model generation completed")
            elif args.run_analysis:
                print("BUILD FEM + SAFE ANALYSIS FINISHED SUCCESSFULLY!")
                print("="*60)
                print("✓ Build FEM: Model generation completed")
                print("✓ SAFE Analysis: Analysis and SQLite database created")
            elif args.foundation_design:
                print("FOUNDATION DESIGN AUTOMATION FINISHED SUCCESSFULLY!")
                print("="*60)
                print("✓ Foundation Design: Design automation completed")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("AUTOMATION WORKFLOW FAILED")
            print("="*60)
            print("Please check the logs for detailed error information.")
            print("="*60)
            
    except Exception as e:
        print(f"\nERROR: Unexpected error in main workflow: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    return success
    

if __name__ == "__main__":
    main()
