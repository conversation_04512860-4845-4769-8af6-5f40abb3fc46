import sys
import uuid

import build_fem.write_safe.safe22.safe22_class as _class_safe22
import build_fem.write_safe.safe22.safe22_str_prop
import _main_class
import os

from read.read_steel import read_input_steel
from read.read_geology import read_input_geology
from read.read_property import read_input_property
from read.read_geometry import read_input_geometry
from read.read_loading import read_input_loading

generated_guids = []


def generate_unique_guid():
    while True:
        new_guid = str(uuid.uuid4())
        if new_guid not in generated_guids:
            generated_guids.append(new_guid)
            return new_guid


def _generate_table_data(previous_TableData, titles_list, df_append):
    if previous_TableData:
        TableData = list(previous_TableData)
    else:
        TableData = []

    # Remove all occurrences of the name in TableData
    for idx, row in df_append.iterrows():
        name = row[titles_list[0]]
        while name in TableData:
            index = TableData.index(name)
            del TableData[index:index + len(titles_list)]

    for idx, row in df_append.iterrows():
        for title_tuple in titles_list:
            # Check if the second element of the tuple is 'GUID'
            if len(title_tuple) > 1 and title_tuple[1] == 'GUID':
                value = str(generate_unique_guid())
            elif title_tuple not in row.index:  # Check if the column exists in the DataFrame row
                value = ''
            else:
                value = str(row[title_tuple])
            TableData.append(value)
    return TableData


def tabledata_LoadPatternDefinitions(df_append, previous_TableData=None):
    dataframe_attribute_name = 'LoadPatternDefinitions'
    titles = [
        ('TABLE:  Load Pattern Definitions', 'Name', ''),
        ('TABLE:  Load Pattern Definitions', 'Is Auto Load', ''),
        ('TABLE:  Load Pattern Definitions', 'Type', ''),
        ('TABLE:  Load Pattern Definitions', 'Self Weight Multiplier', ''),
        ('TABLE:  Load Pattern Definitions', 'GUID', '')
    ]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_LoadCasesLinearStatic(df_append, previous_TableData=None):
    dataframe_attribute_name = 'LoadCasesLinearStatic'
    titles = [
        ('TABLE:  Load Case Definitions - Linear Static', 'Name', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Exclude Group', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Initial Condition', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Nonlinear Case', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Load Type', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Load Name', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Load SF', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Trans Accel SF', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Rot Accel SF', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Design Type', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'User Design Type', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'GUID', ''),
        ('TABLE:  Load Case Definitions - Linear Static', 'Notes', '')
    ]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_LoadCombinationDefinitions(df_append, previous_TableData=None):
    dataframe_attribute_name = 'LoadCombinationDefinitions'
    titles = [
        ('TABLE:  Load Combination Definitions', 'Name', ''),
        ('TABLE:  Load Combination Definitions', 'Type', ''),
        ('TABLE:  Load Combination Definitions', 'Is Auto', ''),
        ('TABLE:  Load Combination Definitions', 'Load Name', ''),
        ('TABLE:  Load Combination Definitions', 'Mode', ''),
        ('TABLE:  Load Combination Definitions', 'SF', ''),
        ('TABLE:  Load Combination Definitions', 'GUID', ''),
        ('TABLE:  Load Combination Definitions', 'Notes', '')
    ]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_ConcFrameDesignComboData(df_append, previous_TableData=None):
    dataframe_attribute_name = 'ConcFrameDesignComboData'
    titles = [
        ('TABLE:  Concrete Frame Design Load Combination Data', 'Combo Type', ''),
        ('TABLE:  Concrete Frame Design Load Combination Data', 'Combo Name', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_ConcSlabDesignComboData(df_append, previous_TableData=None):
    dataframe_attribute_name = 'ConcSlabDesignComboData'
    titles = [
        ('TABLE:  Concrete Slab Design Load Combination Data', 'Combo Type', ''),
        ('TABLE:  Concrete Slab Design Load Combination Data', 'Combo Name', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropGeneral(df_append, previous_TableData=None):
    # ['Material', 'Type', 'SymType', 'Grade', 'Color', 'GUID', 'Notes']
    dataframe_attribute_name = 'MatPropGeneral'
    titles = [
        ('TABLE:  Material Properties - General', 'Material', ''),
        ('TABLE:  Material Properties - General', 'Type', ''),
        ('TABLE:  Material Properties - General', 'SymType', ''),
        ('TABLE:  Material Properties - General', 'Grade', ''),
        ('TABLE:  Material Properties - General', 'Color', ''),
        ('TABLE:  Material Properties - General', 'GUID', ''),
        ('TABLE:  Material Properties - General', 'Notes', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropBasicMechProps(df_append, previous_TableData=None):
    # ['Material', 'DensityType', 'UnitWeight', 'UnitMass',
    # 'E1', 'E2', 'E3', 'G12', 'G13', 'G23', 'U12', 'U13', 'U23', 'A1', 'A2', 'A3']
    dataframe_attribute_name = 'MatPropBasicMechProps'
    titles = [
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'Material', ''),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'DensityType', ''),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'UnitWeight', 'kN/m³'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'UnitMass', 'kN-s²/m⁴'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'E1', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'E2', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'E3', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'G12', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'G13', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'G23', 'kN/m²'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'U12', ''),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'U13', ''),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'U23', ''),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'A1', '1/C'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'A2', '1/C'),
        ('TABLE:  Material Properties - Basic Mechanical Properties', 'A3', '1/C')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropSteelData(df_append, previous_TableData=None):
    # ['Material', 'Fy', 'Fu', 'Fye', 'Fue', 'SSCurveOpt', 'SSHysType', 'SHard', 'SMax', 'SRup', 'FinalSlope']
    dataframe_attribute_name = 'MatPropSteelData'
    titles = [
        ('TABLE:  Material Properties - Steel Data', 'Material', ''),
        ('TABLE:  Material Properties - Steel Data', 'Fy', 'kN/m²'),
        ('TABLE:  Material Properties - Steel Data', 'Fu', 'kN/m²'),
        ('TABLE:  Material Properties - Steel Data', 'Fye', 'kN/m²'),
        ('TABLE:  Material Properties - Steel Data', 'Fue', 'kN/m²'),
        ('TABLE:  Material Properties - Steel Data', 'SSCurveOpt', ''),
        ('TABLE:  Material Properties - Steel Data', 'SSHysType', ''),
        ('TABLE:  Material Properties - Steel Data', 'SHard', 'm/m'),
        ('TABLE:  Material Properties - Steel Data', 'SMax', 'm/m'),
        ('TABLE:  Material Properties - Steel Data', 'SRup', 'm/m'),
        ('TABLE:  Material Properties - Steel Data', 'FinalSlope', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropConcreteData(df_append, previous_TableData=None):
    # ['Material', 'Fc', 'LtWtConc', 'LtWtFact', 'IsUserFr', 'UserFr', 'SSCurveOpt', 'SSHysType',
    # 'SFc', 'SCap', 'FinalSlope', 'FAngle', 'DAngle', 'TimeType', 'TimeE', 'EFact', 'TimeCreep',
    # 'CreepFact', 'TimeShrink', 'ShrinkFact', 'CreepType', 'CreepTerms']
    dataframe_attribute_name = 'MatPropConcreteData'
    titles = [
        ('TABLE:  Material Properties - Concrete Data', 'Material', ''),
        ('TABLE:  Material Properties - Concrete Data', 'Fc', 'kN/m²'),
        ('TABLE:  Material Properties - Concrete Data', 'LtWtConc', ''),
        ('TABLE:  Material Properties - Concrete Data', 'LtWtFact', ''),
        ('TABLE:  Material Properties - Concrete Data', 'IsUserFr', ''),
        ('TABLE:  Material Properties - Concrete Data', 'UserFr', 'kN/m²'),
        ('TABLE:  Material Properties - Concrete Data', 'SSCurveOpt', ''),
        ('TABLE:  Material Properties - Concrete Data', 'SSHysType', ''),
        ('TABLE:  Material Properties - Concrete Data', 'SFc', 'm/m'),
        ('TABLE:  Material Properties - Concrete Data', 'SCap', 'm/m'),
        ('TABLE:  Material Properties - Concrete Data', 'FinalSlope', ''),
        ('TABLE:  Material Properties - Concrete Data', 'FAngle', 'deg'),
        ('TABLE:  Material Properties - Concrete Data', 'DAngle', 'deg'),
        ('TABLE:  Material Properties - Concrete Data', 'TimeType', ''),
        ('TABLE:  Material Properties - Concrete Data', 'TimeE', ''),
        ('TABLE:  Material Properties - Concrete Data', 'EFact', ''),
        ('TABLE:  Material Properties - Concrete Data', 'TimeCreep', ''),
        ('TABLE:  Material Properties - Concrete Data', 'CreepFact', ''),
        ('TABLE:  Material Properties - Concrete Data', 'TimeShrink', ''),
        ('TABLE:  Material Properties - Concrete Data', 'ShrinkFact', ''),
        ('TABLE:  Material Properties - Concrete Data', 'CreepType', ''),
        ('TABLE:  Material Properties - Concrete Data', 'CreepTerms', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropRebarData(df_append, previous_TableData=None):
    # ['Material', 'Fy', 'Fu', 'Fye', 'Fue', 'SSCurveOpt', 'SSHysType', 'SHard', 'SCap', 'FinalSlope']
    dataframe_attribute_name = 'MatPropRebarData'
    titles = [
        ('TABLE:  Material Properties - Rebar Data', 'Material', ''),
        ('TABLE:  Material Properties - Rebar Data', 'Fy', 'kN/m²'),
        ('TABLE:  Material Properties - Rebar Data', 'Fu', 'kN/m²'),
        ('TABLE:  Material Properties - Rebar Data', 'Fye', 'kN/m²'),
        ('TABLE:  Material Properties - Rebar Data', 'Fue', 'kN/m²'),
        ('TABLE:  Material Properties - Rebar Data', 'SSCurveOpt', ''),
        ('TABLE:  Material Properties - Rebar Data', 'SSHysType', ''),
        ('TABLE:  Material Properties - Rebar Data', 'SHard', 'm/m'),
        ('TABLE:  Material Properties - Rebar Data', 'SCap', 'm/m'),
        ('TABLE:  Material Properties - Rebar Data', 'FinalSlope', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_MatPropTendonData(df_append, previous_TableData=None):
    # ['Material', 'Fy', 'Fu', 'SSCurveOpt', 'SSHysType', 'FinalSlope']

    dataframe_attribute_name = 'MatPropTendonData'
    titles = [
        ('TABLE:  Material Properties - Tendon Data', 'Material', ''),
        ('TABLE:  Material Properties - Tendon Data', 'Fy', 'kN/m²'),
        ('TABLE:  Material Properties - Tendon Data', 'Fu', 'kN/m²'),
        ('TABLE:  Material Properties - Tendon Data', 'SSCurveOpt', ''),
        ('TABLE:  Material Properties - Tendon Data', 'SSHysType', ''),
        ('TABLE:  Material Properties - Tendon Data', 'FinalSlope', '')]
    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameSecDefConcRect(df_append, previous_TableData=None):
    # ['Name', 'Material', 'FromFile', 'FileName', 'SectInFile', 't3', 't2',
    # 'RigidZone', 'ColumnDropPanel', 'ColumnDropPanelAxis2', 'ColumnDropPanelAxis3',
    # 'ColumnDropPanelSlabProperty',
    # 'NotSizeType', 'NotAutoFact', 'NotUserSize', 'SectionType', 'RebarMatL',
    # 'RebarMatS', 'FlngDimOpt', 'FlngWidth',
    # 'SlabDepth', 'CoverTop', 'CoverBot', 'AMod', 'A2Mod', 'A3Mod', 'JMod',
    # 'I2Mod', 'I3Mod', 'MMod', 'WMod',
    # 'Color', 'GUID', 'Notes']

    # ['Name', 'Material', 'From File?', 'File Name', 'Section in File', 'Depth', 'Width',
    # 'Rigid Zone?', 'Column Drop Panel?', 'Automatic Drop Panel Column 2-Axis', 'Automatic Drop Panel Column 3-Axis',
    # 'Column Drop Panel Slab Property',
    # 'Notional Size Type', 'Notional Auto Factor', 'Notional User Size', 'Section Type', 'Longitudinal Rebar Material',
    # 'Shear Rebar Material', 'Flange Dimension Option', 'Flange Width',
    # 'Slab Depth', 'Cover Top', 'Cover Bottom', 'Area Modifier', 'As2 Modifier', 'As3 Modifier', 'J Modifier',
    # 'I22 Modifier', 'I33 Modifier', 'Mass Modifier', 'Weight Modifier',
    # 'Color', 'GUID', 'Notes']

    dataframe_attribute_name = 'FrameSecDefConcRect'
    titles = [
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Name', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Material', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'From File?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'File Name', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Section in File', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Depth', 'm'),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Width', 'm'),

        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Rigid Zone?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Column Drop Panel?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Automatic Drop Panel Column 2-Axis', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Automatic Drop Panel Column 3-Axis', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Column Drop Panel Slab Property', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Notional Size Type', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Notional Auto Factor', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Notional User Size', 'm'),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Section Type', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Longitudinal Rebar Material', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Shear Rebar Material', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Flange Dimension Option', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Flange Width', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Slab Depth', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Cover Top', 'm'),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Cover Bottom', 'm'),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Area Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'As2 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'As3 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'J Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'I22 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'I33 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Mass Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Weight Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Color', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'GUID', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Notes', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameSecDefConcCircle(df_append, previous_TableData=None):
    # ['Name', 'Material', 'FromFile', 'FileName', 'SectInFile', 't3',
    # 'RigidZone', 'ColumnDropPanel', 'ColumnDropPanelAxis2', 'ColumnDropPanelAxis3',
    # 'ColumnDropPanelSlabProperty', 'NotSizeType', 'NotAutoFact', 'NotUserSize',
    # 'AMod', 'A2Mod', 'A3Mod', 'JMod', 'I2Mod', 'I3Mod',
    # 'MMod', 'WMod', 'Color', 'GUID', 'Notes']

    # ['Name', 'Material', 'From File?', 'File Name', 'Section in File', 'Diameter',
    # 'Rigid Zone?', 'Column Drop Panel?', 'Automatic Drop Panel Column 2-Axis', 'Automatic Drop Panel Column 3-Axis',
    # 'Column Drop Panel Slab Property', 'Notional Size Type', 'Notional Auto Factor', 'Notional User Size',
    # 'Area Modifier', 'As2 Modifier', 'As3 Modifier', 'J Modifier', 'I22 Modifier', 'I33 Modifier',
    # 'Mass Modifier', 'Weight Modifier', 'Color', 'GUID', 'Notes']

    dataframe_attribute_name = 'FrameSecDefConcCircle'
    titles = [
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Name', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Material', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'From File?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'File Name', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Section in File', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Diameter', 'm'),

        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Rigid Zone?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Column Drop Panel?', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Automatic Drop Panel Column 2-Axis', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Automatic Drop Panel Column 3-Axis', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Column Drop Panel Slab Property', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Notional Size Type', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Notional Auto Factor', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Notional User Size', 'm'),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Area Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'As2 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'As3 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'J Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'I22 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'I33 Modifier', ''),

        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Mass Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Weight Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Color', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'GUID', ''),
        ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Notes', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameSecDefSteelI(df_append, previous_TableData=None):
    # ['Name', 'Material', 'FromFile', 'FileName', 'SectInFile',
    # 't3', 't2', 'tf', 'tw',
    # 't2b', 'tfb', 'FilletRad', 'AMod',
    # 'A2Mod', 'A3Mod', 'JMod', 'I2Mod', 'I3Mod',
    # 'MMod', 'WMod', 'Color', 'GUID', 'Notes']

    # ['Name', 'Material', 'From File?', 'File Name', 'Section in File',
    # 'Total Depth', 'Top Flange Width', 'Top Flange Thickness', 'Web Thickness',
    # 'Bottom Flange Width', 'Bottom Flange Thickness', 'Fillet Radius', 'Area Modifier',
    # 'As2 Modifier', 'As3 Modifier', 'J Modifier', 'I22 Modifier', 'I33 Modifier',
    # 'Mass Modifier', 'Weight Modifier', 'Color', 'GUID', 'Notes']

    dataframe_attribute_name = 'FrameSecDefSteelI'
    titles = [
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Name', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Material', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'From File?', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'FileName', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'SectInFile', ''),

        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Total Depth', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Top Flange Width', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Top Flange Thickness', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Web Thickness', 'm'),

        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Bottom Flange Width', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Bottom Flange Thickness', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Fillet Radius', 'm'),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Area Modifier', ''),

        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'As2 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'As3 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'J Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'I22 Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'I33 Modifier', ''),

        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Mass Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Weight Modifier', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Color', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'GUID', ''),
        ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Notes', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_SlabPropertyDefinitions(df_append, previous_TableData=None):
    # ['Name', 'ModelType', 'PropType', 'Material', 'Depth', 'Thickness',
    # 'WidthTop', 'WidthBot', 'RibSpacing1', 'RibSpacing2', 'OneWayLoad',
    # 'NotSizeType', 'NotAutoFact', 'NotUserSize',
    # 'f11Mod', 'f22Mod', 'f12Mod', 'm11Mod', 'm22Mod', 'm12Mod',
    # 'v13Mod', 'v23Mod', 'MMod', 'WMod',
    # 'Color', 'GUID', 'Notes', 'Ortho',
    # 'EffectiveThickness11', 'EffectiveThickness22', 'EffectiveThickness12']

    # ['Name', 'Modeling Type', 'Property Type', 'Material', 'Overall Depth', 'Slab Thickness',
    # 'Rib Width at Top', 'Rib Width at Bottom', 'Rib Spacing 1', 'Rib Spacing 2', 'One Way Load Distribution?',
    # 'Notional Size Type', 'Notional Auto Factor', 'Notional User Size',
    # 'f11 Modifier', 'f22 Modifier', 'f12 Modifier',
    # 'm11 Modifier', 'm22 Modifier', 'm12 Modifier',
    # 'v13 Modifier', 'v23 Modifier', 'Mass Modifier', 'Weight Modifier',
    # 'Color', 'GUID', 'Notes', 'Orthotropic?',
    # 'Effective Thickness-11', 'Effective Thickness-22', 'Effective Thickness-12']

    dataframe_attribute_name = 'SlabPropertyDefinitions'
    titles = [
        ('TABLE:  Slab Property Definitions', 'Name', ''),
        ('TABLE:  Slab Property Definitions', 'Modeling Type', ''),
        ('TABLE:  Slab Property Definitions', 'Property Type', ''),
        ('TABLE:  Slab Property Definitions', 'Material', ''),
        ('TABLE:  Slab Property Definitions', 'Overall Depth', 'm'),
        ('TABLE:  Slab Property Definitions', 'Slab Thickness', 'm'),

        ('TABLE:  Slab Property Definitions', 'Rib Width at Top', 'm'),
        ('TABLE:  Slab Property Definitions', 'Rib Width at Bottom', 'm'),
        ('TABLE:  Slab Property Definitions', 'Rib Spacing 1', 'm'),
        ('TABLE:  Slab Property Definitions', 'Rib Spacing 2', 'm'),
        ('TABLE:  Slab Property Definitions', 'One Way Load Distribution?', ''),

        ('TABLE:  Slab Property Definitions', 'Notional Size Type', ''),
        ('TABLE:  Slab Property Definitions', 'Notional Auto Factor', ''),
        ('TABLE:  Slab Property Definitions', 'Notional User Size', 'm'),

        ('TABLE:  Slab Property Definitions', 'f11 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'f22 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'f12 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'm11 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'm22 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'm12 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'v13 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'v23 Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'Mass Modifier', ''),
        ('TABLE:  Slab Property Definitions', 'Weight Modifier', ''),

        ('TABLE:  Slab Property Definitions', 'Color', ''),
        ('TABLE:  Slab Property Definitions', 'GUID', ''),
        ('TABLE:  Slab Property Definitions', 'Notes', ''),
        ('TABLE:  Slab Property Definitions', 'Orthotropic?', ''),

        ('TABLE:  Slab Property Definitions', 'Effective Thickness-11', 'm'),
        ('TABLE:  Slab Property Definitions', 'Effective Thickness-22', 'm'),
        ('TABLE:  Slab Property Definitions', 'Effective Thickness-12', 'm')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_PointObjectConnectivity(df_append, previous_TableData=None):
    dataframe_attribute_name = 'PointObjectConnectivity'
    titles = [
        ('TABLE:  Point Object Connectivity', 'UniqueName', ''),
        ('TABLE:  Point Object Connectivity', 'Is Auto Point', ''),
        ('TABLE:  Point Object Connectivity', 'IsSpecial', ''),
        ('TABLE:  Point Object Connectivity', 'X', 'm'),
        ('TABLE:  Point Object Connectivity', 'Y', 'm'),
        ('TABLE:  Point Object Connectivity', 'Z', 'm'),
        ('TABLE:  Point Object Connectivity', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_BeamObjectConnectivity(df_append, previous_TableData=None):
    # ['UniqueName', 'UniquePtI', 'UniquePtJ', 'GUID']
    # ['Unique Name', 'Curve Type', 'UniquePtI', 'UniquePtJ', 'Length', 'GUID']
    dataframe_attribute_name = 'BeamObjectConnectivity'
    titles = [
        ('TABLE:  Beam Object Connectivity', 'Unique Name', ''),
        ('TABLE:  Beam Object Connectivity', 'UniquePtI', ''),
        ('TABLE:  Beam Object Connectivity', 'UniquePtJ', ''),
        ('TABLE:  Beam Object Connectivity', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameAssignsEndLenOffsets(df_append, previous_TableData=None):
    # ['UniqueName', 'OffsetOpt', 'OffsetI', 'OffsetJ', 'RigidFact', 'SelfWtOpt']

    dataframe_attribute_name = 'FrameAssignsEndLenOffsets'
    titles = [
        ('TABLE:  Frame Assignments - End Length Offsets', 'UniqueName', ''),
        ('TABLE:  Frame Assignments - End Length Offsets', 'Offset Option', ''),
        ('TABLE:  Frame Assignments - End Length Offsets', 'Offset I', 'm'),
        ('TABLE:  Frame Assignments - End Length Offsets', 'Offset J', 'm'),
        ('TABLE:  Frame Assignments - End Length Offsets', 'Rigid Factor', ''),
        ('TABLE:  Frame Assignments - End Length Offsets', 'Self Weight Option', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameAssignsFrameAutoMesh(df_append, previous_TableData=None):
    # ['UniqueName', 'AutoMesh', 'AtIntJoints', 'AtIntersect',
    # 'MinNumber', 'NumSegments', 'MaxLength', 'SegLength']
    dataframe_attribute_name = 'FrameAssignsFrameAutoMesh'
    titles = [
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'UniqueName', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Auto Mesh', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'At Intermediate Joints', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'At Intersections', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Min Number?', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Number Segments', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Max Length?', ''),
        ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Segment Length', 'm')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameAssignsOutputStations(df_append, previous_TableData=None):
    # ['UniqueName', 'StationOpt', 'MaxSpacing', 'MinStations']
    # ['UniqueName', 'Station Option', 'Max Spacing', 'Min Stations']

    dataframe_attribute_name = 'FrameAssignsOutputStations'
    titles = [
        ('TABLE:  Frame Assignments - Output Stations', 'UniqueName', ''),
        ('TABLE:  Frame Assignments - Output Stations', 'Station Option', ''),
        ('TABLE:  Frame Assignments - Output Stations', 'Max Spacing', ''),
        ('TABLE:  Frame Assignments - Output Stations', 'Min Stations', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameAssignsSectProp(df_append, previous_TableData=None):
    # ['UniqueName', 'AutoSelect', 'SectProp']

    dataframe_attribute_name = 'FrameAssignsSectProp'
    titles = [
        ('TABLE:  Frame Assignments - Section Properties', 'UniqueName', ''),
        ('TABLE:  Frame Assignments - Section Properties', 'Auto Select List', ''),
        ('TABLE:  Frame Assignments - Section Properties', 'Section Property', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameAssignsInsertionPoint(df_append, previous_TableData=None):
    # ['UniqueName', 'CardinalPt', 'Mirror2', 'Mirror3',
    # 'OffsetCSys', 'XI', 'YI', 'ZI', 'XJ', 'YJ', 'ZJ', 'NoTransform']

    # ['UniqueName', 'Cardinal Point', 'Mirror2', 'Mirror3',
    # 'Offset CSys', 'XI', 'YI', 'ZI', 'XJ', 'YJ', 'ZJ', 'No Transform Stiffness']

    dataframe_attribute_name = 'FrameAssignsInsertionPoint'
    titles = [
        ('TABLE:  Frame Assignments - Insertion Point', 'UniqueName', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'Cardinal Point', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'Mirror2', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'Mirror3', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'Offset CSys', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'XI', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'YI', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'ZI', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'XJ', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'YJ', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'ZJ', ''),
        ('TABLE:  Frame Assignments - Insertion Point', 'No Transform Stiffness', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_NullAreaObjectConnectivity(df_append, previous_TableData=None):
    # ['UniqueName', 'UniquePt1', 'UniquePt2', 'UniquePt3', 'UniquePt4', 'GUID']

    dataframe_attribute_name = 'NullAreaObjectConnectivity'
    titles = [
        ('TABLE:  Null Area Object Connectivity', 'Unique Name', ''),
        ('TABLE:  Null Area Object Connectivity', 'UniquePt1', ''),
        ('TABLE:  Null Area Object Connectivity', 'UniquePt2', ''),
        ('TABLE:  Null Area Object Connectivity', 'UniquePt3', ''),
        ('TABLE:  Null Area Object Connectivity', 'UniquePt4', ''),
        ('TABLE:  Null Area Object Connectivity', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_AreaAssignsInsertionPoint(df_append, previous_TableData=None):
    # ['UniqueName', 'Cardinal Point', 'Coordinate System', 'Point Number', 'Point Unique Name', 'Offset1', 'Offset2', 'Offset3', 'Transform']
    # ['UniqueName', 'CardinalPt', 'CoordSys', 'PointNumber', 'Offset1', 'Offset2', 'Offset3', 'Transform']
    # ['', '', '', '', '', 'm', 'm', 'm', '']

    dataframe_attribute_name = 'AreaAssignsInsertionPoint'
    titles = [
        ('TABLE:  Area Assignments - Insertion Point', 'UniqueName', ''),
        ('TABLE:  Area Assignments - Insertion Point', 'Cardinal Point', ''),
        ('TABLE:  Area Assignments - Insertion Point', 'Coordinate System', ''),
        ('TABLE:  Area Assignments - Insertion Point', 'Point Number', ''),
        ('TABLE:  Area Assignments - Insertion Point', 'Offset1', 'm'),
        ('TABLE:  Area Assignments - Insertion Point', 'Offset2', 'm'),
        ('TABLE:  Area Assignments - Insertion Point', 'Offset3', 'm'),
        ('TABLE:  Area Assignments - Insertion Point', 'Transform', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_AreaAssignsSectProp(df_append, previous_TableData=None):
    # ['UniqueName', 'Section Property', 'Property Type']
    # ['', '', '']
    # ['UniqueName', 'SectProp', 'PropType']
    dataframe_attribute_name = 'AreaAssignsSectProp'
    titles = [
        ('TABLE:  Area Assignments - Section Properties', 'UniqueName', ''),
        ('TABLE:  Area Assignments - Section Properties', 'Section Property', ''),
        ('TABLE:  Area Assignments - Section Properties', 'Property Type', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_NullLineObjectConnectivity(df_append, previous_TableData=None):
    # ['Unique Name', 'UniquePtI', 'UniquePtJ', 'Length', 'GUID']
    # ['', '', '', 'm', '']
    # ['UniqueName', 'UniquePtI', 'UniquePtJ', 'GUID']

    dataframe_attribute_name = 'NullLineObjectConnectivity'
    titles = [
        ('TABLE:  Null Line Object Connectivity', 'Unique Name', ''),
        ('TABLE:  Null Line Object Connectivity', 'UniquePtI', ''),
        ('TABLE:  Null Line Object Connectivity', 'UniquePtJ', ''),
        ('TABLE:  Null Line Object Connectivity', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FloorObjectConnectivity(df_append, previous_TableData=None):
    # ['UniqueName', 'UniquePt1', 'UniquePt2', 'UniquePt3', 'UniquePt4', 'GUID']

    dataframe_attribute_name = 'FloorObjectConnectivity'
    titles = [
        ('TABLE:  Floor Object Connectivity', 'Unique Name', ''),
        ('TABLE:  Floor Object Connectivity', 'UniquePt1', ''),
        ('TABLE:  Floor Object Connectivity', 'UniquePt2', ''),
        ('TABLE:  Floor Object Connectivity', 'UniquePt3', ''),
        ('TABLE:  Floor Object Connectivity', 'UniquePt4', ''),
        ('TABLE:  Floor Object Connectivity', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_AreaAssignsEdgeConstraints(df_append, previous_TableData=None):
    # ['UniqueName', 'Constraint']
    dataframe_attribute_name = 'AreaAssignsEdgeConstraints'
    titles = [
        ('TABLE:  Area Assignments - Auto Edge Constraints', 'UniqueName', ''),
        ('TABLE:  Area Assignments - Auto Edge Constraints', 'Constraint', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_AreaAssignsFloorAutoMesh(df_append, previous_TableData=None):
    # ['UniqueName', 'Mesh Option', 'N1', 'N2', 'At Beams', 'At Walls', 'At Grids', 'Submesh', 'Submesh Size', 'Add Restraints']
    # ['', '', '', '', '', '', '', '', 'm', '']
    # ['UniqueName', 'MeshOption', 'N1', 'N2', 'AtBeams', 'AtWalls', 'AtGrids', 'Submesh', 'SubmeshSize', 'Restraints']
    dataframe_attribute_name = 'AreaAssignsFloorAutoMesh'
    titles = [
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'UniqueName', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Mesh Option', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'N1', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'N2', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'At Beams', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'At Walls', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'At Grids', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Submesh', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Submesh Size', ''),
        ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Add Restraints', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_ConcSlbOverFEBased(df_append, previous_TableData=None):
    # ['Unique Name', 'Rebar Material', 'Cover Specification Type', 'Dir 1 Top Cover', 'Dir 1 Bottom Cover',
    #  'Dir 2 Top Cover', 'Dir 2 Bottom Cover', 'LLRF', 'Design?', 'Ignote PT?']
    # ['', '', '', 'm', 'm', 'm', 'm', '', '', '']
    # ['UniqueName', 'RebarMat', 'CoverType', 'Dir1TopCov', 'Dir1BotCov', 'Dir2TopCov', 'Dir2BotCov', 'LLRF', 'DoDesign', 'IgnorePT']

    dataframe_attribute_name = 'ConcSlbOverFEBased'
    titles = [
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Unique Name', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Rebar Material', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Cover Specification Type', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Dir 1 Top Cover', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Dir 1 Bottom Cover', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Dir 2 Top Cover', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Dir 2 Bottom Cover', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'LLRF', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Design?', ''),
        ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Ignote PT?', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_GroupDefinitions(df_append, previous_TableData=None):
    dataframe_attribute_name = 'GroupDefinitions'
    titles = [
        ('TABLE:  Group Definitions', 'Name', ''),
        ('TABLE:  Group Definitions', 'Color', ''),
        ('TABLE:  Group Definitions', 'Steel Design?', ''),
        ('TABLE:  Group Definitions', 'Concrete Design?', ''),
        ('TABLE:  Group Definitions', 'Composite Design?', ''),
        ('TABLE:  Group Definitions', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_GroupAssignments(df_append, previous_TableData=None):
    dataframe_attribute_name = 'GroupAssignments'
    titles = [
        ('TABLE:  Group Assignments', 'Group Name', ''),
        ('TABLE:  Group Assignments', 'Object Type', ''),
        ('TABLE:  Group Assignments', 'Object Unique Name', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_SpringPropsPointSprings(df_append, previous_TableData=None):
    dataframe_attribute_name = 'SpringPropsPointSprings'
    titles = [
        ('TABLE:  Spring Property Definitions - Point Springs', 'Name', ''),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness UX', 'kN/m'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness UY', 'kN/m'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness UZ', 'kN/m'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness RX', 'kN-m/rad'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness RY', 'kN-m/rad'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Stiffness RZ', 'kN-m/rad'),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Nonlinearity Specification', ''),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Nonlinear Option', ''),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Color', ''),
        ('TABLE:  Spring Property Definitions - Point Springs', 'GUID', ''),
        ('TABLE:  Spring Property Definitions - Point Springs', 'Notes', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_JointAssignsSprings(df_append, previous_TableData=None):
    dataframe_attribute_name = 'JointAssignsSprings'
    titles = [
        ('TABLE:  Joint Assignments - Springs', 'UniqueName', ''),
        ('TABLE:  Joint Assignments - Springs', 'SpringProp', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=None):
    # ['UniqueName', 'IncludeInMesh']
    dataframe_attribute_name = 'JointAssignsFloorMeshOpt'
    titles = [
        ('TABLE:  Joint Assignments - Floor Meshing Option', 'UniqueName', ''),
        ('TABLE:  Joint Assignments - Floor Meshing Option', 'IncludeInMesh', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_JointLoadsForce(df_append, previous_TableData=None):
    # ['UniqueName', 'LoadPattern', 'FX', 'FY', 'FZ', 'MX', 'MY', 'MZ', 'XDim', 'YDim', 'GUID']
    dataframe_attribute_name = 'JointLoadsForce'
    titles = [
        ('TABLE:  Joint Loads Assignments - Force', 'UniqueName', ''),
        ('TABLE:  Joint Loads Assignments - Force', 'Load Pattern', ''),
        ('TABLE:  Joint Loads Assignments - Force', 'FX', 'kN'),
        ('TABLE:  Joint Loads Assignments - Force', 'FY', 'kN'),
        ('TABLE:  Joint Loads Assignments - Force', 'FZ', 'kN'),
        ('TABLE:  Joint Loads Assignments - Force', 'MX', 'kN-m'),
        ('TABLE:  Joint Loads Assignments - Force', 'MY', 'kN-m'),
        ('TABLE:  Joint Loads Assignments - Force', 'MZ', 'kN-m'),
        ('TABLE:  Joint Loads Assignments - Force', 'X Dimension', 'm'),
        ('TABLE:  Joint Loads Assignments - Force', 'Y Dimension', 'm'),
        ('TABLE:  Joint Loads Assignments - Force', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_FrameLoadsDistributed(df_append, previous_TableData=None):
    dataframe_attribute_name = 'FrameLoadsDistributed'
    titles = [
        ('TABLE:  Frame Loads Assignments - Distributed', 'UniqueName', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Load Pattern', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Load Type', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Direction', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Distance Type', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Relative Distance A', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Relative Distance B', ''),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Absolute Distance A', 'm'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Absolute Distance B', 'm'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Force A', 'kN/m'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Force B', 'kN/m'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Moment A', 'kN-m/m'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'Moment B', 'kN-m/m'),
        ('TABLE:  Frame Loads Assignments - Distributed', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def tabledata_AreaLoadsUniform(df_append, previous_TableData=None):
    dataframe_attribute_name = 'AreaLoadsUniform'
    titles = [
        ('TABLE:  Area Load Assignments - Uniform', 'UniqueName', ''),
        ('TABLE:  Area Load Assignments - Uniform', 'Load Pattern', ''),
        ('TABLE:  Area Load Assignments - Uniform', 'Direction', ''),
        ('TABLE:  Area Load Assignments - Uniform', 'Load', 'kN/m²'),
        ('TABLE:  Area Load Assignments - Uniform', 'GUID', '')]

    return _generate_table_data(previous_TableData, titles, df_append)


def read_from_safe22(SelectedTable="Load Pattern Definitions"):
    print('')
    print(SelectedTable)
    # Initialize variables for table fields and data
    TableVersion = NumberFields = NumberRecords = TableLength = 0
    FieldKey = FieldName = Description = UnitsString = IsImportable = []
    FieldsKeysIncluded = []
    GroupName = ImportLog = ""
    IsRowWise = FillImportLog = True
    NumFatalErrors = NumErrorsMsgs = NumWarnMsgs = NumInfoMsgs = 0
    TableData = []

    [ret, TableVersion, NumberFields, FieldKey, FieldName, Description, UnitsString,
     IsImportable] = DatabaseTables.GetAllFieldsInTable(SelectedTable, TableVersion, NumberFields, FieldKey, FieldName,
                                                        Description, UnitsString, IsImportable)
    # print(TableVersion)
    # print(NumberFields)
    print(list(FieldKey))
    print(list(FieldName))
    print(list(UnitsString))

    [ret, TableVersion, FieldsKeysIncluded, NumberRecords, TableData] = DatabaseTables.GetTableForEditingArray(
        SelectedTable, GroupName, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

    # print(TableVersion)
    print(list(FieldsKeysIncluded))
    # print(NumberRecords)
    # print(list(TableData))
    return SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData


def write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData):
    # Table data array
    FillImportLog = True
    NumFatalErrors = 0
    NumErrorsMsgs = 0
    NumWarnMsgs = 0
    NumInfoMsgs = 0
    ImportLog = ""
    TableLength = 0

    ret = DatabaseTables.SetTableForEditingArray(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords,
                                                 TableData)

    [ret, NumFatalErrors, NumErrorsMsgs, NumWarnMsgs, NumInfoMsgs, ImportLog] = DatabaseTables.ApplyEditedTables(
        FillImportLog, NumFatalErrors, NumErrorsMsgs, NumWarnMsgs, NumInfoMsgs, ImportLog)
    print([ret, NumFatalErrors, NumErrorsMsgs, NumWarnMsgs, NumInfoMsgs, ImportLog])


file_paths = _main_class.FilePaths()

base_path = r"C:\Users\<USER>\Desktop\DHP"
# base_path = r"C:\Users\<USER>\Desktop\DHP"

file_paths.ExcelProperty = base_path + r'\A.SAFEInput_Property.xlsx'
file_paths.ExcelGeometry = base_path + r'\A.SAFEInput_Geometry.xlsx'
file_paths.ExcelGeology = base_path + r'\A.SAFEInput_Geology.xlsx'
file_paths.ExcelLoading = base_path + r'\A.SAFEInput_Loading.xlsx'

excel_inputs = _main_class.ExcelInputs()

if os.path.exists(file_paths.ExcelProperty):
    excel_inputs = read_input_property(excel_inputs, file_paths)
if os.path.exists(file_paths.ExcelGeometry):
    excel_inputs = read_input_geometry(excel_inputs, file_paths)
if os.path.exists(file_paths.ExcelGeology):
    excel_inputs = read_input_geology(excel_inputs, file_paths)
if os.path.exists(file_paths.ExcelLoading):
    excel_inputs = read_input_loading(excel_inputs, file_paths)
if os.path.exists(file_paths.DataSteel):
    excel_inputs = read_input_steel(excel_inputs, file_paths)

# set the following flag to true to select the .NET Core run-time environment,
# false to select .NET Framework run-time environment.
UseNETCore = False  # True #

if UseNETCore:
    from pythonnet import load

    load("coreclr")
    import clr

else:
    import clr

    clr.AddReference("System.Runtime.InteropServices")

# set the following path to the installed SAFE program directory
clr.AddReference(R'C:\Program Files\Computers and Structures\SAFE 22\SAFEv1.dll')
from SAFEv1 import *

# set the following flag to True to execute on a remote computer
Remote = False

# if the above flag is True, set the following variable to the hostname of the remote computer
# remember that the remote computer must have SAFE installed and be running the CSiAPIService.exe
RemoteComputer = "SpareComputer-DT"

# set the following flag to True to attach to an existing instance of the program
# otherwise a new instance of the program will be started
AttachToInstance = False

# set the following flag to True to manually specify the path to SAFE.exe
# this allows for a connection to a version of SAFE other than the latest installation
# otherwise the latest installed version of SAFE will be launched
SpecifyPath = False

# if the above flag is set to True, specify the path to SAFE below
ProgramPath = R"C:\Program Files\Computers and Structures\SAFE 22\SAFE.exe"

# full path to the model
# set it to the desired path of your model
# APIPath = R'C:\CSi_SAFE_API_Example'
# if not os.path.exists(APIPath):
#     try:
#         os.makedirs(APIPath)
#     except OSError:
#         pass
# ModelPath = APIPath + os.sep + 'API_1-002.fdb'

# create API helper object
helper = cHelper(Helper())

if AttachToInstance:
    # attach to a running instance of SAFE
    try:
        # get the active object
        # 'SAFE uses API infrastructure from ETABS; as a consequence, the main connection object is called the ETABSObject
        if Remote:
            mySAFEObject = cOAPI(helper.GetObjectHost(RemoteComputer, "CSI.SAFE.API.ETABSObject"))
        else:
            mySAFEObject = cOAPI(helper.GetObject("CSI.SAFE.API.ETABSObject"))
    except:
        print("No running instance of the program found or failed to attach.")
        sys.exit(-1)

else:
    if SpecifyPath:
        try:
            # 'create an instance of the object from the specified path
            if Remote:
                mySAFEObject = cOAPI(helper.CreateObjectHost(RemoteComputer, ProgramPath))
            else:
                mySAFEObject = cOAPI(helper.CreateObject(ProgramPath))
        except:
            print("Cannot start a new instance of the program from " + ProgramPath)
            sys.exit(-1)
    else:

        try:
            # create an instance of the object from the latest installed SAFE
            # 'SAFE uses API infrastructure from ETABS; as a consequence, the main connection object is called the ETABSObject
            if Remote:
                mySAFEObject = cOAPI(helper.CreateObjectProgIDHost(RemoteComputer, "CSI.SAFE.API.ETABSObject"))
            else:
                mySAFEObject = cOAPI(helper.CreateObjectProgID("CSI.SAFE.API.ETABSObject"))

        except:
            print("Cannot start a new instance of the program.")
            sys.exit(-1)

    # start SAFE application
    app_start_ret = mySAFEObject.ApplicationStart()
    print(f"mySAFEObject.ApplicationStart() returned: {app_start_ret}")

SapModel = cSapModel(mySAFEObject.SapModel)

# initialize model
ret = SapModel.InitializeNewModel(eUnits.kN_m_C)

ret = SapModel.File.NewBlank()

ret = SapModel.SetPresentUnits(eUnits.kN_m_C)

DatabaseTables = cDatabaseTables(SapModel.DatabaseTables)

safe22_dfs = _class_safe22.Safe22DataFrames()

# load pattern
safe22_dfs, df_append = build_fem.write_load_comb_safe22.load_pattern_LoadPatternDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Load Pattern Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_LoadPatternDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# load case
safe22_dfs, df_append = build_fem.write_load_comb_safe22.load_case_LoadCasesLinearStatic_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Load Case Definitions - Linear Static"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_LoadCasesLinearStatic(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# load combination
safe22_dfs, df_append = build_fem.write_load_comb_safe22.load_comb_LoadCombinationDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Load Combination Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_LoadCombinationDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_load_comb_safe22.load_comb_ConcFrameDesignComboData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Concrete Frame Design Load Combination Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_ConcFrameDesignComboData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_load_comb_safe22.load_comb_ConcSlabDesignComboData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Concrete Slab Design Load Combination Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_ConcSlabDesignComboData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# material
safe22_dfs, df_append = build_fem.write_material_safe22.material_MatPropGeneral_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - General"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropGeneral(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_material_safe22.material_MatPropBasicMechProps_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - Basic Mechanical Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropBasicMechProps(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_material_safe22.materials_MatPropSteelData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - Steel Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropSteelData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_material_safe22.material_MatPropConcreteData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - Concrete Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropConcreteData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_material_safe22.material_MatPropRebarData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - Rebar Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropRebarData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_material_safe22.material_MatPropTendonData_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Material Properties - Tendon Data"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_MatPropTendonData(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam property
safe22_dfs, df_append = build_fem.write_str_prop_safe22.beam_FrameSecDefConcRect_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Section Property Definitions - Concrete Rectangular"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameSecDefConcRect(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# bp property
safe22_dfs, df_append = build_fem.write_str_prop_safe22.bp_FrameSecDefConcCircle_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Section Property Definitions - Concrete Circle"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameSecDefConcCircle(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# h pile property
safe22_dfs, df_append = build_fem.write_str_prop_safe22.hp_FrameSecDefSteelI_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Section Property Definitions - Steel I/Wide Flange"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameSecDefSteelI(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab property
safe22_dfs, df_append = build_fem.write_str_prop_safe22.slab_SlabPropertyDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Slab Property Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_SlabPropertyDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# points
safe22_dfs, df_append = build_fem.write_geometry_safe22.point_PointObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Point Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_PointObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam geometry
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_BeamObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Beam Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_BeamObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam offset
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - End Length Offsets"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsEndLenOffsets(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam mesh
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Frame Auto Mesh Options"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsFrameAutoMesh(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam stations
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Output Stations"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsOutputStations(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam insertion point
safe22_dfs, df_append = build_fem.write_geometry_safe22.beam_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Insertion Point"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsInsertionPoint(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column area assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.column_NullAreaObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Null Area Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_NullAreaObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column area insertion point
safe22_dfs, df_append = build_fem.write_geometry_safe22.column_AreaAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Insertion Point"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsInsertionPoint(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column area property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.column_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall geometry
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_NullLineObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Null Line Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_NullLineObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall offset
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - End Length Offsets"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsEndLenOffsets(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall mesh
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Frame Auto Mesh Options"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsFrameAutoMesh(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall insertion point
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Insertion Point"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsInsertionPoint(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall stations
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Output Stations"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsOutputStations(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall section property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.wall_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load geometry assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_NullLineObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Null Line Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_NullLineObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load offset
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - End Length Offsets"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsEndLenOffsets(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load mesh
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Frame Auto Mesh Options"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsFrameAutoMesh(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load insertion point
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Insertion Point"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsInsertionPoint(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load stations
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Output Stations"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsOutputStations(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.line_load_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# lkp geometry assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.lkp_NullAreaObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Null Area Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_NullAreaObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# lkp area section property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.lkp_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab geometry assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_FloorObjectConnectivity_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Floor Object Connectivity"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FloorObjectConnectivity(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab area section property assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Section Properties"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsSectProp(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab area insertion point
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_AreaAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Insertion Point"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsInsertionPoint(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab area edge constraint
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_AreaAssignsEdgeConstraints_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Auto Edge Constraints"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsEdgeConstraints(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab area mesh
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_AreaAssignsFloorAutoMesh_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Assignments - Floor Auto Mesh Options"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaAssignsFloorAutoMesh(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab area Concrete Slab Design Overwrites - Finite Element Based
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_ConcSlbOverFEBased_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Concrete Slab Design Overwrites - Finite Element Based"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_ConcSlbOverFEBased(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab group define
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab group assign
safe22_dfs, df_append = build_fem.write_geometry_safe22.slab_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# point load
safe22_dfs, df_append = build_fem.write_load_safe22.point_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Loads Assignments - Force"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointLoadsForce(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# point load mesh
safe22_dfs, df_append = build_fem.write_load_safe22.point_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Assignments - Floor Meshing Option"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# point load group define
safe22_dfs, df_append = build_fem.write_load_safe22.point_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# point load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.point_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load
safe22_dfs, df_append = build_fem.write_load_safe22.line_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Loads Assignments - Distributed"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameLoadsDistributed(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load mesh
safe22_dfs, df_append = build_fem.write_load_safe22.line_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Assignments - Floor Meshing Option"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load group define
safe22_dfs, df_append = build_fem.write_load_safe22.line_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# line load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.line_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# slab load
safe22_dfs, df_append = build_fem.write_load_safe22.slab_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Load Assignments - Uniform"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaLoadsUniform(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# lkp load
safe22_dfs, df_append = build_fem.write_load_safe22.lkp_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Load Assignments - Uniform"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaLoadsUniform(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam load
safe22_dfs, df_append = build_fem.write_load_safe22.beam_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Loads Assignments - Distributed"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameLoadsDistributed(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam load mesh
safe22_dfs, df_append = build_fem.write_load_safe22.beam_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Assignments - Floor Meshing Option"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam load group define
safe22_dfs, df_append = build_fem.write_load_safe22.beam_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# beam load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.beam_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column point load
safe22_dfs, df_append = build_fem.write_load_safe22.column_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Loads Assignments - Force"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointLoadsForce(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column point load mesh
safe22_dfs, df_append = build_fem.write_load_safe22.column_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Assignments - Floor Meshing Option"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column point load group define
safe22_dfs, df_append = build_fem.write_load_safe22.column_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column point load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.column_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# column area load
safe22_dfs, df_append = build_fem.write_load_safe22.column_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Area Load Assignments - Uniform"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_AreaLoadsUniform(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall load
safe22_dfs, df_append = build_fem.write_load_safe22.wall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Loads Assignments - Distributed"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameLoadsDistributed(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall load mesh
safe22_dfs, df_append = build_fem.write_load_safe22.wall_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Assignments - Floor Meshing Option"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointAssignsFloorMeshOpt(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall load group define
safe22_dfs, df_append = build_fem.write_load_safe22.wall_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# wall load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.wall_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# core wall load
safe22_dfs, df_append = build_fem.write_load_safe22.corewall_load_FrameLoadsDistributed_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Frame Loads Assignments - Distributed"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_FrameLoadsDistributed(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_load_safe22.corewall_group_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

safe22_dfs, df_append = build_fem.write_load_safe22.corewall_group_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# pile load
safe22_dfs, df_append = build_fem.write_load_safe22.pile_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Joint Loads Assignments - Force"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_JointLoadsForce(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# pile load group define
safe22_dfs, df_append = build_fem.write_load_safe22.pile_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Definitions"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupDefinitions(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

# pile load group assign
safe22_dfs, df_append = build_fem.write_load_safe22.pile_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
SelectedTable = "Group Assignments"
SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData = read_from_safe22(SelectedTable)
TableData = tabledata_GroupAssignments(df_append, previous_TableData=TableData)
write_to_safe22(SelectedTable, TableVersion, FieldsKeysIncluded, NumberRecords, TableData)

print('done')
