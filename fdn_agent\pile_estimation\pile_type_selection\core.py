﻿"""
Pile Type Selection Core Module

This module contains the main classes for pile type pre-selection functionality.
It provides the core engines for pile type selection and integrated estimation.

Author: Foundation Automation System
Date: June 14, 2025
"""

from typing import List, Dict, Any, Optional, Callable
import logging

from .pile_type_preselection import PileTypePreselector
from .utils import PileTypeSelectionUtils
from ..data_types.pile_preselection_types import (
    PileTypePreselectionCriteria,
    PileTypeCandidate,
    PileTypePreselectionResult
)
from ..data_types import Point2D, GroupElements, LocalCoordinateSystem
from ..exceptions import PileTypeSelectionError
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)


class IntegratedPileEstimationEngine:
    """
    Integrated Pile Estimation Engine with Pre-Selection
    
    Combines pile type pre-selection with NSGA-III optimization for complete
    pile layout generation workflow.
    """
    
    def __init__(self, log_callback: Optional[Callable] = None):
        """
        Initialize the integrated estimation engine.

        Args:
            log_callback: Optional callback function for logging
        """
        log_function_entry(log_callback, "IntegratedPileEstimationEngine.__init__")

        enhanced_log(log_callback, "Initializing Integrated Pile Estimation Engine", 'INFO')

        self.log_callback = log_callback or (lambda msg: logging.info(msg))

        try:
            enhanced_log(log_callback, "Creating PileTypePreselector instance", 'DEBUG')
            self.preselector = PileTypePreselector(self.log_callback)
            log_validation_result(log_callback, "PileTypePreselector Creation", True, "Preselector initialized successfully")

            enhanced_log(log_callback, "Creating PileTypeSelectionUtils instance", 'DEBUG')
            self.utils = PileTypeSelectionUtils(self.log_callback)
            log_validation_result(log_callback, "PileTypeSelectionUtils Creation", True, "Utils initialized successfully")

            enhanced_log(log_callback, "Integrated Pile Estimation Engine initialized successfully", 'INFO')

        except Exception as e:
            error_msg = f"Failed to initialize Integrated Pile Estimation Engine: {e}"
            log_error_with_context(log_callback, e, "IntegratedPileEstimationEngine initialization")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise

        log_function_exit(log_callback, "IntegratedPileEstimationEngine.__init__", "Initialization completed")
    
    def estimate_pile_layout_with_preselection(self,
                                             group_elements: GroupElements,
                                             required_load: float,
                                             excel_inputs: Any,
                                             selected_pile_types: List[Dict[str, Any]],
                                             user_edge_distance: float = 0.4,
                                             min_spacing: float = 1.5,
                                             pile_diameter: float = 0.6,
                                             site_boundary: Optional[Any] = None,
                                             config_overrides: Optional[Dict[str, Any]] = None,
                                             create_preselection_dxf: bool = True,
                                             output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete pile layout estimation with pile type pre-selection and NSGA-III optimization.

        Args:
            group_elements: Structural elements (columns/walls) for the group
            required_load: Required total load capacity in kN
            excel_inputs: Excel input data
            selected_pile_types: Available pile types from GUI
            user_edge_distance: User-defined edge distance from GUI
            min_spacing: Minimum pile spacing
            pile_diameter: Pile diameter
            site_boundary: Optional site boundary constraint
            config_overrides: Optional optimization configuration overrides
            create_preselection_dxf: Whether to create preselection DXF visualization
            output_dir: Output directory for DXF files

        Returns:
            Dictionary containing complete estimation results
        """
        log_function_entry(self.log_callback, "estimate_pile_layout_with_preselection",
                          num_elements=len(group_elements.get('columns', [])) + len(group_elements.get('walls', [])),
                          required_load=required_load, num_pile_types=len(selected_pile_types),
                          user_edge_distance=user_edge_distance, min_spacing=min_spacing,
                          pile_diameter=pile_diameter, has_site_boundary=site_boundary is not None,
                          create_dxf=create_preselection_dxf)

        with create_timed_logger(self.log_callback, "integrated_pile_estimation"):
            enhanced_log(self.log_callback, "Starting integrated pile estimation with pre-selection", 'INFO')

            # Log input validation
            log_validation_result(self.log_callback, "Required Load Positive", required_load > 0,
                                f"Required load: {required_load} kN")
            log_validation_result(self.log_callback, "Pile Types Available", len(selected_pile_types) > 0,
                                f"{len(selected_pile_types)} pile types provided")
            log_validation_result(self.log_callback, "Group Elements Present",
                                len(group_elements.get('columns', [])) + len(group_elements.get('walls', [])) > 0,
                                f"Group has {len(group_elements.get('columns', []))} columns and {len(group_elements.get('walls', []))} walls")

            # Log configuration parameters
            log_calculation_result(self.log_callback, "User_edge_distance", user_edge_distance, "m")
            log_calculation_result(self.log_callback, "Min_spacing", min_spacing, "m")
            log_calculation_result(self.log_callback, "Pile_diameter", pile_diameter, "m")

            try:
                # Step 1: Prepare pile type candidates
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Step 1: Prepare pile type candidates")
                enhanced_log(self.log_callback, f"Preparing {len(selected_pile_types)} pile type candidates", 'INFO')

                pile_candidates = self.utils.prepare_pile_type_candidates(selected_pile_types, excel_inputs)

                if not pile_candidates:
                    error_msg = "No valid pile type candidates provided"
                    log_validation_result(self.log_callback, "Pile Candidates Preparation", False, error_msg)
                    log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "pile candidates preparation")
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    raise PileTypeSelectionError(error_msg)
                else:
                    log_validation_result(self.log_callback, "Pile Candidates Preparation", True,
                                        f"{len(pile_candidates)} valid candidates prepared")
                    log_performance_metric(self.log_callback, "Prepared_pile_candidates", len(pile_candidates), "candidates")
                    enhanced_log(self.log_callback, f"Successfully prepared {len(pile_candidates)} pile type candidates", 'INFO')

                # Step 2: Perform pile type pre-selection with DXF visualization
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Step 2: Perform pile type pre-selection")
                enhanced_log(self.log_callback, "Performing pile type pre-selection with DXF visualization", 'INFO')

                preselection_result = self.utils.perform_preselection(
                    group_elements, required_load, excel_inputs,
                    pile_candidates, user_edge_distance, site_boundary,
                    create_preselection_dxf, output_dir, self.preselector
                )

                if preselection_result is None:
                    error_msg = "Pile type pre-selection failed to return results"
                    log_validation_result(self.log_callback, "Pre-selection Execution", False, error_msg)
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    raise PileTypeSelectionError(error_msg)
                else:
                    log_validation_result(self.log_callback, "Pre-selection Execution", True,
                                        f"Selected pile type: {preselection_result.selected_pile_type.display_name}")
                    log_calculation_result(self.log_callback, "Selected_pile_type",
                                         preselection_result.selected_pile_type.display_name, "")
                    log_calculation_result(self.log_callback, "Preselection_total_capacity",
                                         preselection_result.total_capacity, "kN")
                    log_calculation_result(self.log_callback, "Preselection_utilization",
                                         preselection_result.utilization_ratio, "ratio")
                    log_performance_metric(self.log_callback, "Preselection_possible_piles",
                                         preselection_result.total_possible_piles, "piles")
                    enhanced_log(self.log_callback, f"Pre-selection completed: {preselection_result.selected_pile_type.display_name} "
                               f"with {preselection_result.total_possible_piles} possible piles", 'INFO')

                # Step 3: Prepare for NSGA-III optimization with selected pile type
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Step 3: Prepare optimization input")
                enhanced_log(self.log_callback, "Preparing NSGA-III optimization input with selected pile type", 'INFO')

                optimization_input = self.utils.prepare_optimization_input(
                    group_elements, preselection_result, min_spacing, pile_diameter
                )

                if optimization_input is None:
                    error_msg = "Failed to prepare optimization input"
                    log_validation_result(self.log_callback, "Optimization Input Preparation", False, error_msg)
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    raise PileTypeSelectionError(error_msg)
                else:
                    log_validation_result(self.log_callback, "Optimization Input Preparation", True,
                                        "Optimization input prepared successfully")
                    enhanced_log(self.log_callback, "Optimization input prepared successfully", 'INFO')

                # Step 4: Run NSGA-III optimization with pre-selected pile type
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Step 4: Run NSGA-III optimization")
                enhanced_log(self.log_callback, "Running NSGA-III optimization with pre-selected pile type", 'INFO')

                optimized_layout = self.utils.run_optimization(
                    optimization_input, excel_inputs, site_boundary, config_overrides, self.log_callback
                )

                if optimized_layout is None:
                    error_msg = "NSGA-III optimization failed to return results"
                    log_validation_result(self.log_callback, "NSGA-III Optimization", False, error_msg)
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    raise PileTypeSelectionError(error_msg)
                else:
                    log_validation_result(self.log_callback, "NSGA-III Optimization", True,
                                        "Optimization completed successfully")
                    enhanced_log(self.log_callback, "NSGA-III optimization completed successfully", 'INFO')

                # Step 5: Combine results
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Step 5: Combine results")
                enhanced_log(self.log_callback, "Combining pre-selection and optimization results", 'INFO')

                final_result = self.utils.combine_results(preselection_result, optimized_layout)

                if final_result is None:
                    error_msg = "Failed to combine pre-selection and optimization results"
                    log_validation_result(self.log_callback, "Results Combination", False, error_msg)
                    enhanced_log(self.log_callback, error_msg, 'ERROR')
                    raise PileTypeSelectionError(error_msg)
                else:
                    log_validation_result(self.log_callback, "Results Combination", True,
                                        "Results combined successfully")

                    # Log final results summary
                    if isinstance(final_result, dict):
                        if 'total_piles' in final_result:
                            log_performance_metric(self.log_callback, "Final_total_piles",
                                                 final_result['total_piles'], "piles")
                        if 'total_capacity' in final_result:
                            log_performance_metric(self.log_callback, "Final_total_capacity",
                                                 final_result['total_capacity'], "kN")
                        if 'utilization_ratio' in final_result:
                            log_performance_metric(self.log_callback, "Final_utilization_ratio",
                                                 final_result['utilization_ratio'], "ratio")

                    enhanced_log(self.log_callback, "Results combination completed successfully", 'INFO')

                enhanced_log(self.log_callback, "Integrated pile estimation completed successfully", 'INFO')
                log_algorithm_step(self.log_callback, "Integrated Estimation", "Completed successfully")

                log_function_exit(self.log_callback, "estimate_pile_layout_with_preselection", "Success")
                return final_result

            except Exception as e:
                error_msg = f"Integrated pile estimation failed: {e}"
                log_error_with_context(self.log_callback, e, "integrated pile estimation")
                enhanced_log(self.log_callback, error_msg, 'ERROR')
                log_algorithm_step(self.log_callback, "Integrated Estimation", f"Failed: {e}")
                log_function_exit(self.log_callback, "estimate_pile_layout_with_preselection", "Failed")
                raise


def quick_pile_type_preselection(group_elements: GroupElements,
                                required_load: float,
                                excel_inputs: Any,
                                selected_pile_types: List[Dict[str, Any]],
                                user_edge_distance: float = 0.4,
                                site_boundary: Optional[Any] = None,
                                log_callback: Optional[Callable] = None) -> Dict[str, str]:
    """
    Quick pile type pre-selection without full optimization.

    Returns only the selected pile type for rapid decision making.

    Args:
        group_elements: Structural elements
        required_load: Required load in kN
        excel_inputs: Excel input data
        selected_pile_types: Available pile types from GUI
        user_edge_distance: User-defined edge distance
        site_boundary: Optional site boundary
        log_callback: Optional logging callback

    Returns:
        Dictionary with selected pile type information
    """
    log_function_entry(log_callback, "quick_pile_type_preselection",
                      num_elements=len(group_elements.get('columns', [])) + len(group_elements.get('walls', [])),
                      required_load=required_load, num_pile_types=len(selected_pile_types),
                      user_edge_distance=user_edge_distance, has_site_boundary=site_boundary is not None)

    with create_timed_logger(log_callback, "quick_pile_type_preselection"):
        enhanced_log(log_callback, "Starting quick pile type pre-selection", 'INFO')

        try:
            # Input validation
            log_validation_result(log_callback, "Required Load Positive", required_load > 0,
                                f"Required load: {required_load} kN")
            log_validation_result(log_callback, "Pile Types Available", len(selected_pile_types) > 0,
                                f"{len(selected_pile_types)} pile types provided")
            log_validation_result(log_callback, "Group Elements Present",
                                len(group_elements.get('columns', [])) + len(group_elements.get('walls', [])) > 0,
                                f"Group has {len(group_elements.get('columns', []))} columns and {len(group_elements.get('walls', []))} walls")

            # Log configuration
            log_calculation_result(log_callback, "User_edge_distance", user_edge_distance, "m")

            # Import utility functions
            enhanced_log(log_callback, "Importing pile preselection utilities", 'DEBUG')
            from ..utils.pile_preselection_utils import (
                create_pile_type_candidates_from_gui,
                integrate_with_existing_capacity_calculation
            )

            # Create preselector instance
            enhanced_log(log_callback, "Creating PileTypePreselector instance", 'DEBUG')
            preselector = PileTypePreselector(log_callback)
            log_validation_result(log_callback, "Preselector Creation", True, "Preselector created successfully")

            # Create pile candidates from GUI data
            enhanced_log(log_callback, f"Creating pile candidates from {len(selected_pile_types)} GUI selections", 'INFO')
            pile_candidates = create_pile_type_candidates_from_gui(selected_pile_types, log_callback)

            if not pile_candidates:
                error_msg = "No valid pile type candidates created from GUI data"
                log_validation_result(log_callback, "Pile Candidates Creation", False, error_msg)
                log_error_with_context(log_callback, PileTypeSelectionError(error_msg), "pile candidates creation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileTypeSelectionError(error_msg)
            else:
                log_validation_result(log_callback, "Pile Candidates Creation", True,
                                    f"{len(pile_candidates)} valid candidates created")
                log_performance_metric(log_callback, "Created_pile_candidates", len(pile_candidates), "candidates")
                enhanced_log(log_callback, f"Successfully created {len(pile_candidates)} pile candidates", 'INFO')

            # Log candidate details
            for i, candidate in enumerate(pile_candidates):
                enhanced_log(log_callback, f"Candidate {i+1}: {candidate.display_name} - "
                           f"{candidate.capacity_per_pile} kN, {candidate.min_spacing} m spacing", 'DEBUG')

            # Create preselection criteria
            enhanced_log(log_callback, "Creating pile type preselection criteria", 'DEBUG')
            criteria = PileTypePreselectionCriteria(
                required_total_load=required_load,
                group_elements=group_elements,
                excel_inputs=excel_inputs,
                user_edge_distance=user_edge_distance,
                site_boundary=site_boundary
            )
            log_validation_result(log_callback, "Preselection Criteria Creation", True,
                                "Criteria object created successfully")

            # Perform pile type preselection
            log_algorithm_step(log_callback, "Quick Preselection", "Performing pile type preselection")
            enhanced_log(log_callback, "Performing pile type preselection", 'INFO')

            result = preselector.preselect_pile_type(criteria, pile_candidates)

            if result is None:
                error_msg = "Pile type preselection failed to return results"
                log_validation_result(log_callback, "Preselection Execution", False, error_msg)
                log_error_with_context(log_callback, PileTypeSelectionError(error_msg), "pile type preselection")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileTypeSelectionError(error_msg)
            else:
                log_validation_result(log_callback, "Preselection Execution", True,
                                    f"Selected: {result.selected_pile_type.display_name}")

                # Log preselection results
                log_calculation_result(log_callback, "Selected_pile_type", result.selected_pile_type.display_name, "")
                log_calculation_result(log_callback, "Selected_capacity_per_pile", result.selected_pile_type.capacity_per_pile, "kN")
                log_calculation_result(log_callback, "Total_possible_piles", result.total_possible_piles, "piles")
                log_calculation_result(log_callback, "Total_capacity", result.total_capacity, "kN")
                log_calculation_result(log_callback, "Utilization_ratio", result.utilization_ratio, "ratio")

                # Log performance metrics
                log_performance_metric(log_callback, "Preselected_possible_piles", result.total_possible_piles, "piles")
                log_performance_metric(log_callback, "Preselected_total_capacity", result.total_capacity, "kN")
                log_performance_metric(log_callback, "Preselected_utilization", result.utilization_ratio, "ratio")

                enhanced_log(log_callback, f"Preselection completed: {result.selected_pile_type.display_name} "
                           f"with {result.total_possible_piles} possible piles, "
                           f"{result.total_capacity:.2f} kN total capacity, "
                           f"{result.utilization_ratio:.1%} utilization", 'INFO')

            # Prepare return dictionary
            return_dict = {
                "selected_type": result.selected_pile_type.pile_type.name,
                "display_name": result.selected_pile_type.display_name,
                "capacity_per_pile": result.selected_pile_type.capacity_per_pile,
                "possible_piles": result.total_possible_piles,
                "total_capacity": result.total_capacity,
                "utilization": f"{result.utilization_ratio:.1%}"
            }

            # Validate return dictionary
            log_validation_result(log_callback, "Return Dictionary Creation", True,
                                f"Dictionary with {len(return_dict)} fields created")

            enhanced_log(log_callback, "Quick pile type preselection completed successfully", 'INFO')
            log_algorithm_step(log_callback, "Quick Preselection", "Completed successfully")

            log_function_exit(log_callback, "quick_pile_type_preselection",
                            f"Selected: {return_dict['display_name']}")
            return return_dict

        except Exception as e:
            error_msg = f"Quick pile type preselection failed: {e}"
            log_error_with_context(log_callback, e, "quick pile type preselection")
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_algorithm_step(log_callback, "Quick Preselection", f"Failed: {e}")
            log_function_exit(log_callback, "quick_pile_type_preselection", "Failed")
            raise

