"""
Visualizer Module for 3D Pile Volume Analysis

This module handles 3D visualization of pile geometries, overlaps, and
site boundaries using PyVista for interactive and static rendering.
"""

import pyvista as pv
import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path

from .geometry_engine import PileGeometry
from .volume_calculator import VolumeResult
from .overlap_analyzer import OverlapResult

logger = logging.getLogger(__name__)


class Visualizer:
    """
    Handles 3D visualization of pile volume analysis results using PyVista.
    """
    
    def __init__(self, window_size: Tuple[int, int] = (1200, 800)):
        """
        Initialize the Visualizer.
        
        Args:
            window_size: Window size for PyVista plots
        """
        self.window_size = window_size
        
        # Set PyVista theme
        pv.set_plot_theme('document')
        pv.global_theme.window_size = window_size
        pv.global_theme.show_scalar_bar = True
        pv.global_theme.font.size = 12
        
    def visualize_pile_geometries(self, 
                                geometries: Dict[str, PileGeometry],
                                volume_results: Optional[Dict[str, VolumeResult]] = None,
                                show_parts: bool = True,
                                show_labels: bool = True) -> pv.Plotter:
        """
        Visualize pile geometries with optional volume coloring.
        
        Args:
            geometries: Dictionary of pile geometries
            volume_results: Optional volume results for coloring
            show_parts: Whether to show individual parts
            show_labels: Whether to show pile labels
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        if not geometries:
            logger.warning("No geometries to visualize")
            return plotter
            
        # Color scheme
        colors = {
            'part1': 'lightblue',    # Pile cylinder
            'part2': 'orange',       # Frustum
            'part3': 'lightgreen',   # Soil cylinder
            'combined': 'gray'
        }
        
        # Add each pile geometry
        for pile_id, geometry in geometries.items():
            try:
                if show_parts:
                    # Add individual parts
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part1_cylinder, 
                        color=colors['part1'], 
                        label=f"{pile_id}_Part1" if len(geometries) <= 10 else None
                    )
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part2_frustum, 
                        color=colors['part2'],
                        label=f"{pile_id}_Part2" if len(geometries) <= 10 else None
                    )
                    self._add_trimesh_to_plotter(
                        plotter, geometry.part3_cylinder, 
                        color=colors['part3'],
                        label=f"{pile_id}_Part3" if len(geometries) <= 10 else None
                    )
                else:
                    # Add combined geometry
                    color = colors['combined']
                    if volume_results and pile_id in volume_results:
                        # Color by volume
                        volume = volume_results[pile_id].final_volume
                        color = volume  # Will be mapped to colormap
                        
                    self._add_trimesh_to_plotter(
                        plotter, geometry.combined_mesh,
                        color=color,
                        label=pile_id if len(geometries) <= 20 else None
                    )
                    
                # Add pile labels
                if show_labels and len(geometries) <= 50:
                    center = geometry.combined_mesh.centroid
                    plotter.add_point_labels(
                        [center], [pile_id],
                        point_size=0, font_size=10,
                        text_color='black'
                    )
                    
            except Exception as e:
                logger.warning(f"Error visualizing pile {pile_id}: {e}")
                
        # Configure view
        plotter.add_axes()
        plotter.show_grid()
        
        if volume_results and not show_parts:
            plotter.add_scalar_bar(title="Final Volume")
            
        plotter.camera_position = 'iso'
        
        return plotter
        
    def visualize_overlaps(self, 
                          geometries: Dict[str, PileGeometry],
                          overlap_results: List[OverlapResult],
                          highlight_overlaps: bool = True) -> pv.Plotter:
        """
        Visualize pile geometries with overlap highlighting.
        
        Args:
            geometries: Dictionary of pile geometries
            overlap_results: List of overlap results
            highlight_overlaps: Whether to highlight overlapping regions
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Get overlapping pile IDs
        overlapping_piles = set()
        for overlap in overlap_results:
            overlapping_piles.add(overlap.pile1_id)
            overlapping_piles.add(overlap.pile2_id)
            
        # Add pile geometries
        for pile_id, geometry in geometries.items():
            try:
                color = 'red' if pile_id in overlapping_piles else 'lightblue'
                opacity = 0.7 if pile_id in overlapping_piles else 0.9
                
                self._add_trimesh_to_plotter(
                    plotter, geometry.combined_mesh,
                    color=color, opacity=opacity,
                    label=pile_id if len(geometries) <= 20 else None
                )
                
            except Exception as e:
                logger.warning(f"Error visualizing pile {pile_id}: {e}")
                
        # Add overlap regions
        if highlight_overlaps:
            for overlap in overlap_results:
                if overlap.overlap_mesh is not None:
                    try:
                        self._add_trimesh_to_plotter(
                            plotter, overlap.overlap_mesh,
                            color='yellow', opacity=0.8,
                            label=f"Overlap_{overlap.pile1_id}_{overlap.pile2_id}"
                        )
                    except Exception as e:
                        logger.warning(f"Error visualizing overlap: {e}")
                        
        plotter.add_axes()
        plotter.show_grid()
        plotter.camera_position = 'iso'
        
        return plotter
        
    def visualize_site_boundary(self, 
                              boundary_coords: List[Tuple[float, float]],
                              elevation_range: Tuple[float, float],
                              geometries: Optional[Dict[str, PileGeometry]] = None) -> pv.Plotter:
        """
        Visualize site boundary with optional pile geometries.
        
        Args:
            boundary_coords: Site boundary coordinates
            elevation_range: (min_elevation, max_elevation)
            geometries: Optional pile geometries to include
            
        Returns:
            PyVista plotter object
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Create boundary mesh
        try:
            boundary_mesh = self._create_boundary_mesh(boundary_coords, elevation_range)
            plotter.add_mesh(
                boundary_mesh, 
                color='lightgray', 
                opacity=0.3,
                show_edges=True,
                edge_color='black',
                label='Site Boundary'
            )
        except Exception as e:
            logger.error(f"Error creating boundary visualization: {e}")
            
        # Add pile geometries if provided
        if geometries:
            for pile_id, geometry in geometries.items():
                try:
                    self._add_trimesh_to_plotter(
                        plotter, geometry.combined_mesh,
                        color='blue', opacity=0.8,
                        label=pile_id if len(geometries) <= 20 else None
                    )
                except Exception as e:
                    logger.warning(f"Error adding pile {pile_id}: {e}")
                    
        plotter.add_axes()
        plotter.show_grid()
        plotter.camera_position = 'iso'
        
        return plotter
        
    def _add_trimesh_to_plotter(self, plotter: pv.Plotter, mesh: trimesh.Trimesh,
                               color: Union[str, float] = 'blue', 
                               opacity: float = 1.0,
                               label: Optional[str] = None) -> None:
        """
        Add a Trimesh object to PyVista plotter.
        
        Args:
            plotter: PyVista plotter
            mesh: Trimesh object
            color: Color specification
            opacity: Mesh opacity
            label: Optional label for legend
        """
        try:
            # Convert Trimesh to PyVista
            if mesh.vertices is None or len(mesh.vertices) == 0:
                logger.warning("Empty mesh, skipping")
                return
                
            # Create PyVista mesh
            faces = mesh.faces
            if len(faces) > 0:
                # Convert faces to PyVista format (add count prefix)
                pv_faces = np.column_stack([
                    np.full(len(faces), 3),  # Triangle count
                    faces
                ]).flatten()
                
                pv_mesh = pv.PolyData(mesh.vertices, pv_faces)
            else:
                # Point cloud
                pv_mesh = pv.PolyData(mesh.vertices)
                
            # Add to plotter
            plotter.add_mesh(
                pv_mesh,
                color=color,
                opacity=opacity,
                label=label,
                show_edges=False
            )
            
        except Exception as e:
            logger.warning(f"Error adding mesh to plotter: {e}")
            
    def _create_boundary_mesh(self, 
                            boundary_coords: List[Tuple[float, float]],
                            elevation_range: Tuple[float, float]) -> pv.PolyData:
        """
        Create PyVista mesh for site boundary.
        
        Args:
            boundary_coords: Boundary coordinates
            elevation_range: Elevation range
            
        Returns:
            PyVista boundary mesh
        """
        min_elev, max_elev = elevation_range
        height = max_elev - min_elev
        
        # Create bottom and top polygons
        coords = np.array(boundary_coords)
        n_points = len(coords)
        
        # Bottom vertices
        bottom_vertices = np.column_stack([
            coords[:, 0], coords[:, 1], 
            np.full(n_points, min_elev)
        ])
        
        # Top vertices
        top_vertices = np.column_stack([
            coords[:, 0], coords[:, 1], 
            np.full(n_points, max_elev)
        ])
        
        # Combine vertices
        vertices = np.vstack([bottom_vertices, top_vertices])
        
        # Create faces
        faces = []
        
        # Bottom face
        bottom_face = [n_points] + list(range(n_points))
        faces.extend(bottom_face)
        
        # Top face (reverse order)
        top_face = [n_points] + list(range(n_points, 2*n_points))[::-1]
        faces.extend(top_face)
        
        # Side faces
        for i in range(n_points):
            next_i = (i + 1) % n_points
            # Two triangles per side
            faces.extend([3, i, next_i, n_points + i])
            faces.extend([3, next_i, n_points + next_i, n_points + i])
            
        return pv.PolyData(vertices, faces)
        
    def save_visualization(self, plotter: pv.Plotter, 
                         filename: Union[str, Path],
                         format: str = 'png') -> str:
        """
        Save visualization to file.
        
        Args:
            plotter: PyVista plotter
            filename: Output filename
            format: Output format ('png', 'jpg', 'svg')
            
        Returns:
            Path to saved file
        """
        try:
            filepath = Path(filename).with_suffix(f'.{format}')
            plotter.screenshot(str(filepath))
            logger.info(f"Visualization saved to {filepath}")
            return str(filepath)
        except Exception as e:
            logger.error(f"Error saving visualization: {e}")
            raise
            
    def create_volume_comparison_plot(self, 
                                    volume_results: Dict[str, VolumeResult]) -> pv.Plotter:
        """
        Create a plot comparing theoretical vs calculated volumes.
        
        Args:
            volume_results: Volume calculation results
            
        Returns:
            PyVista plotter with comparison visualization
        """
        plotter = pv.Plotter(window_size=self.window_size)
        
        # Extract data for plotting
        pile_ids = list(volume_results.keys())
        calculated_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        
        # Create bar chart-like visualization
        # This is a simplified approach - for detailed charts, consider matplotlib
        
        plotter.add_text("Volume Comparison", position='upper_left', font_size=16)
        
        return plotter
