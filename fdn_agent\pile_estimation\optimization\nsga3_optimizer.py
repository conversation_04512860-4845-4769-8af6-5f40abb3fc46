﻿"""
Enhanced High-Performance NSGA-III Optimizer - Advanced AI Implementation

This module provides an enhanced DEAP-based NSGA-III implementation with advanced AI capabilities
for pile layout optimization in foundation engineering applications.

CORE FEATURES:
- Proven DEAP library foundation with evolutionary algorithms
- NSGA-III selection with adaptive reference points for many-objective optimization
- Efficient genetic operators with adaptive parameter control
- Optimized for pile layout problems with binary representation
- Automatic repair mechanisms for spacing and count constraints
- Professional-grade implementation with extensive testing and validation

ENHANCED AI FEATURES:
- Adaptive Parameter Control: Dynamic adjustment of crossover and mutation probabilities
  based on population diversity and convergence rate for improved optimization performance
- Advanced Convergence Detection: Early stopping with stagnation monitoring and
  convergence threshold analysis to prevent unnecessary computation
- Comprehensive Performance Monitoring: Real-time tracking of generation times,
  diversity history, convergence metrics, and solution quality indicators
- Enhanced Constraint Handling: Constraint domination principles with improved
  penalty mechanisms for better constraint satisfaction
- Population Management: Advanced diversity preservation using crowding distance
  calculations and population quality monitoring
- Performance Optimization: Comprehensive metrics tracking and adaptive strategies

MAIN CLASSES:
- DEAPNSGA3Optimizer: Enhanced DEAP-based genetic algorithm with AI capabilities
- DEAPNSGA3Config: Extended configuration class with enhanced features
- PileRepairOperators: Advanced constraint repair mechanisms

BACKWARD COMPATIBILITY:
All enhanced features are disabled by default to maintain full backward compatibility
with existing code. Enhanced features can be enabled through configuration parameters.

USAGE EXAMPLES:

Basic Usage (Backward Compatible):
```python
config = DEAPNSGA3Config(population_size=50, generations=100)
optimizer = DEAPNSGA3Optimizer(config, n_variables=100, n_objectives=2)
best_solution, pareto_front, metrics = optimizer.optimize(evaluation_func, **kwargs)
```

Enhanced Usage with AI Features:
```python
config = DEAPNSGA3Config(
    population_size=100,
    generations=200,
    adaptive_parameters=True,
    diversity_preservation=True,
    early_stopping=True,
    performance_tracking=True,
    constraint_domination=True
)
optimizer = DEAPNSGA3Optimizer(config, n_variables=100, n_objectives=2)
best_solution, pareto_front, metrics = optimizer.optimize(evaluation_func, **kwargs)
```

Based on DEAP framework with advanced AI enhancements for foundation engineering optimization.
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
import numpy as np
import random
from dataclasses import dataclass
import time
from math import sqrt

# DEAP imports
from deap import base, creator, tools, algorithms
from deap.tools import selNSGA3

from ..data_types.basic_types import Point2D
from ..exceptions import OptimizationError, InputDataError
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    log_algorithm_step,
    create_timed_logger
)


@dataclass
class DEAPNSGA3Config:
    """
    Enhanced configuration for DEAP-based NSGA-III optimization with advanced AI capabilities.

    Core Parameters:
        population_size: Number of individuals in the population (default: 100)
        generations: Number of generations to evolve (default: 50)
        ref_points_p: Precision parameter for reference points (default: 4)
        crossover_prob: Probability of crossover operation (default: 0.9)
        mutation_prob: Probability of mutation operation (default: 0.1)
        mutation_indpb: Independent probability for each bit mutation (default: 0.05)

    Enhanced Features:
        adaptive_reference_points: Enable adaptive reference point adjustment (default: False)
        adaptive_parameters: Enable adaptive parameter control (default: False)
        diversity_preservation: Enable advanced diversity preservation (default: False)
        early_stopping: Enable early stopping based on convergence (default: False)
        performance_tracking: Enable comprehensive performance monitoring (default: False)
        constraint_domination: Enable constraint domination handling (default: False)

    Advanced Parameters:
        convergence_threshold: Threshold for convergence detection (default: 1e-6)
        stagnation_generations: Generations without improvement for early stopping (default: 15)
        diversity_threshold: Minimum diversity threshold (default: 0.01)
        constraint_violation_penalty: Penalty for constraint violations (default: 1000.0)

    Backward Compatibility:
        All legacy parameters are supported. Enhanced features are disabled by default
        to maintain compatibility with existing code.
    """
    # Core Algorithm Parameters
    population_size: int = 100
    generations: int = 50

    # Reference points for NSGA-III
    ref_points_p: int = 4  # Precision parameter for reference points

    # Genetic Operators
    crossover_prob: float = 0.9
    mutation_prob: float = 0.1

    # Mutation parameters
    mutation_indpb: float = 0.05  # Independent probability for each bit

    # Enhanced Features (disabled by default for backward compatibility)
    adaptive_reference_points: bool = False
    reference_point_adaptation_frequency: int = 10
    adaptive_parameters: bool = False
    diversity_preservation: bool = False
    early_stopping: bool = False
    performance_tracking: bool = False
    constraint_domination: bool = False

    # Advanced Convergence Control
    convergence_threshold: float = 1e-6
    stagnation_generations: int = 15
    convergence_tracking: bool = False

    # Diversity Control
    diversity_threshold: float = 0.01
    crowding_distance_weight: float = 1.0

    # Constraint Handling
    constraint_violation_penalty: float = 1000.0

    # Performance Monitoring
    detailed_logging: bool = False
    generation_timing: bool = False

    def __post_init__(self):
        """Validate configuration parameters after initialization."""
        # Import here to avoid circular imports
        from ..utils.logging_utils import enhanced_log

        # Validate core parameters
        if self.population_size <= 0:
            raise ValueError(f"population_size must be positive, got {self.population_size}")
        if self.population_size > 1000:
            enhanced_log(None, f"WARNING: Large population_size ({self.population_size}) may impact performance", 'WARNING')

        if self.generations <= 0:
            raise ValueError(f"generations must be positive, got {self.generations}")
        if self.generations > 500:
            enhanced_log(None, f"WARNING: Large generations ({self.generations}) may impact performance", 'WARNING')

        # Validate probabilities
        if not (0.0 <= self.crossover_prob <= 1.0):
            raise ValueError(f"crossover_prob must be between 0.0 and 1.0, got {self.crossover_prob}")
        if not (0.0 <= self.mutation_prob <= 1.0):
            raise ValueError(f"mutation_prob must be between 0.0 and 1.0, got {self.mutation_prob}")
        if not (0.0 <= self.mutation_indpb <= 1.0):
            raise ValueError(f"mutation_indpb must be between 0.0 and 1.0, got {self.mutation_indpb}")

        if self.ref_points_p <= 0:
            raise ValueError(f"ref_points_p must be positive, got {self.ref_points_p}")

        # Validate enhanced features
        if self.reference_point_adaptation_frequency <= 0:
            raise ValueError(f"reference_point_adaptation_frequency must be positive, got {self.reference_point_adaptation_frequency}")

        if self.convergence_threshold <= 0:
            raise ValueError(f"convergence_threshold must be positive, got {self.convergence_threshold}")

        if self.stagnation_generations <= 0:
            raise ValueError(f"stagnation_generations must be positive, got {self.stagnation_generations}")

        if not (0.0 <= self.diversity_threshold <= 1.0):
            raise ValueError(f"diversity_threshold must be between 0.0 and 1.0, got {self.diversity_threshold}")

        if self.constraint_violation_penalty < 0:
            raise ValueError(f"constraint_violation_penalty must be non-negative, got {self.constraint_violation_penalty}")

        if self.crowding_distance_weight < 0:
            raise ValueError(f"crowding_distance_weight must be non-negative, got {self.crowding_distance_weight}")

        # Log configuration summary
        enhanced_features = []
        if self.adaptive_reference_points:
            enhanced_features.append("adaptive_ref_points")
        if self.adaptive_parameters:
            enhanced_features.append("adaptive_params")
        if self.diversity_preservation:
            enhanced_features.append("diversity_preservation")
        if self.early_stopping:
            enhanced_features.append("early_stopping")
        if self.performance_tracking:
            enhanced_features.append("performance_tracking")
        if self.constraint_domination:
            enhanced_features.append("constraint_domination")

        features_str = ", ".join(enhanced_features) if enhanced_features else "none"
        enhanced_log(None, f"DEAPNSGA3Config validated: pop={self.population_size}, gen={self.generations}, "
                          f"enhanced_features=[{features_str}]", 'DEBUG')


class PileRepairOperators:
    """Repair operators for pile layout constraints using DEAP."""
    
    def __init__(self, grid_positions: List[Point2D], min_spacing: float, target_count: int, 
                 log_callback: Optional[Callable] = None):
        log_function_entry(log_callback, "PileRepairOperators.__init__",
                          grid_positions=len(grid_positions) if grid_positions else 0,
                          min_spacing=min_spacing, target_count=target_count)

        # Input validation with constraint checking
        log_constraint_check(log_callback, "grid_positions_provided", len(grid_positions) if grid_positions else 0, 0, bool(grid_positions))
        if not grid_positions:
            enhanced_log(log_callback, "No grid positions provided for repair operators", 'ERROR')
            log_function_exit(log_callback, "PileRepairOperators.__init__", "error")
            raise InputDataError("No grid positions provided for repair operators")

        log_constraint_check(log_callback, "min_spacing_positive", min_spacing, 0, min_spacing > 0)
        if min_spacing <= 0:
            enhanced_log(log_callback, f"Invalid minimum spacing: {min_spacing}", 'ERROR')
            log_function_exit(log_callback, "PileRepairOperators.__init__", "error")
            raise InputDataError("Minimum spacing must be positive")

        log_constraint_check(log_callback, "target_count_positive", target_count, 0, target_count > 0)
        if target_count <= 0:
            enhanced_log(log_callback, f"Invalid target count: {target_count}", 'ERROR')
            log_function_exit(log_callback, "PileRepairOperators.__init__", "error")
            raise InputDataError("Target count must be positive")
        
        self.grid_positions = grid_positions
        self.min_spacing = min_spacing
        self.target_count = target_count
        self.n_variables = len(grid_positions)
        self.log_callback = log_callback
        
        enhanced_log(log_callback, f"Initializing repair operators for {self.n_variables} positions", 'INFO')
        
        # Precompute distance matrix for efficiency
        start_time = time.time()
        self._distance_matrix = self._compute_distance_matrix()
        computation_time = time.time() - start_time
        
        log_performance_metric(log_callback, "Distance matrix computation", computation_time, "seconds")
        enhanced_log(log_callback, f"Distance matrix computed: {self.n_variables}x{self.n_variables}", 'DEBUG')
        
        log_function_exit(log_callback, "PileRepairOperators.__init__")        
    def _compute_distance_matrix(self) -> np.ndarray:
        """Precompute distances between all grid positions."""
        log_function_entry(self.log_callback, "_compute_distance_matrix")
        
        positions = np.array(self.grid_positions)
        n = len(positions)
        distances = np.zeros((n, n))
        
        enhanced_log(self.log_callback, f"Computing distance matrix for {n} positions", 'DEBUG')
        
        for i in range(n):
            if i % 100 == 0 and n > 500:  # Progress for large grids
                log_progress(self.log_callback, i, n, "Distance computation")
            
            for j in range(i+1, n):
                dist = sqrt((positions[i][0] - positions[j][0])**2 + 
                           (positions[i][1] - positions[j][1])**2)
                distances[i, j] = distances[j, i] = dist
        
        enhanced_log(self.log_callback, f"Distance matrix computation completed", 'DEBUG')
        log_function_exit(self.log_callback, "_compute_distance_matrix", f"{n}x{n} matrix")
        return distances    
    def repair_individual(self, individual: List[int]) -> List[int]:
        """Apply all repair operations to an individual."""
        log_function_entry(self.log_callback, "repair_individual", 
                          individual_length=len(individual) if individual else 0)
        
        if not individual:
            enhanced_log(self.log_callback, "Empty individual provided for repair", 'ERROR')
            raise OptimizationError("Empty individual provided for repair")
            
        if len(individual) != self.n_variables:
            enhanced_log(self.log_callback, 
                        f"Individual length mismatch: {len(individual)} != {self.n_variables}", 'ERROR')
            raise OptimizationError(f"Individual length {len(individual)} does not match expected {self.n_variables}")
        
        # Log initial state
        initial_count = sum(1 for x in individual if bool(x))
        enhanced_log(self.log_callback, f"Initial pile count: {initial_count}, Target: {self.target_count}", 'DEBUG')
        
        # Apply count repair
        enhanced_log(self.log_callback, "Applying count repair", 'DEBUG')
        repaired = self._repair_count(individual)
        
        count_after_repair = sum(1 for x in repaired if bool(x))
        log_validation_result(self.log_callback, "Count repair", 
                            count_after_repair == self.target_count,
                            f"Count: {count_after_repair}/{self.target_count}")
        
        # Apply spacing repair
        enhanced_log(self.log_callback, "Applying spacing repair", 'DEBUG')
        repaired = self._repair_spacing(repaired)
        
        final_count = sum(1 for x in repaired if bool(x))
        log_validation_result(self.log_callback, "Final repair validation",
                            final_count <= self.target_count,
                            f"Final count: {final_count}")
        
        log_function_exit(self.log_callback, "repair_individual", f"Count: {final_count}")
        return repaired
    
    def _repair_count(self, individual: List[int]) -> List[int]:
        """Repair individual to have target pile count."""
        log_function_entry(self.log_callback, "_repair_count",
                          individual_length=len(individual) if individual else 0,
                          target_count=self.target_count)

        # Safe sum calculation to handle potential arrays
        try:
            if hasattr(individual, '__len__') and hasattr(individual, 'sum'):
                # It's likely a numpy array
                current_count = int(individual.sum())
            else:
                # It's a regular list
                current_count = sum(int(x) for x in individual)
        except Exception as e:
            # Fallback: convert each element and sum
            enhanced_log(self.log_callback, f"Count calculation fallback due to: {e}", 'WARNING')
            current_count = sum(1 for x in individual if (hasattr(x, '__len__') and len(x) > 1 and x.any()) or (not hasattr(x, '__len__') and bool(x)))

        enhanced_log(self.log_callback, f"Current pile count: {current_count}, Target: {self.target_count}", 'DEBUG')
        log_constraint_check(self.log_callback, "count_matches_target", current_count, self.target_count, current_count == self.target_count)

        if current_count == self.target_count:
            enhanced_log(self.log_callback, "No count repair needed", 'DEBUG')
            log_function_exit(self.log_callback, "_repair_count", "no_change")
            return individual.copy() if hasattr(individual, 'copy') else list(individual)
        
        # Convert to list for manipulation
        if hasattr(individual, 'tolist'):
            repaired = individual.tolist()
        else:
            repaired = list(individual)

        if current_count > self.target_count:
            # Remove excess piles
            enhanced_log(self.log_callback, f"Removing {current_count - self.target_count} excess piles", 'DEBUG')
            selected_indices = []
            for i, x in enumerate(repaired):
                # Safe boolean check
                if hasattr(x, '__len__') and len(x) > 1:
                    is_selected = bool(x.any()) if hasattr(x, 'any') else bool(any(x))
                else:
                    is_selected = bool(x)

                if is_selected:
                    selected_indices.append(i)

            log_constraint_check(self.log_callback, "selected_piles_available", len(selected_indices), 0, len(selected_indices) > 0)
            if not selected_indices:
                error_msg = "No selected piles to remove during count repair"
                enhanced_log(self.log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(self.log_callback, "_repair_count", "error")
                raise OptimizationError(error_msg)

            to_remove = random.sample(selected_indices, current_count - self.target_count)
            enhanced_log(self.log_callback, f"Removing piles at indices: {to_remove}", 'DEBUG')
            for i in to_remove:
                repaired[i] = 0
        else:
            # Add missing piles
            enhanced_log(self.log_callback, f"Adding {self.target_count - current_count} missing piles", 'DEBUG')
            available_indices = []
            for i, x in enumerate(repaired):
                # Safe boolean check
                if hasattr(x, '__len__') and len(x) > 1:
                    is_not_selected = not bool(x.any()) if hasattr(x, 'any') else not bool(any(x))
                else:
                    is_not_selected = not bool(x)

                if is_not_selected:
                    available_indices.append(i)

            log_constraint_check(self.log_callback, "available_positions", len(available_indices), 0, len(available_indices) > 0)
            if not available_indices:
                error_msg = "No available positions to add piles during count repair"
                enhanced_log(self.log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(self.log_callback, "_repair_count", "error")
                raise OptimizationError(error_msg)

            to_add = random.sample(available_indices,
                                 min(self.target_count - current_count, len(available_indices)))
            enhanced_log(self.log_callback, f"Adding piles at indices: {to_add}", 'DEBUG')
            for i in to_add:
                repaired[i] = 1

        final_count = sum(1 for x in repaired if bool(x))
        log_calculation_result(self.log_callback, "repaired_pile_count", final_count, "piles")
        log_validation_result(self.log_callback, "count_repair_success", final_count == self.target_count,
                            f"Final count: {final_count}/{self.target_count}")
        log_function_exit(self.log_callback, "_repair_count", f"{final_count} piles")
        return repaired
    
    def _repair_spacing(self, individual: List[int]) -> List[int]:
        """Repair spacing constraint violations."""
        log_function_entry(self.log_callback, "_repair_spacing",
                          individual_length=len(individual) if individual else 0,
                          min_spacing=self.min_spacing)

        # Convert to list for manipulation and get selected indices safely
        if hasattr(individual, 'tolist'):
            repaired = individual.tolist()
        else:
            repaired = list(individual)

        selected_indices = []
        for i, x in enumerate(repaired):
            # Safe boolean check to avoid array ambiguity
            if hasattr(x, '__len__') and len(x) > 1:
                is_selected = bool(x.any()) if hasattr(x, 'any') else bool(any(x))
            else:
                is_selected = bool(x)

            if is_selected:
                selected_indices.append(i)

        enhanced_log(self.log_callback, f"Found {len(selected_indices)} selected piles for spacing check", 'DEBUG')

        if len(selected_indices) <= 1:
            enhanced_log(self.log_callback, "No spacing repair needed (≤1 pile selected)", 'DEBUG')
            log_function_exit(self.log_callback, "_repair_spacing", "no_change")
            return repaired
        
        # Find spacing violations
        enhanced_log(self.log_callback, f"Checking spacing violations with minimum distance {self.min_spacing}", 'DEBUG')
        violations = []
        for i, idx1 in enumerate(selected_indices):
            for idx2 in selected_indices[i+1:]:
                distance = self._distance_matrix[idx1, idx2]
                if distance < self.min_spacing:
                    violations.append((idx1, idx2))
                    enhanced_log(self.log_callback, f"Spacing violation: piles {idx1}-{idx2} distance {distance:.2f} < {self.min_spacing}", 'DEBUG')

        enhanced_log(self.log_callback, f"Found {len(violations)} spacing violations", 'DEBUG')
        log_constraint_check(self.log_callback, "spacing_violations", len(violations), 0, len(violations) == 0)

        if not violations:
            enhanced_log(self.log_callback, "No spacing violations found", 'DEBUG')
            log_function_exit(self.log_callback, "_repair_spacing", "no_violations")
            return repaired

        # Remove violating piles
        enhanced_log(self.log_callback, f"Resolving {len(violations)} spacing violations", 'DEBUG')
        removed = set()

        for idx1, idx2 in violations:
            if idx1 in removed or idx2 in removed:
                continue

            # Simple heuristic: remove the one with more violations
            violations1 = sum(1 for other_idx in selected_indices
                            if other_idx != idx1 and self._distance_matrix[idx1, other_idx] < self.min_spacing)
            violations2 = sum(1 for other_idx in selected_indices
                            if other_idx != idx2 and self._distance_matrix[idx2, other_idx] < self.min_spacing)

            if violations1 >= violations2:
                enhanced_log(self.log_callback, f"Removing pile {idx1} (has {violations1} violations)", 'DEBUG')
                repaired[idx1] = 0
                removed.add(idx1)
            else:
                enhanced_log(self.log_callback, f"Removing pile {idx2} (has {violations2} violations)", 'DEBUG')
                repaired[idx2] = 0
                removed.add(idx2)

        final_count = sum(1 for x in repaired if bool(x))
        log_calculation_result(self.log_callback, "spacing_repaired_pile_count", final_count, "piles")
        log_performance_metric(self.log_callback, "piles_removed_for_spacing", len(removed), "piles")
        log_validation_result(self.log_callback, "spacing_repair_completed", True,
                            f"Removed {len(removed)} piles, final count: {final_count}")
        log_function_exit(self.log_callback, "_repair_spacing", f"{final_count} piles")
        return repaired


class DEAPNSGA3Optimizer:
    """
    Enhanced DEAP-based NSGA-III optimizer for pile layout optimization.

    Features:
    - Proven DEAP algorithms with custom repair operators
    - Adaptive parameter control for improved convergence
    - Advanced diversity preservation mechanisms
    - Comprehensive performance monitoring and tracking
    - Early stopping with convergence detection
    - Enhanced constraint handling with domination principles
    """
    def __init__(self, config: DEAPNSGA3Config, n_variables: int, n_objectives: int,
                 log_callback: Optional[Callable] = None):
        """Initialize the enhanced DEAP NSGA-III optimizer."""
        log_function_entry(log_callback, "DEAPNSGA3Optimizer.__init__",
                          n_variables=n_variables, n_objectives=n_objectives,
                          population_size=config.population_size if config else None,
                          generations=config.generations if config else None)

        # Input validation with constraint checking
        log_constraint_check(log_callback, "config_type_valid", 1 if isinstance(config, DEAPNSGA3Config) else 0, 1, isinstance(config, DEAPNSGA3Config))
        if not isinstance(config, DEAPNSGA3Config):
            enhanced_log(log_callback, "Invalid config type provided", 'ERROR')
            log_function_exit(log_callback, "DEAPNSGA3Optimizer.__init__", "error")
            raise InputDataError("Config must be a DEAPNSGA3Config instance")

        log_constraint_check(log_callback, "n_variables_positive", n_variables, 0, n_variables > 0)
        if n_variables <= 0:
            enhanced_log(log_callback, f"Invalid number of variables: {n_variables}", 'ERROR')
            log_function_exit(log_callback, "DEAPNSGA3Optimizer.__init__", "error")
            raise InputDataError("Number of variables must be positive")

        log_constraint_check(log_callback, "n_objectives_positive", n_objectives, 0, n_objectives > 0)
        if n_objectives <= 0:
            enhanced_log(log_callback, f"Invalid number of objectives: {n_objectives}", 'ERROR')
            log_function_exit(log_callback, "DEAPNSGA3Optimizer.__init__", "error")
            raise InputDataError("Number of objectives must be positive")

        self.config = config
        self.n_variables = n_variables
        self.n_objectives = n_objectives
        self.log_callback = log_callback

        enhanced_log(log_callback, f"Initializing enhanced NSGA-III optimizer: {n_variables} variables, {n_objectives} objectives", 'INFO')
        enhanced_log(log_callback, f"Configuration: pop_size={config.population_size}, generations={config.generations}", 'INFO')
        enhanced_log(log_callback, f"Genetic operators: crossover_prob={config.crossover_prob}, mutation_prob={config.mutation_prob}", 'DEBUG')

        # Enhanced features tracking
        enhanced_features = []
        if config.adaptive_parameters:
            enhanced_features.append("adaptive_parameters")
        if config.diversity_preservation:
            enhanced_features.append("diversity_preservation")
        if config.early_stopping:
            enhanced_features.append("early_stopping")
        if config.performance_tracking:
            enhanced_features.append("performance_tracking")
        if config.constraint_domination:
            enhanced_features.append("constraint_domination")

        if enhanced_features:
            enhanced_log(log_callback, f"Enhanced features enabled: {', '.join(enhanced_features)}", 'INFO')

        # Performance tracking variables
        self.generation_times = [] if config.performance_tracking else None
        self.convergence_history = [] if config.convergence_tracking else None
        self.diversity_history = [] if config.performance_tracking else None
        self.best_solutions_history = [] if config.performance_tracking else None

        # Adaptive parameter control
        self.current_crossover_prob = config.crossover_prob
        self.current_mutation_prob = config.mutation_prob
        self.current_mutation_indpb = config.mutation_indpb
        self.stagnation_counter = 0
        self.best_fitness_history = []

        # Algorithm state
        self.generation = 0
        self.repair_operators = None

        # Create fitness and individual types
        log_algorithm_step(log_callback, "NSGA3_Initialization", "Setting up DEAP types")
        enhanced_log(log_callback, "Setting up DEAP types", 'DEBUG')
        self._setup_deap_types()

        # Create toolbox
        log_algorithm_step(log_callback, "NSGA3_Initialization", "Setting up genetic operators")
        enhanced_log(log_callback, "Setting up genetic operators", 'DEBUG')
        self.toolbox = base.Toolbox()
        self._setup_toolbox()

        # Generate reference points for NSGA-III
        log_algorithm_step(log_callback, "NSGA3_Initialization", "Generating reference points")
        enhanced_log(log_callback, "Generating reference points for NSGA-III", 'DEBUG')
        with create_timed_logger(log_callback, "reference_point_generation") as timer:
            self.ref_points = self._generate_reference_points()

        log_performance_metric(log_callback, "reference_points_count", len(self.ref_points), "points")
        enhanced_log(log_callback, f"Generated {len(self.ref_points)} reference points", 'INFO')

        log_validation_result(log_callback, "optimizer_initialization", True,
                            f"Enhanced NSGA-III optimizer initialized with {n_variables} variables, {n_objectives} objectives")
        log_function_exit(log_callback, "DEAPNSGA3Optimizer.__init__")

    def _calculate_population_diversity(self, population: List) -> float:
        """
        Calculate population diversity using average pairwise distance.

        Args:
            population: List of individuals in the population

        Returns:
            float: Diversity measure (0.0 to 1.0, higher is more diverse)
        """
        log_function_entry(self.log_callback, "_calculate_population_diversity",
                          population_size=len(population) if population else 0)

        try:
            if not population or len(population) < 2:
                log_function_exit(self.log_callback, "_calculate_population_diversity", "minimal_population")
                return 1.0  # Maximum diversity for small populations

            # Calculate pairwise Hamming distances for binary individuals
            total_distance = 0.0
            comparisons = 0

            for i in range(len(population)):
                for j in range(i + 1, len(population)):
                    # Calculate Hamming distance
                    distance = sum(1 for a, b in zip(population[i], population[j]) if a != b)
                    total_distance += distance / self.n_variables  # Normalize by number of variables
                    comparisons += 1

            diversity = total_distance / comparisons if comparisons > 0 else 0.0

            log_calculation_result(self.log_callback, "population_diversity", diversity, "ratio")
            log_function_exit(self.log_callback, "_calculate_population_diversity", f"diversity={diversity:.4f}")

            return diversity

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_population_diversity")
            return 0.5  # Default moderate diversity

    def _calculate_convergence_rate(self, current_best_fitness: float) -> float:
        """
        Calculate convergence rate based on fitness improvement history.

        Args:
            current_best_fitness: Current best fitness value

        Returns:
            float: Convergence rate (improvement per generation)
        """
        log_function_entry(self.log_callback, "_calculate_convergence_rate",
                          current_fitness=current_best_fitness)

        try:
            self.best_fitness_history.append(current_best_fitness)

            # Keep only recent history for convergence calculation
            max_history = min(10, self.config.stagnation_generations)
            if len(self.best_fitness_history) > max_history:
                self.best_fitness_history = self.best_fitness_history[-max_history:]

            if len(self.best_fitness_history) < 2:
                log_function_exit(self.log_callback, "_calculate_convergence_rate", "insufficient_history")
                return 0.0

            # Calculate average improvement rate
            improvements = []
            for i in range(1, len(self.best_fitness_history)):
                prev_fitness = self.best_fitness_history[i-1]
                curr_fitness = self.best_fitness_history[i]
                if prev_fitness > 0:  # Avoid division by zero
                    improvement = (prev_fitness - curr_fitness) / prev_fitness
                    improvements.append(max(0, improvement))  # Only positive improvements

            convergence_rate = sum(improvements) / len(improvements) if improvements else 0.0

            log_calculation_result(self.log_callback, "convergence_rate", convergence_rate, "improvement/gen")
            log_function_exit(self.log_callback, "_calculate_convergence_rate", f"rate={convergence_rate:.6f}")

            return convergence_rate

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_convergence_rate")
            return 0.0

    def _adapt_parameters(self, generation: int, population_diversity: float,
                         convergence_rate: float) -> None:
        """
        Adapt algorithm parameters based on population state and convergence.

        Args:
            generation: Current generation number
            population_diversity: Current population diversity measure
            convergence_rate: Current convergence rate
        """
        log_function_entry(self.log_callback, "_adapt_parameters",
                          generation=generation, diversity=population_diversity,
                          convergence_rate=convergence_rate)

        if not self.config.adaptive_parameters:
            log_function_exit(self.log_callback, "_adapt_parameters", "disabled")
            return

        try:
            original_crossover = self.current_crossover_prob
            original_mutation = self.current_mutation_prob

            # Adapt crossover probability based on diversity
            if population_diversity < self.config.diversity_threshold:
                # Low diversity - increase crossover to promote exploration
                self.current_crossover_prob = min(0.95, self.config.crossover_prob + 0.1)
                enhanced_log(self.log_callback, f"Low diversity ({population_diversity:.4f}) - increased crossover to {self.current_crossover_prob:.3f}", 'DEBUG')
            else:
                # High diversity - use normal crossover
                self.current_crossover_prob = self.config.crossover_prob

            # Adapt mutation probability based on convergence
            if convergence_rate < 0.001:  # Very slow convergence
                # Increase mutation for more exploration
                self.current_mutation_prob = min(0.3, self.config.mutation_prob + 0.05)
                enhanced_log(self.log_callback, f"Slow convergence ({convergence_rate:.6f}) - increased mutation to {self.current_mutation_prob:.3f}", 'DEBUG')
            elif convergence_rate > 0.01:  # Fast convergence
                # Decrease mutation for exploitation
                self.current_mutation_prob = max(0.05, self.config.mutation_prob - 0.02)
                enhanced_log(self.log_callback, f"Fast convergence ({convergence_rate:.6f}) - decreased mutation to {self.current_mutation_prob:.3f}", 'DEBUG')
            else:
                # Normal convergence - use default mutation
                self.current_mutation_prob = self.config.mutation_prob

            # Log parameter changes
            if abs(self.current_crossover_prob - original_crossover) > 0.001 or \
               abs(self.current_mutation_prob - original_mutation) > 0.001:
                log_performance_metric(self.log_callback, "crossover_prob_adapted", self.current_crossover_prob, "probability")
                log_performance_metric(self.log_callback, "mutation_prob_adapted", self.current_mutation_prob, "probability")
                enhanced_log(self.log_callback, f"Parameters adapted: crossover={self.current_crossover_prob:.3f}, mutation={self.current_mutation_prob:.3f}", 'INFO')

            log_function_exit(self.log_callback, "_adapt_parameters", "completed")

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_adapt_parameters")

    def _check_convergence(self, current_best_fitness: float, generation: int) -> bool:
        """
        Check if the optimization has converged based on multiple criteria.

        Args:
            current_best_fitness: Current best fitness value
            generation: Current generation number

        Returns:
            bool: True if convergence is detected, False otherwise
        """
        log_function_entry(self.log_callback, "_check_convergence",
                          fitness=current_best_fitness, generation=generation)

        if not self.config.early_stopping:
            log_function_exit(self.log_callback, "_check_convergence", "disabled")
            return False

        try:
            # Update convergence history
            if self.convergence_history is not None:
                self.convergence_history.append(current_best_fitness)

            # Check if we have enough history for convergence analysis
            if len(self.best_fitness_history) < self.config.stagnation_generations:
                log_function_exit(self.log_callback, "_check_convergence", "insufficient_history")
                return False

            # Check for stagnation (no significant improvement)
            recent_history = self.best_fitness_history[-self.config.stagnation_generations:]
            best_recent = min(recent_history)
            worst_recent = max(recent_history)

            # Calculate relative improvement
            if best_recent > 0:
                relative_improvement = (worst_recent - best_recent) / best_recent
            else:
                relative_improvement = abs(worst_recent - best_recent)

            log_calculation_result(self.log_callback, "relative_improvement", relative_improvement, "ratio")

            # Check convergence threshold
            converged = relative_improvement < self.config.convergence_threshold

            if converged:
                self.stagnation_counter += 1
                enhanced_log(self.log_callback, f"Stagnation detected: improvement={relative_improvement:.8f} < threshold={self.config.convergence_threshold:.8f}", 'INFO')
                enhanced_log(self.log_callback, f"Stagnation counter: {self.stagnation_counter}/{self.config.stagnation_generations}", 'INFO')
            else:
                self.stagnation_counter = 0

            # Check if we should stop
            should_stop = self.stagnation_counter >= self.config.stagnation_generations

            if should_stop:
                enhanced_log(self.log_callback, f"Early stopping triggered after {generation} generations", 'INFO')
                log_performance_metric(self.log_callback, "early_stopping_generation", generation, "generation")
                log_validation_result(self.log_callback, "convergence_detected", True,
                                    f"Converged at generation {generation} with fitness {current_best_fitness:.6f}")

            log_constraint_check(self.log_callback, "convergence_threshold", relative_improvement,
                               self.config.convergence_threshold, relative_improvement < self.config.convergence_threshold)

            log_function_exit(self.log_callback, "_check_convergence", f"converged={should_stop}")
            return should_stop

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_check_convergence")
            return False

    def _update_performance_metrics(self, generation: int, population: List,
                                   generation_time: float) -> None:
        """
        Update performance metrics for monitoring and analysis.

        Args:
            generation: Current generation number
            population: Current population
            generation_time: Time taken for this generation
        """
        log_function_entry(self.log_callback, "_update_performance_metrics",
                          generation=generation, population_size=len(population) if population else 0,
                          generation_time=generation_time)

        if not self.config.performance_tracking:
            log_function_exit(self.log_callback, "_update_performance_metrics", "disabled")
            return

        try:
            # Update generation times
            if self.generation_times is not None:
                self.generation_times.append(generation_time)

                # Log performance metrics
                if generation % 10 == 0:  # Log every 10 generations
                    avg_time = sum(self.generation_times) / len(self.generation_times)
                    log_performance_metric(self.log_callback, "avg_generation_time", avg_time, "seconds")
                    log_performance_metric(self.log_callback, "current_generation_time", generation_time, "seconds")

            # Calculate and store population diversity
            if self.diversity_history is not None and population:
                diversity = self._calculate_population_diversity(population)
                self.diversity_history.append(diversity)

                if generation % 10 == 0:  # Log every 10 generations
                    log_performance_metric(self.log_callback, "population_diversity", diversity, "ratio")

            # Store best solutions
            if self.best_solutions_history is not None and population:
                # Find best individual in current population
                best_individual = None
                best_fitness = float('inf')

                for ind in population:
                    if hasattr(ind.fitness, 'values') and ind.fitness.values:
                        fitness_sum = sum(ind.fitness.values)
                        if fitness_sum < best_fitness:
                            best_fitness = fitness_sum
                            best_individual = list(ind)

                if best_individual is not None:
                    self.best_solutions_history.append({
                        'generation': generation,
                        'fitness': best_fitness,
                        'solution': best_individual
                    })

            log_function_exit(self.log_callback, "_update_performance_metrics", "completed")

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_update_performance_metrics")

    def _calculate_constraint_violations(self, individual: List[int],
                                       grid_positions: List, min_spacing: float) -> Dict[str, float]:
        """
        Calculate constraint violations for an individual solution.

        Args:
            individual: Binary individual representing pile selection
            grid_positions: Available grid positions
            min_spacing: Minimum spacing requirement

        Returns:
            Dictionary containing constraint violation measures
        """
        log_function_entry(self.log_callback, "_calculate_constraint_violations",
                          individual_length=len(individual) if individual else 0,
                          min_spacing=min_spacing)

        try:
            violations = {
                'spacing_violations': 0.0,
                'boundary_violations': 0.0,
                'count_violations': 0.0,
                'total_violation': 0.0
            }

            if not individual or not grid_positions:
                log_function_exit(self.log_callback, "_calculate_constraint_violations", "empty_input")
                return violations

            # Get selected pile positions
            selected_indices = [i for i, x in enumerate(individual) if bool(x)]

            if not selected_indices:
                log_function_exit(self.log_callback, "_calculate_constraint_violations", "no_selection")
                return violations

            # Calculate spacing violations
            spacing_violation_count = 0
            min_distance_found = float('inf')

            for i, idx1 in enumerate(selected_indices):
                for idx2 in selected_indices[i+1:]:
                    if hasattr(self, 'repair_operators') and self.repair_operators:
                        distance = self.repair_operators._distance_matrix[idx1, idx2]
                    else:
                        # Calculate distance manually if no repair operators
                        pos1 = grid_positions[idx1]
                        pos2 = grid_positions[idx2]
                        distance = sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

                    min_distance_found = min(min_distance_found, distance)

                    if distance < min_spacing:
                        spacing_violation_count += 1
                        violations['spacing_violations'] += (min_spacing - distance) / min_spacing

            # Normalize spacing violations
            if len(selected_indices) > 1:
                max_possible_violations = len(selected_indices) * (len(selected_indices) - 1) // 2
                violations['spacing_violations'] = violations['spacing_violations'] / max_possible_violations

            # Calculate total violation
            violations['total_violation'] = (violations['spacing_violations'] +
                                           violations['boundary_violations'] +
                                           violations['count_violations'])

            log_calculation_result(self.log_callback, "spacing_violations", violations['spacing_violations'], "ratio")
            log_calculation_result(self.log_callback, "total_constraint_violation", violations['total_violation'], "ratio")

            log_function_exit(self.log_callback, "_calculate_constraint_violations",
                            f"total_violation={violations['total_violation']:.4f}")

            return violations

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_constraint_violations")
            return {'spacing_violations': 1.0, 'boundary_violations': 0.0,
                   'count_violations': 0.0, 'total_violation': 1.0}

    def _apply_constraint_domination(self, individual1: List[int], individual2: List[int],
                                   grid_positions: List, min_spacing: float) -> int:
        """
        Apply constraint domination comparison between two individuals.

        Args:
            individual1: First individual for comparison
            individual2: Second individual for comparison
            grid_positions: Available grid positions
            min_spacing: Minimum spacing requirement

        Returns:
            int: -1 if individual1 dominates, 1 if individual2 dominates, 0 if non-dominated
        """
        log_function_entry(self.log_callback, "_apply_constraint_domination")

        if not self.config.constraint_domination:
            log_function_exit(self.log_callback, "_apply_constraint_domination", "disabled")
            return 0

        try:
            # Calculate constraint violations for both individuals
            violations1 = self._calculate_constraint_violations(individual1, grid_positions, min_spacing)
            violations2 = self._calculate_constraint_violations(individual2, grid_positions, min_spacing)

            total_violation1 = violations1['total_violation']
            total_violation2 = violations2['total_violation']

            # Constraint domination rules:
            # 1. Feasible solution dominates infeasible solution
            # 2. Among infeasible solutions, one with less violation dominates
            # 3. Among feasible solutions, use objective domination

            feasible1 = total_violation1 < 1e-6
            feasible2 = total_violation2 < 1e-6

            if feasible1 and not feasible2:
                # Individual1 is feasible, individual2 is not
                log_function_exit(self.log_callback, "_apply_constraint_domination", "ind1_feasible_dominates")
                return -1
            elif not feasible1 and feasible2:
                # Individual2 is feasible, individual1 is not
                log_function_exit(self.log_callback, "_apply_constraint_domination", "ind2_feasible_dominates")
                return 1
            elif not feasible1 and not feasible2:
                # Both infeasible - compare violations
                if total_violation1 < total_violation2:
                    log_function_exit(self.log_callback, "_apply_constraint_domination", "ind1_less_violation")
                    return -1
                elif total_violation1 > total_violation2:
                    log_function_exit(self.log_callback, "_apply_constraint_domination", "ind2_less_violation")
                    return 1
                else:
                    log_function_exit(self.log_callback, "_apply_constraint_domination", "equal_violations")
                    return 0
            else:
                # Both feasible - use objective domination (handled elsewhere)
                log_function_exit(self.log_callback, "_apply_constraint_domination", "both_feasible")
                return 0

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_apply_constraint_domination")
            return 0

    def _calculate_crowding_distance(self, population: List) -> List[float]:
        """
        Calculate crowding distance for diversity preservation.

        Args:
            population: List of individuals in the population

        Returns:
            List of crowding distances for each individual
        """
        log_function_entry(self.log_callback, "_calculate_crowding_distance",
                          population_size=len(population) if population else 0)

        try:
            if not population or len(population) < 3:
                # Return maximum distance for small populations
                distances = [float('inf')] * len(population) if population else []
                log_function_exit(self.log_callback, "_calculate_crowding_distance", "small_population")
                return distances

            n_individuals = len(population)
            distances = [0.0] * n_individuals

            # Calculate crowding distance for each objective
            for obj_idx in range(self.n_objectives):
                # Sort population by objective value
                sorted_indices = sorted(range(n_individuals),
                                      key=lambda i: population[i].fitness.values[obj_idx]
                                      if hasattr(population[i].fitness, 'values') and population[i].fitness.values
                                      else float('inf'))

                # Set boundary points to infinite distance
                distances[sorted_indices[0]] = float('inf')
                distances[sorted_indices[-1]] = float('inf')

                # Calculate objective range
                obj_values = [population[i].fitness.values[obj_idx]
                            for i in sorted_indices
                            if hasattr(population[i].fitness, 'values') and population[i].fitness.values]

                if len(obj_values) > 2:
                    obj_range = max(obj_values) - min(obj_values)

                    if obj_range > 0:
                        # Calculate crowding distance for intermediate points
                        for i in range(1, len(sorted_indices) - 1):
                            if distances[sorted_indices[i]] != float('inf'):
                                prev_obj = population[sorted_indices[i-1]].fitness.values[obj_idx]
                                next_obj = population[sorted_indices[i+1]].fitness.values[obj_idx]
                                distances[sorted_indices[i]] += abs(next_obj - prev_obj) / obj_range

            # Apply crowding distance weight
            if self.config.diversity_preservation:
                distances = [d * self.config.crowding_distance_weight for d in distances]

            log_calculation_result(self.log_callback, "avg_crowding_distance",
                                 sum(d for d in distances if d != float('inf')) / len([d for d in distances if d != float('inf')])
                                 if any(d != float('inf') for d in distances) else 0.0, "distance")

            log_function_exit(self.log_callback, "_calculate_crowding_distance", "completed")
            return distances

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_crowding_distance")
            return [0.0] * len(population) if population else []

    def _preserve_population_diversity(self, population: List, target_size: int) -> List:
        """
        Preserve population diversity using crowding distance and other mechanisms.

        Args:
            population: Current population
            target_size: Target population size

        Returns:
            Population with preserved diversity
        """
        log_function_entry(self.log_callback, "_preserve_population_diversity",
                          current_size=len(population) if population else 0,
                          target_size=target_size)

        if not self.config.diversity_preservation:
            log_function_exit(self.log_callback, "_preserve_population_diversity", "disabled")
            return population[:target_size] if population else []

        try:
            if not population or len(population) <= target_size:
                log_function_exit(self.log_callback, "_preserve_population_diversity", "no_reduction_needed")
                return population

            # Calculate crowding distances
            crowding_distances = self._calculate_crowding_distance(population)

            # Calculate population diversity
            current_diversity = self._calculate_population_diversity(population)

            # If diversity is too low, prioritize diverse individuals
            if current_diversity < self.config.diversity_threshold:
                enhanced_log(self.log_callback, f"Low diversity ({current_diversity:.4f}) - prioritizing diverse individuals", 'DEBUG')

                # Sort by crowding distance (descending) to prefer diverse individuals
                sorted_indices = sorted(range(len(population)),
                                      key=lambda i: crowding_distances[i], reverse=True)

                preserved_population = [population[i] for i in sorted_indices[:target_size]]
            else:
                # Normal selection - balance fitness and diversity
                # Combine fitness and crowding distance for selection
                combined_scores = []
                for i, ind in enumerate(population):
                    if hasattr(ind.fitness, 'values') and ind.fitness.values:
                        fitness_score = sum(ind.fitness.values)
                        diversity_score = crowding_distances[i] if crowding_distances[i] != float('inf') else 1000.0
                        # Lower fitness is better, higher diversity is better
                        combined_score = fitness_score - 0.1 * diversity_score  # Weight diversity
                        combined_scores.append((combined_score, i))
                    else:
                        combined_scores.append((float('inf'), i))

                # Sort by combined score and select best
                combined_scores.sort(key=lambda x: x[0])
                preserved_population = [population[idx] for _, idx in combined_scores[:target_size]]

            # Verify diversity preservation
            final_diversity = self._calculate_population_diversity(preserved_population)

            log_performance_metric(self.log_callback, "diversity_before_preservation", current_diversity, "ratio")
            log_performance_metric(self.log_callback, "diversity_after_preservation", final_diversity, "ratio")

            enhanced_log(self.log_callback, f"Population diversity: {current_diversity:.4f} → {final_diversity:.4f}", 'DEBUG')

            log_function_exit(self.log_callback, "_preserve_population_diversity",
                            f"preserved_size={len(preserved_population)}")

            return preserved_population

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_preserve_population_diversity")
            return population[:target_size] if population else []

    def _monitor_population_quality(self, population: List, generation: int) -> Dict[str, float]:
        """
        Monitor population quality metrics for analysis and adaptation.

        Args:
            population: Current population
            generation: Current generation number

        Returns:
            Dictionary containing population quality metrics
        """
        log_function_entry(self.log_callback, "_monitor_population_quality",
                          population_size=len(population) if population else 0,
                          generation=generation)

        try:
            quality_metrics = {
                'avg_fitness': 0.0,
                'best_fitness': float('inf'),
                'worst_fitness': 0.0,
                'fitness_std': 0.0,
                'diversity': 0.0,
                'feasible_ratio': 0.0
            }

            if not population:
                log_function_exit(self.log_callback, "_monitor_population_quality", "empty_population")
                return quality_metrics

            # Calculate fitness statistics
            fitness_values = []
            feasible_count = 0

            for ind in population:
                if hasattr(ind.fitness, 'values') and ind.fitness.values:
                    fitness_sum = sum(ind.fitness.values)
                    fitness_values.append(fitness_sum)

                    # Check feasibility (assuming fitness < 1000 indicates feasible)
                    if fitness_sum < 1000.0:
                        feasible_count += 1

            if fitness_values:
                quality_metrics['avg_fitness'] = sum(fitness_values) / len(fitness_values)
                quality_metrics['best_fitness'] = min(fitness_values)
                quality_metrics['worst_fitness'] = max(fitness_values)

                if len(fitness_values) > 1:
                    variance = sum((f - quality_metrics['avg_fitness'])**2 for f in fitness_values) / len(fitness_values)
                    quality_metrics['fitness_std'] = sqrt(variance)

                quality_metrics['feasible_ratio'] = feasible_count / len(population)

            # Calculate diversity
            quality_metrics['diversity'] = self._calculate_population_diversity(population)

            # Log quality metrics periodically
            if generation % 10 == 0:
                log_performance_metric(self.log_callback, "population_avg_fitness", quality_metrics['avg_fitness'], "fitness")
                log_performance_metric(self.log_callback, "population_best_fitness", quality_metrics['best_fitness'], "fitness")
                log_performance_metric(self.log_callback, "population_diversity", quality_metrics['diversity'], "ratio")
                log_performance_metric(self.log_callback, "population_feasible_ratio", quality_metrics['feasible_ratio'], "ratio")

                enhanced_log(self.log_callback, f"Population quality - Avg: {quality_metrics['avg_fitness']:.3f}, "
                                              f"Best: {quality_metrics['best_fitness']:.3f}, "
                                              f"Diversity: {quality_metrics['diversity']:.3f}, "
                                              f"Feasible: {quality_metrics['feasible_ratio']:.1%}", 'DEBUG')

            log_function_exit(self.log_callback, "_monitor_population_quality", "completed")
            return quality_metrics

        except Exception as e:
            log_error_with_context(self.log_callback, e, "_monitor_population_quality")
            return quality_metrics

    def _setup_deap_types(self):
        """Setup DEAP creator types."""
        # Create unique class names to avoid conflicts
        fitness_name = f"FitnessMin_{id(self)}"
        individual_name = f"Individual_{id(self)}"
        
        # Clear existing types if they exist
        try:
            if hasattr(creator, fitness_name):
                delattr(creator, fitness_name)
            if hasattr(creator, individual_name):
                delattr(creator, individual_name)
        except AttributeError:
            pass
        
        # Create fitness class (minimization for all objectives)
        creator.create(fitness_name, base.Fitness, weights=(-1.0,) * self.n_objectives)
          # Create individual class
        creator.create(individual_name, list, fitness=getattr(creator, fitness_name))
        
        # Store references for later use
        self.fitness_class = getattr(creator, fitness_name)
        self.individual_class = getattr(creator, individual_name)
    
    def _setup_toolbox(self):
        """Setup DEAP toolbox with genetic operators."""
        # Individual creation
        self.toolbox.register("attr_bool", random.randint, 0, 1)
        self.toolbox.register("individual", tools.initRepeat, self.individual_class, 
                            self.toolbox.attr_bool, n=self.n_variables)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # Genetic operators
        self.toolbox.register("mate", tools.cxTwoPoint)
        self.toolbox.register("mutate", tools.mutFlipBit, indpb=self.config.mutation_indpb)
        self.toolbox.register("select", selNSGA3, ref_points=None)  # Will be set later    
    def _generate_reference_points(self) -> np.ndarray:
        """
        Generate uniformly distributed reference points on a simplex for NSGA-III.
        Uses Das and Dennis approach for uniform reference point distribution.
        """
        log_function_entry(self.log_callback, "_generate_reference_points")
        
        try:
            # Calculate number of divisions for each objective
            n_divisions = max(1, min(10, int(100 / self.n_objectives)))
            enhanced_log(self.log_callback, f"Using {n_divisions} divisions for {self.n_objectives} objectives", 'DEBUG')
            
            # Generate structured reference points using Das and Dennis approach
            enhanced_log(self.log_callback, "Generating Das-Dennis reference points", 'DEBUG')
            ref_points = self._generate_das_dennis_points(self.n_objectives, n_divisions)
            
            # Validate generated reference points
            if ref_points is not None and hasattr(ref_points, 'size') and ref_points.size > 0:
                # Ensure we have a reasonable number of reference points
                min_points = max(self.n_objectives + 1, 10)
                enhanced_log(self.log_callback, f"Generated {len(ref_points)} Das-Dennis points", 'DEBUG')
                
                if len(ref_points) < min_points:
                    # Add additional points if needed
                    enhanced_log(self.log_callback, f"Adding {min_points - len(ref_points)} additional reference points", 'DEBUG')
                    additional_points = self._generate_additional_reference_points(min_points - len(ref_points))
                    if additional_points is not None and additional_points.size > 0:
                        ref_points = np.vstack([ref_points, additional_points])
                        enhanced_log(self.log_callback, f"Total reference points after addition: {len(ref_points)}", 'DEBUG')
                
                log_validation_result(self.log_callback, "Reference point generation",
                                    len(ref_points) >= min_points,
                                    f"Generated {len(ref_points)} points")
                log_function_exit(self.log_callback, "_generate_reference_points", f"{len(ref_points)} points")
                return ref_points
            else:
                # Generate default reference points if none were created
                enhanced_log(self.log_callback, "Das-Dennis generation failed, using default approach", 'WARNING')
                result = self._generate_default_reference_points()
                log_function_exit(self.log_callback, "_generate_reference_points", f"{len(result)} default points")
                return result
                
        except Exception as e:
            # Fallback to default reference points on any error
            enhanced_log(self.log_callback, f"Reference point generation failed: {str(e)}", 'ERROR')
            result = self._generate_default_reference_points()
            log_function_exit(self.log_callback, "_generate_reference_points", f"{len(result)} fallback points")
            return result
    
    def _generate_das_dennis_points(self, n_obj: int, n_divisions: int) -> np.ndarray:
        """Generate Das-Dennis reference points manually."""
        def generate_recursive(n_obj, n_divisions, current_sum=0, current_point=None):
            if current_point is None:
                current_point = []
            
            if n_obj == 1:
                current_point.append(n_divisions - current_sum)
                return [current_point[:]]
            
            points = []
            for i in range(n_divisions - current_sum + 1):
                new_point = current_point + [i]
                points.extend(generate_recursive(n_obj - 1, n_divisions, current_sum + i, new_point))
            
            return points
        
        try:
            points = generate_recursive(n_obj, n_divisions)
            ref_points = np.array(points, dtype=float) / n_divisions
            return ref_points
        except Exception:
            # Fallback to simple uniform generation
            n_points = max(20, n_obj * 5)
            return np.random.uniform(0, 1, (n_points, n_obj))
    
    def _generate_additional_reference_points(self, n_additional: int) -> np.ndarray:
        """Generate additional reference points to meet minimum requirements."""
        try:
            # Generate random points and normalize to simplex
            additional_points = np.random.uniform(0, 1, (n_additional, self.n_objectives))
            additional_points = additional_points / np.sum(additional_points, axis=1, keepdims=True)
            return additional_points
        except Exception:
            return np.array([]).reshape(0, self.n_objectives)

    def _generate_default_reference_points(self) -> np.ndarray:
        """Generate default reference points when other methods fail."""
        # Create simple uniform reference points
        n_points = max(20, self.n_objectives * 5)
        ref_points = np.random.uniform(0, 1, (n_points, self.n_objectives))
        
        # Normalize to sum to 1 (simplex constraint)
        ref_points = ref_points / np.sum(ref_points, axis=1, keepdims=True)
        
        return ref_points
    
    def optimize(self, evaluation_func, eval_kwargs=None, **kwargs):
        """
        Enhanced main optimization method using DEAP NSGA-III with advanced features.

        This method implements the complete enhanced NSGA-III optimization workflow including:
        - Adaptive parameter control based on population diversity and convergence
        - Advanced convergence detection with early stopping
        - Comprehensive performance monitoring and metrics tracking
        - Enhanced constraint handling with domination principles
        - Population diversity preservation mechanisms

        Args:
            evaluation_func: Function to evaluate individuals
            eval_kwargs: Additional arguments for evaluation function
            **kwargs: Additional optimization parameters including:
                - grid_positions: Available grid positions for pile placement
                - min_spacing: Minimum spacing between piles
                - target_count: Target number of piles
                - log_callback: Logging callback function

        Returns:
            Tuple of (best_individual, pareto_front, optimization_metrics)
            - best_individual: Best solution found
            - pareto_front: Set of non-dominated solutions
            - optimization_metrics: Dictionary containing performance metrics

        Raises:
            OptimizationError: If optimization fails
        """
        # Extract optimization parameters
        grid_positions = kwargs.get('grid_positions', [])
        min_spacing = kwargs.get('min_spacing', 1.5)
        target_count = kwargs.get('target_count', 10)
        log_callback = kwargs.get('log_callback', self.log_callback)

        log_function_entry(log_callback, "optimize",
                          evaluation_func=evaluation_func.__name__ if hasattr(evaluation_func, '__name__') else 'function',
                          grid_positions=len(grid_positions),
                          min_spacing=min_spacing,
                          target_count=target_count,
                          population_size=self.config.population_size,
                          generations=self.config.generations)

        # Input validation with constraint checking
        log_constraint_check(log_callback, "evaluation_func_provided", 1 if evaluation_func else 0, 1, evaluation_func is not None)
        if evaluation_func is None:
            enhanced_log(log_callback, "Evaluation function is required", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise InputDataError("Evaluation function is required")

        if eval_kwargs is None:
            eval_kwargs = {}

        log_constraint_check(log_callback, "grid_positions_provided", len(grid_positions), 0, len(grid_positions) > 0)
        if not grid_positions:
            enhanced_log(log_callback, "Grid positions are required for optimization", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise InputDataError("Grid positions are required for optimization")

        log_constraint_check(log_callback, "min_spacing_positive", min_spacing, 0, min_spacing > 0)
        if min_spacing <= 0:
            enhanced_log(log_callback, "Minimum spacing must be positive", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise InputDataError("Minimum spacing must be positive")

        log_constraint_check(log_callback, "target_count_positive", target_count, 0, target_count > 0)
        if target_count <= 0:
            enhanced_log(log_callback, "Target count must be positive", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise InputDataError("Target count must be positive")

        log_constraint_check(log_callback, "target_count_feasible", target_count, len(grid_positions), target_count <= len(grid_positions))
        if target_count > len(grid_positions):
            enhanced_log(log_callback, f"Target count ({target_count}) exceeds available positions ({len(grid_positions)})", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise InputDataError("Target count cannot exceed available grid positions")

        # Log optimization start
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Starting DEAP NSGA-III optimization")
        enhanced_log(log_callback, f"Starting DEAP NSGA-III optimization...", 'INFO')
        enhanced_log(log_callback, f"Population: {self.config.population_size}, Generations: {self.config.generations}", 'INFO')
        enhanced_log(log_callback, f"Grid positions: {len(grid_positions)}, Target piles: {target_count}", 'INFO')
        enhanced_log(log_callback, f"Reference points: {len(self.ref_points)}", 'INFO')
        enhanced_log(log_callback, f"Crossover probability: {self.config.crossover_prob}, Mutation probability: {self.config.mutation_prob}", 'DEBUG')

        # Initialize repair operators
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Initializing repair operators")
        enhanced_log(log_callback, "Initializing repair operators", 'DEBUG')
        self.repair_operators = PileRepairOperators(grid_positions, min_spacing, target_count, log_callback)

        # Register evaluation function
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Setting up evaluation function")
        enhanced_log(log_callback, "Setting up evaluation function with repair", 'DEBUG')
        evaluate_func = self._evaluate_with_repair(evaluation_func, self.repair_operators, log_callback)
        self.toolbox.register("evaluate", evaluate_func)

        # Update selection with reference points
        enhanced_log(log_callback, "Registering NSGA-III selection with reference points", 'DEBUG')
        self.toolbox.register("select", selNSGA3, ref_points=self.ref_points)

        # Create statistics with safe handling
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Setting up statistics tracking")
        enhanced_log(log_callback, "Setting up statistics tracking", 'DEBUG')

        def safe_fitness_extraction(ind):
            """Safely extract fitness values from individual."""
            if hasattr(ind, 'fitness') and hasattr(ind.fitness, 'values') and ind.fitness.values:
                return ind.fitness.values
            else:
                return (float('inf'),) * self.n_objectives

        stats = tools.Statistics(safe_fitness_extraction)
        stats.register("avg", lambda x: np.mean(x, axis=0) if x else [0.0] * self.n_objectives)
        stats.register("std", lambda x: np.std(x, axis=0) if len(x) > 1 else [0.0] * self.n_objectives)
        stats.register("min", lambda x: np.min(x, axis=0) if x else [float('inf')] * self.n_objectives)
        stats.register("max", lambda x: np.max(x, axis=0) if x else [0.0] * self.n_objectives)

        # Create hall of fame
        enhanced_log(log_callback, "Creating Pareto front hall of fame", 'DEBUG')
        hof = tools.ParetoFront()

        # Initialize population
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Initializing population")
        enhanced_log(log_callback, f"Initializing population with {self.config.population_size} individuals", 'DEBUG')
        with create_timed_logger(log_callback, "population_initialization") as timer:
            population = self.toolbox.population(n=self.config.population_size)

        enhanced_log(log_callback, f"Population initialized with {len(population)} individuals", 'INFO')
        log_performance_metric(log_callback, "initial_population_size", len(population), "individuals")
        enhanced_log(log_callback, f"Starting evolutionary algorithm with {self.config.generations} generations", 'INFO')

        # Run evolution with progress tracking
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Starting evolutionary algorithm")
        enhanced_log(log_callback, "Starting evolutionary algorithm execution", 'INFO')

        # Enhanced evolutionary algorithm with adaptive features
        with create_timed_logger(log_callback, "enhanced_evolutionary_algorithm") as timer:
            enhanced_log(log_callback, "Starting enhanced evolutionary algorithm with adaptive features", 'INFO')

            # Initialize optimization metrics
            optimization_metrics = {
                'generations_completed': 0,
                'early_stopping_triggered': False,
                'convergence_generation': None,
                'final_diversity': 0.0,
                'total_evaluations': 0,
                'adaptive_parameter_changes': 0
            }

            # Main evolutionary loop with enhanced features
            final_pop = population
            logbook = tools.Logbook()
            logbook.header = ['gen', 'nevals'] + (stats.fields if stats else [])

            for generation in range(self.config.generations):
                generation_start_time = time.time()

                # Log generation start
                if generation % 10 == 0 or generation < 5:
                    enhanced_log(log_callback, f"Generation {generation}/{self.config.generations}", 'INFO')

                # Evaluate population
                invalid_ind = [ind for ind in final_pop if not ind.fitness.valid]
                fitnesses = self.toolbox.map(evaluate_func, invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit

                # Update hall of fame
                if hof is not None:
                    hof.update(final_pop)

                # Calculate current best fitness for convergence checking
                current_best_fitness = float('inf')
                if final_pop:
                    for ind in final_pop:
                        if hasattr(ind.fitness, 'values') and ind.fitness.values:
                            fitness_sum = sum(ind.fitness.values)
                            if fitness_sum < current_best_fitness:
                                current_best_fitness = fitness_sum

                # Calculate population diversity
                population_diversity = self._calculate_population_diversity(final_pop)

                # Calculate convergence rate
                convergence_rate = self._calculate_convergence_rate(current_best_fitness)

                # Adaptive parameter control
                if self.config.adaptive_parameters:
                    old_crossover = self.current_crossover_prob
                    old_mutation = self.current_mutation_prob
                    self._adapt_parameters(generation, population_diversity, convergence_rate)

                    # Update toolbox with new parameters if they changed
                    if (abs(self.current_crossover_prob - old_crossover) > 0.001 or
                        abs(self.current_mutation_prob - old_mutation) > 0.001):
                        optimization_metrics['adaptive_parameter_changes'] += 1
                        # Re-register operators with new probabilities
                        self.toolbox.register("mutate", tools.mutFlipBit, indpb=self.current_mutation_indpb)

                # Check for convergence and early stopping
                if self._check_convergence(current_best_fitness, generation):
                    optimization_metrics['early_stopping_triggered'] = True
                    optimization_metrics['convergence_generation'] = generation
                    enhanced_log(log_callback, f"Early stopping at generation {generation}", 'INFO')
                    break

                # Selection for next generation
                if generation < self.config.generations - 1:  # Don't select on last generation
                    # Update reference points for selection
                    self.toolbox.select.keywords['ref_points'] = self.ref_points
                    final_pop = self.toolbox.select(final_pop, self.config.population_size)

                    # Apply crossover and mutation with current probabilities
                    offspring = algorithms.varAnd(final_pop, self.toolbox,
                                                self.current_crossover_prob,
                                                self.current_mutation_prob)
                    final_pop = offspring

                # Update performance metrics
                generation_time = time.time() - generation_start_time
                self._update_performance_metrics(generation, final_pop, generation_time)

                # Record statistics
                record = stats.compile(final_pop) if stats else {}
                logbook.record(gen=generation, nevals=len(invalid_ind), **record)

                # Update generation counter
                self.generation = generation
                optimization_metrics['generations_completed'] = generation + 1

            # Final metrics
            optimization_metrics['final_diversity'] = self._calculate_population_diversity(final_pop)

            enhanced_log(log_callback, f"Enhanced evolutionary algorithm completed", 'INFO')
            enhanced_log(log_callback, f"Generations completed: {optimization_metrics['generations_completed']}", 'INFO')
            enhanced_log(log_callback, f"Early stopping: {optimization_metrics['early_stopping_triggered']}", 'INFO')
            enhanced_log(log_callback, f"Parameter adaptations: {optimization_metrics['adaptive_parameter_changes']}", 'INFO')

        log_performance_metric(log_callback, "final_population_size", len(final_pop) if final_pop else 0, "individuals")
        log_performance_metric(log_callback, "generations_completed", optimization_metrics['generations_completed'], "generations")
        log_performance_metric(log_callback, "early_stopping_triggered", 1 if optimization_metrics['early_stopping_triggered'] else 0, "boolean")
        log_performance_metric(log_callback, "adaptive_parameter_changes", optimization_metrics['adaptive_parameter_changes'], "changes")

        # Log evaluation summary
        if hasattr(evaluate_func, 'log_summary'):
            evaluate_func.log_summary()

        log_constraint_check(log_callback, "final_population_not_empty", len(final_pop) if final_pop else 0, 0, bool(final_pop))
        if not final_pop:
            error_msg = "Optimization produced empty final population"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise OptimizationError(error_msg)

        # Extract best individual and Pareto front
        log_algorithm_step(log_callback, "NSGA3_Optimization", "Processing optimization results")
        enhanced_log(log_callback, "Processing optimization results", 'DEBUG')

        best_individual = None
        best_fitness = float('inf')
        valid_individuals = 0

        for ind in final_pop:
            if hasattr(ind.fitness, 'values') and ind.fitness.values:
                valid_individuals += 1
                overall_fitness = sum(ind.fitness.values)
                if overall_fitness < best_fitness:
                    best_fitness = overall_fitness
                    best_individual = list(ind)

        log_performance_metric(log_callback, "valid_individuals_in_final_pop", valid_individuals, "individuals")
        log_constraint_check(log_callback, "best_individual_found", 1 if best_individual else 0, 1, best_individual is not None)

        if best_individual is None:
            error_msg = "No valid best individual found in final population"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "optimize", "error")
            raise OptimizationError(error_msg)

        # Build Pareto front
        enhanced_log(log_callback, "Building Pareto front from hall of fame", 'DEBUG')
        pareto_front = [list(ind) for ind in hof] if hof else []

        # Log final results
        log_calculation_result(log_callback, "best_fitness", best_fitness, "fitness_units")
        log_calculation_result(log_callback, "pareto_front_size", len(pareto_front), "solutions")
        log_performance_metric(log_callback, "optimization_convergence", best_fitness, "fitness")

        # Count selected piles in best solution
        selected_piles = sum(1 for x in best_individual if bool(x))
        log_calculation_result(log_callback, "best_solution_pile_count", selected_piles, "piles")

        enhanced_log(log_callback, f"Optimization completed successfully", 'INFO')
        enhanced_log(log_callback, f"Best fitness: {best_fitness:.6f}", 'INFO')
        enhanced_log(log_callback, f"Best solution has {selected_piles} piles", 'INFO')
        enhanced_log(log_callback, f"Pareto front size: {len(pareto_front)}", 'INFO')

        # Add performance metrics to return
        optimization_metrics.update({
            'best_fitness': best_fitness,
            'pareto_front_size': len(pareto_front),
            'selected_piles': selected_piles,
            'generation_times': self.generation_times if self.config.performance_tracking else None,
            'diversity_history': self.diversity_history if self.config.performance_tracking else None,
            'convergence_history': self.convergence_history if self.config.convergence_tracking else None,
            'best_solutions_history': self.best_solutions_history if self.config.performance_tracking else None
        })

        log_validation_result(log_callback, "optimization_success", True,
                            f"Found best solution with fitness {best_fitness:.6f} and {len(pareto_front)} Pareto solutions")
        log_function_exit(log_callback, "optimize", f"fitness={best_fitness:.6f}")

        return best_individual, pareto_front, optimization_metrics
    
    def _evaluate_with_repair(self, evaluation_func, repair_operators, log_callback=None):
        """Create a wrapper function that applies repair before evaluation."""
        evaluation_count = 0
        repair_count = 0
        error_count = 0

        def evaluate_and_repair(individual):
            """Evaluate a single individual after applying repair operators."""
            nonlocal evaluation_count, repair_count, error_count
            evaluation_count += 1

            try:
                # Apply repair operators if available
                if repair_operators:
                    # Convert individual to list for repair operations
                    individual_list = list(individual)
                    original_count = sum(1 for x in individual_list if bool(x))
                    repaired_individual = repair_operators.repair_individual(individual_list)
                    repaired_count = sum(1 for x in repaired_individual if bool(x))

                    # Update the individual with repaired values
                    individual[:] = repaired_individual

                    if original_count != repaired_count:
                        repair_count += 1
                        if evaluation_count % 100 == 0:  # Log every 100th repair
                            enhanced_log(log_callback, f"Repair applied: {original_count} → {repaired_count} piles", 'DEBUG')

                # Evaluate the (possibly repaired) individual
                fitness = evaluation_func(individual)

                # Ensure fitness is a tuple with correct number of objectives
                if isinstance(fitness, (list, tuple)):
                    if len(fitness) == self.n_objectives:
                        if evaluation_count % 500 == 0:  # Log every 500th evaluation
                            enhanced_log(log_callback, f"Evaluation {evaluation_count}: fitness = {fitness}", 'DEBUG')
                        return fitness
                    else:
                        # Wrong number of objectives - return penalty values
                        enhanced_log(log_callback, f"Wrong number of objectives: got {len(fitness)}, expected {self.n_objectives}", 'WARNING')
                        return (1000.0,) * self.n_objectives
                else:
                    # Single value returned - expand to match objectives
                    expanded_fitness = (float(fitness),) * self.n_objectives
                    if evaluation_count % 500 == 0:  # Log every 500th evaluation
                        enhanced_log(log_callback, f"Evaluation {evaluation_count}: expanded fitness = {expanded_fitness}", 'DEBUG')
                    return expanded_fitness

            except Exception as e:
                error_count += 1
                if error_count % 10 == 0:  # Log every 10th error
                    enhanced_log(log_callback, f"Evaluation error #{error_count}: {str(e)[:100]}", 'WARNING')
                # If repair or evaluation fails, assign high penalty values
                return (1000.0,) * self.n_objectives

        # Add logging summary method
        def log_evaluation_summary():
            if log_callback:
                log_performance_metric(log_callback, "total_evaluations", evaluation_count, "evaluations")
                log_performance_metric(log_callback, "repairs_applied", repair_count, "repairs")
                log_performance_metric(log_callback, "evaluation_errors", error_count, "errors")
                repair_rate = (repair_count / evaluation_count * 100) if evaluation_count > 0 else 0
                error_rate = (error_count / evaluation_count * 100) if evaluation_count > 0 else 0
                log_performance_metric(log_callback, "repair_rate", repair_rate, "%")
                log_performance_metric(log_callback, "error_rate", error_rate, "%")
                enhanced_log(log_callback, f"Evaluation summary: {evaluation_count} total, {repair_count} repairs ({repair_rate:.1f}%), {error_count} errors ({error_rate:.1f}%)", 'INFO')

        # Store summary function for later use
        evaluate_and_repair.log_summary = log_evaluation_summary

        return evaluate_and_repair


# NOTE: ZERO FALLBACK POLICY ENFORCED - All legacy fallbacks permanently removed.
# Use DEAPNSGA3Optimizer and DEAPNSGA3Config directly for all optimization needs.

