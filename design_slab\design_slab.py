import os
import sys
import time
import warnings
from tkinter import filedialog

import numpy as np
import pandas as pd
import pyodbc
from tqdm import tqdm


def write_excel(df, path_excel_output, sheet):
    # Check if the Excel file exists
    if os.path.isfile(path_excel_output):
        # If the file exists, open it and get the existing sheets
        with pd.ExcelWriter(path_excel_output, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
            df.to_excel(writer, index=False, sheet_name=sheet)
    else:
        # If the file does not exist, create a new Workbook object
        df.to_excel(path_excel_output, sheet_name=sheet, index=False)
    return df


def write_csv(df, path_csv_output):
    # Check if the Excel file exists
    if os.path.isfile(path_csv_output):
        df.to_csv(path_csv_output, mode='w', index=False)

    else:
        # If the file does not exist, create a new Workbook object
        df.to_csv(path_csv_output, index=False)
    return df


def read_mdbs(file_paths, table_name):
    conn = pyodbc.connect(
        r'Driver={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=' + file_paths + ';')

    sql = 'SELECT * FROM [' + table_name + ']'
    df = pd.read_sql(sql, conn)

    return df


def read_excel(path_excel_input, sheetname):
    df = pd.read_excel(path_excel_input, sheet_name=sheetname)
    return df


class FilePath:
    def __init__(self, access_file=None, strip_geo=None, capdata=None, strip_csv=None, cap_csv=None,
                 cap_design_excel=None,
                 csv_outputfolder=None, grouping=None, loadcombination=None, excel_outputfolder=None,
                 prokon_outputfolder=None, loadmapping=None, wall_button=None, col_button=None):
        self.access_file = access_file
        self.strip_csv = strip_csv
        self.cap_csv = cap_csv
        self.cap_design_excel = cap_design_excel
        self.csv_outputfolder = csv_outputfolder

        self.grouping = grouping
        self.loadcombination = loadcombination
        self.prokon_outputfolder = prokon_outputfolder
        self.excel_outputfolder = excel_outputfolder
        self.loadmapping = loadmapping
        self.wall_button = wall_button
        self.col_button = col_button


class DesignData:
    def __init__(self, df_accs_pt_coordinates=None, df_accs_caps_data=None, df_accs_strip=None, df_strip_design=None,
                 cap_list=None, df_top_X_strip_parameters=None, df_top_Y_strip_parameters=None,
                 df_bot_X_strip_parameters=None, df_bot_Y_strip_parameters=None,
                 load_pattern_frame=None,
                 pier_force_table=None):
        # External Data
        self.df_accs_pt_coordinates = df_accs_pt_coordinates  # Access
        self.df_accs_caps_data = df_accs_caps_data  # Access
        self.df_accs_strip = df_accs_strip  # Access
        self.df_strip_design = df_strip_design  # Excel

        # List
        self.cap_list = cap_list

        self.df_top_X_strip_parameters = df_top_X_strip_parameters
        self.df_top_Y_strip_parameters = df_top_Y_strip_parameters
        self.df_bot_X_strip_parameters = df_bot_X_strip_parameters
        self.df_bot_Y_strip_parameters = df_bot_Y_strip_parameters

        self.load_pattern_frame = load_pattern_frame
        self.pier_force_table = pier_force_table


def safe_slab_design():
    file_path = FilePath()
    design_data = DesignData()

    print('Please select Access file')
    file_path.access_file = filedialog.askopenfilename()
    # file_path.access_file = r'C:/Users/<USER>/Desktop/AIS Automation/3. Pile Cap Design/Example 2a/A.SAFEModel(SHP).accdb'
    print('\nPlease select excel file')
    file_path.cap_design_excel = filedialog.askopenfilename()
    # file_path.cap_design_excel = r'C:/Users/<USER>/Desktop/AIS Automation/3. Pile Cap Design/Example 2a/Design2/pile_cap_design.xlsx'
    file_path.csv_outputfolder = os.path.dirname(file_path.cap_design_excel) + '/Cap Drawing'
    # Check whether the specified path exists or not
    if not os.path.exists(file_path.csv_outputfolder):
        # Create a new directory because it does not exist
        os.makedirs(file_path.csv_outputfolder)
    if not os.path.exists(file_path.csv_outputfolder + '/cap'):
        os.makedirs(file_path.csv_outputfolder + '/cap')
    if not os.path.exists(file_path.csv_outputfolder + '/x_top'):
        os.makedirs(file_path.csv_outputfolder + '/x_top')
    if not os.path.exists(file_path.csv_outputfolder + '/x_bot'):
        os.makedirs(file_path.csv_outputfolder + '/x_bot')
    if not os.path.exists(file_path.csv_outputfolder + '/y_top'):
        os.makedirs(file_path.csv_outputfolder + '/y_top')
    if not os.path.exists(file_path.csv_outputfolder + '/y_bot'):
        os.makedirs(file_path.csv_outputfolder + '/y_bot')

    # Importing data from Access File
    print("\nReading Data")
    file_path, design_data = read_external_data(file_path, design_data)

    # Get Pile_Cap_list
    for index, row in design_data.df_accs_strip.iterrows():
        split_list = row['Strip'].split('_')
        design_data.df_accs_strip.loc[index, 'Way'] = split_list[0]
        design_data.df_accs_strip.loc[index, 'Cap'] = split_list[1]
    design_data.cap_list = design_data.df_accs_strip['Cap'].unique()

    # Design data based on cap name
    print('Writing CSV')
    time.sleep(0.1)
    for cap in tqdm(design_data.cap_list, total=design_data.cap_list.size, ncols=100, maxinterval=10):
        file_path, design_data = cap_draw_parameters(file_path, design_data, cap)
        file_path, design_data = strip_draw_parameters_combined(file_path, design_data, cap)

        cap_csv = file_path.csv_outputfolder + '/cap/' + cap + '_gh_cap.csv'
        strip_x_t_csv = file_path.csv_outputfolder + '/x_top/' + cap + 'x_top_strip.csv'
        strip_x_b_csv = file_path.csv_outputfolder + '/x_bot/' + cap + 'x_bot_strip.csv'
        strip_y_t_csv = file_path.csv_outputfolder + '/y_top/' + cap + 'y_top_strip.csv'
        strip_y_b_csv = file_path.csv_outputfolder + '/y_bot/' + cap + 'y_bot_strip.csv'

        while True:
            try:
                DB = write_csv(design_data.df_cap_coor, cap_csv)
                DB = write_csv(design_data.df_top_X_strip_parameters, strip_x_t_csv)
                DB = write_csv(design_data.df_top_Y_strip_parameters, strip_y_t_csv)
                DB = write_csv(design_data.df_bot_X_strip_parameters, strip_x_b_csv)
                DB = write_csv(design_data.df_bot_Y_strip_parameters, strip_y_b_csv)
                break
            except PermissionError:
                print("Please close the Excel file.")
                time.sleep(5)  # Wait for 5 seconds before checking again


def read_external_data(file_path, design_data):
    # Cap Data
    # Set point coordinates
    try:

        table_name = 'Object Geometry - Point Coordinates'
        design_data.df_accs_pt_coordinates = read_mdbs(file_path.access_file, table_name)
    except Exception as e:
        print(f'{table_name} does not existed in access file, the program is terminated.')
        sys.exit()

    # Match Area Name with points
    try:
        table_name = 'Object Geometry - Areas 01 - General'
        df_temp_caps_data = read_mdbs(file_path.access_file, table_name)

    except Exception as e:
        print(f'{table_name} does not existed in access file, the program is terminated.')
        sys.exit()
    design_data.df_accs_caps_data = pd.DataFrame()
    design_data.df_accs_caps_data[['Cap', 'Pt1', 'Pt2', 'Pt3', 'Pt4']] = df_temp_caps_data[
        ['Area', 'Point1', 'Point2', 'Point3', 'Point4']]
    # Convert the points of Cap to a list -> Points
    df_grouped = design_data.df_accs_caps_data.groupby('Cap').apply(
        lambda x: [point for point in x[['Pt1', 'Pt2', 'Pt3', 'Pt4']].values.flatten().tolist() if
                   point is not None]).reset_index(name='Points')
    design_data.df_accs_caps_data = design_data.df_accs_caps_data.drop(['Pt1', 'Pt2', 'Pt3', 'Pt4'], axis=1)
    design_data.df_accs_caps_data = pd.merge(design_data.df_accs_caps_data, df_grouped[['Cap', 'Points']], on='Cap')
    design_data.df_accs_caps_data = design_data.df_accs_caps_data.drop_duplicates(['Cap'])

    # Strip Data
    try:
        table_name = 'Object Geometry - Design Strips'
        design_data.df_accs_strip = read_mdbs(file_path.access_file, table_name)
    except Exception as e:
        print(f'{table_name} does not existed in access file, the program is terminated.')
        sys.exit()

    # Create strip_design for Strip design parameters
    df_strip_design = read_excel(file_path.cap_design_excel, 'Strip_Design')
    design_data.df_strip_design = df_strip_design[
        ['Strip', 'GlobalX', 'GlobalY', 'Thickness', 'Top_St_Area', 'Bot_St_Area', 'Top_Rebar_Assign',
         'Bot_Rebar_Assign']]

    return file_path, design_data


def cap_draw_parameters(file_path, design_data, cap):
    # Filter Data with cap
    df_cap_data = design_data.df_accs_caps_data[design_data.df_accs_caps_data['Cap'] == cap]

    design_data.df_cap_coor = pd.DataFrame()
    for index, row in df_cap_data.iterrows():
        num = 0
        for pt in row['Points']:
            index1 = design_data.df_cap_coor.shape[0]
            num = num + 1
            name = 'pt' + str(num)
            design_data.df_cap_coor.loc[index1, 'Cap'] = row['Cap']
            design_data.df_cap_coor.loc[index1, ['x', 'y']] = match_pt(pt, design_data.df_accs_pt_coordinates)

    return file_path, design_data


def match_pt(pt_name, df_coordinates):
    # Create a dictionary mapping point names to coordinates
    coordinates_dict = df_coordinates.set_index('Point').to_dict(orient='index')

    if pt_name in coordinates_dict:
        point_data = coordinates_dict[pt_name]
        global_x = point_data['GlobalX']
        global_y = point_data['GlobalY']
        return global_x, global_y
    else:
        return None


def strip_draw_parameters_combined(file_path, design_data, cap):
    # Filter Data with cap
    df_strip = design_data.df_accs_strip[
        (design_data.df_accs_strip['Cap'] == cap) & (design_data.df_accs_strip['Way'] == 'DSX')]

    # Create strip_geo for Strip width parameters
    df_strip_geo = pd.DataFrame(columns=['Strip', 'Left_offset', 'Right_offset'])
    strip_list = df_strip['Strip'].unique()
    for strip in strip_list:
        sort_df = df_strip.loc[df_strip['Strip'] == strip].reset_index(drop=True)
        index = df_strip_geo.shape[0]
        df_strip_geo.loc[index, ['Strip', 'Left_offset', 'Right_offset']] = sort_df.loc[
            0, ['Strip', 'WALeft', 'WARight']].values

    # Strip Drawings parameters
    design_data.df_top_X_strip_parameters = strip_drawing_parameters(strip_list, design_data.df_strip_design,
                                                                     df_strip_geo, 'top')
    design_data.df_bot_X_strip_parameters = strip_drawing_parameters(strip_list, design_data.df_strip_design,
                                                                     df_strip_geo, 'bot')

    # Filter Data with cap
    df_strip = design_data.df_accs_strip[
        (design_data.df_accs_strip['Cap'] == cap) & (design_data.df_accs_strip['Way'] == 'DSY')]

    # Create strip_geo for Strip width parameters
    df_strip_geo = pd.DataFrame(columns=['Strip', 'Left_offset', 'Right_offset'])
    strip_list = df_strip['Strip'].unique()
    for strip in strip_list:
        sort_df = df_strip.loc[df_strip['Strip'] == strip].reset_index(drop=True)
        index = df_strip_geo.shape[0]
        df_strip_geo.loc[index, ['Strip', 'Left_offset', 'Right_offset']] = sort_df.loc[
            0, ['Strip', 'WALeft', 'WARight']].values

    # Strip Drawings parameters
    design_data.df_top_Y_strip_parameters = strip_drawing_parameters(strip_list, design_data.df_strip_design,
                                                                     df_strip_geo, 'top')
    design_data.df_bot_Y_strip_parameters = strip_drawing_parameters(strip_list, design_data.df_strip_design,
                                                                     df_strip_geo, 'bot')
    return file_path, design_data


def strip_drawing_parameters(strip_list, df_strip_design, df_strip_geo, layer):
    # Top Rebar design of the Cap
    df_strip_drawing = pd.DataFrame()
    if layer == "top":
        St_area = 'Top_St_Area'
        Rebar_assign = 'Top_Rebar_Assign'
    elif layer == "bot":
        St_area = 'Bot_St_Area'
        Rebar_assign = 'Bot_Rebar_Assign'

    for strip in strip_list:
        strip_data = df_strip_design[df_strip_design['Strip'] == strip].reset_index(drop=True)
        count = strip_data.shape[0]
        St_x, St_y, Ed_x, Ed_y = 0, 0, 0, 0
        steel_design = ''
        for index, row in strip_data.iterrows():
            # Strip starting data
            if (St_x == 0 and St_y == 0):
                St_x = strip_data.loc[index, 'GlobalX']
                St_y = strip_data.loc[index, 'GlobalY']
                if strip_data.loc[index, St_area] >= strip_data.loc[index + 1, St_area]:
                    steel_design = strip_data.loc[index, Rebar_assign]
                else:
                    steel_design = strip_data.loc[index + 1, Rebar_assign]
                continue
            # Interval Strip design
            elif (St_x != 0 and St_y != 0) and (index < count - 2):
                # Check if steel design is changed
                if strip_data.loc[index, St_area] >= strip_data.loc[index + 1, St_area] or np.isnan(
                        strip_data.loc[index + 1, St_area]):
                    top_steel_design_n = strip_data.loc[index, Rebar_assign]
                else:
                    top_steel_design_n = strip_data.loc[index + 1, Rebar_assign]

                if top_steel_design_n != steel_design:
                    # steel design is changed
                    Ed_x = strip_data.loc[index, 'GlobalX']
                    Ed_y = strip_data.loc[index, 'GlobalY']
                    new_row = {'Strip': strip, 'St_x': St_x, 'St_y': St_y, 'Ed_x': Ed_x, 'Ed_y': Ed_y,
                               'steel_design': steel_design}
                    df_strip_drawing = pd.concat([df_strip_drawing, pd.DataFrame([new_row])], ignore_index=True)
                    St_x = strip_data.loc[index, 'GlobalX']
                    St_y = strip_data.loc[index, 'GlobalY']
                    steel_design = top_steel_design_n
                continue

            # strip end point
            elif (St_x != 0 and St_y != 0) and (index == count - 2):
                top_steel_design_n = strip_data.loc[index, Rebar_assign]
                if top_steel_design_n != steel_design:
                    # steel design is changed
                    Ed_x = strip_data.loc[index, 'GlobalX']
                    Ed_y = strip_data.loc[index, 'GlobalY']
                    new_row = {'Strip': strip, 'St_x': St_x, 'St_y': St_y, 'Ed_x': Ed_x, 'Ed_y': Ed_y,
                               'steel_design': steel_design}
                    df_strip_drawing = pd.concat([df_strip_drawing, pd.DataFrame([new_row])], ignore_index=True)
                    St_x = strip_data.loc[index, 'GlobalX']
                    St_y = strip_data.loc[index, 'GlobalY']
                    steel_design = top_steel_design_n
                    Ed_x = strip_data.loc[index + 1, 'GlobalX']
                    Ed_y = strip_data.loc[index + 1, 'GlobalY']
                    new_row = {'Strip': strip, 'St_x': St_x, 'St_y': St_y, 'Ed_x': Ed_x, 'Ed_y': Ed_y,
                               'steel_design': steel_design}
                    df_strip_drawing = pd.concat([df_strip_drawing, pd.DataFrame([new_row])], ignore_index=True)

                else:
                    Ed_x = strip_data.loc[index + 1, 'GlobalX']
                    Ed_y = strip_data.loc[index + 1, 'GlobalY']
                    new_row = {'Strip': strip, 'St_x': St_x, 'St_y': St_y, 'Ed_x': Ed_x, 'Ed_y': Ed_y,
                               'steel_design': steel_design}
                    df_strip_drawing = pd.concat([df_strip_drawing, pd.DataFrame([new_row])], ignore_index=True)
                break

    df_strip_parameters = pd.merge(df_strip_drawing, df_strip_geo, on='Strip')
    df_strip_parameters = df_strip_parameters.reset_index(drop=True)
    return df_strip_parameters


if __name__ == "__main__":
    warnings.simplefilter(action='ignore', category=UserWarning)
    warnings.simplefilter(action='ignore', category=FutureWarning)
    pd.options.mode.chained_assignment = None  # default='warn'
    safe_slab_design()
