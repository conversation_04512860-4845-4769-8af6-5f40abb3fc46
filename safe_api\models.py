"""
Model Management Module

This module handles SAFE model initialization, configuration,
data import, and analysis execution.
"""

import os
import sys

# Add the parent directory (workspace root) to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

import _main_class
from .safe_environment import get_safe_environment

from read.read_geology import read_input_geology
from read.read_geometry import read_input_geometry
from read.read_loading import read_input_loading
from read.read_property import read_input_property
from read.read_steel import read_input_steel

def setup_file_paths_and_read_inputs(config_obj, main_class_module, main_read_module):
    """
    Sets up file paths and reads input from Excel files.
    
    Args:
        config_obj: Configuration object
        main_class_module: Main class module
        main_read_module: Main read module
        
    Returns:
        tuple: (excel_inputs, file_paths_obj)
    """
    file_paths = main_class_module.FilePaths()
    file_paths.ExcelProperty = config_obj.EXCEL_PROPERTY_FILE
    file_paths.ExcelGeometry = config_obj.EXCEL_GEOMETRY_FILE
    file_paths.ExcelGeology = config_obj.EXCEL_GEOLOGY_FILE
    file_paths.ExcelLoading = config_obj.EXCEL_LOADING_FILE

    excel_inputs = main_class_module.ExcelInputs()
    if os.path.exists(file_paths.ExcelProperty):
        excel_inputs = read_input_property(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelGeometry):
        excel_inputs = read_input_geometry(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelGeology):
        excel_inputs = read_input_geology(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelLoading):
        excel_inputs = read_input_loading(excel_inputs, file_paths)
    if os.path.exists(file_paths.DataSteel):
        excel_inputs = read_input_steel(excel_inputs, file_paths)
    return excel_inputs, file_paths


def initialize_model(sap_model_obj):
    """
    Initializes the SAFE model and sets units.
    
    Args:
        sap_model_obj: SAFE SapModel object
        
    Returns:
        int: Initialization return code
    """
    # Get SAFEv1 module from environment
    _, _, SAFEv1, _, _, _, _, _, _ = get_safe_environment()
    
    init_ret = sap_model_obj.InitializeNewModel(SAFEv1.eUnits.kN_m_C)
    print(f"SapModel.InitializeNewModel() returned: {init_ret}")

    _force_units = SAFEv1.eForce.NotApplicable
    _length_units = SAFEv1.eLength.NotApplicable
    _temperature_units = SAFEv1.eTemperature.NotApplicable

    [ret_present_units, _force_units, _length_units, _temperature_units] = sap_model_obj.GetPresentUnits_2(
        _force_units, _length_units, _temperature_units)
    print(
        f"Present units: {_force_units}, {_length_units}, {_temperature_units} (ret: {ret_present_units})")

    _db_force_units = SAFEv1.eForce.NotApplicable
    _db_length_units = SAFEv1.eLength.NotApplicable
    _db_temperature_units = SAFEv1.eTemperature.NotApplicable

    [ret_db_units, _db_force_units, _db_length_units, _db_temperature_units] = sap_model_obj.GetDatabaseUnits_2(
        _db_force_units, _db_length_units, _db_temperature_units)
    print(
        f"Database units: {_db_force_units}, {_db_length_units}, {_db_temperature_units} (ret: {ret_db_units})")
    return init_ret


def import_and_run_analysis(sap_model_obj, model_import_path, model_save_path):
    """
    Imports data, saves the model, and runs the analysis.
    
    Args:
        sap_model_obj: SAFE SapModel object
        model_import_path (str): Path to import file
        model_save_path (str): Path to save model
        
    Returns:
        tuple: (db_tables, analyze_obj)
    """
    # Get SAFEv1 module from environment
    _, _, SAFEv1, _, _, _, _, _, _ = get_safe_environment()
    
    file_type = SAFEv1.eFileTypeIO.DBTablesExcel
    import_type = 1  # Assuming Type=1 is a constant for this import operation
    ret_import = sap_model_obj.File.ImportFile(
        model_import_path, file_type, import_type)
    print(f"SapModel.File.ImportFile() returned: {ret_import}")

    db_tables = SAFEv1.cDatabaseTables(sap_model_obj.DatabaseTables)

    analyze_obj = SAFEv1.cAnalyze(sap_model_obj.Analyze)
    ret_save = sap_model_obj.File.Save(model_save_path)
    print(f"SapModel.File.Save() returned: {ret_save}")

    ret_analysis = analyze_obj.RunAnalysis()
    print(f"Analyze.RunAnalysis() returned: {ret_analysis}")
    return db_tables, analyze_obj


def setup_model_and_inputs(config_obj):
    """
    Setup model file paths and read Excel inputs.
    
    Args:
        config_obj: Configuration object
        
    Returns:
        tuple: (excel_inputs, file_paths_obj)
    """
    excel_inputs, file_paths_obj = setup_file_paths_and_read_inputs(
        config_obj, _main_class, _main_read)
    print(
        f"File paths set up and inputs read. Excel Properties Path: {file_paths_obj.ExcelProperty}")
    return excel_inputs, file_paths_obj


def get_sap_model_object(safe_object):
    """
    Get SapModel object from SAFE object.
    
    Args:
        safe_object: SAFE application object
        
    Returns:
        SAFE SapModel object
    """
    # Get SAFEv1 module from environment
    _, _, SAFEv1, _, _, _, _, _, _ = get_safe_environment()
    
    sap_model_obj = SAFEv1.cSapModel(safe_object.SapModel)
    print("SapModel object obtained.")
    return sap_model_obj
