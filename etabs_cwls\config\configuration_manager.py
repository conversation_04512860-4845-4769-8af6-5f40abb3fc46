"""
Configuration Manager Module for ETABS CWLS

This module handles configuration management for the ETABS Core Wall Loading
Schedule system, including ETABS version-specific mappings, load factors,
and system configuration validation.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional configuration management patterns

Key Functions:
    assign_ETABS_converter: Configure ETABS version-specific mappings
    validate_system_configuration: Validate overall system configuration
    get_default_load_factors: Get default load factor configurations

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

from typing import Optional, Callable, Dict, Any

from etabs_cwls.core import _class
from etabs_cwls.config.exceptions import DataValidationError, ConfigurationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger
)


def assign_ETABS_converter(
    ETABS_converter: _class.ETABS_converter, 
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> _class.ETABS_converter:
    """
    Configure ETABS converter with version-specific table and column mappings.
    
    This function sets up the ETABS converter object with the appropriate table
    names and column names based on the ETABS version. It replaces the legacy
    manual assignment with the enhanced converter's built-in functionality.
    
    Args:
        ETABS_converter: ETABS converter object to configure
        design_data: Design data object containing version information
        log_callback: Optional logging callback function
        
    Returns:
        Configured ETABS converter object
        
    Raises:
        DataValidationError: If ETABS version is invalid
        ConfigurationError: If converter configuration fails
        
    Example:
        >>> converter = _class.ETABS_converter()
        >>> data = _class.design_data(etabsversion=2)
        >>> configured_converter = assign_ETABS_converter(converter, data)
    """
    log_function_entry(log_callback, "assign_ETABS_converter", 
                      etabs_version=design_data.etabsversion)
    
    try:
        # Validate inputs
        if not isinstance(ETABS_converter, _class.ETABS_converter):
            raise DataValidationError(
                f"ETABS_converter must be an ETABS_converter instance, got {type(ETABS_converter).__name__}",
                field_name="ETABS_converter",
                invalid_value=type(ETABS_converter).__name__,
                expected_type="ETABS_converter",
                error_code="INVALID_CONVERTER_TYPE"
            )
        
        if not isinstance(design_data, _class.design_data):
            raise DataValidationError(
                f"design_data must be a design_data instance, got {type(design_data).__name__}",
                field_name="design_data",
                invalid_value=type(design_data).__name__,
                expected_type="design_data",
                error_code="INVALID_DESIGN_DATA_TYPE"
            )
        
        if design_data.etabsversion is None:
            raise DataValidationError(
                "ETABS version must be set in design_data before configuring converter",
                field_name="etabsversion",
                invalid_value=None,
                error_code="MISSING_ETABS_VERSION"
            )
        
        enhanced_log(log_callback, f"Configuring ETABS converter for version {design_data.etabsversion}", 'INFO')
        
        # Use the enhanced converter's built-in configuration method
        with create_timed_logger(log_callback, "converter_configuration") as timer:
            ETABS_converter.set_version_mappings(design_data.etabsversion, log_callback)
            
            # Validate the configuration
            ETABS_converter.validate_mappings(log_callback)
        
        # Log configuration summary
        summary = ETABS_converter.get_summary()
        enhanced_log(log_callback, f"Converter configured for {summary['version']['name']}", 'INFO')
        log_calculation_result(log_callback, "table_mappings_count", 
                             len(summary['table_mappings']), "mappings")
        log_calculation_result(log_callback, "column_mappings_count", 
                             len(summary['column_mappings']), "mappings")
        
        log_validation_result(log_callback, "converter_configuration", True,
                            f"Successfully configured converter for {summary['version']['name']}")
        log_function_exit(log_callback, "assign_ETABS_converter", "Configured converter")
        
        return ETABS_converter
        
    except Exception as e:
        log_error_with_context(log_callback, e, "assign_ETABS_converter")
        log_validation_result(log_callback, "converter_configuration", False, str(e))
        
        # Convert generic exceptions to specific types
        if isinstance(e, (DataValidationError, ConfigurationError)):
            raise
        else:
            raise ConfigurationError(
                f"Failed to configure ETABS converter: {str(e)}",
                error_code="CONVERTER_CONFIGURATION_FAILED"
            ) from e


def validate_system_configuration(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate overall system configuration for consistency and completeness.
    
    Args:
        file_path: File path configuration object
        design_data: Design data configuration object
        ETABS_converter: ETABS converter configuration object
        log_callback: Optional logging callback function
        
    Returns:
        True if configuration is valid
        
    Raises:
        ConfigurationError: If configuration validation fails
    """
    log_function_entry(log_callback, "validate_system_configuration")
    
    try:
        enhanced_log(log_callback, "Validating system configuration", 'INFO')
        
        # Validate file path configuration
        if file_path.accessfile1:
            file_path.validate_all_paths(log_callback)
        
        # Validate design data configuration
        design_data.validate_configuration(log_callback)
        
        # Validate ETABS converter configuration
        if ETABS_converter.etabs_version:
            ETABS_converter.validate_mappings(log_callback)
        
        # Check version consistency
        if (design_data.etabsversion and ETABS_converter.etabs_version and 
            design_data.etabsversion != ETABS_converter.etabs_version):
            raise ConfigurationError(
                f"Version mismatch: design_data has version {design_data.etabsversion}, "
                f"converter has version {ETABS_converter.etabs_version}",
                error_code="VERSION_MISMATCH"
            )
        
        enhanced_log(log_callback, "System configuration validation completed", 'INFO')
        log_validation_result(log_callback, "system_configuration", True,
                            "All configuration components validated successfully")
        log_function_exit(log_callback, "validate_system_configuration", True)
        
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, "validate_system_configuration")
        log_validation_result(log_callback, "system_configuration", False, str(e))
        raise


def get_default_load_factors(log_callback: Optional[Callable] = None) -> Dict[str, float]:
    """Get default load factor configuration for structural analysis."""
    log_function_entry(log_callback, "get_default_load_factors")
    
    try:
        default_factors = {
            'wind_factor': 1.0,
            'soil_factor': 1.0,
            'earthquake_factor': 1.0,
            'uplift_factor': 1.0
        }
        
        enhanced_log(log_callback, "Retrieved default load factors", 'DEBUG')
        log_calculation_result(log_callback, "default_factors_count", len(default_factors), "factors")
        
        log_function_exit(log_callback, "get_default_load_factors", f"{len(default_factors)} factors")
        return default_factors
        
    except Exception as e:
        log_error_with_context(log_callback, e, "get_default_load_factors")
        raise


def apply_load_factors_to_design_data(
    design_data: _class.design_data,
    load_factors: Dict[str, float],
    log_callback: Optional[Callable] = None
) -> None:
    """
    Apply load factors to design data configuration.
    
    Args:
        design_data: Design data object to configure
        load_factors: Dictionary of load factors to apply
        log_callback: Optional logging callback function
        
    Raises:
        DataValidationError: If load factors are invalid
        ConfigurationError: If application fails
    """
    log_function_entry(log_callback, "apply_load_factors_to_design_data",
                      factor_count=len(load_factors))
    
    try:
        # Validate inputs
        if not isinstance(design_data, _class.design_data):
            raise DataValidationError(
                f"design_data must be a design_data instance, got {type(design_data).__name__}",
                field_name="design_data",
                invalid_value=type(design_data).__name__,
                expected_type="design_data",
                error_code="INVALID_DESIGN_DATA_TYPE"
            )
        
        if not isinstance(load_factors, dict):
            raise DataValidationError(
                f"load_factors must be a dictionary, got {type(load_factors).__name__}",
                field_name="load_factors",
                invalid_value=type(load_factors).__name__,
                expected_type="dict",
                error_code="INVALID_LOAD_FACTORS_TYPE"
            )
        
        enhanced_log(log_callback, f"Applying {len(load_factors)} load factors to design data", 'INFO')
        
        # Apply load factors using the design data's built-in method
        design_data.set_load_factors(
            wind_factor=load_factors.get('wind_factor', 1.0),
            soil_factor=load_factors.get('soil_factor', 1.0),
            earthquake_factor=load_factors.get('earthquake_factor', 1.0),
            uplift_factor=load_factors.get('uplift_factor', 1.0),
            log_callback=log_callback
        )
        
        enhanced_log(log_callback, "Load factors applied successfully", 'INFO')
        log_validation_result(log_callback, "load_factors_application", True,
                            f"Applied {len(load_factors)} load factors")
        log_function_exit(log_callback, "apply_load_factors_to_design_data", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "apply_load_factors_to_design_data")
        log_validation_result(log_callback, "load_factors_application", False, str(e))
        
        # Convert generic exceptions to specific types
        if isinstance(e, (DataValidationError, ConfigurationError)):
            raise
        else:
            raise ConfigurationError(
                f"Failed to apply load factors: {str(e)}",
                error_code="LOAD_FACTORS_APPLICATION_FAILED"
            ) from e


def get_configuration_summary(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> Dict[str, Any]:
    """
    Get comprehensive configuration summary for debugging and validation.
    
    Args:
        file_path: File path configuration object
        design_data: Design data configuration object
        ETABS_converter: ETABS converter configuration object
        log_callback: Optional logging callback function
        
    Returns:
        Dictionary containing configuration summary
    """
    log_function_entry(log_callback, "get_configuration_summary")
    
    try:
        summary = {
            'file_path': file_path.get_file_info() if hasattr(file_path, 'get_file_info') else {},
            'design_data': design_data.get_summary() if hasattr(design_data, 'get_summary') else {},
            'etabs_converter': ETABS_converter.get_summary() if hasattr(ETABS_converter, 'get_summary') else {},
            'timestamp': None  # Could add timestamp if needed
        }
        
        enhanced_log(log_callback, "Configuration summary generated", 'DEBUG')
        log_function_exit(log_callback, "get_configuration_summary", "Summary generated")
        
        return summary
        
    except Exception as e:
        log_error_with_context(log_callback, e, "get_configuration_summary")
        raise


# Export all functions
__all__ = [
    'assign_ETABS_converter',
    'validate_system_configuration',
    'get_default_load_factors',
    'apply_load_factors_to_design_data',
    'get_configuration_summary'
]
