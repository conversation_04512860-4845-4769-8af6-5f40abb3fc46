# Utilities Package for Pile Estimation

This package provides a collection of general-purpose utility functions and helper modules used across various components of the pile foundation estimation system. It encompasses geometric operations, layout heuristics, mathematical calculations, coordinate processing, and input validation.

## Overview

The `utils` package centralizes common functionalities that support the core logic of grouping, load calculation, pile layout, and visualization. By providing these shared utilities, it promotes code reusability, reduces redundancy, and ensures consistency in data handling and processing throughout the project.

## Key Modules

- `coordinate_utils.py`: Contains functions for resolving and processing coordinates from Excel inputs. Handles conversion of various input formats into unified internal representations for columns and walls.
- `geometry_utils.py`: Contains functions for various 2D and 3D geometric calculations, such as distance computations, centroid calculations, polygon operations, and structural element polygon creation from Excel data.
- `layout_utils.py`: Provides utilities specifically for pile layout generation, including determining the layout case, validating pile spacing, calculating pile group centers, and applying site boundary filters.
- `math_utils.py`: Offers a set of mathematical helper functions for safe division, clamping values, rounding, weighted averages, interpolation, and approximate comparisons.
- `validation_utils.py`: Includes functions for validating various inputs and outputs within the system, such as Excel data structures, pile configurations, structural elements, coordinate dataframes, and generated pile locations.
- `__init__.py`: Aggregates and exports a selection of the most commonly used utility functions from its sub-modules.