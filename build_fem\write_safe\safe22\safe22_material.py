"""SAFE22 Material Properties Module

This module provides comprehensive functionality for handling material property definitions 
and assignments in SAFE22 finite element models. It manages the conversion of material 
data from Excel input formats to SAFE22 DataFrame structures, covering all material types 
used in reinforced concrete design and analysis.

SAFE22 is CSI's advanced finite element analysis software for reinforced concrete slab 
design, requiring precise material property definitions for accurate structural analysis 
and code-compliant design. This module handles the complex material property relationships 
and stress-strain characteristics required for nonlinear analysis.

Key Functionality:
- General material property definitions with symmetry type classification
- Basic mechanical properties (modulus, density, thermal expansion, <PERSON><PERSON><PERSON>'s ratio)
- Steel material data with yield/ultimate strength and stress-strain curves
- Concrete material data with compressive strength and nonlinear behavior
- Reinforcement material data (rebar properties and characteristics)
- Tendon material data (prestressing steel properties and behavior)
- Automatic material property calculations and unit conversions
- Stress-strain curve definitions for nonlinear analysis

Material Types Supported:
- Concrete: Normal weight, lightweight, high-strength concrete grades
- Steel: Structural steel for beams, columns, and composite construction
- Rebar: Reinforcing steel bars with various grades and properties
- Tendon: Prestressing steel for post-tensioned concrete elements

SAFE22 Material Property Structure:
- Material Properties - General: Basic material identification and type
- Material Properties - Basic Mechanical: Fundamental mechanical properties
- Material Properties - Steel Data: Steel-specific properties and behavior
- Material Properties - Concrete Data: Concrete-specific properties and behavior
- Material Properties - Rebar Data: Reinforcement steel properties
- Material Properties - Tendon Data: Prestressing steel properties

Material Property Units (Consistent with SAFE22):
- Stress: kN/m² (kiloNewtons per square meter) - converted to MPa for display
- Density: kN/m³ (kiloNewtons per cubic meter) for unit weight
- Modulus: kN/m² (elastic modulus, shear modulus)
- Strain: Unitless (typical values: 0.002-0.004 for concrete, 0.01-0.17 for steel)
- Temperature: °C (Celsius) for thermal expansion coefficients

Advanced SAFE22 Material Features:
- Nonlinear stress-strain relationships for accurate analysis
- Hysteretic behavior modeling for seismic analysis
- Strain hardening and softening characteristics
- Material-specific failure criteria and ultimate strain limits
- Automatic shear modulus calculation based on material type
- Temperature-dependent material properties
- Confined concrete behavior modeling

Material Symmetry Types:
- Isotropic: Same properties in all directions (concrete, steel)
- Uniaxial: Properties defined in one direction only (rebar, tendons)
- Orthotropic: Different properties in perpendicular directions (advanced materials)

Stress-Strain Curve Options:
- Simple: Linear elastic-perfectly plastic behavior
- Detailed: Multi-linear stress-strain relationships
- Code-based: Standard curves from design codes (ACI, Eurocode)
- User-defined: Custom stress-strain relationships

Material Grade Classifications:
- Concrete: C20, C25, C30, C35, C40, C50 (compressive strength in MPa)
- Steel: S235, S275, S355, S420, S460 (yield strength in MPa)
- Rebar: Grade 40, Grade 60, Grade 80 (yield strength in ksi)
- Tendon: Grade 250, Grade 270 (ultimate strength in ksi)

Design Code Integration:
- Material properties comply with major design codes
- Automatic factor of safety applications
- Code-specific material models and behavior
- Design strength vs. nominal strength relationships

Performance Considerations:
- Efficient material property consolidation across multiple sources
- Optimized unit conversions for SAFE22 compatibility
- Memory-efficient DataFrame operations for large material libraries
- Automatic property validation and error checking

Dependencies:
- pandas: DataFrame operations and data manipulation
- numpy: Numerical operations and array handling
- typing: Type hints for improved code clarity
- Excel input data with standardized material property format

Usage:
    from build_fem.write_safe.safe22.safe22_material import material_MatPropGeneral_safe22
    
    # Process general material properties
    safe22_dfs, materials_df = material_MatPropGeneral_safe22(excel_inputs, safe22_dfs)
    
    # Process basic mechanical properties
    safe22_dfs, mech_df = material_MatPropBasicMechProps_safe22(excel_inputs, safe22_dfs)
    
    # Process steel material data
    safe22_dfs, steel_df = materials_MatPropSteelData_safe22(excel_inputs, safe22_dfs)

Authors: <AUTHORS>
Version: 5.6.9 (SAFE22 Compatible)
Last Modified: 2024
"""

import numpy as np
import pandas as pd
from typing import Tuple, Any, Optional, Dict, List


def material_MatPropGeneral_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """Create general material property definitions for SAFE22 model."""
    # Extract material data from different Excel input sources
    # Each material type is processed separately to maintain data integrity
    df_concrete = excel_inputs.Concrete.copy()      # Concrete materials (C20, C30, C40, etc.)
    df_steel = excel_inputs.Steel.copy()            # Structural steel materials (S235, S355, etc.)
    df_rebar = excel_inputs.Rebar.copy()            # Reinforcing steel materials (Grade 40, 60, etc.)
    df_tendon = excel_inputs.Tendon.copy()          # Prestressing steel materials (Grade 270, etc.)

    # Combine all material types with common identification columns
    # This creates a unified material library for the SAFE22 model
    titles = ['Material', 'Type']
    df_material = pd.concat(
        [df_concrete[titles],       # Concrete material identifiers
         df_steel[titles],          # Steel material identifiers
         df_rebar[titles],          # Rebar material identifiers
         df_tendon[titles]          # Tendon material identifiers
         ],
        ignore_index=True
    )

    # Set default display properties for all materials in SAFE22 interface
    df_material['Color'] = 'Green'  # Consistent color scheme for material visualization

    # Create SAFE22 format DataFrame with material properties and symmetry classification
    # Symmetry type is critical for finite element analysis behavior
    df_22 = pd.DataFrame({
        'Material': df_material['Material'],                                                    # Material identifier
        'Type': df_material['Type'],                                                           # Material classification
        'SymType': np.where(df_material['Type'].isin(['Rebar', 'Tendon']), 'Uniaxial', 'Isotropic'),  # Symmetry classification
        'Grade': 'Unknown',                                                                    # Material grade (to be updated)
        'Color': df_material['Color'],                                                         # Display color
        'GUID': None,                                                                          # Database identifier (SAFE22 populated)
        'Notes': None                                                                          # Additional information
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns_22 = safe22_dfs.MatPropGeneral.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values,
        columns=pd.MultiIndex.from_tuples(target_columns_22)
    )

    # Append new material definitions to existing SAFE22 general material property data
    # This builds the complete material library for finite element analysis
    safe22_dfs.MatPropGeneral = pd.concat(
        [safe22_dfs.MatPropGeneral, df_append],
        ignore_index=True
    )
    return safe22_dfs, df_append


def material_MatPropBasicMechProps_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """Process basic mechanical properties for all material types in SAFE22.

    Processes fundamental mechanical properties from Excel inputs and creates SAFE22 compatible 
    basic mechanical properties DataFrame. This function handles the essential engineering 
    properties required for finite element analysis including elastic modulus, density, 
    thermal expansion, and Poisson's ratio.

    The function performs automatic unit conversions and calculates derived properties such as 
    shear modulus based on material type and established engineering relationships. Material-specific 
    calculations ensure accurate representation of structural behavior.

    """
    # Extract material data from different Excel input sources
    df_concrete = excel_inputs.Concrete.copy()     # Concrete mechanical properties
    df_steel = excel_inputs.Steel.copy()           # Steel mechanical properties
    df_rebar = excel_inputs.Rebar.copy()           # Rebar mechanical properties
    df_tendon = excel_inputs.Tendon.copy()         # Tendon mechanical properties

    # Set Poisson's ratio to None for uniaxial materials (Rebar and Tendon)
    # These materials have directional properties only and don't exhibit lateral strain
    df_rebar['U (Unitless)'] = None      # No Poisson's ratio for reinforcing steel
    df_tendon['U (Unitless)'] = None     # No Poisson's ratio for prestressing steel

    # Combine all material types with essential mechanical property columns
    # This creates a unified dataset for mechanical property processing
    titles = ['Material', 'Type', 'UnitWt (kN/m3)', 'E (kN/m2)', 'A (1/C)', 'U (Unitless)']
    df_material = pd.concat(
        [df_concrete[titles],       # Concrete mechanical data
         df_steel[titles],          # Steel mechanical data
         df_rebar[titles],          # Rebar mechanical data
         df_tendon[titles]          # Tendon mechanical data
         ],
        ignore_index=True
    )

    # Set default display properties for material visualization
    df_material['Color'] = 'Green'  # Consistent color scheme for all materials

    # Initialize target DataFrame with proper SAFE22 MultiIndex column structure
    target_columns = safe22_dfs.MatPropBasicMechProps.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))
    
    if not df_material.empty:
        # Create SAFE22 format DataFrame with fundamental mechanical properties
        # All properties are converted to appropriate SAFE22 units and formats
        mech_props = pd.DataFrame({
            'Material': df_material['Material'],                                        # Material identifier
            'DensityType': 'Weight',                                                   # Weight-based density specification
            'UnitWeight': df_material['UnitWt (kN/m3)'],                              # Unit weight in kN/m³
            'UnitMass': df_material['UnitWt (kN/m3)'] * 101.971619222242,             # Unit mass conversion (kN/m³ to kg/m³)
            'E1': df_material['E (kN/m2)'] / 1000,                                    # Elastic modulus (kN/m² to MPa)
            'G12': None,                                                               # Shear modulus (calculated below)
            'U12': df_material['U (Unitless)'],                                       # Poisson's ratio
            'A1': df_material['A (1/C)']                                              # Thermal expansion coefficient
        })

        # Calculate material-specific shear modulus based on established engineering relationships
        # Different materials have different E/G ratios based on their microstructure
        
        # Uniaxial materials (Rebar and Tendon): No shear modulus required
        for mat_type in ['Rebar', 'Tendon']:
            condition = df_material['Type'] == mat_type
            mech_props.loc[condition, 'G12'] = None

        # Structural Steel: G = E / 2.6 (typical for steel materials)
        condition = df_material['Type'] == 'Steel'
        mech_props.loc[condition, 'G12'] = mech_props.loc[condition, 'E1'] / 2.6

        # Concrete: G = E / 2.4 (typical for concrete materials)
        condition = df_material['Type'] == 'Concrete'
        mech_props.loc[condition, 'G12'] = mech_props.loc[condition, 'E1'] / 2.4

        # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
        df_append = pd.DataFrame(
            mech_props.values,
            columns=pd.MultiIndex.from_tuples(target_columns)
        )

        # Append new mechanical properties to existing SAFE22 basic mechanical properties data
        safe22_dfs.MatPropBasicMechProps = pd.concat(
            [safe22_dfs.MatPropBasicMechProps, df_append],
            ignore_index=True
        )
    return safe22_dfs, df_append


def materials_MatPropSteelData_safe22(excel_inputs, safe22_dfs):
    """Process steel material data with stress-strain characteristics for SAFE22.

    Processes steel material properties from Excel inputs and creates SAFE22 compatible steel 
    material data DataFrame. This function handles the specific properties required for steel 
    elements in finite element analysis, including yield strength, ultimate strength, and 
    stress-strain curve parameters for nonlinear analysis.

    Steel materials in SAFE22 require detailed stress-strain relationships for accurate 
    modeling of yielding, strain hardening, and ultimate failure. The function sets up 
    appropriate parameters for both elastic and inelastic behavior.

    """
    df_steel = excel_inputs.Steel.copy()

    # Create SAFE22 format DataFrame with steel material properties and stress-strain parameters
    # All strength values are converted from kN/m² to MPa for SAFE22 compatibility
    steel_props_22 = pd.DataFrame({
        'Material': df_steel['Material'],                        # Steel material identifier
        'Fy': df_steel['Fy (kN/m2)'] / 1000,                   # Yield strength (kN/m² to MPa)
        'Fu': df_steel['Fu (kN/m2)'] / 1000,                   # Ultimate tensile strength (kN/m² to MPa)
        'Fye': df_steel['Fy (kN/m2)'] / 1000,                 # Expected yield strength (= Fy for design)
        'Fue': df_steel['Fu (kN/m2)'] / 1000,                 # Expected ultimate strength (= Fu for design)
        'SSCurveOpt': 'Simple',                                 # Stress-strain curve option (elastic-plastic)
        'SSHysType': 'Kinematic',                               # Hysteretic behavior type for seismic analysis
        'SHard': 0.015,                                         # Strain at onset of strain hardening (1.5%)
        'SMax': 0.11,                                           # Strain at maximum stress (11%)
        'SRup': 0.17,                                           # Strain at rupture (17%)
        'FinalSlope': -0.1                                      # Post-ultimate stress-strain slope
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns_22 = safe22_dfs.MatPropSteelData.columns.tolist()
    df_append = pd.DataFrame(
        steel_props_22.values,
        columns=pd.MultiIndex.from_tuples(target_columns_22)
    )

    # Append new steel material data to existing SAFE22 steel material properties
    safe22_dfs.MatPropSteelData = pd.concat(
        [safe22_dfs.MatPropSteelData, df_append],
        ignore_index=True
    )
    return safe22_dfs, df_append


def material_MatPropConcreteData_safe22(excel_inputs, safe22_dfs):
    """Process concrete material data with nonlinear stress-strain characteristics for SAFE22.

    Processes concrete material properties from Excel inputs and creates SAFE22 compatible concrete 
    material data DataFrame. This function handles the specific properties required for concrete 
    elements in finite element analysis, including compressive strength and stress-strain curve 
    parameters for nonlinear analysis of reinforced concrete structures.

    Concrete materials in SAFE22 require detailed stress-strain relationships for accurate 
    modeling of compression behavior, cracking, and post-peak softening. The function sets up 
    appropriate parameters for both linear and nonlinear concrete behavior.

    """
    df_concrete = excel_inputs.Concrete.copy()

    # Create SAFE22 format DataFrame with concrete material properties and stress-strain parameters
    # Compressive strength is converted from kN/m² to MPa for SAFE22 compatibility
    concrete_props_22 = pd.DataFrame({
        'Material': df_concrete['Material'],                     # Concrete material identifier
        'Fc': df_concrete['Fc (kN/m2)'] / 1000,                # Compressive strength (kN/m² to MPa)
        'LtWtConc': 'No',                                       # Normal weight concrete (not lightweight)
        'IsUserFr': 'No',                                       # Use program-calculated modulus of rupture
        'SSCurveOpt': 'Simple',                                 # Standard concrete stress-strain curve
        'SSHysType': 'Kinematic',                               # Hysteretic behavior type for seismic analysis
        'SFc': 0.002,                                           # Strain at peak compressive stress (0.2%)
        'SCap': 0.004,                                          # Ultimate strain capacity (0.4%)
        'FinalSlope': -0.1,                                     # Post-peak stress-strain slope
        'FAngle': 0.0,                                          # Friction angle for confined concrete behavior
        'DAngle': 0.0                                           # Dilation angle for confined concrete behavior
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns_22 = safe22_dfs.MatPropConcreteData.columns.tolist()
    df_append = pd.DataFrame(
        concrete_props_22.values,
        columns=pd.MultiIndex.from_tuples(target_columns_22)
    )

    # Append new concrete material data to existing SAFE22 concrete material properties
    safe22_dfs.MatPropConcreteData = pd.concat(
        [safe22_dfs.MatPropConcreteData, df_append],
        ignore_index=True
    )
    return safe22_dfs, df_append


def material_MatPropRebarData_safe22(excel_inputs, safe22_dfs):
    """Process rebar material data with stress-strain characteristics for SAFE22.

    Processes reinforcing steel (rebar) material properties from Excel inputs and creates SAFE22 
    compatible rebar material data DataFrame. This function handles the specific properties 
    required for reinforcement elements in finite element analysis, including yield strength, 
    ultimate strength, and stress-strain curve parameters for nonlinear analysis.

    Rebar materials in SAFE22 require detailed stress-strain relationships for accurate 
    modeling of reinforcement behavior in tension and compression. The function sets up 
    appropriate parameters for reinforcement yielding, strain hardening, and ultimate capacity.

    """
    df_rebar = excel_inputs.Rebar.copy()

    # Create SAFE22 format DataFrame with rebar material properties and stress-strain parameters
    # All strength values are converted from kN/m² to MPa for SAFE22 compatibility
    rebar_props_22 = pd.DataFrame({
        'Material': df_rebar['Material'],                        # Rebar material identifier
        'Fy': df_rebar['Fy (kN/m2)'] / 1000,                   # Yield strength (kN/m² to MPa)
        'Fu': df_rebar['Fu (kN/m2)'] / 1000,                   # Ultimate tensile strength (kN/m² to MPa)
        'Fye': df_rebar['Fy (kN/m2)'] / 1000,                 # Expected yield strength (= Fy for design)
        'Fue': df_rebar['Fu (kN/m2)'] / 1000,                 # Expected ultimate strength (= Fu for design)
        'SSCurveOpt': 'Simple',                                 # Stress-strain curve option (elastic-plastic)
        'SSHysType': 'Kinematic',                               # Hysteretic behavior type for seismic analysis
        'SHard': 0.01,                                          # Strain at onset of strain hardening (1.0%)
        'SCap': 0.09,                                           # Strain capacity (9.0%)
        'FinalSlope': -0.1                                      # Post-ultimate stress-strain slope
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns_22 = safe22_dfs.MatPropRebarData.columns.tolist()
    df_append = pd.DataFrame(
        rebar_props_22.values,
        columns=pd.MultiIndex.from_tuples(target_columns_22)
    )

    # Append new rebar material data to existing SAFE22 rebar material properties
    safe22_dfs.MatPropRebarData = pd.concat(
        [safe22_dfs.MatPropRebarData, df_append],
        ignore_index=True
    )
    return safe22_dfs, df_append


def material_MatPropTendonData_safe22(excel_inputs, safe22_dfs):
    """Process tendon material data with prestressing characteristics for SAFE22.

    Processes prestressing steel (tendon) material properties from Excel inputs and creates SAFE22 
    compatible tendon material data DataFrame. This function handles the specific properties 
    required for post-tensioned elements in finite element analysis, including yield strength, 
    ultimate strength, and stress-strain curve parameters for prestressed concrete analysis.

    Tendon materials in SAFE22 require detailed stress-strain relationships for accurate 
    modeling of prestressing steel behavior under high stress levels. The function sets up 
    appropriate parameters for prestressing steel yielding and ultimate capacity.

    """
    df_tendon = excel_inputs.Material.copy()

    # Initialize target DataFrame with proper SAFE22 MultiIndex column structure
    target_columns_22 = safe22_dfs.MatPropTendonData.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns_22))

    # Process tendon materials only if data is available
    # Tendon materials are optional and may not be present in all projects
    if not df_tendon.empty:
        # Create SAFE22 format DataFrame with tendon material properties and stress-strain parameters
        # All strength values are converted from kN/m² to MPa for SAFE22 compatibility
        tendon_props_22 = pd.DataFrame({
            'Material': df_tendon['Material'],                   # Tendon material identifier
            'Fy': df_tendon['Fy (kN/m2)'] / 1000,              # Yield strength (kN/m² to MPa)
            'Fu': df_tendon['Fu (kN/m2)'] / 1000,              # Ultimate tensile strength (kN/m² to MPa)
            'SSCurveOpt': '270 ksi',                            # Standard prestressing steel stress-strain curve
            'SSHysType': 'Kinematic',                           # Hysteretic behavior type for seismic analysis
            'FinalSlope': -0.1                                  # Post-ultimate stress-strain slope
        })

        # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
        df_append = pd.DataFrame(
            tendon_props_22.values,
            columns=pd.MultiIndex.from_tuples(target_columns_22)
        )

        # Append new tendon material data to existing SAFE22 tendon material properties
        safe22_dfs.MatPropTendonData = pd.concat(
            [safe22_dfs.MatPropTendonData, df_append],
            ignore_index=True
        )
    return safe22_dfs, df_append
