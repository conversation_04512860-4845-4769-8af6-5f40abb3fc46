"""
Foundation Design Automation GUI Module

This module provides a comprehensive graphical user interface for the Foundation-Automation
system, enabling streamlined structural foundation design workflows through an intuitive
desktop application. The GUI serves as the primary user interaction layer for SAFE-based
foundation design automation, integrating file management, design calculations, and result
visualization in a cohesive interface.

Key Features:
- **File Management**: Intuitive selection and management of SAFE database files and Excel inputs
- **Design Automation**: One-click execution of complete ULS/SLS foundation design workflows  
- **Progress Monitoring**: Real-time progress tracking with detailed status logging
- **Result Management**: Automated result organization and previous result cleanup capabilities
- **Configuration Management**: User-friendly design option selection and parameter configuration
- **Error Handling**: Comprehensive error reporting with detailed diagnostics and user feedback
- **Multi-threading**: Non-blocking GUI operation during intensive calculation processes

Technical Architecture:
- **GUI Framework**: Tkinter-based interface with ttk styling for modern appearance
- **Threading Model**: Background processing threads prevent GUI freezing during calculations
- **Data Management**: Integrated with main_class data structures for seamless workflow integration
- **Logging System**: Performance-optimized batch logging with automatic save capabilities
- **File Integration**: Direct integration with SAFE database files and Excel configuration data
- **Memory Management**: Optimized data structures with bounded logging queues

Foundation Design Integration:
The GUI serves as the primary interface for the Foundation-Automation system, orchestrating:
- **SAFE Database Processing**: Direct integration with SAFE structural analysis results
- **Excel Configuration Management**: User-friendly management of design parameters and schedules
- **ULS/SLS Design Calculations**: Automated Ultimate and Serviceability Limit State analysis
- **Design Code Compliance**: Support for multiple international design standards including GEO TGN 53
- **Result Generation**: Comprehensive output generation including PROKON files and design reports
- **Quality Assurance**: Built-in validation and error checking throughout the design process

User Experience Design:
- **Intuitive Workflow**: Logical progression from file selection through design completion
- **Visual Feedback**: Progress indicators and status messages keep users informed
- **Error Prevention**: Validation checks prevent common user errors and invalid configurations
- **Accessibility**: Clear labeling and logical tab order for enhanced usability
- **Professional Appearance**: Modern styling appropriate for engineering applications
- **Responsive Design**: Adaptive layout scaling for different screen sizes and resolutions

Design Options Support:
- **GEO TGN 53 Compliance**: Toggle support for Singapore's Technical Guidance Note No. 53
- **Design Code Selection**: Integration with multiple international design standards
- **Parameter Customization**: User-configurable design parameters through Excel integration
- **Result Customization**: Flexible output options for various reporting requirements

Data Flow Architecture:
1. **Input Stage**: File selection and parameter configuration through GUI controls
2. **Validation Stage**: Input validation and file integrity checking
3. **Processing Stage**: Background execution of design calculations with progress monitoring  
4. **Output Stage**: Result generation and automatic file organization
5. **Cleanup Stage**: Optional cleanup of previous results and log management

Performance Characteristics:
- **Non-blocking Interface**: Multi-threaded design ensures responsive user interaction
- **Memory Efficient**: Bounded queues and optimized data structures prevent memory issues
- **Scalable Logging**: Batch update system handles high-volume log generation efficiently
- **Error Resilience**: Comprehensive exception handling maintains stability under error conditions
- **Resource Management**: Proper cleanup of threads and file handles prevents resource leaks

Integration Points:
- **SAFE Database Files**: Direct reading and processing of structural analysis results
- **Excel Configuration**: Integration with Excel-based design schedules and parameters
- **Designer Main Module**: Orchestration of core design calculation workflows
- **Result Output Systems**: Integration with PROKON file generation and report systems
- **Logging Infrastructure**: Comprehensive integration with application-wide logging systems

Technical Dependencies:
- tkinter: Core GUI framework providing cross-platform desktop interface capabilities
- threading: Background processing support for non-blocking calculation execution
- main_class: Core data structures and workflow integration for foundation design automation
- designer_main: Core calculation engines for ULS/SLS design and automation workflows
- datetime: Timestamp generation for logging and audit trail functionality

Usage Patterns:
    Basic foundation design workflow:
    >>> # Initialize GUI application
    >>> root = tk.Tk()
    >>> app = SafeDesignCheckerGUI(root)
    >>> 
    >>> # User selects files through GUI controls
    >>> # User configures design options (GEO TGN 53, etc.)
    >>> # User initiates design automation
    >>> # System processes design in background thread
    >>> # Results automatically generated and organized
    >>> 
    >>> root.mainloop()

    Integration with existing applications:
    >>> # Create as child window
    >>> parent_window = tk.Tk()
    >>> design_gui = SafeDesignCheckerGUI(parent_window)
    >>> # GUI appears as modal dialog within parent application

Professional Standards:
- **Engineering Workflow**: Designed specifically for structural engineering professionals
- **Industry Standards**: Compatible with international foundation design codes and practices
- **Quality Assurance**: Built-in validation and error checking for professional reliability
- **Documentation**: Comprehensive logging and audit trail for regulatory compliance
- **File Management**: Professional file organization suitable for project documentation

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 and modern foundation design workflows
"""

import os
import sys
# Add the parent directory (workspace root) to sys.path for module access
# This enables proper importing of core foundation automation modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
    
import threading
import tkinter as tk
import traceback
from datetime import datetime
from tkinter import filedialog, messagebox, ttk
from collections import deque
from typing import Optional, Callable, Any

import main_class as main_class
from design_fdn.designer_main import (
    initialize_file_paths, 
    read_input_data, 
    run_uls_sls_calculations, 
    run_design_automation,
    delete_previous_results
)


class SafeDesignCheckerGUI:
    """
    Comprehensive GUI application for Foundation Design Automation system.
    
    This class provides a complete graphical user interface for structural foundation design
    automation, integrating SAFE database processing, Excel configuration management, and
    automated ULS/SLS design calculations. The interface enables engineers to efficiently
    execute foundation design workflows through an intuitive desktop application.

    The GUI architecture emphasizes user experience, performance, and reliability through:
    - Non-blocking multi-threaded processing for responsive interaction
    - Comprehensive error handling and user feedback systems
    - Performance-optimized logging with batch updates
    - Professional styling and intuitive workflow organization
    - Integration with industry-standard tools (SAFE, Excel, PROKON)

    Key Components:
    - **File Management**: SAFE database and Excel folder selection with validation
    - **Design Configuration**: Design code options and parameter selection interface
    - **Process Control**: One-click automation execution with progress monitoring
    - **Status Monitoring**: Real-time logging with save/clear functionality
    - **Result Management**: Automated organization and cleanup of design outputs

    Technical Architecture:
    - **Main Thread**: GUI event handling and user interaction management
    - **Worker Threads**: Background execution of intensive design calculations
    - **Data Structures**: Integration with main_class objects for workflow coordination
    - **Logging System**: Performance-optimized batch logging with bounded queues
    - **Error Handling**: Comprehensive exception management with user-friendly reporting

    Workflow Integration:
    The GUI orchestrates the complete foundation design process including:
    1. **Input Validation**: File selection and parameter validation
    2. **Data Loading**: SAFE database and Excel configuration processing
    3. **Design Execution**: ULS/SLS calculations with progress monitoring
    4. **Result Generation**: Automated output creation and file organization
    5. **Quality Assurance**: Comprehensive error checking and status reporting

    Attributes:
        file_paths (main_class.FilePaths): File path management object for project organization
        excel_inputs (main_class.ExcelInputs): Excel-based configuration and schedule data
        excel_outputs (main_class.ExcelOutputs): Design calculation results and analysis data
        safe_mdbs (main_class.SafeMdbs): SAFE database connection and data management
        design_results (main_class.DesignResults): Comprehensive design verification results
        
        mdb_path_var (tk.StringVar): GUI variable for SAFE database file path selection
        excel_folder_var (tk.StringVar): GUI variable for Excel configuration folder path
        tgn53_var (tk.IntVar): Design code option for GEO TGN 53 compliance selection
        
        log_queue (deque): Performance-optimized queue for batch status logging
        log_update_pending (bool): Flag for coordinating batch log updates
        
        root (tk.Tk | tk.Toplevel): Root window object for GUI display and event handling
        is_top_level (bool): Flag indicating window hierarchy for proper closure handling
        
        progress (ttk.Progressbar): Visual progress indicator for long-running operations
        status_text (tk.Text): Multi-line text widget for detailed status and error reporting

    Design Patterns:
    - **Model-View Controller**: Clear separation between data, UI, and control logic
    - **Observer Pattern**: Status logging and progress monitoring through callbacks
    - **Command Pattern**: Button actions encapsulated in discrete command methods
    - **Strategy Pattern**: Configurable design options through parameter selection

    Performance Optimization:
    - **Batch Logging**: Queued log updates prevent GUI freezing during intensive logging
    - **Threading**: Background processing maintains responsive user interface
    - **Memory Management**: Bounded queues prevent memory growth during long operations
    - **Resource Cleanup**: Proper disposal of threads and file handles

    Error Handling Strategy:
    - **User-Friendly Messages**: Technical errors translated to actionable user guidance
    - **Detailed Logging**: Comprehensive error traces for debugging and support
    - **Graceful Degradation**: Partial failures don't compromise overall application stability
    - **Recovery Options**: Clear paths for users to recover from error conditions
    """
    
    def __init__(self, root: Optional[tk.Tk] = None) -> None:
        """
        Initialize the Foundation Design Automation GUI application.
        
        Creates and configures the complete GUI interface with all necessary components
        for foundation design automation workflows. Initializes data structures, GUI
        elements, and sets up the application window with professional styling and
        optimal user experience configuration.

        Initialization Process:
        1. **Data Structure Setup**: Initialize all core data objects for workflow management
        2. **GUI Variable Creation**: Set up tkinter variables for form controls and data binding  
        3. **Window Configuration**: Create and configure main window with appropriate styling
        4. **Component Creation**: Build complete UI layout with all necessary controls
        5. **Event Binding**: Connect GUI events to appropriate handler methods

        GUI Components Initialized:
        - **File Selection Panel**: SAFE database and Excel folder selection controls
        - **Design Options Panel**: Configuration options including GEO TGN 53 compliance
        - **Action Controls**: Buttons for result cleanup and design automation execution
        - **Progress Monitoring**: Progress bar and status logging for user feedback
        - **Utility Functions**: Log management, save/clear functionality, and window controls
        """
        # Initialize core data structures for foundation design workflow
        # These objects manage the complete design process from input to output
        self.file_paths = main_class.FilePaths()  # Project file organization and path management
        self.excel_inputs = main_class.ExcelInputs()  # Excel-based configuration and schedules
        self.excel_outputs = main_class.ExcelOutputs()  # Design calculation results
        self.safe_mdbs = main_class.SafeMdbs()  # SAFE database connections and data
        self.design_results = main_class.DesignResults()  # Comprehensive design verification
        
        # Initialize GUI control variables for data binding and form management
        # StringVar objects provide two-way data binding between GUI controls and application data
        self.mdb_path_var = tk.StringVar()  # SAFE database file path selection
        self.excel_folder_var = tk.StringVar()  # Excel configuration folder path
        
        # Performance optimization: batch log updates to prevent GUI freezing
        # Bounded queue prevents memory growth during extensive logging operations
        self.log_queue = deque(maxlen=1000)  # Limit queue size to prevent memory issues
        self.log_update_pending = False  # Flag for coordinating batch updates

        # Create root window with appropriate hierarchy for flexible deployment
        # Supports both standalone application and integration with existing systems
        if root is None:
            # Standalone application mode: create top-level window
            self.root = tk.Tk()
            self.is_top_level = False
        else:
            # Integrated mode: create child window for embedding in existing applications
            self.root = tk.Toplevel(root)
            self.is_top_level = True
            
        # Configure main window with professional appearance and optimal dimensions
        # Window sizing accommodates all controls while maintaining reasonable screen usage
        self.root.title("SAFE Design Checker")
        self.root.geometry("800x800")

        # Set application icon for professional branding
        # Icon enhances professional appearance and brand recognition
        try:
            self.root.iconbitmap('AIS.ico')
        except:
            pass  # Continue without icon if file not found        # Create complete UI layout with all necessary components
        # UI creation is delegated to separate method for better code organization
        self.create_ui()

    def create_ui(self) -> None:
        """
        Create and configure the complete user interface layout with all necessary components.
        
        This method constructs the comprehensive GUI layout for foundation design automation,
        organizing all controls into logical functional groups with professional styling and
        optimal user experience. The interface design emphasizes workflow efficiency and
        clear visual hierarchy for complex engineering applications.

        UI Architecture:
        - **Hierarchical Layout**: Logical grouping of related controls using labeled frames
        - **Responsive Design**: Proper use of pack geometry manager for adaptive sizing
        - **Professional Styling**: TTK widgets for modern appearance and consistency
        - **Accessibility**: Clear labeling and logical tab order for enhanced usability
        - **Visual Feedback**: Progress indicators and status displays for user awareness

        Component Organization:
        1. **File Selection Panel**: SAFE database and Excel folder selection controls
        2. **Design Options Panel**: Configuration options including design code compliance
        3. **Action Control Panel**: Primary operation buttons for workflow execution
        4. **Progress Monitoring Panel**: Visual progress indicator for long operations
        5. **Status Display Panel**: Comprehensive logging and message display area
        6. **Utility Control Panel**: Log management and application control buttons

        Layout Strategy:
        - **Top-down Organization**: Workflow-oriented arrangement from input to output
        - **Grouped Functionality**: Related controls organized in labeled frames
        - **Progressive Disclosure**: Advanced options organized to minimize cognitive load
        - **Status Prominence**: Important feedback prominently displayed

        Widget Configuration:
        - **Entry Fields**: Appropriate sizing for typical file paths and parameters
        - **Buttons**: Consistent sizing and clear action-oriented labeling
        - **Text Areas**: Scrollable status display for comprehensive feedback
        - **Progress Indicators**: Visual feedback during intensive operations

        Event Binding:
        All interactive elements are properly connected to their respective handler
        methods using command parameters and variable binding for seamless operation.

        Example UI Structure:
            ┌─ Main Frame ────────────────────────────────────┐
            │ ┌─ File Selection ───────────────────────────┐  │
            │ │ SAFE Database: [Entry] [Browse]            │  │
            │ │ Excel Folder:  [Entry] [Browse]            │  │
            │ └────────────────────────────────────────────┘  │
            │ ┌─ Design Options ───────────────────────────┐  │
            │ │ GEO TGN 53: (•) Yes  ( ) No               │  │
            │ └────────────────────────────────────────────┘  │
            │ ┌─ Actions ──────────────────────────────────┐  │
            │ │ [Clear Results] [Run Design Automation]    │  │
            │ └────────────────────────────────────────────┘  │
            │ ┌─ Progress ─────────────────────────────────┐  │
            │ │ [Progress Bar]                             │  │
            │ └────────────────────────────────────────────┘  │
            │ ┌─ Status ───────────────────────────────────┐  │
            │ │ [Scrollable Text Area]                     │  │
            │ └────────────────────────────────────────────┘  │
            │ [Clear Log] [Save Log]           [Close]       │
            └─────────────────────────────────────────────────┘

        Returns:
            None: Method performs UI construction with no return value.

        UI Construction Process:
        1. **Main Container**: Create primary frame with consistent padding
        2. **File Selection**: Build file input controls with browse functionality
        3. **Configuration**: Add design option controls with default selections
        4. **Operations**: Create action buttons for primary workflow functions
        5. **Feedback**: Implement progress and status display components
        6. **Utilities**: Add log management and window control functions
        """
        # Create main container frame with consistent padding for professional appearance
        # Padding ensures proper spacing from window edges and visual breathing room
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # File selection frame: Primary input controls for essential project files
        # LabelFrame provides visual grouping and clear functional identification
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=5)

        # SAFE Result MDB file selector: Critical input for structural analysis data
        # Grid layout provides precise control alignment and professional appearance
        self._create_file_selector(file_frame, "SAFE Result Database:", 0, self.mdb_path_var, self.select_db_file)

        # Excel Master Data folder selector: Configuration and schedule data source
        # Separate control enables flexible file organization and project management
        self._create_file_selector(file_frame, "Excel Master Data Folder:", 1, self.excel_folder_var, self.select_excel_folder)

        # Design options frame: Configuration controls for design methodology selection
        # Separate frame emphasizes importance of design code compliance decisions
        tgn_frame = ttk.LabelFrame(main_frame, text="Design Options", padding="10")
        tgn_frame.pack(fill=tk.X, pady=5)

        # GEO TGN 53 compliance selection: Critical design methodology choice
        # Radio buttons provide clear binary choice with visual feedback of selection
        ttk.Label(tgn_frame, text="Follow GEO Technical Guidance Note No. 53 (TGN 53)?").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.tgn53_var = tk.IntVar(value=1)  # Default to Yes (1) for Singapore compliance
        ttk.Radiobutton(tgn_frame, text="Yes", variable=self.tgn53_var, value=1).grid(row=0, column=1, padx=20, sticky=tk.W)
        ttk.Radiobutton(tgn_frame, text="No", variable=self.tgn53_var, value=0).grid(row=0, column=2, padx=20, sticky=tk.W)

        # Actions frame: Primary workflow control buttons for user operations
        # Horizontal layout emphasizes workflow progression and related functionality
        actions_frame = ttk.LabelFrame(main_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=5)

        # Action buttons: Core workflow operations with appropriate sizing and spacing
        # Left-to-right arrangement follows logical workflow from cleanup to execution
        ttk.Button(actions_frame, text="Clear Previous Results",
                   command=self.delete_previous_results, width=20).pack(side=tk.LEFT, padx=10)
        ttk.Button(actions_frame, text="Run Design Automation",
                   command=self.run_design_automation, width=30).pack(side=tk.LEFT, padx=10)        # Progress monitoring frame: Visual feedback for long-running operations
        # Separate frame provides clear visual separation for process status indication
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=5)

        # Progress bar: Indeterminate mode provides continuous feedback during processing
        # Horizontal orientation fits well within window layout and provides clear visual cue
        self.progress = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)

        # Status display frame: Comprehensive logging and message display area
        # Expandable frame accommodates extensive status information and scrolling
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Status text widget with scrollbar: Multi-line display for detailed feedback
        # Word wrapping and scrolling accommodate extensive status and error information
        self.status_text = tk.Text(status_frame, wrap=tk.WORD, width=80, height=5)
        scrollbar = ttk.Scrollbar(status_frame, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Utility control frame: Log management and application control buttons
        # Bottom placement provides logical workflow completion and always-accessible controls
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # Log management and application control buttons
        # Left side: Log utilities, Right side: Application control for logical grouping
        ttk.Button(buttons_frame, text="Clear Log", command=self.clear_log, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Save Log", command=self.save_log, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Close", command=self.close_window, width=20).pack(side=tk.RIGHT, padx=5)

    def _create_file_selector(self, 
                             parent: ttk.Widget, 
                             label_text: str, 
                             row: int, 
                             text_var: tk.StringVar, 
                             command: Callable[[], None]) -> None:
        """
        Create standardized file selection widget group with label, entry, and browse button.
        
        This helper method constructs a complete file selection interface consisting of
        a descriptive label, text entry field for path display/editing, and browse button
        for interactive file/folder selection. The standardized layout ensures consistency
        across all file selection controls in the application.

        Widget Configuration:
        - **Label**: Descriptive text identifying the file selection purpose
        - **Entry**: Text field displaying selected path with manual editing capability
        - **Button**: Browse button triggering file/folder selection dialog
        """
        # Label: Descriptive text with consistent left alignment for professional appearance
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky=tk.W, pady=5)
        
        # Entry field: Path display with manual editing capability and appropriate width
        ttk.Entry(parent, textvariable=text_var, width=50).grid(row=row, column=1, padx=5, pady=5)
        
        # Browse button: Interactive file selection with consistent styling and spacing
        ttk.Button(parent, text="Browse", command=command).grid(row=row, column=2, padx=5, pady=5)

    def select_db_file(self) -> None:
        """
        Handle SAFE database file selection with automatic Excel folder configuration.
        
        Presents file selection dialog for SAFE result database files and automatically
        configures the Excel Master Data folder to the same directory for user convenience.
        This intelligent defaults approach streamlines the typical workflow where database
        and configuration files are co-located in project directories.

        Supported File Types:
        - **MDB Files**: Microsoft Access database format (legacy SAFE versions)
        - **ACCDB Files**: Microsoft Access database format (modern SAFE versions)  
        - **SQLite Files**: SQLite database format (alternative SAFE storage)

        Workflow Automation:
        1. **File Selection**: Present dialog with appropriate file type filters
        2. **Path Validation**: Verify selected file exists and is accessible
        3. **Variable Update**: Set database path variable for application use
        4. **Status Logging**: Record selection event for audit trail
        5. **Auto-Configuration**: Set Excel folder to same directory as database
        6. **Confirmation Logging**: Record automatic folder configuration

        User Experience Features:
        - **Intelligent Defaults**: Automatic Excel folder setting reduces user steps
        - **Clear Feedback**: Status logging confirms both selections for user awareness
        - **Error Prevention**: File type filtering prevents selection of incompatible files
        - **Workflow Efficiency**: Single action configures both required paths

        Returns:
            None: Method performs file selection and configuration with no return value.

        File Selection Dialog Configuration:
        - **Title**: "Select SAFE Result Database" for clear dialog identification
        - **File Types**: Filtered to database formats for error prevention
        - **Default Directory**: Uses system default for initial dialog location

        Example Workflow:
            User clicks browse → Dialog opens → Selects "Project.mdb" 
            → Database path set to "/project/Project.mdb"
            → Excel folder auto-set to "/project/"
            → Status logged for both configurations
        """
        # Present file selection dialog with appropriate filters for SAFE database files
        # File type filtering prevents selection of incompatible files and guides user choice
        file_path = filedialog.askopenfilename(
            title="Select SAFE Result Database",
            filetypes=[("Database files", "*.mdb *.accdb *.sqlite"), ("All files", "*.*")]
        )
        
        # Process selection only if user confirmed file choice (didn't cancel dialog)
        if file_path:
            # Update GUI variable to display selected path and enable application access
            self.mdb_path_var.set(file_path)
            self.log_status(f"Selected SAFE Result Database: {file_path}")
            
            # Automatically configure Excel Master Data Folder for user convenience
            # Most projects co-locate database and configuration files in same directory
            excel_folder_path = os.path.dirname(file_path)
            self.excel_folder_var.set(excel_folder_path)
            self.log_status(f"Automatically set Excel Master Data Folder to: {excel_folder_path}")

    def select_excel_folder(self) -> None:
        """
        Handle Excel Master Data folder selection for configuration file access.
        
        Presents folder selection dialog for locating Excel configuration files containing
        design schedules, material properties, and other project parameters. The selected
        folder should contain all necessary Excel files for foundation design automation.

        Expected Folder Contents:
        - **Design Schedules**: Pile schedules, reinforcement configurations, material specifications
        - **Load Data**: Load combination definitions and analysis parameters
        - **Configuration Files**: Design code settings, analysis options, output preferences
        - **Reference Data**: Material databases, section properties, design coefficients

        Folder Selection Process:
        1. **Dialog Presentation**: Show directory selection dialog with clear title
        2. **Path Validation**: Verify selected folder exists and is accessible
        3. **Variable Update**: Set folder path variable for application use
        4. **Status Logging**: Record selection event for audit trail and user confirmation

        Integration Notes:
        - Selected folder becomes base path for all Excel file access during design process
        - Application will search this folder for required configuration files
        - Folder structure should follow established project organization conventions
        - Missing required files will be reported during initialization phase

        Returns:
            None: Method performs folder selection and configuration with no return value.

        User Experience:
        - **Clear Purpose**: Dialog title clearly identifies expected folder content
        - **Validation Feedback**: Status logging confirms selection for user awareness
        - **Error Prevention**: Folder-only selection prevents file selection errors

        Example Usage:
            User clicks browse → Dialog opens → Selects "/project/config/"
            → Excel folder path set → Status logged → Ready for design process
        """
        # Present folder selection dialog with clear identification of expected content
        # Directory-only selection prevents user confusion with file selection
        folder_path = filedialog.askdirectory(title="Select Folder for SAFE Excel Master Data")
        
        # Process selection only if user confirmed folder choice (didn't cancel dialog)
        if folder_path:
            # Update GUI variable to display selected path and enable application access
            self.excel_folder_var.set(folder_path)
            self.log_status(f"Selected Excel Master Data Folder: {folder_path}")

    def clear_log(self) -> None:
        """
        Clear all content from the status display and reset logging queue.
        
        Removes all text from the status text widget and clears the internal logging
        queue to provide a clean slate for new operations. This utility function helps
        manage log size and provides users with a way to focus on current operations
        without clutter from previous activities.

        Clearing Operations:
        1. **Text Widget Clearing**: Remove all content from the status display area
        2. **Queue Reset**: Clear the internal batch logging queue to prevent stale messages
        3. **Confirmation Logging**: Add confirmation message to verify clearing operation

        User Experience:
        - **Immediate Feedback**: Status display is instantly cleared for visual confirmation
        - **Audit Trail**: Clearing event is logged with timestamp for operation tracking
        - **Memory Management**: Queue clearing prevents accumulation of stale log entries

        Returns:
            None: Method performs clearing operations with no return value.

        Usage Context:
        - Before starting new design operations to focus on current activity
        - When status display becomes cluttered with extensive previous operations
        - As part of workflow reset when switching between different projects
        - For memory management during long application sessions

        Implementation Notes:
        - Uses tkinter Text widget delete method for complete content removal
        - Clears both displayed content and internal queue for consistent state
        - Confirmation message provides immediate user feedback of successful operation
        """
        # Clear all content from the status text display widget
        # Indexes 1.0 to END ensure complete content removal including all lines
        self.status_text.delete(1.0, tk.END)
        
        # Clear the internal logging queue to prevent stale messages
        # Prevents old queued messages from appearing after clear operation
        self.log_queue.clear()
        
        # Log confirmation message to provide immediate user feedback
        # Timestamp in log confirms when clearing operation occurred
        self.log_status("Log cleared")

    def save_log(self) -> None:
        """
        Save current status log content to user-selected file location.
        
        Presents file save dialog for user to specify location and filename for saving
        the complete status log content. This functionality enables users to preserve
        comprehensive records of design operations for documentation, troubleshooting,
        and audit trail purposes.

        Supported File Formats:
        - **Text Files (.txt)**: Standard text format for broad compatibility
        - **Log Files (.log)**: Specialized log format for automated processing
        - **All Files**: User flexibility for custom file extensions

        Save Process:
        1. **Dialog Presentation**: Show save file dialog with appropriate file type filters
        2. **Path Validation**: Verify selected location is writable and accessible
        3. **Content Extraction**: Retrieve complete text from status display widget
        4. **File Writing**: Save content with proper encoding and error handling
        5. **Confirmation**: Log successful save operation with file path

        File Content:
        - **Complete Status Log**: All messages currently displayed in status widget
        - **Timestamp Preservation**: All timestamps and formatting maintained
        - **Error Information**: Includes any error messages and stack traces
        - **Audit Trail**: Complete record of all operations performed

        Returns:
            None: Method performs save operation with no return value.

        Error Handling:
        - **Write Permission**: Handles cases where target location is not writable
        - **File System**: Manages disk space and file system errors gracefully
        - **User Cancellation**: Handles dialog cancellation without error
        - **Path Validation**: Ensures target directory exists or creates it

        Usage Scenarios:
        - Preserving design session records for project documentation
        - Creating troubleshooting information for technical support
        - Maintaining audit trails for regulatory compliance
        - Sharing operation logs with team members or consultants
        """        # Present save file dialog with appropriate file type filters
        # Default extension and filters guide user to appropriate file formats
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("Log files", "*.log"), ("All files", "*.*")],
            title="Save Log As"
        )
        
        # Process save operation only if user confirmed file path (didn't cancel dialog)
        if file_path:
            # Delegate actual file writing to specialized helper method
            # Helper method handles detailed file operations and error management
            self._save_log_to_path(file_path)

    def initialize_data(self) -> bool:
        """
        Initialize and validate all required data sources for foundation design automation.
        
        Performs comprehensive initialization of file paths, data loading, and validation
        to prepare the application for design automation execution. This method coordinates
        multiple initialization phases and provides detailed feedback about the process
        and any issues encountered.

        Initialization Phases:
        1. **Path Validation**: Verify both SAFE database and Excel folder are selected
        2. **File Path Setup**: Initialize complete project file organization structure  
        3. **Data Loading**: Read and parse all Excel configuration and schedule files
        4. **Database Connection**: Establish connection to SAFE result database
        5. **Validation**: Verify all required data is available and properly formatted

        Data Sources Initialized:
        - **SAFE Database**: Structural analysis results and load combinations
        - **Excel Inputs**: Design schedules, material properties, configuration parameters
        - **File Structure**: Complete project directory organization and output paths
        - **Database Objects**: SAFE model database connections and query interfaces

        Returns:
            bool: Success status of initialization process.
                - True: All data sources successfully initialized and ready for design
                - False: Initialization failed due to missing files, invalid data, or errors

        Error Handling:
        - **Missing Files**: Clear error messages for missing database or Excel folder
        - **Invalid Data**: Detailed error reporting for malformed or missing Excel data
        - **Database Issues**: Connection error handling with diagnostic information
        - **File System**: Permission and access error management

        User Feedback:
        - **Progress Logging**: Detailed status messages throughout initialization process
        - **Error Dialogs**: User-friendly error messages for critical failures
        - **Success Confirmation**: Clear indication when initialization completes successfully

        Validation Requirements:
        - **Database Path**: Must point to valid SAFE result database file
        - **Excel Folder**: Must contain all required configuration Excel files
        - **File Permissions**: Read access to database and Excel files
        - **Data Integrity**: Proper formatting and completeness of Excel data

        Example Usage:
            >>> if self.initialize_data():
            ...     # Proceed with design automation
            ...     self.run_design_process()
            ... else:
            ...     # Handle initialization failure
            ...     self.show_error_message()
        """
        # Extract file paths from GUI variables for validation and processing
        # Both paths are required for complete foundation design automation workflow
        mdb_path = self.mdb_path_var.get()
        excel_folder = self.excel_folder_var.get()

        # Validate that both required paths have been selected by user
        # Early validation prevents wasted processing time on incomplete configuration
        if not mdb_path or not excel_folder:
            messagebox.showerror("Error", "Please select both the SAFE Result Database and Excel Master Data Folder.")
            return False

        # Initialize file path structure for complete project organization
        # This creates the directory structure and file organization for all outputs
        self.log_status("Initializing file paths...")
        self.file_paths = initialize_file_paths(mdb_path, excel_folder, self.log_status)
        
        # Validate that file path initialization was successful
        # Failure typically indicates file system issues or invalid input paths
        if self.file_paths is None:
            return False        # Load and validate all required input data from Excel and database sources
        # This comprehensive data loading process prepares all information needed for design
        try:
            self.log_status("Reading input data...")
            read_input_data(self.file_paths, self.excel_inputs, self.safe_mdbs, self.log_status)
            self.log_status("Input data loaded successfully.")
            return True
        except Exception as e:
            # Handle data loading errors with detailed reporting and user notification
            # Errors may include missing files, format issues, or database connection problems
            self.log_status(f"Error reading input files: {str(e)}")
            messagebox.showerror("Error", f"Failed to read input files: {str(e)}")
            return False

    def run_design_automation(self) -> None:
        """
        Execute complete foundation design automation workflow with progress monitoring.
        
        Orchestrates the entire foundation design process from initialization through
        final result generation. This method serves as the primary entry point for
        design automation, coordinating all phases of the workflow while maintaining
        responsive user interface through background processing.

        Automation Workflow:
        1. **Data Initialization**: Validate and load all required input data sources
        2. **File Path Validation**: Verify all required paths and permissions are available
        3. **Progress Activation**: Start visual progress indicator for user feedback
        4. **Background Processing**: Launch worker thread for intensive calculations
        5. **Result Generation**: Automated creation of all design outputs and reports

        Threading Architecture:
        - **Main Thread**: Maintains responsive GUI and user interaction capability
        - **Worker Thread**: Executes intensive calculations without blocking interface
        - **Progress Communication**: Real-time status updates between threads
        - **Error Handling**: Comprehensive error management across thread boundaries

        User Experience Features:
        - **Non-blocking Interface**: GUI remains responsive during long calculations
        - **Visual Progress**: Progress bar indicates active processing status
        - **Status Logging**: Detailed real-time feedback about process progression
        - **Error Recovery**: Clear error messages and recovery guidance

        Validation Requirements:
        - **Data Sources**: All required input data must be successfully initialized
        - **File Paths**: SAFE database and Excel configuration paths must be valid
        - **Permissions**: Write access required for result generation directories
        - **Dependencies**: All required modules and libraries must be available

        Returns:
            None: Method initiates background processing with no return value.

        Process Coordination:
        - **Initialization Check**: Validates all prerequisites before processing begins
        - **Resource Allocation**: Prepares all necessary resources for calculation workflow
        - **Thread Management**: Properly manages background thread lifecycle
        - **Progress Monitoring**: Coordinates visual feedback throughout process

        Error Scenarios:
        - **Initialization Failure**: Method returns early if data initialization fails
        - **Path Validation**: Error dialog if required file paths are not configured
        - **Processing Errors**: Worker thread handles calculation errors independently
        - **Resource Issues**: Proper cleanup and error reporting for resource problems

        Example Workflow:
            User clicks "Run Design Automation" → Validation checks pass 
            → Progress bar starts → Background calculations begin
            → Status updates appear → Results generated → Success notification
        """
        # Validate and initialize all required data sources before processing begins
        # Early validation prevents wasted resources on incomplete configurations
        if not self.initialize_data():
            return

        # Verify that SAFE database path has been properly initialized
        # This double-check ensures database connectivity before intensive processing
        if not hasattr(self.file_paths, 'ResultMdbSAFE') or not self.file_paths.ResultMdbSAFE:
            messagebox.showerror("Error", "Please initialize file paths first.")
            return

        # Start visual progress indication to provide immediate user feedback
        # Indeterminate mode shows activity without specific progress percentage
        self.progress.start()

        # Launch background processing thread to maintain responsive GUI
        # Daemon thread ensures proper cleanup if main application closes
        thread = threading.Thread(target=self._run_design_automation_thread, daemon=True)
        thread.start()

    def _run_design_automation_thread(self) -> None:
        """
        Execute foundation design calculations in background thread for non-blocking operation.
        
        This method implements the core computational workflow for foundation design automation,
        executing both Ultimate Limit State (ULS) and Serviceability Limit State (SLS) calculations
        followed by comprehensive design automation. The method runs in a dedicated background
        thread to maintain GUI responsiveness during intensive calculations.

        Calculation Workflow:
        1. **Configuration Extraction**: Retrieve design options from GUI controls
        2. **ULS/SLS Calculations**: Execute structural analysis and limit state verification
        3. **Design Automation**: Perform automated design verification and optimization
        4. **Result Compilation**: Generate comprehensive outputs and reports
        5. **Completion Handling**: Coordinate final status updates and cleanup

        Threading Coordination:
        - **Background Execution**: Runs in separate thread to prevent GUI freezing
        - **Main Thread Communication**: Uses root.after() for thread-safe GUI updates
        - **Error Isolation**: Exception handling prevents thread errors from crashing GUI
        - **Resource Management**: Proper cleanup and status coordination

        Design Code Integration:
        - **GEO TGN 53**: Configurable compliance with Singapore Technical Guidance Note
        - **International Standards**: Support for various foundation design codes
        - **User Selection**: Design methodology determined by GUI radio button selection
        - **Code-Specific Parameters**: Automatic adjustment of design factors and procedures

        Process Phases:
        1. **ULS Analysis**: Ultimate limit state verification with load combinations
        2. **SLS Analysis**: Serviceability limit state checks for deflection and rotation
        3. **Design Verification**: Automated checking against design code requirements
        4. **Report Generation**: Comprehensive documentation of design results

        Error Handling Strategy:
        - **Comprehensive Exception Catching**: All calculation errors captured and handled
        - **Detailed Error Logging**: Complete stack traces preserved for debugging
        - **User-Friendly Messages**: Technical errors translated to actionable guidance
        - **Graceful Recovery**: Proper cleanup and status indication on errors

        Returns:
            None: Method executes calculations with status updates via logging callbacks.

        Thread Safety:
        - **GUI Updates**: All GUI modifications use thread-safe root.after() scheduling
        - **Shared Data**: Proper synchronization of data structures between threads
        - **Error Reporting**: Thread-safe error message display and status updates
        - **Resource Access**: Coordinated access to file system and database resources

        Completion Actions:
        - **Progress Indication**: Visual progress bar stopped upon completion
        - **Status Notification**: Success message displayed to user
        - **Log Preservation**: Automatic saving of complete operation log
        - **Result Availability**: All design outputs ready for user access
        """
        try:
            # Extract design code configuration from GUI controls
            # TGN 53 setting determines compliance with Singapore foundation design standards
            use_tgn53 = self.tgn53_var.get()

            # Execute Ultimate and Serviceability Limit State calculations
            # This phase performs comprehensive structural analysis and verification
            run_uls_sls_calculations(
                self.file_paths, 
                self.safe_mdbs, 
                self.excel_inputs, 
                self.excel_outputs, 
                self.design_results,
                self.log_status
            )

            # Execute automated design verification and optimization workflow
            # This phase generates final design recommendations and compliance verification
            run_design_automation(
                self.file_paths, 
                self.safe_mdbs, 
                self.excel_inputs, 
                self.excel_outputs, 
                self.design_results,
                use_tgn53,
                self.log_status
            )

            # Schedule completion actions on main thread for thread-safe GUI updates
            # After() method ensures GUI updates occur on appropriate thread
            self.root.after(0, self._complete_design)
            self.root.after(0, self._auto_save_log)

        except Exception as e:
            # Comprehensive error handling with detailed diagnostic information
            # Preserves complete error context for debugging and user support
            error_msg = f"Error in design process: {str(e)}"
            detailed_error = f"{error_msg}\n\n{traceback.format_exc()}"
            self.log_status(f"--- Error ---\n{detailed_error}")
            
            # Schedule error handling actions on main thread for thread safety
            # Lambda function ensures proper error message display without thread conflicts
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
            self.root.after(0, self.progress.stop)
            self.root.after(0, self._auto_save_log)

    def _complete_design(self) -> None:
        """
        Handle successful completion of design automation workflow.
        
        Coordinates final status updates, progress indication cleanup, and user notification
        when the complete design automation process finishes successfully. This method
        provides clear completion feedback and ensures proper cleanup of processing indicators.

        Completion Actions:
        1. **Progress Cleanup**: Stop visual progress indicator to signal completion
        2. **Status Logging**: Record successful completion with timestamp
        3. **User Notification**: Display success dialog for immediate user awareness
        4. **State Reset**: Prepare interface for potential subsequent operations

        User Experience:
        - **Visual Confirmation**: Progress bar stops to indicate completion
        - **Detailed Logging**: Timestamped completion message in status log
        - **Modal Notification**: Success dialog ensures user awareness of completion
        - **Interface Readiness**: GUI ready for new operations or result review

        Returns:
            None: Method performs completion actions with no return value.
        """
        # Stop progress indicator to provide clear visual completion feedback
        self.progress.stop()
        
        # Log successful completion with timestamp for audit trail
        self.log_status("Design Automation process finished successfully!")
        
        # Display success notification to ensure user awareness of completion
        messagebox.showinfo("Success", "Design Automation process finished successfully!")

    def _save_log_to_path(self, file_path: str) -> None:
        """
        Save complete status log content to specified file path with error handling.
        
        Performs the actual file writing operation for log preservation, including
        directory creation, proper encoding, and comprehensive error handling.
        This method handles all file system operations required for log persistence.

        File Operations:
        1. **Content Extraction**: Retrieve complete text from status widget
        2. **Directory Creation**: Ensure target directory exists for file writing
        3. **File Writing**: Save content with UTF-8 encoding for international characters
        4. **Error Handling**: Manage file system errors and permission issues
        5. **Confirmation**: Log successful save operation for user feedback
        """
        # Extract complete content from status text widget for file writing
        # Indexes 1.0 to END ensure complete content capture including final line
        log_content = self.status_text.get(1.0, tk.END)
        
        try:
            # Extract directory path and create if necessary for file accessibility
            # Only attempt directory creation if path contains directory components
            dir_path = os.path.dirname(file_path)
            if dir_path:  # Only create directory if path is not empty
                os.makedirs(dir_path, exist_ok=True)
                
            # Write log content with UTF-8 encoding for international character support
            # Context manager ensures proper file closure even if errors occur
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(log_content)
                
            # Log successful save operation for user feedback and audit trail
            self.log_status(f"Log saved to: {file_path}")
            
        except Exception as e:
            # Handle file system errors with detailed diagnostic information
            # Error logging helps users identify and resolve file access issues
            self.log_status(f"Failed to save log to {file_path}: {str(e)}")

    def _auto_save_log(self) -> None:
        """
        Automatically save log to predefined path if configured in file paths.
        
        Provides automatic log preservation using the configured log file path
        from the file paths structure. This ensures important operation logs
        are preserved without requiring explicit user action.

        Auto-Save Logic:
        1. **Path Verification**: Check if log path is defined in file paths configuration
        2. **Conditional Save**: Only attempt save if valid path is available
        3. **Delegation**: Use standard log saving method for consistent operation
        4. **Silent Operation**: No user interaction required for automatic saving

        Integration:
        - **File Paths**: Uses log path from initialized file paths structure
        - **Standard Method**: Delegates to _save_log_to_path for consistent handling
        - **Workflow Integration**: Called automatically at key workflow completion points
        - **Error Handling**: Inherits error handling from delegated save method

        Returns:
            None: Method performs automatic save operation when configured.

        Configuration Requirements:
        - **File Paths Initialization**: file_paths object must be properly initialized
        - **Log Path Configuration**: Log attribute must contain valid file path
        - **Directory Permissions**: Write access required for configured log directory
        """
        # Check if file paths are initialized and log path is configured
        # Auto-save only occurs when explicitly configured by user or system
        if hasattr(self.file_paths, 'Log') and self.file_paths.Log:
            # Delegate to standard log saving method for consistent operation
            # This ensures uniform error handling and status reporting
            self._save_log_to_path(self.file_paths.Log)

    def log_status(self, message: str) -> None:
        """
        Add timestamped message to status display with performance-optimized batch processing.
        
        This method provides the primary logging interface for the application, handling
        both immediate console output and GUI display with performance optimization through
        batch processing. The batching system prevents GUI freezing during intensive
        logging operations while maintaining real-time feedback for users.

        Logging Architecture:
        - **Immediate Console**: Direct terminal output for development and debugging
        - **Queued GUI Updates**: Batch processing prevents interface blocking
        - **Timestamp Integration**: Automatic timestamp generation for audit trails
        - **Performance Optimization**: Batch updates every 100ms for optimal responsiveness

        Message Processing:
        1. **Timestamp Generation**: Add current date/time for chronological tracking
        2. **Console Output**: Immediate terminal display for real-time monitoring
        3. **Queue Addition**: Store message for batch GUI processing
        4. **Batch Scheduling**: Schedule GUI update if not already pending
        """
        # Generate timestamp for chronological message ordering and audit trail
        # Format provides precise timing for operation tracking and debugging
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        full_message = f"[{timestamp}] {message}\n"
        
        # Provide immediate console output for development and monitoring
        # Strip newline for clean terminal display while preserving GUI formatting
        print(full_message.rstrip())  # Remove the newline for terminal output
        
        # Add message to batch processing queue for performance-optimized GUI updates
        # Bounded queue prevents memory growth during extensive logging operations
        self.log_queue.append(full_message)
        
        # Schedule batch GUI update if not already pending to prevent redundant updates
        # 100ms interval balances responsiveness with performance optimization
        if not self.log_update_pending:
            self.log_update_pending = True
            self.root.after(100, self._batch_update_status)  # Update every 100ms

    def _batch_update_status(self) -> None:
        """
        Process queued log messages in batches for optimal GUI performance.
        
        This method implements the core performance optimization for status logging,
        processing multiple queued messages in a single GUI update operation.
        Batch processing prevents interface freezing during high-volume logging
        while maintaining responsive user experience.

        Batch Processing Logic:
        1. **Queue Processing**: Extract all currently queued messages
        2. **Message Aggregation**: Combine messages into single text block
        3. **Single GUI Update**: Insert all messages in one operation
        4. **Auto-Scroll**: Ensure latest messages are visible
        5. **Flag Reset**: Clear pending flag to allow next batch cycle

        Performance Benefits:
        - **Reduced GUI Calls**: Single text widget update instead of multiple operations
        - **Responsive Interface**: Prevents blocking during intensive logging periods
        - **Memory Efficiency**: Queue clearing prevents accumulation of processed messages
        - **Visual Continuity**: Smooth scrolling maintains user experience

        Returns:
            None: Method performs GUI updates with no return value.

        Implementation Details:
        - **Queue Emptying**: Complete queue processing in each batch cycle
        - **Message Concatenation**: Efficient string joining for minimal overhead
        - **Auto-Scrolling**: Automatic positioning to latest message
        - **State Management**: Proper flag handling for next batch coordination
        """
        # Process all currently queued messages to minimize GUI update frequency
        # Batch processing provides significant performance improvement during intensive logging
        if self.log_queue:
            # Extract all queued messages for single-operation GUI update
            # List comprehension provides efficient message aggregation
            messages = []
            while self.log_queue:
                messages.append(self.log_queue.popleft())
            
            # Update text widget in single operation for optimal performance
            # Join operation minimizes string manipulation overhead
            self.status_text.insert(tk.END, ''.join(messages))
            
            # Automatically scroll to latest message for user awareness
            # Ensures new status information is immediately visible
            self.status_text.see(tk.END)
        
        # Reset pending flag to enable next batch cycle scheduling
        # Proper state management prevents missed updates and redundant scheduling
        self.log_update_pending = False

    def delete_previous_results(self) -> None:
        """
        Clean up previous design results with user confirmation and comprehensive feedback.
        
        Provides safe deletion of all previous design automation results with user
        confirmation, comprehensive validation, and detailed status reporting.
        This utility function enables clean project restarts and prevents result
        conflicts between different design iterations.

        Deletion Process:
        1. **Path Validation**: Verify SAFE database path is selected
        2. **User Confirmation**: Present clear deletion confirmation dialog
        3. **Temporary Setup**: Initialize file paths for deletion operation
        4. **Result Cleanup**: Execute deletion with progress monitoring
        5. **State Reset**: Clear application data structures
        6. **Confirmation**: Provide success/failure feedback

        Safety Features:
        - **User Confirmation**: Explicit confirmation required before deletion
        - **Path Display**: Clear indication of deletion target directory
        - **Validation Checks**: Verification of required selections before processing
        - **Error Handling**: Graceful handling of deletion failures

        Cleanup Scope:
        - **Analytical_Results Directory**: Complete removal of previous outputs
        - **Application State**: Reset of design results and output data structures
        - **File Organization**: Preparation for new design automation execution
        - **Memory Cleanup**: Clear cached results and temporary data

        Returns:
            None: Method performs cleanup operations with user feedback.

        User Experience:
        - **Clear Confirmation**: Explicit dialog showing deletion target
        - **Status Feedback**: Real-time logging of deletion progress
        - **Success Notification**: Clear indication of successful completion
        - **Error Recovery**: Informative error messages for troubleshooting

        Example Workflow:
            User clicks "Clear Previous Results" → Path validation passes
            → Confirmation dialog shows target directory → User confirms
            → Deletion executes with progress logging → Success notification
        """
        # Validate that SAFE database path has been selected before attempting cleanup
        # Early validation prevents confusion and provides clear user guidance
        mdb_path = self.mdb_path_var.get()

        if not mdb_path:
            messagebox.showerror("Error", "Please select the SAFE Result Database first.")
            return

        # Present confirmation dialog with clear indication of deletion scope
        # Detailed path display enables informed user decision making
        base_dir = os.path.dirname(mdb_path)
        results_dir = os.path.join(base_dir, 'Analytical_Results')
        
        if not messagebox.askyesno("Confirm Deletion",
                                   f"Are you sure you want to delete all previous results?\n"
                                   f"This will remove everything in:\n{results_dir}"):
            return

        # Initialize temporary file paths object for deletion operation
        # Isolated object prevents interference with main application state
        temp_file_paths = main_class.FilePaths()
        temp_file_paths.ResultMdbSAFE = mdb_path
        
        # Execute deletion with comprehensive status monitoring
        # Deletion function provides detailed progress feedback through logging callback
        success = delete_previous_results(temp_file_paths, self.log_status)
        
        # Handle completion based on deletion success status
        if success:
            # Reset application data structures to clean state for new operations
            # Complete state clearing ensures no residual data from previous runs
            self.design_results = main_class.DesignResults()
            self.excel_outputs = main_class.ExcelOutputs()
            messagebox.showinfo("Success", "Previous results cleared successfully.")
        else:
            # Provide clear error indication if deletion fails
            # Error message guides user to potential troubleshooting steps
            messagebox.showerror("Error", "Failed to clear previous results.")

    def close_window(self) -> None:
        """
        Handle application closure with user confirmation and proper cleanup.
        
        Manages the complete application shutdown process including user confirmation,
        proper resource cleanup, and appropriate closure method based on application
        deployment mode. This method ensures clean application termination regardless
        of how the GUI was instantiated.

        Closure Process:
        1. **User Confirmation**: Present exit confirmation dialog
        2. **Deployment Detection**: Determine standalone vs. integrated mode
        3. **Resource Cleanup**: Proper disposal of GUI resources and threads
        4. **System Exit**: Appropriate termination method for deployment mode

        Deployment Modes:
        - **Standalone Application**: Use sys.exit() for complete program termination
        - **Integrated Component**: Use destroy() for clean widget disposal
        - **Top-Level Window**: Use destroy() to close child window only
        - **Main Window**: Use quit() and exit() for complete application closure

        User Experience:
        - **Confirmation Dialog**: Prevents accidental application closure
        - **Clear Question**: Simple yes/no question for exit intention
        - **Immediate Response**: Quick closure upon confirmation
        - **Cancellation Support**: Easy cancellation if closure unintended

        Returns:
            None: Method performs closure operations or returns on cancellation.

        Resource Management:
        - **Thread Cleanup**: Proper termination of background processing threads
        - **GUI Resources**: Clean disposal of all tkinter widgets and windows
        - **Memory Cleanup**: Release of application data structures and references
        - **System Integration**: Appropriate cleanup based on deployment context

        Example Usage:
            User clicks "Close" → Confirmation dialog appears
            → User confirms → Resources cleaned up → Application terminates
        """
        # Present confirmation dialog to prevent accidental application closure
        # Simple yes/no question provides clear choice for user decision
        if messagebox.askyesno("Exit", "Are you sure you want to exit?"):
            # Handle closure based on application deployment mode
            # Different closure methods ensure proper cleanup for each deployment type
            if self.is_top_level:
                # Integrated mode: destroy child window but leave parent application running
                # Proper cleanup of GUI resources without affecting parent application
                self.root.destroy()
            else:
                # Standalone mode: complete application termination with full cleanup
                # Quit mainloop and exit system for complete program termination
                self.root.quit()
                sys.exit(0)
