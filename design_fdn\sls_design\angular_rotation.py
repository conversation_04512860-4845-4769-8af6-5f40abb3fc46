"""
Serviceability Limit State (SLS) Angular Rotation Analysis for Foundation Design

This module provides functionality for checking angular rotation limits in foundation slabs
as part of serviceability limit state design verification. Angular rotation analysis is
critical for ensuring structural performance meets comfort and functionality requirements
under service loads, particularly for sensitive equipment and occupancy conditions.

Key Features:
- Angular rotation limit checking against code requirements (1:500 criterion)
- Point-to-slab mapping for distributed analysis across slab elements
- Slab group organization for systematic evaluation and reporting
- Failure identification and detailed reporting for design iteration
- Integration with SAFE analysis results and foundation design workflows

Author: Foundation-Automation Development Team
Compatible with: Foundation design workflows and SAFE 16/22 analysis systems
"""

import numpy as np
import pandas as pd
from scipy.spatial import distance
from typing import Dict, List, Optional, Callable, Tuple, Any, Union
from design_fdn.design_fdn_config import (
    SHEET_VE_DSETT_FAIL, SHEET_PILE_DSETT_FAIL, SHEET_VE_DSETT_CHECK, 
    SHEET_VE_DSETT, SHEET_VE_DS, SHEET_VE_DISTANCE, SHEET_PILE_DSETT_CHECK,
    SHEET_PILE_DSETT, SHEET_PILE_DS, SHEET_PILE_DISTANCE
)

from design_fdn.sls_design.diff_settlement import _calculate_differential_settlement

def check_slab_angular_rotation_xy(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Perform comprehensive angular rotation analysis for foundation slab serviceability verification.
    
    This function analyzes angular rotation results from SAFE finite element analysis to verify
    compliance with serviceability limit state requirements. The analysis maps analysis points
    to slab elements, applies rotation limits, identifies failures, and generates detailed
    reports for design verification and iteration.

    The analysis workflow includes:
    - Point-to-slab mapping from slab geometry definitions
    - Slab group assignment for systematic organization
    - Angular rotation limit checking (1:500 criterion)
    - Failure identification and comprehensive reporting
    - Integration with overall foundation design results
    """   
    # Extract lateral displacement data from SAFE analysis results
    # This contains point-based rotation values (Rz) for serviceability evaluation
    df_lateral_displacement = excel_outputs.LateralDisplacement.copy()
    
    # Extract slab definition data with geometry and grouping information
    # Contains slab marks, groups, and semicolon-delimited point references
    df_slab = excel_inputs.Slab.copy()
    
    # Initialize point-to-slab mapping dictionary for associating analysis points with slab elements
    # This mapping is essential for organizing rotation results by structural element
    slab_point_mapping = {}

    # Process each slab definition to create comprehensive point-to-slab mapping
    for _, row in df_slab.iterrows():
        # Validate that slab has both property definition and point references
        if pd.notna(row['Slab Prop']) and pd.notna(row.get('Points')):
            slab_mark = row['Slab']
            
            # Parse semicolon-delimited point string to extract individual point references
            # Points format: "P1;P2;P3;P4" representing slab corner/edge points
            points = str(row['Points']).split(';')
            
            # Map each point to its parent slab element for rotation analysis organization
            for point in points:
                point = point.strip()  # Remove whitespace that may cause mapping issues
                if point:  # Only process non-empty point references to avoid errors
                    slab_point_mapping[point] = slab_mark    # Apply point-to-slab mapping to lateral displacement data
    # This associates each analysis point with its corresponding slab element
    df_lateral_displacement['Slab_Mark'] = df_lateral_displacement['Point'].map(slab_point_mapping)
    
    # Remove rows where point-to-slab mapping failed (unmapped points)
    # Only analyze points that belong to defined slab elements for meaningful results
    df_lateral_displacement = df_lateral_displacement.dropna(subset=['Slab_Mark'])

    # Create slab-to-group mapping for systematic organization and reporting
    # Groups allow for organized analysis of related slab elements (e.g., by floor, zone)
    slab_group_mapping = df_slab[df_slab['Slab Group'].notna()].set_index('Slab')['Slab Group'].to_dict()
    
    # Apply slab group mapping to organize results by structural groupings
    # This enables systematic review and reporting of rotation performance by zone/group
    df_lateral_displacement['Slab_Group'] = df_lateral_displacement['Slab_Mark'].map(slab_group_mapping)

    # Perform angular rotation serviceability limit checking
    # Calculate absolute rotation values for bi-directional evaluation (positive and negative rotations)
    df_lateral_displacement['abs(Rz) (Rad)'] = df_lateral_displacement['Rz (Rad)'].abs()
    
    # Apply serviceability limit criterion: 1:500 (0.002 radians) maximum rotation
    # This limit ensures acceptable performance for occupant comfort and equipment operation
    df_lateral_displacement['Check (<=1:500)'] = np.where(
        df_lateral_displacement['abs(Rz) (Rad)'] <= 1/500, 'OK', 'NOT OK'
    )

    # Calculate maximum absolute rotation for overall performance assessment
    # This provides a global measure of foundation performance for reporting
    rz_max = df_lateral_displacement['abs(Rz) (Rad)'].max()
    if log_callback:
        log_callback("")
        log_callback("📐 SERVICEABILITY VERIFICATION - Angular Rotation Analysis")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   Checking foundation slab angular rotation compliance with code requirements")
        log_callback(f"   • Maximum angular rotation detected: {rz_max:.6f} radians")
        log_callback(f"   • Maximum angular rotation ratio: 1:{1/rz_max:.0f}")
        log_callback(f"   • Code limit requirement: ≤1:500 (≤{1/500:.6f} radians)")

    # Identify and isolate points that fail the angular rotation criterion
    # Failure points require design attention and potential modification
    df_fail = df_lateral_displacement[df_lateral_displacement['abs(Rz) (Rad)'] > 1/500].copy()

    # Report overall analysis results with appropriate success/failure messaging
    if len(df_fail) > 0:
        if log_callback:
            log_callback("")
            log_callback("❌ FAIL: Angular rotation exceeds allowable code limits")
            log_callback(f"   • Detected ratio 1:{1/rz_max:.0f} > Code limit 1:500")
            log_callback("   • DESIGN ACTION REQUIRED: Foundation stiffness enhancement needed")
            log_callback("   • Consider: Increased slab thickness, additional pile support, or revised layout")
    else:
        if log_callback:
            log_callback("")
            log_callback("✅ PASS: Angular rotation complies with serviceability requirements")
            log_callback(f"   • Detected ratio 1:{1/rz_max:.0f} ≤ Code limit 1:500")
            log_callback("   • Foundation provides adequate rotational stiffness for structural performance")
            log_callback("   • Serviceability criteria satisfied for equipment operation and occupant comfort")

    # Organize results systematically for professional reporting and review
    # Sort by group, slab, output case, and point for logical presentation
    df_lateral_displacement.sort_values(by=['Slab_Group', 'Slab_Mark', 'OutputCase', 'Point'], inplace=True)
    
    # Export comprehensive results to CSV files for design documentation and review
    # Complete results file contains all analysis points with pass/fail status
    df_lateral_displacement.to_csv(file_paths.ResultAngularRotation, index=False)
    
    # Failure summary file contains only points exceeding rotation limits for focused review
    df_fail.to_csv(file_paths.ResultAngularRotationFail, index=False)

    # Update design results container with angular rotation analysis outcomes
    # These results integrate with overall foundation design workflow and documentation
    design_results.AngularRotation = df_lateral_displacement.copy()
    design_results.AngularRotationFail = df_fail.copy()
    
    return design_results


def check_slab_angular_rotation_z(
    file_paths: Any,
    safe_mdbs: Any,
    excel_inputs: Any,
    excel_outputs: Any,
    design_results: Dict[str, Any],
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Perform comprehensive angular rotation analysis for slab foundation system.
    """
    
    # Extract coordinate data for analysis points at foundation level (Z=0)
    # Filter for ground level points to focus on foundation elements
    df_point_coord = safe_mdbs.PointCoord[safe_mdbs.PointCoord['GlobalZ'] == 0].copy()

    # Get settlement displacement data from SAFE analysis results
    # This contains nodal displacements for all load cases and points
    df_nodal_displacements = excel_outputs.LateralDisplacement.copy()

    df_slab = excel_inputs.Slab.copy()
    slab_groups = df_slab['Slab Group'].unique()
    # remove empty and missing values in slab_groups
    slab_groups = slab_groups[pd.notna(slab_groups)]

    if log_callback:
        log_callback(f"Processing {len(slab_groups)} slab groups: {list(slab_groups)}")

    # Dictionary to store results for each slab group
    all_slab_results = {}
    slab_check_results = {}
    slab_fail_results = {}

    for slab_group in slab_groups:
        if log_callback:
            log_callback(f"Processing slab group: {slab_group}")
            
        slab_points = df_slab[df_slab['Slab Group'] == slab_group]['Points'].str.split(';').explode().str.strip()
        # remove duplicates in slab_points
        slab_points = slab_points.drop_duplicates().tolist()

        df_point_coord_slab = df_point_coord[df_point_coord['Point'].isin(slab_points)].copy()

        # Calculate differential settlement for slab points
        slab_results = _calculate_differential_settlement(
            df_point_coord_slab, df_nodal_displacements, slab_points, 'Slab', log_callback
        )

        # Store results for this slab group
        all_slab_results[slab_group] = slab_results

        # Create SLS compliance check matrices
        # Apply 1/500 differential settlement limit as acceptance criterion
        slab_check = slab_results['diff_settlement'].copy()
        slab_check[slab_check >= 500] = 'OK'  # Mark compliant elements as 'OK'
        slab_check_results[slab_group] = slab_check

        # Create failure identification matrices
        # Isolate non-compliant elements for detailed reporting
        slab_fail = slab_results['diff_settlement'].copy()
        slab_fail[slab_fail >= 500] = None  # Remove compliant elements from failure report
        slab_fail_results[slab_group] = slab_fail

    # Export comprehensive results to Excel file with multiple sheets
    # Each sheet represents a slab group with horizontally arranged dataframes
    if file_paths.ResultAngularRotation:
        with pd.ExcelWriter(file_paths.ResultAngularRotation, engine='openpyxl') as writer:
            for slab_group in slab_groups:
                if log_callback:
                    log_callback(f"Exporting results for slab group: {slab_group}")
                
                # Get the three dataframes for this slab group
                diff_settlement_df = all_slab_results[slab_group]['diff_settlement'].copy()
                abs_ds_df = all_slab_results[slab_group]['abs_ds'].copy()
                dist_df = all_slab_results[slab_group]['dist'].copy()
                
                # Reset index to make data easier to work with
                diff_settlement_df_reset = diff_settlement_df.reset_index()
                abs_ds_df_reset = abs_ds_df.reset_index()
                dist_df_reset = dist_df.reset_index()
                
                # Create sheet name from slab group (ensure it's a valid Excel sheet name)
                sheet_name = str(slab_group).replace('/', '_').replace('\\', '_')[:31]  # Excel sheet name limit
                
                # Arrange dataframes horizontally by concatenating columns
                # Add separators between dataframes for clarity
                
                # Add title headers for each section
                diff_settlement_df_reset.columns = ['LOAD CASE', 'POINT'] + [f"1/X_DiffSettlement_{col}" for col in diff_settlement_df_reset.columns[2:]]
                abs_ds_df_reset.columns = ['LOAD CASE', 'POINT'] + [f"abs_dS_{col}" for col in abs_ds_df_reset.columns[2:]]
                dist_df_reset.columns = ['LOAD CASE', 'POINT'] + [f"Dist_{col}" for col in dist_df_reset.columns[2:]]
                
                # Merge dataframes horizontally on LOAD CASE and POINT
                combined_df = diff_settlement_df_reset.merge(
                    abs_ds_df_reset, on=['LOAD CASE', 'POINT'], how='outer'
                ).merge(
                    dist_df_reset, on=['LOAD CASE', 'POINT'], how='outer'
                )
                
                # Write to Excel sheet
                combined_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                if log_callback:
                    log_callback(f"  ✓ Exported {len(combined_df)} rows to sheet '{sheet_name}'")

    # Export failure results to separate Excel file
    # Only include differential settlement values below threshold (< 500)
    if file_paths.ResultAngularRotationFail:
        with pd.ExcelWriter(file_paths.ResultAngularRotationFail, engine='openpyxl') as writer:
            for slab_group in slab_groups:
                # Get differential settlement dataframe for this slab group
                diff_settlement_df = all_slab_results[slab_group]['diff_settlement'].copy()
                
                # Filter only values below threshold (< 500)
                # Create a boolean mask for values that fail the criterion
                numeric_cols = diff_settlement_df.select_dtypes(include=[np.number]).columns
                fail_mask = (diff_settlement_df[numeric_cols] < 500).any(axis=1)
                
                # Apply the mask to get only failing rows
                diff_settlement_fail = diff_settlement_df[fail_mask].copy()
                
                # Further filter individual cell values < 500 (set others to NaN)
                for col in numeric_cols:
                    diff_settlement_fail[col] = diff_settlement_fail[col].where(
                        diff_settlement_fail[col] < 500, np.nan
                    )
                
                # Only export if there are failing values
                if not diff_settlement_fail.empty and not diff_settlement_fail[numeric_cols].isna().all().all():
                    sheet_name = str(slab_group).replace('/', '_').replace('\\', '_')[:31]
                    diff_settlement_fail_reset = diff_settlement_fail.reset_index()
                    diff_settlement_fail_reset.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    if log_callback:
                        log_callback(f"  ⚠️  Exported {len(diff_settlement_fail_reset)} failing rows to sheet '{sheet_name}'")

    # Update design results container with angular rotation analysis outcomes
    # Store the complete results for downstream processing
    design_results.AngularRotation = all_slab_results.copy()
    design_results.AngularRotationFail = slab_fail_results.copy()
    
    # Generate summary log for overall analysis results
    if log_callback:
        total_groups = len(slab_groups)
        failing_groups = sum(1 for group in slab_groups if not slab_fail_results[group].isna().all().all())
        
        log_callback("")
        log_callback("📐 ANGULAR ROTATION ANALYSIS COMPLETE")
        log_callback("═" * 50)
        log_callback(f"   • Total slab groups analyzed: {total_groups}")
        log_callback(f"   • Groups with failures: {failing_groups}")
        log_callback(f"   • Groups passing criteria: {total_groups - failing_groups}")
        
        if failing_groups > 0:
            log_callback("   ❌ DESIGN ACTION REQUIRED for failing groups")
            log_callback("   • Review fail report for detailed analysis")
            log_callback("   • Consider foundation stiffness enhancement")
        else:
            log_callback("   ✅ ALL GROUPS PASS: Angular rotation criteria satisfied")
    
    return design_results