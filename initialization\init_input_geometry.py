from datetime import datetime

import pandas as pd

from build_fem import build_fem_config as config


def init_input_geometry(excel_inputs, file_paths):
    path_excel_output = file_paths.ExcelGeometry

    df_site_boundary = pd.DataFrame(columns=['X (m)', 'Y (m)'])

    df_borehole = pd.DataFrame(columns=['Borehole', 'X (m)', 'Y (m)'])

    df_bp = pd.DataFrame(columns=['Pile Mark', 'Pile Section', 'X (m)', 'Y (m)', 'BX (m)', 'BY (m)',
                                  'Pile Head Condition (PIN/FIX)', 'Pile Shaft Diameter (m)',
                                  'Pile Socket Diameter (m)', 'Pile Base Diameter (m)',
                                  'Pile Cap Bottom Level (mPD)',
                                  'Ground Level (mPD)', 'Target Level (mPD)', 'Founding Level (mPD)',
                                  'Target Stratum', 'Effective Weight of Soil Column and Soil/Rock Cone (kN)'])
    df_shp = pd.DataFrame(
        columns=['Pile Mark', 'Pile Section', 'Steel Grade', 'Rock Socket Shear Stud (Y/N)', 'X (m)', 'Y (m)', 'BX (m)',
                 'BY (m)', 'Pile Local Axis (Deg)', 'Pile Head Condition (PIN/FIX)',
                 'Pile Shaft Diameter (m)', 'Pile Socket Diameter (m)', 'Pile Base Diameter (m)',
                 'Pile Cap Bottom Level (mPD)',
                 'Ground Level (mPD)', 'Target Level (mPD)', 'Founding Level (mPD)', 'Target Stratum',
                 'Effective Weight of Soil Column and Soil/Rock Cone (kN)'])
    df_dhp = pd.DataFrame(
        columns=['Pile Mark', 'Pile Section', 'Steel Grade', 'X (m)', 'Y (m)', 'BX (m)', 'BY (m)',
                 'Pile Local Axis (Deg)', 'Pile Head Condition (PIN/FIX)', 'Pile Shaft Diameter (m)',
                 'Pile Base Diameter (m)', 'Pile Cap Bottom Level (mPD)', 'Ground Level (mPD)', 'Founding Level (mPD)',
                 'Target Stratum', 'Effective Weight of Soil Column and Soil/Rock Cone (kN)', 'Group Reduction Factor'])
    df_mp = pd.DataFrame(columns=['Pile Mark', 'Pile Section', 'X (m)', 'Y (m)', 'BX (m)', 'BY (m)',
                                  'Pile Head Condition (PIN/FIX)', 'Pile Shaft Diameter (m)',
                                  'Pile Socket Diameter (m)', 'Pile Base Diameter (m)',
                                  'Pile Cap Bottom Level (mPD)',
                                  'Ground Level (mPD)', 'Target Level (mPD)', 'Founding Level (mPD)',
                                  'Target Stratum', 'Effective Weight of Soil Column and Soil/Rock Cone (kN)'])

    df_point = pd.DataFrame(columns=['Point', 'X (m)', 'Y (m)'])
    df_slab = pd.DataFrame(columns=['Slab', 'Soil Prop', 'Slab Prop', 'Load Group', 'Slab Group', 'Points'])
    df_opening = pd.DataFrame(columns=['Opening', 'Points'])
    df_beam = pd.DataFrame(columns=['Beam', 'Beam Prop', 'Points'])
    df_column = pd.DataFrame(columns=['Column', 'Center Point', 'Points'])
    df_wall = pd.DataFrame(columns=['Wall', 'Wall Group', 'Points'])

    df_point_load = pd.DataFrame(columns=['Point Load', 'Point'])
    df_line_load = pd.DataFrame(columns=['Line Load', 'Points'])
    df_lkp = pd.DataFrame(columns=['LKP', 'Load Group', 'Points'])

    df_ru = pd.DataFrame(columns=['Pile Mark', 'Pile Volume (m3)', 'Column Volume (m3)', 'Cone Volume (m3)',
                                  'Effective Weight of Concrete (kN/m3)',
                                  'Effective Weight of Soil (kN/m3)', 'Effective Weight of Rock (kN/m3)',
                                  'Soil Pile Weight (kN)', 'Soil Column Weight (kN)',
                                  'Soil Cone Weight (kN)', 'Rock Cone Weight (kN)',                                  'Effective Weight of Soil Column and Soil/Rock Cone (kN)'])

    with pd.ExcelWriter(path_excel_output) as writer:
        df_site_boundary.to_excel(writer, sheet_name=config.SHEET_SITE_BOUNDARY, index=False)
        df_borehole.to_excel(writer, sheet_name=config.SHEET_BOREHOLE, index=False)
        df_bp.to_excel(writer, sheet_name=config.SHEET_PILE_BP, index=False)
        df_shp.to_excel(writer, sheet_name=config.SHEET_PILE_SHP, index=False)
        df_dhp.to_excel(writer, sheet_name=config.SHEET_PILE_DHP, index=False)
        df_mp.to_excel(writer, sheet_name=config.SHEET_PILE_MP, index=False)
        df_point.to_excel(writer, sheet_name=config.SHEET_POINT, index=False)
        df_beam.to_excel(writer, sheet_name=config.SHEET_BEAM, index=False)
        df_column.to_excel(writer, sheet_name=config.SHEET_COLUMN, index=False)
        df_wall.to_excel(writer, sheet_name=config.SHEET_WALL, index=False)
        df_slab.to_excel(writer, sheet_name=config.SHEET_SLAB, index=False)
        df_opening.to_excel(writer, sheet_name=config.SHEET_OPENING, index=False)
        df_point_load.to_excel(writer, sheet_name=config.SHEET_POINT_LOAD, index=False)
        df_line_load.to_excel(writer, sheet_name=config.SHEET_LINE_LOAD, index=False)
        df_lkp.to_excel(writer, sheet_name=config.SHEET_LKP, index=False)
        df_ru.to_excel(writer, sheet_name=config.SHEET_RU, index=False)

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Initialized Excel Input (Geometry)!')
    return excel_inputs
