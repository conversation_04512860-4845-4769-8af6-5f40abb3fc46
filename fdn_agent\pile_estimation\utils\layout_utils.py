﻿"""
Layout utility functions for pile estimation.
"""

from math import sqrt
from typing import List, Dict, Any, Optional, Callable
from shapely.geometry import Point, Polygon
from ..data_types import Point2D
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def determine_layout_case(sub_clusters: Dict[str, Dict[str, Any]], log_callback: Optional[Callable] = None) -> str:
    """
    Determine which layout case applies based on sub-cluster composition.

    FIXED LOGIC:
    - Case 1: Exactly 1 column, 0 walls (single column only)
    - Case 2: Exactly 0 columns, 1 wall (single wall only)
    - Case 4: All other combinations (mixed elements, multiple elements, multiple sub-clusters)
    """
    log_function_entry(log_callback, "determine_layout_case", 
                      num_clusters=len(sub_clusters))
    
    try:
        num_sub_clusters = len(sub_clusters)
        enhanced_log(log_callback, f"Processing {num_sub_clusters} sub-clusters", 'DEBUG')

        if num_sub_clusters == 0:
            enhanced_log(log_callback, "No sub-clusters found, returning UNKNOWN case", 'WARNING')
            log_function_exit(log_callback, "determine_layout_case", "UNKNOWN")
            return "UNKNOWN"

        # Analyze composition of all sub-clusters
        total_columns = 0
        total_walls = 0

        enhanced_log(log_callback, "Analyzing sub-cluster composition", 'DEBUG')
        for cluster_id, cluster_data in sub_clusters.items():
            elements = cluster_data.get('elements', {})
            columns = elements.get('columns', [])
            walls = elements.get('walls', [])

            cluster_columns = len(columns)
            cluster_walls = len(walls)
            total_columns += cluster_columns
            total_walls += cluster_walls
            
            enhanced_log(log_callback, f"Cluster {cluster_id}: {cluster_columns} columns, {cluster_walls} walls", 'DEBUG')

        log_calculation_result(log_callback, "Total elements", 
                             f"{total_columns} columns, {total_walls} walls")

        # FIXED: Only single sub-cluster with specific composition qualifies for Case 1 or Case 2
        if num_sub_clusters == 1:
            enhanced_log(log_callback, "Single sub-cluster detected, checking composition", 'INFO')
            # Single sub-cluster: check specific composition
            if total_columns == 1 and total_walls == 0:
                case_result = "CASE_1"
                enhanced_log(log_callback, "Layout case determined: Single column only", 'INFO')
            elif total_columns == 0 and total_walls == 1:
                case_result = "CASE_2"
                enhanced_log(log_callback, "Layout case determined: Single wall only", 'INFO')
            else:
                # FIXED: Mixed elements (columns + walls) or multiple elements → Case 4
                case_result = "CASE_4"
                enhanced_log(log_callback, "Layout case determined: Mixed or multiple elements", 'INFO')
        else:
            # FIXED: Multiple sub-clusters always use Case 4 optimization
            case_result = "CASE_4"
            enhanced_log(log_callback, "Layout case determined: Multiple sub-clusters", 'INFO')

        log_calculation_result(log_callback, "Layout Case Decision", case_result)
        log_function_exit(log_callback, "determine_layout_case", case_result)
        return case_result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "determine_layout_case")
        enhanced_log(log_callback, "Falling back to CASE_4 due to error", 'WARNING')
        log_function_exit(log_callback, "determine_layout_case", "CASE_4")
        return "CASE_4"


def validate_spacing(positions: List[Point2D], min_spacing: float, log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """Validate minimum spacing requirements."""
    log_function_entry(log_callback, "validate_spacing", 
                      num_positions=len(positions), min_spacing=min_spacing)
    
    try:
        if len(positions) <= 1:
            enhanced_log(log_callback, "Single or no positions detected, spacing validation passed", 'DEBUG')
            result = {"is_valid": True, "violations": 0, "min_actual_spacing": float('inf')}
            log_validation_result(log_callback, "Spacing validation", True, 
                                "Single position - no spacing check needed")
            log_function_exit(log_callback, "validate_spacing", result)
            return result
        
        enhanced_log(log_callback, f"Checking spacing between {len(positions)} positions", 'DEBUG')
        violations = 0
        min_actual_spacing = float('inf')
        
        with create_timed_logger(log_callback, "spacing validation calculation") as timer:
            for i, pos1 in enumerate(positions):
                for j, pos2 in enumerate(positions[i+1:], i+1):
                    dist = sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
                    min_actual_spacing = min(min_actual_spacing, dist)
                    
                    if dist < min_spacing:
                        violations += 1
                        enhanced_log(log_callback, 
                                   f"Spacing violation: positions {i}-{j} distance {dist:.3f}m < {min_spacing}m", 
                                   'WARNING')
        
        is_valid = violations == 0
        result = {
            "is_valid": is_valid,
            "violations": violations,
            "min_actual_spacing": min_actual_spacing
        }
        
        log_calculation_result(log_callback, "Minimum actual spacing", f"{min_actual_spacing:.3f}m")
        log_constraint_check(log_callback, "Minimum spacing requirement", 
                           min_actual_spacing, min_spacing, is_valid)
        
        if violations > 0:
            enhanced_log(log_callback, f"Found {violations} spacing violations", 'WARNING')
            log_validation_result(log_callback, "Spacing validation", False, 
                                f"{violations} violations found")
        else:
            enhanced_log(log_callback, "All spacing requirements satisfied", 'INFO')
            log_validation_result(log_callback, "Spacing validation", True, 
                                "All spacings meet requirements")
        
        log_performance_metric(log_callback, "Spacing checks performed", 
                             len(positions) * (len(positions) - 1) // 2, "comparisons")
        
        log_function_exit(log_callback, "validate_spacing", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "validate_spacing")
        error_result = {"is_valid": False, "violations": -1, "min_actual_spacing": 0.0}
        log_function_exit(log_callback, "validate_spacing", error_result)
        return error_result


def calculate_pile_center(positions: List[Point2D], log_callback: Optional[Callable] = None) -> Point2D:
    """Calculate centroid of pile positions."""
    log_function_entry(log_callback, "calculate_pile_center", num_positions=len(positions))
    
    try:
        if not positions:
            enhanced_log(log_callback, "No positions provided, returning origin (0,0)", 'WARNING')
            result = (0.0, 0.0)
            log_calculation_result(log_callback, "Pile center (empty)", f"({result[0]:.3f}, {result[1]:.3f})")
            log_function_exit(log_callback, "calculate_pile_center", result)
            return result
        
        enhanced_log(log_callback, f"Calculating centroid for {len(positions)} pile positions", 'DEBUG')
        
        with create_timed_logger(log_callback, "centroid calculation") as timer:
            center_x = sum(pos[0] for pos in positions) / len(positions)
            center_y = sum(pos[1] for pos in positions) / len(positions)
            result = (center_x, center_y)
        
        log_calculation_result(log_callback, "Pile center", 
                             f"({result[0]:.3f}, {result[1]:.3f})")
        
        # Log statistics for debugging
        x_coords = [pos[0] for pos in positions]
        y_coords = [pos[1] for pos in positions]
        enhanced_log(log_callback, 
                    f"X range: [{min(x_coords):.3f}, {max(x_coords):.3f}], "
                    f"Y range: [{min(y_coords):.3f}, {max(y_coords):.3f}]", 'DEBUG')
        
        log_function_exit(log_callback, "calculate_pile_center", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_pile_center")
        error_result = (0.0, 0.0)
        log_function_exit(log_callback, "calculate_pile_center", error_result)
        return error_result


def calculate_center_offset(positions: List[Point2D], target_center: Point2D, 
                          log_callback: Optional[Callable] = None) -> float:
    """Calculate offset between pile center and target center."""
    log_function_entry(log_callback, "calculate_center_offset", 
                      num_positions=len(positions), 
                      target_center=f"({target_center[0]:.3f}, {target_center[1]:.3f})")
    
    try:
        enhanced_log(log_callback, "Computing pile center for offset calculation", 'DEBUG')
        pile_center = calculate_pile_center(positions, log_callback)
        
        enhanced_log(log_callback, f"Pile center: ({pile_center[0]:.3f}, {pile_center[1]:.3f})", 'DEBUG')
        enhanced_log(log_callback, f"Target center: ({target_center[0]:.3f}, {target_center[1]:.3f})", 'DEBUG')
        
        with create_timed_logger(log_callback, "offset distance calculation") as timer:
            offset = sqrt((pile_center[0] - target_center[0])**2 + (pile_center[1] - target_center[1])**2)
        
        log_calculation_result(log_callback, "Center offset distance", f"{offset:.3f}m")
        
        # Provide context for the offset magnitude
        if offset < 0.1:
            enhanced_log(log_callback, "Excellent pile center alignment (< 0.1m offset)", 'INFO')
        elif offset < 0.3:
            enhanced_log(log_callback, "Good pile center alignment (< 0.3m offset)", 'INFO')
        elif offset < 0.5:
            enhanced_log(log_callback, "Acceptable pile center alignment (< 0.5m offset)", 'INFO')
        else:
            enhanced_log(log_callback, f"Large center offset detected: {offset:.3f}m", 'WARNING')
        
        log_function_exit(log_callback, "calculate_center_offset", offset)
        return offset
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_center_offset")
        error_result = float('inf')
        log_function_exit(log_callback, "calculate_center_offset", error_result)
        return error_result


def apply_site_boundary_filter(positions: List[Point2D], site_boundary: Polygon,
                             log_callback: Optional[Callable] = None) -> List[Point2D]:
    """Filter positions to keep only those within site boundary."""
    log_function_entry(log_callback, "apply_site_boundary_filter", 
                      num_positions=len(positions))
    
    try:
        if site_boundary is None:
            enhanced_log(log_callback, "No site boundary provided, returning all positions", 'WARNING')
            log_function_exit(log_callback, "apply_site_boundary_filter", positions)
            return positions
        
        enhanced_log(log_callback, f"Filtering {len(positions)} positions against site boundary", 'DEBUG')
        valid_positions = []
        
        with create_timed_logger(log_callback, "boundary filtering") as timer:
            for i, pos in enumerate(positions):
                pile_point = Point(pos[0], pos[1])
                
                # Check if position is within or on boundary
                if site_boundary.contains(pile_point) or site_boundary.touches(pile_point):
                    valid_positions.append(pos)
                    enhanced_log(log_callback, f"Position {i} ({pos[0]:.3f}, {pos[1]:.3f}) accepted", 'DEBUG')
                else:
                    enhanced_log(log_callback, f"Position {i} ({pos[0]:.3f}, {pos[1]:.3f}) rejected - outside boundary", 'DEBUG')
        
        filtered_count = len(positions) - len(valid_positions)
        
        log_calculation_result(log_callback, "Positions retained", 
                             f"{len(valid_positions)}/{len(positions)}")
        
        if filtered_count > 0:
            enhanced_log(log_callback, f"Filtered out {filtered_count} positions outside site boundary", 'INFO')
            log_performance_metric(log_callback, "Filter efficiency", 
                                 (len(valid_positions) / len(positions)) * 100, "%")
        else:
            enhanced_log(log_callback, "All positions within site boundary", 'INFO')
        
        log_validation_result(log_callback, "Site boundary filtering", True, 
                            f"{len(valid_positions)} valid positions")
        
        log_function_exit(log_callback, "apply_site_boundary_filter", valid_positions)
        return valid_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "apply_site_boundary_filter")
        enhanced_log(log_callback, "Returning original positions due to filtering error", 'WARNING')
        log_function_exit(log_callback, "apply_site_boundary_filter", positions)
        return positions


def generate_layout_summary(positions: List[Point2D], load_center: Point2D, 
                           min_spacing: float, log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """Generate comprehensive layout summary."""
    log_function_entry(log_callback, "generate_layout_summary", 
                      num_positions=len(positions), 
                      load_center=f"({load_center[0]:.3f}, {load_center[1]:.3f})",
                      min_spacing=min_spacing)
    
    try:
        enhanced_log(log_callback, "Generating comprehensive layout summary", 'INFO')
        
        with create_timed_logger(log_callback, "layout summary generation") as timer:
            # Validate spacing
            enhanced_log(log_callback, "Validating pile spacing constraints", 'DEBUG')
            spacing_info = validate_spacing(positions, min_spacing, log_callback)
            
            # Calculate center offset
            enhanced_log(log_callback, "Calculating center offset from load center", 'DEBUG')
            center_offset = calculate_center_offset(positions, load_center, log_callback)
            
            # Calculate pile center
            enhanced_log(log_callback, "Computing pile centroid", 'DEBUG')
            pile_center = calculate_pile_center(positions, log_callback)
        
        summary = {
            "pile_count": len(positions),
            "pile_center": pile_center,
            "load_center": load_center,
            "center_offset": center_offset,
            "spacing_valid": spacing_info["is_valid"],
            "spacing_violations": spacing_info["violations"],
            "min_actual_spacing": spacing_info["min_actual_spacing"]
        }
        
        # Log comprehensive summary results
        enhanced_log(log_callback, "=== LAYOUT SUMMARY ===", 'INFO')
        log_calculation_result(log_callback, "Total pile count", len(positions))
        log_calculation_result(log_callback, "Pile center", 
                             f"({pile_center[0]:.3f}, {pile_center[1]:.3f})")
        log_calculation_result(log_callback, "Load center", 
                             f"({load_center[0]:.3f}, {load_center[1]:.3f})")
        log_calculation_result(log_callback, "Center offset", f"{center_offset:.3f}m")
        
        # Enhanced spacing summary with detailed analysis
        if spacing_info["is_valid"]:
            enhanced_log(log_callback, "✅ All spacing constraints satisfied", 'INFO')
            enhanced_log(log_callback, f"Minimum spacing achieved: {spacing_info['min_actual_spacing']:.3f}m (required: {min_spacing:.3f}m)", 'INFO')
        else:
            enhanced_log(log_callback, f"❌ {spacing_info['violations']} spacing violations found", 'WARNING')
            enhanced_log(log_callback, f"Minimum spacing achieved: {spacing_info['min_actual_spacing']:.3f}m (required: {min_spacing:.3f}m)", 'WARNING')
            enhanced_log(log_callback, f"Spacing deficit: {min_spacing - spacing_info['min_actual_spacing']:.3f}m", 'WARNING')

        log_calculation_result(log_callback, "Minimum actual spacing",
                             f"{spacing_info['min_actual_spacing']:.3f}m")
        log_validation_result(log_callback, "final_layout_validation", spacing_info["is_valid"],
                            f"Generated {len(positions)} piles with {'valid' if spacing_info['is_valid'] else 'invalid'} spacing")
        
        # Performance assessment
        layout_quality = "EXCELLENT" if (spacing_info["is_valid"] and center_offset < 0.1) else \
                        "GOOD" if (spacing_info["is_valid"] and center_offset < 0.3) else \
                        "ACCEPTABLE" if spacing_info["is_valid"] else "POOR"
        
        enhanced_log(log_callback, f"Overall layout quality: {layout_quality}", 'INFO')
        log_calculation_result(log_callback, "Layout quality assessment", layout_quality)
        
        enhanced_log(log_callback, "=== END SUMMARY ===", 'INFO')
        
        log_validation_result(log_callback, "Layout summary generation", True, 
                            f"Summary complete for {len(positions)} piles")
        
        log_function_exit(log_callback, "generate_layout_summary", summary)
        return summary
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generate_layout_summary")
        error_summary = {
            "pile_count": len(positions),
            "pile_center": (0.0, 0.0),
            "load_center": load_center,
            "center_offset": float('inf'),
            "spacing_valid": False,
            "spacing_violations": -1,
            "min_actual_spacing": 0.0
        }
        enhanced_log(log_callback, "Returning error summary due to calculation failure", 'ERROR')
        log_function_exit(log_callback, "generate_layout_summary", error_summary)
        return error_summary

