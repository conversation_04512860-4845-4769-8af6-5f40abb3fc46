from datetime import datetime

import pandas as pd

from build_fem import build_fem_config as config


def init_input_property(excel_inputs, file_paths):
    path_excel_output = file_paths.ExcelProperty

    concrete = [['C20', 'Concrete', 18700000, 0.2, 0.00001, 24.5, 20000],
                ['C25', 'Concrete', 20500000, 0.2, 0.00001, 24.5, 25000],
                ['C30', 'Concrete', 22200000, 0.2, 0.00001, 24.5, 30000],
                ['C35', 'Concrete', 23700000, 0.2, 0.00001, 24.5, 35000],
                ['C40', 'Concrete', 25100000, 0.2, 0.00001, 24.5, 40000],
                ['C45', 'Concrete', 26400000, 0.2, 0.00001, 24.5, 45000],
                ['C50', 'Concrete', 27700000, 0.2, 0.00001, 24.5, 50000],
                ['C55', 'Concrete', 28900000, 0.2, 0.00001, 24.5, 55000],
                ['C60', 'Concrete', 30000000, 0.2, 0.00001, 24.5, 60000],
                ['C65', 'Concrete', 31100000, 0.2, 0.00001, 24.5, 65000],
                ['C70', 'Concrete', 32200000, 0.2, 0.00001, 24.5, 70000],
                ['C75', 'Concrete', 33200000, 0.2, 0.00001, 24.5, 75000],
                ['C80', 'Concrete', 34200000, 0.2, 0.00001, 24.5, 80000],
                ['C85', 'Concrete', 35100000, 0.2, 0.00001, 24.5, 85000],
                ['C90', 'Concrete', 36000000, 0.2, 0.00001, 24.5, 90000],
                ['C95', 'Concrete', 36900000, 0.2, 0.00001, 24.5, 95000],
                ['C100', 'Concrete', 37800000, 0.2, 0.00001, 24.5, 100000],
                ['C20 (w/o SW)', 'Concrete', 18700000, 0.2, 0.00001, 0, 20000],
                ['C25 (w/o SW)', 'Concrete', 20500000, 0.2, 0.00001, 0, 25000],
                ['C30 (w/o SW)', 'Concrete', 22200000, 0.2, 0.00001, 0, 30000],
                ['C35 (w/o SW)', 'Concrete', 23700000, 0.2, 0.00001, 0, 35000],
                ['C40 (w/o SW)', 'Concrete', 25100000, 0.2, 0.00001, 0, 40000],
                ['C45 (w/o SW)', 'Concrete', 26400000, 0.2, 0.00001, 0, 45000],
                ['C50 (w/o SW)', 'Concrete', 27700000, 0.2, 0.00001, 0, 50000],
                ['C55 (w/o SW)', 'Concrete', 28900000, 0.2, 0.00001, 0, 55000],
                ['C60 (w/o SW)', 'Concrete', 30000000, 0.2, 0.00001, 0, 60000],
                ['C65 (w/o SW)', 'Concrete', 31100000, 0.2, 0.00001, 0, 65000],
                ['C70 (w/o SW)', 'Concrete', 32200000, 0.2, 0.00001, 0, 70000],
                ['C75 (w/o SW)', 'Concrete', 33200000, 0.2, 0.00001, 0, 75000],
                ['C80 (w/o SW)', 'Concrete', 34200000, 0.2, 0.00001, 0, 80000],
                ['C85 (w/o SW)', 'Concrete', 35100000, 0.2, 0.00001, 0, 85000],
                ['C90 (w/o SW)', 'Concrete', 36000000, 0.2, 0.00001, 0, 90000],
                ['C95 (w/o SW)', 'Concrete', 36900000, 0.2, 0.00001, 0, 95000],
                ['C100 (w/o SW)', 'Concrete', 37800000, 0.2, 0.00001, 0, 100000]]
    df_concrete = pd.DataFrame(concrete,
                               columns=['Material', 'Type', 'E (kN/m2)', 'U (Unitless)', 'A (1/C)',
                                        'UnitWt (kN/m3)', 'Fc (kN/m2)'])

    steel = [['S450', 'Steel', 205000000, 0.3, 0.0000117, 78.5, 430000, 430000],
             ['S450 (w/o SW)', 'Steel', 205000000, 0.3, 0.0000117, 0, 430000, 430000]]
    df_steel = pd.DataFrame(steel,
                            columns=['Material', 'Type', 'E (kN/m2)', 'U (Unitless)', 'A (1/C)',
                                     'UnitWt (kN/m3)', 'Fy (kN/m2)', 'Fu (kN/m2)'])

    rebar = [['REBAR04', 'Rebar', 200000000, 0.0000117, 78.5, 460000, 460000],
             ['REBAR13', 'Rebar', 200000000, 0.0000117, 78.5, 500000, 500000]]
    df_rebar = pd.DataFrame(rebar,
                            columns=['Material', 'Type', 'E (kN/m2)', 'A (1/C)',
                                     'UnitWt (kN/m3)', 'Fy (kN/m2)', 'Fu (kN/m2)'])

    tendon = [['A416MGr186', 'Tendon', 196500599.8512, 0.0000117, 78.5, 1690000, 1860000]]
    df_tendon = pd.DataFrame(tendon,
                             columns=['Material', 'Type', 'E (kN/m2)', 'A (1/C)',
                                      'UnitWt (kN/m3)', 'Fy (kN/m2)', 'Fu (kN/m2)'])

    beam_prop_data = []
    concrete_grades_beam = ['C45', 'C60']  # Made specific if different from column/slab
    for ConcreteGrade in concrete_grades_beam:
        for b_mm in range(500, 1000, 500):
            for d_mm in range(500, 1000, 100):
                beam_prop_data.append([f'B{d_mm}X{b_mm}_{ConcreteGrade}',
                                       ConcreteGrade,
                                       f'{d_mm / 1000}',
                                       f'{b_mm / 1000}'])
    df_beam_prop = pd.DataFrame(beam_prop_data, columns=['Beam Prop', 'Material', 'Depth (m)', 'Width (m)'])

    bp_prop_data = []
    concrete_grades_column = ['C45', 'C60']  # Made specific if different from beam/slab
    for ConcreteGrade in concrete_grades_column:
        for t_mm in range(1000, 3000, 100):
            bp_prop_data.append(
                [f'BP{t_mm}_{ConcreteGrade}', 'Circular', ConcreteGrade, t_mm / 1000, 'Yes'])

    df_bp_prop = pd.DataFrame(bp_prop_data,
                              columns=['Column', 'Type', 'Material', 'Diameter (m)',
                                       'AutoRigid (Yes/No)'])

    hp_prop_data = [
        ['UBP_305X305X149', 'General', 'S450', 'No', 318.5, 316, 20.7, 20.6, 316, 20.7, 15.2,
         0.35, 0.3, 0.0191, 0.00573597, 0.0130824, 0.00000314, 0.00010914, 0.00033322],
        ['UBP_305X305X180', 'General', 'S450', 'No', 326.7, 319.7, 24.8, 24.8, 319.7, 24.8, 15.2,
         0.35, 0.3, 0.02293, 0.00687208, 0.01585712, 0.000004627, 0.000135, 0.00041],
        ['UBP_305X305X223', 'General', 'S450', 'No', 337.9, 325.7, 30.4, 30.3, 325.7, 30.4, 15.2,
         0.35, 0.3, 0.0284, 0.00842384, 0.01980256, 0.000009984, 0.00017577, 0.00052699]
    ]
    df_hp_prop = pd.DataFrame(hp_prop_data,
                              columns=['Column', 'Type', 'Material', 'AutoRigid (Yes/No)', 'Total Depth (mm)',
                                       'Top Flange Width (mm)', 'Top Flange Thickness (mm)', 'Web Thickness (mm)',
                                       'Bottom Flange Width (mm)', 'Bottom Flange Thickness (mm)', 'Fillet Radius (mm)',
                                       'SecDim2 (m)', 'SecDim3 (m)', 'Area (m2)', 'As2 (m2)', 'As3 (m2)', 'J (m4)',
                                       'I22 (m4)', 'I33 (m4)'])

    slab_prop_data = []
    concrete_grades_slab = ['C45', 'C60']  # Made specific if different from beam/column
    for ConcreteGrade in concrete_grades_slab:
        for t_mm in range(150, 350, 50):
            slab_prop_data.append([f'S{t_mm}_{ConcreteGrade}', ConcreteGrade, f'{t_mm / 1000}'])

    df_slab_prop = pd.DataFrame(slab_prop_data, columns=['Slab Prop', 'Material', 'Thickness (m)'])

    with pd.ExcelWriter(path_excel_output) as writer:
        df_concrete.to_excel(writer, sheet_name=config.SHEET_CONCRETE, index=False)
        df_steel.to_excel(writer, sheet_name=config.SHEET_STEEL, index=False)
        df_rebar.to_excel(writer, sheet_name=config.SHEET_REBAR, index=False)
        df_tendon.to_excel(writer, sheet_name=config.SHEET_TENDON, index=False)
        df_slab_prop.to_excel(writer, sheet_name=config.SHEET_SLAB_PROP, index=False)
        df_beam_prop.to_excel(writer, sheet_name=config.SHEET_BEAM_PROP, index=False)
        df_bp_prop.to_excel(writer, sheet_name=config.SHEET_BP_PROP, index=False)
        df_hp_prop.to_excel(writer, sheet_name=config.SHEET_HP_PROP, index=False)


    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Initialized Excel Input (Property)!')
    return excel_inputs
