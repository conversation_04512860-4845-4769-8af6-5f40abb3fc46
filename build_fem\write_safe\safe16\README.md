# SAFE16 Integration Package

This package provides Python integration with SAFE 16 software for finite element analysis, specifically designed for foundation design and analysis in the Foundation Automation system.

## Package Structure

The package is organized into several key modules:

### Core Modules

1. `safe16_class.py`
   - Main data structure module
   - Implements `Safe16DataFrames` class for managing SAFE 16 data tables
   - Provides standardized DataFrame creation for various SAFE 16 data structures

2. `safe16_bp_shp_dhp.py`
   - Handles beam properties, shapes, and dimensions
   - Manages structural section properties for SAFE 16 models

3. `safe16_str_prop.py`
   - Manages structural properties and definitions
   - Defines material properties and section characteristics

### Geometry and Material Handling

1. `safe16_geometry.py`
   - Handles geometric definitions and transformations
   - Manages coordinate systems and object positioning

2. `safe16_material.py`
   - Defines material properties and characteristics
   - Manages material assignments and properties

### Load Handling

1. `safe16_load.py`
   - Manages load definitions and applications
   - Handles load patterns and magnitudes

2. `safe16_load_comb.py`
   - Manages load combinations and analysis cases
   - Implements load combination rules and factors

### Export and Integration

1. `safe16_export.py`
   - Handles export of SAFE 16 models
   - Manages file I/O operations for SAFE 16 data

## Key Features

### Data Management
- Structured representation of SAFE 16 data using Pandas DataFrames
- Multi-level indexing for complex data relationships
- Standardized data initialization and validation

### Model Building
- Automated slab mesh generation
- Beam and column property management
- Material property assignment
- Load pattern definition

### Integration
- Seamless integration with SAFE 16 software
- Support for Hong Kong CP-2004 concrete code
- Unit system management (KN, m, C)

### Error Handling
- Robust validation of input data
- Standardized error reporting
- Data consistency checks

## Usage Example

```python
from safe16_class import Safe16DataFrames

# Create SAFE 16 data structure
safe_data = Safe16DataFrames()

# Access program control data
program_control = safe_data.ProgramControl

# Access structural properties
structural_props = safe_data.StructuralProperties
```

## Configuration

### Default Settings
- Program Version: SAFE 12.3.1
- Units: KN, m, C
- Concrete Code: Hong Kong CP-2004
- Model Datum: 0m
- Standard Height Above Datum: 0m
- Standard Height Below Datum: 3m

### Customization
- All settings can be modified through the appropriate DataFrame attributes
- Follows SAFE 16 standard data structures and conventions

## Best Practices

1. Use the standardized DataFrame structure for all data operations
2. Maintain data consistency across related tables
3. Follow SAFE 16 naming conventions for objects and properties
4. Validate all input data before processing
5. Use the provided helper methods for complex operations

## Error Handling

The package includes comprehensive error handling for:
- Invalid data formats
- Missing required fields
- Data consistency violations
- File I/O operations
- SAFE 16 specific validation rules

## Integration Points

This package integrates with:
- Foundation Automation system's FEM building capabilities
- SAFE 16 finite element analysis software
- Other structural analysis modules in the system
