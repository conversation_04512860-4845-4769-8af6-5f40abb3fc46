from datetime import datetime

import pandas as pd

from read.functions import _load_sheet_data
from read.read_geometry import _identity_process


def _process_borehole_spt_data(df_borehole_spt):
    if df_borehole_spt.empty:
        return df_borehole_spt
    # loop the unique "Borehole Name" and add 'SPT Level (mPD)' = 99999 with 'SPTN Value' = 0 and 'Soil Type (GS/NCC/OS)'='GS'
    new_rows = []
    for borehole_name in df_borehole_spt['Borehole'].unique():
        new_rows.append(
            {'Borehole': borehole_name, 'SPT Level (mPD)': 99999, 'SPTN Value': 0, 'Soil Type (GS/NCC/OS)': 'GS'})
        new_rows.append(
            {'Borehole': borehole_name, 'SPT Level (mPD)': -99999, 'SPTN Value': 0, 'Soil Type (GS/NCC/OS)': 'GS'})

    if new_rows:  # Only concat if there are new rows to add
        df_borehole_spt = pd.concat([df_borehole_spt, pd.DataFrame(new_rows)], ignore_index=True)

    df_borehole_spt = df_borehole_spt.sort_values(by=['Borehole', 'SPT Level (mPD)'], ascending=[True, False])
    return df_borehole_spt


def read_input_geology(excel_inputs, file_paths):
    path_excel_input = file_paths.ExcelGeology
    geology_attributes = [
        'BoreholeSPT', 'PileSoilSpringSetting', 'LateralSoilSpring', 'Borehole'
    ]

    header_single = [0]

    try:
        _load_sheet_data(path_excel_input, 'BoreholeSPT', excel_inputs, 'BoreholeSPT', _process_borehole_spt_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'PileSoilSpringSetting', excel_inputs, 'PileSoilSpringSetting',
                         _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'LateralSoilSpring', excel_inputs, 'LateralSoilSpring', _identity_process,
                         header=header_single)

        # Create Borehole DataFrame after Borehole (from geometry) and BoreholeSPT (from geology) are loaded
        # It's assumed excel_inputs.Borehole is already populated by read_input_geometry
        # If read_input_geometry failed or Borehole was not found, it should be an empty DataFrame.
        df_borehole_loc = getattr(excel_inputs, 'Borehole', pd.DataFrame())
        df_borehole_spt = getattr(excel_inputs, 'BoreholeSPT', pd.DataFrame())

        if not df_borehole_loc.empty and not df_borehole_spt.empty:
            # Ensure 'Borehole' column exists in both DataFrames before merging
            if 'Borehole' in df_borehole_loc.columns and 'Borehole' in df_borehole_spt.columns:
                df_borehole = pd.merge(df_borehole_loc, df_borehole_spt, how='left', on='Borehole')
                # Drop rows where 'SPTN Value' is NaN only if the column exists
                if 'SPTN Value' in df_borehole.columns:
                    df_borehole = df_borehole.dropna(subset=['SPTN Value'])
                else:
                    # If 'SPTN Value' column is missing after merge (e.g. df_borehole_spt was empty or had no 'SPTN Value')
                    # df_borehole might be just df_borehole_loc or an empty df if merge failed.
                    # Decide on behavior: for now, assign as is, or assign empty if critical columns missing.
                    print(
                        "Warning: 'SPTN Value' column not found for creating Borehole DataFrame. Resulting Borehole data might be incomplete.")
                excel_inputs.Borehole = df_borehole
            else:
                print(
                    "Warning: 'Borehole' column missing in Borehole or BoreholeSPT. Cannot create combined Borehole DataFrame.")
                excel_inputs.Borehole = pd.DataFrame()
        elif not df_borehole_loc.empty and df_borehole_spt.empty:
            # If BoreholeSPT is empty (e.g., sheet not found or processing failed),
            # Borehole will effectively be Borehole, or we might want it empty.
            # For now, let's make it empty as the merge would be meaningless for geology purposes.
            print("Warning: BoreholeSPT data is empty. Combined Borehole DataFrame will be empty.")
            excel_inputs.Borehole = pd.DataFrame()
        else:
            # If Borehole is empty, or both are empty
            excel_inputs.Borehole = pd.DataFrame()

    except FileNotFoundError:
        print(
            f"Halting geology reading: Excel file at '{path_excel_input}' not found. All geology attributes will be empty.")
        for attr_name in geology_attributes:
            # Ensure Borehole is not overwritten if it was successfully read by another function
            if attr_name == 'Borehole' and hasattr(excel_inputs, 'Borehole') and not getattr(excel_inputs,
                                                                                                   'Borehole').empty:
                continue
            setattr(excel_inputs, attr_name, pd.DataFrame())
        return excel_inputs

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Read Excel Input (Geology)!')
    return excel_inputs
