"""
Foundation-Automation SAFE Mini-Pile Spring Writer - Point Spring Processing System

This module provides comprehensive mini-pile (MP) spring processing functionality for
converting pile foundation data into SAFE 16 and SAFE 22 format specifications.
It handles mini-pile spring definitions, point coordinates, spring properties, and
group assignments required for pile foundation analysis.

Key Features:
- Dual-format export supporting both SAFE 16 (legacy) and SAFE 22 (current) formats
- Mini-pile spring coordinate generation and point connectivity
- Comprehensive spring property assignment with stiffness coefficients
- Point spring assignments for pile-soil interaction modeling
- Group organization for pile analysis and design optimization
- Flexible data processing with robust error handling and validation

Mini-Pile Processing Components:
- Point Coordinates: Pile top location definitions with special point designations
- Spring Properties: Translational and rotational stiffness coefficients
- Spring Assignments: Connection between pile points and spring properties
- Group Definitions: Organizational structure for pile analysis and results
- Format Conversion: Automatic translation between SAFE 16 and SAFE 22 formats

Pile Foundation Applications:
- Mini-pile foundation systems with point spring representations
- Pile-soil interaction modeling using spring stiffness coefficients
- Group organization for pile design optimization and result processing
- Integration with lateral soil spring systems for comprehensive analysis
- Support for custom pile configurations and specialized applications

Format Compatibility:
- SAFE 16: Legacy format support with traditional pile spring structures
- SAFE 22: Enhanced format with improved connectivity and analysis features
- Automatic format conversion ensuring consistency across software versions
- Validation of pile spring constraints and engineering limits

Workflow Integration:
- Processes Excel input data into SAFE-compatible mini-pile definitions
- Coordinates with lateral soil spring generation for comprehensive pile analysis
- Integrates with foundation design modules for complete pile system modeling
- Supports group-based analysis and design optimization workflows

Dependencies:
- numpy: Numerical array operations for efficient data processing
- pandas: DataFrame operations for structured data manipulation
- Excel input data structures with validated mini-pile specifications
- SAFE format-specific dataframe structures for output generation

Technical Notes:
- Spring stiffness values are validated for engineering reasonableness
- Point coordinates are processed with special point designations for SAFE compatibility
- Group assignments support multiple organizational schemes for analysis optimization
- Error handling ensures robust processing of large pile foundation datasets
- Data validation maintains consistency between SAFE 16 and SAFE 22 formats

Helper Functions:
- _append_safe22_data: Flexible data appending utility for SAFE 22 format processing
- Supports various data types including scalars, arrays, and pandas Series
- Automatic data type conversion and validation for format compatibility

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 structural analysis software
"""

import numpy as np
import pandas as pd


def _append_safe22_data(target_df, column_data_list, num_rows):
    """
    Flexible data appending utility for SAFE 22 format DataFrame processing.
    
    This helper function provides robust data appending capabilities for SAFE 22
    format DataFrames, handling various data types and formats with automatic
    conversion and validation. It supports scalars, arrays, Series, and mixed
    data types while maintaining format consistency and error checking.

    The function handles comprehensive data processing including:
    - Multiple data type support (scalars, arrays, Series, lists)
    - Automatic broadcasting for scalar values across multiple rows
    - Data type conversion and validation for format compatibility
    - Multi-dimensional array handling with intelligent reshaping
    - Error checking and validation for data consistency

    Data Type Support:
    - pandas Series: Direct value extraction and processing
    - numpy arrays: Native array handling with dimension validation
    - Scalars: Automatic broadcasting across specified number of rows
    - Lists: Conversion to numpy arrays with object dtype support
    - None values: Proper handling with object dtype for compatibility
    """
    if num_rows == 0:
        return target_df

    processed_columns = []
    if not target_df.columns.empty and len(column_data_list) != len(target_df.columns):
        raise ValueError(
            f"Number of data columns provided ({len(column_data_list)}) "
            f"does not match number of columns in target_df ({len(target_df.columns)})."
        )

    for i, data_item in enumerate(column_data_list):
        current_col_values = None
        if isinstance(data_item, pd.Series):
            current_col_values = data_item.values
        elif isinstance(data_item, np.ndarray):
            current_col_values = data_item
        # Check for scalar types (includes str, int, float, bool, NoneType)
        elif pd.api.types.is_scalar(data_item) or data_item is None:
            current_col_values = np.full(num_rows, data_item, dtype=object if data_item is None else None)
        else:  # Assumed to be a list or other iterable
            current_col_values = np.array(data_item, dtype=object)  # Use dtype=object for mixed lists

        # Ensure the array is 1D and has the correct number of rows
        if current_col_values.ndim == 0:  # Scalar numpy array (e.g. np.array(5))
            current_col_values = np.full(num_rows, current_col_values.item(),
                                         dtype=object if current_col_values.item() is None else None)
        elif current_col_values.ndim == 1 and len(
                current_col_values) == 1 and num_rows > 1:  # Broadcast single-element array
            current_col_values = np.full(num_rows, current_col_values[0],
                                         dtype=object if current_col_values[0] is None else None)
        elif current_col_values.ndim > 1:  # Attempt to flatten if it makes sense (e.g. list of lists for a single column)
            if current_col_values.shape[0] == num_rows and current_col_values.shape[1] == 1:
                current_col_values = current_col_values.flatten()
            elif current_col_values.shape[0] == 1 and current_col_values.shape[1] == num_rows:  # row vector
                current_col_values = current_col_values.flatten()
            else:
                raise ValueError(
                    f"Column data item {i} is multidimensional with shape {current_col_values.shape} "
                    f"and cannot be unambiguously converted to {num_rows} rows."
                )

        if len(current_col_values) != num_rows:
            raise ValueError(
                f"Column data item {i} has incorrect length. Expected {num_rows}, "
                f"got {len(current_col_values)}. Original data: {str(data_item)[:100]}"
            )

        processed_columns.append(current_col_values)

    if not processed_columns and target_df.columns.empty:  # No columns to add, target also has no columns
        # Appending num_rows of empty records if num_rows > 0
        df_append = pd.DataFrame(index=pd.RangeIndex(num_rows))
    elif not processed_columns and not target_df.columns.empty:
        # This case should be caught by the check len(column_data_list) != len(target_df.columns)
        # but as a safeguard:
        raise ValueError("No data provided for non-empty target DataFrame columns.")
    else:
        # np.column_stack requires 1D arrays.
        # Ensure all columns are 1D before stacking.
        final_columns_for_stack = [col.reshape(-1) for col in processed_columns]
        values_array = np.column_stack(final_columns_for_stack)
        df_append = pd.DataFrame(values_array, columns=target_df.columns)

    return pd.concat([target_df, df_append], ignore_index=True)


def write_spring_pile_mp(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Process and export comprehensive mini-pile spring definitions for pile foundation analysis.
    
    This function orchestrates the complete mini-pile (MP) spring processing workflow,
    converting Excel-based pile foundation data into SAFE 16 and SAFE 22 format
    data structures. It handles all aspects of mini-pile modeling including point
    coordinates, spring properties, assignments, and group organization.

    The mini-pile processing workflow includes:
    1. Mini-pile spring data validation and extraction from Excel inputs
    2. Point coordinate generation with special point designations
    3. Spring property calculation and assignment with stiffness coefficients
    4. Point spring assignment linking pile points to spring properties
    5. Group definition and assignment for analysis optimization
    6. Cross-format compatibility validation between SAFE 16 and SAFE 22

    Mini-Pile Components Processed:
    - Point Coordinates: Pile top locations with global coordinates and special designations
    - Spring Properties: Translational stiffness (Ux, Uy, Uz) and rotational constraints
    - Spring Assignments: Direct linkage between pile points and spring property definitions
    - Group Organization: Multiple grouping schemes for analysis and design optimization
    - Visual Properties: Color coding for model visualization and quality assurance
    """    
    # Create working copies of related data for reference and potential future processing
    # LateralSoilSpring and Pile data are copied but not currently used in mini-pile processing
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()
    df_pile = excel_inputs.Pile.copy()

    # Process mini-pile springs only if data is available in Excel inputs
    if not excel_inputs.MPSpring.empty:
        # Create working copy of mini-pile spring data to avoid modifying source
        df_mp_spring = excel_inputs.MPSpring.copy()
        
        # Generate point names by appending '_T' suffix to pile marks for pile top identification
        # This naming convention ensures consistency with SAFE modeling requirements
        df_mp_spring['Point Name'] = df_mp_spring['Pile Mark'] + '_T'
        num_rows_mp = len(df_mp_spring)

        # SAFE 16 FORMAT PROCESSING
        
        # Add pile top point coordinates to SAFE 16 geometry definitions
        # Special points are designated for proper SAFE software recognition and processing
        data_geom_coords = {
            ('TABLE:  Object Geometry - Point Coordinates', 'Point', 'Text'): df_mp_spring['Point Name'].values,
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalX', 'm'): df_mp_spring['X (m)'].values,
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalY', 'm'): df_mp_spring['Y (m)'].values,
            ('TABLE:  Object Geometry - Point Coordinates', 'GlobalZ', 'm'): 0,
            ('TABLE:  Object Geometry - Point Coordinates', 'SpecialPt', 'Yes/No'): 'Yes'
        }
        df_append_geom_coords = pd.DataFrame(data_geom_coords)
        safe16_dfs.ObjGeomPointCoordinates = pd.concat(
            [safe16_dfs.ObjGeomPointCoordinates, df_append_geom_coords],
            ignore_index=True        )

        # SAFE 22 FORMAT PROCESSING
        
        # Add point connectivity definitions for SAFE 22 enhanced format
        # Point connectivity includes enhanced metadata for improved analysis capabilities
        # Assumed column order: 'UniqueName', 'Is Auto Point', 'IsSpecial', 'X', 'Y', 'Z', 'GUID'
        column_data_poc = [
            df_mp_spring['Point Name'],
            'No',  # Is Auto Point
            'Yes',  # IsSpecial
            df_mp_spring['X (m)'],
            df_mp_spring['Y (m)'],
            0,  # Z
            None  # GUID
        ]
        safe22_dfs.PointObjectConnectivity = _append_safe22_data(
            safe22_dfs.PointObjectConnectivity, column_data_poc, num_rows_mp        )

        # Define mini-pile spring properties for SAFE 16 format
        # Spring stiffness coefficients define pile-soil interaction behavior
        # Rotational stiffness values are set to zero for typical mini-pile applications
        data_spring_props = {
            ('TABLE:  Spring Properties - Point', 'Spring', 'Text'): df_mp_spring['Point Name'].values,
            ('TABLE:  Spring Properties - Point', 'Ux', 'kN/m'): df_mp_spring['Pile Stiffness X (kN/m)'].values,
            ('TABLE:  Spring Properties - Point', 'Uy', 'kN/m'): df_mp_spring['Pile Stiffness Y (kN/m)'].values,
            ('TABLE:  Spring Properties - Point', 'Uz', 'kN/m'): df_mp_spring['Pile Stiffness Z (kN/m)'].values,
            ('TABLE:  Spring Properties - Point', 'Rx', 'kN-m/rad'): 0,
            ('TABLE:  Spring Properties - Point', 'Ry', 'kN-m/rad'): 0,
            ('TABLE:  Spring Properties - Point', 'Rz', 'kN-m/rad'): 0,
            ('TABLE:  Spring Properties - Point', 'NonlinOpt', 'Text'): 'None (Linear)',
            ('TABLE:  Spring Properties - Point', 'Color', 'Text'): 'Green'
        }
        df_append_spring_props = pd.DataFrame(data_spring_props)
        safe16_dfs.SpringPropertiesPoint = pd.concat(
            [safe16_dfs.SpringPropertiesPoint, df_append_spring_props],
            ignore_index=True        )

        # Define enhanced spring properties for SAFE 22 format
        # SAFE 22 format includes additional specification options and metadata
        # Assumed column order: 'Name', 'Stiffness UX', 'Stiffness UY', 'Stiffness UZ', 
        # 'Stiffness RX', 'Stiffness RY', 'Stiffness RZ', 'Nonlinearity Specification', 
        # 'Nonlinear Option', 'Color', 'GUID', 'Notes'
        column_data_spps = [
            df_mp_spring['Point Name'],
            df_mp_spring['Pile Stiffness X (kN/m)'],
            df_mp_spring['Pile Stiffness Y (kN/m)'],
            df_mp_spring['Pile Stiffness Z (kN/m)'],
            0,  # Stiffness RX
            0,  # Stiffness RY
            0,  # Stiffness RZ
            'Quick',  # Nonlinearity Specification
            'None (Linear)',  # Nonlinear Option
            'Green',  # Color
            None,  # GUID
            None  # Notes
        ]
        safe22_dfs.SpringPropsPointSprings = _append_safe22_data(
            safe22_dfs.SpringPropsPointSprings, column_data_spps, num_rows_mp        )

        # Assign point springs to pile points for SAFE 16 format
        # This creates the direct linkage between pile points and their spring properties
        data_psa = {
            ('TABLE:  Point Spring Assignments', 'Point', 'Text'): df_mp_spring['Point Name'].values,
            ('TABLE:  Point Spring Assignments', 'Spring', 'Text'): df_mp_spring['Point Name'].values
        }
        df_append_psa = pd.DataFrame(data_psa)
        safe16_dfs.PointSpringAssignments = pd.concat(
            [safe16_dfs.PointSpringAssignments, df_append_psa], ignore_index=True        )

        # Assign joint springs for SAFE 22 format with enhanced connectivity
        # SAFE 22 joint assignments provide improved spring-to-point linkage capabilities
        # Assumed column order: 'UniqueName', 'SpringProp'
        column_data_jas = [
            df_mp_spring['Point Name'],  # UniqueName
            df_mp_spring['Point Name']  # SpringProp
        ]
        safe22_dfs.JointAssignsSprings = _append_safe22_data(
            safe22_dfs.JointAssignsSprings, column_data_jas, num_rows_mp        )

        # GROUP ASSIGNMENT PROCESSING FOR ANALYSIS OPTIMIZATION
        
        # Extract point names for efficient reuse in multiple group assignments
        point_names_values = df_mp_spring['Point Name'].values

        # SAFE 16 Group Assignments - Multiple grouping schemes for comprehensive analysis
        
        # Group assignment: A.Pile_Top - Collective group for all pile top points
        # Enables global analysis and design checks across all pile foundations
        data_group_top = {
            ('TABLE:  Group Assignments', 'Group', 'Text'): 'A.Pile_Top',
            ('TABLE:  Group Assignments', 'ObjType', 'Text'): 'Point',
            ('TABLE:  Group Assignments', 'ObjLabel', 'Text'): point_names_values
        }
        df_append_group_top = pd.DataFrame(data_group_top)
        safe16_dfs.GroupAssignments = pd.concat(            [safe16_dfs.GroupAssignments, df_append_group_top], ignore_index=True
        )

        # Group assignment: A.Pile_Bottom - Reference group for pile bottom connections
        # Provides organizational structure for pile bottom constraint and connection analysis
        data_group_bottom = {
            ('TABLE:  Group Assignments', 'Group', 'Text'): 'A.Pile_Bottom',
            ('TABLE:  Group Assignments', 'ObjType', 'Text'): 'Point',
            ('TABLE:  Group Assignments', 'ObjLabel', 'Text'): point_names_values
        }
        df_append_group_bottom = pd.DataFrame(data_group_bottom)
        safe16_dfs.GroupAssignments = pd.concat(            [safe16_dfs.GroupAssignments, df_append_group_bottom], ignore_index=True
        )

        # Group assignment: Individual Piles - Separate groups for each pile mark
        # Enables individual pile analysis and design optimization for specific piles
        data_group_piles = {
            ('TABLE:  Group Assignments', 'Group', 'Text'): df_mp_spring['Pile Mark'].values,
            ('TABLE:  Group Assignments', 'ObjType', 'Text'): 'Point',
            ('TABLE:  Group Assignments', 'ObjLabel', 'Text'): point_names_values
        }
        df_append_group_piles = pd.DataFrame(data_group_piles)
        safe16_dfs.GroupAssignments = pd.concat(            [safe16_dfs.GroupAssignments, df_append_group_piles], ignore_index=True
        )

        # SAFE 22 Group Assignments - Enhanced group management with improved capabilities
        # SAFE 22 format provides enhanced group assignment features and metadata
        # Assumed column order: 'Group Name', 'Object Type', 'Object Unique Name'

        # Group assignment: A.Pile_Top for SAFE 22 format with enhanced tracking
        column_data_ga_top = [
            'A.Pile_Top',
            'Point',
            df_mp_spring['Point Name']
        ]
        safe22_dfs.GroupAssignments = _append_safe22_data(
            safe22_dfs.GroupAssignments, column_data_ga_top, num_rows_mp        )

        # Group assignment: A.Pile_Bottom for SAFE 22 format with enhanced connectivity
        column_data_ga_bottom = [
            'A.Pile_Bottom',
            'Point',
            df_mp_spring['Point Name']
        ]
        safe22_dfs.GroupAssignments = _append_safe22_data(
            safe22_dfs.GroupAssignments, column_data_ga_bottom, num_rows_mp        )

        # Group assignment: Individual Piles for SAFE 22 format with enhanced capabilities
        # Each pile gets its own group based on pile mark for individual analysis and optimization
        column_data_ga_piles = [
            df_mp_spring['Pile Mark'],  # Group Name (individual pile marks)
            'Point',  # Object Type
            df_mp_spring['Point Name']  # Object Unique Name (corresponding point names)
        ]
        safe22_dfs.GroupAssignments = _append_safe22_data(
            safe22_dfs.GroupAssignments, column_data_ga_piles, num_rows_mp        )

    # Return updated dataframes with complete mini-pile spring definitions
    # Both SAFE 16 and SAFE 22 formats are processed for maximum compatibility
    return safe16_dfs, safe22_dfs
