"""SAFE22 Geometry Module

This module provides comprehensive functionality for handling geometric definitions and 
transformations in SAFE22 finite element models. It manages the conversion of geometric 
data from Excel input formats to SAFE22 DataFrame structures, handling all aspects of 
structural element geometry including connectivity, properties, and mesh parameters.

SAFE22 is CSI's next-generation finite element analysis software for reinforced concrete 
slab design and analysis, building upon the proven SAFE16 platform with enhanced 
capabilities and improved user interface.

Key Functionality:
- Point object connectivity and coordinate system management
- Beam and column geometry definitions with advanced meshing
- Wall and slab geometric properties with automatic mesh generation
- Structural element property assignments and transformations
- Coordinate system transformations and local axis definitions
- Advanced mesh generation parameters and output station control
- Group definitions and element assignments for design organization

Structural Element Types Supported:
- Points: Coordinate definitions with special point handling
- Beams: Frame elements with rectangular/custom cross-sections
- Columns: Area elements with circular/rectangular/H-shaped sections
- Walls: Null line elements for lateral force resistance
- Slabs: Floor elements with solid/ribbed configurations
- Line Loads: Distributed loading elements for structural analysis

SAFE22 Geometric Data Structure:
- Object Connectivity: Point-to-point element definitions
- Property Assignments: Cross-section and material assignments
- Insertion Points: Element local coordinate system definitions
- Auto Mesh Settings: Automatic mesh generation parameters
- Output Stations: Analysis result output locations
- Edge Constraints: Boundary condition specifications
- Group Management: Element organization for design and analysis

Geometric Property Units (Consistent with SAFE22):
- Length: m (meters) - coordinates, dimensions, offsets
- Area: m² (square meters) - cross-sectional areas
- Moment of Inertia: m⁴ (meters to the fourth power)
- Angles: degrees - rotations and orientations
- Mesh Size: m (meters) - element subdivision parameters

Advanced SAFE22 Features:
- Automatic mesh generation with size control
- Edge constraint handling for slab boundaries
- Multi-point connectivity for complex geometries
- Group-based design organization and optimization
- Enhanced output station control for detailed results
- Rigid zone modeling at beam-slab interfaces
- Advanced insertion point options for accurate modeling

Coordinate System Management:
- Global coordinate system (X, Y, Z) with Z=0 for 2D analysis
- Local element coordinate systems with insertion point control
- Point filtering for temporary construction points
- Automatic GUID generation for element tracking
- Special point designation for critical locations

Mesh Generation Control:
- Automatic mesh refinement at intersections
- User-defined maximum element sizes
- Minimum station requirements for output
- Intermediate joint recognition and handling
- Segment length control for analysis accuracy

Design Integration Features:
- Group definitions for design optimization
- Element property assignments for code compliance
- Edge constraint management for boundary conditions
- FE-based design overwrites for custom requirements
- Reinforcement bar size integration

Performance Considerations:
- Efficient DataFrame operations for large models
- Memory-optimized point processing with filtering
- Batch processing for multiple element types
- Optimized column structure handling for SAFE22 format
- Parallel processing capabilities for complex geometries

Dependencies:
- pandas: DataFrame operations and data manipulation
- numpy: Numerical operations and array handling
- typing: Type hints for improved code clarity
- Excel input data with standardized geometric format

Usage:
    from build_fem.write_safe.safe22.safe22_geometry import point_PointObjectConnectivity_safe22
    
    # Process point connectivity
    safe22_dfs, points_df = point_PointObjectConnectivity_safe22(excel_inputs, safe22_dfs)
    
    # Process beam geometry
    safe22_dfs = _write_beam_geometry_lines_safe22(excel_inputs, safe22_dfs)
    
    # Process slab elements
    safe22_dfs, slab_df = slab_FloorObjectConnectivity_safe22(excel_inputs, safe22_dfs)

Authors: <AUTHORS>
Version: 5.6.9 (SAFE22 Compatible)
Last Modified: 2024
"""

import numpy as np
import pandas as pd
from typing import Tuple, Any, Optional, Dict, List


def point_PointObjectConnectivity_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """Create point object connectivity data for SAFE22 model."""
    df_point = excel_inputs.Point
    
    # Filter out temporary construction points (those containing '_T' suffix)
    # These are typically used during geometry creation but should not appear in final model
    df_point_filtered = df_point[
        ~df_point['Point'].str.contains('_T', na=False)
    ].copy()

    # Create SAFE22 format DataFrame with required point properties
    # Structure matches SAFE22 PointObjectConnectivity table requirements
    df_22 = pd.DataFrame({
        'UniqueName': df_point_filtered['Point'],        # Point identifier for connectivity
        'Is Auto Point': 'No',                           # User-defined points (not auto-generated)
        'IsSpecial': 'Yes',                              # Mark as special points for design consideration
        'X': df_point_filtered['X (m)'],                 # X-coordinate in global system (meters)
        'Y': df_point_filtered['Y (m)'],                 # Y-coordinate in global system (meters)
        'Z': 0,                                          # Z-coordinate set to 0 for 2D slab analysis
        'GUID': None                                     # Unique identifier (populated by SAFE22)
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    # This ensures compatibility with existing SAFE22 DataFrame format
    df_append = pd.DataFrame(
        df_22.values, columns=safe22_dfs.PointObjectConnectivity.columns
    )

    # Append new point data to existing point connectivity data
    # ignore_index=True ensures continuous indexing across all points
    safe22_dfs.PointObjectConnectivity = pd.concat(
        [safe22_dfs.PointObjectConnectivity, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def beam_BeamObjectConnectivity_safe22(excel_inputs: Any, safe22_dfs: Any) -> Tuple[Any, pd.DataFrame]:
    """Create beam object connectivity data for SAFE22 model.

    Processes beam connectivity data from Excel inputs and creates SAFE22 compatible beam 
    connectivity DataFrame. This function establishes the fundamental connectivity between 
    points for beam elements, which are typically used for supporting beams, transfer beams, 
    and primary structural framing in concrete slab systems.

    The function parses point connectivity information from the Excel format (semicolon-separated 
    point pairs) and converts it to SAFE22's structured connectivity format required for 
    finite element analysis.
    """
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency with SAFE22 internal naming conventions
    # This standardizes column names for downstream processing functions
    df_beam.rename(columns={
        'Beam': 'Line (Text)',                    # Beam identifier becomes line element
        'Beam Prop': 'BeamProp (Text)'           # Property reference for section assignment
    }, inplace=True)

    # Parse semicolon-separated point connectivity string into individual point columns
    # Format: 'P1;P2' becomes PointI='P1', PointJ='P2' for I-J connectivity
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Extract essential geometric connectivity data for SAFE22 beam object creation
    # These three columns define the fundamental beam element topology
    geom_lines_df = df_beam[[
        'Line (Text)',          # Beam element identifier
        'PointI (Text)',        # Starting point (I-end)
        'PointJ (Text)'         # Ending point (J-end)
    ]]

    # Create SAFE22 format DataFrame with beam connectivity properties
    # Structure matches SAFE22 BeamObjectConnectivity table requirements
    df_22 = pd.DataFrame({
        'Unique Name': geom_lines_df['Line (Text)'],     # Beam identifier for element reference
        'UniquePtI': geom_lines_df['PointI (Text)'],     # I-end point identifier
        'UniquePtJ': geom_lines_df['PointJ (Text)'],     # J-end point identifier
        'Length': None,                                  # Element length (calculated by SAFE22)
        'GUID': None                                     # Unique identifier (populated by SAFE22)
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns = safe22_dfs.BeamObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append new beam connectivity data to existing SAFE22 beam object data
    # This builds the complete beam connectivity table for the finite element model
    safe22_dfs.BeamObjectConnectivity = pd.concat(
        [safe22_dfs.BeamObjectConnectivity, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def beam_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs):
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency and split points
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to form "PointI (Text)" and "PointJ (Text)"
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    geom_lines_df = df_beam[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']]

    df_22 = pd.DataFrame({
        'Unique Name': geom_lines_df['Line (Text)'],
        'Offset Option': 'User',
        'Offset I': 0,
        'Offset J': 0,
        'Rigid Factor': 0,
        'Self Weight Option': 'Auto'
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsEndLenOffsets.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsEndLenOffsets = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsEndLenOffsets, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def beam_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs):
    """Configure automatic mesh generation parameters for beam elements in SAFE22.

    Sets up automatic mesh generation parameters for beam elements to ensure proper finite 
    element subdivision for accurate analysis results. The function configures mesh density, 
    intersection handling, and segment length control to optimize analysis accuracy while 
    maintaining computational efficiency.

    SAFE22's automatic meshing system intelligently subdivides beam elements based on 
    geometry, loading, and intersection requirements. This function establishes the 
    parameters that control this subdivision process.
    """
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency with SAFE22 internal naming conventions
    df_beam.rename(columns={
        'Beam': 'Line (Text)',                    # Beam identifier for mesh assignment
        'Beam Prop': 'BeamProp (Text)'           # Property reference (not used for meshing)
    }, inplace=True)

    # Parse semicolon-separated point connectivity for beam identification
    # Required for proper element identification in mesh assignment table
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Extract beam identifiers for auto mesh parameter assignment
    # Only beam names are needed for mesh configuration
    geom_lines_df = df_beam[[
        'Line (Text)',          # Beam element identifier
        'PointI (Text)',        # I-end point (for reference)
        'PointJ (Text)'         # J-end point (for reference)
    ]]

    # Create SAFE22 format DataFrame with automatic mesh generation parameters
    # These settings control how SAFE22 subdivides beam elements for analysis
    df_22 = pd.DataFrame({
        'Unique Name': geom_lines_df['Line (Text)'],     # Beam identifier for mesh assignment
        'Auto Mesh': 'Yes',                              # Enable automatic mesh generation
        'At Intermediate Joints': 'Yes',                 # Refine mesh at intermediate joints
        'At Intersections': 'Yes',                       # Refine mesh at beam intersections
        'Min Number?': 'No',                             # No minimum element count enforcement
        'Max Length?': 'Yes',                            # Enforce maximum segment length
        'Segment Length': 1                              # Maximum segment length (1.0 meter)
    })

    # Convert to target DataFrame format with proper SAFE22 MultiIndex column structure
    target_columns = safe22_dfs.FrameAssignsFrameAutoMesh.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append auto mesh assignments to existing SAFE22 frame mesh data
    # This configures mesh generation for all beam elements in the model
    safe22_dfs.FrameAssignsFrameAutoMesh = pd.concat(
        [safe22_dfs.FrameAssignsFrameAutoMesh, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def beam_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs):
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency and split points
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to form "PointI (Text)" and "PointJ (Text)"
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)


    geom_lines_df = df_beam[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']]

    df_22 = pd.DataFrame({
        'Unique Name': geom_lines_df['Line (Text)'],
        'Station Option': 'Min Stations',
        'Min Stations': 3
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsOutputStations.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsOutputStations = pd.concat(
        [safe22_dfs.FrameAssignsOutputStations,
         df_append], ignore_index=True  # Verify this target table
    )
    return safe22_dfs, df_append


def _write_beam_geometry_lines_safe22(excel_inputs, safe22_dfs):
    """Coordinate complete beam geometry processing for SAFE22.

    Main coordination function that processes all beam geometry aspects for SAFE22 in the 
    correct sequence. This function orchestrates the complete beam element definition workflow 
    including connectivity, mesh settings, offsets, and output control.

    The function ensures that beam elements are properly defined with all necessary attributes 
    for finite element analysis, including automatic mesh generation, output stations for 
    results, and end length offsets for accurate modeling.
    """
    # Step 1: Establish fundamental beam connectivity between points
    # This creates the basic beam element topology in the finite element model
    safe22_dfs, df_append = beam_BeamObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)

    # Step 2: Configure beam end length offsets for accurate connection modeling
    # This handles rigid zones and connection details at beam ends
    safe22_dfs, df_append = beam_FrameAssignsEndLenOffsets_safe22(
        excel_inputs, safe22_dfs)

    # Step 3: Set up automatic mesh generation parameters for beam elements
    # This ensures proper element subdivision for accurate analysis results
    safe22_dfs, df_append = beam_FrameAssignsFrameAutoMesh_safe22(
        excel_inputs, safe22_dfs)

    # Step 4: Define output stations for detailed result extraction along beam length
    # This controls where analysis results are calculated and reported
    safe22_dfs, df_append = beam_FrameAssignsOutputStations_safe22(
        excel_inputs, safe22_dfs)

    return safe22_dfs


def beam_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency and split points
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to form "PointI (Text)" and "PointJ (Text)"
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    prop_assign_df = df_beam[~df_beam['BeamProp (Text)'].isnull()].reset_index(
        drop=True)

    df_22 = pd.DataFrame({
        'UniqueName': prop_assign_df['Line (Text)'],
        'Shape': None,  # Default value, or determine if available from source
        'Auto Select List': 'N.A.',  # Default value
        'Section Property': prop_assign_df['BeamProp (Text)']
    })

    target_columns = safe22_dfs.FrameAssignsSectProp.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsSectProp = pd.concat(
        [safe22_dfs.FrameAssignsSectProp, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def beam_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs):
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for consistency and split points
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to form "PointI (Text)" and "PointJ (Text)"
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)


    insertion_point_df = df_beam[~df_beam['Line (Text)'].isnull()].reset_index(
        drop=True)

    df_22 = pd.DataFrame({
        'UniqueName': insertion_point_df['Line (Text)'],
        'Cardinal Point': '8 (Top Center)',  # Default from original
        'Mirror2': 'No',
        'Mirror3': 'No',
        'No Transform Stiffness': 'No'
    })

    target_columns = safe22_dfs.FrameAssignsInsertionPoint.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsInsertionPoint = pd.concat(
        [safe22_dfs.FrameAssignsInsertionPoint, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def wall_NullLineObjectConnectivity_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_wall['Line (Text)'],
        'UniquePtI': df_wall['PointI (Text)'],
        'UniquePtJ': df_wall['PointJ (Text)'],
        'Length': None,  # Default value
        'GUID': None  # Default value
    })

    target_columns = safe22_dfs.NullLineObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.NullLineObjectConnectivity = pd.concat(
        [safe22_dfs.NullLineObjectConnectivity, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def wall_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_wall['Line (Text)'],
        'Offset Option': 'User',
        'Offset I': 0,
        'Offset J': 0,
        'Rigid Factor': 0,
        'Self Weight Option': 'Auto'
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsEndLenOffsets.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsEndLenOffsets = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsEndLenOffsets, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def wall_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_wall['Line (Text)'],
        'Auto Mesh': 'Yes',
        'At Intermediate Joints': 'Yes',
        'At Intersections': 'Yes',
        'Min Number?': 'No',
        'Max Length?': 'Yes',
        'Segment Length': 1
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsFrameAutoMesh.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsFrameAutoMesh = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsFrameAutoMesh, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def wall_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()
    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'UniqueName': df_wall['Line (Text)'],
        'Cardinal Point': '8 (Top Center)',  # Default value
        'Mirror2': 'No',  # Default value
        'Mirror3': 'No',  # Default value
        'No Transform Stiffness': 'No'  # Default value
    })

    target_columns = safe22_dfs.FrameAssignsInsertionPoint.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsInsertionPoint = pd.concat(
        [safe22_dfs.FrameAssignsInsertionPoint, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def wall_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_wall['Line (Text)'],
        'Station Option': 'Min Stations',
        'Min Stations': 3
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsOutputStations.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsOutputStations = pd.concat(
        [safe22_dfs.FrameAssignsOutputStations,
         df_append], ignore_index=True  # Verify this target table
    )
    return safe22_dfs, df_append


def wall_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_wall = excel_inputs.Wall.copy()

    # explode "Points" by ';' to form "PointI (Text)" and "PointJ (Text)" in dataframe
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    df_wall = df_wall[[
        'Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any of the key identifiers are NaN, as they cannot define a line
    df_wall.dropna(
        subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_wall['Line (Text)'],
        'Shape': None,
        'Auto Select List': 'N.A.',
        'Section Property': 'None'
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsSectProp.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsSectProp = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsSectProp, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def _write_wall_geometry_lines_safe22(excel_inputs, safe22_dfs):
    safe22_dfs, df_append = wall_NullLineObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_FrameAssignsEndLenOffsets_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_FrameAssignsFrameAutoMesh_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_FrameAssignsInsertionPoint_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_FrameAssignsOutputStations_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = wall_FrameAssignsSectProp_safe22(
        excel_inputs, safe22_dfs)

    return safe22_dfs


def _write_column_assign_safe22(excel_inputs, safe22_dfs):
    safe22_dfs, df_append = column_NullAreaObjectConnectivity_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_AreaAssignsInsertionPoint_safe22(
        excel_inputs, safe22_dfs)
    safe22_dfs, df_append = column_AreaAssignsSectProp_safe22(
        excel_inputs, safe22_dfs)
    return safe22_dfs


def column_NullAreaObjectConnectivity_safe22(excel_inputs, safe22_dfs):
    df_column = excel_inputs.Column.copy()

    df_column['Auto (Yes/No)'] = 'No'
    # rename 'Column' to 'Area (Text)' for consistency with other functions
    df_column.rename(
        columns={
            'Column': 'Area (Text)',
            'Center Point': 'Center Point (Text)'},
        inplace=True
    )

    # Verify this target table
    target_columns = safe22_dfs.NullAreaObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    if df_column.index.size != 0:
        for i in range(df_column.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_column.columns and not pd.isna(df_column['Points'][i]):
                # Split points by semicolon
                points = df_column['Points'][i].split(';')

                # Process points in groups of 4
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Get 4 points, padding with np.nan if less than 4 remain
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    row_22 = df_append.index.size
                    if is_row_start:
                        df_append.loc[row_22] = [
                            df_column['Area (Text)'][i], point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]
                        is_row_start = False
                    else:
                        df_append.loc[row_22] = [
                            df_column['Area (Text)'][i], point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]

        safe22_dfs.NullAreaObjectConnectivity = pd.concat(
            [safe22_dfs.NullAreaObjectConnectivity,
             df_append], ignore_index=True  # Verify this target table
        )
    return safe22_dfs, df_append


def column_AreaAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs):
    df_column = excel_inputs.Column.copy()

    df_column['Auto (Yes/No)'] = 'No'
    # rename 'Column' to 'Area (Text)' for consistency with other functions
    df_column.rename(
        columns={
            'Column': 'Area (Text)',
            'Center Point': 'Center Point (Text)'},
        inplace=True
    )

    # Verify this target table
    target_columns = safe22_dfs.AreaAssignsInsertionPoint.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    if df_column.index.size != 0:
        for i in range(df_column.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_column.columns and not pd.isna(df_column['Points'][i]):
                # Split points by semicolon
                points = df_column['Points'][i].split(';')

                # Process points in groups of 4, but only create one insertion point record per area
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Only add one insertion point record for the first group of points
                    if is_row_start:
                        idx_22 = df_append.index.size
                        df_append.loc[idx_22] = [
                            df_column['Area (Text)'][i], 'Top', 'No']
                        is_row_start = False
                        break  # Only need one insertion point record per area

        safe22_dfs.AreaAssignsInsertionPoint = pd.concat(
            # Verify this target table
            [safe22_dfs.AreaAssignsInsertionPoint, df_append], ignore_index=True
        )
    return safe22_dfs, df_append


def column_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_column = excel_inputs.Column.copy()

    df_column['Auto (Yes/No)'] = 'No'
    # rename 'Column' to 'Area (Text)' for consistency with other functions
    df_column.rename(
        columns={
            'Column': 'Area (Text)',
            'Center Point': 'Center Point (Text)'},
        inplace=True
    )

    # Verify this target table
    target_columns = safe22_dfs.AreaAssignsSectProp.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    if df_column.index.size != 0:
        for i in range(df_column.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_column.columns and not pd.isna(df_column['Points'][i]):
                # Split points by semicolon
                points = df_column['Points'][i].split(';')

                # Process points in groups of 4, but only create one section property record per area
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Only add one section property record for the first group of points
                    if is_row_start:
                        idx_22 = df_append.index.size
                        df_append.loc[idx_22] = [
                            df_column['Area (Text)'][i], 'None', 'Null']
                        is_row_start = False
                        break  # Only need one section property record per area

        safe22_dfs.AreaAssignsSectProp = pd.concat(
            # Verify this target table
            [safe22_dfs.AreaAssignsSectProp, df_append], ignore_index=True
        )
    return safe22_dfs, df_append


def _create_safe22_slab_area_data(slab_name, points_list, safe22_dfs):
    num_points = len(points_list)
    # Determine the maximum number of points based on the target table columns
    # This assumes columns are named Point1, Point2, ... PointN
    max_points_in_table = 0
    for col_tuple in safe22_dfs.FloorObjectConnectivity.columns:
        # Assuming first element of tuple is the main column name
        col_name = col_tuple[0]
        if isinstance(col_name, str) and col_name.startswith('Point') and col_name[5:].isdigit():
            max_points_in_table = max(max_points_in_table, int(col_name[5:]))

    if max_points_in_table == 0:  # Fallback or if table is empty/not structured as expected
        max_points_in_table = 16  # Default to a reasonable number if detection fails
        # print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} Warning: Could not determine max points for SAFE22 FloorObjectConnectivity. Defaulting to {max_points_in_table}.")

    data = {
        ('Unique Name', ''): slab_name,
        ('Number Points', ''): num_points,
        ('GUID', ''): None  # Default
    }
    for i in range(1, max_points_in_table + 1):
        point_val = points_list[i - 1] if i <= num_points else None
        # SAFE22 columns might not have '(Text)'
        data[(f'Point{i}', '')] = point_val
    return pd.DataFrame([data])


def slab_AreaAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    df_22 = pd.DataFrame({
        'Column 1': df_slab['Area (Text)'],
        'Column 2': 'Top',  # Default value
        'Column 3': 'No'  # Default value
    })

    target_columns = safe22_dfs.AreaAssignsInsertionPoint.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.AreaAssignsInsertionPoint = pd.concat(
        [safe22_dfs.AreaAssignsInsertionPoint, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_AreaAssignsEdgeConstraints_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    df_slab = df_slab[df_slab['SlabProp (Text)'].notna()]

    df_22 = pd.DataFrame({
        'Column 1': df_slab['Area (Text)'],
        'Column 2': 'Yes',  # Default value
    })

    target_columns = safe22_dfs.AreaAssignsEdgeConstraints.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.AreaAssignsEdgeConstraints = pd.concat(
        [safe22_dfs.AreaAssignsEdgeConstraints, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_AreaAssignsFloorAutoMesh_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    df_slab = df_slab[df_slab['SlabProp (Text)'].notna()]

    df_22 = pd.DataFrame({
        'Column 1': df_slab['Area (Text)'],
        'Column 2': 'Default',
        'Column 3': 'No'
    })

    target_columns = safe22_dfs.AreaAssignsFloorAutoMesh.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.AreaAssignsFloorAutoMesh = pd.concat(
        [safe22_dfs.AreaAssignsFloorAutoMesh, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_ConcSlbOverFEBased_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    df_slab = df_slab[df_slab['SlabProp (Text)'].notna()]

    df_22 = pd.DataFrame({
        'Column 1': df_slab['Area (Text)'],
        'Column 2': 'REBAR13',
        'Column 3': 'From Preferences',
        'Column 4': 1,
        'Column 5': 'Yes',
        'Column 6': 'No'
    })

    target_columns = safe22_dfs.ConcSlbOverFEBased.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.ConcSlbOverFEBased = pd.concat(
        [safe22_dfs.ConcSlbOverFEBased, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def lkp_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_lkp = excel_inputs.LKP.copy()

    df_lkp['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_lkp.rename(
        columns={
            'LKP': 'Area (Text)',
            'Load Group': 'LoadGroup (Text)'},
        inplace=True
    )

    df_22 = pd.DataFrame({
        'Column 1': df_lkp['Area (Text)'],
        'Column 2': 'None',
        'Column 3': 'Null'
    })

    target_columns = safe22_dfs.AreaAssignsSectProp.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.AreaAssignsSectProp = pd.concat(
        [safe22_dfs.AreaAssignsSectProp, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_FloorObjectConnectivity_safe22(excel_inputs, safe22_dfs):
    """Create slab floor object connectivity data for SAFE22 model.

    Processes slab connectivity data from Excel inputs and creates SAFE22 compatible floor 
    object connectivity DataFrame. This function handles complex multi-point slab geometries 
    by processing variable numbers of points that define slab boundaries and openings.

    Slabs in SAFE22 are represented as floor objects that can have complex geometries with 
    multiple points defining the perimeter. The function processes points in groups of 4 
    (quadrilaterals) but can handle irregular polygons by creating multiple connectivity 
    records for the same slab.
    """
    df_slab = excel_inputs.Slab.copy()

    # Set default properties for slab elements in SAFE22
    df_slab['Auto (Yes/No)'] = 'No'  # User-defined slabs (not auto-generated)
    
    # Rename columns for consistency with SAFE22 internal naming conventions
    # This standardizes column names across all slab processing functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',                    # Slab identifier becomes area element
            'Soil Prop': 'SoilProp (Text)',          # Soil property reference (if applicable)
            'Slab Prop': 'SlabProp (Text)',          # Slab property reference for thickness/material
            'Load Group': 'LoadGroup (Text)',        # Load group assignment for loading
            'Slab Group': 'SlabGroup (Text)'},       # Slab group for design organization
        inplace=True
    )

    # Initialize target DataFrame with proper SAFE22 MultiIndex column structure
    target_columns = safe22_dfs.FloorObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    # Process each slab in the input data
    if df_slab.index.size != 0:
        for i in range(df_slab.index.size):
            slab_prop = df_slab['SlabProp (Text)'][i]
            
            # Only process slabs that have valid property assignments and point data
            if 'Points' in df_slab.columns and not pd.isna(df_slab['Points'][i]):
                # Parse semicolon-separated point connectivity string
                # Format: 'P1;P2;P3;P4;P5;P6' for complex slab geometries
                points = df_slab['Points'][i].split(';')

                # Process points in groups of 4 to handle complex slab geometries
                # Each group represents a quadrilateral sub-region of the slab
                for j in range(0, len(points), 4):
                    # Extract up to 4 points for current connectivity record
                    point_group = points[j:j+4]
                    
                    # Pad with NaN if fewer than 4 points remain in group
                    # This ensures consistent record structure for SAFE22
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    # Only create connectivity record if slab has valid property assignment
                    if not pd.isna(slab_prop):
                        # Create new connectivity record with slab name and 4 corner points
                        # Additional None values are for extended point fields and metadata
                        new_row = [
                            df_slab['Area (Text)'][i],    # Slab identifier
                            point_group[0],               # Point 1 (corner 1)
                            point_group[1],               # Point 2 (corner 2)
                            point_group[2],               # Point 3 (corner 3)
                            point_group[3],               # Point 4 (corner 4)
                            None,                         # Extended point fields
                            None, None, None              # Additional metadata fields
                        ]
                        
                        # Add connectivity record to the DataFrame
                        idx_22 = df_append.index.size
                        df_append.loc[idx_22] = new_row

    # Append all new slab connectivity records to existing SAFE22 floor object data
    # This builds the complete floor connectivity table for finite element analysis
    safe22_dfs.FloorObjectConnectivity = pd.concat(
        [safe22_dfs.FloorObjectConnectivity, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def lkp_NullAreaObjectConnectivity_safe22(excel_inputs, safe22_dfs):
    df_lkp = excel_inputs.LKP.copy()

    df_lkp['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_lkp.rename(
        columns={
            'LKP': 'Area (Text)',
            'Load Group': 'LoadGroup (Text)'},
        inplace=True
    )

    target_columns = safe22_dfs.NullAreaObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    if df_lkp.index.size != 0:
        for i in range(df_lkp.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_lkp.columns and not pd.isna(df_lkp['Points'][i]):
                # Split points by semicolon
                points = df_lkp['Points'][i].split(';')

                # Process points in groups of 4
                for j in range(0, len(points), 4):
                    # Get 4 points, padding with np.nan if less than 4 remain
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    new_row = [df_lkp['Area (Text)'][i], point_group[0], point_group[1], point_group[2], point_group[3], None,
                                None, None]
                    idx_22 = df_append.index.size
                    df_append.loc[idx_22] = new_row

    safe22_dfs.NullAreaObjectConnectivity = pd.concat(
        [safe22_dfs.NullAreaObjectConnectivity, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_AreaAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'
    # rename 'Slab' to 'Area (Text)' for consistency with other functions
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    condition = ~df_slab['SlabProp (Text)'].isnull()
    df = df_slab.loc[condition].reset_index(drop=True)

    tuples_22 = [
        ('TABLE:  Area Assignments - Section Properties', 'UniqueName', ''),
        ('TABLE:  Area Assignments - Section Properties', 'Section Property', ''),
        ('TABLE:  Area Assignments - Section Properties', 'Property Type', '')]
    df_append = pd.DataFrame(columns=tuples_22)

    if df.index.size != 0:
        df = df[['Area (Text)', 'SlabProp (Text)']]
        df_22 = df.copy()
        df_22['Property Type'] = 'Slab'

        df_append = pd.DataFrame(df_22.values, columns=tuples_22)
        safe22_dfs.AreaAssignsSectProp = pd.concat(
            [safe22_dfs.AreaAssignsSectProp, df_append], ignore_index=True)
    return safe22_dfs, df_append


def slab_GroupDefinitions_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()
    df_slab.dropna(subset=['Slab Group'], inplace=True)
    target_columns_tuples = safe22_dfs.GroupDefinitions.columns.tolist()

    df_22_list = []
    # Use unique to avoid duplicate definitions
    for group_name in df_slab['Slab Group'].unique():
        df_22_list.append({
            target_columns_tuples[0]: group_name,
            target_columns_tuples[1]: 'Red',
            target_columns_tuples[2]: 'No',
            target_columns_tuples[3]: 'No',
            target_columns_tuples[4]: 'No',
            target_columns_tuples[5]: None  # GUID
        })

    if not df_22_list:
        return

    df_append = pd.DataFrame(df_22_list)
    df_append = df_append[list(
        pd.MultiIndex.from_tuples(target_columns_tuples))]

    safe22_dfs.GroupDefinitions = pd.concat(
        [safe22_dfs.GroupDefinitions, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def slab_GroupAssignments_safe22(excel_inputs, safe22_dfs):
    df_slab = excel_inputs.Slab.copy()
    df_slab.dropna(subset=['Slab Group'], inplace=True)

    target_columns_tuples = safe22_dfs.GroupAssignments.columns.tolist()

    all_new_assignments_22 = []
    # Iterate through each row with individual slab and its corresponding SlabGroup
    for _, row in df_slab.iterrows():
        slab_name = row['Slab']  # Individual slab name
        group_name = row['Slab Group']   # Corresponding group name

        all_new_assignments_22.append({
            target_columns_tuples[0]: group_name,
            target_columns_tuples[1]: 'Area',
            target_columns_tuples[2]: slab_name
        })

    if not all_new_assignments_22:
        # Return empty DataFrame with proper structure if no assignments
        empty_df = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns_tuples))
        return safe22_dfs, empty_df

    df_append = pd.DataFrame(all_new_assignments_22)
    df_append = df_append[list(
        pd.MultiIndex.from_tuples(target_columns_tuples))]

    safe22_dfs.GroupAssignments = pd.concat(
        [safe22_dfs.GroupAssignments, df_append], ignore_index=True
    )

    return safe22_dfs, df_append


def line_load_NullLineObjectConnectivity_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)


    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    target_cols = safe22_dfs.NullLineObjectConnectivity.columns.tolist()
    df_append = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_cols))
    df_append[('TABLE:  Null Line Object Connectivity',
               'Unique Name', '')] = df_line_loads['Line Load (Text)']
    df_append[('TABLE:  Null Line Object Connectivity',
               'UniquePtI', '')] = df_line_loads['PointI (Text)']
    df_append[('TABLE:  Null Line Object Connectivity',
               'UniquePtJ', '')] = df_line_loads['PointJ (Text)']
    df_append[('TABLE:  Null Line Object Connectivity', 'Length', 'm')] = None
    df_append[('TABLE:  Null Line Object Connectivity', 'GUID', '')] = None

    safe22_dfs.NullLineObjectConnectivity = pd.concat(
        [safe22_dfs.NullLineObjectConnectivity, df_append], ignore_index=True
    )

    return safe22_dfs, df_append


def line_load_FrameAssignsEndLenOffsets_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_line_loads['Line Load (Text)'],
        'Offset Option': 'User',
        'Offset I': 0,
        'Offset J': 0,
        'Rigid Factor': 0,
        'Self Weight Option': 'Auto'
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsEndLenOffsets.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsEndLenOffsets = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsEndLenOffsets, df_append], ignore_index=True
    )

    return safe22_dfs, df_append


def line_load_FrameAssignsFrameAutoMesh_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_line_loads['Line Load (Text)'],
        'Auto Mesh': 'Yes',
        'At Intermediate Joints': 'Yes',
        'At Intersections': 'Yes',
        'Min Number?': 'No',
        'Max Length?': 'Yes',
        'Segment Length': 1
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsFrameAutoMesh.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsFrameAutoMesh = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsFrameAutoMesh, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def line_load_FrameAssignsInsertionPoint_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    df_22 = pd.DataFrame({
        'UniqueName': df_line_loads['Line Load (Text)'],
        'Cardinal Point': '8 (Top Center)',  # Default value
        'Mirror2': 'No',  # Default value
        'Mirror3': 'No',  # Default value
        'No Transform Stiffness': 'No'  # Default value
    })

    target_columns = safe22_dfs.FrameAssignsInsertionPoint.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsInsertionPoint = pd.concat(
        [safe22_dfs.FrameAssignsInsertionPoint, df_append], ignore_index=True
    )
    return safe22_dfs, df_append


def line_load_FrameAssignsOutputStations_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_line_loads['Line Load (Text)'],
        'Station Option': 'Min Stations',
        'Min Stations': 3
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsOutputStations.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsOutputStations = pd.concat(
        [safe22_dfs.FrameAssignsOutputStations,
         df_append], ignore_index=True  # Verify this target table
    )
    return safe22_dfs, df_append


def line_load_FrameAssignsSectProp_safe22(excel_inputs, safe22_dfs):
    df_lineload = excel_inputs.LineLoad.copy()

    # explode the 'Points' to 'PointI (Text)' and 'PointJ (Text)' columns
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # rename 'Line Load' to 'Line Load (Text)' for consistency
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    df_line_loads.dropna(subset=required_cols, inplace=True)

    df_22 = pd.DataFrame({
        'Unique Name': df_line_loads['Line Load (Text)'],
        'Shape': None,
        'Auto Select List': 'N.A.',
        'Section Property': 'None'
    })

    # Verify this target table
    target_columns = safe22_dfs.FrameAssignsSectProp.columns.tolist()
    df_append = pd.DataFrame(
        df_22.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    safe22_dfs.FrameAssignsSectProp = pd.concat(
        # Verify this target table
        [safe22_dfs.FrameAssignsSectProp, df_append], ignore_index=True
    )
    return safe22_dfs, df_append
