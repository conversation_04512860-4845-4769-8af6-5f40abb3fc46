# Foundation Design Modules (`design_fdn/`)

## Main Design Components
- **`designer_main.py`**: Core automation functions for foundation design workflow
- **`designer_gui.py`**: SafeDesignCheckerGUI class for design interface
- **`design_fdn_config.py`**: Extensive configuration with 80+ constants for:
  - File names for various design outputs
  - Excel sheet names for different design aspects
  - Folder structure for analytical results
  - Database table names for ULS/SLS analysis

## Data Processing & Analysis
- **`data_processing.py`**: Core calculation functions:
  - `cal_pile_sls()`, `cal_pile_uls()` for serviceability and ultimate limit states
  - `cal_pile_pdelta()` for P-Delta effects
  - `process_nsf()` for net shaft friction
  - `cal_settlement()` for settlement calculations
- **`pile_capacity.py`**: Comprehensive pile capacity calculations:
  - Material properties and design constants
  - Rock parameter configurations (TGN53, COP2017)
  - Capacity calculations for BP, SHP, DHP, MP pile types
  - Friction capacity calculations

## Design Algorithms
### ULS Design for Bored Piles (`uls_design_bp/`)
- **`circular_column.py`**: CircularColumn class and rebar coordinate calculations
- **`nm_curve_calc.py`**: N-M interaction curve calculations
- **`nm_database.py`**: Pre-calculated curve database management
- **`rebar_design.py`**: Optimal rebar design algorithms
- **`uls_bp_nm.py`**: Main N-M design workflow with parallel processing
- **`uls_bp_v.py`**: Shear design and link calculations
- **`prokon_c13.py`**: Prokon file generation for external verification

### ULS Design for Steel H-Piles (`uls_design_hp/`)
- **`steel_design.py`**: Steel design strength calculations
- **`uls_hp_nmv.py`**: Combined N-M-V checks for steel H-piles

### SLS Design (`sls_design/`)
- **`deflection.py`**: Pile deflection calculations and checks
- **`angular_rotation.py`**: Slab angular rotation verification
- **`diff_settlement.py`**: Differential settlement analysis

## Specialized Functions
- **`piling_schedule.py`**: Stepping effect and soil-rock cone calculations
- **`read.py`**: SAFE database reading and Excel output processing
- **`initialization.py`**: Excel file initialization for rebar configurations
- **`error_handling.py`**: Centralized error logging and details extraction