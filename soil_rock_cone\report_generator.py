"""
Report Generator Module for 3D Pile Volume Analysis

This module handles generation of detailed reports including individual pile
volume reports and overlap analysis reports in the exact format specified
in the requirements.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
from datetime import datetime

from .volume_calculator import VolumeResult
from .overlap_analyzer import OverlapResult

logger = logging.getLogger(__name__)


class ReportGenerator:
    """
    Handles generation of detailed reports for pile volume analysis.
    """
    
    def __init__(self):
        """Initialize the ReportGenerator."""
        pass
        
    def generate_individual_pile_report(self, 
                                      pile_data: pd.DataFrame,
                                      volume_results: Dict[str, VolumeResult],
                                      theoretical_volumes: pd.DataFrame) -> pd.DataFrame:
        """
        Generate individual pile volume report in the exact format specified.
        
        Args:
            pile_data: Original pile data
            volume_results: Volume calculation results
            theoretical_volumes: Theoretical volume calculations
            
        Returns:
            DataFrame with individual pile volume report
        """
        report_data = []
        
        for _, pile in pile_data.iterrows():
            pile_id = pile['pile_id']
            
            # Get volume results
            if pile_id in volume_results:
                vol_result = volume_results[pile_id]
            else:
                logger.warning(f"No volume results found for pile {pile_id}")
                continue
                
            # Get theoretical volumes
            theoretical_row = theoretical_volumes[
                theoretical_volumes['pile_id'] == pile_id
            ]
            
            if theoretical_row.empty:
                logger.warning(f"No theoretical volumes found for pile {pile_id}")
                continue
                
            theoretical_row = theoretical_row.iloc[0]
            
            # Create report row according to specification
            report_row = {
                'Pile_ID': pile_id,
                'X_Coordinate': pile['x_coord'],
                'Y_Coordinate': pile['y_coord'],
                'Top_Elevation': pile['top_elevation'],
                'Bottom_Elevation': pile['bottom_elevation'],
                'Pile_Length': pile['pile_length'],
                'Diameter': pile['diameter'],
                'Radius': pile['radius'],
                'Material_Type': pile['material_type'],
                'Projection_Angle': pile['projection_angle'],
                'Frustum_Base_Radius': pile['frustum_base_radius'],
                
                # Part 1 - Pile Cylinder
                'Part1_Volume_Calculated': vol_result.part1_volume,
                'Part1_Volume_Theoretical': theoretical_row['part1_theoretical'],
                'Part1_Volume_Difference': vol_result.part1_volume - theoretical_row['part1_theoretical'],
                'Part1_Volume_Error_Percent': self._calculate_error_percent(
                    vol_result.part1_volume, theoretical_row['part1_theoretical']
                ),
                
                # Part 2 - Soil/Rock Frustum
                'Part2_Volume_Calculated': vol_result.part2_volume,
                'Part2_Volume_Theoretical': theoretical_row['part2_theoretical'],
                'Part2_Volume_Difference': vol_result.part2_volume - theoretical_row['part2_theoretical'],
                'Part2_Volume_Error_Percent': self._calculate_error_percent(
                    vol_result.part2_volume, theoretical_row['part2_theoretical']
                ),
                
                # Part 3 - Soil Cylinder
                'Part3_Volume_Calculated': vol_result.part3_volume,
                'Part3_Volume_Theoretical': theoretical_row['part3_theoretical'],
                'Part3_Volume_Difference': vol_result.part3_volume - theoretical_row['part3_theoretical'],
                'Part3_Volume_Error_Percent': self._calculate_error_percent(
                    vol_result.part3_volume, theoretical_row['part3_theoretical']
                ),
                
                # Total Volumes
                'Total_Volume_Calculated': vol_result.total_volume,
                'Total_Volume_Theoretical': theoretical_row['total_theoretical'],
                'Total_Volume_Difference': vol_result.total_volume - theoretical_row['total_theoretical'],
                'Total_Volume_Error_Percent': self._calculate_error_percent(
                    vol_result.total_volume, theoretical_row['total_theoretical']
                ),
                
                # Boundary and Overlap Effects
                'Volume_After_Boundary_Clipping': vol_result.clipped_volume,
                'Volume_Lost_to_Overlaps': vol_result.overlap_volume,
                'Final_Assigned_Volume': vol_result.final_volume,
                
                # Efficiency Metrics
                'Boundary_Clipping_Efficiency': self._calculate_efficiency(
                    vol_result.clipped_volume, vol_result.total_volume
                ),
                'Overlap_Efficiency': self._calculate_efficiency(
                    vol_result.final_volume, vol_result.clipped_volume
                ),
                'Overall_Efficiency': self._calculate_efficiency(
                    vol_result.final_volume, vol_result.total_volume
                )
            }
            
            report_data.append(report_row)
            
        df = pd.DataFrame(report_data)
        logger.info(f"Generated individual pile report with {len(df)} piles")
        
        return df
        
    def generate_overlap_analysis_report(self, 
                                       overlap_results: List[OverlapResult],
                                       volume_results: Dict[str, VolumeResult]) -> pd.DataFrame:
        """
        Generate overlap analysis report in the exact format specified.
        
        Args:
            overlap_results: List of overlap analysis results
            volume_results: Volume calculation results
            
        Returns:
            DataFrame with overlap analysis report
        """
        if not overlap_results:
            return pd.DataFrame(columns=[
                'Pile1_ID', 'Pile2_ID', 'Overlap_Type', 'Overlap_Volume',
                'Pile1_Total_Volume', 'Pile2_Total_Volume', 'Overlap_Percentage_Pile1',
                'Overlap_Percentage_Pile2', 'Volume_Distribution_Pile1', 'Volume_Distribution_Pile2'
            ])
            
        report_data = []
        
        for overlap in overlap_results:
            pile1_id = overlap.pile1_id
            pile2_id = overlap.pile2_id
            
            # Get volume results
            vol1 = volume_results.get(pile1_id)
            vol2 = volume_results.get(pile2_id)
            
            if not vol1 or not vol2:
                logger.warning(f"Missing volume results for overlap {pile1_id}-{pile2_id}")
                continue
                
            # Calculate overlap percentages
            overlap_pct1 = self._calculate_percentage(overlap.overlap_volume, vol1.total_volume)
            overlap_pct2 = self._calculate_percentage(overlap.overlap_volume, vol2.total_volume)
            
            # Calculate volume distribution (proportional to total volumes)
            total_combined = vol1.total_volume + vol2.total_volume
            if total_combined > 0:
                dist1 = overlap.overlap_volume * (vol1.total_volume / total_combined)
                dist2 = overlap.overlap_volume * (vol2.total_volume / total_combined)
            else:
                dist1 = dist2 = 0.0
                
            report_row = {
                'Pile1_ID': pile1_id,
                'Pile2_ID': pile2_id,
                'Overlap_Type': overlap.overlap_type,
                'Overlap_Volume': overlap.overlap_volume,
                'Pile1_Total_Volume': vol1.total_volume,
                'Pile2_Total_Volume': vol2.total_volume,
                'Overlap_Percentage_Pile1': overlap_pct1,
                'Overlap_Percentage_Pile2': overlap_pct2,
                'Volume_Distribution_Pile1': dist1,
                'Volume_Distribution_Pile2': dist2
            }
            
            report_data.append(report_row)
            
        df = pd.DataFrame(report_data)
        logger.info(f"Generated overlap analysis report with {len(df)} overlaps")
        
        return df
        
    def generate_summary_report(self, 
                              pile_data: pd.DataFrame,
                              volume_results: Dict[str, VolumeResult],
                              overlap_volumes: Dict[Tuple[str, str], float],
                              boundary_info: Dict) -> Dict[str, Union[int, float, str]]:
        """
        Generate summary statistics report.
        
        Args:
            pile_data: Original pile data
            volume_results: Volume calculation results
            overlap_volumes: Overlap volume data
            boundary_info: Site boundary information
            
        Returns:
            Dictionary with summary statistics
        """
        if not volume_results:
            return {}
            
        # Extract volume data
        total_volumes = [r.total_volume for r in volume_results.values()]
        final_volumes = [r.final_volume for r in volume_results.values()]
        overlap_volume_list = [r.overlap_volume for r in volume_results.values()]
        
        summary = {
            # Project Information
            'Analysis_Date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'Total_Piles_Analyzed': len(volume_results),
            'Site_Boundary_Area': boundary_info.get('area', 0.0),
            'Site_Boundary_Perimeter': boundary_info.get('perimeter', 0.0),
            
            # Volume Statistics
            'Total_Theoretical_Volume': sum(total_volumes),
            'Total_Final_Volume': sum(final_volumes),
            'Total_Volume_Lost_to_Overlaps': sum(overlap_volume_list),
            'Average_Pile_Volume': np.mean(total_volumes),
            'Maximum_Pile_Volume': max(total_volumes) if total_volumes else 0.0,
            'Minimum_Pile_Volume': min(total_volumes) if total_volumes else 0.0,
            'Volume_Standard_Deviation': np.std(total_volumes),
            
            # Efficiency Metrics
            'Overall_Volume_Efficiency': sum(final_volumes) / sum(total_volumes) if sum(total_volumes) > 0 else 0.0,
            'Average_Overlap_Loss_Percentage': np.mean([
                r.overlap_volume / r.total_volume * 100 if r.total_volume > 0 else 0.0
                for r in volume_results.values()
            ]),
            
            # Overlap Statistics
            'Total_Overlapping_Pairs': len(overlap_volumes),
            'Total_Overlap_Volume': sum(overlap_volumes.values()),
            'Average_Overlap_Volume': np.mean(list(overlap_volumes.values())) if overlap_volumes else 0.0,
            'Maximum_Overlap_Volume': max(overlap_volumes.values()) if overlap_volumes else 0.0,
            
            # Material Type Breakdown
            'Soil_Piles_Count': len(pile_data[pile_data['material_type'].str.lower() == 'soil']),
            'Rock_Piles_Count': len(pile_data[pile_data['material_type'].str.lower() == 'rock']),
        }
        
        return summary
        
    def _calculate_error_percent(self, calculated: float, theoretical: float) -> float:
        """Calculate percentage error between calculated and theoretical values."""
        if theoretical == 0:
            return 0.0 if calculated == 0 else float('inf')
        return abs(calculated - theoretical) / theoretical * 100
        
    def _calculate_efficiency(self, actual: float, total: float) -> float:
        """Calculate efficiency as percentage."""
        if total == 0:
            return 100.0 if actual == 0 else 0.0
        return actual / total * 100
        
    def _calculate_percentage(self, part: float, total: float) -> float:
        """Calculate percentage."""
        if total == 0:
            return 0.0
        return part / total * 100
        
    def export_reports_to_csv(self, 
                            individual_report: pd.DataFrame,
                            overlap_report: pd.DataFrame,
                            summary_report: Dict,
                            output_dir: Union[str, Path] = "output") -> Dict[str, str]:
        """
        Export all reports to CSV files.
        
        Args:
            individual_report: Individual pile volume report
            overlap_report: Overlap analysis report
            summary_report: Summary statistics
            output_dir: Output directory path
            
        Returns:
            Dictionary mapping report type to file path
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Export individual pile report
        individual_file = output_path / f"individual_pile_volumes_{timestamp}.csv"
        individual_report.to_csv(individual_file, index=False)
        
        # Export overlap report
        overlap_file = output_path / f"overlap_analysis_{timestamp}.csv"
        overlap_report.to_csv(overlap_file, index=False)
        
        # Export summary report
        summary_file = output_path / f"summary_report_{timestamp}.csv"
        summary_df = pd.DataFrame([summary_report])
        summary_df.to_csv(summary_file, index=False)
        
        file_paths = {
            'individual_report': str(individual_file),
            'overlap_report': str(overlap_file),
            'summary_report': str(summary_file)
        }
        
        logger.info(f"Exported reports to {output_dir}")
        return file_paths
