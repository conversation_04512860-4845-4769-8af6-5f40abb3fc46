﻿"""
Pile Cap Geometry

Complete pile cap geometry functionality including:
- Core pile cap polygon creation functions
- Enhanced geometry utilities for columns and walls
- Coordinate transformation utilities
- Validation functions
- Type definitions and configurations
"""

from typing import List, Tuple, Dict, Any, Optional, Callable
import pandas as pd

from shapely.geometry import Point, Polygon, LineString, MultiPoint
from shapely.ops import unary_union

# Import types and exceptions
from ..data_types import GroupElements, PileCapResult, PileCapConfig, LocalCoordinateSystem
from ..exceptions import InputDataError, GeometryError, PileCapGeometryError
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)

# Import utility functions that are used by the core functions
from ..utils.geometry_utils import (
    create_column_polygons_from_excel,
    create_wall_polygons_from_excel,
    group_continuous_walls_from_excel,
    extract_all_structural_points,
    extract_wall_segments_from_excel,
    group_continuous_walls,
    create_continuous_path
)
from ..utils.validation_utils import validate_pile_cap_polygon, clip_to_site_boundary
from ..utils.coordinate_utils import (
    find_minimum_area_bounding_rectangle,
    global_to_local_coordinates,
    local_to_global_coordinates,
    generate_local_grid_positions
)


def create_pile_cap_polygon(group_elements: GroupElements,
                           excel_inputs: Optional[Any] = None,
                           site_poly: Optional[Polygon] = None,
                           edge_dist: float = 0.4,
                           log_callback: Optional[Callable] = None) -> PileCapResult:
    """
    Create a pile cap polygon that encloses all structural elements in the group.

    Args:
        group_elements: Dictionary containing 'columns' and 'walls' lists
        excel_inputs: Optional excel inputs containing Column and Point dataframes
        site_poly: Optional site boundary polygon for clipping
        edge_dist: Minimum distance from structural elements to pile cap edge
        log_callback: Optional callback for logging geometry operations

    Returns:
        PileCapResult dictionary with polygon, validity, warnings, and errors

    Raises:
        InputDataError: If input data is invalid
        PileCapGeometryError: If pile cap creation fails
    """
    log_function_entry(log_callback, "create_pile_cap_polygon",
                      num_columns=len(group_elements.get('columns', [])) if group_elements else 0,
                      num_walls=len(group_elements.get('walls', [])) if group_elements else 0,
                      edge_dist=edge_dist, has_excel_inputs=excel_inputs is not None,
                      has_site_poly=site_poly is not None)

    with create_timed_logger(log_callback, "pile_cap_polygon_creation"):
        enhanced_log(log_callback, "Creating pile cap polygon from structural elements", 'INFO')

        # Input validation
        enhanced_log(log_callback, "Validating input parameters", 'DEBUG')

        if not group_elements or not any(group_elements.values()):
            error_msg = "No structural elements provided for pile cap creation"
            log_validation_result(log_callback, "Group Elements Present", False, error_msg)
            log_error_with_context(log_callback, InputDataError(error_msg), "create_pile_cap_polygon input validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            num_columns = len(group_elements.get('columns', []))
            num_walls = len(group_elements.get('walls', []))
            log_validation_result(log_callback, "Group Elements Present", True,
                                f"{num_columns} columns, {num_walls} walls")
            enhanced_log(log_callback, f"Group elements: {num_columns} columns, {num_walls} walls", 'DEBUG')

        if site_poly is not None and not isinstance(site_poly, Polygon):
            error_msg = "site_poly must be a Shapely Polygon or None"
            log_validation_result(log_callback, "Site Polygon Type", False, error_msg)
            log_error_with_context(log_callback, InputDataError(error_msg), "create_pile_cap_polygon site polygon validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            log_validation_result(log_callback, "Site Polygon Type", True,
                                f"Site polygon: {'provided' if site_poly is not None else 'not provided'}")

        if edge_dist <= 0:
            error_msg = "edge_dist must be positive"
            log_validation_result(log_callback, "Edge Distance Positive", False, error_msg)
            log_constraint_check(log_callback, "Edge Distance", edge_dist, 0, False)
            log_error_with_context(log_callback, InputDataError(error_msg), "create_pile_cap_polygon edge distance validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            log_validation_result(log_callback, "Edge Distance Positive", True, f"Edge distance: {edge_dist} m")
            log_constraint_check(log_callback, "Edge Distance", edge_dist, 0, True)
            log_calculation_result(log_callback, "Edge_distance", edge_dist, "m")

        warnings_list: List[str] = []
        errors_list: List[str] = []

        try:
            # Collect buffered polygons for all columns and walls
            log_algorithm_step(log_callback, "Pile Cap Creation", "Step 1: Collect buffered structural areas")
            enhanced_log(log_callback, "Collecting buffered structural areas", 'INFO')

            cap_poly = _collect_buffered_structural_areas(group_elements, excel_inputs, edge_dist,
                                                        warnings_list, log_callback)

            if not cap_poly or cap_poly.is_empty:
                error_msg = "No valid column or wall areas found for pile cap creation"
                log_validation_result(log_callback, "Structural Areas Collection", False, error_msg)
                log_error_with_context(log_callback, PileCapGeometryError(error_msg), "structural areas collection")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileCapGeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Structural Areas Collection", True,
                                    f"Collected polygon with area {cap_poly.area:.3f} m²")
                log_calculation_result(log_callback, "Initial_cap_area", cap_poly.area, "m²")
                enhanced_log(log_callback, f"Successfully collected structural areas: {cap_poly.area:.3f} m²", 'INFO')

            # Clip to site boundary if provided
            if site_poly is not None and not site_poly.is_empty:
                log_algorithm_step(log_callback, "Pile Cap Creation", "Step 2: Clip to site boundary")
                enhanced_log(log_callback, "Clipping pile cap to site boundary", 'INFO')

                original_area = cap_poly.area
                cap_poly = clip_to_site_boundary(cap_poly, site_poly, warnings_list, log_callback)
                clipped_area = cap_poly.area

                area_reduction = (original_area - clipped_area) / original_area if original_area > 0 else 0
                log_calculation_result(log_callback, "Area_reduction_ratio", area_reduction, "ratio")
                log_calculation_result(log_callback, "Clipped_cap_area", clipped_area, "m²")

                if area_reduction > 0.1:  # More than 10% reduction
                    enhanced_log(log_callback, f"Significant area reduction due to site clipping: {area_reduction:.1%}", 'WARNING')
                else:
                    enhanced_log(log_callback, f"Site boundary clipping completed with minimal area reduction: {area_reduction:.1%}", 'DEBUG')
            else:
                enhanced_log(log_callback, "No site boundary provided, skipping clipping step", 'DEBUG')

            # Validate the final pile cap
            log_algorithm_step(log_callback, "Pile Cap Creation", "Step 3: Validate pile cap polygon")
            enhanced_log(log_callback, "Validating final pile cap polygon", 'INFO')

            is_valid = validate_pile_cap_polygon(cap_poly, [], edge_dist, True,
                                               group_elements, excel_inputs, errors_list, log_callback)

            if not is_valid:
                error_msg = f"Generated pile cap polygon is invalid: {'; '.join(errors_list)}"
                log_validation_result(log_callback, "Final Pile Cap Validation", False, error_msg)
                log_error_with_context(log_callback, PileCapGeometryError(error_msg), "pile cap polygon validation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileCapGeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Final Pile Cap Validation", True,
                                    "Pile cap polygon passed all validation checks")
                enhanced_log(log_callback, "Pile cap polygon validation passed", 'INFO')

            # Compute local coordinate system for the pile cap
            log_algorithm_step(log_callback, "Pile Cap Creation", "Step 4: Compute local coordinate system")
            enhanced_log(log_callback, "Computing local coordinate system for pile cap", 'DEBUG')

            local_system = None
            if cap_poly and not cap_poly.is_empty and cap_poly.is_valid:
                try:
                    local_system_result = find_minimum_area_bounding_rectangle(cap_poly)
                    local_system = local_system_result.local_system

                    if local_system:
                        log_validation_result(log_callback, "Local Coordinate System", True,
                                            "Local coordinate system computed successfully")
                        enhanced_log(log_callback, "Local coordinate system computed successfully", 'DEBUG')
                    else:
                        log_validation_result(log_callback, "Local Coordinate System", False,
                                            "Failed to compute local coordinate system")
                        enhanced_log(log_callback, "Failed to compute local coordinate system", 'WARNING')

                except Exception as e:
                    error_msg = f"Error computing local coordinate system: {e}"
                    log_error_with_context(log_callback, e, "local coordinate system computation")
                    enhanced_log(log_callback, error_msg, 'WARNING')

            # Log final results
            log_performance_metric(log_callback, "Final_pile_cap_area", cap_poly.area, "m²")
            log_performance_metric(log_callback, "Pile_cap_warnings", len(warnings_list), "warnings")
            log_performance_metric(log_callback, "Pile_cap_errors", len(errors_list), "errors")

            result = {
                'polygon': cap_poly,
                'local_system': local_system,
                'is_valid': is_valid,
                'warnings': warnings_list,
                'errors': errors_list
            }

            enhanced_log(log_callback, f"Pile cap polygon creation completed successfully: "
                       f"{cap_poly.area:.3f} m² area, {len(warnings_list)} warnings", 'INFO')
            log_algorithm_step(log_callback, "Pile Cap Creation", "Completed successfully")

            log_function_exit(log_callback, "create_pile_cap_polygon", f"Success: {cap_poly.area:.3f} m²")
            return result

        except Exception as e:
            error_msg = f"Pile cap polygon creation failed: {e}"
            log_error_with_context(log_callback, e, "create_pile_cap_polygon")
            enhanced_log(log_callback, error_msg, 'ERROR')
            log_algorithm_step(log_callback, "Pile Cap Creation", f"Failed: {e}")
            log_function_exit(log_callback, "create_pile_cap_polygon", "Failed")
            raise


def create_pile_cap_for_piles_and_structures(group_elements: GroupElements,
                                            pile_locations: List[Tuple[float, float]],
                                            excel_inputs: Optional[Any] = None,
                                            site_poly: Optional[Polygon] = None,
                                            edge_dist: float = 0.4,
                                            pile_radius: float = 0.3,
                                            log_callback: Optional[Callable] = None) -> PileCapResult:
    """
    Create a pile cap polygon that is the convex hull of all structural elements and pile locations.

    Args:
        group_elements: Dictionary containing 'columns' and 'walls' lists
        pile_locations: List of (x, y) coordinates for pile positions
        excel_inputs: Optional excel inputs containing Column and Point dataframes
        site_poly: Optional site boundary polygon for clipping
        edge_dist: Minimum distance from structural elements to pile cap edge
        pile_radius: Pile radius (kept for compatibility)
        log_callback: Optional callback for logging geometry operations

    Returns:
        PileCapResult dictionary with polygon, validity, warnings, and errors

    Raises:
        InputDataError: If input data is invalid
        PileCapGeometryError: If pile cap creation fails
    """
    log_function_entry(log_callback, "create_pile_cap_for_piles_and_structures",
                      num_pile_locations=len(pile_locations) if pile_locations else 0,
                      num_columns=len(group_elements.get('columns', [])) if group_elements else 0,
                      num_walls=len(group_elements.get('walls', [])) if group_elements else 0,
                      edge_dist=edge_dist, pile_radius=pile_radius,
                      has_excel_inputs=excel_inputs is not None,
                      has_site_poly=site_poly is not None)

    with create_timed_logger(log_callback, "pile_cap_for_piles_and_structures"):
        enhanced_log(log_callback, "Creating pile cap polygon from piles and structural elements", 'INFO')

        # Enhanced validation with better error handling
        enhanced_log(log_callback, "Validating input parameters", 'DEBUG')

        if not group_elements:
            group_elements = {}
            enhanced_log(log_callback, "No group elements provided, using empty dictionary", 'DEBUG')

        # Safe check for group_elements values
        has_elements = False
        if isinstance(group_elements, dict):
            for key, value in group_elements.items():
                if value and len(value) > 0:
                    has_elements = True
                    break

        num_columns = len(group_elements.get('columns', []))
        num_walls = len(group_elements.get('walls', []))
        num_piles = len(pile_locations) if pile_locations else 0

        enhanced_log(log_callback, f"Input summary: {num_columns} columns, {num_walls} walls, {num_piles} pile locations", 'DEBUG')

        if not has_elements and not pile_locations:
            error_msg = "No structural elements or pile locations provided"
            log_validation_result(log_callback, "Elements or Piles Present", False, error_msg)
            log_error_with_context(log_callback, InputDataError(error_msg), "create_pile_cap_for_piles_and_structures input validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            log_validation_result(log_callback, "Elements or Piles Present", True,
                                f"{num_columns} columns, {num_walls} walls, {num_piles} piles")

        if site_poly is not None and not isinstance(site_poly, Polygon):
            error_msg = "site_poly must be a Shapely Polygon or None"
            log_validation_result(log_callback, "Site Polygon Type", False, error_msg)
            log_error_with_context(log_callback, InputDataError(error_msg), "site polygon validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            log_validation_result(log_callback, "Site Polygon Type", True,
                                f"Site polygon: {'provided' if site_poly is not None else 'not provided'}")

        if edge_dist <= 0:
            error_msg = "edge_dist must be positive"
            log_validation_result(log_callback, "Edge Distance Positive", False, error_msg)
            log_constraint_check(log_callback, "Edge Distance", edge_dist, 0, False)
            log_error_with_context(log_callback, InputDataError(error_msg), "edge distance validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise InputDataError(error_msg)
        else:
            log_validation_result(log_callback, "Edge Distance Positive", True, f"Edge distance: {edge_dist} m")
            log_constraint_check(log_callback, "Edge Distance", edge_dist, 0, True)
            log_calculation_result(log_callback, "Edge_distance", edge_dist, "m")
            log_calculation_result(log_callback, "Pile_radius", pile_radius, "m")

        warnings_list: List[str] = []
        errors_list: List[str] = []

        try:
            # Collect all coordinate points from structural elements and piles
            log_algorithm_step(log_callback, "Pile Cap with Piles", "Step 1: Collect coordinate points")
            enhanced_log(log_callback, "Collecting coordinate points from structural elements and piles", 'INFO')

            all_points = []

            # Extract structural points with enhanced error handling
            if has_elements:
                enhanced_log(log_callback, "Extracting structural points from group elements", 'DEBUG')
                try:
                    structural_points = extract_all_structural_points(group_elements, excel_inputs, warnings_list)
                    if structural_points:
                        all_points.extend(structural_points)
                        log_performance_metric(log_callback, "Structural_points_extracted", len(structural_points), "points")
                        enhanced_log(log_callback, f"Extracted {len(structural_points)} structural points", 'DEBUG')
                    else:
                        enhanced_log(log_callback, "No structural points extracted", 'DEBUG')
                except Exception as e:
                    error_msg = f"Error extracting structural points: {str(e)}"
                    warnings_list.append(error_msg)
                    log_error_with_context(log_callback, e, "structural points extraction")
                    enhanced_log(log_callback, error_msg, 'WARNING')

            # Add pile locations with enhanced validation
            if pile_locations:
                enhanced_log(log_callback, f"Validating and adding {len(pile_locations)} pile locations", 'DEBUG')

                valid_pile_count = 0
                # Validate pile locations
                for i, loc in enumerate(pile_locations):
                    enhanced_log(log_callback, f"Validating pile location {i+1}: {loc}", 'DEBUG')

                    if not isinstance(loc, (list, tuple)) or len(loc) < 2:
                        error_msg = f"Invalid pile location format at index {i}"
                        log_validation_result(log_callback, f"Pile Location {i+1} Format", False, error_msg)
                        log_error_with_context(log_callback, InputDataError(error_msg), f"pile location {i} validation")
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise InputDataError(error_msg)

                    try:
                        x_coord = float(loc[0])
                        y_coord = float(loc[1])

                        # Check for valid coordinates (not NaN or infinite)
                        if not (isinstance(x_coord, (int, float)) and isinstance(y_coord, (int, float)) and
                                abs(x_coord) < float('inf') and abs(y_coord) < float('inf')):
                            raise ValueError(f"Invalid coordinate values: ({x_coord}, {y_coord})")

                        all_points.append((x_coord, y_coord))
                        valid_pile_count += 1
                        log_validation_result(log_callback, f"Pile Location {i+1} Coordinates", True,
                                            f"Valid coordinates: ({x_coord:.3f}, {y_coord:.3f})")

                    except (ValueError, TypeError) as e:
                        error_msg = f"Invalid pile coordinates at index {i}: {str(e)}"
                        log_validation_result(log_callback, f"Pile Location {i+1} Coordinates", False, error_msg)
                        log_error_with_context(log_callback, InputDataError(error_msg), f"pile coordinates {i} validation")
                        enhanced_log(log_callback, error_msg, 'ERROR')
                        raise InputDataError(error_msg)

                log_performance_metric(log_callback, "Valid_pile_locations", valid_pile_count, "piles")
                enhanced_log(log_callback, f"Successfully validated {valid_pile_count} pile locations", 'DEBUG')

            total_points = len(all_points)
            log_performance_metric(log_callback, "Total_coordinate_points", total_points, "points")
            enhanced_log(log_callback, f"Total coordinate points collected: {total_points}", 'INFO')

            if total_points < 3:
                error_msg = f"Insufficient points to create pile cap polygon (minimum 3 required, got {total_points})"
                log_validation_result(log_callback, "Sufficient Points for Polygon", False, error_msg)
                log_constraint_check(log_callback, "Minimum Points", total_points, 3, False)
                log_error_with_context(log_callback, PileCapGeometryError(error_msg), "point count validation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileCapGeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Sufficient Points for Polygon", True,
                                    f"{total_points} points available for polygon creation")
                log_constraint_check(log_callback, "Minimum Points", total_points, 3, True)

            # Create convex hull from all points
            log_algorithm_step(log_callback, "Pile Cap with Piles", "Step 2: Create convex hull")
            enhanced_log(log_callback, f"Creating convex hull from {total_points} points", 'INFO')

            multipoint = MultiPoint(all_points)
            convex_hull = multipoint.convex_hull

            log_calculation_result(log_callback, "Convex_hull_type", type(convex_hull).__name__, "")

            # Ensure we have a valid polygon
            if not isinstance(convex_hull, Polygon):
                enhanced_log(log_callback, f"Convex hull is not a polygon ({type(convex_hull).__name__}), attempting to convert", 'WARNING')

                if hasattr(convex_hull, 'coords'):
                    buffer_distance = max(0.1, edge_dist)
                    convex_hull = convex_hull.buffer(buffer_distance)
                    log_calculation_result(log_callback, "Convex_hull_buffer_distance", buffer_distance, "m")
                    enhanced_log(log_callback, f"Buffered convex hull by {buffer_distance} m", 'DEBUG')
                else:
                    error_msg = "Convex hull did not produce a polygon"
                    log_validation_result(log_callback, "Convex Hull Polygon", False, error_msg)
                    log_error_with_context(log_callback, GeometryError(error_msg), "convex hull polygon creation")
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise GeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Convex Hull Polygon", True, "Convex hull is a valid polygon")

            # Validate the convex hull
            if not convex_hull.is_valid or convex_hull.is_empty:
                error_msg = "Invalid convex hull geometry"
                log_validation_result(log_callback, "Convex Hull Validity", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), "convex hull validation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise GeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Convex Hull Validity", True,
                                    f"Valid convex hull with area {convex_hull.area:.3f} m²")
                log_calculation_result(log_callback, "Convex_hull_area", convex_hull.area, "m²")

            # Buffer the convex hull by edge_dist to create the final pile cap
            log_algorithm_step(log_callback, "Pile Cap with Piles", "Step 3: Buffer convex hull")
            enhanced_log(log_callback, f"Buffering convex hull by {edge_dist} m to create pile cap", 'INFO')

            cap_poly = convex_hull.buffer(edge_dist, join_style=2)

            if not isinstance(cap_poly, Polygon) or cap_poly.is_empty:
                error_msg = "Failed to create valid pile cap polygon from convex hull"
                log_validation_result(log_callback, "Pile Cap Buffer Creation", False, error_msg)
                log_error_with_context(log_callback, PileCapGeometryError(error_msg), "pile cap buffer creation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileCapGeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Pile Cap Buffer Creation", True,
                                    f"Pile cap created with area {cap_poly.area:.3f} m²")
                log_calculation_result(log_callback, "Buffered_pile_cap_area", cap_poly.area, "m²")
                enhanced_log(log_callback, f"Successfully created pile cap: {cap_poly.area:.3f} m²", 'INFO')

        except Exception as e:
            error_msg = f"Error creating pile cap geometry: {str(e)}"
            log_error_with_context(log_callback, e, "pile cap geometry creation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)

        # Clip to site boundary if provided
        if site_poly is not None and not site_poly.is_empty:
            log_algorithm_step(log_callback, "Pile Cap with Piles", "Step 4: Clip to site boundary")
            enhanced_log(log_callback, "Clipping pile cap to site boundary", 'INFO')

            try:
                original_area = cap_poly.area
                cap_poly = clip_to_site_boundary(cap_poly, site_poly, warnings_list, log_callback)
                clipped_area = cap_poly.area

                area_reduction = (original_area - clipped_area) / original_area if original_area > 0 else 0
                log_calculation_result(log_callback, "Site_clipping_area_reduction", area_reduction, "ratio")

                if area_reduction > 0.1:  # More than 10% reduction
                    enhanced_log(log_callback, f"Significant area reduction due to site clipping: {area_reduction:.1%}", 'WARNING')
                else:
                    enhanced_log(log_callback, f"Site boundary clipping completed: {area_reduction:.1%} area reduction", 'DEBUG')

            except Exception as e:
                error_msg = f"Error clipping to site boundary: {str(e)}"
                warnings_list.append(error_msg)
                log_error_with_context(log_callback, e, "site boundary clipping")
                enhanced_log(log_callback, error_msg, 'WARNING')
        else:
            enhanced_log(log_callback, "No site boundary provided, skipping clipping step", 'DEBUG')

        # Basic validation
        log_algorithm_step(log_callback, "Pile Cap with Piles", "Step 5: Final validation")
        enhanced_log(log_callback, "Performing final pile cap validation", 'DEBUG')

        is_valid = cap_poly.is_valid and not cap_poly.is_empty
        if not is_valid:
            error_msg = "Generated pile cap polygon is invalid or empty"
            log_validation_result(log_callback, "Final Pile Cap Validation", False, error_msg)
            log_error_with_context(log_callback, PileCapGeometryError(error_msg), "final pile cap validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)
        else:
            log_validation_result(log_callback, "Final Pile Cap Validation", True,
                                f"Valid pile cap with area {cap_poly.area:.3f} m²")

        # Log final results
        log_performance_metric(log_callback, "Final_pile_cap_area", cap_poly.area, "m²")
        log_performance_metric(log_callback, "Pile_cap_warnings", len(warnings_list), "warnings")
        log_performance_metric(log_callback, "Pile_cap_errors", len(errors_list), "errors")

        result = {
            'polygon': cap_poly,
            'is_valid': is_valid,
            'warnings': warnings_list,
            'errors': errors_list
        }

        enhanced_log(log_callback, f"Pile cap for piles and structures completed successfully: "
                   f"{cap_poly.area:.3f} m² area, {len(warnings_list)} warnings", 'INFO')
        log_algorithm_step(log_callback, "Pile Cap with Piles", "Completed successfully")

        log_function_exit(log_callback, "create_pile_cap_for_piles_and_structures", f"Success: {cap_poly.area:.3f} m²")
        return result


def _collect_buffered_structural_areas(group_elements: GroupElements, excel_inputs: Optional[Any],
                                     edge_dist: float, warnings_list: List[str],
                                     log_callback: Optional[Callable] = None) -> Polygon:
    """
    Collect buffered polygons for all columns and walls and union them.

    Raises:
        PileCapGeometryError: If no valid structural areas can be created
    """
    log_function_entry(log_callback, "_collect_buffered_structural_areas",
                      num_columns=len(group_elements.get('columns', [])) if group_elements else 0,
                      num_walls=len(group_elements.get('walls', [])) if group_elements else 0,
                      edge_dist=edge_dist, has_excel_inputs=excel_inputs is not None)

    with create_timed_logger(log_callback, "collect_buffered_structural_areas"):
        enhanced_log(log_callback, "Collecting buffered structural areas", 'DEBUG')

        if not group_elements or not any(group_elements.values()):
            error_msg = "No structural elements provided"
            log_validation_result(log_callback, "Structural Elements Present", False, error_msg)
            log_error_with_context(log_callback, PileCapGeometryError(error_msg), "_collect_buffered_structural_areas")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)
        else:
            num_columns = len(group_elements.get('columns', []))
            num_walls = len(group_elements.get('walls', []))
            log_validation_result(log_callback, "Structural Elements Present", True,
                                f"{num_columns} columns, {num_walls} walls")

        buffered_polys = []
        seen_elements = set()

        # Process columns
        if excel_inputs is not None:
            column_names = [col[0] for col in group_elements.get('columns', []) if col and len(col) >= 1]
            if column_names:
                enhanced_log(log_callback, f"Processing {len(column_names)} columns", 'DEBUG')
                _process_enhanced_columns(column_names, excel_inputs, edge_dist, buffered_polys,
                                        seen_elements, warnings_list, log_callback)
                log_performance_metric(log_callback, "Columns_processed", len(column_names), "columns")
            else:
                enhanced_log(log_callback, "No column names to process", 'DEBUG')
        else:
            enhanced_log(log_callback, "No Excel inputs provided for column processing", 'DEBUG')

        # Process walls
        if excel_inputs is not None:
            wall_names = [wall[0] for wall in group_elements.get('walls', []) if wall and len(wall) >= 1]
            if wall_names:
                enhanced_log(log_callback, f"Processing {len(wall_names)} walls", 'DEBUG')
                _process_enhanced_walls(wall_names, excel_inputs, edge_dist, buffered_polys,
                                      seen_elements, warnings_list, log_callback)
                log_performance_metric(log_callback, "Walls_processed", len(wall_names), "walls")
            else:
                enhanced_log(log_callback, "No wall names to process", 'DEBUG')
        else:
            enhanced_log(log_callback, "No Excel inputs provided for wall processing", 'DEBUG')

        total_buffered_polys = len(buffered_polys)
        log_performance_metric(log_callback, "Total_buffered_polygons", total_buffered_polys, "polygons")

        if not buffered_polys:
            error_msg = "No valid structural polygons could be created"
            log_validation_result(log_callback, "Buffered Polygons Created", False, error_msg)
            log_error_with_context(log_callback, PileCapGeometryError(error_msg), "buffered polygons creation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)
        else:
            log_validation_result(log_callback, "Buffered Polygons Created", True,
                                f"{total_buffered_polys} buffered polygons created")
            enhanced_log(log_callback, f"Successfully created {total_buffered_polys} buffered polygons", 'DEBUG')

        # Union all buffered polygons
        enhanced_log(log_callback, f"Performing union operation on {total_buffered_polys} polygons", 'DEBUG')

        try:
            unioned = unary_union(buffered_polys)
            log_calculation_result(log_callback, "Union_operation", "completed", "")
            log_calculation_result(log_callback, "Union_result_type", type(unioned).__name__, "")

        except Exception as e:
            error_msg = f"Error during union operation: {e}"
            log_error_with_context(log_callback, e, "polygon union operation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)

        # Handle MultiPolygon case - create a single polygon using convex hull
        if hasattr(unioned, 'geoms'):
            enhanced_log(log_callback, f"Union result is MultiPolygon with {len(unioned.geoms)} geometries", 'DEBUG')

            polys = [p for p in unioned.geoms if isinstance(p, Polygon) and not p.is_empty]
            valid_polys_count = len(polys)
            log_performance_metric(log_callback, "Valid_polygons_in_union", valid_polys_count, "polygons")

            if not polys:
                error_msg = "No valid polygons in union result"
                log_validation_result(log_callback, "Valid Polygons in Union", False, error_msg)
                log_error_with_context(log_callback, PileCapGeometryError(error_msg), "union result validation")
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise PileCapGeometryError(error_msg)
            else:
                log_validation_result(log_callback, "Valid Polygons in Union", True,
                                    f"{valid_polys_count} valid polygons found")

            if len(polys) == 1:
                enhanced_log(log_callback, "Single polygon in union result, returning directly", 'DEBUG')
                result_poly = polys[0]
                log_calculation_result(log_callback, "Final_polygon_area", result_poly.area, "m²")
                log_function_exit(log_callback, "_collect_buffered_structural_areas", f"Single polygon: {result_poly.area:.3f} m²")
                return result_poly

            # Multiple polygons - create convex hull
            enhanced_log(log_callback, f"Multiple polygons ({len(polys)}), creating convex hull", 'DEBUG')

            all_coords = []
            for i, poly in enumerate(polys):
                coords = list(poly.exterior.coords[:-1])
                all_coords.extend(coords)
                enhanced_log(log_callback, f"Polygon {i+1}: {len(coords)} coordinates, area {poly.area:.3f} m²", 'DEBUG')

            total_coords = len(all_coords)
            log_performance_metric(log_callback, "Total_coordinates_for_hull", total_coords, "coordinates")

            if total_coords >= 3:
                enhanced_log(log_callback, f"Creating convex hull from {total_coords} coordinates", 'DEBUG')
                multipoint = MultiPoint(all_coords)
                convex_hull = multipoint.convex_hull

                if isinstance(convex_hull, Polygon):
                    log_calculation_result(log_callback, "Convex_hull_area", convex_hull.area, "m²")
                    enhanced_log(log_callback, f"Convex hull created: {convex_hull.area:.3f} m²", 'DEBUG')
                    log_function_exit(log_callback, "_collect_buffered_structural_areas", f"Convex hull: {convex_hull.area:.3f} m²")
                    return convex_hull
                else:
                    enhanced_log(log_callback, "Convex hull is not a polygon, using largest polygon", 'WARNING')
                    result_poly = max(polys, key=lambda p: p.area)
                    log_calculation_result(log_callback, "Largest_polygon_area", result_poly.area, "m²")
                    log_function_exit(log_callback, "_collect_buffered_structural_areas", f"Largest polygon: {result_poly.area:.3f} m²")
                    return result_poly
            else:
                enhanced_log(log_callback, f"Insufficient coordinates ({total_coords}) for convex hull, using largest polygon", 'WARNING')
                result_poly = max(polys, key=lambda p: p.area)
                log_calculation_result(log_callback, "Largest_polygon_area", result_poly.area, "m²")
                log_function_exit(log_callback, "_collect_buffered_structural_areas", f"Largest polygon: {result_poly.area:.3f} m²")
                return result_poly

        if isinstance(unioned, Polygon):
            enhanced_log(log_callback, f"Union result is single polygon: {unioned.area:.3f} m²", 'DEBUG')
            log_calculation_result(log_callback, "Union_polygon_area", unioned.area, "m²")
            log_function_exit(log_callback, "_collect_buffered_structural_areas", f"Union polygon: {unioned.area:.3f} m²")
            return unioned
        else:
            error_msg = "Union operation did not produce a valid polygon"
            log_validation_result(log_callback, "Union Result Type", False, f"Got {type(unioned).__name__}")
            log_error_with_context(log_callback, PileCapGeometryError(error_msg), "union result type validation")
            enhanced_log(log_callback, error_msg, 'ERROR')
            raise PileCapGeometryError(error_msg)


def _process_enhanced_columns(column_names: List[str], excel_inputs: Any, edge_dist: float,
                            buffered_polys: List[Polygon], seen_elements: set, warnings_list: List[str],
                            log_callback: Optional[Callable] = None):
    """Process columns using enhanced geometry utilities."""
    log_function_entry(log_callback, "_process_enhanced_columns",
                      num_columns=len(column_names), edge_dist=edge_dist)

    with create_timed_logger(log_callback, "process_enhanced_columns"):
        enhanced_log(log_callback, f"Processing {len(column_names)} columns with enhanced geometry utilities", 'DEBUG')

        if not column_names:
            enhanced_log(log_callback, "No column names provided, returning early", 'DEBUG')
            log_function_exit(log_callback, "_process_enhanced_columns", "No columns to process")
            return

        try:
            enhanced_log(log_callback, f"Creating column polygons from Excel for columns: {column_names}", 'DEBUG')
            column_polygons = create_column_polygons_from_excel(excel_inputs, column_names)

            if not column_polygons:
                warning_msg = "No valid column polygons could be created"
                warnings_list.append(warning_msg)
                log_validation_result(log_callback, "Column Polygons Creation", False, warning_msg)
                enhanced_log(log_callback, warning_msg, 'WARNING')
                log_function_exit(log_callback, "_process_enhanced_columns", "No valid polygons created")
                return
            else:
                log_validation_result(log_callback, "Column Polygons Creation", True,
                                    f"{len(column_polygons)} column polygons created")
                enhanced_log(log_callback, f"Successfully created {len(column_polygons)} column polygons", 'DEBUG')

            processed_columns = 0
            buffered_columns = 0

            for column_name, column_data in column_polygons.items():
                enhanced_log(log_callback, f"Processing column: {column_name}", 'DEBUG')

                if column_name in seen_elements:
                    enhanced_log(log_callback, f"Column {column_name} already processed, skipping", 'DEBUG')
                    continue
                seen_elements.add(column_name)
                processed_columns += 1

                polygon = column_data['polygon']
                if not isinstance(polygon, Polygon) or polygon.is_empty:
                    warning_msg = f"Invalid polygon for column {column_name}"
                    warnings_list.append(warning_msg)
                    log_validation_result(log_callback, f"Column {column_name} Polygon", False, warning_msg)
                    enhanced_log(log_callback, warning_msg, 'WARNING')
                    continue
                else:
                    log_validation_result(log_callback, f"Column {column_name} Polygon", True,
                                        f"Valid polygon with area {polygon.area:.3f} m²")
                    log_calculation_result(log_callback, f"Column_{column_name}_area", polygon.area, "m²")

                # Buffer the polygon
                enhanced_log(log_callback, f"Buffering column {column_name} by {edge_dist} m", 'DEBUG')

                try:
                    buffered = polygon.buffer(edge_dist, join_style=2)

                    if buffered.is_valid and not buffered.is_empty:
                        buffered_polys.append(buffered)
                        buffered_columns += 1
                        log_validation_result(log_callback, f"Column {column_name} Buffer", True,
                                            f"Buffered area: {buffered.area:.3f} m²")
                        log_calculation_result(log_callback, f"Column_{column_name}_buffered_area", buffered.area, "m²")
                        enhanced_log(log_callback, f"Successfully buffered column {column_name}: {buffered.area:.3f} m²", 'DEBUG')
                    else:
                        warning_msg = f"Failed to buffer column {column_name}"
                        warnings_list.append(warning_msg)
                        log_validation_result(log_callback, f"Column {column_name} Buffer", False, warning_msg)
                        enhanced_log(log_callback, warning_msg, 'WARNING')

                except Exception as e:
                    warning_msg = f"Error buffering column {column_name}: {e}"
                    warnings_list.append(warning_msg)
                    log_error_with_context(log_callback, e, f"column {column_name} buffering")
                    enhanced_log(log_callback, warning_msg, 'WARNING')

            # Log processing summary
            log_performance_metric(log_callback, "Columns_processed", processed_columns, "columns")
            log_performance_metric(log_callback, "Columns_buffered", buffered_columns, "columns")

            enhanced_log(log_callback, f"Column processing completed: {processed_columns} processed, "
                       f"{buffered_columns} successfully buffered", 'DEBUG')

        except Exception as e:
            error_msg = f"Error processing enhanced columns: {e}"
            log_error_with_context(log_callback, e, "_process_enhanced_columns")
            enhanced_log(log_callback, error_msg, 'ERROR')
            warnings_list.append(error_msg)

    log_function_exit(log_callback, "_process_enhanced_columns", f"{buffered_columns} columns buffered")


def _process_enhanced_walls(wall_names: List[str], excel_inputs: Any, edge_dist: float,
                          buffered_polys: List[Polygon], seen_elements: set, warnings_list: List[str],
                          log_callback: Optional[Callable] = None):
    """Process walls using enhanced geometry utilities."""
    log_function_entry(log_callback, "_process_enhanced_walls",
                      num_walls=len(wall_names), edge_dist=edge_dist)

    with create_timed_logger(log_callback, "process_enhanced_walls"):
        enhanced_log(log_callback, f"Processing {len(wall_names)} walls with enhanced geometry utilities", 'DEBUG')

        if not wall_names:
            enhanced_log(log_callback, "No wall names provided, returning early", 'DEBUG')
            log_function_exit(log_callback, "_process_enhanced_walls", "No walls to process")
            return

        buffered_walls = 0
        processed_walls = 0

        try:
            # Try grouped continuous walls first
            enhanced_log(log_callback, f"Attempting to group continuous walls from {len(wall_names)} walls", 'DEBUG')
            grouped_walls = group_continuous_walls_from_excel(excel_inputs, wall_names)

            if grouped_walls:
                log_validation_result(log_callback, "Wall Grouping", True,
                                    f"{len(grouped_walls)} wall groups created")
                enhanced_log(log_callback, f"Successfully created {len(grouped_walls)} wall groups", 'DEBUG')

                for i, group_data in enumerate(grouped_walls):
                    enhanced_log(log_callback, f"Processing wall group {i+1}/{len(grouped_walls)}", 'DEBUG')

                    polygon = group_data['polygon']
                    wall_names_in_group = group_data['wall_names']

                    enhanced_log(log_callback, f"Wall group {i+1} contains walls: {wall_names_in_group}", 'DEBUG')

                    if not isinstance(polygon, Polygon) or polygon.is_empty:
                        warning_msg = f"Invalid polygon in wall group {i+1}"
                        warnings_list.append(warning_msg)
                        log_validation_result(log_callback, f"Wall Group {i+1} Polygon", False, warning_msg)
                        enhanced_log(log_callback, warning_msg, 'WARNING')
                        continue
                    else:
                        log_validation_result(log_callback, f"Wall Group {i+1} Polygon", True,
                                            f"Valid polygon with area {polygon.area:.3f} m²")
                        log_calculation_result(log_callback, f"Wall_group_{i+1}_area", polygon.area, "m²")

                    # Buffer the wall group
                    enhanced_log(log_callback, f"Buffering wall group {i+1} by {edge_dist} m", 'DEBUG')

                    try:
                        buffered = polygon.buffer(edge_dist, cap_style=2, join_style=2)

                        if buffered.is_valid and not buffered.is_empty:
                            buffered_polys.append(buffered)
                            seen_elements.update(wall_names_in_group)
                            buffered_walls += len(wall_names_in_group)
                            processed_walls += len(wall_names_in_group)

                            log_validation_result(log_callback, f"Wall Group {i+1} Buffer", True,
                                                f"Buffered area: {buffered.area:.3f} m²")
                            log_calculation_result(log_callback, f"Wall_group_{i+1}_buffered_area", buffered.area, "m²")
                            enhanced_log(log_callback, f"Successfully buffered wall group {i+1}: {buffered.area:.3f} m²", 'DEBUG')
                        else:
                            warning_msg = f"Failed to buffer wall group {i+1}"
                            warnings_list.append(warning_msg)
                            log_validation_result(log_callback, f"Wall Group {i+1} Buffer", False, warning_msg)
                            enhanced_log(log_callback, warning_msg, 'WARNING')

                    except Exception as e:
                        warning_msg = f"Error buffering wall group {i+1}: {e}"
                        warnings_list.append(warning_msg)
                        log_error_with_context(log_callback, e, f"wall group {i+1} buffering")
                        enhanced_log(log_callback, warning_msg, 'WARNING')
            else:
                log_validation_result(log_callback, "Wall Grouping", False, "No wall groups created")
                enhanced_log(log_callback, "No wall groups created, will process individual walls", 'DEBUG')

            # Handle individual walls
            enhanced_log(log_callback, "Processing individual walls", 'DEBUG')
            wall_polygons = create_wall_polygons_from_excel(excel_inputs, wall_names)

            if not wall_polygons:
                enhanced_log(log_callback, "No individual wall polygons created", 'DEBUG')
            else:
                log_validation_result(log_callback, "Individual Wall Polygons", True,
                                    f"{len(wall_polygons)} individual wall polygons created")
                enhanced_log(log_callback, f"Created {len(wall_polygons)} individual wall polygons", 'DEBUG')

            for wall_name, wall_data in wall_polygons.items():
                if wall_name in seen_elements:
                    enhanced_log(log_callback, f"Wall {wall_name} already processed in group, skipping", 'DEBUG')
                    continue

                enhanced_log(log_callback, f"Processing individual wall: {wall_name}", 'DEBUG')
                processed_walls += 1

                polygon = wall_data['polygon']
                if not isinstance(polygon, Polygon) or polygon.is_empty:
                    warning_msg = f"Invalid polygon for wall {wall_name}"
                    warnings_list.append(warning_msg)
                    log_validation_result(log_callback, f"Wall {wall_name} Polygon", False, warning_msg)
                    enhanced_log(log_callback, warning_msg, 'WARNING')
                    continue
                else:
                    log_validation_result(log_callback, f"Wall {wall_name} Polygon", True,
                                        f"Valid polygon with area {polygon.area:.3f} m²")
                    log_calculation_result(log_callback, f"Wall_{wall_name}_area", polygon.area, "m²")

                # Buffer the individual wall
                enhanced_log(log_callback, f"Buffering wall {wall_name} by {edge_dist} m", 'DEBUG')

                try:
                    buffered = polygon.buffer(edge_dist, cap_style=2, join_style=2)

                    if buffered.is_valid and not buffered.is_empty:
                        buffered_polys.append(buffered)
                        seen_elements.add(wall_name)
                        buffered_walls += 1

                        log_validation_result(log_callback, f"Wall {wall_name} Buffer", True,
                                            f"Buffered area: {buffered.area:.3f} m²")
                        log_calculation_result(log_callback, f"Wall_{wall_name}_buffered_area", buffered.area, "m²")
                        enhanced_log(log_callback, f"Successfully buffered wall {wall_name}: {buffered.area:.3f} m²", 'DEBUG')
                    else:
                        warning_msg = f"Failed to buffer wall {wall_name}"
                        warnings_list.append(warning_msg)
                        log_validation_result(log_callback, f"Wall {wall_name} Buffer", False, warning_msg)
                        enhanced_log(log_callback, warning_msg, 'WARNING')

                except Exception as e:
                    warning_msg = f"Error buffering wall {wall_name}: {e}"
                    warnings_list.append(warning_msg)
                    log_error_with_context(log_callback, e, f"wall {wall_name} buffering")
                    enhanced_log(log_callback, warning_msg, 'WARNING')

            # Log processing summary
            log_performance_metric(log_callback, "Walls_processed", processed_walls, "walls")
            log_performance_metric(log_callback, "Walls_buffered", buffered_walls, "walls")

            enhanced_log(log_callback, f"Wall processing completed: {processed_walls} processed, "
                       f"{buffered_walls} successfully buffered", 'DEBUG')

        except Exception as e:
            error_msg = f"Error processing enhanced walls: {e}"
            log_error_with_context(log_callback, e, "_process_enhanced_walls")
            enhanced_log(log_callback, error_msg, 'ERROR')
            warnings_list.append(error_msg)

    log_function_exit(log_callback, "_process_enhanced_walls", f"{buffered_walls} walls buffered")







