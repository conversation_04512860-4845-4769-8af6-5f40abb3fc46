# Configuration and Core System Files

## Application Configuration (`config/`)
- **`app_config.py`**: Central configuration hub
  - Application metadata: Title, geometry, version (V5.3)
  - Security settings: Login attempts (3), timeout (15 min), session duration (5 hours)
  - Email configuration: Sender credentials, domain (@asiainfrasolutions.com)
  - SSL configuration flags for development/production environments
  - Google Drive URLs for user authorization lists (Base/Ultimate users)
  - File paths: Data directory, security logs, application icon
  - Environment variable support for testing bypasses

- **`ssl_config.py`**: SSL/TLS certificate handling
  - Custom SSL context creation with certificate fallbacks
  - Corporate environment support with proxy detection
  - Certificate validation with custom CA bundle support
  - Requests library SSL configuration with warnings management

## Email Notification System (`email_notifications/`)
- **`notification.py`**: Complete email communication system
  - **Password Generation**: GUID-based secure password creation (30 chars default)
  - **Email Delivery**: Multi-context SMTP with SSL fallback mechanisms
  - **Authentication Emails**: OTP delivery for user login
  - **Usage Logging**: Activity tracking with timestamp and version info
  - **SSL Fallback**: Graceful degradation for corporate firewalls
  - **Error Handling**: Comprehensive failure recovery and logging

## User Interface Components (`ui/`)
- **`components.py`**: Reusable UI building blocks
  - BaseFrame: Common frame functionality with window clearing
  - HeaderFrame: Consistent header with user info and logout
  - TimerLabel: Session timer display with activation controls
  - Menu creation: Standard application menu with About dialog
  - Window setup: Icon configuration and error handling
  - Version display: Software version and copyright information

- **`login_frame.py`**: Authentication interface
  - Username entry with domain hints
  - Password generation request functionality
  - Secure password entry (masked)
  - Lockout status display
  - Development login bypass (conditional)
  - Input validation and error feedback

## SAFE API Integration (`safe_api/main.py`)
- **Complete Automation Orchestration**:
  - `automate_build_fem()`: Full FEM building pipeline with soil springs
  - `automate_foundation_design()`: ULS/SLS calculations and design automation
  - `automate_complete_workflow()`: End-to-end automation (FEM → Analysis → Design)
  - `process_safe_analysis_results()`: SQLite database processing workflow
  - Command-line interface with argparse support
  - Integrated logging system with timestamped entries
  - Error handling and recovery mechanisms
  - SAFE connection management and model processing