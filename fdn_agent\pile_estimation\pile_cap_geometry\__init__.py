﻿"""
Pile Cap Geometry Package

Simplified modular package for pile cap geometry operations.
"""

# Configuration classes (moved to types module)
from ..data_types import (
    PileCapConfig,
    LocalCoordinateSystem
)

# Core pile cap creation functions
from .pile_cap_geometry import (
    create_pile_cap_polygon,
    create_pile_cap_for_piles_and_structures
)

# Coordinate system functions (moved to utils.coordinate_utils)
from ..utils.coordinate_utils import (
    find_minimum_area_bounding_rectangle,
    global_to_local_coordinates,
    local_to_global_coordinates,
    generate_local_grid_positions
)

# Validation functions (moved to utils.validation_utils)
from ..utils.validation_utils import (
    validate_pile_cap_polygon,
    clip_to_site_boundary
)

# Structural extraction functions (moved to utils.geometry_utils)
from ..utils.geometry_utils import (
    extract_all_structural_points,
    extract_wall_segments_from_excel,
    group_continuous_walls,
    create_continuous_path
)

__all__ = [
    # Configuration
    'PileCapConfig',
    'LocalCoordinateSystem',
    
    # Core functions
    'create_pile_cap_polygon',
    'create_pile_cap_for_piles_and_structures',
    
    # Coordinate systems
    'find_minimum_area_bounding_rectangle',
    'global_to_local_coordinates',
    'local_to_global_coordinates', 
    'generate_local_grid_positions',
    
    # Validation
    'validate_pile_cap_polygon',
    'clip_to_site_boundary',
    
    # Structural extraction
    'extract_all_structural_points',
    'extract_wall_segments_from_excel',
    'group_continuous_walls',
    'create_continuous_path'
]

