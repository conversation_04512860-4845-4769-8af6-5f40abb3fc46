"""
SAFE API Environment Setup Module

This module handles the initialization and setup of the SAFE API environment,
including .NET CLR configuration and SAFEv1 module imports.
"""

import os
import sys

# Add the parent directory (workspace root) to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from . import config


def setup_safe_api_environment():
    """
    Sets up the SAFE API environment by configuring .NET CLR and importing required modules.
    
    Returns:
        tuple: A tuple containing (clr_module, Marshal_obj, SAFEv1_module, Remote, 
               RemoteComputer, AttachToInstance, SpecifyPath, ProgramPath, helper)
    """
    _UseNETCore = False  # True #

    _Marshal_obj = None  # Initialize to None

    if _UseNETCore:
        from pythonnet import load
        load("coreclr")
        import clr as _clr_module
    else:
        import clr as _clr_module
        _clr_module.AddReference("System.Runtime.InteropServices")
        from System.Runtime.InteropServices import <PERSON> as _Marshal_obj_temp
        _Marshal_obj = _Marshal_obj_temp  # Assign if .NET Framework

    _clr_module.AddReference(config.SAFE_DLL_PATH)

    # Import SAFEv1 as a module
    import SAFEv1 as _SAFEv1_module

    # Configuration variables
    _Remote = config.REMOTE_CONNECTION
    _RemoteComputer = config.REMOTE_COMPUTER_HOSTNAME
    _AttachToInstance = config.ATTACH_TO_INSTANCE
    _SpecifyPath = config.SPECIFY_PROGRAM_PATH
    _ProgramPath = config.SAFE_PROGRAM_PATH

    # Helper object, accessed via the imported SAFEv1 module
    _helper = _SAFEv1_module.cHelper(_SAFEv1_module.Helper())

    return (_clr_module, _Marshal_obj, _SAFEv1_module, _Remote, _RemoteComputer, 
            _AttachToInstance, _SpecifyPath, _ProgramPath, _helper)


def get_safe_environment():
    """
    Get the configured SAFE API environment.
    
    Returns:
        tuple: Environment configuration tuple
    """
    return setup_safe_api_environment()
