"""
DXF plotting functions for pile estimation visualization.

This module serves as the main interface for all plotting functions,
importing and re-exporting functions from specialized modules for
backward compatibility.
"""

# Import all functions from specialized modules
from .base_plotters import plot_site_boundary_dxf, add_cross_marker
from .pile_plotters import (
    plot_group_dxf, plot_enhanced_pile_dxf, plot_load_centroid_dxf,
    plot_sub_cluster_centroids_dxf, plot_pile_centroid_dxf
)
from .structure_plotters import plot_columns_dxf, plot_walls_dxf
from .analysis_plotters import plot_preselection_analysis_dxf, plot_possible_pile_positions_dxf
from .dimension_plotters import (
    add_title_block_dxf, add_dimensions_dxf, add_pile_spacing_dimensions,
    add_pile_cap_dimensions
)

# Re-export all functions for backward compatibility
__all__ = [
    'plot_site_boundary_dxf',
    'plot_group_dxf',
    'plot_enhanced_pile_dxf',
    'plot_load_centroid_dxf',
    'plot_sub_cluster_centroids_dxf',
    'plot_pile_centroid_dxf',
    'plot_columns_dxf',
    'plot_walls_dxf',
    'plot_preselection_analysis_dxf',
    'plot_possible_pile_positions_dxf',
    'add_cross_marker',
    'add_title_block_dxf',
    'add_dimensions_dxf',
    'add_pile_spacing_dimensions',
    'add_pile_cap_dimensions'
]


# All functions are now imported from specialized modules above
# This file serves as a facade for backward compatibility


# All plotting functions are now imported from specialized modules above.
# This file serves as a facade for backward compatibility.


# End of file - all functions are imported from specialized modules