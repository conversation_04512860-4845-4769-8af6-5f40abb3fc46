"""
SAFE 22 Beam Properties, Shapes, and Deep Hole Piles Module

This module provides functions for handling beam properties, structural shapes,
and deep hole pile (DHP) definitions in SAFE 22 finite element models. It manages
the conversion of pile and foundation data from Excel input formats to SAFE 22
DataFrame structures.

The module handles:
- Pile point definitions and connectivity
- <PERSON>le column geometry and properties
- Pile section property assignments
- Pile insertion points and local axes
- Pile end releases and restraints
- Pile spring definitions and assignments
- Pile group definitions and assignments
- Deep hole pile (DHP) specific configurations
- Micropile (MP) configurations

Functions:
    Pile Points and Geometry:
        - _write_pile_points_safe22: Create pile point connectivity
        - _write_pile_column_safe22: Create pile column geometry
        - _write_pile_prop_assign_safe22: Assign pile section properties

    Pile Configuration:
        - _write_pile_point_insertion_safe22: Set pile insertion points
        - _write_pile_local_axis_safe22: Configure pile local axes
        - _write_pile_end_release_safe22: Set pile end releases
        - _write_pile_end_restraint_safe22: Configure pile end restraints

    Pile Springs and Groups:
        - _write_pile_spring_safe22: Create pile spring definitions
        - _write_pile_group_safe22: Set up pile group assignments

    Helper Functions:
        - _add_pile_restraints_safe22: Helper for adding pile restraints

Example:
    >>> from safe22_bp_shp_dhp import _write_pile_points_safe22
    >>> safe22_dfs = _write_pile_points_safe22(excel_inputs, safe22_dfs)
    >>> print("Pile points created successfully")
"""

import pandas as pd
from typing import Tuple, Any, Optional, Dict, List


def _write_pile_points_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Create pile point connectivity data for SAFE 22 model.

    Processes lateral soil spring data from Excel inputs and creates SAFE 22
    compatible point connectivity for pile foundations. Each point represents
    a node in the pile model with 3D coordinates.
    """
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Prepare pile point data for SAFE 22 format
    df_safe22 = pd.DataFrame({
        'UniqueName': df_soil_spring['Point Name'],
        'Is Auto Point': 'No',  # User-defined points
        'IsSpecial': 'Yes',  # Mark as special points for pile foundations
        'X': df_soil_spring['X (m)'],  # X coordinate
        'Y': df_soil_spring['Y (m)'],  # Y coordinate
        'Z': df_soil_spring['Z (m)'],  # Z coordinate (depth)
        'GUID': None  # Will be populated if needed
    })

    # Get column structure from the target DataFrame
    target_columns_safe22 = safe22_dfs.PointObjectConnectivity.columns

    # Create a new DataFrame with the same columns and order for appending
    df_append_safe22 = pd.DataFrame(df_safe22.values, columns=target_columns_safe22)

    # Add pile points to existing point connectivity data
    safe22_dfs.PointObjectConnectivity = pd.concat(
        [safe22_dfs.PointObjectConnectivity, df_append_safe22],
        ignore_index=True
    )

    return safe22_dfs


def _write_pile_column_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Create pile column geometry and frame assignments for SAFE 22 model.

    Processes pile point data to create column connectivity and associated frame
    assignments including end length offsets, auto-mesh settings, and output stations.
    This function handles the geometric definition of pile segments as frame elements.
    """
    # Extract all pile point names from lateral soil spring data
    all_pile_point_names = excel_inputs.LateralSoilSpring['Point Name'].copy()

    # Determine pile segment names using point naming convention
    # Segment names are points that are not immediately followed by bottom points ('_B')
    # This identifies the start of each pile segment
    mask_segment_names = ~(
        all_pile_point_names.str.contains('_B', na=False).shift(-1, axis=0).fillna(False).astype(bool))
    segment_names = all_pile_point_names[mask_segment_names].reset_index(drop=True)

    # Identify segment start points (all points except bottom points)
    # These represent the I-end of each pile segment
    mask_segment_starts = ~all_pile_point_names.str.contains('_T', na=False)
    segment_starts = all_pile_point_names[mask_segment_starts].reset_index(drop=True)

    # Identify segment end points (all points except top points)
    # These represent the J-end of each pile segment
    mask_segment_ends = ~all_pile_point_names.str.contains('_B', na=False)
    segment_ends = all_pile_point_names[mask_segment_ends].reset_index(drop=True)

    # SAFE22: Prepare data for multiple related tables
    # Each tuple contains (target_attribute_name, dataframe_data)
    safe22_updates_definitions = [
        (
            "ColumnObjectConnectivity",  # Pile segment connectivity definitions
            pd.DataFrame({
                'UniqueName': segment_names,    # Unique identifier for each pile segment
                'UniquePtI': segment_starts,    # I-end point name (start of segment)
                'UniquePtJ': segment_ends,      # J-end point name (end of segment)
                'Length': None,                 # Length calculated automatically by SAFE
                'GUID': None                    # Unique identifier populated by SAFE
            })
        ),
        (
            "FrameAssignsEndLenOffsets",  # End length offset assignments
            pd.DataFrame({
                'Unique Name': segment_names,
                'Offset Option': 'User',        # User-defined offsets
                'Offset I': 0,                  # Zero offset at I-end
                'Offset J': 0,                  # Zero offset at J-end
                'Rigid Factor': 0,              # No rigid zone factor
                'Self Weight Option': 'Auto'    # Automatic self-weight calculation
            })
        ),
        (
            "FrameAssignsFrameAutoMesh",  # Auto-mesh settings for pile segments
            pd.DataFrame({
                'Unique Name': segment_names,
                'Auto Mesh': 'Yes',                    # Enable auto-meshing
                'At Intermediate Joints': 'Yes',       # Mesh at intermediate joints
                'At Intersections': 'Yes',             # Mesh at intersections
                'Min Number?': 'No',                   # No minimum number constraint
                'Max Length?': 'Yes',                  # Apply maximum length constraint
                'Segment Length': 1                    # Maximum segment length: 1 meter
            })
        ),
        (
            "FrameAssignsOutputStations",  # Output station configurations
            pd.DataFrame({
                'Unique Name': segment_names,
                'Station Option': 'Min Stations',      # Use minimum stations option
                'Min Stations': 3                      # Minimum 3 output stations per segment
            })
        )
    ]

    # Process each table update systematically
    for attr_name, temp_df in safe22_updates_definitions:
        # Get the current target table from safe22_dfs
        current_target_table = getattr(safe22_dfs, attr_name)
        target_columns_multi_index = current_target_table.columns

        # Create DataFrame with matching column structure (handles MultiIndex)
        df_to_append = pd.DataFrame(temp_df.values, columns=target_columns_multi_index)

        # Concatenate with existing data and update the target table
        updated_table = pd.concat([current_target_table, df_to_append], ignore_index=True)
        setattr(safe22_dfs, attr_name, updated_table)

    return safe22_dfs


def _write_pile_prop_assign_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Assign pile section properties to pile segments in SAFE 22 model.

    Maps pile section properties from pile definitions to individual pile segments
    based on pile mark identifiers. This function establishes the structural
    properties for each pile segment in the finite element model.
    """
    df_pile = excel_inputs.Pile.copy()
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Identify pile segments by finding points that are not immediately before bottom points
    # This excludes intermediate points that are followed by bottom points
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    target_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']

    # Extract base pile identifier from point names
    # Example: 'P1_T' -> 'P1', 'P2_1' -> 'P2'
    base_pile_ids = target_identifiers.str.split('_').str[0]

    # Create efficient mapping from pile mark to section property
    pile_section_map = df_pile.set_index('Pile Mark')['Pile Section']

    # Map each pile segment to its corresponding section property
    mapped_pile_sections = base_pile_ids.map(pile_section_map)

    # SAFE22: Create frame section property assignments
    df_data_safe22 = pd.DataFrame({
        'UniqueName': target_identifiers,           # Pile segment identifier
        'Shape': None,                              # Shape defined by section property
        'Auto Select List': 'N.A.',                # Not using auto-selection
        'Section Property': mapped_pile_sections    # Assigned section property name
    })

    # Ensure DataFrame columns match target table structure (handles MultiIndex)
    target_columns_safe22 = safe22_dfs.FrameAssignsSectProp.columns
    df_append_safe22 = pd.DataFrame(df_data_safe22.values, columns=target_columns_safe22)

    # Add section property assignments to existing data
    safe22_dfs.FrameAssignsSectProp = pd.concat(
        [safe22_dfs.FrameAssignsSectProp, df_append_safe22], ignore_index=True
    )
    return safe22_dfs


def _write_pile_point_insertion_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Configure pile insertion points for SAFE 22 model.

    Sets up frame insertion point assignments for pile segments, defining how
    the pile cross-section is positioned relative to the centerline. This is
    critical for proper pile modeling and load transfer calculations.
    """
    # Extract lateral soil spring data
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Identify pile segments by excluding points immediately before bottom points
    # This ensures all pile segments (not just intermediate points) get insertion points
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    pile_points_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']

    # SAFE22: Configure frame insertion point assignments
    df_data_safe22 = pd.DataFrame({
        'UniqueName': pile_points_identifiers,
        'Cardinal Point': '10 (Centroid)',         # Center pile section on centerline
        'Mirror2': 'No',                           # No mirroring about local 2-axis
        'Mirror3': 'No',                           # No mirroring about local 3-axis
        'No Transform Stiffness': 'No'             # Apply stiffness transformations
    })

    # Ensure DataFrame columns match target table structure (handles MultiIndex)
    target_columns_safe22 = safe22_dfs.FrameAssignsInsertionPoint.columns
    df_append_safe22 = pd.DataFrame(df_data_safe22.values, columns=target_columns_safe22)

    # Add insertion point assignments to existing data
    safe22_dfs.FrameAssignsInsertionPoint = pd.concat(
        [safe22_dfs.FrameAssignsInsertionPoint, df_append_safe22],
        ignore_index=True
    )

    return safe22_dfs


def _write_pile_local_axis_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Configure pile local axis orientations for SAFE 22 model.

    Calculates and assigns local axis rotations for pile segments based on pile
    geometry and orientation. This is critical for proper pile behavior modeling,
    especially for non-circular pile sections and directional loading.
    """
    df_pile_properties = excel_inputs.Pile.copy()
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Identify pile segments (points not immediately before bottom points)
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    target_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']

    # Extract base pile identifiers for property lookup
    # Example: 'P1_T' -> 'P1', 'P2_1' -> 'P2'
    base_pile_ids = target_identifiers.str.split('_').str[0]

    # Create mapping DataFrame for efficient merging
    df_for_merge = pd.DataFrame({
        'TargetIdentifier': target_identifiers,
        'BasePileID': base_pile_ids
    })

    # Select required pile properties for local axis calculation
    pile_props_to_merge = df_pile_properties[[
        'Pile Mark', 'X (m)', 'Y (m)', 'BX (m)', 'BY (m)', 'Pile Local Axis (Deg)'
    ]]

    # Merge pile properties with target identifiers
    df_merged_properties = pd.merge(
        df_for_merge,
        pile_props_to_merge,
        left_on='BasePileID',
        right_on='Pile Mark',
        how='left'
    )

    # Determine if pile coordinates differ from bearing coordinates
    # This indicates pile inclination or offset requiring axis adjustment
    condition_add_90_degrees = (
            (df_merged_properties['X (m)'] != df_merged_properties['BX (m)']) |
            (df_merged_properties['Y (m)'] != df_merged_properties['BY (m)'])
    )

    # Calculate local axis angle for SAFE 16 convention
    # Add 90° for piles with coordinate differences
    local_axis_safe16 = (
            df_merged_properties['Pile Local Axis (Deg)'] + 90 * condition_add_90_degrees
    )

    # This accounts for differences between SAFE 16 and SAFE 22 local axis conventions
    local_axis_safe22 = local_axis_safe16

    # Create local axis assignment data
    df_data_safe22 = pd.DataFrame({
        'UniqueName': df_merged_properties['TargetIdentifier'],
        'Angle': local_axis_safe22                              # Final local axis angle in degrees
    })

    # Ensure DataFrame columns match target table structure (handles MultiIndex)
    target_columns_safe22 = safe22_dfs.FrameAssignsLocalAxes.columns
    df_append_safe22 = pd.DataFrame(df_data_safe22.values, columns=target_columns_safe22)

    # Add local axis assignments to existing data
    safe22_dfs.FrameAssignsLocalAxes = pd.concat(
        [safe22_dfs.FrameAssignsLocalAxes, df_append_safe22], ignore_index=True
    )

    return safe22_dfs


def _write_pile_end_release_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Configure pile end releases for SAFE 22 model.

    Sets up moment releases at pile heads for piles with PIN connection conditions.
    This is critical for modeling realistic pile-to-cap connections where moment
    transfer is limited or not intended.
    """
    df_pile_properties = excel_inputs.Pile.copy()
    df_soil_spring_details = excel_inputs.LateralSoilSpring.copy()

    # Extract all pile point identifiers (top, intermediate, bottom)
    all_segment_identifiers = df_soil_spring_details['Point Name'].copy()

    # Extract base pile mark for property lookup
    # Example: 'P1_T' -> 'P1', 'P2_1' -> 'P2'
    base_pile_marks = all_segment_identifiers.str.split('_').str[0]

    # Create mapping DataFrame for efficient merging
    df_temp = pd.DataFrame({
        'SegmentIdentifier': all_segment_identifiers,
        'BasePileMark': base_pile_marks
    })

    # Merge with pile properties to get head condition information
    df_merged = pd.merge(
        df_temp,
        df_pile_properties[['Pile Mark', 'Pile Head Condition (PIN/FIX)']],
        left_on='BasePileMark',
        right_on='Pile Mark',
        how='left'
    )

    # Identify pile top segments with PIN connection condition
    # Only top points ('_T') of PIN piles need moment releases
    condition_is_pinned_top = (
            (df_merged['Pile Head Condition (PIN/FIX)'] == 'PIN') &
            (df_merged['SegmentIdentifier'].str.contains('_T', na=False))
    )

    pinned_top_segments = df_merged.loc[condition_is_pinned_top, 'SegmentIdentifier'].reset_index(drop=True)

    # Early return if no PIN connections found
    if pinned_top_segments.empty:
        return safe22_dfs

    # SAFE22: Configure frame end releases for PIN connections
    df_data_safe22 = pd.DataFrame({
        'UniqueName': pinned_top_segments,
        'PI': 'No',                    # No axial force release at I-end
        'PJ': 'No',                    # No axial force release at J-end
        'V2I': 'No',                   # No shear release in local 2-direction at I-end
        'V2J': 'No',                   # No shear release in local 2-direction at J-end
        'V3I': 'No',                   # No shear release in local 3-direction at I-end
        'V3J': 'No',                   # No shear release in local 3-direction at J-end
        'TI': 'No',                    # No torsional release at I-end
        'TJ': 'No',                    # No torsional release at J-end
        'M2I': 'No',                   # Release moment about local 2-axis at pile head
        'M2J': 'Yes',                  # No moment release at pile tip
        'M3I': 'No',                   # Release moment about local 3-axis at pile head
        'M3J': 'Yes'                   # No moment release at pile tip
    })

    # Ensure DataFrame columns match target table structure (handles MultiIndex)
    target_columns_safe22 = safe22_dfs.FrameAssignsReleases.columns
    df_append_safe22 = pd.DataFrame(df_data_safe22.values, columns=target_columns_safe22)

    # Add end release assignments to existing data
    safe22_dfs.FrameAssignsReleases = pd.concat(
        [safe22_dfs.FrameAssignsReleases, df_append_safe22], ignore_index=True
    )

    return safe22_dfs


def _add_pile_restraints_safe22(df_pile_subset: pd.DataFrame, safe22_dfs: Any, s22_restraint_values: Dict[str, str]) -> Any:
    """
    Helper function to add pile end restraints to SAFE 22 DataFrames.

    Applies restraint conditions to pile bottom points based on pile type and
    foundation conditions. This function is used by the main restraint assignment
    function to handle different pile types with appropriate boundary conditions.
    """
    # Early return if no piles to process
    if df_pile_subset.empty:
        return safe22_dfs

    # Generate bottom point names for restraint application
    base_points = df_pile_subset['Pile Mark'] + '_B'

    # SAFE22: Create joint restraint assignments
    df_s22_temp = pd.DataFrame({
        'UniqueName': base_points,                    # Pile bottom point identifiers
        'UX': s22_restraint_values['UX'],            # X-direction translation restraint
        'UY': s22_restraint_values['UY'],            # Y-direction translation restraint
        'UZ': s22_restraint_values['UZ'],            # Z-direction translation restraint
        'RX': s22_restraint_values['RX'],            # X-axis rotation restraint
        'RY': s22_restraint_values['RY'],            # Y-axis rotation restraint
        'RZ': s22_restraint_values['RZ']             # Z-axis rotation restraint
    })

    # Ensure DataFrame columns match target table structure (handles MultiIndex)
    target_columns_s22_tuples = safe22_dfs.JointAssignsRestraints.columns.tolist()
    df_append_s22 = pd.DataFrame(
        df_s22_temp.values, columns=pd.MultiIndex.from_tuples(target_columns_s22_tuples)
    )

    # Add restraint assignments to existing data
    safe22_dfs.JointAssignsRestraints = pd.concat(
        [safe22_dfs.JointAssignsRestraints, df_append_s22], ignore_index=True
    )
    return safe22_dfs


def _write_pile_end_restraint_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Configure pile end restraints based on pile type for SAFE 22 model.

    Applies appropriate boundary conditions to pile bottom points based on pile
    type and installation method. Different pile types require different restraint
    patterns to accurately model their interaction with surrounding soil.
    """
    df_pile = excel_inputs.Pile.copy()

    # Configuration 1: Standard piles (NOT DHP and NOT MP)
    # These piles have full lateral restraint from soil friction and end bearing
    condition_not_dhp_mp = (df_pile['Pile Type'] != 'DHP') & (df_pile['Pile Type'] != 'MP')
    df_pile_not_dhp_mp = df_pile[condition_not_dhp_mp]

    # Standard pile restraints: Full lateral and vertical restraint, free rotation
    s22_restraints_not_dhp_mp = {
        'UX': 'Yes',    # Lateral restraint in X-direction
        'UY': 'Yes',    # Lateral restraint in Y-direction
        'UZ': 'Yes',    # Vertical restraint (end bearing)
        'RX': 'No',     # Free rotation about X-axis
        'RY': 'No',     # Free rotation about Y-axis
        'RZ': 'No'      # Free rotation about Z-axis
    }
    safe22_dfs = _add_pile_restraints_safe22(df_pile_not_dhp_mp, safe22_dfs, s22_restraints_not_dhp_mp)

    # Configuration 2: Deep Hole Piles (DHP)
    # These piles are socketed into rock with limited lateral restraint
    condition_dhp = df_pile['Pile Type'] == 'DHP'
    df_pile_dhp = df_pile[condition_dhp]

    # DHP restraints: Free lateral movement, vertical restraint only
    s22_restraints_dhp = {
        'UX': 'No',     # Free lateral movement in X-direction (socket connection)
        'UY': 'No',     # Free lateral movement in Y-direction (socket connection)
        'UZ': 'Yes',    # Vertical restraint (end bearing on rock)
        'RX': 'No',     # Free rotation about X-axis
        'RY': 'No',     # Free rotation about Y-axis
        'RZ': 'No'      # Free rotation about Z-axis
    }
    safe22_dfs = _add_pile_restraints_safe22(df_pile_dhp, safe22_dfs, s22_restraints_dhp)

    return safe22_dfs


def _write_pile_spring_safe22(excel_inputs: Any, safe22_dfs: Any) -> Any:
    """
    Configure pile lateral soil springs for SAFE 22 model.

    Creates point spring properties and assignments to model lateral soil-pile
    interaction. Springs represent the lateral resistance provided by surrounding
    soil at various depths along the pile length.
    """
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Filter for points with valid subgrade reaction data
    # Only points with soil interaction need spring properties
    condition = ~pd.isna(df_soil_spring['Subgrade Reaction (kN/m3)'])
    df_soil_spring_filtered = df_soil_spring[condition].copy()

    # Early return if no valid spring data found
    if df_soil_spring_filtered.empty:
        return safe22_dfs

    # Extract spring data for property definitions
    point_names_filtered = df_soil_spring_filtered['Point Name'].values
    spring_x_filtered = df_soil_spring_filtered['Spring X (kN/m)'].values
    spring_y_filtered = df_soil_spring_filtered['Spring Y (kN/m)'].values

    # SAFE22: Create spring property definitions
    df_data_s22_props = pd.DataFrame({
        'Name': point_names_filtered,                    # Unique spring property name
        'Stiffness UX': spring_x_filtered,              # Lateral stiffness in X-direction
        'Stiffness UY': spring_y_filtered,              # Lateral stiffness in Y-direction
        'Stiffness UZ': 0,                              # No vertical springs
        'Stiffness RX': 0,                              # No rotational springs about X-axis
        'Stiffness RY': 0,                              # No rotational springs about Y-axis
        'Stiffness RZ': 0,                              # No rotational springs about Z-axis
        'Nonlinearity Specification': 'Quick',          # Quick nonlinearity setup
        'Nonlinear Option': 'None (Linear)',            # Linear springs only
        'Color': 'Green',                               # Visualization color
        'GUID': None,                                   # Unique identifier
        'Notes': None                                   # No notes
    })

    # Add spring properties to existing data
    target_columns_s22_props = safe22_dfs.SpringPropsPointSprings.columns
    df_append_s22_props = pd.DataFrame(df_data_s22_props.values, columns=target_columns_s22_props)
    safe22_dfs.SpringPropsPointSprings = pd.concat(
        [safe22_dfs.SpringPropsPointSprings, df_append_s22_props],
        ignore_index=True
    )

    # SAFE22: Create spring assignments to points
    df_data_s22_assign = pd.DataFrame({
        'UniqueName': point_names_filtered,             # Point identifier
        'SpringProp': point_names_filtered              # Assigned spring property name
    })

    # Add spring assignments to existing data
    target_columns_s22_assign = safe22_dfs.JointAssignsSprings.columns
    df_append_s22_assign = pd.DataFrame(df_data_s22_assign.values, columns=target_columns_s22_assign)
    safe22_dfs.JointAssignsSprings = pd.concat(
        [safe22_dfs.JointAssignsSprings, df_append_s22_assign],
        ignore_index=True
    )
    return safe22_dfs


def _write_pile_group_safe22(
    safe22_dfs: Any,
    pile_marks_series: pd.Series,  # Unused but kept for consistency with calling interface
    all_point_names: pd.Series,
    top_points: pd.Series,
    bottom_points: pd.Series,
    line_segments_for_pile_line_group: pd.Series,
    group_names_list: List[str]
) -> Any:
    """
    Create pile group definitions and assignments for SAFE 22 model.

    Organizes pile elements into logical groups for visualization, selection,
    and analysis purposes. Creates both general pile groups and individual
    pile-specific groups for detailed pile management.
    """

    def append_to_safe22(target_df_attr: str, data_dict: Dict[str, Any]) -> None:
        """
        Helper function to create and append data to SAFE22 DataFrames.
        """
        target_df = getattr(safe22_dfs, target_df_attr)
        df_to_append = pd.DataFrame(data_dict)
        # Ensure columns match target structure, especially for MultiIndex
        df_to_append = pd.DataFrame(df_to_append.values, columns=target_df.columns)
        setattr(safe22_dfs, target_df_attr, pd.concat([target_df, df_to_append], ignore_index=True))

    def add_group_assignments_s22(group_name: str, obj_type: str, obj_labels_s22: pd.Series) -> None:
        """
        Helper function to add group assignments for SAFE22.
        """
        # Ensure input is a pandas Series
        if not isinstance(obj_labels_s22, pd.Series):
            obj_labels_s22 = pd.Series(obj_labels_s22)

        # Skip if no objects to assign
        if obj_labels_s22.empty:
            return

        # Create group assignment data
        append_to_safe22('GroupAssignments', {
            'Group Name': group_name,
            'Object Type': obj_type,
            'Object Unique Name': obj_labels_s22.values
        })

    # Create general pile group definitions
    append_to_safe22('GroupDefinitions', {
        'Name': group_names_list,                    # Group names list
        'Color': 'Yellow',                          # Visualization color
        'Steel Design?': 'No',                      # Not used for steel design
        'Concrete Design?': 'No',                   # Not used for concrete design
        'Composite Design?': 'No',                  # Not used for composite design
        'GUID': None                                # Unique identifier
    })

    # Assign objects to general pile groups

    # Group 'A.Pile_Top': All pile top elements
    add_group_assignments_s22('A.Pile_Top', 'Point', top_points)
    add_group_assignments_s22('A.Pile_Top', 'Line', top_points)  # Line names match point names

    # Group 'A.Pile_Bottom': All pile bottom elements
    add_group_assignments_s22('A.Pile_Bottom', 'Point', bottom_points)
    add_group_assignments_s22('A.Pile_Bottom', 'Line', bottom_points)

    # Group 'A.Pile_Line': All pile line segments
    add_group_assignments_s22('A.Pile_Line', 'Line', line_segments_for_pile_line_group)

    # Group 'A.Pile_Point': All pile points
    add_group_assignments_s22('A.Pile_Point', 'Point', all_point_names)

    # Create individual pile groups for points (B.PilePoint_PXX)
    base_pile_ids_from_points = all_point_names.str.split('_').str[0]
    group_names_for_pile_points = 'B.PilePoint_' + base_pile_ids_from_points
    add_group_assignments_s22(group_names_for_pile_points, 'Point', all_point_names)

    # Create individual pile groups for lines (B.PileLine_PXX)
    base_pile_ids_from_line_segments = line_segments_for_pile_line_group.str.split('_').str[0]
    group_names_for_pile_lines = 'B.PileLine_' + base_pile_ids_from_line_segments
    add_group_assignments_s22(group_names_for_pile_lines, 'Line', line_segments_for_pile_line_group)

    return safe22_dfs

