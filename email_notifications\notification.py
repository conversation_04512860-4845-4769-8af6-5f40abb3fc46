import datetime
import random
import smtplib
import ssl
import string
import uuid
from email.message import EmailMessage
import requests
import pandas as pd
from io import StringIO
import urllib3
import logging

# Import configuration from centralized config
from config.app_config import (
    EMAIL_SENDER,
    EMAIL_PASSWORD,
    EMAIL_DOMAIN,
    SOFTWARE_VERSION,
    SSL_VERIFY_REQUESTS,
    SSL_STRICT_SMTP
)

# Disable SSL warnings for development (remove in production if possible)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Use EMAIL_DOMAIN from config (keeping AIS_EMAIL_DOMAIN for backward compatibility)
AIS_EMAIL_DOMAIN = EMAIL_DOMAIN


def create_smtp_ssl_context():
    """Create SSL context for SMTP with fallback options."""
    if SSL_STRICT_SMTP:
        # Use default SSL context (strict verification)
        return ssl.create_default_context()
    else:
        # Create relaxed SSL context for corporate environments
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        return context


def generate_password_key(length=30):
    """Generate a password key based on GUID.
    """
    if length <= 32:
        # Generate a single GUID and truncate to desired length
        guid = str(uuid.uuid4()).replace('-', '')  # Remove hyphens for cleaner password
        return guid[:length]
    else:
        # For longer passwords, concatenate multiple GUIDs
        password = ""
        while len(password) < length:
            guid = str(uuid.uuid4()).replace('-', '')
            password += guid
        return password[:length]


def send_password_email(user_name, password_key_to_send):
    """Send password email with SSL fallback handling."""
    email_receiver = user_name + AIS_EMAIL_DOMAIN
    subject = f"Foundation RPA {SOFTWARE_VERSION} Login Password"
    body = f"""
    Dear {user_name},

    Your Login Password for Foundation RPA {SOFTWARE_VERSION}:
    {password_key_to_send}

    Regards,
    Alex Sze
    """
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)
    
    # Try to send with SSL fallback
    success, message = _send_email_with_ssl_fallback(em, email_receiver, "password email")
    if success:
        logging.info(f'Sent Login Password to {email_receiver}')
        # Don't use print() in GUI applications - it can cause issues
        return True
    else:
        logging.error(f'Failed to send password email: {message}')
        raise Exception(f'Failed to send password email: {message}')


def send_email_log(user_name, case, version_type="Base", user_email=None):
    """Send usage log email with SSL fallback handling."""
    email_receiver = EMAIL_SENDER
    subject = 'Foundation RPA Usage Log'
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    user_email_address = user_name + AIS_EMAIL_DOMAIN
    body = f"""
    Timestamp: {timestamp}
    Username: {user_name}
    Email: {user_email_address}
    Version: {SOFTWARE_VERSION} ({version_type})
    Action: {case}
    """
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)
    
    # Try to send with SSL fallback
    success, message = _send_email_with_ssl_fallback(em, email_receiver, "usage log")
    if not success:
        logging.warning(f'Failed to send email log: {message}')
        # Don't raise exception for logging failures


def _send_email_with_ssl_fallback(email_message, receiver_email, description="email"):
    """Helper function to send email with SSL fallback handling."""
    try:
        # Try with default SSL context first
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context) as smtp:
            smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
            smtp.sendmail(EMAIL_SENDER, receiver_email, email_message.as_string())
        return True, "Email sent successfully"
        
    except ssl.SSLError as ssl_error:
        logging.warning(f'SSL Error sending {description}: {ssl_error}')
        # Try with relaxed SSL context
        try:
            context = create_smtp_ssl_context()
            with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context) as smtp:
                smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
                smtp.sendmail(EMAIL_SENDER, receiver_email, email_message.as_string())
            return True, f"Email sent successfully (with relaxed SSL)"
            
        except Exception as fallback_error:
            error_msg = f'Failed to send {description} even with relaxed SSL: {fallback_error}'
            logging.error(error_msg)
            return False, error_msg
            
    except smtplib.SMTPException as smtp_error:
        error_msg = f'SMTP Error sending {description}: {smtp_error}'
        logging.error(error_msg)
        return False, error_msg
        
    except Exception as general_error:
        error_msg = f'Unexpected error sending {description}: {general_error}'
        logging.error(error_msg)
        return False, error_msg


# Note: User authorization functions have been moved to auth.security_manager
# The following functions are now handled by SecurityManager:
# - get_authorized_users()
# - is_authorized_user() 
# - User list fetching and validation
# The fetch_user_list() function has been removed as it's redundant with SecurityManager