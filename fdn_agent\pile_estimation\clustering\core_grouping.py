﻿"""
Core Grouping Functions

Contains the main functions for 3D grouping of structural elements using 
machine learning clustering techniques.
"""

import warnings
from typing import List, Dict, Any, Optional
import numpy as np
from scipy.cluster.hierarchy import linkage, fcluster
from scipy.spatial.distance import squareform

from ..data_types import ColumnData, WallData, GroupElements
from ..exceptions import GroupingError, GeometryError
from .helpers import (
    preprocess_elements, compute_distance_matrix, find_optimal_threshold_internal,
    form_groups, create_single_element_group, validate_groups
)
from ..utils.logging_utils import (
    enhanced_log, log_function_entry, log_function_exit, log_progress, 
    log_performance_metric, log_validation_result, log_algorithm_step,
    log_calculation_result, log_error_with_context, create_timed_logger
)


def create_element_groups(columns: List[ColumnData], walls: List[WallData], 
                         distance_threshold: float = 5.0, log_callback=None) -> Dict[str, GroupElements]:
    """
    Create 3D groups using hierarchical clustering with distance thresholds.
    
    Args:
        columns: List of column data tuples (name, x, y, base_level)
        walls: List of wall data tuples (name, points_list, base_level)
        distance_threshold: Maximum distance for clustering elements (meters)
        log_callback: Optional callback for logging clustering progress
        
    Returns:
        Dictionary mapping group IDs to GroupElements
        
    Raises:
        GroupingError: If clustering fails or produces invalid groups
        GeometryError: If there are issues with the input geometry
    """
    log_function_entry(log_callback, "create_element_groups", 
                      columns_count=len(columns), walls_count=len(walls), 
                      distance_threshold=distance_threshold)
    
    with create_timed_logger(log_callback, "3D Element Grouping") as timer:
        try:
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Starting fixed threshold grouping", 
                             f"threshold={distance_threshold}m")
            enhanced_log(log_callback, f"Input elements: {len(columns)} columns, {len(walls)} walls", 'INFO')            
            # Convert all elements to a common format
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Preprocessing elements")
            elements = preprocess_elements(columns, walls, log_callback)
            
            if not elements:
                raise GroupingError("No valid elements to group")
                
            # If only one element, return it as a single group
            if len(elements) == 1:
                enhanced_log(log_callback, "Only one element found, creating single-element group", 'INFO')
                result = create_single_element_group(elements[0])
                log_function_exit(log_callback, "create_element_groups", f"{len(result)} groups")
                return result
                
            # Compute distance matrix between all elements
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Computing distance matrix", 
                             f"{len(elements)} elements")
            distance_matrix = compute_distance_matrix(elements, log_callback)

            min_dist = np.min(distance_matrix[distance_matrix > 0])
            max_dist = np.max(distance_matrix)
            avg_dist = np.mean(distance_matrix[distance_matrix > 0])
            log_calculation_result(log_callback, "Distance Matrix Stats", 
                                 f"Min: {min_dist:.2f}m, Max: {max_dist:.2f}m, Avg: {avg_dist:.2f}m")

            # Apply hierarchical clustering with single linkage
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Applying single linkage clustering")
            linkage_matrix = linkage(squareform(distance_matrix), method='single')

            # Form clusters using the specified threshold
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Forming clusters", 
                             f"threshold={distance_threshold}m")
            cluster_labels = fcluster(
                linkage_matrix, 
                t=distance_threshold,
                criterion='distance'
            )
            
            unique_clusters = len(set(cluster_labels))
            log_calculation_result(log_callback, "Clustering Result", 
                                 f"{unique_clusters} clusters from {len(elements)} elements")
            
            # Log cluster size distribution
            for i in range(1, unique_clusters + 1):
                count = np.sum(cluster_labels == i)
                enhanced_log(log_callback, f"Cluster {i}: {count} elements", 'DEBUG')

            # Group elements by cluster
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Organizing elements into groups")
            groups = form_groups(elements, cluster_labels, log_callback)

            # Log final group statistics and validate
            stats = get_clustering_statistics(groups, log_callback)
            log_performance_metric(log_callback, "Groups Created", len(groups), "groups")
            log_performance_metric(log_callback, "Average Group Size", stats.get('avg_group_size', 0), "elements")
            
            # Validate the resulting groups
            log_algorithm_step(log_callback, "Hierarchical Clustering", "Validating groups")
            validate_groups(groups)
            log_validation_result(log_callback, "Group Validation", True, f"{len(groups)} groups passed validation")
            
            enhanced_log(log_callback, "3D grouping completed successfully", 'INFO')
            log_function_exit(log_callback, "create_element_groups", f"{len(groups)} groups")
            return groups
            
        except Exception as e:
            log_error_with_context(log_callback, e, "create_element_groups")
            raise GroupingError(f"Failed to create 3D groups: {str(e)}") from e


def find_optimal_grouping_threshold(columns: List[ColumnData], walls: List[WallData],
                                   min_threshold: float = 1.0, max_threshold: float = 20.0,
                                   step: float = 0.5, metric: str = 'silhouette', 
                                   log_callback=None) -> float:
    """
    Find the optimal distance threshold for hierarchical clustering.
    
    Args:
        columns: List of column data tuples
        walls: List of wall data tuples
        min_threshold: Minimum threshold to try (meters)
        max_threshold: Maximum threshold to try (meters)
        step: Step size for threshold search (meters)
        metric: Currently only 'silhouette' is supported
        log_callback: Optional callback for logging threshold search progress
        
    Returns:
        Optimal distance threshold for clustering
        
    Raises:
        GroupingError: If optimal threshold cannot be determined
        GeometryError: If there are issues with the input geometry
    """
    log_function_entry(log_callback, "find_optimal_grouping_threshold",
                      columns_count=len(columns), walls_count=len(walls),
                      min_threshold=min_threshold, max_threshold=max_threshold,
                      step=step, metric=metric)
    
    with create_timed_logger(log_callback, "Optimal Threshold Search") as timer:
        try:
            log_algorithm_step(log_callback, "Threshold Optimization", "Starting threshold search",
                             f"range: {min_threshold}-{max_threshold}m, step: {step}m, metric: {metric}")

            # Preprocess elements like in create_element_groups
            log_algorithm_step(log_callback, "Threshold Optimization", "Preprocessing elements")
            elements = preprocess_elements(columns, walls, log_callback)

            if len(elements) <= 1:
                enhanced_log(log_callback, f"Only {len(elements)} element(s), returning minimum threshold {min_threshold}m", 'INFO')
                log_function_exit(log_callback, "find_optimal_grouping_threshold", min_threshold)
                return min_threshold

            # Compute distance matrix and linkage matrix with single linkage
            log_algorithm_step(log_callback, "Threshold Optimization", "Computing distance matrix and linkage")
            distance_matrix = compute_distance_matrix(elements, log_callback)
            linkage_matrix = linkage(squareform(distance_matrix), method='single')
            
            # Calculate search parameters
            search_range = max_threshold - min_threshold
            num_steps = int(search_range / step) + 1
            log_performance_metric(log_callback, "Search Steps", num_steps, "thresholds")
            
            # Call the internal method
            log_algorithm_step(log_callback, "Threshold Optimization", "Running silhouette analysis")
            optimal_threshold = find_optimal_threshold_internal(
                elements, distance_matrix, linkage_matrix, 
                min_threshold, max_threshold, step, log_callback
            )
            
            log_calculation_result(log_callback, "Optimal Threshold", optimal_threshold, "meters")
            enhanced_log(log_callback, f"Optimal threshold search completed: {optimal_threshold:.2f}m", 'INFO')
            log_function_exit(log_callback, "find_optimal_grouping_threshold", optimal_threshold)
            return optimal_threshold

        except Exception as e:
            log_error_with_context(log_callback, e, "find_optimal_grouping_threshold")
            raise GroupingError(f"Failed to find optimal threshold: {str(e)}") from e


def create_element_groups_with_auto_threshold(columns: List[ColumnData], walls: List[WallData],
                                             min_threshold: float = 1.0, max_threshold: float = 20.0, 
                                             step: float = 0.5, metric: str = 'silhouette', 
                                             log_callback=None) -> Dict[str, GroupElements]:
    """
    Create 3D groups using the automatically determined best threshold.
    
    Args:
        columns: List of column data tuples
        walls: List of wall data tuples
        min_threshold: Minimum threshold to try (meters)
        max_threshold: Maximum threshold to try (meters)
        step: Step size for threshold search (meters)
        metric: Currently only 'silhouette' is supported
        log_callback: Optional callback for logging clustering progress
        
    Returns:
        Dictionary mapping group IDs to GroupElements
        
    Raises:
        GroupingError: If clustering fails or produces invalid groups        GeometryError: If there are issues with the input geometry
    """
    log_function_entry(log_callback, "create_element_groups_with_auto_threshold",
                      columns_count=len(columns), walls_count=len(walls),
                      min_threshold=min_threshold, max_threshold=max_threshold,
                      step=step, metric=metric)
    
    with create_timed_logger(log_callback, "Automatic 3D Element Grouping") as timer:
        try:
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Starting automatic grouping")
            enhanced_log(log_callback, f"Grouping {len(columns)} columns and {len(walls)} walls...", 'INFO')

            # Preprocess elements
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Preprocessing elements")
            elements = preprocess_elements(columns, walls, log_callback)

            if not elements:
                raise GroupingError("No valid elements to group")

            # If only one element, return it as a single group
            if len(elements) == 1:
                enhanced_log(log_callback, "Only one element found, creating single-element group", 'INFO')
                result = create_single_element_group(elements[0])
                log_function_exit(log_callback, "create_element_groups_with_auto_threshold", f"{len(result)} groups")
                return result

            # Compute distance matrix
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Computing distance matrix", 
                             f"{len(elements)} elements")
            distance_matrix = compute_distance_matrix(elements, log_callback)

            # Apply hierarchical clustering with single linkage
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Computing linkage matrix")
            linkage_matrix = linkage(squareform(distance_matrix), method='single')
            
            # Find optimal threshold
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Finding optimal threshold",
                             "using silhouette analysis")
            optimal_threshold = find_optimal_threshold_internal(
                elements, distance_matrix, linkage_matrix,
                min_threshold, max_threshold, step, log_callback
            )

            log_calculation_result(log_callback, "Optimal Threshold", optimal_threshold, "meters")
            enhanced_log(log_callback, f"Using optimal threshold {optimal_threshold:.2f}m for final clustering", 'INFO')

            # Form clusters using the optimal threshold
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Forming final clusters",
                             f"threshold={optimal_threshold:.2f}m")
            cluster_labels = fcluster(
                linkage_matrix,
                t=optimal_threshold,
                criterion='distance'
            )

            unique_clusters = len(set(cluster_labels))
            log_calculation_result(log_callback, "Final Clustering Result",
                                 f"{unique_clusters} clusters from {len(elements)} elements")
            
            # Log cluster size distribution
            for i in range(1, unique_clusters + 1):
                count = np.sum(cluster_labels == i)
                enhanced_log(log_callback, f"Cluster {i}: {count} elements", 'DEBUG')

            # Group elements by cluster
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Organizing elements into final groups")
            groups = form_groups(elements, cluster_labels, log_callback)

            # Log comprehensive group statistics and validate
            stats = get_clustering_statistics(groups, log_callback)
            log_performance_metric(log_callback, "Groups Created", len(groups), "groups")
            log_performance_metric(log_callback, "Average Group Size", stats.get('avg_group_size', 0), "elements")
            log_performance_metric(log_callback, "Groups with Mixed Elements", stats.get('groups_with_mixed', 0), "groups")
            
            # Validate the resulting groups
            log_algorithm_step(log_callback, "Auto-Threshold Clustering", "Validating final groups")
            validate_groups(groups)
            log_validation_result(log_callback, "Group Validation", True, f"{len(groups)} groups passed validation")
            
            enhanced_log(log_callback, "Automatic 3D grouping completed successfully", 'INFO')
            log_function_exit(log_callback, "create_element_groups_with_auto_threshold", f"{len(groups)} groups")
            return groups
            
        except Exception as e:
            log_error_with_context(log_callback, e, "create_element_groups_with_auto_threshold")
            raise GroupingError(f"Failed to create 3D groups automatically: {str(e)}") from e


def get_clustering_statistics(groups: Dict[str, GroupElements], log_callback=None) -> Dict[str, Any]:
    """
    Get statistics about the clustering results.
    
    Args:
        groups: Dictionary of groups from clustering
        log_callback: Optional callback for logging statistics
        
    Returns:
        Dictionary with clustering statistics
    """
    log_function_entry(log_callback, "get_clustering_statistics", groups_count=len(groups))
    
    stats = {
        'total_groups': len(groups),
        'total_columns': 0,
        'total_walls': 0,
        'group_sizes': [],
        'groups_with_columns_only': 0,
        'groups_with_walls_only': 0,
        'groups_with_mixed': 0
    }
    
    enhanced_log(log_callback, "Computing clustering statistics", 'DEBUG')
    log_performance_metric(log_callback, "Total Groups", len(groups), "groups")

    for group_name, group_data in groups.items():
        num_columns = len(group_data.get('columns', []))
        num_walls = len(group_data.get('walls', []))
        group_size = num_columns + num_walls

        stats['total_columns'] += num_columns
        stats['total_walls'] += num_walls
        stats['group_sizes'].append(group_size)

        enhanced_log(log_callback, f"{group_name}: {group_size} elements ({num_columns} columns, {num_walls} walls)", 'DEBUG')

        if num_columns > 0 and num_walls == 0:
            stats['groups_with_columns_only'] += 1
        elif num_walls > 0 and num_columns == 0:
            stats['groups_with_walls_only'] += 1
        elif num_columns > 0 and num_walls > 0:
            stats['groups_with_mixed'] += 1

    if stats['group_sizes']:
        stats['avg_group_size'] = sum(stats['group_sizes']) / len(stats['group_sizes'])
        stats['min_group_size'] = min(stats['group_sizes'])
        stats['max_group_size'] = max(stats['group_sizes'])

    # Log comprehensive statistics
    total_elements = stats['total_columns'] + stats['total_walls']
    log_performance_metric(log_callback, "Total Elements", total_elements, "elements")
    log_performance_metric(log_callback, "Total Columns", stats['total_columns'], "columns")
    log_performance_metric(log_callback, "Total Walls", stats['total_walls'], "walls")
    
    enhanced_log(log_callback, f"Group types - Columns only: {stats['groups_with_columns_only']}, "
                              f"Walls only: {stats['groups_with_walls_only']}, "
                              f"Mixed: {stats['groups_with_mixed']}", 'INFO')
    
    if 'avg_group_size' in stats:
        log_performance_metric(log_callback, "Average Group Size", stats['avg_group_size'], "elements")
        log_performance_metric(log_callback, "Min Group Size", stats['min_group_size'], "elements")
        log_performance_metric(log_callback, "Max Group Size", stats['max_group_size'], "elements")
    
    enhanced_log(log_callback, f"Group size distribution: {stats['group_sizes']}", 'DEBUG')
    
    log_function_exit(log_callback, "get_clustering_statistics", f"{len(stats)} statistics computed")
    return stats

