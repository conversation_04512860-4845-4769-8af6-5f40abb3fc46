# Foundation Automation - Configuration Package

The config package provides centralized configuration management for the Foundation Automation system. It includes settings for application behavior, security, email, SSL handling, and system paths.

## Package Structure

The package is organized into several key modules:

### Core Configuration

1. `app_config.py`
   - Application-wide configuration settings
   - Security settings and constants
   - Email configuration
   - SSL settings
   - URL constants
   - Path definitions
   - Software version management

2. `ssl_config.py`
   - SSL configuration and certificate handling
   - SSL context creation
   - Requests library SSL configuration
   - Proxy handling
   - Certificate verification

### Configuration Files

- `__init__.py`: Package initialization
- `app_config.py`: Main application configuration
- `ssl_config.py`: SSL and certificate configuration

## Key Configuration Settings

### Application Settings
- `APP_TITLE`: Application title
- `APP_GEOMETRY`: Default window size
- `SOFTWARE_VERSION`: Application version

### Security Settings
- `MAX_LOGIN_ATTEMPTS`: Maximum login attempts
- `LOGIN_TIMEOUT_MINUTES`: Login timeout duration
- `SESSION_TOKEN_BYTES`: Session token size
- `SESSION_VERIFY_INTERVAL_MS`: Session verification interval
- `SESSION_DURATION_HOURS`: Session duration

### Email Configuration
- `EMAIL_SENDER`: Email sender address
- `EMAIL_PASSWORD`: Email authentication
- `EMAIL_DOMAIN`: Email domain

### SSL Settings
- `SSL_VERIFY_REQUESTS`: SSL verification for requests
- `SSL_STRICT_SMTP`: Strict SSL enforcement

### Path Configuration
- `DATA_DIR`: Base data directory
- `SECURITY_LOG_PATH`: Security log location
- `ICON_PATH`: Application icon location

## SSL Configuration

The package includes comprehensive SSL configuration through `ssl_config.py`:

1. SSL Context Creation
   - Uses certifi for certificate handling
   - Supports custom certificate bundles
   - Handles proxy settings

2. Requests Configuration
   - Manages SSL verification
   - Handles corporate environment SSL issues
   - Provides warning system for insecure configurations

## Usage Examples

### Accessing Configuration Settings
```python
from config.app_config import APP_TITLE, SOFTWARE_VERSION

# Example usage
print(f"Running {APP_TITLE} version {SOFTWARE_VERSION}")
```

### Using SSL Configuration
```python
from config.ssl_config import get_ssl_context

# Get SSL context for secure connections
ssl_context = get_ssl_context()
```

## Best Practices

1. Keep sensitive information (like passwords) in environment variables
2. Use SSL verification in production environments
3. Handle SSL exceptions appropriately
4. Follow corporate security policies
5. Regularly update certificate bundles

## Error Handling

The package includes error handling for:
- SSL verification failures
- Proxy configuration issues
- Certificate loading problems
- Path existence checks

## Integration Points

This package integrates with:
- Foundation Automation GUI
- Email notification system
- Security system
- Network communication modules
- File system operations

## Version

Current configuration version: V5.3 (matches notification.py version)

## Environment Variables

The package supports several environment variables:
- `CUSTOM_CA_BUNDLE`: Path to custom certificate bundle
- `HTTP_PROXY`: HTTP proxy settings
- `HTTPS_PROXY`: HTTPS proxy settings
- `EMAIL_PASSWORD`: Email authentication password

## Security Considerations

1. Keep sensitive information in environment variables
2. Use proper SSL verification in production
3. Handle proxy settings securely
4. Regularly update certificates
5. Follow corporate security policies
