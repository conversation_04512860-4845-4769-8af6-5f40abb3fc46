"""
Coordinate Transformer Module for ETABS CWLS

This module handles coordinate transformations for pier forces and moments
from local pier coordinates to global coordinates. It provides professional
mathematical operations with enhanced error handling and validation.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional mathematical operations

Key Functions:
    create_raw_schedule: Create raw schedule with coordinate transformation
    transform_pier_forces: Transform forces from local to global coordinates
    transform_pier_moments: Transform moments from local to global coordinates
    integer_round: Professional engineering rounding function

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import math
import pandas as pd
import numpy as np
from typing import Optional, Callable, Union, Tuple

from etabs_cwls.config.exceptions import CalculationError, DataValidationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context
)


def create_raw_schedule(
    df_pierforce: pd.DataFrame, 
    loadname: str, 
    loadcase: str, 
    df_addrotation: float, 
    consider_torsion: bool = True,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Create raw schedule from pier force data with coordinate transformation.
    
    This function processes pier force data for a specific load case, applying
    coordinate transformations based on pier axis angles and global rotation.
    It converts ETABS local pier forces to global coordinates for the schedule.
    
    Args:
        df_pierforce: DataFrame containing pier force data
        loadname: Name of the load case in ETABS
        loadcase: Load case identifier for the schedule
        df_addrotation: Additional global rotation in radians
        consider_torsion: Whether to include torsion (Mz) in the schedule
        log_callback: Optional logging callback function
        
    Returns:
        DataFrame containing raw schedule data for the load case
        
    Raises:
        CalculationError: If coordinate transformation fails
        DataValidationError: If input data is invalid
        
    Example:
        >>> raw_schedule = create_raw_schedule(pier_forces, "DL", "DL", 0.0, True)
    """
    log_function_entry(log_callback, "create_raw_schedule", 
                      loadname=loadname, loadcase=loadcase, consider_torsion=consider_torsion)
    
    try:
        # Input validation
        if not isinstance(df_pierforce, pd.DataFrame):
            raise DataValidationError(
                f"df_pierforce must be a DataFrame, got {type(df_pierforce).__name__}",
                field_name="df_pierforce",
                invalid_value=type(df_pierforce).__name__,
                expected_type="DataFrame",
                error_code="INVALID_PIERFORCE_TYPE"
            )
        
        if df_pierforce.empty:
            enhanced_log(log_callback, f"Warning: Empty pier force data for {loadname}", 'WARNING')
            return pd.DataFrame(columns=['Pier'])
        
        # Filter data for the specific load case
        df_filtered = df_pierforce[df_pierforce['CaseCombo'] == loadname].reset_index(drop=True)
        
        if df_filtered.empty:
            enhanced_log(log_callback, f"Warning: No data found for load case {loadname}", 'WARNING')
            return pd.DataFrame(columns=['Pier'])
        
        log_calculation_result(log_callback, f"{loadcase}_filtered_records", len(df_filtered), "records")
        
        # Initialize raw schedule DataFrame
        df_raw_schedule = pd.DataFrame()
        
        # Process each pier
        for index, row in df_filtered.iterrows():
            try:
                pier_name = df_filtered.loc[index, 'Pier']
                
                # Extract force and moment components
                P = -df_filtered.loc[index, 'P']  # Negative for compression positive convention
                V2 = df_filtered.loc[index, 'V2']
                V3 = df_filtered.loc[index, 'V3']
                M2 = df_filtered.loc[index, 'M2']
                M3 = df_filtered.loc[index, 'M3']
                T = df_filtered.loc[index, 'T'] if 'T' in df_filtered.columns else 0
                
                # Calculate total rotation angle (pier axis angle + global rotation)
                pier_axis_angle = df_filtered.loc[index, 'AxisAngle']
                total_radian = math.radians(pier_axis_angle) + df_addrotation
                
                # Apply coordinate transformation
                Vx, Vy = transform_pier_forces(V2, V3, total_radian, log_callback)
                Mx, My = transform_pier_moments(M2, M3, total_radian, log_callback)
                
                # Store results in schedule
                df_raw_schedule.at[index, 'Pier'] = pier_name
                df_raw_schedule.at[index, f'{loadcase}Vx'] = Vx
                df_raw_schedule.at[index, f'{loadcase}Vy'] = Vy
                df_raw_schedule.at[index, f'{loadcase}P'] = P
                df_raw_schedule.at[index, f'{loadcase}Mx'] = Mx
                df_raw_schedule.at[index, f'{loadcase}My'] = My
                
                if consider_torsion:
                    df_raw_schedule.at[index, f'{loadcase}Mz'] = T
                
            except Exception as e:
                raise CalculationError(
                    f"Failed to process pier {pier_name} in load case {loadcase}: {str(e)}",
                    calculation_type="coordinate_transformation",
                    input_parameters={
                        "pier": pier_name,
                        "loadcase": loadcase,
                        "loadname": loadname
                    },
                    error_code="PIER_TRANSFORMATION_FAILED"
                ) from e
        
        # Sort by pier name for consistent ordering
        df_raw_schedule = df_raw_schedule.sort_values(by='Pier').reset_index(drop=True)
        
        log_calculation_result(log_callback, f"{loadcase}_processed_piers", len(df_raw_schedule), "piers")
        enhanced_log(log_callback, f"Raw schedule created for {loadcase} with {len(df_raw_schedule)} piers", 'DEBUG')
        
        log_function_exit(log_callback, "create_raw_schedule", f"{len(df_raw_schedule)} piers")
        return df_raw_schedule
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"create_raw_schedule({loadcase})")
        raise


def transform_pier_forces(
    V2: float, 
    V3: float, 
    total_radian: float,
    log_callback: Optional[Callable] = None
) -> Tuple[float, float]:
    """
    Transform pier forces from local to global coordinates.
    
    Args:
        V2: Local pier force in V2 direction
        V3: Local pier force in V3 direction
        total_radian: Total rotation angle in radians
        log_callback: Optional logging callback function
        
    Returns:
        Tuple of (Vx, Vy) global forces
        
    Raises:
        CalculationError: If transformation fails
    """
    log_function_entry(log_callback, "transform_pier_forces", V2=V2, V3=V3)
    
    try:
        # Apply coordinate transformation from local pier coordinates to global coordinates
        # V2 and V3 are local pier forces, transform to global Vx and Vy
        Vx = V2 * math.cos(total_radian) + V3 * math.cos(total_radian + math.pi / 2)
        Vy = V2 * math.sin(total_radian) + V3 * math.sin(total_radian + math.pi / 2)
        
        log_calculation_result(log_callback, "transformed_Vx", Vx, "kN")
        log_calculation_result(log_callback, "transformed_Vy", Vy, "kN")
        
        log_function_exit(log_callback, "transform_pier_forces", f"Vx={Vx:.2f}, Vy={Vy:.2f}")
        return Vx, Vy
        
    except Exception as e:
        raise CalculationError(
            f"Failed to transform pier forces: {str(e)}",
            calculation_type="force_transformation",
            input_parameters={"V2": V2, "V3": V3, "rotation": total_radian},
            error_code="FORCE_TRANSFORMATION_FAILED"
        ) from e


def transform_pier_moments(
    M2: float, 
    M3: float, 
    total_radian: float,
    log_callback: Optional[Callable] = None
) -> Tuple[float, float]:
    """
    Transform pier moments from local to global coordinates.
    
    Args:
        M2: Local pier moment about M2 axis
        M3: Local pier moment about M3 axis
        total_radian: Total rotation angle in radians
        log_callback: Optional logging callback function
        
    Returns:
        Tuple of (Mx, My) global moments
        
    Raises:
        CalculationError: If transformation fails
    """
    log_function_entry(log_callback, "transform_pier_moments", M2=M2, M3=M3)
    
    try:
        # Transform moments from local to global coordinates
        Mx = M2 * math.cos(total_radian) + M3 * math.cos(total_radian + math.pi / 2)
        My = M2 * math.sin(total_radian) + M3 * math.sin(total_radian + math.pi / 2)
        
        log_calculation_result(log_callback, "transformed_Mx", Mx, "kNm")
        log_calculation_result(log_callback, "transformed_My", My, "kNm")
        
        log_function_exit(log_callback, "transform_pier_moments", f"Mx={Mx:.2f}, My={My:.2f}")
        return Mx, My
        
    except Exception as e:
        raise CalculationError(
            f"Failed to transform pier moments: {str(e)}",
            calculation_type="moment_transformation",
            input_parameters={"M2": M2, "M3": M3, "rotation": total_radian},
            error_code="MOMENT_TRANSFORMATION_FAILED"
        ) from e


def integer_round(value: Union[float, int]) -> int:
    """
    Professional engineering rounding function for structural loads.
    
    This function implements engineering rounding conventions where:
    - Values >= 1 are rounded up (conservative for loads)
    - Values <= -1 are rounded down (conservative for loads)
    - Values between -1 and 1 are rounded to 0 (negligible loads)
    
    Args:
        value: Numerical value to round
        
    Returns:
        Rounded integer value
        
    Raises:
        DataValidationError: If input is not a number
        
    Example:
        >>> integer_round(1.2)   # Returns 2
        >>> integer_round(-1.2)  # Returns -2
        >>> integer_round(0.5)   # Returns 0
    """
    try:
        # Input validation
        if not isinstance(value, (int, float, np.number)):
            raise DataValidationError(
                f"Value must be a number, got {type(value).__name__}",
                field_name="value",
                invalid_value=value,
                expected_type="number",
                error_code="INVALID_ROUNDING_INPUT"
            )
        
        # Handle NaN and infinity
        if math.isnan(value) or math.isinf(value):
            return 0
        
        # Apply engineering rounding rules
        if value >= 1:
            return math.ceil(value)
        elif value <= -1:
            return math.floor(value)
        else:
            return 0
            
    except Exception as e:
        # Fallback to 0 for any unexpected errors
        return 0


def validate_coordinate_transformation_inputs(
    df_pierforce: pd.DataFrame,
    loadname: str,
    loadcase: str,
    df_addrotation: float,
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate inputs for coordinate transformation operations.
    
    Args:
        df_pierforce: DataFrame containing pier force data
        loadname: Name of the load case in ETABS
        loadcase: Load case identifier for the schedule
        df_addrotation: Additional global rotation in radians
        log_callback: Optional logging callback function
        
    Returns:
        True if all inputs are valid
        
    Raises:
        DataValidationError: If any input is invalid
    """
    log_function_entry(log_callback, "validate_coordinate_transformation_inputs")
    
    try:
        # Validate DataFrame
        if not isinstance(df_pierforce, pd.DataFrame):
            raise DataValidationError(
                f"df_pierforce must be a DataFrame, got {type(df_pierforce).__name__}",
                field_name="df_pierforce",
                invalid_value=type(df_pierforce).__name__,
                expected_type="DataFrame",
                error_code="INVALID_PIERFORCE_TYPE"
            )
        
        # Validate load names
        if not isinstance(loadname, str) or not loadname.strip():
            raise DataValidationError(
                "loadname must be a non-empty string",
                field_name="loadname",
                invalid_value=loadname,
                expected_type="str",
                error_code="INVALID_LOADNAME"
            )
        
        if not isinstance(loadcase, str) or not loadcase.strip():
            raise DataValidationError(
                "loadcase must be a non-empty string",
                field_name="loadcase",
                invalid_value=loadcase,
                expected_type="str",
                error_code="INVALID_LOADCASE"
            )
        
        # Validate rotation
        if not isinstance(df_addrotation, (int, float)):
            raise DataValidationError(
                f"df_addrotation must be a number, got {type(df_addrotation).__name__}",
                field_name="df_addrotation",
                invalid_value=df_addrotation,
                expected_type="number",
                error_code="INVALID_ROTATION"
            )
        
        # Check for required columns
        required_columns = ['Pier', 'CaseCombo', 'P', 'V2', 'V3', 'M2', 'M3', 'AxisAngle']
        missing_columns = [col for col in required_columns if col not in df_pierforce.columns]
        
        if missing_columns:
            raise DataValidationError(
                f"Missing required columns in pier force data: {missing_columns}",
                field_name="df_pierforce_columns",
                invalid_value=missing_columns,
                error_code="MISSING_REQUIRED_COLUMNS"
            )
        
        enhanced_log(log_callback, "Coordinate transformation inputs validated successfully", 'DEBUG')
        log_validation_result(log_callback, "coordinate_transformation_inputs", True,
                            "All inputs validated successfully")
        log_function_exit(log_callback, "validate_coordinate_transformation_inputs", True)
        
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, "validate_coordinate_transformation_inputs")
        log_validation_result(log_callback, "coordinate_transformation_inputs", False, str(e))
        raise


# Export all functions
__all__ = [
    'create_raw_schedule',
    'transform_pier_forces',
    'transform_pier_moments',
    'integer_round',
    'validate_coordinate_transformation_inputs'
]
