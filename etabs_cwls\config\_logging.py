"""
Legacy Logging Module for ETABS CWLS

This module provides backward compatibility for existing logging functionality
while transitioning to the enhanced logging system. It maintains the original
logger interface while redirecting to external log file storage.

Note: This module is deprecated. New code should use logging_config.py directly.
"""

import logging
import warnings
from etabs_cwls.config.logging_config import get_logger, get_log_file_path

# Issue deprecation warning
warnings.warn(
    "etabs_cwls._logging is deprecated. Use etabs_cwls.logging_config instead.",
    DeprecationWarning,
    stacklevel=2
)

# Get the enhanced logger
logger = get_logger()

# For backward compatibility, expose the log file path
log_file_path = get_log_file_path()

# Legacy logger reference for backward compatibility
__all__ = ['logger', 'log_file_path']
