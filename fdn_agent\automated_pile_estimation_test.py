"""
Automated Pile Estimation Testing Function

This module provides automated testing functionality for the pile estimation system
without requiring the GUI. It uses the same default values and workflow as the GUI
to perform comprehensive testing of the "1. Initialize" and "2. Run Estimation" steps.

Author: Foundation Automation System
Date: June 16, 2025
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List

# Import the main agent functions
from fdn_agent import agent_main


class AutomatedPileEstimationTester:
    """
    Automated tester for pile estimation system that replicates GUI functionality
    without requiring the GUI interface.
    """
    
    def __init__(self, input_folder: str, log_callback: Optional[callable] = None):
        """
        Initialize the automated tester.
        
        Args:
            input_folder: Path to the input folder containing Excel files
            log_callback: Optional callback function for logging messages
        """
        self.input_folder = str(Path(input_folder))
        self.log_callback = log_callback or self._default_log_callback
        
        # Default values from GUI (agent_gui.py lines 49-59)
        self.default_pile_types = {
            'DHP': [{'capacity': 3663, 'section': 'UBP_305x305x223', 'diameter': None, 'min_spacing': 1.2}],
            'SHP': [{'capacity': 6106, 'section': 'UBP_305x305x223', 'diameter': None, 'min_spacing': 1.85}],
            'BP': [
                {'capacity': 41155, 'section': None, 'diameter': 2.0, 'min_spacing': 6.0},
                {'capacity': 72064, 'section': None, 'diameter': 2.0, 'min_spacing': 6.0},
                {'capacity': 64304, 'section': None, 'diameter': 2.5, 'min_spacing': 7.5},
                {'capacity': 112875, 'section': None, 'diameter': 2.5, 'min_spacing': 7.5},
                {'capacity': 92598, 'section': None, 'diameter': 3, 'min_spacing': 9},
                {'capacity': 162804, 'section': None, 'diameter': 3, 'min_spacing': 9},
                # High-capacity pile types for extreme loads (>150kN)
                {'capacity': 200000, 'section': None, 'diameter': 3.5, 'min_spacing': 10.5},
                {'capacity': 250000, 'section': None, 'diameter': 4.0, 'min_spacing': 12.0},
                {'capacity': 300000, 'section': None, 'diameter': 4.5, 'min_spacing': 13.5}
            ]
        }
        
        # Default parameters from GUI (agent_gui.py line 190)
        self.default_edge_distance = 0.4  # meters
        
        # Set output directory (agent_gui.py line 277)
        self.output_directory = str(Path(input_folder) / "pile_estimation_results")
        
        # Initialize storage for results
        self.file_paths = None
        self.excel_inputs = None
        self.excel_outputs = None
        self.estimation_results = None
        
        self.log_callback(f"🤖 Automated Pile Estimation Tester Initialized")
        self.log_callback(f"📁 Input Folder: {self.input_folder}")
        self.log_callback(f"📁 Output Directory: {self.output_directory}")
    
    def _default_log_callback(self, message: str):
        """Default logging function that prints to console with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def get_default_selected_pile_types(self) -> List[Dict[str, Any]]:
        """
        Get the default selected pile types for testing.
        Uses all available pile types to ensure comprehensive testing.
        
        Returns:
            List of pile type configurations for optimization
        """
        selected_types = []
        
        # Add DHP (default selection)
        selected_types.extend(self.default_pile_types['DHP'])
        
        # Add SHP (default selection)
        selected_types.extend(self.default_pile_types['SHP'])
        
        # Add ALL BP types for comprehensive testing (including high-capacity types)
        selected_types.extend(self.default_pile_types['BP'])
        
        # Format for optimization (add type field)
        formatted_types = []
        for pile_type in selected_types:
            if pile_type.get('diameter') is not None:
                # BP type
                formatted_type = {
                    'type': 'BP',
                    'capacity': pile_type['capacity'],
                    'section': pile_type['section'],
                    'diameter': pile_type['diameter'],
                    'min_spacing': pile_type['min_spacing']
                }
            else:
                # DHP or SHP type - determine from capacity
                if pile_type['capacity'] == 3663:
                    pile_type_name = 'DHP'
                else:
                    pile_type_name = 'SHP'
                
                formatted_type = {
                    'type': pile_type_name,
                    'capacity': pile_type['capacity'],
                    'section': pile_type['section'],
                    'diameter': pile_type['diameter'],
                    'min_spacing': pile_type['min_spacing']
                }
            
            formatted_types.append(formatted_type)
        
        return formatted_types
    
    def step_1_initialize(self) -> bool:
        """
        Execute Step 1: Initialize Setup
        
        This replicates the functionality of the "1. Initialize" button in the GUI
        (agent_gui.py initialize_setup_thread method, lines 402-433)
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        self.log_callback("=" * 60)
        self.log_callback("🚀 STEP 1: INITIALIZE SETUP")
        self.log_callback("=" * 60)
        
        try:
            # Validate input folder exists
            if not os.path.exists(self.input_folder):
                self.log_callback(f"❌ Error: Input folder does not exist: {self.input_folder}")
                return False
            
            self.log_callback(f"📂 Validating input folder: {self.input_folder}")
            
            # Call automated_initialize_setup (same as GUI)
            self.log_callback("🔄 Calling automated_initialize_setup...")
            
            self.file_paths, self.excel_inputs, self.excel_outputs = agent_main.automated_initialize_setup(
                input_folder=self.input_folder,
                existing_folder=None,  # No existing folder for basic test
                log_callback=self.log_callback
            )
            
            # Validate results (same validation as GUI lines 412-422)
            if self.file_paths and self.excel_inputs and self.excel_outputs:
                self.log_callback(" Excel file initialized successfully!")
                self.log_callback(f"📊 File paths object: {type(self.file_paths).__name__}")
                self.log_callback(f"📊 Excel inputs object: {type(self.excel_inputs).__name__}")
                self.log_callback(f"📊 Excel outputs object: {type(self.excel_outputs).__name__}")
                return True
            else:
                self.log_callback("❌ Excel file initialization failed - invalid results")
                return False
                
        except Exception as e:
            # STRICT ERROR HANDLING - NO FALLBACKS ALLOWED (same as GUI lines 424-430)
            error_msg = f"Failed to initialize file paths: {str(e)}"
            self.log_callback(f"❌ FAILED: {error_msg}")
            # Re-raise to ensure complete failure
            raise
    
    def step_2_run_estimation(self) -> bool:
        """
        Execute Step 2: Run Pile Estimation
        
        This replicates the functionality of the "2. Run AI Pile Type Pre-Selection + 
        NSGA-III Optimization" button in the GUI (agent_gui.py run_pile_estimation_with_visualization 
        method, lines 435-520)
        
        Returns:
            bool: True if estimation successful, False otherwise
        """
        self.log_callback("=" * 60)
        self.log_callback("🚀 STEP 2: RUN PILE ESTIMATION")
        self.log_callback("=" * 60)
        
        try:
            # Validate that initialization was completed
            if not self.excel_inputs:
                self.log_callback("❌ Error: Please run step_1_initialize() first")
                return False
            
            # Get selected pile types for optimization (same as GUI lines 451-455)
            selected_pile_types = self.get_default_selected_pile_types()
            
            if not selected_pile_types:
                self.log_callback("❌ Error: No pile types selected for optimization")
                return False
            
            self.log_callback(f" Selected {len(selected_pile_types)} pile types for optimization:")
            for pile_type in selected_pile_types:
                if pile_type.get('diameter'):
                    self.log_callback(f"   - {pile_type['type']}: Capacity={pile_type['capacity']}kN, "
                                   f"Diameter={pile_type['diameter']}m, Min Spacing={pile_type['min_spacing']}m")
                else:
                    self.log_callback(f"   - {pile_type['type']}: Capacity={pile_type['capacity']}kN, "
                                   f"Section={pile_type['section']}, Min Spacing={pile_type['min_spacing']}m")
            
            # Validate parameters (same as GUI lines 458-470)
            edge_dist = self.default_edge_distance
            output_dir = self.output_directory
            
            if edge_dist <= 0:
                self.log_callback("❌ Error: Edge distance must be greater than 0")
                return False
            
            if not output_dir:
                self.log_callback("❌ Error: Output directory not specified")
                return False
            
            # Test write permissions for output directory (same as GUI lines 472-495)
            try:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                
                # Test write permission
                test_file = output_path / "test_write_permission.tmp"
                test_file.write_text("test")
                test_file.unlink()
                
                self.log_callback(f" Output directory validated: {output_dir}")
                
            except PermissionError as e:
                error_msg = f"Permission denied for selected directory: {output_dir}"
                self.log_callback(f"❌ {error_msg}")
                raise PermissionError(error_msg) from e
            
            except Exception as e:
                error_msg = f"Cannot access output directory: {str(e)}"
                self.log_callback(f"❌ {error_msg}")
                return False
            
            # Run pile estimation (same as GUI lines 497-520)
            self.log_callback("🤖 Running AI pile layout optimization with AutoCAD DXF visualization...")
            self.log_callback("🔄 Using improved wall polyline buffering for accurate pile cap geometry...")
            self.log_callback("🔧 Using fixed grouping threshold for consistent debugging...")

            start_time = time.time()

            # Call the main estimation function with fixed grouping threshold for debugging
            self.estimation_results = agent_main.run_pile_estimation_with_multi_type_optimization(
                excel_inputs=self.excel_inputs,
                selected_pile_types=selected_pile_types,
                edge_dist=edge_dist,
                optimization_method="cost_efficiency",  # Default from GUI
                output_dir=output_dir,
                log_callback=self.log_callback,
                # Use automatic threshold optimization for comprehensive testing
                grouping_min_threshold=0.5,
                grouping_max_threshold=5.0,
                grouping_step=0.5
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Validate results
            if self.estimation_results and self.estimation_results.get('success'):
                self.log_callback(f" AI pile layout optimization completed successfully!")
                self.log_callback(f"⏱️ Execution time: {execution_time:.2f} seconds")
                
                # Log results summary
                self._log_results_summary()
                return True
            else:
                self.log_callback("❌ AI pile layout optimization failed: No valid results returned")
                return False
                
        except Exception as e:
            error_msg = f"AI pile layout optimization failed: {str(e)}"
            self.log_callback(f"❌ {error_msg}")
            # Re-raise to ensure complete failure (NO FALLBACK POLICY)
            raise
    
    def _log_results_summary(self):
        """Log a summary of the estimation results."""
        if not self.estimation_results:
            return
        
        summary = self.estimation_results.get('summary', {})
        optimization = self.estimation_results.get('optimization', {})
        
        self.log_callback("📊 RESULTS SUMMARY:")
        self.log_callback(f"   Groups processed: {summary.get('total_groups', 0)}")
        self.log_callback(f"   Total piles: {summary.get('total_piles', 0)}")
        self.log_callback(f"   Optimized layout: {optimization.get('optimized_layout', 'N/A')}")
        
        # Log file outputs
        if self.estimation_results.get('dxf_file'):
            self.log_callback(f"   Final DXF: {self.estimation_results['dxf_file']}")
        
        if self.estimation_results.get('consolidated_preselection_dxf_path'):
            self.log_callback(f"   Preselection DXF: {self.estimation_results['consolidated_preselection_dxf_path']}")
    
    def run_complete_test(self) -> bool:
        """
        Run the complete automated test sequence.
        
        Returns:
            bool: True if both steps completed successfully, False otherwise
        """
        self.log_callback(" STARTING COMPLETE AUTOMATED PILE ESTIMATION TEST")
        self.log_callback(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        overall_start_time = time.time()
        
        try:
            # Step 1: Initialize
            if not self.step_1_initialize():
                self.log_callback("❌ COMPLETE TEST FAILED: Step 1 initialization failed")
                return False
            
            # Step 2: Run Estimation
            if not self.step_2_run_estimation():
                self.log_callback("❌ COMPLETE TEST FAILED: Step 2 estimation failed")
                return False
            
            # Success
            overall_end_time = time.time()
            total_time = overall_end_time - overall_start_time
            
            self.log_callback("=" * 60)
            self.log_callback("🎉 COMPLETE AUTOMATED TEST SUCCESSFUL!")
            self.log_callback(f"⏱️ Total execution time: {total_time:.2f} seconds")
            self.log_callback(f"📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_callback("=" * 60)
            
            return True
            
        except Exception as e:
            self.log_callback(f"❌ COMPLETE TEST FAILED WITH EXCEPTION: {str(e)}")
            return False


def run_automated_pile_estimation_test(input_folder: str = None, log_callback: Optional[callable] = None) -> bool:
    """
    Convenience function to run the automated pile estimation test.
    
    Args:
        input_folder: Path to input folder. If None, uses the specified test folder.
        log_callback: Optional callback function for logging messages
        
    Returns:
        bool: True if test completed successfully, False otherwise
    """
    # Use the specified test folder if no input folder provided
    if input_folder is None:
        # Use the existing test data directory
        input_folder = r"C:\Users\<USER>\Downloads\AI_Test TCTL"
    
    # Create and run the tester
    tester = AutomatedPileEstimationTester(input_folder, log_callback)
    return tester.run_complete_test()


if __name__ == "__main__":
    """
    Main entry point for running the automated test.
    """
    # Run the automated test with the specified folder
    success = run_automated_pile_estimation_test()
    
    if success:
        print("\n🎉 Automated pile estimation test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Automated pile estimation test failed!")
        sys.exit(1)
