"""Convert structural element properties from Excel to SAFE16 format.

Processes beam, column, and slab properties with geometric dimensions and material
assignments for SAFE16 reinforced concrete design and analysis."""

import pandas as pd


def _write_beam_rectangular_section_safe16(excel_inputs, safe16_dfs):
    """Convert rectangular beam cross-section properties from Excel to SAFE16 format."""
    df_beam_prop = excel_inputs.BeamProp.copy()

    # Set top and bottom widths equal to the beam width for rectangular sections
    # This creates a uniform rectangular cross-section typical for concrete beams
    df_beam_prop['WidthTop (m)'] = df_beam_prop['Width (m)']
    df_beam_prop['WidthBot (m)'] = df_beam_prop['Width (m)']

    # Extract relevant columns for rectangular section properties
    # These are the essential geometric parameters for SAFE16 beam design
    df_beam_prop = df_beam_prop[['Beam Prop',      # Beam property identifier
                                 'Material',       # Material assignment
                                 'Depth (m)',      # Beam depth (vertical dimension)
                                 'WidthTop (m)',   # Top width (= Width for rectangular)
                                 'WidthBot (m)']].copy()  # Bottom width (= Width for rectangular)

    if not df_beam_prop.empty:
        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns = safe16_dfs.BeamProperties02Rectangle.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_to_append = pd.DataFrame(
            df_beam_prop.values,
            columns=pd.MultiIndex.from_tuples(target_columns)
        )

        # Append rectangular beam properties to existing SAFE16 data
        safe16_dfs.BeamProperties02Rectangle = pd.concat(
            [safe16_dfs.BeamProperties02Rectangle, df_to_append],
            ignore_index=True
        )

    return safe16_dfs


def _write_column_circular_section_safe16(excel_inputs, safe16_dfs):
    """Convert circular column cross-section properties from Excel to SAFE16 format."""
    df_bp_prop = excel_inputs.BPProp.copy()

    # Set default SAFE16 options for circular columns
    # AutoDrop disabled - no automatic drop panel generation
    df_bp_prop['AutoDrop (Yes/No)'] = 'No'
    # IncludeCap disabled - pile cap not included in analysis
    df_bp_prop['IncludeCap (Yes/No)'] = 'No'

    # Extract relevant columns for circular column properties
    # These parameters define the circular column geometry and behavior
    df_bp_prop = df_bp_prop[['Column',            # Column property identifier
                             'Material',          # Material assignment
                             'Diameter (m)',      # Column diameter (primary dimension)
                             'AutoRigid (Yes/No)', # Auto-rigid zone at slab interface
                             'AutoDrop (Yes/No)',  # Auto drop panel (disabled)
                             'IncludeCap (Yes/No)']].copy()  # Include pile cap (disabled)

    # Convert to SAFE16 format with proper MultiIndex column structure
    target_columns = safe16_dfs.ColumnProperties03Circular.columns.tolist()
    df_append_16 = pd.DataFrame(
        df_bp_prop.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append circular column properties to existing SAFE16 data
    safe16_dfs.ColumnProperties03Circular = pd.concat(
        [safe16_dfs.ColumnProperties03Circular, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_column_h_section_safe16(excel_inputs, safe16_dfs):
    """Process H-section (steel) column properties for SAFE16.
    
    Converts H-section column property definitions from Excel input to SAFE16 format.
    H-sections (also called I-beams or wide-flange sections) are common structural steel
    columns used in steel and composite construction. This function processes both
    geometric dimensions and calculated section properties.
    """
    df_hp_prop = excel_inputs.HPProp.copy()
    
    # Set default SAFE16 options for H-section columns
    # AutoDrop disabled - not applicable for steel sections
    df_hp_prop['AutoDrop (Yes/No)'] = 'No'

    # Extract relevant columns for H-section column properties
    # Includes both geometric dimensions and calculated section properties
    df_16 = df_hp_prop[
        ['Column',          # Column property identifier
         'Material',        # Material assignment (steel)
         'SecDim2 (m)',     # Section width (flange width)
         'SecDim3 (m)',     # Section depth (web depth)
         'AutoRigid (Yes/No)', # Auto-rigid zone flag
         'AutoDrop (Yes/No)',  # Auto drop panel (disabled)
         'Area (m2)',       # Cross-sectional area
         'As2 (m2)',        # Shear area in local 2-direction
         'As3 (m2)',        # Shear area in local 3-direction
         'J (m4)',          # Torsional constant
         'I22 (m4)',        # Moment of inertia about local 2-axis
         'I33 (m4)']        # Moment of inertia about local 3-axis
    ].copy()

    # Convert to SAFE16 format with proper MultiIndex column structure
    target_columns = safe16_dfs.ColumnProps06GeneralShape.columns.tolist()
    df_append_16 = pd.DataFrame(
        df_16.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append H-section column properties to existing SAFE16 data
    safe16_dfs.ColumnProps06GeneralShape = pd.concat(
        [safe16_dfs.ColumnProps06GeneralShape, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_slab_general_props_safe16(excel_inputs, safe16_dfs):
    """Process general slab properties for SAFE16.
    
    Converts general slab property definitions from Excel input to SAFE16 format.
    This function handles the basic identification and display properties for slab
    elements, setting up the foundation for detailed slab property definitions.
    """
    df_slab_prop = excel_inputs.SlabProp.copy()

    # Set default SAFE16 properties for slab elements
    df_slab_prop['Type'] = 'Slab'      # Element type classification
    df_slab_prop['Color'] = 'Yellow'   # Display color in SAFE16 interface

    # Extract relevant columns for general slab properties
    df_slab_prop = df_slab_prop[['Slab Prop',  # Slab property identifier
                                 'Type',       # Element type (Slab)
                                 'Color']].copy()  # Display color (Yellow)
                                 
    if not df_slab_prop.empty:
        # Convert to SAFE16 format with proper MultiIndex column structure
        target_columns = safe16_dfs.SlabProperties01General.columns.tolist()
        df_append_16 = pd.DataFrame(
            df_slab_prop.values, columns=pd.MultiIndex.from_tuples(target_columns)
        )
        
        # Append general slab properties to existing SAFE16 data
        safe16_dfs.SlabProperties01General = pd.concat(
            [safe16_dfs.SlabProperties01General, df_append_16], ignore_index=True
        )

    return safe16_dfs


def _write_slab_solid_props_safe16(excel_inputs, safe16_dfs):
    """Process solid slab properties for SAFE16.
    
    Converts solid slab property definitions from Excel input to SAFE16 format.
    Solid slabs are the most common concrete slab type, characterized by uniform
    thickness and material properties. This function processes the essential
    geometric and material properties for solid slab design.
    """
    df_slab_prop = excel_inputs.SlabProp.copy()

    # Set default SAFE16 properties for solid slab elements
    df_slab_prop['Type'] = 'Slab'           # Slab element type
    df_slab_prop['Ortho (Yes/No)'] = 'No'   # Isotropic material behavior (standard)

    # Extract relevant columns for solid slab properties
    df_slab_prop = df_slab_prop[['Slab Prop',        # Slab property identifier
                                 'Type',             # Element type (Slab)
                                 'Material',         # Material assignment
                                 'Thickness (m)',    # Slab thickness (primary dimension)
                                 'Ortho (Yes/No)']].copy()  # Orthotropic flag (No)
                                 
    if not df_slab_prop.empty:
        # Convert to SAFE16 format with proper MultiIndex column structure
        target_columns = safe16_dfs.SlabProp02SolidSlabs.columns.tolist()
        df_append_16 = pd.DataFrame(
            df_slab_prop.values, columns=pd.MultiIndex.from_tuples(target_columns)
        )
        
        # Append solid slab properties to existing SAFE16 data
        safe16_dfs.SlabProp02SolidSlabs = pd.concat(
            [safe16_dfs.SlabProp02SolidSlabs, df_append_16],
            ignore_index=True
        )

    return safe16_dfs


def _write_slab_prop_safe16(excel_inputs, safe16_dfs):
    """Coordinate complete slab property processing for SAFE16.
    
    Main coordination function that processes all slab property types for SAFE16.
    This function orchestrates the conversion of slab properties in the correct
    sequence, ensuring that general properties are established before detailed
    solid slab properties are processed.
    
    The function handles the complete slab property workflow:
    1. Process general slab properties (identification and display)
    2. Process solid slab properties (thickness and material)
    """
    # Process general slab properties first (foundation for other properties)
    safe16_dfs = _write_slab_general_props_safe16(excel_inputs, safe16_dfs)

    # Process solid slab properties (thickness and material definitions)
    safe16_dfs = _write_slab_solid_props_safe16(excel_inputs, safe16_dfs)

    return safe16_dfs


def _write_beam_general_properties_safe16(excel_inputs, safe16_dfs):
    """Process general beam properties for SAFE16.
    
    Converts general beam property definitions from Excel input to SAFE16 format.
    This function handles the basic identification and display properties for beam
    elements, establishing the foundation for detailed beam cross-section definitions.
    """
    df_beam_prop = excel_inputs.BeamProp.copy()

    # Set default SAFE16 properties for beam elements
    df_beam_prop['Type'] = 'Rectangular'  # Standard concrete beam cross-section type
    df_beam_prop['Color'] = 'Red'         # Display color in SAFE16 interface

    # Extract relevant columns for general beam properties
    general_props = df_beam_prop[['Beam Prop',  # Beam property identifier
                                  'Type',       # Cross-section type (Rectangular)
                                  'Color']].copy()  # Display color (Red)

    if not general_props.empty:
        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns = safe16_dfs.BeamProperties01General.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_to_append = pd.DataFrame(
            general_props.values,
            columns=pd.MultiIndex.from_tuples(target_columns)
        )

        # Append general beam properties to existing SAFE16 data
        safe16_dfs.BeamProperties01General = pd.concat(
            [safe16_dfs.BeamProperties01General, df_to_append],
            ignore_index=True
        )
    return safe16_dfs
