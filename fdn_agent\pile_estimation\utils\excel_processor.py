"""
Enhanced Excel Processing with Advanced Data Validation

This module provides enhanced Excel file processing capabilities:
- Robust data validation and sanitization
- Advanced error handling and recovery
- Performance-optimized reading and writing
- Data type inference and conversion
- Comprehensive logging and monitoring

Author: Foundation Automation System
Date: June 18, 2025
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass
import warnings
import time

# Enhanced logging system imports
try:
    from .logging_utils import (
        enhanced_log,
        log_function_entry,
        log_function_exit,
        log_validation_result,
        log_calculation_result,
        log_performance_metric,
        log_error_with_context,
        create_timed_logger
    )
    ENHANCED_LOGGING_AVAILABLE = True
except ImportError:
    ENHANCED_LOGGING_AVAILABLE = False
    # Fallback logging functions
    def enhanced_log(log_callback, message, level='INFO'): 
        if log_callback: log_callback(f"[{level}] {message}")
    def log_function_entry(log_callback, func_name, **kwargs): 
        if log_callback: log_callback(f"[DEBUG] Entering {func_name}")
    def log_function_exit(log_callback, func_name, **kwargs): 
        if log_callback: log_callback(f"[DEBUG] Exiting {func_name}")
    def log_validation_result(log_callback, name, passed, details): 
        if log_callback: log_callback(f"[{'INFO' if passed else 'WARNING'}] {name}: {'PASSED' if passed else 'FAILED'} - {details}")
    def log_calculation_result(log_callback, name, result, unit): 
        if log_callback: log_callback(f"[INFO] {name}: {result} {unit}")
    def log_performance_metric(log_callback, name, value, unit): 
        if log_callback: log_callback(f"[DEBUG] {name}: {value} {unit}")
    def log_error_with_context(log_callback, error, context): 
        if log_callback: log_callback(f"[ERROR] {context}: {error}")
    class create_timed_logger:
        def __init__(self, log_callback, operation_name):
            self.log_callback = log_callback
            self.operation_name = operation_name
            self.start_time = None
        def __enter__(self):
            self.start_time = time.time()
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.start_time and self.log_callback:
                duration = time.time() - self.start_time
                self.log_callback(f"[DEBUG] {self.operation_name} completed in {duration:.3f}s")

@dataclass
class ExcelProcessingConfig:
    """Configuration for enhanced Excel processing."""
    
    # Data Validation
    strict_validation: bool = True
    auto_fix_data_types: bool = True
    handle_missing_values: bool = True
    missing_value_strategy: str = "interpolate"  # "drop", "fill_zero", "interpolate", "forward_fill"
    
    # Performance Optimization
    chunk_size: int = 10000
    use_chunked_reading: bool = False
    optimize_memory: bool = True
    
    # Error Handling
    skip_invalid_sheets: bool = True
    continue_on_error: bool = True
    max_error_count: int = 10
    
    # Data Type Inference
    infer_datetime: bool = True
    infer_numeric: bool = True
    decimal_precision: int = 6
    
    # Validation Rules
    required_columns: Dict[str, List[str]] = None
    column_data_types: Dict[str, Dict[str, str]] = None
    value_ranges: Dict[str, Dict[str, Tuple[float, float]]] = None
    
    def __post_init__(self):
        """Initialize default validation rules if not provided."""
        if self.required_columns is None:
            self.required_columns = {
                'Point': ['Point', 'X (m)', 'Y (m)'],
                'Column': ['Column', 'Point'],
                'Wall': ['Wall', 'Points'],
                'LoadPat': ['LoadPat (Text)', 'Load Type']
            }
        
        if self.column_data_types is None:
            self.column_data_types = {
                'Point': {'X (m)': 'float64', 'Y (m)': 'float64', 'Z (m)': 'float64'},
                'LoadPat': {'LoadPat (Text)': 'str', 'Load Type': 'str'}
            }

class EnhancedExcelProcessor:
    """
    Enhanced Excel processor with advanced data validation and error handling.
    
    Features:
    - Robust data validation with automatic error correction
    - Performance-optimized reading with chunked processing
    - Comprehensive data type inference and conversion
    - Advanced missing value handling strategies
    - Detailed logging and error reporting
    """
    
    def __init__(self, config: ExcelProcessingConfig, log_callback: Optional[Callable] = None):
        """Initialize the enhanced Excel processor."""
        log_function_entry(log_callback, "EnhancedExcelProcessor.__init__")
        
        self.config = config
        self.log_callback = log_callback
        
        # Processing statistics
        self.processing_stats = {
            'files_processed': 0,
            'sheets_processed': 0,
            'rows_processed': 0,
            'errors_encountered': 0,
            'data_fixes_applied': 0
        }
        
        log_validation_result(log_callback, "excel_processor_initialization", True,
                            f"Strict validation: {config.strict_validation}")
        
        log_function_exit(log_callback, "EnhancedExcelProcessor.__init__")
    
    def read_excel_file(self, file_path: Union[str, Path], 
                       sheet_names: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """
        Read Excel file with enhanced validation and error handling.
        
        Args:
            file_path: Path to Excel file
            sheet_names: Optional list of specific sheets to read
            
        Returns:
            Dictionary mapping sheet names to DataFrames
        """
        log_function_entry(self.log_callback, "read_excel_file", 
                          file_path=str(file_path))
        
        with create_timed_logger(self.log_callback, "excel_file_reading") as timer:
            try:
                file_path = Path(file_path)
                
                # Validate file existence
                if not file_path.exists():
                    raise FileNotFoundError(f"Excel file not found: {file_path}")
                
                if not file_path.suffix.lower() in ['.xlsx', '.xls']:
                    raise ValueError(f"Invalid Excel file format: {file_path.suffix}")
                
                log_validation_result(self.log_callback, "file_validation", True,
                                    f"File exists and has valid format")
                
                # Read Excel file
                try:
                    if sheet_names:
                        excel_data = pd.read_excel(file_path, sheet_name=sheet_names, engine='openpyxl')
                    else:
                        excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
                except Exception as e:
                    enhanced_log(self.log_callback, f"Failed to read with openpyxl, trying xlrd: {e}", 'WARNING')
                    try:
                        if sheet_names:
                            excel_data = pd.read_excel(file_path, sheet_name=sheet_names, engine='xlrd')
                        else:
                            excel_data = pd.read_excel(file_path, sheet_name=None, engine='xlrd')
                    except Exception as e2:
                        raise Exception(f"Failed to read Excel file with both engines: openpyxl={e}, xlrd={e2}")
                
                # Ensure excel_data is a dictionary
                if isinstance(excel_data, pd.DataFrame):
                    excel_data = {'Sheet1': excel_data}
                
                log_calculation_result(self.log_callback, "sheets_read", 
                                     len(excel_data), "sheets")
                
                # Process each sheet
                processed_data = {}
                for sheet_name, df in excel_data.items():
                    try:
                        processed_df = self._process_sheet(sheet_name, df)
                        processed_data[sheet_name] = processed_df
                        self.processing_stats['sheets_processed'] += 1
                        self.processing_stats['rows_processed'] += len(processed_df)
                    except Exception as e:
                        self.processing_stats['errors_encountered'] += 1
                        if self.config.skip_invalid_sheets:
                            enhanced_log(self.log_callback, f"Skipping invalid sheet '{sheet_name}': {e}", 'WARNING')
                            continue
                        else:
                            raise
                
                self.processing_stats['files_processed'] += 1
                
                log_performance_metric(self.log_callback, "processing_rate",
                                     self.processing_stats['rows_processed'] / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                     "rows/s")
                
                log_function_exit(self.log_callback, "read_excel_file",
                                result=f"Processed {len(processed_data)} sheets")
                
                return processed_data
                
            except Exception as e:
                log_error_with_context(self.log_callback, e, "read_excel_file")
                raise
    
    def _process_sheet(self, sheet_name: str, df: pd.DataFrame) -> pd.DataFrame:
        """Process individual sheet with validation and data cleaning."""
        log_function_entry(self.log_callback, "_process_sheet",
                          sheet_name=sheet_name, shape=df.shape)
        
        try:
            # Make a copy to avoid modifying original
            processed_df = df.copy()
            
            # Remove completely empty rows and columns
            processed_df = processed_df.dropna(how='all').dropna(axis=1, how='all')
            
            if processed_df.empty:
                enhanced_log(self.log_callback, f"Sheet '{sheet_name}' is empty after cleaning", 'WARNING')
                return processed_df
            
            # Validate required columns
            if sheet_name in self.config.required_columns:
                missing_cols = self._validate_required_columns(sheet_name, processed_df)
                if missing_cols and self.config.strict_validation:
                    raise ValueError(f"Missing required columns in '{sheet_name}': {missing_cols}")
            
            # Apply data type conversions
            if sheet_name in self.config.column_data_types:
                processed_df = self._apply_data_types(sheet_name, processed_df)
            
            # Handle missing values
            if self.config.handle_missing_values:
                processed_df = self._handle_missing_values(processed_df)
            
            # Validate data ranges
            if sheet_name in self.config.value_ranges:
                processed_df = self._validate_value_ranges(sheet_name, processed_df)
            
            log_calculation_result(self.log_callback, f"sheet_{sheet_name}_processed",
                                 f"Shape: {processed_df.shape}", "")
            
            log_function_exit(self.log_callback, "_process_sheet")
            
            return processed_df
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, f"_process_sheet({sheet_name})")
            raise
    
    def _validate_required_columns(self, sheet_name: str, df: pd.DataFrame) -> List[str]:
        """Validate that required columns are present."""
        required_cols = self.config.required_columns.get(sheet_name, [])
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            enhanced_log(self.log_callback, 
                        f"Missing required columns in '{sheet_name}': {missing_cols}", 'WARNING')
        else:
            log_validation_result(self.log_callback, f"{sheet_name}_required_columns", True,
                                f"All {len(required_cols)} required columns present")
        
        return missing_cols
    
    def _apply_data_types(self, sheet_name: str, df: pd.DataFrame) -> pd.DataFrame:
        """Apply specified data type conversions."""
        type_specs = self.config.column_data_types.get(sheet_name, {})
        conversions_applied = 0
        
        for col, target_type in type_specs.items():
            if col in df.columns:
                try:
                    if target_type == 'float64':
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    elif target_type == 'int64':
                        df[col] = pd.to_numeric(df[col], errors='coerce').astype('Int64')
                    elif target_type == 'str':
                        df[col] = df[col].astype(str)
                    elif target_type == 'datetime':
                        df[col] = pd.to_datetime(df[col], errors='coerce')
                    
                    conversions_applied += 1
                    
                except Exception as e:
                    enhanced_log(self.log_callback, 
                                f"Failed to convert column '{col}' to {target_type}: {e}", 'WARNING')
        
        if conversions_applied > 0:
            log_calculation_result(self.log_callback, f"{sheet_name}_type_conversions",
                                 conversions_applied, "columns")
            self.processing_stats['data_fixes_applied'] += conversions_applied
        
        return df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values according to configured strategy."""
        if df.isnull().sum().sum() == 0:
            return df  # No missing values
        
        strategy = self.config.missing_value_strategy
        fixes_applied = 0
        
        if strategy == "drop":
            original_len = len(df)
            df = df.dropna()
            fixes_applied = original_len - len(df)
            
        elif strategy == "fill_zero":
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            df[numeric_cols] = df[numeric_cols].fillna(0)
            fixes_applied = len(numeric_cols)
            
        elif strategy == "interpolate":
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if df[col].isnull().any():
                    df[col] = df[col].interpolate()
                    fixes_applied += 1
            
        elif strategy == "forward_fill":
            df = df.fillna(method='ffill')
            fixes_applied = 1
        
        if fixes_applied > 0:
            log_calculation_result(self.log_callback, "missing_value_handling",
                                 fixes_applied, "fixes")
            self.processing_stats['data_fixes_applied'] += fixes_applied
        
        return df
    
    def _validate_value_ranges(self, sheet_name: str, df: pd.DataFrame) -> pd.DataFrame:
        """Validate that values are within specified ranges."""
        range_specs = self.config.value_ranges.get(sheet_name, {})
        violations_found = 0
        
        for col, (min_val, max_val) in range_specs.items():
            if col in df.columns and df[col].dtype in [np.float64, np.int64]:
                violations = (df[col] < min_val) | (df[col] > max_val)
                violation_count = violations.sum()
                
                if violation_count > 0:
                    violations_found += violation_count
                    enhanced_log(self.log_callback,
                                f"Range violations in '{col}': {violation_count} values outside [{min_val}, {max_val}]",
                                'WARNING')
                    
                    if self.config.auto_fix_data_types:
                        # Clip values to valid range
                        df[col] = df[col].clip(min_val, max_val)
                        self.processing_stats['data_fixes_applied'] += violation_count
        
        if violations_found == 0:
            log_validation_result(self.log_callback, f"{sheet_name}_value_ranges", True,
                                "All values within specified ranges")
        
        return df
    
    def write_excel_file(self, data: Dict[str, pd.DataFrame], file_path: Union[str, Path],
                        include_index: bool = False) -> None:
        """
        Write data to Excel file with enhanced formatting and validation.
        
        Args:
            data: Dictionary mapping sheet names to DataFrames
            file_path: Output file path
            include_index: Whether to include DataFrame index in output
        """
        log_function_entry(self.log_callback, "write_excel_file",
                          file_path=str(file_path), sheet_count=len(data))
        
        with create_timed_logger(self.log_callback, "excel_file_writing") as timer:
            try:
                file_path = Path(file_path)
                
                # Ensure output directory exists
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Write to Excel file
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    sheets_written = 0
                    total_rows = 0
                    
                    for sheet_name, df in data.items():
                        try:
                            df.to_excel(writer, sheet_name=sheet_name, index=include_index)
                            sheets_written += 1
                            total_rows += len(df)
                            
                            enhanced_log(self.log_callback,
                                        f"Written sheet '{sheet_name}': {len(df)} rows, {len(df.columns)} columns",
                                        'DEBUG')
                        
                        except Exception as e:
                            enhanced_log(self.log_callback,
                                        f"Failed to write sheet '{sheet_name}': {e}", 'ERROR')
                            if not self.config.continue_on_error:
                                raise
                
                log_calculation_result(self.log_callback, "excel_file_written",
                                     f"{sheets_written} sheets, {total_rows} rows", "")
                
                log_performance_metric(self.log_callback, "writing_rate",
                                     total_rows / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                     "rows/s")
                
                log_function_exit(self.log_callback, "write_excel_file",
                                result=f"File written: {file_path}")
                
            except Exception as e:
                log_error_with_context(self.log_callback, e, "write_excel_file")
                raise
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics."""
        return {
            **self.processing_stats,
            'error_rate': self.processing_stats['errors_encountered'] / max(1, self.processing_stats['sheets_processed']),
            'fix_rate': self.processing_stats['data_fixes_applied'] / max(1, self.processing_stats['rows_processed'])
        }

def create_enhanced_excel_processor(config: Optional[ExcelProcessingConfig] = None,
                                   log_callback: Optional[Callable] = None) -> EnhancedExcelProcessor:
    """
    Factory function to create an enhanced Excel processor.
    
    Args:
        config: Optional configuration (uses default if None)
        log_callback: Optional logging callback
        
    Returns:
        Configured enhanced Excel processor
    """
    log_function_entry(log_callback, "create_enhanced_excel_processor")
    
    if config is None:
        config = ExcelProcessingConfig()
        enhanced_log(log_callback, "Using default Excel processing configuration", 'INFO')
    
    processor = EnhancedExcelProcessor(config, log_callback)
    
    log_function_exit(log_callback, "create_enhanced_excel_processor",
                    result="Enhanced Excel processor created")
    
    return processor
