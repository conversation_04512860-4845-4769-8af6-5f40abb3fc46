"""
Console Interface Module for ETABS CWLS

This module provides the console-based user interface for the ETABS Core Wall
Loading Schedule system. It handles user input, menu display, and workflow
coordination for the deprecated console interface.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional user interaction patterns

Key Functions:
    main: Console-based main entry point (deprecated - use GUI)
    get_etabs_version_input: User input validation for ETABS version
    display_menu_and_get_selection: Menu display and selection validation

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import sys
from typing import Optional, Callable

from etabs_cwls.core import _class
from etabs_cwls.config.exceptions import DataValidationError, ConfigurationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_error_with_context, create_timed_logger, get_logger
)
from etabs_cwls.config.configuration_manager import assign_ETABS_converter
from etabs_cwls.io.file_path_manager import filepath_selection
from etabs_cwls.processing.initialization_processor import initialization
from etabs_cwls.processing.pier_force_processor import create_schedule_pier_force
from etabs_cwls.processing.joint_reaction_processor import create_schedule_joint_reaction


def main(log_callback: Optional[Callable] = None) -> None:
    """
    Console-based main entry point for ETABS CWLS processing.
    
    Note: This function is deprecated in favor of the GUI interface.
    It provides a console-based workflow for backward compatibility but
    lacks the enhanced user experience and error handling of the GUI.
    
    Args:
        log_callback: Optional logging callback function
        
    Raises:
        ConfigurationError: If system configuration fails
        DataValidationError: If user input validation fails
        
    Example:
        >>> from etabs_cwls.console_interface import main
        >>> main()  # Launches console interface (deprecated)
    """
    log_function_entry(log_callback, "main")
    
    # Issue deprecation warning
    enhanced_log(log_callback, 
                "Warning: Console interface is deprecated. Please use the GUI interface (etabs_cwls.main())", 
                'WARNING')
    
    try:
        # Initialize enhanced logging
        logger = get_logger()
        if log_callback is None:
            log_callback = lambda msg, level='INFO': getattr(logger, level.lower())(msg)
        
        enhanced_log(log_callback, "Starting ETABS CWLS console interface", 'INFO')
        
        # Initialize data objects with enhanced logging
        file_path = _class.file_path(log_callback=log_callback)
        design_data = _class.design_data(log_callback=log_callback)
        ETABS_converter = _class.ETABS_converter(log_callback=log_callback)
        
        # Display application header
        _display_application_header(log_callback)
        
        program = 10
        
        with create_timed_logger(log_callback, "console_session") as session_timer:
            while program != 0:
                try:
                    # ETABS version selection with validation
                    if design_data.etabsversion not in [1, 2]:
                        design_data.etabsversion = get_etabs_version_input(log_callback)

                    # Configure ETABS converter
                    ETABS_converter = assign_ETABS_converter(ETABS_converter, design_data, log_callback)

                    # Display menu and get user selection
                    program = display_menu_and_get_selection(design_data.etabsversion, log_callback)
                    
                    # Process user selection
                    if program == 1:
                        enhanced_log(log_callback, "User selected: Initialize design excel", 'INFO')
                        file_path = filepath_selection(file_path, program, ETABS_converter, log_callback)
                        file_path, design_data = initialization(file_path, design_data, ETABS_converter, log_callback)
                        print("\n✓ Design initialization completed successfully")
                        
                    elif program == 2:
                        enhanced_log(log_callback, "User selected: Create pier force schedule", 'INFO')
                        file_path = filepath_selection(file_path, program, ETABS_converter, log_callback)
                        design_data = create_schedule_pier_force(file_path, design_data, ETABS_converter, log_callback)
                        print("\n✓ Pier force schedule creation completed successfully")
                        
                    elif program == 3 and design_data.etabsversion != 2:
                        enhanced_log(log_callback, "User selected: Create joint reaction schedule", 'INFO')
                        file_path = filepath_selection(file_path, program, ETABS_converter, log_callback)
                        design_data = create_schedule_joint_reaction(file_path, design_data, log_callback)
                        print("\n✓ Joint reaction schedule creation completed successfully")
                        
                    elif program == 0:
                        enhanced_log(log_callback, "User selected: Exit program", 'INFO')
                        print("\nThank you for using ETABS CWLS!")
                        break
                        
                    else:
                        raise DataValidationError(
                            f"Invalid menu selection: {program}",
                            field_name="program_selection",
                            invalid_value=program,
                            error_code="INVALID_MENU_SELECTION"
                        )
                
                except KeyboardInterrupt:
                    enhanced_log(log_callback, "User interrupted program execution", 'INFO')
                    print("\n\nProgram interrupted by user. Exiting...")
                    break
                    
                except (DataValidationError, ConfigurationError) as e:
                    enhanced_log(log_callback, f"User input error: {e}", 'ERROR')
                    print(f"\n❌ Error: {e}")
                    print("Please try again with valid input.")
                    
                except Exception as e:
                    log_error_with_context(log_callback, e, "main_console_loop")
                    print(f"\n❌ Unexpected error: {e}")
                    print("Please check the log file for details.")
                    break
        
        log_validation_result(log_callback, "console_session", True, "Console session completed successfully")
        log_function_exit(log_callback, "main", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "main")
        log_validation_result(log_callback, "console_session", False, str(e))
        print(f"\n❌ Fatal error: {e}")
        print("Please check the log file for details.")
        raise


def _display_application_header(log_callback: Optional[Callable] = None) -> None:
    """Display the application header with version and copyright information."""
    print("=" * 60)
    print("Foundation Automation - ETABS Core Wall Loading Schedule")
    print("Version 5.6.9 - Enhanced Professional Edition")
    print("Design for ETABS 2016/2017/2021")
    print("Copyright © 2023-2025 Foundation Automation. All rights reserved.")
    print("=" * 60)
    print("\nNote: GUI interface recommended for better user experience")
    print("=" * 60)
    
    enhanced_log(log_callback, "Application header displayed", 'DEBUG')


def get_etabs_version_input(log_callback: Optional[Callable] = None) -> int:
    """
    Get ETABS version input from user with validation.
    
    Args:
        log_callback: Optional logging callback function
        
    Returns:
        Validated ETABS version (1 or 2)
        
    Raises:
        DataValidationError: If input validation fails
    """
    log_function_entry(log_callback, "get_etabs_version_input")
    
    try:
        print("\n" + "=" * 40)
        print("ETABS Version Selection")
        print("=" * 40)
        print("1 - ETABS 2016/2017")
        print("2 - ETABS 2021")
        print("=" * 40)
        
        while True:
            try:
                user_input = input("Select ETABS VERSION (1 or 2): ").strip()
                enhanced_log(log_callback, f"User input for ETABS version: '{user_input}'", 'DEBUG')
                
                if not user_input:
                    print("❌ Please enter a valid selection (1 or 2)")
                    continue
                
                version = int(user_input)
                
                if version not in [1, 2]:
                    print("❌ Invalid selection. Please enter 1 for ETABS 2016/2017 or 2 for ETABS 2021")
                    continue
                
                version_name = "ETABS 2016/2017" if version == 1 else "ETABS 2021"
                enhanced_log(log_callback, f"ETABS version selected: {version_name}", 'INFO')
                log_function_exit(log_callback, "get_etabs_version_input", version)
                return version
                
            except ValueError:
                print("❌ Invalid input. Please enter a number (1 or 2)")
                continue
            except KeyboardInterrupt:
                raise
            except Exception as e:
                log_error_with_context(log_callback, e, "get_etabs_version_input")
                print("❌ Error processing input. Please try again.")
                continue
                
    except Exception as e:
        log_error_with_context(log_callback, e, "get_etabs_version_input")
        raise


def display_menu_and_get_selection(etabs_version: int, log_callback: Optional[Callable] = None) -> int:
    """
    Display program menu and get user selection.
    
    Args:
        etabs_version: ETABS version (1 or 2)
        log_callback: Optional logging callback function
        
    Returns:
        User's menu selection
        
    Raises:
        DataValidationError: If input validation fails
    """
    log_function_entry(log_callback, "display_menu_and_get_selection", etabs_version=etabs_version)
    
    try:
        print("\n" + "=" * 50)
        print("Program Menu")
        print("=" * 50)
        print("1 - Initialize design excel")
        print("2 - Create table based on pier force")
        
        if etabs_version != 2:
            print("3 - Create table based on point reaction")
        
        print("0 - Exit Program")
        print("=" * 50)
        
        while True:
            try:
                user_input = input("Select function (0-3): ").strip()
                enhanced_log(log_callback, f"User menu selection: '{user_input}'", 'DEBUG')
                
                if not user_input:
                    print("❌ Please enter a valid selection")
                    continue
                
                selection = int(user_input)
                
                # Validate selection based on ETABS version
                valid_selections = [0, 1, 2]
                if etabs_version != 2:
                    valid_selections.append(3)
                
                if selection not in valid_selections:
                    if etabs_version == 2 and selection == 3:
                        print("❌ Point reaction option not available for ETABS 2021")
                    else:
                        print(f"❌ Invalid selection. Please enter a number from {valid_selections}")
                    continue
                
                log_function_exit(log_callback, "display_menu_and_get_selection", selection)
                return selection
                
            except ValueError:
                print("❌ Invalid input. Please enter a number")
                continue
            except KeyboardInterrupt:
                raise
            except Exception as e:
                log_error_with_context(log_callback, e, "display_menu_and_get_selection")
                print("❌ Error processing input. Please try again.")
                continue
                
    except Exception as e:
        log_error_with_context(log_callback, e, "display_menu_and_get_selection")
        raise


# Export all functions
__all__ = [
    'main',
    'get_etabs_version_input',
    'display_menu_and_get_selection'
]
