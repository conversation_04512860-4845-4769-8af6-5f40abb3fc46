"""Foundation Design Automation - Main Entry Point"""

import logging
import tkinter as tk
import warnings
import sys

import pandas as pd

from app_controller import ApplicationController

# Check if debug mode is requested
DEBUG_MODE = '--debug' in sys.argv or '-d' in sys.argv

# Configure logging and warnings
logging.basicConfig(
    level=logging.DEBUG if DEBUG_MODE else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]  # Only console output, no file logging
)

# Suppress specific warnings
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# Configure pandas
pd.options.mode.chained_assignment = None


def main():
    """Main application entry point."""
    root = tk.Tk()
    
    try:
        # Create and run application
        app = ApplicationController(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"Critical error in main application: {e}")
        raise


if __name__ == '__main__':
    main()
