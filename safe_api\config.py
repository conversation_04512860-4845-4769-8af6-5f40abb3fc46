# config.py

# Runtime environment
# True to select .NET Core, False for .NET Framework
USE_NET_CORE = False

# SAFE API connection settings
SAFE_DLL_PATH = R'C:\Program Files\Computers and Structures\SAFE 22\SAFEv1.dll'
REMOTE_CONNECTION = False
# Hostname if REMOTE_CONNECTION is True
REMOTE_COMPUTER_HOSTNAME = "SpareComputer-DT"
ATTACH_TO_INSTANCE = False  # True to attach to an existing SAFE instance
# True to specify SAFE.exe path, False to use latest installed
SPECIFY_PROGRAM_PATH = False
# Path to SAFE.exe if SPECIFY_PROGRAM_PATH is True
SAFE_PROGRAM_PATH = R"C:\\Program Files\\Computers and Structures\\SAFE 22\\SAFE.exe"

# File paths
# Base path for project files - adjust as needed for your environment
# BASE_PROJECT_PATH = R"C:\Users\<USER>\Desktop\DHP\test"
BASE_PROJECT_PATH = R"C:\Users\<USER>\Desktop\DHP\Test"

# Directory to store the Access database files
# The database filename will be derived from the model import Excel file.
ACCESS_DB_DIR = BASE_PROJECT_PATH + R'\Databases'

# Input Excel files
# These can be absolute paths or constructed relative to BASE_PROJECT_PATH
EXCEL_PROPERTY_FILE = BASE_PROJECT_PATH + R'\A.SAFEInput_Property.xlsx'
EXCEL_GEOMETRY_FILE = BASE_PROJECT_PATH + R'\A.SAFEInput_Geometry.xlsx'
EXCEL_GEOLOGY_FILE = BASE_PROJECT_PATH + R'\A.SAFEInput_Geology.xlsx'
EXCEL_LOADING_FILE = BASE_PROJECT_PATH + R'\A.SAFEInput_Loading.xlsx'

# Model and import/export files
# These can be absolute paths or constructed relative to BASE_PROJECT_PATH
MODEL_IMPORT_EXCEL_FILE = BASE_PROJECT_PATH + \
                          R"\SAFE Model\B.SAFE22_Model.xlsx"
MODEL_SAVE_FILE = BASE_PROJECT_PATH + R"\SAFE Model\B.SAFE22_Model.fdb"

# Sanity check: If remote connection is true, the CSiAPIService.exe must be running on the remote computer.
# This variable is for informational purposes or potential future checks.
REMOTE_SERVICE_REQUIRED = REMOTE_CONNECTION
