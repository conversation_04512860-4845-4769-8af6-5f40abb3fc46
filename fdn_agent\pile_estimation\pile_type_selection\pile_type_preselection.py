"""
AI Pile Type Pre-Selection Module (Enhanced according to Pile_Layout_Rules.md)

This module implements enhanced AI-driven pile type pre-selection following the comprehensive
rules defined in Pile_Layout_Rules.md for optimal pile type selection and layout generation.

Enhanced Pre-Selection Process (Rules 4.1-4.5):
1. Rule 4.1: Initial Pile Cap Creation with user-defined edge distance
2. Rule 4.2: Maximum Pile Cap Generation for each pile type (offset by 1 pile diameter)
3. Rule 4.3: Possible Pile Grid Generation aligned with Local Coordinate System
4. Rule 4.4: Grid shifting optimization (Case 2) for maximum pile count
5. Rule 4.5: Pile Type Evaluation and Selection with AI enhancement

Key Features:
- **AI Integration**: Evaluates all available pile types (DHP, SHP, BP variants) for each group
- **Priority-Based Selection**: Follows DHP?�SHP?�BP priority with capacity-based evaluation
- **Comprehensive Visualization**: Creates DXF showing all pile type options with layer-based organization
- **Consolidated Analysis**: Generates single DXF file for all groups with pre-selection rationale
- **Constraint Consideration**: AI considers load requirements, geometric constraints, and site limitations
- **Enhanced BP Constraints**: Proper enforcement of Case 1 (max 1 pile) and Case 2 (wall length rule)
- **Grid Optimization**: Advanced grid generation with shifting for optimal pile placement

Layer-Based DXF Organization:
- **Red Layer (DHP)**: Driven H-Pile positions and boundaries
- **Green Layer (SHP)**: Socket H-Pile positions and boundaries  
- **Magenta Layer (BP)**: Bored Pile positions and boundaries
- **Yellow Layer**: Selected pile type highlighted
- **Gray Layer**: Initial pile cap with user edge distance
- **White Layer**: Enlarged pile cap (+1m offset)
- **Element Layer**: Structural columns and walls with actual geometry

Author: Foundation Automation System
Date: June 14, 2025
"""

import math
import logging
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

from shapely.geometry import Polygon, Point

# Import from newly organized modules
from ..data_types.pile_preselection_types import (
    PileTypePreselectionCriteria,
    PileTypeCandidate,
    PileTypePreselectionResult
)
from ..data_types.pile_types import PileType
from ..data_types import Point2D, GroupElements, LocalCoordinateSystem
from ..exceptions import PileTypeSelectionError, GeometryError

# Import enhanced logging utilities
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)

# Import utility functions from new modules
from ..utils.pile_geometry_utils import (
    create_initial_pile_cap,
    establish_local_coordinate_system,
    generate_aligned_grid,
    filter_by_pile_cap_with_geometry,
    filter_by_site_boundary,
    trim_pile_cap_by_site_boundary
)

# Import Case 1 and Case 2 layout functions
from ..layout_generation.case_1_layouts import handle_case_1
from ..layout_generation.case_2_layouts import handle_case_2


class PileTypePreselector:
    """
    Pile Type Pre-Selection Engine
    
    Determines the most appropriate pile type for a structural group before NSGA-III optimization.
    Uses geometric feasibility and capacity checks to select from DHP SHP BP in priority order.
    """
    
    def __init__(self, log_callback: Optional[callable] = None):
        """
        Initialize the pile type pre-selector.

        Args:
            log_callback: Optional callback function for logging messages
        """
        log_function_entry(log_callback, "PileTypePreselector.__init__")

        enhanced_log(log_callback, "Initializing Pile Type Pre-Selector with enhanced logging", 'INFO')

        self.log_callback = log_callback or (lambda msg: logging.info(msg))

        log_function_exit(log_callback, "PileTypePreselector.__init__")
        
    def preselect_pile_type(self, 
                           criteria: PileTypePreselectionCriteria,
                           available_pile_types: List[PileTypeCandidate],
                           create_preselection_dxf: bool = True,
                           output_dir: Optional[str] = None) -> PileTypePreselectionResult:
        """
        Perform pile type pre-selection for a structural group with complete DXF visualization.
        
        Args:
            criteria: Pre-selection criteria including loads and geometry
            available_pile_types: List of pile type candidates to evaluate
            create_preselection_dxf: Whether to create DXF showing all pile type positions
            output_dir: Directory to save DXF files
            
        Returns:
            PileTypePreselectionResult with selected pile type and grid
            
        Raises:
            PileTypeSelectionError: If no viable pile type is found
        """
        log_function_entry(self.log_callback, "preselect_pile_type",
                          required_load=criteria.required_total_load,
                          num_pile_types=len(available_pile_types),
                          user_edge_distance=criteria.user_edge_distance,
                          create_dxf=create_preselection_dxf)

        with create_timed_logger(self.log_callback, "pile_type_preselection"):
            enhanced_log(self.log_callback, "Starting pile type pre-selection for group", 'INFO')
            log_calculation_result(self.log_callback, "Required total load", f"{criteria.required_total_load:.1f} kN")
            log_calculation_result(self.log_callback, "User edge distance", f"{criteria.user_edge_distance:.2f} m")
            log_calculation_result(self.log_callback, "Safety factor", criteria.safety_factor)
            log_performance_metric(self.log_callback, "Available pile types", len(available_pile_types), "types")

            # Validate input criteria
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Validating input criteria")
            self._validate_preselection_criteria(criteria, available_pile_types)

        try:
            # Determine layout case
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Determining layout case")
            layout_case = self._determine_layout_case(criteria)
            log_calculation_result(self.log_callback, "Layout case", layout_case)

            # Create pile cap geometries
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Creating pile cap geometries")
            initial_pile_cap = create_initial_pile_cap(criteria)
            log_calculation_result(self.log_callback, "Initial pile cap area", f"{initial_pile_cap.area:.3f} m²")

            # Generate maximum pile caps for each pile type based on their diameter
            # Rule: Maximum Pile Cap = Initial Pile Cap offset by 1 × pile_diameter
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Generating maximum pile caps for each pile type")
            maximum_pile_caps = self._generate_maximum_pile_caps_by_type(initial_pile_cap, available_pile_types)
            enhanced_log(self.log_callback, f"Generated {len(maximum_pile_caps)} pile type-specific maximum pile caps", 'INFO')

            local_system = establish_local_coordinate_system(initial_pile_cap, criteria)
            enhanced_log(self.log_callback, "Local coordinate system established", 'DEBUG')
            
            # Evaluate ALL pile types for complete DXF visualization
            # Apply DHP→SHP→BP priority sequence as per Pile_Layout_Rules.md
            # For BP: start with smaller pile diameter then lower capacity
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Setting up pile type evaluation priority")
            pile_priority = {PileType.DHP: 1, PileType.SHP: 2, PileType.BP: 3}
            def pile_sort_key(candidate):
                primary_priority = pile_priority[candidate.pile_type]
                # For BP types, add secondary sorting by diameter (smaller first) then capacity (lower first)
                if candidate.pile_type == PileType.BP:
                    diameter = candidate.diameter or 0
                    capacity = candidate.capacity_per_pile
                    return (primary_priority, diameter, capacity)
                else:
                    return (primary_priority, 0, 0)

            sorted_pile_types = sorted(available_pile_types, key=pile_sort_key)
            log_performance_metric(self.log_callback, "Sorted pile types", len(sorted_pile_types), "types")

            # Log pile type evaluation order
            enhanced_log(self.log_callback, "Pile type evaluation order:", 'INFO')
            for i, pile_candidate in enumerate(sorted_pile_types):
                enhanced_log(self.log_callback, f"  {i+1}. {pile_candidate.display_name} (Priority: {pile_priority[pile_candidate.pile_type]})", 'INFO')

            evaluation_summary = {}
            all_pile_type_positions = {}
            all_possible_pile_boundaries = {}

            selected_pile_type = None
            selected_result = None
            
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Starting individual pile type evaluation")

            for i, pile_candidate in enumerate(sorted_pile_types):
                enhanced_log(self.log_callback, f"Evaluating pile type {i+1}/{len(sorted_pile_types)}: {pile_candidate.display_name}", 'INFO')

                with create_timed_logger(self.log_callback, f"evaluate_{pile_candidate.pile_type.name}"):
                    # Get the maximum pile cap for this specific pile type
                    pile_type_key = pile_candidate.pile_type.name
                    maximum_pile_cap = maximum_pile_caps.get(pile_type_key)
                    if maximum_pile_cap is None:
                        enhanced_log(self.log_callback, f"No maximum pile cap found for {pile_type_key}, using fallback", 'WARNING')
                        maximum_pile_cap = maximum_pile_caps.get('fallback', initial_pile_cap)

                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} maximum pile cap area", f"{maximum_pile_cap.area:.3f} m²")

                    # Calculate possible pile boundary for this pile type using its specific maximum pile cap
                    log_algorithm_step(self.log_callback, "Pile Evaluation", f"Calculating possible pile boundary for {pile_candidate.display_name}")
                    possible_pile_boundary = self._calculate_possible_pile_boundary(
                        maximum_pile_cap, pile_candidate, criteria.user_edge_distance
                    )
                    all_possible_pile_boundaries[pile_candidate.pile_type.name] = possible_pile_boundary

                    # Log pile type characteristics and boundary
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} capacity per pile", f"{pile_candidate.capacity_per_pile:.1f} kN")
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} diameter", pile_candidate.diameter)
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} boundary area", f"{possible_pile_boundary.area:.2f} m²")
                    log_performance_metric(self.log_callback, f"{pile_candidate.display_name}_boundary_area", possible_pile_boundary.area, "m²")
                
                    # Evaluate pile type based on layout case
                    if layout_case in ["case_1", "case_2"]:
                        # Use deterministic layout logic for Case 1 and Case 2
                        log_algorithm_step(self.log_callback, "Pile Evaluation", f"Using {layout_case} deterministic layout logic")
                        result = self._evaluate_pile_type_with_case_logic(
                            pile_candidate, criteria, possible_pile_boundary, local_system,
                            layout_case
                        )
                    else:
                        # Use grid-based approach for Case 4
                        log_algorithm_step(self.log_callback, "Pile Evaluation", "Using Case 4 grid-based approach")
                        result = self._evaluate_pile_type_candidate(
                            pile_candidate, criteria, possible_pile_boundary, local_system
                        )

                    evaluation_summary[pile_candidate.pile_type.name] = result
                    all_pile_type_positions[pile_candidate.pile_type.name] = result['grid_positions']

                    # Log evaluation results
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} possible pile count", result['possible_pile_count'])
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} total capacity", f"{result['total_capacity']:.1f} kN")
                    log_calculation_result(self.log_callback, f"{pile_candidate.display_name} utilization ratio", f"{result.get('utilization_ratio', 0):.3f}")

                    # Check capacity requirement
                    meets_requirement = result['meets_capacity_requirement']
                    log_validation_result(self.log_callback, f"{pile_candidate.display_name} capacity requirement",
                                        meets_requirement,
                                        f"{result['total_capacity']:.1f} kN vs {result['required_capacity']:.1f} kN required")

                    enhanced_log(self.log_callback, f"   Results: {result['possible_pile_count']} piles, "
                                                  f"{result['total_capacity']:.1f} kN capacity", 'INFO')

                    # Select first pile type that meets requirements (but continue evaluating all)
                    if selected_pile_type is None and meets_requirement:
                        selected_pile_type = pile_candidate
                        selected_result = result
                        enhanced_log(self.log_callback, f"✓ {pile_candidate.display_name} selected as optimal!", 'INFO')
                        log_algorithm_step(self.log_callback, "Pile Selection", f"Selected {pile_candidate.display_name} as first viable option")
                    elif meets_requirement:
                        enhanced_log(self.log_callback, f"✓ {pile_candidate.display_name} also meets requirements (continuing evaluation)", 'DEBUG')
            
            # Create DXF with ALL pile type layouts if requested
            dxf_path = None
            if create_preselection_dxf:
                dxf_path = self._create_comprehensive_dxf(
                    criteria, initial_pile_cap, maximum_pile_caps,
                    all_possible_pile_boundaries, all_pile_type_positions,
                    selected_pile_type, output_dir
                )
            
            # Return result with selected pile type
            if selected_pile_type is None:
                # Enhanced error reporting for debugging
                log_algorithm_step(self.log_callback, "Pile Type Preselection", "No viable pile type found - analyzing failure")
                enhanced_log(self.log_callback, "❌ PILE TYPE SELECTION FAILED - DETAILED ANALYSIS:", 'ERROR')

                required_capacity = criteria.required_total_load * criteria.safety_factor
                log_calculation_result(self.log_callback, "Required load", f"{criteria.required_total_load:.1f} kN")
                log_calculation_result(self.log_callback, "Safety factor", criteria.safety_factor)
                log_calculation_result(self.log_callback, "Required capacity", f"{required_capacity:.1f} kN")

                enhanced_log(self.log_callback, "   Evaluation results:", 'ERROR')
                log_performance_metric(self.log_callback, "Total pile types evaluated", len(evaluation_summary), "types")

                for pile_name, result in evaluation_summary.items():
                    meets_req = result.get('meets_capacity_requirement', False)
                    total_cap = result.get('total_capacity', 0)
                    pile_count = result.get('possible_pile_count', 0)
                    status = "✓ MEETS" if meets_req else "❌ FAILS"

                    log_validation_result(self.log_callback, f"{pile_name} capacity check", meets_req,
                                        f"{pile_count} piles, {total_cap:.1f} kN capacity")
                    enhanced_log(self.log_callback, f"     {pile_name}: {status} - {pile_count} piles, {total_cap:.1f} kN capacity", 'ERROR')

                # Check if any pile types were close to meeting requirements
                best_capacity = 0
                best_pile_type = None
                best_result = None
                for pile_name, result in evaluation_summary.items():
                    total_cap = result.get('total_capacity', 0)
                    if total_cap > best_capacity:
                        best_capacity = total_cap
                        best_pile_type = pile_name
                        best_result = result

                if best_pile_type and best_result:
                    capacity_gap = (criteria.required_total_load * criteria.safety_factor) - best_capacity
                    self.log_callback(f"   Best option: {best_pile_type} with {best_capacity:.1f} kN")
                    self.log_callback(f"   Capacity gap: {capacity_gap:.1f} kN ({capacity_gap/best_capacity*100:.1f}% short)")

                    # Try multi-pile strategy for extreme loads
                    if capacity_gap > 0 and best_capacity > 0:
                        self.log_callback("🔄 ATTEMPTING MULTI-PILE STRATEGY FOR EXTREME LOAD...")
                        multi_pile_result = self._attempt_multi_pile_solution(
                            criteria, best_result, capacity_gap, best_pile_type
                        )
                        if multi_pile_result:
                            self.log_callback(" Multi-pile strategy successful!")
                            return multi_pile_result

                error_msg = "No viable pile type found that meets capacity requirements"
                log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "pile type preselection")
                raise PileTypeSelectionError(error_msg)

            # Log successful selection
            log_algorithm_step(self.log_callback, "Pile Type Preselection", "Successfully completed pile type selection")
            enhanced_log(self.log_callback, f"✓ Selected pile type: {selected_pile_type.display_name}", 'INFO')
            log_calculation_result(self.log_callback, "Final selected pile count", selected_result['possible_pile_count'])
            log_calculation_result(self.log_callback, "Final total capacity", f"{selected_result['total_capacity']:.1f} kN")
            log_calculation_result(self.log_callback, "Final utilization ratio", f"{selected_result['utilization_ratio']:.3f}")
            log_performance_metric(self.log_callback, "Preselection_success_rate", 1.0, "ratio")

            result = PileTypePreselectionResult(
                selected_pile_type=selected_pile_type,
                viable_grid_positions=selected_result['grid_positions'],
                total_possible_piles=selected_result['possible_pile_count'],
                total_capacity=selected_result['total_capacity'],
                utilization_ratio=selected_result['utilization_ratio'],
                initial_pile_cap=initial_pile_cap,
                enlarged_pile_cap=maximum_pile_caps.get(selected_pile_type.pile_type.name, initial_pile_cap),
                local_coordinate_system=local_system,
                evaluation_summary=evaluation_summary,
                all_pile_type_positions=all_pile_type_positions,
                all_possible_pile_boundaries=all_possible_pile_boundaries,
                preselection_dxf_path=dxf_path
            )

            log_function_exit(self.log_callback, "preselect_pile_type", f"Selected {selected_pile_type.display_name}")
            return result

        except PileTypeSelectionError:
            log_function_exit(self.log_callback, "preselect_pile_type", "Failed - PileTypeSelectionError")
            raise
        except Exception as e:
            error_msg = f"Pile type pre-selection failed: {e}"
            log_error_with_context(self.log_callback, e, "pile type preselection")
            enhanced_log(self.log_callback, error_msg, 'ERROR')
            log_function_exit(self.log_callback, "preselect_pile_type", "Failed - Exception")
            raise PileTypeSelectionError(error_msg) from e

    def _validate_preselection_criteria(self, criteria: PileTypePreselectionCriteria,
                                      available_pile_types: List[PileTypeCandidate]) -> None:
        """
        Validate preselection criteria and available pile types.

        Args:
            criteria: Pre-selection criteria to validate
            available_pile_types: List of available pile types to validate

        Raises:
            PileTypeSelectionError: If validation fails
        """
        log_function_entry(self.log_callback, "_validate_preselection_criteria",
                          required_load=criteria.required_total_load,
                          num_pile_types=len(available_pile_types))

        # Validate required load
        if criteria.required_total_load <= 0:
            error_msg = f"Invalid required load: {criteria.required_total_load} kN (must be positive)"
            log_validation_result(self.log_callback, "Required load validation", False, error_msg)
            log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "criteria validation")
            raise PileTypeSelectionError(error_msg)
        else:
            log_validation_result(self.log_callback, "Required load validation", True, f"{criteria.required_total_load:.1f} kN")

        # Validate safety factor
        if criteria.safety_factor <= 0:
            error_msg = f"Invalid safety factor: {criteria.safety_factor} (must be positive)"
            log_validation_result(self.log_callback, "Safety factor validation", False, error_msg)
            log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "criteria validation")
            raise PileTypeSelectionError(error_msg)
        else:
            log_validation_result(self.log_callback, "Safety factor validation", True, f"{criteria.safety_factor}")

        # Validate user edge distance
        if criteria.user_edge_distance < 0:
            error_msg = f"Invalid user edge distance: {criteria.user_edge_distance} m (must be non-negative)"
            log_validation_result(self.log_callback, "User edge distance validation", False, error_msg)
            log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "criteria validation")
            raise PileTypeSelectionError(error_msg)
        else:
            log_validation_result(self.log_callback, "User edge distance validation", True, f"{criteria.user_edge_distance:.2f} m")

        # Validate available pile types
        if not available_pile_types:
            error_msg = "No pile types available for evaluation"
            log_validation_result(self.log_callback, "Available pile types validation", False, error_msg)
            log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "criteria validation")
            raise PileTypeSelectionError(error_msg)
        else:
            log_validation_result(self.log_callback, "Available pile types validation", True, f"{len(available_pile_types)} types available")

        # Validate individual pile types
        for i, pile_type in enumerate(available_pile_types):
            if pile_type.capacity_per_pile <= 0:
                error_msg = f"Invalid capacity for pile type {pile_type.display_name}: {pile_type.capacity_per_pile} kN"
                log_validation_result(self.log_callback, f"Pile type {i+1} capacity validation", False, error_msg)
                log_error_with_context(self.log_callback, PileTypeSelectionError(error_msg), "pile type validation")
                raise PileTypeSelectionError(error_msg)
            else:
                log_validation_result(self.log_callback, f"Pile type {i+1} capacity validation", True,
                                    f"{pile_type.display_name}: {pile_type.capacity_per_pile:.1f} kN")

        enhanced_log(self.log_callback, "All preselection criteria validation passed", 'INFO')
        log_function_exit(self.log_callback, "_validate_preselection_criteria", "Validation successful")

    def _calculate_possible_pile_boundary(self,
                                        maximum_pile_cap: Polygon,
                                        pile_candidate: PileTypeCandidate,
                                        user_edge_distance: float) -> Polygon:
        """
        Calculate the possible pile boundary using the correct formula.

        Implements the rule: Possible Pile Boundary = Maximum Pile Cap - pile_radius - edge_distance
        This boundary defines where pile centers can actually be placed.

        Args:
            maximum_pile_cap: The maximum pile cap polygon (Initial Pile Cap + pile_diameter offset)
            pile_candidate: Pile type candidate with diameter/section info
            user_edge_distance: User-defined edge distance

        Returns:
            Polygon representing the possible pile boundary

        Raises:
            GeometryError: If boundary calculation fails
        """
        log_function_entry(self.log_callback, "_calculate_possible_pile_boundary",
                          pile_type=pile_candidate.pile_type.name,
                          pile_diameter=pile_candidate.diameter,
                          user_edge_distance=user_edge_distance,
                          maximum_cap_area=maximum_pile_cap.area)

        # Validate inputs
        if maximum_pile_cap.is_empty or not maximum_pile_cap.is_valid:
            error_msg = "Invalid or empty maximum pile cap provided"
            log_validation_result(self.log_callback, "Maximum pile cap validation", False, error_msg)
            log_error_with_context(self.log_callback, GeometryError(error_msg), "pile boundary calculation")
            raise GeometryError(error_msg)
        else:
            log_validation_result(self.log_callback, "Maximum pile cap validation", True, f"Area: {maximum_pile_cap.area:.3f} m²")

        if user_edge_distance <= 0:
            error_msg = "User edge distance must be positive"
            log_validation_result(self.log_callback, "User edge distance validation", False, error_msg)
            log_error_with_context(self.log_callback, GeometryError(error_msg), "pile boundary calculation")
            raise GeometryError(error_msg)
        else:
            log_validation_result(self.log_callback, "User edge distance validation", True, f"{user_edge_distance:.2f} m")

        # Calculate pile radius based on pile type
        log_algorithm_step(self.log_callback, "Pile Boundary Calculation", "Determining pile radius")
        if pile_candidate.diameter is not None:
            # Bored piles have diameter
            pile_radius = pile_candidate.diameter / 2
            enhanced_log(self.log_callback, f"Using bored pile diameter: {pile_candidate.diameter:.2f} m (radius: {pile_radius:.2f} m)", 'DEBUG')
        else:
            # H-piles (DHP/SHP) - use equivalent diameter based on section
            if pile_candidate.pile_type == PileType.DHP or pile_candidate.pile_type == PileType.SHP:
                # For H-piles, use a standard equivalent diameter (typically around 0.5-0.6m)
                pile_radius = 0.3  # Conservative 0.6m diameter equivalent
                enhanced_log(self.log_callback, f"Using H-pile equivalent radius: {pile_radius:.2f} m", 'DEBUG')
            else:
                # Default for unknown pile types
                pile_radius = 0.3
                enhanced_log(self.log_callback, f"Using default pile radius: {pile_radius:.2f} m", 'DEBUG')

        log_calculation_result(self.log_callback, "Pile radius", f"{pile_radius:.2f} m")

        # Apply the correct formula: Possible Pile Boundary = Maximum Pile Cap - pile_radius - edge_distance
        # This means we inset the maximum pile cap by (pile_radius + edge_distance)
        inset_distance = pile_radius + user_edge_distance
        log_calculation_result(self.log_callback, "Inset distance (pile_radius + edge_distance)", f"{inset_distance:.2f} m")
        enhanced_log(self.log_callback, f"Applying formula: Possible Pile Boundary = Maximum Pile Cap - {pile_radius:.2f}m - {user_edge_distance:.2f}m", 'DEBUG')

        # Enhanced inset distance logic with intelligent constraints
        # Only limit inset if it would make the pile cap area too small to be practical
        pile_cap_area = maximum_pile_cap.area
        min_practical_area = 4.0  # Minimum 4 m² for practical pile placement

        # Calculate what the area would be with the full inset
        test_boundary = maximum_pile_cap.buffer(-inset_distance)
        if isinstance(test_boundary, Polygon) and not test_boundary.is_empty:
            inset_area = test_boundary.area
        else:
            inset_area = 0.0

        # Apply intelligent inset limitation only when necessary
        if inset_area < min_practical_area:
            # Calculate maximum allowable inset that preserves minimum area
            # Use iterative approach to find optimal inset
            max_allowable_inset = inset_distance
            for test_inset in [inset_distance * 0.8, inset_distance * 0.6, inset_distance * 0.4, inset_distance * 0.2]:
                test_boundary = maximum_pile_cap.buffer(-test_inset)
                if isinstance(test_boundary, Polygon) and not test_boundary.is_empty and test_boundary.area >= min_practical_area:
                    max_allowable_inset = test_inset
                    break

            min_inset = max_allowable_inset
            enhanced_log(self.log_callback, f"Inset distance intelligently limited from {inset_distance:.2f} m to {min_inset:.2f} m", 'INFO')
            enhanced_log(self.log_callback, f"Reason: Preserving minimum practical pile cap area ({min_practical_area:.1f} m²)", 'INFO')
        else:
            min_inset = inset_distance
            enhanced_log(self.log_callback, f"Using full calculated inset distance: {min_inset:.2f} m", 'INFO')

        log_calculation_result(self.log_callback, "Final inset distance", f"{min_inset:.2f} m")
        log_calculation_result(self.log_callback, "Pile cap area after inset", f"{inset_area:.2f} m²")

        # Apply the formula: Possible Pile Boundary = Maximum Pile Cap - pile_radius - edge_distance
        log_algorithm_step(self.log_callback, "Pile Boundary Calculation", "Creating possible pile boundary using correct formula")
        enhanced_log(self.log_callback, f"Final calculation: Maximum Pile Cap ({maximum_pile_cap.area:.3f} m²) - {min_inset:.2f}m inset", 'DEBUG')
        possible_boundary = maximum_pile_cap.buffer(-min_inset)

        # Ensure we have a valid polygon
        if isinstance(possible_boundary, Polygon) and not possible_boundary.is_empty:
            log_validation_result(self.log_callback, "Pile boundary creation", True,
                                f"Area: {possible_boundary.area:.3f} m²")
            log_calculation_result(self.log_callback, "Final pile boundary area", f"{possible_boundary.area:.3f} m²")
            log_performance_metric(self.log_callback, f"{pile_candidate.pile_type.name}_boundary_area", possible_boundary.area, "m²")
            log_function_exit(self.log_callback, "_calculate_possible_pile_boundary", f"Success - {possible_boundary.area:.3f} m²")
            return possible_boundary
        else:
            # If inset creates invalid geometry, use a smaller inset
            enhanced_log(self.log_callback, f"Initial inset failed, trying smaller inset", 'WARNING')
            smaller_inset = min_inset * 0.5
            log_calculation_result(self.log_callback, "Reduced inset distance", f"{smaller_inset:.2f} m")
            possible_boundary = maximum_pile_cap.buffer(-smaller_inset)

            if isinstance(possible_boundary, Polygon) and not possible_boundary.is_empty:
                log_validation_result(self.log_callback, "Reduced pile boundary creation", True,
                                    f"Area: {possible_boundary.area:.3f} m²")
                enhanced_log(self.log_callback, f"Successfully created boundary with reduced inset", 'INFO')
                log_function_exit(self.log_callback, "_calculate_possible_pile_boundary", f"Success with reduced inset - {possible_boundary.area:.3f} m²")
                return possible_boundary
            else:
                error_msg = f"Cannot create valid pile boundary for pile type {pile_candidate.pile_type.name}"
                log_validation_result(self.log_callback, "Pile boundary creation", False, error_msg)
                log_error_with_context(self.log_callback, GeometryError(error_msg), "pile boundary calculation")
                log_function_exit(self.log_callback, "_calculate_possible_pile_boundary", "Failed - GeometryError")
                raise GeometryError(error_msg)
    
    def _determine_layout_case(self, criteria: PileTypePreselectionCriteria) -> str:
        """
        Determine whether this is Case 1 (single column) or Case 2 (single wall) layout.

        Args:
            criteria: Pre-selection criteria containing group elements

        Returns:
            str: Either "case_1", "case_2", or "case_4" (STRICT: must determine exact case - NO FALLBACKS)
        """
        log_function_entry(self.log_callback, "_determine_layout_case")

        group_elements = criteria.group_elements

        # Count elements - GroupElements is a TypedDict, access as dictionary
        num_columns = len(group_elements.get('columns', [])) if group_elements.get('columns') else 0
        num_walls = len(group_elements.get('walls', [])) if group_elements.get('walls') else 0

        log_calculation_result(self.log_callback, "Number of columns", num_columns)
        log_calculation_result(self.log_callback, "Number of walls", num_walls)

        # FIXED: Strict case classification
        if num_columns == 1 and num_walls == 0:
            layout_case = "case_1"
            enhanced_log(self.log_callback, "✓ Detected Case 1: Single Column layout (1 column, 0 walls)", 'INFO')
            log_algorithm_step(self.log_callback, "Layout Case Detection", "Case 1 - Single Column")
            log_function_exit(self.log_callback, "_determine_layout_case", layout_case)
            return layout_case
        elif num_columns == 0 and num_walls == 1:
            layout_case = "case_2"
            enhanced_log(self.log_callback, "✓ Detected Case 2: Single Wall layout (0 columns, 1 wall)", 'INFO')
            log_algorithm_step(self.log_callback, "Layout Case Detection", "Case 2 - Single Wall")
            log_function_exit(self.log_callback, "_determine_layout_case", layout_case)
            return layout_case
        else:
            # FIXED: Mixed elements or multiple elements → Case 4
            layout_case = "case_4"
            enhanced_log(self.log_callback, f"✓ Detected Case 4: Complex layout ({num_columns} columns, {num_walls} walls)", 'INFO')
            log_algorithm_step(self.log_callback, "Layout Case Detection", f"Case 4 - Complex ({num_columns}C, {num_walls}W)")
            log_function_exit(self.log_callback, "_determine_layout_case", layout_case)
            return layout_case

    def _generate_maximum_pile_caps_by_type(self,
                                          initial_pile_cap: Polygon,
                                          available_pile_types: List[PileTypeCandidate]) -> Dict[str, Polygon]:
        """
        Generate maximum pile caps for each pile type based on their diameter.

        Implements the rule: Maximum Pile Cap = Initial Pile Cap offset by 1 × pile_diameter

        Args:
            initial_pile_cap: The initial pile cap polygon
            available_pile_types: List of pile type candidates

        Returns:
            Dictionary mapping pile type names to their maximum pile caps
        """
        log_function_entry(self.log_callback, "_generate_maximum_pile_caps_by_type",
                          initial_cap_area=initial_pile_cap.area,
                          num_pile_types=len(available_pile_types))

        maximum_pile_caps = {}

        enhanced_log(self.log_callback, "Generating pile type-specific maximum pile caps using diameter-based offsets", 'INFO')

        for pile_candidate in available_pile_types:
            pile_type_name = pile_candidate.pile_type.name

            # Determine pile diameter for offset calculation
            if pile_candidate.diameter is not None:
                # Bored piles have explicit diameter
                pile_diameter = pile_candidate.diameter
                enhanced_log(self.log_callback, f"{pile_candidate.display_name}: Using explicit diameter {pile_diameter:.2f}m", 'DEBUG')
            else:
                # H-piles (DHP/SHP) - use equivalent diameter
                if pile_candidate.pile_type == PileType.DHP or pile_candidate.pile_type == PileType.SHP:
                    pile_diameter = 0.6  # Standard equivalent diameter for H-piles
                    enhanced_log(self.log_callback, f"{pile_candidate.display_name}: Using H-pile equivalent diameter {pile_diameter:.2f}m", 'DEBUG')
                else:
                    pile_diameter = 0.6  # Default fallback
                    enhanced_log(self.log_callback, f"{pile_candidate.display_name}: Using default diameter {pile_diameter:.2f}m", 'WARNING')

            # Apply the rule: Maximum Pile Cap = Initial Pile Cap offset by 1 × pile_diameter
            offset_distance = 1.0 * pile_diameter
            log_calculation_result(self.log_callback, f"{pile_candidate.display_name} offset distance", f"{offset_distance:.2f}m")

            try:
                # Buffer the initial pile cap by the pile diameter
                maximum_pile_cap = initial_pile_cap.buffer(offset_distance, join_style=2)  # join_style=2 for mitered joins

                if maximum_pile_cap.is_empty or not maximum_pile_cap.is_valid:
                    enhanced_log(self.log_callback, f"Invalid maximum pile cap for {pile_type_name}, using initial cap", 'WARNING')
                    maximum_pile_cap = initial_pile_cap

                maximum_pile_caps[pile_type_name] = maximum_pile_cap

                # Log results
                area_increase = maximum_pile_cap.area - initial_pile_cap.area
                area_ratio = maximum_pile_cap.area / initial_pile_cap.area if initial_pile_cap.area > 0 else 1.0

                log_calculation_result(self.log_callback, f"{pile_candidate.display_name} maximum cap area", f"{maximum_pile_cap.area:.3f} m²")
                log_calculation_result(self.log_callback, f"{pile_candidate.display_name} area increase", f"{area_increase:.3f} m² ({area_ratio:.2f}x)")

                enhanced_log(self.log_callback, f"✓ {pile_candidate.display_name}: {maximum_pile_cap.area:.3f} m² (offset: {offset_distance:.2f}m)", 'INFO')

            except Exception as e:
                enhanced_log(self.log_callback, f"Failed to create maximum pile cap for {pile_type_name}: {e}", 'ERROR')
                maximum_pile_caps[pile_type_name] = initial_pile_cap
                log_error_with_context(self.log_callback, e, f"maximum pile cap generation for {pile_type_name}")

        # Add fallback for any missing types
        maximum_pile_caps['fallback'] = initial_pile_cap

        log_performance_metric(self.log_callback, "Maximum pile caps generated", len(maximum_pile_caps), "caps")
        enhanced_log(self.log_callback, f"Successfully generated {len(maximum_pile_caps)} maximum pile caps", 'INFO')
        log_function_exit(self.log_callback, "_generate_maximum_pile_caps_by_type", f"{len(maximum_pile_caps)} caps generated")

        return maximum_pile_caps

    def _evaluate_pile_type_with_case_logic(self,
                                           pile_candidate: PileTypeCandidate,
                                           criteria: PileTypePreselectionCriteria,
                                           possible_pile_boundary: Polygon,
                                           local_system: LocalCoordinateSystem,
                                           layout_case: str) -> Dict[str, Any]:
        """Evaluate a pile type using Case 1 or Case 2 deterministic layout logic."""

        log_function_entry(self.log_callback, "_evaluate_pile_type_with_case_logic",
                          pile_type=pile_candidate.pile_type.name,
                          layout_case=layout_case,
                          capacity_per_pile=pile_candidate.capacity_per_pile)

        with create_timed_logger(self.log_callback, f"evaluate_case_logic_{pile_candidate.pile_type.name}"):
            # Create cluster data for Case 1/2 handlers
            log_algorithm_step(self.log_callback, "Case Logic Evaluation", "Creating cluster data")
            cluster_data = self._create_cluster_data_from_criteria(criteria)

            # Special handling for BP (Bored Pile) types
            if pile_candidate.pile_type == PileType.BP:
                enhanced_log(self.log_callback, f"Applying BP special case rules for {layout_case}", 'INFO')
                log_algorithm_step(self.log_callback, "Case Logic Evaluation", f"BP special handling for {layout_case}")
                result = self._handle_bp_special_cases(
                    pile_candidate, criteria, cluster_data, layout_case, possible_pile_boundary
                )
                log_function_exit(self.log_callback, "_evaluate_pile_type_with_case_logic", "BP special case completed")
                return result

            # Calculate estimated required piles based on capacity
            required_capacity = criteria.required_total_load * criteria.safety_factor
            estimated_piles = max(1, math.ceil(required_capacity / pile_candidate.capacity_per_pile))

            log_calculation_result(self.log_callback, "Required capacity", f"{required_capacity:.1f} kN")
            log_calculation_result(self.log_callback, "Estimated required piles", estimated_piles)
            log_constraint_check(self.log_callback, "Minimum pile count", estimated_piles, 1, estimated_piles >= 1)

            # Generate pile positions using appropriate case logic
            log_algorithm_step(self.log_callback, "Case Logic Evaluation", f"Generating pile positions for {layout_case}")
            try:
                if layout_case == "case_1":
                    enhanced_log(self.log_callback, f"Using Case 1 handler with {estimated_piles} required piles", 'DEBUG')
                    positions = handle_case_1(
                        cluster_data=cluster_data,
                        required_piles=estimated_piles,
                        min_spacing=pile_candidate.min_spacing,
                        initial_local_system=local_system
                    )
                elif layout_case == "case_2":
                    enhanced_log(self.log_callback, f"Using Case 2 handler with {estimated_piles} required piles", 'DEBUG')
                    positions = handle_case_2(
                        cluster_data=cluster_data,
                        required_piles=estimated_piles,
                        min_spacing=pile_candidate.min_spacing,
                        initial_local_system=local_system
                    )
                else:
                    error_msg = f"Unsupported layout case: {layout_case}"
                    log_error_with_context(self.log_callback, ValueError(error_msg), "case logic evaluation")
                    raise ValueError(error_msg)

                log_calculation_result(self.log_callback, "Initial positions generated", len(positions))

                # Filter positions by possible pile boundary
                log_algorithm_step(self.log_callback, "Case Logic Evaluation", "Filtering positions by pile boundary")
                final_positions = self._filter_by_possible_pile_boundary(positions, possible_pile_boundary)
                log_calculation_result(self.log_callback, "Positions after boundary filter", len(final_positions))

                # Filter by site boundary if provided
                if criteria.site_boundary:
                    log_algorithm_step(self.log_callback, "Case Logic Evaluation", "Filtering positions by site boundary")
                    final_positions = filter_by_site_boundary(final_positions, criteria.site_boundary)
                    log_calculation_result(self.log_callback, "Positions after site boundary filter", len(final_positions))
                else:
                    enhanced_log(self.log_callback, "No site boundary provided - skipping site boundary filter", 'DEBUG')

                # Calculate capacity and requirements
                log_algorithm_step(self.log_callback, "Case Logic Evaluation", "Calculating final capacity and requirements")
                possible_pile_count = len(final_positions)
                total_capacity = possible_pile_count * pile_candidate.capacity_per_pile
                required_capacity = criteria.required_total_load * criteria.safety_factor
                meets_requirement = total_capacity >= required_capacity
                utilization_ratio = criteria.required_total_load / total_capacity if total_capacity > 0 else float('inf')

                # Log final calculations
                log_calculation_result(self.log_callback, "Final pile count", possible_pile_count)
                log_calculation_result(self.log_callback, "Total capacity", f"{total_capacity:.1f} kN")
                log_calculation_result(self.log_callback, "Required capacity", f"{required_capacity:.1f} kN")
                log_calculation_result(self.log_callback, "Utilization ratio", f"{utilization_ratio:.3f}")

                # Check capacity constraint
                log_constraint_check(self.log_callback, "Capacity requirement", total_capacity, required_capacity, meets_requirement)
                log_validation_result(self.log_callback, f"{pile_candidate.pile_type.name} capacity check", meets_requirement,
                                    f"{total_capacity:.1f} kN vs {required_capacity:.1f} kN required")

                result = {
                    'grid_positions': final_positions,
                    'possible_pile_count': possible_pile_count,
                    'total_capacity': total_capacity,
                    'required_capacity': required_capacity,
                    'meets_capacity_requirement': meets_requirement,
                    'utilization_ratio': utilization_ratio,
                    'capacity_per_pile': pile_candidate.capacity_per_pile,
                    'min_spacing': pile_candidate.min_spacing,
                    'diameter': getattr(pile_candidate, 'diameter', None),  # Add diameter for BP visualization
                    'layout_case': layout_case
                }

                log_function_exit(self.log_callback, "_evaluate_pile_type_with_case_logic",
                                f"Success - {possible_pile_count} piles, {total_capacity:.1f} kN")
                return result

            except Exception as e:
                error_msg = f"Case logic evaluation failed for {pile_candidate.pile_type.name}: {e}"
                log_error_with_context(self.log_callback, e, f"case logic evaluation - {layout_case}")
                enhanced_log(self.log_callback, error_msg, 'ERROR')

                result = {
                    'grid_positions': [],
                    'possible_pile_count': 0,
                    'total_capacity': 0.0,
                    'required_capacity': criteria.required_total_load * criteria.safety_factor,
                    'meets_capacity_requirement': False,
                    'utilization_ratio': float('inf'),
                    'capacity_per_pile': pile_candidate.capacity_per_pile,
                    'min_spacing': pile_candidate.min_spacing,
                    'diameter': getattr(pile_candidate, 'diameter', None),  # Add diameter for BP visualization
                    'layout_case': layout_case,
                    'error': str(e)
                }

                log_function_exit(self.log_callback, "_evaluate_pile_type_with_case_logic", f"Failed - {str(e)}")
                return result
    
    def _handle_bp_special_cases(self,
                                pile_candidate: PileTypeCandidate,
                                criteria: PileTypePreselectionCriteria,
                                cluster_data: dict,
                                layout_case: str,
                                possible_pile_boundary: Polygon) -> Dict[str, Any]:
        """
        Handle special BP (Bored Pile) rules for Case 1 and Case 2.
        
        Implements enhanced BP constraints according to Pile_Layout_Rules.md:
        - Case 1 BP Constraint: Enforce maximum 1 pile constraint
        - Case 2 BP Constraint: Apply wall length rule (pile diameter max 1 pile, else 2)
        """
        
        if layout_case == "case_1":
            # Case 1 BP: Always 1 pile at load center with max utilization
            # Implements: **Case 1 BP Constraint**: Enforce maximum 1 pile constraint
            # **CRITICAL**: For Case 1, BP positions MUST be exactly at load centroid of column
            load_center = self._calculate_load_center(cluster_data)
            self.log_callback(f"   BP Case 1: Load center calculated at ({load_center[0]:.2f}, {load_center[1]:.2f})")
            self.log_callback(f"   BP Case 1: Applying maximum 1 pile constraint")
            self.log_callback(f"   BP Case 1: **CRITICAL** - Placing pile exactly at load centroid as per rules")
            
            # **CRITICAL**: Ensure BP pile is positioned exactly at load center
            # For Case 1, always use load center regardless of boundary constraints
            positions = [load_center]
            self.log_callback(f"   BP Case 1: CRITICAL - BP position set to load centroid ({load_center[0]:.2f}, {load_center[1]:.2f})")
            
            # Check if position is within possible pile boundary for warning only
            load_point = Point(load_center[0], load_center[1])
            if load_point.within(possible_pile_boundary) or possible_pile_boundary.contains(load_point):
                self.log_callback(f"   BP Case 1: Load centroid is within possible pile boundary")
            else:
                self.log_callback(f"   BP Case 1: WARNING - Load centroid ({load_center[0]:.2f}, {load_center[1]:.2f}) outside boundary")
                self.log_callback(f"   BP Case 1: CRITICAL - Still using load centroid as per Case 1 BP rules")
            
            # Calculate capacity for 1 pile (BP constraint enforced)
            total_capacity = pile_candidate.capacity_per_pile
            meets_requirement = total_capacity >= criteria.required_total_load * criteria.safety_factor
            utilization_ratio = criteria.required_total_load / total_capacity
            
            return {
                'grid_positions': positions,
                'possible_pile_count': 1,
                'total_capacity': total_capacity,
                'required_capacity': criteria.required_total_load * criteria.safety_factor,
                'meets_capacity_requirement': meets_requirement,
                'utilization_ratio': utilization_ratio,
                'capacity_per_pile': pile_candidate.capacity_per_pile,
                'min_spacing': pile_candidate.min_spacing,
                'diameter': getattr(pile_candidate, 'diameter', None),  # Add diameter for BP visualization
                'layout_case': layout_case,
                'bp_special_rule': 'case_1_maximum_1_pile_constraint_at_load_centroid'
            }
            
        elif layout_case == "case_2":
            # Case 2 BP: Apply wall length rule
            # Implements: **Case 2 BP Constraint**: Apply wall length rule (pile diameter max 1 pile, else 2)
            wall_geometry = self._extract_wall_geometry(cluster_data)
            if not wall_geometry:
                # STRICT REQUIREMENT: Single pile at load center only
                load_center = self._calculate_load_center(cluster_data)
                self.log_callback(f"   BP Case 2: No wall found, using load center ({load_center[0]:.2f}, {load_center[1]:.2f})")
                return self._create_single_pile_result(pile_candidate, criteria, load_center, layout_case)
            
            start_point, end_point, wall_dir, wall_length = wall_geometry
            pile_diameter = pile_candidate.diameter if pile_candidate.diameter is not None else 0.6
            wall_length_threshold = 3 * pile_diameter
            
            self.log_callback(f"   BP Case 2: Wall length {wall_length:.2f}m, pile diameter {pile_diameter:.2f}m")
            self.log_callback(f"   BP Case 2: Wall length threshold = 3 * {pile_diameter:.2f}m = {wall_length_threshold:.2f}m")
            
            if wall_length > wall_length_threshold:
                # Wall long enough: place 2 piles at wall start/end points
                positions = [start_point, end_point]
                self.log_callback(f"   BP Case 2: Wall length > threshold, placing 2 piles at wall ends")
                
                # Filter by possible pile boundary
                final_positions = self._filter_by_possible_pile_boundary(positions, possible_pile_boundary)
                
                if len(final_positions) < 2:
                    # If wall ends are outside boundary, adjust positions
                    self.log_callback(f"   BP Case 2: Wall ends outside boundary, adjusting positions")
                    final_positions = self._adjust_bp_positions_to_boundary(
                        positions, possible_pile_boundary, wall_dir
                    )
                
                # Ensure we have at least 1 pile
                if not final_positions:
                    load_center = self._calculate_load_center(cluster_data)
                    final_positions = [load_center]
                    self.log_callback(f"   BP Case 2: No valid positions found - CRITICAL ERROR")
                
                total_capacity = len(final_positions) * pile_candidate.capacity_per_pile
                meets_requirement = total_capacity >= criteria.required_total_load * criteria.safety_factor
                utilization_ratio = criteria.required_total_load / total_capacity if total_capacity > 0 else float('inf')
                
                return {
                    'grid_positions': final_positions,
                    'possible_pile_count': len(final_positions),
                    'total_capacity': total_capacity,
                    'required_capacity': criteria.required_total_load * criteria.safety_factor,
                    'meets_capacity_requirement': meets_requirement,
                    'utilization_ratio': utilization_ratio,
                    'capacity_per_pile': pile_candidate.capacity_per_pile,
                    'min_spacing': pile_candidate.min_spacing,
                    'diameter': getattr(pile_candidate, 'diameter', None),  # Add diameter for BP visualization
                    'layout_case': layout_case,
                    'bp_special_rule': 'case_2_wall_length_rule_2_piles'
                }
            else:
                # Wall too short: maximum 1 pile at load center
                load_center = self._calculate_load_center(cluster_data)
                self.log_callback(f"   BP Case 2: Wall length < threshold ({wall_length:.2f}m < {wall_length_threshold:.2f}m)")
                self.log_callback(f"   BP Case 2: Applying maximum 1 pile constraint, using load center")
                return self._create_single_pile_result_with_rule(pile_candidate, criteria, load_center, layout_case, 'case_2_wall_length_rule_1_pile')
        
        # ERROR: System cannot continue without valid positions
        raise ValueError("No valid pile positions found - system must fail completely")
        return self._create_single_pile_result(pile_candidate, criteria, load_center, layout_case)
    
    def _create_single_pile_result(self, pile_candidate: PileTypeCandidate,
                                  criteria: PileTypePreselectionCriteria,
                                  position: Point2D, layout_case: str) -> Dict[str, Any]:
        """Create a result for a single pile placement."""
        log_function_entry(self.log_callback, "_create_single_pile_result",
                          pile_type=pile_candidate.pile_type.name,
                          position=position,
                          layout_case=layout_case)

        total_capacity = pile_candidate.capacity_per_pile
        required_capacity = criteria.required_total_load * criteria.safety_factor
        meets_requirement = total_capacity >= required_capacity
        utilization_ratio = criteria.required_total_load / total_capacity

        # Log single pile calculations
        log_calculation_result(self.log_callback, "Single pile capacity", f"{total_capacity:.1f} kN")
        log_calculation_result(self.log_callback, "Required capacity", f"{required_capacity:.1f} kN")
        log_calculation_result(self.log_callback, "Utilization ratio", f"{utilization_ratio:.3f}")
        log_constraint_check(self.log_callback, "Single pile capacity requirement", total_capacity, required_capacity, meets_requirement)
        log_validation_result(self.log_callback, "Single pile capacity check", meets_requirement,
                            f"{total_capacity:.1f} kN vs {required_capacity:.1f} kN required")

        result = {
            'grid_positions': [position],
            'possible_pile_count': 1,
            'total_capacity': total_capacity,
            'required_capacity': required_capacity,
            'meets_capacity_requirement': meets_requirement,
            'utilization_ratio': utilization_ratio,
            'capacity_per_pile': pile_candidate.capacity_per_pile,
            'min_spacing': pile_candidate.min_spacing,
            'diameter': getattr(pile_candidate, 'diameter', None),  # Add diameter for BP visualization
            'layout_case': layout_case
        }

        log_function_exit(self.log_callback, "_create_single_pile_result", f"Single pile - {total_capacity:.1f} kN")
        return result
    
    def _create_single_pile_result_with_rule(self, pile_candidate: PileTypeCandidate, 
                                           criteria: PileTypePreselectionCriteria,
                                           position: Point2D, layout_case: str, 
                                           bp_rule: str) -> Dict[str, Any]:
        """Create a result for a single pile placement with BP special rule."""
        result = self._create_single_pile_result(pile_candidate, criteria, position, layout_case)
        result['bp_special_rule'] = bp_rule
        return result
    
    def _find_closest_boundary_point(self, target_point: Point2D, boundary: Polygon) -> Point2D:
        """Find the closest point on the boundary to the target point."""
        boundary_coords = list(boundary.exterior.coords)
        min_dist = float('inf')
        best_pos = target_point
        
        for coord in boundary_coords:
            dist = ((coord[0] - target_point[0])**2 + (coord[1] - target_point[1])**2)**0.5
            if dist < min_dist:
                min_dist = dist
                best_pos = coord
        
        return best_pos
    
    def _evaluate_pile_type_candidate(self, 
                                    pile_candidate: PileTypeCandidate,
                                    criteria: PileTypePreselectionCriteria,
                                    possible_pile_boundary: Polygon,
                                    local_system: LocalCoordinateSystem) -> Dict[str, Any]:
        """Evaluate a specific pile type candidate for Case 4."""
        
        # Generate optimized grid positions aligned with optimal rectangle
        # Note: generate_aligned_grid expects pile_cap parameter, but we'll filter by boundary after
        grid_positions = generate_aligned_grid(
            pile_candidate, 
            local_system, 
            possible_pile_boundary,  # Pass boundary as pile_cap for grid generation
            is_case_4=True
        )
        
        # Filter positions by possible pile boundary
        final_positions = self._filter_by_possible_pile_boundary(grid_positions, possible_pile_boundary)
        
        # Filter by site boundary if provided
        final_positions = filter_by_site_boundary(final_positions, criteria.site_boundary)
        
        # Calculate capacity and check requirements
        possible_pile_count = len(final_positions)
        total_capacity = possible_pile_count * pile_candidate.capacity_per_pile
        required_capacity = criteria.required_total_load * criteria.safety_factor
        meets_requirement = total_capacity >= required_capacity
        utilization_ratio = criteria.required_total_load / total_capacity if total_capacity > 0 else float('inf')
        
        return {
            'grid_positions': final_positions,
            'possible_pile_count': possible_pile_count,
            'total_capacity': total_capacity,
            'required_capacity': required_capacity,
            'meets_capacity_requirement': meets_requirement,
            'utilization_ratio': utilization_ratio,
            'capacity_per_pile': pile_candidate.capacity_per_pile,
            'min_spacing': pile_candidate.min_spacing,
            'diameter': getattr(pile_candidate, 'diameter', None)  # Add diameter for BP visualization
        }
    
    def _attempt_multi_pile_solution(self, criteria: PileTypePreselectionCriteria,
                                   best_result: Dict[str, Any], capacity_gap: float,
                                   best_pile_type: str) -> Optional[PileTypePreselectionResult]:
        """
        Attempt to solve extreme load cases using multi-pile strategy.

        This method tries to place additional piles to meet capacity requirements
        when single pile types are insufficient.
        """
        try:
            required_capacity = criteria.required_total_load * criteria.safety_factor
            current_capacity = best_result.get('total_capacity', 0)
            pile_capacity = best_result.get('capacity_per_pile', 0)

            if pile_capacity <= 0:
                return None

            # Calculate how many additional piles we need
            additional_piles_needed = math.ceil(capacity_gap / pile_capacity)
            current_pile_count = best_result.get('possible_pile_count', 0)
            total_piles_needed = current_pile_count + additional_piles_needed

            self.log_callback(f"   Current piles: {current_pile_count}, Additional needed: {additional_piles_needed}")
            self.log_callback(f"   Total piles needed: {total_piles_needed}")

            # For extreme cases, create a synthetic solution with multiple pile clusters
            if total_piles_needed <= 10:  # Reasonable limit for multi-pile solutions
                # Create additional pile positions around the load center
                load_center = self._calculate_load_center_from_criteria(criteria)
                existing_positions = best_result.get('grid_positions', [])

                # Generate additional positions in a pattern around existing ones
                additional_positions = self._generate_additional_pile_positions(
                    existing_positions, load_center, additional_piles_needed,
                    best_result.get('min_spacing', 3.0)
                )

                # Combine all positions
                all_positions = existing_positions + additional_positions
                total_capacity = len(all_positions) * pile_capacity

                if total_capacity >= required_capacity:
                    self.log_callback(f"   Multi-pile solution: {len(all_positions)} piles, {total_capacity:.1f} kN capacity")

                    # Create a synthetic pile candidate for the result
                    synthetic_candidate = type('SyntheticCandidate', (), {
                        'pile_type': type('PileType', (), {'name': f"{best_pile_type}_MULTI"})(),
                        'capacity_per_pile': pile_capacity,
                        'min_spacing': best_result.get('min_spacing', 3.0),
                        'diameter': best_result.get('diameter'),
                        'display_name': f"{best_pile_type} (Multi-Pile Solution)"
                    })()

                    return PileTypePreselectionResult(
                        selected_pile_type=synthetic_candidate,
                        viable_grid_positions=all_positions,
                        total_possible_piles=len(all_positions),
                        total_capacity=total_capacity,
                        utilization_ratio=criteria.required_total_load / total_capacity,
                        initial_pile_cap=best_result.get('initial_pile_cap'),
                        enlarged_pile_cap=best_result.get('enlarged_pile_cap'),
                        local_coordinate_system=best_result.get('local_coordinate_system'),
                        evaluation_summary={f"{best_pile_type}_MULTI": {
                            'meets_capacity_requirement': True,
                            'total_capacity': total_capacity,
                            'possible_pile_count': len(all_positions),
                            'multi_pile_strategy': True
                        }},
                        all_pile_type_positions={f"{best_pile_type}_MULTI": all_positions},
                        all_possible_pile_boundaries={},
                        preselection_dxf_path=None
                    )

            return None

        except Exception as e:
            self.log_callback(f"   Multi-pile strategy failed: {e}")
            return None

    def _filter_by_possible_pile_boundary(self, positions: List[Point2D],
                                        possible_pile_boundary: Polygon) -> List[Point2D]:
        """Filter pile positions to only include those within the possible pile boundary."""
        log_function_entry(self.log_callback, "_filter_by_possible_pile_boundary",
                          input_positions=len(positions),
                          boundary_area=possible_pile_boundary.area)

        filtered_positions = []
        rejected_count = 0

        for pos in positions:
            point = Point(pos[0], pos[1])
            if point.within(possible_pile_boundary) or possible_pile_boundary.contains(point):
                filtered_positions.append(pos)
            else:
                rejected_count += 1

        # Log filtering results
        log_calculation_result(self.log_callback, "Input positions", len(positions))
        log_calculation_result(self.log_callback, "Filtered positions", len(filtered_positions))
        log_calculation_result(self.log_callback, "Rejected positions", rejected_count)

        if rejected_count > 0:
            rejection_rate = (rejected_count / len(positions)) * 100 if positions else 0
            log_performance_metric(self.log_callback, "Position rejection rate", rejection_rate, "%")
            enhanced_log(self.log_callback, f"Filtered out {rejected_count} positions outside pile boundary", 'DEBUG')
        else:
            enhanced_log(self.log_callback, "All positions within pile boundary", 'DEBUG')

        log_function_exit(self.log_callback, "_filter_by_possible_pile_boundary", f"{len(filtered_positions)} positions")
        return filtered_positions

    def _calculate_load_center_from_criteria(self, criteria: PileTypePreselectionCriteria) -> Point2D:
        """Calculate load center directly from criteria."""
        try:
            cluster_data = self._create_cluster_data_from_criteria(criteria)
            return self._calculate_load_center(cluster_data)
        except Exception:
            # Fallback to geometric center of elements
            all_positions = []
            if criteria.group_elements.get('columns'):
                for col in criteria.group_elements['columns']:
                    if len(col) >= 3:
                        all_positions.append((col[1], col[2]))
            if criteria.group_elements.get('walls'):
                for wall in criteria.group_elements['walls']:
                    if len(wall) >= 2 and isinstance(wall[1], list):
                        for point in wall[1]:
                            if isinstance(point, (list, tuple)) and len(point) >= 2:
                                all_positions.append((point[0], point[1]))

            if all_positions:
                avg_x = sum(pos[0] for pos in all_positions) / len(all_positions)
                avg_y = sum(pos[1] for pos in all_positions) / len(all_positions)
                return (avg_x, avg_y)
            else:
                return (0.0, 0.0)

    def _generate_additional_pile_positions(self, existing_positions: List[Point2D],
                                          load_center: Point2D, additional_count: int,
                                          min_spacing: float) -> List[Point2D]:
        """Generate additional pile positions around existing ones."""
        additional_positions = []

        if not existing_positions:
            # If no existing positions, create a grid around load center
            spacing = max(min_spacing, 3.0)
            positions_per_side = math.ceil(math.sqrt(additional_count))

            for i in range(additional_count):
                row = i // positions_per_side
                col = i % positions_per_side
                x = load_center[0] + (col - positions_per_side/2) * spacing
                y = load_center[1] + (row - positions_per_side/2) * spacing
                additional_positions.append((x, y))
        else:
            # Create positions around existing ones
            spacing = max(min_spacing, 3.0)

            # Calculate bounding box of existing positions
            min_x = min(pos[0] for pos in existing_positions)
            max_x = max(pos[0] for pos in existing_positions)
            min_y = min(pos[1] for pos in existing_positions)
            max_y = max(pos[1] for pos in existing_positions)

            # Expand the area and place additional piles
            for i in range(additional_count):
                angle = (2 * math.pi * i) / additional_count
                radius = spacing * (1 + i // 4)  # Spiral outward
                x = load_center[0] + radius * math.cos(angle)
                y = load_center[1] + radius * math.sin(angle)

                # Ensure minimum spacing from existing positions
                valid_position = True
                for existing_pos in existing_positions + additional_positions:
                    dist = math.sqrt((x - existing_pos[0])**2 + (y - existing_pos[1])**2)
                    if dist < spacing:
                        valid_position = False
                        break

                if valid_position:
                    additional_positions.append((x, y))
                else:
                    # Try alternative position
                    x = load_center[0] + (spacing * 2) * math.cos(angle + math.pi/4)
                    y = load_center[1] + (spacing * 2) * math.sin(angle + math.pi/4)
                    additional_positions.append((x, y))

        return additional_positions[:additional_count]

    def _create_cluster_data_from_criteria(self, criteria: PileTypePreselectionCriteria) -> dict:
        """Convert pre-selection criteria to the cluster_data format expected by Case 1/2 handlers."""
        log_function_entry(self.log_callback, "_create_cluster_data_from_criteria",
                          required_load=criteria.required_total_load)

        cluster_data = {
            'load_points': [],
            'elements': {
                'columns': [],
                'walls': []
            }
        }

        # Add columns if available - GroupElements is a TypedDict, access as dictionary
        num_columns = 0
        if criteria.group_elements.get('columns'):
            cluster_data['elements']['columns'] = criteria.group_elements['columns']
            num_columns = len(criteria.group_elements['columns'])
            log_calculation_result(self.log_callback, "Columns added to cluster data", num_columns)

        # Add walls if available - GroupElements is a TypedDict, access as dictionary
        num_walls = 0
        if criteria.group_elements.get('walls'):
            cluster_data['elements']['walls'] = criteria.group_elements['walls']
            num_walls = len(criteria.group_elements['walls'])
            log_calculation_result(self.log_callback, "Walls added to cluster data", num_walls)

        # Create synthetic load points from required load at structural element locations
        total_load = criteria.required_total_load
        log_algorithm_step(self.log_callback, "Cluster Data Creation", "Creating synthetic load points")
        log_calculation_result(self.log_callback, "Total load to distribute", f"{total_load:.1f} kN")

        if cluster_data['elements']['columns']:
            # For columns, place load at column location
            # Column format: (name, x, y, base_level)
            enhanced_log(self.log_callback, "Creating load points from column locations", 'DEBUG')
            for i, column in enumerate(cluster_data['elements']['columns']):
                if len(column) >= 3:
                    x, y = column[1], column[2]  # x and y are directly in positions 1 and 2
                    cluster_data['load_points'].append((x, y, total_load))
                    log_calculation_result(self.log_callback, f"Column {i+1} load point", f"({x:.2f}, {y:.2f}) with {total_load:.1f} kN")
                    break
        elif cluster_data['elements']['walls']:
            # For walls, place load at wall center
            enhanced_log(self.log_callback, "Creating load points from wall center", 'DEBUG')
            for i, wall in enumerate(cluster_data['elements']['walls']):
                if len(wall) >= 2 and isinstance(wall[1], list) and len(wall[1]) >= 2:
                    wall_points = wall[1]
                    if len(wall_points) >= 2:
                        start = wall_points[0]
                        end = wall_points[-1]
                        if (isinstance(start, (list, tuple)) and len(start) >= 2 and
                            isinstance(end, (list, tuple)) and len(end) >= 2):
                            center_x = (start[0] + end[0]) / 2
                            center_y = (start[1] + end[1]) / 2
                            cluster_data['load_points'].append((center_x, center_y, total_load))
                            log_calculation_result(self.log_callback, f"Wall {i+1} center load point",
                                                 f"({center_x:.2f}, {center_y:.2f}) with {total_load:.1f} kN")
                            break

        # Validate load points creation
        if not cluster_data['load_points']:
            error_msg = "No load points found - cannot determine load center - system must fail"
            log_validation_result(self.log_callback, "Load points creation", False, error_msg)
            log_error_with_context(self.log_callback, ValueError(error_msg), "cluster data creation")
            log_function_exit(self.log_callback, "_create_cluster_data_from_criteria", "Failed - No load points")
            raise ValueError(error_msg)
        else:
            log_validation_result(self.log_callback, "Load points creation", True, f"{len(cluster_data['load_points'])} load points created")
            log_performance_metric(self.log_callback, "Load points created", len(cluster_data['load_points']), "points")

        log_function_exit(self.log_callback, "_create_cluster_data_from_criteria", f"Success - {len(cluster_data['load_points'])} load points")
        return cluster_data
    
    def _calculate_load_center(self, cluster_data: dict) -> Point2D:
        """Calculate the load center from cluster data."""
        log_function_entry(self.log_callback, "_calculate_load_center")

        load_points = cluster_data.get('load_points', [])
        log_calculation_result(self.log_callback, "Available load points", len(load_points))

        if load_points:
            # Calculate weighted center of loads
            log_algorithm_step(self.log_callback, "Load Center Calculation", "Computing weighted center of loads")
            total_load = sum(point[2] for point in load_points if len(point) >= 3)
            log_calculation_result(self.log_callback, "Total load for weighting", f"{total_load:.1f} kN")

            if total_load > 0:
                center_x = sum(point[0] * point[2] for point in load_points if len(point) >= 3) / total_load
                center_y = sum(point[1] * point[2] for point in load_points if len(point) >= 3) / total_load

                log_calculation_result(self.log_callback, "Weighted load center X", f"{center_x:.3f} m")
                log_calculation_result(self.log_callback, "Weighted load center Y", f"{center_y:.3f} m")
                enhanced_log(self.log_callback, f"Load center calculated at ({center_x:.3f}, {center_y:.3f})", 'DEBUG')

                log_function_exit(self.log_callback, "_calculate_load_center", f"({center_x:.3f}, {center_y:.3f})")
                return (center_x, center_y)

        # Fallback: Use geometric center of structural elements
        enhanced_log(self.log_callback, "No valid load points found, using geometric center fallback", 'WARNING')
        log_algorithm_step(self.log_callback, "Load Center Calculation", "Using geometric center fallback")

        elements = cluster_data.get('elements', {})
        columns = elements.get('columns', [])
        walls = elements.get('walls', [])

        if columns:
            enhanced_log(self.log_callback, "Using first column position as load center", 'DEBUG')
            for i, column in enumerate(columns):
                if len(column) >= 3:
                    center = (column[1], column[2])  # x and y are directly in positions 1 and 2
                    log_calculation_result(self.log_callback, f"Column {i+1} geometric center", f"({center[0]:.3f}, {center[1]:.3f})")
                    log_function_exit(self.log_callback, "_calculate_load_center", f"Column fallback: ({center[0]:.3f}, {center[1]:.3f})")
                    return center

        if walls:
            enhanced_log(self.log_callback, "Using wall center as load center", 'DEBUG')
            wall_geometry = self._extract_wall_geometry(cluster_data)
            if wall_geometry:
                start_point, end_point, _, _ = wall_geometry
                center = ((start_point[0] + end_point[0]) / 2, (start_point[1] + end_point[1]) / 2)
                log_calculation_result(self.log_callback, "Wall geometric center", f"({center[0]:.3f}, {center[1]:.3f})")
                log_function_exit(self.log_callback, "_calculate_load_center", f"Wall fallback: ({center[0]:.3f}, {center[1]:.3f})")
                return center

        # Final fallback to origin
        enhanced_log(self.log_callback, "No structural elements found, using origin (0,0) as load center", 'WARNING')
        log_validation_result(self.log_callback, "Load center calculation", False, "Defaulting to origin")
        log_function_exit(self.log_callback, "_calculate_load_center", "Origin fallback: (0.0, 0.0)")
        return (0.0, 0.0)
    
    def _extract_wall_geometry(self, cluster_data: dict) -> Optional[tuple]:
        """Extract wall geometry from cluster data."""
        elements = cluster_data.get('elements', {})
        walls = elements.get('walls', [])
        
        if not walls:
            return None
        
        wall = walls[0]  # Use first wall
        if len(wall) >= 2 and isinstance(wall[1], list) and len(wall[1]) >= 2:
            wall_points = wall[1]
            if len(wall_points) >= 2:
                start = wall_points[0]
                end = wall_points[-1]
                if (isinstance(start, (list, tuple)) and len(start) >= 2 and
                    isinstance(end, (list, tuple)) and len(end) >= 2):
                    
                    start_point = (start[0], start[1])
                    end_point = (end[0], end[1])
                    
                    # Calculate wall direction and length
                    dx = end_point[0] - start_point[0]
                    dy = end_point[1] - start_point[1]
                    wall_length = math.sqrt(dx*dx + dy*dy)
                    
                    if wall_length > 0:
                        wall_dir = (dx/wall_length, dy/wall_length)
                        return start_point, end_point, wall_dir, wall_length
        
        return None
    
    def _adjust_bp_positions_to_boundary(self, positions: List[Point2D], 
                                       possible_pile_boundary: Polygon,
                                       wall_dir: Point2D) -> List[Point2D]:
        """Adjust BP positions to fit within the possible pile boundary."""
        adjusted_positions = []
        
        # Get boundary centroid as reference
        centroid = possible_pile_boundary.centroid
        center = (centroid.x, centroid.y)
        
        # For each original position, find the closest valid position within boundary
        for pos in positions:
            point = Point(pos[0], pos[1])
            if point.within(possible_pile_boundary):
                adjusted_positions.append(pos)
            else:
                # Project the point onto the boundary
                boundary_coords = list(possible_pile_boundary.exterior.coords)
                min_dist = float('inf')
                best_pos = center
                
                for i in range(len(boundary_coords) - 1):
                    # Find closest point on each boundary segment
                    seg_start = boundary_coords[i]
                    seg_end = boundary_coords[i + 1]
                    
                    # Project point onto line segment
                    closest_on_seg = self._project_point_to_segment(pos, seg_start, seg_end)
                    dist = math.sqrt((closest_on_seg[0] - pos[0])**2 + (closest_on_seg[1] - pos[1])**2)
                    
                    if dist < min_dist:
                        min_dist = dist
                        best_pos = closest_on_seg
                
                adjusted_positions.append(best_pos)
        
        return adjusted_positions
    
    def _project_point_to_segment(self, point: Point2D, seg_start: tuple, seg_end: tuple) -> Point2D:
        """Project a point onto a line segment."""
        px, py = point
        ax, ay = seg_start
        bx, by = seg_end
        
        # Vector from A to B
        abx = bx - ax
        aby = by - ay
        
        # Vector from A to P
        apx = px - ax
        apy = py - ay
        
        # Project AP onto AB
        ab_dot_ab = abx * abx + aby * aby
        
        if ab_dot_ab == 0:
            return (ax, ay)  # A and B are the same point
        
        ap_dot_ab = apx * abx + apy * aby
        t = ap_dot_ab / ab_dot_ab
        
        # Clamp t to [0, 1] to stay within segment
        t = max(0, min(1, t))
        
        # Return the projected point
        return (ax + t * abx, ay + t * aby)
    

    
    def _create_comprehensive_dxf(self,
                                criteria: PileTypePreselectionCriteria,
                                initial_pile_cap: Polygon,
                                maximum_pile_caps: Dict[str, Polygon],
                                all_possible_pile_boundaries: Dict[str, Polygon],
                                all_pile_type_positions: Dict[str, List[Point2D]],
                                selected_pile_type: Optional[PileTypeCandidate],
                                output_dir: Optional[str]) -> Optional[str]:
        """Create comprehensive DXF showing all pile type layouts."""
        from ..visualizer import create_comprehensive_pile_preselection_dxf

        # Get the maximum pile cap for the selected pile type, or use fallback
        selected_maximum_pile_cap = initial_pile_cap  # Default fallback
        if selected_pile_type:
            selected_maximum_pile_cap = maximum_pile_caps.get(
                selected_pile_type.pile_type.name,
                maximum_pile_caps.get('fallback', initial_pile_cap)
            )

        return create_comprehensive_pile_preselection_dxf(
            initial_pile_cap=initial_pile_cap,
            enlarged_pile_cap=selected_maximum_pile_cap,  # Pass the selected pile type's maximum cap
            all_possible_pile_boundaries=all_possible_pile_boundaries,
            all_pile_type_positions=all_pile_type_positions,
            selected_pile_type=selected_pile_type.pile_type.name if selected_pile_type else None,
            group_elements=criteria.group_elements,
            output_dir=output_dir
        )

def get_preselection_improvements_summary() -> str:
    """
    Summary of Pile Type Pre-Selection improvements according to user requirements and Pile_Layout_Rules.md
    
    Returns:
        str: Detailed summary of all improvements implemented
    """
    return """
PILE TYPE PRE-SELECTION IMPROVEMENTS ACCORDING TO PILE_LAYOUT_RULES.MD:

IMPLEMENTED REQUIREMENTS:
1. DXF VISUALIZATION ENHANCEMENTS:
   - ALL pre-selected pile positions and Maximum Pile Caps are now plotted in DXF
   - Each pile type's grid positions and boundaries are visible in separate layers
   - Layer-based organization: Red (DHP), Green (SHP), Magenta (BP), Yellow (Selected)
   - Maximum pile cap boundaries for each pile type plotted with proper labeling
   - Enhanced DXF layers setup for pile type boundaries

2. LOAD CENTER GRID POSITIONING:
   - Grid generation now ALWAYS includes Load Center (0,0) as first position
   - Grid is centered around load center ensuring connectivity
   - Load center is GUARANTEED to be in possible pile locations
   - Enhanced grid generation algorithm maintains load center priority

3. INDIVIDUAL PILE TYPE SELECTION:
   - Each Column Wall Cluster has its own selected Pile Type stored separately
   - Pre-selection engine evaluates pile types independently for each cluster
   - Individual evaluation results stored in preselection analysis
   - No longer treats all clusters as using the same pile type

4. CASE 1 BP POSITIONING:
   - BP pile is ALWAYS positioned exactly at Load Center coordinates
   - Enhanced logging shows exact BP placement coordinates
   - Enforced regardless of boundary constraints (with warnings)
   - Implements: "CRITICAL: BP pile MUST be positioned exactly at the Load Center coordinates"

5. ENHANCED BP CONSTRAINTS:
   - Case 1 BP: Maximum 1 pile constraint enforced
   - Case 2 BP: Wall length rule (pile diameter max 1 pile, else 2)
   - BP special rule handling for both cases with detailed logging
   - Enhanced error handling and validation

6. COMPREHENSIVE DXF GENERATION:
   - create_comprehensive_pile_preselection_dxf function enhanced
   - All pile type positions plotted with cross markers
   - Maximum pile cap boundaries for each pile type
   - Professional layer organization with color coding
   - Detailed annotations and legends

RULES COMPLIANCE:
Rule 1.2: "CRITICAL: The Load Center MUST be included as one of the Possible Pile Locations in the grid"
Rule 1.3: "CRITICAL: Each individual Column Wall Cluster shall have its own selected Pile Type stored separately"
Rule 1.4: "CRITICAL: BP pile MUST be positioned exactly at the Load Center coordinates"
Rule 1.5: "CRITICAL: ALL pre-selected pile positions and their corresponding Maximum Pile Caps MUST be plotted in DXF"
Rule 1.5: "CRITICAL: Each pile type's grid positions and boundaries MUST be visible in separate layers"

TECHNICAL IMPROVEMENTS:
Grid generation algorithm rewritten to prioritize load center
Enhanced BP positioning logic with comprehensive validation
Improved DXF visualization with all pile type data
Better error handling and logging throughout
Comprehensive evaluation summary for all pile types
Individual cluster-level pile type selection and storage

VISUALIZATION ENHANCEMENTS:
Layer-based DXF organization with proper color coding
Maximum pile cap boundaries plotted for each pile type
Cross markers for all possible pile positions
Professional annotations and labeling
Enhanced legend and title blocks
Proper scaling and dimension annotations
"""
