"""
Foundation-Automation SAFE Material Property Writer - Structural Material Definition System

This module provides comprehensive material property processing and export functionality
for converting structural material data into SAFE 16 and SAFE 22 format specifications.
It handles all material types used in structural engineering analysis including concrete,
steel, rebar, and post-tensioning tendons with their associated mechanical properties.

Key Features:
- Dual-format export supporting both SAFE 16 (legacy) and SAFE 22 (current) formats
- Comprehensive material type support for all structural engineering applications
- Material property validation and consistency checking across formats
- Mechanical property calculation and assignment for analysis requirements
- Design code compliance verification and property standardization
- Error handling and recovery for robust material processing workflows

Material Types Supported:
- Concrete: Compressive strength, elastic modulus, Poisson's ratio, unit weight
- Steel: Yield strength, ultimate strength, elastic modulus, thermal properties
- Rebar: Reinforcing steel properties, stress-strain characteristics, bond properties
- Tendon: Post-tensioning cable properties, relaxation characteristics, anchorage data
- General: Universal material properties applicable across all material types

Property Categories:
- General Properties: Basic material identification, unit weight, and classification
- Mechanical Properties: Strength, stiffness, and deformation characteristics
- Material-Specific Data: Specialized properties unique to each material type
- Design Parameters: Code-compliant values for structural design applications
- Analysis Settings: Properties required for finite element analysis accuracy

Format Compatibility:
- SAFE 16: Legacy format support with traditional material property structures
- SAFE 22: Enhanced format with improved material modeling capabilities
- Automatic format conversion ensuring consistency across software versions
- Validation of material property constraints and engineering limits

Workflow Integration:
- Processes Excel input data into SAFE-compatible material definitions
- Coordinates with section property modules for complete structural modeling
- Integrates with design code libraries for compliant material specifications
- Supports custom material definitions for specialized applications

Dependencies:
- build_fem.write_safe.safe16: SAFE 16 format-specific material property writers
- build_fem.write_safe.safe22: SAFE 22 format-specific material property writers
- Excel input data structures with validated material property specifications
- Design code libraries for material property standardization and compliance

Technical Notes:
- Material properties are validated against engineering limits and design codes
- Property assignments maintain consistency between analysis and design requirements
- Unit conversions are handled automatically for international design code support
- Error handling ensures robust processing of large material libraries
- Cross-referencing validates material property completeness and accuracy

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 structural analysis software
"""

from datetime import datetime

from build_fem.write_safe.safe16 import (
    _write_general_material_properties_safe16, _write_steel_materials_safe16,
    _write_concrete_materials_safe16, _write_rebar_materials_safe16, _write_tendon_materials_safe16
)
from build_fem.write_safe.safe22 import (
    material_MatPropGeneral_safe22, material_MatPropBasicMechProps_safe22,
    materials_MatPropSteelData_safe22, material_MatPropConcreteData_safe22,
    material_MatPropRebarData_safe22, material_MatPropTendonData_safe22
)


def write_material(excel_inputs, safe16_dfs, safe22_dfs):
    """
    Process and export comprehensive material property definitions for structural analysis.
    
    This function orchestrates the complete material property processing workflow,
    converting Excel-based material specifications into SAFE 16 and SAFE 22 format
    data structures. It handles all structural material types with their associated
    mechanical properties, design parameters, and analysis requirements.

    The material processing workflow includes:
    1. General material property validation and export for all material types
    2. Basic mechanical property calculation and assignment
    3. Material-specific property processing (concrete, steel, rebar, tendon)
    4. Cross-format compatibility validation and consistency checking
    5. Error handling and recovery for robust material library processing

    Material Property Categories Processed:
    - General Properties: Material identification, classification, and basic parameters
    - Mechanical Properties: Strength, stiffness, and deformation characteristics
    - Concrete Data: Compressive strength, elastic modulus, creep, and shrinkage parameters
    - Steel Data: Yield strength, ultimate strength, strain hardening, and thermal properties
    - Rebar Data: Reinforcing steel properties, stress-strain curves, and bond characteristics
    - Tendon Data: Post-tensioning properties, relaxation curves, and anchorage specifications
    """
    try:
        # Process foundational material properties applicable to all material types
        # This includes material identification, unit weight, and basic classification parameters
        safe16_dfs = _write_general_material_properties_safe16(excel_inputs, safe16_dfs)
        safe22_dfs, df_append = material_MatPropGeneral_safe22(excel_inputs, safe22_dfs)

        # Process fundamental mechanical properties for structural analysis
        # Includes elastic modulus, Poisson's ratio, thermal expansion, and shear modulus
        safe22_dfs, df_append = material_MatPropBasicMechProps_safe22(excel_inputs, safe22_dfs)

        # Process material-specific properties with specialized engineering parameters
        
        # Steel material processing: yield strength, ultimate strength, strain hardening
        # Handles structural steel, reinforcing steel, and specialty steel alloys
        safe16_dfs = _write_steel_materials_safe16(excel_inputs, safe16_dfs)
        safe22_dfs, df_append = materials_MatPropSteelData_safe22(excel_inputs, safe22_dfs)

        # Concrete material processing: compressive strength, tensile strength, creep/shrinkage
        # Supports normal weight, lightweight, and high-strength concrete specifications
        safe16_dfs = _write_concrete_materials_safe16(excel_inputs, safe16_dfs)
        safe22_dfs, df_append = material_MatPropConcreteData_safe22(excel_inputs, safe22_dfs)

        # Rebar material processing: reinforcing steel stress-strain characteristics
        # Includes bond properties, development length factors, and fatigue resistance
        safe16_dfs = _write_rebar_materials_safe16(excel_inputs, safe16_dfs)
        safe22_dfs, df_append = material_MatPropRebarData_safe22(excel_inputs, safe22_dfs)

        # Tendon material processing: post-tensioning cable properties and behavior
        # Handles relaxation characteristics, anchorage data, and prestress loss factors
        safe16_dfs = _write_tendon_materials_safe16(excel_inputs, safe16_dfs)
        safe22_dfs, df_append = material_MatPropTendonData_safe22(excel_inputs, safe22_dfs)        
        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Comprehensive error handling with detailed logging for material processing failures
        # Provides timestamp, error details, and graceful recovery to maintain workflow stability
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} ERROR: Material property processing failed with exception: {str(e)}")
        print(f"{now} INFO: Returning unmodified dataframes to maintain workflow continuity")
        print(f"{now} DEBUG: Check material data format and property specifications in Excel inputs")
        # Return unmodified dataframes to allow downstream processing to continue
        return safe16_dfs, safe22_dfs


