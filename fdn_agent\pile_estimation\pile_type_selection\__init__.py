﻿"""
Pile Type Selection Module

This module handles pile type pre-selection for foundation design.
It determines the most appropriate pile type (DHP, SHP, BP) BEFORE running 
NSGA-III optimization, based on capacity requirements and geometric constraints.

WORKFLOW:
1. Pre-select optimal pile type (DHP SHP BP priority)
2. Pass selected pile type to NSGA-III layout optimization
3. Generate optimized pile layout for the selected type

Author: Foundation Automation System
Date: June 14, 2025
"""

# Pile Type Pre-Selection functionality
from .pile_type_preselection import PileTypePreselector
from .core import IntegratedPileEstimationEngine, quick_pile_type_preselection
from .utils import PileTypeSelectionUtils

from ..data_types.pile_preselection_types import (
    PileTypePreselectionCriteria,
    PileTypeCandidate,
    PileTypePreselectionResult
)
from ..data_types.pile_types import PileType
from ..utils.pile_preselection_utils import (
    create_pile_type_candidates_from_gui,
    integrate_with_existing_capacity_calculation
)

# quick_pile_type_preselection is now imported from core module

__all__ = [
    # Main classes
    'PileTypePreselector',
    'IntegratedPileEstimationEngine',
    'PileTypeSelectionUtils',
    
    # Types
    'PileType',
    'PileTypePreselectionCriteria',
    'PileTypeCandidate', 
    'PileTypePreselectionResult',
    
    # Utility functions
    'create_pile_type_candidates_from_gui',
    'integrate_with_existing_capacity_calculation',
    'quick_pile_type_preselection'
]

