import numpy as np
import pandas as pd


class ExcelInputs:
    def __init__(self):
        # Property
        data = []

        self.Material = pd.DataFrame(data,
                                   columns=['Material (Text)', 'Type (Text)', 'E (kN/m2)', 'U (Unitless)', 'A (1/C)',
                                            'UnitWt (kN/m3)', 'Fc (kN/m2)', 'LtWtConc (Yes/No)', 'Fy (kN/m2)',
                                            'Fu (kN/m2)'])
        
        self.SoilProp = pd.DataFrame(data, columns=['Soil (Text)', 'Subgrade (kN/m3)'])

        self.BeamProp = pd.DataFrame(data, columns=['Beam (Text)', 'MatProp (Text)', 'Depth (m)', 'Width (m)'])
        
        self.ColumnProp = pd.DataFrame(data, columns=['Column (Text)', 'Type (Text)', 'MatProp (Text)', 'Diameter (m)',
                                                     'AutoRigid (Yes/No)', 'AutoDrop (Yes/No)', 'IncludeCap (Yes/No)',
                                                     'SecDim2 (m)', 'SecDim3 (m)', 'Area (m2)', 'As2 (m2)', 'As3 (m2)',
                                                     'J (m4)', 'I22 (m4)', 'I33 (m4)'])
        
        self.SlabProp = pd.DataFrame(
            data, columns=['Slab (Text)', 'MatProp (Text)', 'Thickness (m)'])

        # Geometry
        self.SiteBoundary = pd.DataFrame(columns=['X (m)', 'Y (m)'])
        self.Borehole = pd.DataFrame(columns=['Borehole', 'X (m)', 'Y (m)'])
        self.Point = pd.DataFrame(columns=['Point','X (m)', 'Y (m)'])
        self.Beam = pd.DataFrame(columns=['Beam', 'Beam Prop', 'Points'])
        self.Column = pd.DataFrame(columns=['Column', 'Center Point', 'Base Level', 'Points'])
        self.Wall = pd.DataFrame(columns=['Wall', 'Center Point', 'Points', 'Thickness (mm)', 'Base Level', 'Wall Group'])
        self.Slab = pd.DataFrame(columns=['Slab', 'Soil Prop', 'Slab Prop', 'Load Group', 'Slab Group', 'Points'])
        self.LKP = pd.DataFrame(columns=['LKP', 'Load Group', 'Points'])
        self.PointLoad = pd.DataFrame(columns=['Point Load', 'Point'])
        self.LineLoad = pd.DataFrame(columns=['Line Load', 'Points'])
        
        self.BP = pd.DataFrame()
        self.SHP = pd.DataFrame()
        self.DHP = pd.DataFrame()
        self.MP = pd.DataFrame()
        self.MPSpring = pd.DataFrame()
        self.Pile = pd.DataFrame()
        self.Ru = pd.DataFrame()

        # Geotechnical
        self.BoreholeSPT = pd.DataFrame(
            columns=['Borehole', 'SPT Level (mPD)', 'SPTN Value'])
        self.PileSoilSpringSetting = pd.DataFrame(
            columns=['Pile Mark', 'Borehole', 'Rx', 'Ry'])
        self.LateralSoilSpring = pd.DataFrame(
            columns=['Point Name', 'Pile Diameter (m)', 'X (m)', 'Y (m)', 'Z (m)',
                     'Nearest Pile X', 'Nearest Pile X Distance (m)', 'Nearest Pile Y', 'Nearest Pile Y Distance (m)',
                     'Borehole', 'True Z (mPD)', 'SPTN Value', 'Subgrade Reaction (kN/m3)',
                     'Rock Modulus of Deformation Em (GPa)', 'Factor for Em',
                     'Rx', 'Ry', 'Factor Dia',
                     'Distance from Ground Level (m)',
                     'Soil Delta Z(m)', 'Rock Delta Z (m)', 'Delta Z (m)',
                     'Soil Spring X (kN/m)', 'Rock Spring X (kN/m)',
                     'Soil Spring Y (kN/m)', 'Rock Spring Y (kN/m)',
                     'Spring X (kN/m)', 'Spring Y (kN/m)'])

        # Loading
        df_load_pattern = pd.DataFrame(np.array(
            [['SELF WEIGHT', 'SW'],
             ['DEAD', 'DL'],
             ['SUPERIMPOSED DEAD', 'SDL'],
             ['LIVE', 'LL'],
             ['SOIL', 'SL'],
             ['ANTICIPATED UPLIFT', 'Ua'],
             ['POSSIBLE UPLIFT', 'Up', ],
             ['NEGATIVE SKIN FRICTION', 'NSF']]),
            columns=['Load Type', 'LoadPat (Text)'])

        self.LoadPat = df_load_pattern
        self.LoadCase = pd.DataFrame()
        self.LoadComb = pd.DataFrame()
        self.InputLoadPoint = pd.DataFrame()
        self.InputLoadLine = pd.DataFrame()
        self.InputLoadBeam = pd.DataFrame()
        self.InputLoadPile = pd.DataFrame()
        self.InputLoadSlab = pd.DataFrame()
        self.InputLoadLKP = pd.DataFrame()
        # multi_cols = [('Column Data', 'Column Mark'),
        #               ('Column Data', 'Area (m2)'),
        #               ('Column Data', 'Center Point (Text)'),
        #               ('Column Data', 'Cap Thickness (m)'),
        #               ('Column Data', 'Area/Point Load (A/P)')]
        self.InputLoadColumn = pd.DataFrame()
        # multi_cols = [('Wall Data', 'Wall Mark'), ('Wall Data', 'Wall Length (m)'), ('Wall Data', 'Wall Theta (deg)'),
        #               ('Wall Data', 'Cap Thickness (m)')]
        self.InputLoadWall = pd.DataFrame()
        # multi_cols = [('CoreWall Data', 'CoreWall Mark'), ('CoreWall Data', 'CoreWall Length (m)'),
        #               ('CoreWall Data', 'Cap Thickness (m)')]
        self.InputLoadCoreWall = pd.DataFrame()

        # Steel Data
        self.SteelSectionH = pd.DataFrame()
        self.SteelGrade = pd.DataFrame()

        # Bored Pile rebar data
        header = ['Pile Mark', 'Target Utilization (<=1)', 'Diameter (m)', 'Cover (mm)', 'Rebar Clear Spacing (mm)',
                  'Max Layer 1 Rebar Num', 'Layer 1 Rebar Dia (mm)', 'Max Layer 2 Rebar Num', 'Layer 2 Rebar Dia (mm)',
                  'Max Layer 3 Rebar Num''	Layer 3 Rebar Dia (mm)'    'Length (m)', 'Fcu (MPa)', 'Fy (MPa)',
                  'Es (GPa)', 'Fyv (MPa)', 'Max Links Dia (mm)', 'Max Links Spacing (mm)', 'Max Links Legs'
                  ]
        self.UserBPRebar = pd.DataFrame(columns=header)

        df_bp_rebar = pd.DataFrame(
            columns=['Pile Mark', 'Part', 'BP Part Length (m)', 'Target Utilization (<=1)', 'Diameter (m)',
                     'Cover (mm)', 'Rebar Clear Spacing (mm)',
                     'Layer 1 Rebar Num', 'Layer 1 Rebar Dia (mm)',
                     'Layer 2 Rebar Num', 'Layer 2 Rebar Dia (mm)', 'Layer 3 Rebar Num',
                     'Layer 3 Rebar Dia (mm)', 'Steel Ratio (%)', 'Length (m)', 'Fcu (MPa)',
                     'Fy (MPa)', 'Es (GPa)', 'Rotational Angle (Empty or Deg)',
                     'Column Braced (Empty or "x")', 'Omit Slenderness Moment (Empty or "x")',
                     'Allow Angled Neutral Axis (Empty or "x")', 'Top Condition',
                     'Bottom Condition', 'Effective Length Factor (Beta)', 'Code', 'Fyv (MPa)',
                     'Links Dia (mm)', 'Links Spacing (mm)', 'Links Legs'])
        self.BPRebar = df_bp_rebar

        df_bp_segment_rebar = pd.DataFrame(
            columns=['Pile Mark', 'Part', 'Target Utilization (<=1)', 'Diameter (m)',
                     'Cover (mm)', 'Rebar Clear Spacing (mm)',
                     'Layer 1 Rebar Num', 'Layer 1 Rebar Dia (mm)',
                     'Layer 2 Rebar Num', 'Layer 2 Rebar Dia (mm)', 'Layer 3 Rebar Num',
                     'Layer 3 Rebar Dia (mm)', 'Steel Ratio (%)', 'Length (m)', 'Fcu (MPa)',
                     'Fy (MPa)', 'Es (GPa)', 'Rotational Angle (Empty or Deg)',
                     'Column Braced (Empty or "x")', 'Omit Slenderness Moment (Empty or "x")',
                     'Allow Angled Neutral Axis (Empty or "x")', 'Top Condition',
                     'Bottom Condition', 'Effective Length Factor (Beta)', 'Code', 'Fyv (MPa)',
                     'Links Dia (mm)', 'Links Spacing (mm)', 'Links Legs'])
        self.BPSegmentRebar = df_bp_segment_rebar


class ExcelOutputs:
    def __init__(self):
        self.PointLoad = pd.DataFrame()
        self.LineLoad = pd.DataFrame()
        self.BeamLoad = pd.DataFrame()
        self.ColumnLoad = pd.DataFrame()
        self.WallLoad = pd.DataFrame()
        self.CoreWallLoad = pd.DataFrame()
        self.PileULS = pd.DataFrame()
        self.PileSLS = pd.DataFrame()
        self.PileULSPDelta = pd.DataFrame()
        self.PileLocalXY = pd.DataFrame()
        self.PilePDelta = pd.DataFrame()
        self.LateralDisplacement = pd.DataFrame()


class SafeMdbs:
    def __init__(self):
        self.ElementForcesColumnsAndBraces = pd.DataFrame()
        self.NodalDisplacements = pd.DataFrame()
        self.PointCoord = pd.DataFrame()
        self.Line = pd.DataFrame()
        self.ColumnLocalAxes = pd.DataFrame()
        self.safe_version = ''


class SafeMdbs16:
    def __init__(self):
        self.ElementForcesColumnsAndBraces = pd.DataFrame()
        self.NodalDisplacements = pd.DataFrame()
        self.PointCoord = pd.DataFrame()
        self.ColumnLocalAxes = pd.DataFrame()


class SafeMdbs22:
    def __init__(self):
        self.ElementForcesColumns = pd.DataFrame()
        self.JointDisplacements = pd.DataFrame()
        self.PointObjectConnectivity = pd.DataFrame()
        self.FrameAssignsLocalAxes = pd.DataFrame()


class FilePaths:
    def __init__(self):
        self.ExistingExcelProperty = ''
        self.ExistingExcelGeometry = ''
        self.ExistingExcelGeology = ''
        self.ExistingExcelLoading = ''

        self.ExcelProperty = ''
        self.ExcelGeometry = ''
        self.ExcelGeology = ''
        self.ExcelLoading = ''

        self.ExcelSAFE16Model = ''
        self.f2kSAFE16Model = ''

        self.ExcelSAFE22Model = ''
        self.f2kSAFE22Model = ''

        self.ExcelOutputMP = ''
        self.ExcelOutputLoading = ''
        self.ExcelOutputPileULS = ''
        self.ExcelOutputPileSLS = ''
        self.ExcelOutputPileULSPDelta = ''
        self.ExcelOutputPileULSPDeltaCal = ''
        self.ExcelOutputSettlement = ''
        self.ExcelOutputPileLocalXY = ''
        self.ExcelOutputPilePDelta = ''

        self.ResultDesignLoad = ''
        self.ResultPileCapacity = ''
        self.ResultPilingSchedule = ''
        self.ResultBPShearCheck = ''
        self.ResultBPShearDesign = ''
        self.ResultSteelHPileCheckBD = ''
        self.ResultSteelHPileCheckASD = ''
        self.ResultSHPCheck = ''
        self.ResultDHPCheck = ''
        self.ResultProkonFolder = ''
        self.ResultBPNMFolder = ''
        self.ResultBPRebarLog = ''
        self.ResultBPSegmentNMFolder = ''
        self.ResultBPSegmentRebarLog = ''
        self.ResultPileDeflection = ''
        self.ResultPileDeflectionFail = ''
        self.ResultDifferentialSettlement = ''
        self.ResultDifferentialSettlementFail = ''
        self.ResultAngularRotation = ''
        self.ResultAngularRotationFail = ''

        self.ResultMdbSAFE = ''
        self.ResultMdbSAFEMovement = ''
        self.DataSteel = 'Library_Steel/SteelSection.xlsx'
        self.ExcelUserBPRebar = ''
        self.ExcelBPRebar = ''
        self.ExcelBPSegmentRebar = ''

        self.Log = ''


class DesignResults:
    def __init__(self):
        self.DesignLoad = pd.DataFrame()
        self.BPCapacity = pd.DataFrame()
        self.SHPCapacity = pd.DataFrame()
        self.DHPCapacity = pd.DataFrame()
        self.MPCapacity = pd.DataFrame()
        self.PileCapacity = pd.DataFrame()
        self.PilingSchedule = pd.DataFrame()
        self.BPShearCheck = pd.DataFrame()
        self.BPShearDesign = pd.DataFrame()
        self.BPSegmentRebarLog = pd.DataFrame()
        self.BPRebarLog = pd.DataFrame()
        self.SteelHPileCheck = pd.DataFrame()

        self.SHPULSStressCheckBD = pd.DataFrame()
        self.SHPULSVxCheckBD = pd.DataFrame()
        self.SHPULSVyCheckBD = pd.DataFrame()
        self.DHPULSStressCheckBD = pd.DataFrame()
        self.DHPULSVxCheckBD = pd.DataFrame()
        self.DHPULSVyCheckBD = pd.DataFrame()

        self.SHPULSStressCheckASDG = pd.DataFrame()
        self.SHPULSVxCheckASDG = pd.DataFrame()
        self.SHPULSVyCheckASDG = pd.DataFrame()
        self.DHPULSStressCheckASDG = pd.DataFrame()
        self.DHPULSVxCheckASDG = pd.DataFrame()
        self.DHPULSVyCheckASDG = pd.DataFrame()
        self.SHPULSStressCheckASDW = pd.DataFrame()
        self.SHPULSVxCheckASDW = pd.DataFrame()
        self.SHPULSVyCheckASDW = pd.DataFrame()
        self.DHPULSStressCheckASDW = pd.DataFrame()
        self.DHPULSVxCheckASDW = pd.DataFrame()
        self.DHPULSVyCheckASDW = pd.DataFrame()

        self.PileDeflection = pd.DataFrame()
        self.DifferentialSettlement = pd.DataFrame()
        self.PileNeedsPDelta = pd.DataFrame()