﻿"""
Foundation Pile Estimation Package

Comprehensive pile estimation system with AI-driven optimization and professional visualization.

MAIN ENTRY POINTS:
- coordinate_pile_estimation_workflow: Standard pile estimation workflow
- coordinate_pile_estimation_with_multi_type_optimization: AI-enhanced multi-type optimization
- IntegratedPileEstimationEngine: Advanced pile type pre-selection with NSGA-III

WORKFLOW ARCHITECTURE:
1. Data preprocessing and coordinate resolution
2. Intelligent element grouping with auto-threshold optimization
3. Load calculation and centroid analysis
4. AI pile type pre-selection
5. Layout generation (Case 1, 2, 4) with NSGA-III optimization
6. Professional AutoCAD DXF visualization

Author: Foundation Automation System
Date: June 14, 2025
"""

# Main workflow coordinators - PRIMARY ENTRY POINTS
from .pile_workflow_coordinator import (
    coordinate_pile_estimation_workflow,
    coordinate_pile_estimation_with_multi_type_optimization
)

# AI Pile Type Pre-Selection System
from .pile_type_selection.core import (
    IntegratedPileEstimationEngine,
    quick_pile_type_preselection
)

# Core components for advanced usage
from .clustering import (
    create_element_groups_with_auto_threshold,
    create_load_based_sub_clusters
)

from .load_calculator import (
    calculate_group_loads,
    calculate_load_centroids
)

from .pile_cap_geometry import (
    create_pile_cap_polygon,
    create_pile_cap_for_piles_and_structures
)

from .layout_generation.layout_engine import (
    generate_pile_layout,
    calculate_required_piles
)

from .optimization.nsga3_optimizer import (
    DEAPNSGA3Optimizer,
    DEAPNSGA3Config
)

from .visualizer import (
    create_pile_estimation_dxf,
    create_pile_estimation_dxf_with_possible_positions
)

# Utility functions
from .utils import (
    resolve_coordinates_from_excel,
    validate_pile_configuration,
    validate_structural_elements
)

# Core types
from .data_types import (
    Point2D,
    Point3D,
    ColumnData,
    WallData,
    GroupElements,
    PileGroupResult,
    PileLocation,
    LocalCoordinateSystem
)

# Exceptions
from .exceptions import (
    PileEstimationError,
    GroupingError,
    GeometryError,
    LoadCalculationError,
    PileLayoutError,
    PileTypeSelectionError,
    ValidationError,
    OptimizationError,
    VisualizationError
)

__version__ = "5.6.8"

__all__ = [
    # Main workflow coordinators
    'coordinate_pile_estimation_workflow',
    'coordinate_pile_estimation_with_multi_type_optimization',
    
    # AI pile type pre-selection
    'IntegratedPileEstimationEngine',
    'quick_pile_type_preselection',
    
    # Core components
    'create_element_groups_with_auto_threshold',
    'create_load_based_sub_clusters',
    'calculate_group_loads',
    'calculate_load_centroids',
    'create_pile_cap_polygon',
    'create_pile_cap_for_piles_and_structures',
    'generate_pile_layout',
    'calculate_required_piles',
    
    # Optimization
    'DEAPNSGA3Optimizer',
    'DEAPNSGA3Config',
    
    # Visualization
    'create_pile_estimation_dxf',
    'create_pile_estimation_dxf_with_possible_positions',
    
    # Utilities
    'resolve_coordinates_from_excel',
    'validate_pile_configuration',
    'validate_structural_elements',
    
    # Types
    'Point2D',
    'Point3D',
    'ColumnData',
    'WallData',
    'GroupElements',
    'PileGroupResult',
    'PileLocation',
    'LocalCoordinateSystem',
    
    # Exceptions
    'PileEstimationError',
    'GroupingError',
    'GeometryError',
    'LoadCalculationError',
    'PileLayoutError',
    'PileTypeSelectionError',
    'ValidationError',
    'OptimizationError',  
    'VisualizationError'
]

# Module metadata
__author__ = "Foundation Automation System"
__description__ = "Comprehensive pile estimation with AI optimization and professional visualization"

