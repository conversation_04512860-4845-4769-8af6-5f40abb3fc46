﻿"""
Configuration type definitions for pile estimation
"""

from dataclasses import dataclass
from typing import List, Tuple


@dataclass
class PileCapConfig:
    """Configuration parameters for pile cap generation."""
    edge_dist: float = 0.4      # m, minimum column and wall to edge distance
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        if self.edge_dist < 0:
            raise ValueError("edge_dist cannot be negative")


@dataclass
class LocalCoordinateSystem:
    """
    Local coordinate system for pile cap with optimized orientation.
    
    Attributes:
        origin: Origin point of the local coordinate system
        rotation_angle: Rotation angle in radians (from global x-axis to local x-axis)
        long_axis_length: Length of the long axis (local x-axis)
        short_axis_length: Length of the short axis (local y-axis)
        bounding_rect_corners: Corner points of the minimum area bounding rectangle in global coordinates
    """
    origin: Tuple[float, float]
    rotation_angle: float
    long_axis_length: float
    short_axis_length: float
    bounding_rect_corners: List[Tuple[float, float]]

