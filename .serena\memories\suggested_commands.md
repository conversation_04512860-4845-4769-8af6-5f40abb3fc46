# Suggested Commands for Development

## Python Environment
```powershell
# Install dependencies
pip install -r requirements.txt

# Run the main application
python main.py

# Run specific modules
python fdn_agent/agent_gui.py  # Foundation Agent GUI
python etabs_cwls/cwls_gui.py  # ETABS Loading Schedule
python build_fem/builder_gui.py  # SAFE Model Builder
```

## Windows System Commands
```powershell
# Directory navigation
Get-ChildItem  # List directory contents (ls equivalent)
Set-Location   # Change directory (cd equivalent)
Get-Content    # Display file contents (cat equivalent)

# File operations
Copy-Item      # Copy files
Move-Item      # Move/rename files
Remove-Item    # Delete files

# Text search
Select-String  # Search text in files (grep equivalent)
```

## Development Workflow
```powershell
# Testing
python -m pytest  # If tests are available

# Code formatting (if applicable)
python -m black .

# Linting (if applicable) 
python -m flake8 .
```

## Environment Variables
```powershell
# Set environment variables for testing
$env:BYPASS_AUTH_CHECK = "true"  # Bypass authentication for testing
```