﻿"""
Load Calculator Package

Simplified modular package for load calculations and pile estimation.
"""

# Basic load calculations
from .basic_calculations import (
    calculate_element_load,
    calculate_group_loads
)

# Centroid calculations
from .centroid_calculations import (
    calculate_load_centroids
)

# Pile calculations
from .pile_calculations import (
    calculate_required_piles,
    calculate_individual_pile_requirements
)

# Utility functions
from .utils import (
    calculate_load_with_custom_types,
    get_load_summary
)

# Backward compatibility aliases
calculate_total_load_from_axial = calculate_element_load

__all__ = [
    # Core functions
    'calculate_element_load',
    'calculate_group_loads',
    'calculate_load_centroids',
    'calculate_required_piles',
    'calculate_individual_pile_requirements',
    
    # Utility functions
    'calculate_load_with_custom_types',
    'get_load_summary',
    
    # Backward compatibility
    'calculate_total_load_from_axial'
]

