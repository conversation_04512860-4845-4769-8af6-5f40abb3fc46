from datetime import datetime

import pandas as pd

from read.functions import _load_sheet_data
from read.read_geometry import _identity_process


def read_input_property(excel_inputs, file_paths):
    path_excel_input = file_paths.ExcelProperty
    property_attributes = ['Concrete', 'Steel', 'Rebar', 'Tendon', 'Beam Prop', 'BP Prop', 'HP Prop', 'Slab Prop']
    header_single = [0]

    try:
        _load_sheet_data(path_excel_input, 'Concrete', excel_inputs, 'Concrete', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Steel', excel_inputs, 'Steel', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Rebar', excel_inputs, 'Rebar', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Tendon', excel_inputs, 'Tendon', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, '<PERSON>am Prop', excel_inputs, '<PERSON>am<PERSON>rop', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'BP Prop', excel_inputs, 'BPProp', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'HP Prop', excel_inputs, 'HPProp', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Slab Prop', excel_inputs, 'SlabProp', _identity_process,
                         header=header_single)
    except FileNotFoundError:
        # This means the Excel file itself was not found.
        # _load_sheet_data would have printed an error message already.
        print(
            f"Halting property reading: Excel file at '{path_excel_input}' not found. All property attributes will be empty.")
        for attr_name in property_attributes:
            setattr(excel_inputs, attr_name, pd.DataFrame())  # Ensure they are all empty
        return excel_inputs

    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Read Excel Input (Property)!')
    return excel_inputs
