# Complete SAFE Integration and FEM Building System

## SAFE API Integration (`safe_api/`)

### Main Orchestration (`main.py`)
- **Complete Workflow Automation**:
  - `automate_build_fem()`: Full FEM pipeline with soil springs
  - `automate_foundation_design()`: ULS/SLS calculations and design automation  
  - `automate_complete_workflow()`: End-to-end automation (FEM → Analysis → Design)
  - `process_safe_analysis_results()`: SQLite database processing
  - Command-line interface with comprehensive argparse support

### Core SAFE Integration Modules
- **`safe_connection.py`**: SAFE software connection management
- **`models.py`**: SAFE model setup, import, and analysis execution
- **`data_processor.py`**: SAFE result processing and DataFrame extraction
- **`database_manager.py`**: SQLite database operations for results storage
- **`config.py`**: SAFE API configuration and settings
- **`safe_environment.py`**: Environment setup and validation

## FEM Building System (`build_fem/`)

### Main Builder (`builder_main.py`)
- **Automated Initialization**: File paths, input data, existing data integration
- **Soil Spring Generation**: Geotechnical soil-pile interaction modeling
- **SAFE Model Generation**: Complete structural model creation with design strips
- **Progress Tracking**: Comprehensive logging and progress callbacks
- **Error Handling**: Robust error management with detailed diagnostics

### SAFE Model Writers (`write_safe/`)

#### **SAFE 16 Integration** (`safe16/`)
- **`safe16_class.py`**: SAFE 16 data structures and containers
- **`safe16_geometry.py`**: Geometric data processing and export
- **`safe16_material.py`**: Material property definition and management
- **`safe16_load.py`**: Load pattern and case processing
- **`safe16_load_comb.py`**: Load combination generation and management
- **`safe16_str_prop.py`**: Structural property definitions
- **`safe16_bp_shp_dhp.py`**: Pile element processing (BP/SHP/DHP)
- **`safe16_export.py`**: Model export and file generation

#### **SAFE 22 Integration** (`safe22/`)
- **`safe22_class.py`**: SAFE 22 modern data structures
- **`safe22_geometry.py`**: Advanced geometric processing
- **`safe22_material.py`**: Enhanced material definitions
- **`safe22_load.py`**: Advanced load processing
- **`safe22_load_comb.py`**: Sophisticated load combinations
- **`safe22_str_prop.py`**: Modern structural properties
- **`safe22_bp_shp_dhp.py`**: Advanced pile element handling
- **`safe22_export.py`**: Modern export capabilities

### Specialized Writers
- **`write_geometry.py`**: Master geometry processing
- **`write_material.py`**: Comprehensive material management
- **`write_load.py`**: Load processing and validation
- **`write_load_comb.py`**: Load combination logic
- **`write_str_prop.py`**: Structural property management
- **`write_bp_shp_dhp.py`**: Multi-pile type processing
- **`write_mp.py`**: Mini-pile specific processing
- **`write_soil.py`**: Soil spring and interaction modeling
- **`write_design_strip.py`**: Design strip generation for analysis
- **`write_load_output.py`**: Load output formatting

### Builder Components
- **`builder_gui.py`**: SAFE Model Builder GUI interface
- **`builder_safe.py`**: Core SAFE building engine
- **`builder_soil_spring.py`**: Soil spring generation automation
- **`functions.py`**: Common building utilities and helpers
- **`build_fem_config.py`**: FEM building configuration management

## Design Foundation System (`design_fdn/`)

### Main Design Engine (`designer_main.py`)
- **Complete Foundation Design Workflow**: ULS/SLS analysis and verification
- **Multi-pile Type Support**: BP, SHP, DHP, MP comprehensive design
- **SAFE Integration**: Direct result processing from SAFE analysis
- **Design Optimization**: Iterative refinement and section optimization
- **Code Compliance**: International foundation design codes (Eurocode 7, AASHTO)

### Design Components
- **`designer_gui.py`**: Foundation Design Checker GUI
- **`data_processing.py`**: Design data processing and validation
- **`pile_capacity.py`**: Comprehensive pile capacity calculations
- **`piling_schedule.py`**: Piling schedule generation and management
- **`initialization.py`**: Design system initialization
- **`error_handling.py`**: Design-specific error management

### ULS Design Modules (`uls_design_bp/`, `uls_design_hp/`)
- **Bored Pile ULS**: N-M-V interaction analysis, rebar design, shear checks
- **H-Pile ULS**: Steel design verification, stress checks, connection design
- **Advanced Analysis**: Prokon integration, database management, visualization

### SLS Design Modules (`sls_design/`)
- **`settlement.py`**: Settlement analysis and prediction
- **`deflection.py`**: Deflection calculations and limits
- **`diff_settlement.py`**: Differential settlement analysis
- **`angular_rotation.py`**: Angular rotation and tilt analysis

## System Integration Features
- **Dual Version Support**: SAFE 16 and SAFE 22 compatibility
- **Comprehensive Logging**: Multi-level logging with performance tracking
- **Progress Callbacks**: Real-time progress reporting for GUI integration
- **Error Recovery**: Robust error handling with detailed diagnostics
- **Database Integration**: SQLite storage for analysis results
- **Professional Output**: AutoCAD-compatible drawings and reports