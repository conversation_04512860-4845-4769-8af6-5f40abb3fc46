"""
Finite Element Method (FEM) Builder Package
This package provides tools for building FEM models, including:
- GUI interface for FEM model creation
- SAFE integration for structural analysis
- Soil spring modeling
- Main FEM builder functionality
- SAFE file writing utilities
"""

# Import specific functions and classes instead of using import *
try:
    from .builder_gui import SafeModelBuilderGUI, safe_model_builder
except ImportError:
    SafeModelBuilderGUI = None
    safe_model_builder = None

try:
    from .builder_main import (
        automated_initialize_setup,
        automated_generate_soil_spring,
        automated_generate_safe_model
    )
except ImportError:
    automated_initialize_setup = None
    automated_generate_soil_spring = None
    automated_generate_safe_model = None

try:
    from .builder_safe import gen_safe
except ImportError:
    gen_safe = None

try:
    from .builder_soil_spring import gen_soil_spring
except ImportError:
    gen_soil_spring = None

# Import configuration and functions
from . import build_fem_config
from . import functions

# Import submodules
from . import write_safe

__all__ = [
    # Submodules
    'build_fem_config',
    'functions',
    'write_safe',
    
    # Main classes and functions
    'SafeModelBuilderGUI',
    'safe_model_builder',
    'automated_initialize_setup',
    'automated_generate_soil_spring',
    'automated_generate_safe_model',
    'gen_safe',
    'gen_soil_spring'
]

__version__ = '1.0.0'