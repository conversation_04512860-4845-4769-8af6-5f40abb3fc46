"""
Bored Pile Moment-Axial Interaction Analysis Module for Ultimate Limit State (ULS) Design

This module provides comprehensive moment-axial (M-N) interaction analysis capabilities 
for bored pile foundations as part of Ultimate Limit State (ULS) design verification 
in reinforced concrete foundation engineering. It implements advanced analytical methods 
for evaluating the combined effects of axial forces and bending moments on circular 
reinforced concrete pile sections.

The module performs moment-axial interaction analysis including:
- M-N interaction diagram generation based on strain compatibility and force equilibrium
- Reinforcement optimization using binary search algorithms for efficient design
- Multi-layer reinforcement configuration with constructability constraints
- Parallel processing capabilities for large-scale foundation projects
- Database-driven curve caching for computational efficiency optimization

Key Features:
- Automated M-N curve calculation using fiber-based sectional analysis
- Intelligent reinforcement design with steel ratio optimization
- Multi-threaded processing for handling hundreds of pile segments
- Pre-calculated curve database for performance enhancement
- Comprehensive design validation against load combinations
- Detailed visualization and documentation output generation

Author: Foundation Design Automation Team
Version: 1.0
"""

import multiprocessing as mp
import os
import pickle
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable

import pandas as pd
from tqdm import tqdm

# Use direct imports to avoid circular import
from .circular_column import create_column, calculate_rebar_coordinates
from .nm_curve_calc import calculate_nm_curve, calculate_nm_point
from .rebar_design import design_optimal_rebar
from .nm_validation import filter_load_by_segment
from .nm_visualization import export_nm_check
from .nm_database import save_nm_curves_db, load_nm_curves_db, pre_calculate_nm_curves
from design_fdn.design_fdn_config import SHEET_BP_SEGMENT_REBAR, SHEET_BP_REBAR

# For backward compatibility, create aliases for legacy function names
# These aliases maintain compatibility with existing code while supporting refactoring
init_circular_column = create_column
cal_steel_ratio = lambda col: col.steel_ratio
cal_rebar_coord = lambda col: calculate_rebar_coordinates(col)
cal_nm_curve = calculate_nm_curve
cal_nm_curve_point = lambda col, x: calculate_nm_point(col, x)
design_rebar = design_optimal_rebar

# Global variables for multiprocessing worker initialization
# These variables are shared across worker processes to minimize memory usage
_nm_curves_db_global = None      # Pre-calculated M-N curves database
_pile_uls_df_global = None       # Ultimate limit state load combinations


def _init_worker(nm_db: Dict, pile_uls_df: pd.DataFrame) -> None:
    """
    Initialize multiprocessing worker with shared data for efficient parallel processing.
    
    This function sets up global variables in each worker process to avoid repeatedly
    serializing and transferring large datasets. The shared data includes pre-calculated
    M-N curves and load combination data that are accessed by multiple analysis tasks.
    
    Args:
        nm_db (Dict): Pre-calculated M-N curves database containing:
            - Column configuration keys mapped to steel ratio curves
            - Cached analysis results for performance optimization
        pile_uls_df (pd.DataFrame): Ultimate limit state load combinations containing:
            - Load cases with axial forces and bending moments
            - Critical load combinations for design verification
    
    Notes:
        - Called automatically during multiprocessing pool initialization
        - Global variables reduce inter-process communication overhead
        - Memory efficiency is improved through shared data structures
        - Worker processes inherit read-only access to shared datasets
    """
    global _nm_curves_db_global, _pile_uls_df_global
    _nm_curves_db_global = nm_db
    _pile_uls_df_global = pile_uls_df


def _process_segment_parallel(args: Tuple[int, Dict[str, Any], str]) -> Dict[str, Any]:
    """
    Process a single pile segment in parallel worker for M-N interaction design.
    
    This function performs complete M-N interaction analysis and reinforcement design
    for an individual pile segment within a multiprocessing framework. It implements
    the core design workflow including column creation, load filtering, reinforcement
    optimization, and design validation.
    
    The analysis workflow includes:
    1. Column object creation from segment geometry and material properties
    2. Load case filtering and critical load identification
    3. Iterative reinforcement design using optimization algorithms
    4. M-N interaction verification against ultimate limit state criteria
    5. Design documentation and visualization output generation
    
    Args:
        args (Tuple[int, Dict[str, Any], str]): Packed arguments containing:
            - index (int): DataFrame row index for result mapping
            - row_dict (Dict[str, Any]): Segment parameters including:
                * 'Pile Mark': Pile identifier string
                * 'Pile Segment': Segment identifier within pile
                * 'Fcu (MPa)': Concrete characteristic strength
                * 'Diameter (m)': Pile diameter in meters
                * 'Cover (mm)': Concrete cover to reinforcement
                * 'Layer X Rebar Num': Number of rebars in layer X
                * 'Layer X Rebar Dia (mm)': Rebar diameter in layer X
                * 'Rebar Clear Spacing (mm)': Clear spacing between rebars
                * 'Target Utilization (<=1)': Design utilization factor
            - output_folder (str): Directory path for result output files
    
    Returns:
        Dict[str, Any]: Analysis results containing:
            - 'index' (int): Original DataFrame row index
            - 'Layer1RebarNum' (int): Optimized layer 1 rebar count
            - 'Layer1RebarDia' (float): Layer 1 rebar diameter (mm)
            - 'Layer2RebarNum' (int): Optimized layer 2 rebar count
            - 'Layer2RebarDia' (float): Layer 2 rebar diameter (mm)
            - 'Layer3RebarNum' (int): Optimized layer 3 rebar count
            - 'Layer3RebarDia' (float): Layer 3 rebar diameter (mm)
            - 'steel_ratio' (float): Final steel reinforcement ratio
            - 'bool_pass' (bool): Design verification pass/fail status
    
    Notes:
        - Accesses global variables for shared data (curves database, loads)
        - Creates segment-specific output files for design documentation
        - Implements target utilization factor for optimization control
        - Returns comprehensive design parameters for result compilation
    
    Reinforced Concrete Design Context:
        The segment analysis implements standard reinforced concrete design procedures
        for circular sections under combined loading. The optimization process ensures
        minimum steel requirements while satisfying strength and constructability constraints.
    """
    # Unpack worker arguments
    index, row_dict, output_folder = args
    
    # Create circular column object from segment parameters
    # The column object encapsulates geometry, material properties, and reinforcement layout
    segment_mark = f"{row_dict['Pile Mark']}_{row_dict['Pile Segment']}"
    column = create_column(
        row_dict['Pile Mark'],                    # Pile identifier
        segment_mark,                             # Unique segment identifier
        row_dict['Fcu (MPa)'],                   # Concrete characteristic strength
        row_dict['Diameter (m)'] * 1000,         # Convert diameter to mm
        row_dict['Cover (mm)'],                  # Concrete cover to reinforcement
        row_dict['Layer 1 Rebar Num'],          # Initial layer 1 rebar count
        row_dict['Layer 1 Rebar Dia (mm)'],     # Layer 1 rebar diameter
        row_dict['Layer 2 Rebar Num'],          # Initial layer 2 rebar count
        row_dict['Layer 2 Rebar Dia (mm)'],     # Layer 2 rebar diameter
        row_dict['Layer 3 Rebar Num'],          # Initial layer 3 rebar count
        row_dict['Layer 3 Rebar Dia (mm)'],     # Layer 3 rebar diameter
        row_dict['Rebar Clear Spacing (mm)']    # Clear spacing between rebars
    )
    
    # Filter load combinations for this specific segment
    # Extract critical load cases affecting the current segment
    df_load_nm = filter_load_by_segment(_pile_uls_df_global, segment_mark)
    
    # Perform iterative reinforcement design optimization
    # Use target utilization factor to control design conservatism
    scale = row_dict.get('Target Utilization (<=1)', 0.9)  # Default utilization = 90%
    designed_column, df_nm_curve = design_optimal_rebar(
        column,                    # Initial column configuration
        df_load_nm,               # Critical load combinations
        scale,                    # Target utilization factor
        0.001,                    # Steel ratio iteration precision
        _nm_curves_db_global      # Pre-calculated curves database
    )
    
    # Validate design and generate output documentation
    # Export verification results and visualization materials
    bool_pass = export_nm_check(
        designed_column,          # Optimized column design
        df_nm_curve,             # M-N interaction curve
        df_load_nm,              # Applied load combinations
        scale,                   # Design utilization factor
        show=False,              # Suppress interactive display
        path_output_folder=output_folder,  # Output directory
        output_name=segment_mark  # File naming prefix
    )
    
    # Return comprehensive design results for compilation
    return {
        'index': index,
        'Layer1RebarNum': designed_column.layer1_rebar_num,
        'Layer1RebarDia': designed_column.layer1_rebar_dia,
        'Layer2RebarNum': designed_column.layer2_rebar_num,
        'Layer2RebarDia': designed_column.layer2_rebar_dia,
        'Layer3RebarNum': designed_column.layer3_rebar_num,
        'Layer3RebarDia': designed_column.layer3_rebar_dia,
        'steel_ratio': designed_column.steel_ratio,
        'bool_pass': bool_pass
    }


def design_all_bp_segment_nm(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any,
    design_results: Dict[str, Any], 
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Design all bored pile segments with comprehensive M-N curve interaction verification.
    
    This function orchestrates the complete M-N interaction design workflow for all pile
    segments in a foundation project. It implements parallel processing capabilities to
    efficiently handle large numbers of pile segments while maintaining design accuracy
    and generating comprehensive documentation.
    """
    # Extract segment reinforcement data and setup output directory
    df_bp_segment_rebar = excel_inputs.BPSegmentRebar.copy()
    output_folder = file_paths.ResultBPSegmentNMFolder
    
    # Create output directory with error handling
    os.makedirs(output_folder, exist_ok=True)
    
    # Load or create M-N curves database for performance optimization
    # The database caches pre-calculated curves to avoid redundant computations
    nm_db_path = os.path.join(output_folder, 'nm_curves_db.pkl')
    nm_curves_db = load_nm_curves_db(nm_db_path)
    
    # Identify unique column configurations to minimize curve calculations
    # Only generate new curves for configurations not in the database
    unique_columns = []
    seen_keys = set()
    
    for _, row in df_bp_segment_rebar.iterrows():
        # Create column object for configuration analysis
        column = create_column(
            row['Pile Mark'],
            f"{row['Pile Mark']}_{row['Pile Segment']}",
            row['Fcu (MPa)'],
            row['Diameter (m)'] * 1000,  # Convert to mm
            row['Cover (mm)'],
            row['Layer 1 Rebar Num'],
            row['Layer 1 Rebar Dia (mm)'],
            row['Layer 2 Rebar Num'],
            row['Layer 2 Rebar Dia (mm)'],
            row['Layer 3 Rebar Num'],
            row['Layer 3 Rebar Dia (mm)'],
            row['Rebar Clear Spacing (mm)']
        )
        
        # Generate unique key for configuration identification
        key = column.generate_key()
        if key not in seen_keys and key not in nm_curves_db:
            unique_columns.append(column)
            seen_keys.add(key)
    
    # Pre-calculate missing M-N curves for performance optimization
    if unique_columns:
        if log_callback:
            log_callback(f"Pre-calculating NM curves for {len(unique_columns)} unique configurations...")
        
        # Generate curves for new configurations
        new_curves = pre_calculate_nm_curves(unique_columns, progress_callback=log_callback)
        nm_curves_db.update(new_curves)
        
        # Save updated database for future use
        save_nm_curves_db(nm_curves_db, nm_db_path)
    
    # Process all segments using parallel computation
    if log_callback:
        log_callback(f"Processing {len(df_bp_segment_rebar)} pile segments...")
    
    # Prepare multiprocessing tasks with segment parameters
    tasks = [(idx, row.to_dict(), output_folder) 
             for idx, row in df_bp_segment_rebar.iterrows()]
    
    # Configure multiprocessing for optimal performance
    # Reserve one core for system operations to maintain responsiveness
    num_processes = max(1, (os.cpu_count() or 1) - 1)
    
    # Execute parallel processing with shared data initialization
    with mp.Pool(
        processes=num_processes,
        initializer=_init_worker,
        initargs=(nm_curves_db, excel_outputs.PileULS.copy())
    ) as pool:
        
        # Handle progress tracking based on logging availability
        use_tqdm = log_callback is None  # Use terminal progress bar if no callback
        completed_segments = 0
        total_segments = len(tasks)
        
        if use_tqdm:
            # Terminal-based progress tracking
            results = list(tqdm(
                pool.imap_unordered(_process_segment_parallel, tasks),
                total=total_segments,
                desc="Processing segments"
            ))
        else:
            # Callback-based progress tracking for GUI applications
            results = []
            for result in pool.imap_unordered(_process_segment_parallel, tasks):
                results.append(result)
                completed_segments += 1
                
                if log_callback:
                    progress_pct = int(100 * completed_segments / total_segments)
                    log_callback(f"Processing segments: {completed_segments}/{total_segments} ({progress_pct}%)")
    
    # Update dataframe with optimized design results
    for result in results:
        idx = result['index']
        # Update reinforcement configuration with optimized values
        df_bp_segment_rebar.loc[idx, 'Layer 1 Rebar Num'] = result['Layer1RebarNum']
        df_bp_segment_rebar.loc[idx, 'Layer 1 Rebar Dia (mm)'] = result['Layer1RebarDia']
        df_bp_segment_rebar.loc[idx, 'Layer 2 Rebar Num'] = result['Layer2RebarNum']
        df_bp_segment_rebar.loc[idx, 'Layer 2 Rebar Dia (mm)'] = result['Layer2RebarDia']
        df_bp_segment_rebar.loc[idx, 'Layer 3 Rebar Num'] = result['Layer3RebarNum']
        df_bp_segment_rebar.loc[idx, 'Layer 3 Rebar Dia (mm)'] = result['Layer3RebarDia']
        df_bp_segment_rebar.loc[idx, 'Steel Ratio (%)'] = result['steel_ratio']
        df_bp_segment_rebar.loc[idx, 'Pass'] = result['bool_pass']
    
    # Save comprehensive design results to project files
    design_results.BPSegmentRebarLog = df_bp_segment_rebar
    output_path = file_paths.ResultBPSegmentRebarLog
    
    # Create output dataframe excluding shear-related columns for clarity
    df_output = df_bp_segment_rebar.drop(
        columns=['Fyv (MPa)', 'Links Dia (mm)', 'Links Spacing (mm)', 'Links Legs'],
        errors='ignore'  # Ignore missing columns gracefully
    )
    
    # Export results to Excel with proper sheet naming
    df_output.to_excel(output_path, sheet_name=SHEET_BP_SEGMENT_REBAR, index=False)
    
    if log_callback:
        log_callback(f"Completed design for all segments. Results saved to {output_path}")
    
    return design_results


def group_bp_segment_rebar(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any,
    design_results: Dict[str, Any], 
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Group bored pile segments by maximum steel ratio for simplified construction.
    
    This function consolidates segment-level reinforcement designs into pile-level
    configurations by selecting the maximum steel ratio requirements across all
    segments within each pile. This approach simplifies construction by using
    uniform reinforcement throughout the pile length while ensuring adequate
    capacity for all loading conditions.
    
    The grouping process implements conservative design principles where the most
    critical segment requirements govern the entire pile design. This approach
    provides construction benefits through:
    - Simplified reinforcement detailing and procurement
    - Reduced construction complexity and potential errors
    - Consistent structural capacity throughout pile length
    - Enhanced quality control and inspection procedures
    """
    # Extract pile-level and segment-level reinforcement data
    df_bp_rebar = excel_inputs.BPRebar.copy()
    df_segment_rebar = design_results.BPSegmentRebarLog.copy()
    
    # Process each unique pile mark for consolidation
    for pile_mark in df_segment_rebar['Pile Mark'].unique():
        for part in ['A', 'B']:
            # Get all segments for the current pile
            mask = (df_segment_rebar['Pile Mark'] == pile_mark) & (df_segment_rebar['Part'] == part)

            max_idx = df_segment_rebar.loc[mask, 'Steel Ratio (%)'].idxmax()
            max_segment = df_segment_rebar.loc[max_idx]

            condition = (df_bp_rebar['Pile Mark'] == pile_mark) & (df_bp_rebar['Part'] == part)

            # Apply maximum segment requirements to entire pile
            df_bp_rebar.loc[condition, 'Layer 1 Rebar Num'] = max_segment['Layer 1 Rebar Num']
            df_bp_rebar.loc[condition, 'Layer 1 Rebar Dia (mm)'] = max_segment['Layer 1 Rebar Dia (mm)']
            df_bp_rebar.loc[condition, 'Layer 2 Rebar Num'] = max_segment['Layer 2 Rebar Num']
            df_bp_rebar.loc[condition, 'Layer 2 Rebar Dia (mm)'] = max_segment['Layer 2 Rebar Dia (mm)']
            df_bp_rebar.loc[condition, 'Layer 3 Rebar Num'] = max_segment['Layer 3 Rebar Num']
            df_bp_rebar.loc[condition, 'Layer 3 Rebar Dia (mm)'] = max_segment['Layer 3 Rebar Dia (mm)']
            df_bp_rebar.loc[condition, 'Steel Ratio (%)'] = max_segment['Steel Ratio (%)']

    # Filter and sort final results for construction documentation
    # Convert Pile Mark to string to handle mixed data types
    df_bp_rebar['Pile Mark'] = df_bp_rebar['Pile Mark'].astype(str)
    df_bp_rebar = df_bp_rebar.sort_values('Pile Mark')
    
    # Save consolidated results to design database
    design_results.BPRebarLog = df_bp_rebar
    output_path = file_paths.ResultBPRebarLog
    
    # Export to Excel with appropriate sheet naming
    df_bp_rebar.to_excel(output_path, sheet_name=SHEET_BP_REBAR, index=False)
    
    if log_callback:
        log_callback(f"Grouped segments by max steel ratio. Results saved to {output_path}")
    
    return design_results


def column_effective_length_factor(top_condition: str, bot_condition: str) -> float:
    """
    Calculate effective length factor (β) for column stability analysis based on end conditions.
    
    This function determines the effective length factor for reinforced concrete columns
    based on the fixity conditions at the top and bottom ends. The effective length
    factor is crucial for buckling analysis and slenderness calculations in accordance
    with structural design codes.
    
    The effective length factor accounts for:
    - Rotational restraint provided by end connections
    - Translation restraint at column supports
    - Interaction between column and supporting/supported members
    - Code-specified values for standard boundary conditions
    """
    # Standard effective length factor lookup table
    # Based on established structural engineering principles and code requirements
    beta_factors = {
        '1.Fully Fixed': {
            '1.Fully Fixed': 1.2,        # Maximum restraint configuration
            '2:Partially Fixed': 1.3,    # Moderate restraint variation
            '3.Pinned': 1.6              # Mixed restraint condition
        },
        '2:Partially Fixed': {
            '1.Fully Fixed': 1.3,        # Asymmetric partial restraint
            '2:Partially Fixed': 1.5,    # Symmetric partial restraint
            '3.Pinned': 1.8              # Reduced restraint condition
        },
        '3.Pinned': {
            '1.Fully Fixed': 1.6,        # Asymmetric minimal restraint
            '2:Partially Fixed': 1.8,    # Progressive restraint reduction
            '3.Pinned': 2.5              # Classical pin-ended condition
        },
        '4.free': {
            '1.Fully Fixed': 2.2         # Cantilever condition (one end free)
        }
    }
    
    # Lookup effective length factor with graceful handling of undefined combinations
    return beta_factors.get(top_condition, {}).get(bot_condition, 0)
