﻿"""
Core DXF visualization functions for pile estimation results.
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
import os
from pathlib import Path
import time
import ezdxf
from ezdxf import colors
from ezdxf.math import Vec3
import numpy as np
from shapely.geometry import Polygon

from ..data_types import Point2D, ColumnData, WallData, PileGroupResult
from ..exceptions import (
    VisualizationError,
    ValidationError,
    GeometryError,
    InputDataError
)
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)

from .text_manager import TextPlacementManager
from .utils import safe_get, extract_site_boundary_from_excel
from .validation import debug_group_results
from .dxf_setup import setup_layers, setup_units
from .plotters import (
    plot_site_boundary_dxf, plot_group_dxf, plot_columns_dxf, plot_walls_dxf,
    add_title_block_dxf, add_dimensions_dxf, plot_possible_pile_positions_dxf,
    plot_preselection_analysis_dxf
)


def create_pile_estimation_dxf_with_possible_positions(group_results: Dict[str, PileGroupResult],
                                                      possible_positions: List[Point2D] = None,
                                                      site_boundary: Optional[Polygon] = None,
                                                      columns: Optional[List[ColumnData]] = None,
                                                      walls: Optional[List[WallData]] = None,
                                                      save_path: Optional[str] = None,
                                                      excel_inputs: Optional[Any] = None,
                                                      pile_diameter: Optional[float] = None,
                                                      units: str = 'meters',
                                                      text_height: float = 0.5,
                                                      log_callback: Optional[Callable] = None) -> str:
    """
    Create comprehensive AutoCAD DXF visualization of pile estimation results with possible positions.
    
    This enhanced function displays both actual pile positions and possible pile positions
    for Case 3 algorithms that generate alternative placement options.
    
    Args:
        group_results: Dictionary of group results from pile estimation
        possible_positions: List of possible pile positions (for Case 3 visualization)
        site_boundary: Shapely Polygon representing site boundary
        columns: List of column data (optional)
        walls: List of wall data (optional)
        save_path: Path to save the DXF file (optional)
        excel_inputs: Excel inputs for detailed geometry (optional)
        pile_diameter: Pile diameter for circle representation (optional)        units: Drawing units ('meters', 'millimeters', 'feet', 'inches')
        text_height: Text height in drawing units
        log_callback: Logging callback function
        
    Returns:
        Path to the saved DXF file
        
    Raises:
        VisualizationError: If there's an error during visualization
        InputDataError: If input data is invalid or missing
    """
    log_function_entry(log_callback, "create_pile_estimation_dxf_with_possible_positions",
                      num_groups=len(group_results), 
                      num_possible_positions=len(possible_positions) if possible_positions else 0,
                      has_site_boundary=site_boundary is not None,
                      units=units)
    
    try:
        enhanced_log(log_callback, "=== DXF VISUALIZATION WITH POSSIBLE POSITIONS ===", 'INFO')
        
        with create_timed_logger(log_callback, "complete DXF visualization") as viz_timer:
            # Validate input data
            if not group_results:
                enhanced_log(log_callback, "No results provided for visualization", 'ERROR')
                log_constraint_check(log_callback, "Group results present", len(group_results), "> 0", False)
                raise InputDataError("No results provided for visualization")
            
            log_constraint_check(log_callback, "Group results present", len(group_results), "> 0", True)
            enhanced_log(log_callback, f"Visualizing {len(group_results)} groups", 'INFO')
            
            # Determine actual site boundary
            enhanced_log(log_callback, "Determining site boundary", 'DEBUG')
            actual_site_boundary = site_boundary
            if excel_inputs is not None:
                enhanced_log(log_callback, "Extracting site boundary from Excel inputs", 'DEBUG')
                excel_site_boundary = extract_site_boundary_from_excel(excel_inputs, log_callback)
                if excel_site_boundary is not None:
                    actual_site_boundary = excel_site_boundary
                    enhanced_log(log_callback, "Using Excel-derived site boundary", 'INFO')
                else:
                    enhanced_log(log_callback, "No site boundary found in Excel inputs", 'DEBUG')
            
            # Validate site boundary
            if actual_site_boundary is None or not isinstance(actual_site_boundary, Polygon):
                enhanced_log(log_callback, "No valid site boundary provided", 'ERROR')
                log_constraint_check(log_callback, "Valid site boundary", actual_site_boundary is not None, 
                                   "not None", False)
                raise InputDataError("No valid site boundary provided")
            if actual_site_boundary.is_empty:
                enhanced_log(log_callback, "Site boundary is empty", 'ERROR')
                log_constraint_check(log_callback, "Non-empty site boundary", 
                                   not actual_site_boundary.is_empty, "not empty", False)
                raise InputDataError("Site boundary is empty")
            
            log_validation_result(log_callback, "Site boundary validation", True, 
                                f"Valid boundary with area {actual_site_boundary.area:.3f}")
            
            enhanced_log(log_callback, f"Site boundary area: {actual_site_boundary.area:.3f} {units}²", 'DEBUG')
            
            # Create group ID to PC number mapping
            enhanced_log(log_callback, "Creating group ID to PC number mapping", 'DEBUG')
            group_to_pc = {group_id: f"PC{idx:02d}" 
                           for idx, group_id in enumerate(sorted(group_results.keys()), 1)}
            log_calculation_result(log_callback, "Group mappings created", len(group_to_pc))
            
            # Initialize text placement manager
            enhanced_log(log_callback, f"Initializing text manager with height {text_height}", 'DEBUG')
            text_manager = TextPlacementManager(text_height=text_height)
            
            # Create and setup DXF document            enhanced_log(log_callback, "Creating DXF document", 'INFO')
            with create_timed_logger(log_callback, "DXF document setup") as setup_timer:
                doc = ezdxf.new('R2010')
                msp = doc.modelspace()
                setup_layers(doc, units, text_height, log_callback)
                setup_units(doc, units, log_callback)
            
            enhanced_log(log_callback, "DXF document setup completed", 'DEBUG')
            
            # Plot main elements
            enhanced_log(log_callback, "Plotting site boundary", 'DEBUG')
            plot_site_boundary_dxf(msp, actual_site_boundary, log_callback)
            
            # Plot each group
            enhanced_log(log_callback, f"Plotting {len(group_results)} groups", 'INFO')
            for i, (group_id, data) in enumerate(group_results.items()):
                enhanced_log(log_callback, f"Plotting group {i+1}/{len(group_results)}: {group_id}", 'DEBUG')
                plot_group_dxf(msp, group_id, data, pile_diameter, None, 
                              group_to_pc[group_id], text_manager, log_callback)
              # Plot additional elements
            enhanced_log(log_callback, "Adding preselection analysis", 'DEBUG')
            plot_preselection_analysis_dxf(msp, group_results, text_manager, log_callback)
            
            # Plot possible positions if provided
            if possible_positions:
                enhanced_log(log_callback, f"Plotting {len(possible_positions)} possible pile positions", 'DEBUG')
                plot_possible_pile_positions_dxf(msp, possible_positions, pile_diameter, 
                                               text_manager, log_callback)
                log_calculation_result(log_callback, "Possible positions plotted", len(possible_positions))
            
            # Plot structural elements if provided
            if columns:
                enhanced_log(log_callback, f"Plotting {len(columns)} columns", 'DEBUG')
                plot_columns_dxf(msp, columns, excel_inputs, text_height, text_manager, log_callback)
                log_calculation_result(log_callback, "Columns plotted", len(columns))
            
            if walls:
                enhanced_log(log_callback, f"Plotting {len(walls)} walls", 'DEBUG')
                plot_walls_dxf(msp, walls, excel_inputs, text_height, text_manager, log_callback)
                log_calculation_result(log_callback, "Walls plotted", len(walls))
            
            # Add annotations
            enhanced_log(log_callback, "Adding title block and dimensions", 'DEBUG')
            add_title_block_dxf(msp, group_results, text_height, log_callback)
            add_dimensions_dxf(msp, actual_site_boundary, text_height, log_callback)
              # Generate file path and save
            enhanced_log(log_callback, "Generating file path and saving DXF", 'INFO')
            file_path = _generate_file_path(save_path, "pile_estimation", log_callback)
            _save_dxf_file(doc, file_path, log_callback)
            
            log_performance_metric(log_callback, "DXF file size", os.path.getsize(file_path), "bytes")
            enhanced_log(log_callback, f"DXF file saved successfully: {file_path}", 'INFO')
        
        log_function_exit(log_callback, "create_pile_estimation_dxf_with_possible_positions", 
                         result_path=file_path, 
                         file_size=os.path.getsize(file_path))
        
        return file_path
        
    except Exception as e:
        log_error_with_context(log_callback, "create_pile_estimation_dxf_with_possible_positions", e,
                              num_groups=len(group_results) if group_results else 0,
                              has_possible_positions=possible_positions is not None,
                              has_site_boundary=site_boundary is not None)
        raise VisualizationError(f"Failed to create DXF visualization: {e}") from e


def create_pile_estimation_dxf(group_results: Dict[str, PileGroupResult],
                              site_boundary: Optional[Polygon] = None,
                              columns: Optional[List[ColumnData]] = None,
                              walls: Optional[List[WallData]] = None,
                              save_path: Optional[str] = None,
                              excel_inputs: Optional[Any] = None,
                              pile_diameter: Optional[float] = None,
                              units: str = 'meters',
                              text_height: float = 0.5,
                              log_callback: Optional[Callable] = None) -> str:
    """
    Create comprehensive AutoCAD DXF visualization of pile estimation results.
    
    This function creates a detailed CAD drawing showing all aspects of the pile estimation:
    - Site boundary from Excel inputs (if available) or provided boundary
    - Pile caps for each group with color coding
    - Pile locations with proper symbols and circles
    - Load centroids and pile centroids with markers
    - Structural elements (columns and walls) if provided
    - Text annotations and dimensions
    
    Args:
        group_results: Dictionary of group results from pile estimation
        site_boundary: Shapely Polygon representing site boundary
        columns: List of column data (optional)
        walls: List of wall data (optional)
        save_path: Path to save the DXF file (optional)
        excel_inputs: Excel inputs for detailed geometry (optional)
        pile_diameter: Pile diameter for circle representation (optional)
        units: Drawing units ('meters', 'millimeters', 'feet', 'inches')
        text_height: Text height in drawing units
        log_callback: Logging callback function
        
    Returns:
        Path to the saved DXF file
        
    Raises:
        VisualizationError: If there's an error during visualization
        InputDataError: If input data is invalid or missing
    """
    log_function_entry(log_callback, "create_pile_estimation_dxf",
                      num_groups=len(group_results) if group_results else 0,
                      has_site_boundary=site_boundary is not None,
                      units=units)
    
    try:
        enhanced_log(log_callback, "=== DXF VISUALIZATION ===", 'INFO')
        
        with create_timed_logger(log_callback, "complete DXF visualization") as viz_timer:
            # Validate input data
            if not group_results:
                enhanced_log(log_callback, "No results provided for visualization", 'ERROR')
                log_constraint_check(log_callback, "Group results present", 0, "> 0", False)
                raise InputDataError("No results provided for visualization")
            
            log_constraint_check(log_callback, "Group results present", len(group_results), "> 0", True)
            enhanced_log(log_callback, f"Processing {len(group_results)} pile groups for visualization", 'INFO')

            # Determine actual site boundary
            enhanced_log(log_callback, "Determining site boundary", 'DEBUG')
            actual_site_boundary = site_boundary
            if excel_inputs is not None:
                enhanced_log(log_callback, "Extracting site boundary from Excel inputs", 'DEBUG')
                excel_site_boundary = extract_site_boundary_from_excel(excel_inputs, log_callback)
                if excel_site_boundary is not None:
                    actual_site_boundary = excel_site_boundary
                    enhanced_log(log_callback, "Using site boundary from Excel inputs", 'INFO')
                else:
                    enhanced_log(log_callback, "No site boundary found in Excel inputs", 'DEBUG')

            # Validate site boundary
            if actual_site_boundary is None or not isinstance(actual_site_boundary, Polygon):
                enhanced_log(log_callback, "No valid site boundary provided", 'ERROR')
                log_constraint_check(log_callback, "Valid site boundary", actual_site_boundary is not None, 
                                   "not None", False)
                raise InputDataError("No valid site boundary provided")
            if actual_site_boundary.is_empty:
                enhanced_log(log_callback, "Site boundary is empty", 'ERROR')
                log_constraint_check(log_callback, "Non-empty site boundary", 
                                   not actual_site_boundary.is_empty, "not empty", False)
                raise InputDataError("Site boundary is empty")

            log_validation_result(log_callback, "Site boundary validation", True, 
                                f"Valid boundary with area {actual_site_boundary.area:.3f}")

            # Create group ID to PC number mapping
            enhanced_log(log_callback, "Creating group ID to PC number mapping", 'DEBUG')
            group_to_pc = {group_id: f"PC{idx:02d}"
                           for idx, group_id in enumerate(sorted(group_results.keys()), 1)}
            log_calculation_result(log_callback, "Group mappings created", len(group_to_pc))

            # Initialize text placement manager
            enhanced_log(log_callback, f"Initializing text manager with height {text_height}", 'DEBUG')
            text_manager = TextPlacementManager(text_height=text_height)

            # Create and setup DXF document            enhanced_log(log_callback, "Creating DXF document and setting up layers", 'INFO')
            with create_timed_logger(log_callback, "DXF document setup") as setup_timer:
                doc = ezdxf.new('R2010')
                msp = doc.modelspace()
                setup_layers(doc, units, text_height, log_callback)
                setup_units(doc, units, log_callback)
            
            # Plot main elements
            enhanced_log(log_callback, "Drawing site boundary in DXF", 'DEBUG')
            plot_site_boundary_dxf(msp, actual_site_boundary, log_callback)

            # Plot each group
            enhanced_log(log_callback, f"Drawing {len(group_results)} pile groups in DXF", 'INFO')
            for group_id, data in group_results.items():
                enhanced_log(log_callback, f"Drawing group {group_id} ({group_to_pc[group_id]})", 'DEBUG')
                plot_group_dxf(msp, group_id, data, pile_diameter, None,
                              group_to_pc[group_id], text_manager, log_callback)

            # Plot additional elements
            enhanced_log(log_callback, "Adding preselection analysis to DXF", 'DEBUG')
            plot_preselection_analysis_dxf(msp, group_results, text_manager, log_callback)

            if columns:
                enhanced_log(log_callback, f"Drawing {len(columns)} columns in DXF", 'DEBUG')
                plot_columns_dxf(msp, columns, excel_inputs, text_height, text_manager, log_callback)
                log_calculation_result(log_callback, "Columns plotted", len(columns))

            if walls:
                enhanced_log(log_callback, f"Drawing {len(walls)} walls in DXF", 'DEBUG')
                plot_walls_dxf(msp, walls, excel_inputs, text_height, text_manager, log_callback)
                log_calculation_result(log_callback, "Walls plotted", len(walls))

            # Add annotations
            enhanced_log(log_callback, "Adding title block and dimensions to DXF", 'DEBUG')
            add_title_block_dxf(msp, group_results, text_height, log_callback)
            add_dimensions_dxf(msp, actual_site_boundary, text_height, log_callback)            # Generate file path and save
            enhanced_log(log_callback, "Generating file path and saving DXF", 'INFO')
            file_path = _generate_file_path(save_path, "pile_estimation", log_callback)
            _save_dxf_file(doc, file_path, log_callback)
            
            log_performance_metric(log_callback, "DXF file size", os.path.getsize(file_path), "bytes")
            enhanced_log(log_callback, f"DXF file saved to: {file_path}", 'INFO')
        
        log_function_exit(log_callback, "create_pile_estimation_dxf", 
                         result_path=file_path,
                         file_size=os.path.getsize(file_path))

        return file_path
        
    except Exception as e:
        log_error_with_context(log_callback, "create_pile_estimation_dxf", e,
                              num_groups=len(group_results) if group_results else 0,
                              has_site_boundary=site_boundary is not None)
        raise VisualizationError(f"Failed to create DXF visualization: {e}") from e


def create_comprehensive_pile_preselection_dxf(initial_pile_cap: Polygon,
                                             enlarged_pile_cap: Polygon,
                                             all_possible_pile_boundaries: Dict[str, Polygon],
                                             all_pile_type_positions: Dict[str, List[Point2D]],
                                             selected_pile_type: Optional[str],
                                             group_elements: Any,
                                             output_dir: Optional[str] = None,
                                             log_callback: Optional[Callable] = None) -> Optional[str]:
    """
    Create comprehensive DXF visualization showing all pile type layouts and boundaries.
    
    This function creates a detailed DXF drawing showing:
    - Initial pile cap (user edge distance)
    - Enlarged pile cap (with 1m offset)
    - Possible pile boundaries for each pile type (enlarged cap - pile radius - edge distance)
    - All pile type grid positions (DHP, SHP, BP)
    - Selected pile type highlighted
    - Load center and structural elements
    
    Args:
        initial_pile_cap: Initial pile cap polygon
        enlarged_pile_cap: Enlarged pile cap polygon  
        all_possible_pile_boundaries: Dict of possible pile boundaries for each pile type
        all_pile_type_positions: Dict of pile positions for each pile type
        selected_pile_type: Name of the selected pile type (e.g., 'DHP', 'SHP', 'BP')
        group_elements: Structural elements (columns/walls)
        output_dir: Directory to save DXF file
        log_callback: Logging callback function
        
    Returns:
        Path to saved DXF file or None if failed
    """
    log_function_entry(log_callback, "create_comprehensive_pile_preselection_dxf",
                      num_pile_types=len(all_pile_type_positions),
                      selected_type=selected_pile_type,
                      has_output_dir=output_dir is not None)
    
    try:
        enhanced_log(log_callback, "=== COMPREHENSIVE PILE PRESELECTION DXF ===", 'INFO')
        
        with create_timed_logger(log_callback, "preselection DXF creation") as viz_timer:
            import ezdxf
            import time
            from pathlib import Path
            
            # Validate input data
            if not all_pile_type_positions:
                enhanced_log(log_callback, "No pile type positions provided", 'ERROR')
                log_constraint_check(log_callback, "Pile type positions present", 0, "> 0", False)
                return None
            
            log_constraint_check(log_callback, "Pile type positions present", 
                               len(all_pile_type_positions), "> 0", True)
            enhanced_log(log_callback, f"Processing {len(all_pile_type_positions)} pile types", 'INFO')
            
            # Create DXF document
            enhanced_log(log_callback, "Creating DXF document", 'DEBUG')
            doc = ezdxf.new('R2010')
            msp = doc.modelspace()
            
            # Setup layers with specific colors for each pile type
            enhanced_log(log_callback, "Setting up layers and colors", 'DEBUG')
            pile_type_colors = {
                'DHP': 1,  # Red
                'SHP': 3,  # Green  
                'BP': 5,   # Blue
            }
            
            # Create layers
            with create_timed_logger(log_callback, "layer creation") as layer_timer:
                layer_definitions = [
                    ('PILE_CAP_INITIAL', 8),  # Gray
                    ('PILE_CAP_ENLARGED', 7),  # White
                    ('POSSIBLE_BOUNDARY_DHP', 11),  # Light Red
                    ('POSSIBLE_BOUNDARY_SHP', 13),  # Light Green
                    ('POSSIBLE_BOUNDARY_BP', 15),   # Light Blue
                    ('PILES_DHP', pile_type_colors['DHP']),
                    ('PILES_SHP', pile_type_colors['SHP']),
                    ('PILES_BP', pile_type_colors['BP']),
                    ('SELECTED_PILES', 2),  # Yellow
                    ('ELEMENTS', 6),  # Magenta
                    ('TEXT', 7),  # White
                    ('LOAD_CENTER', 1),  # Red
                ]
                
                for layer_name, color in layer_definitions:
                    doc.layers.new(layer_name, dxfattribs={'color': color})
                
                log_calculation_result(log_callback, "Layers created", len(layer_definitions))
            
            # Draw pile caps
            enhanced_log(log_callback, "Drawing pile caps", 'DEBUG')
            if initial_pile_cap and not initial_pile_cap.is_empty:
                coords = list(initial_pile_cap.exterior.coords)
                msp.add_lwpolyline(
                    points=[(x, y) for x, y in coords],
                    close=True,
                    dxfattribs={'layer': 'PILE_CAP_INITIAL'}
                )
                
                # Add label
                centroid = initial_pile_cap.centroid
                msp.add_text(
                    "Initial Pile Cap",
                    dxfattribs={'layer': 'TEXT', 'height': 0.3}
                ).set_pos((centroid.x, centroid.y - 1.0))
                
                log_calculation_result(log_callback, "Initial pile cap area", initial_pile_cap.area)
            
            if enlarged_pile_cap and not enlarged_pile_cap.is_empty:
                coords = list(enlarged_pile_cap.exterior.coords)
                msp.add_lwpolyline(
                    points=[(x, y) for x, y in coords],
                    close=True,
                    dxfattribs={'layer': 'PILE_CAP_ENLARGED'}
                )
                
                # Add label
                centroid = enlarged_pile_cap.centroid
                msp.add_text(
                    "Enlarged Pile Cap (+1m)",
                    dxfattribs={'layer': 'TEXT', 'height': 0.3}
                ).set_pos((centroid.x, centroid.y - 1.5))
                
                log_calculation_result(log_callback, "Enlarged pile cap area", enlarged_pile_cap.area)
            
            # Draw possible pile boundaries for each pile type
            enhanced_log(log_callback, "Drawing possible pile boundaries", 'DEBUG')
            for pile_type, boundary in all_possible_pile_boundaries.items():
                if boundary and not boundary.is_empty:
                    layer_name = f'POSSIBLE_BOUNDARY_{pile_type}'
                    coords = list(boundary.exterior.coords)
                    msp.add_lwpolyline(
                        points=[(x, y) for x, y in coords],
                        close=True,
                        dxfattribs={'layer': layer_name}
                    )
                    
                    # Add label
                    centroid = boundary.centroid
                    msp.add_text(
                        f"{pile_type} Possible Boundary",
                        dxfattribs={'layer': 'TEXT', 'height': 0.25}
                    ).set_pos((centroid.x, centroid.y - 2.0 - list(all_possible_pile_boundaries.keys()).index(pile_type) * 0.5))
                    
                    log_calculation_result(log_callback, f"{pile_type} boundary area", boundary.area)
            
            # Draw pile positions for each pile type
            enhanced_log(log_callback, "Drawing pile positions for all types", 'DEBUG')
            total_piles_drawn = 0
            for pile_type, positions in all_pile_type_positions.items():
                if positions:
                    layer_name = f'PILES_{pile_type}'
                    is_selected = pile_type == selected_pile_type
                    
                    enhanced_log(log_callback, f"Drawing {len(positions)} {pile_type} piles", 'DEBUG')
                    for i, pos in enumerate(positions):
                        # Draw pile as circle
                        circle_layer = 'SELECTED_PILES' if is_selected else layer_name
                        msp.add_circle(
                            center=(pos[0], pos[1]),
                            radius=0.3,  # Standard pile radius for visualization
                            dxfattribs={'layer': circle_layer}
                        )
                        
                        # Add pile number
                        msp.add_text(
                            f"{pile_type}{i+1}",
                            dxfattribs={'layer': 'TEXT', 'height': 0.2}
                        ).set_pos((pos[0] + 0.4, pos[1] + 0.4))
                    
                    total_piles_drawn += len(positions)
                    
                    # Add pile type summary
                    if positions:
                        first_pos = positions[0]                    # Add pile type summary
                    if positions:
                        first_pos = positions[0]
                        summary_text = f"{pile_type}: {len(positions)} piles"
                        if is_selected:
                            summary_text += " (SELECTED)"
                        
                        msp.add_text(
                            summary_text,
                            dxfattribs={'layer': 'TEXT', 'height': 0.4}
                        ).set_pos((first_pos[0] - 3, first_pos[1] + 2 + list(all_pile_type_positions.keys()).index(pile_type) * 0.7))
                    
                    log_calculation_result(log_callback, f"{pile_type} piles drawn", len(positions))
            
            log_calculation_result(log_callback, "Total piles drawn", total_piles_drawn)
            
            # Draw structural elements
            enhanced_log(log_callback, "Drawing structural elements", 'DEBUG')
            elements_drawn = 0
            if hasattr(group_elements, 'columns') and group_elements.columns:
                for column in group_elements.columns:
                    if len(column) >= 2 and isinstance(column[1], (list, tuple)) and len(column[1]) >= 2:
                        x, y = column[1][0], column[1][1]
                        # Draw column as square
                        size = 0.5
                        points = [
                            (x - size/2, y - size/2),
                            (x + size/2, y - size/2),
                            (x + size/2, y + size/2),
                            (x - size/2, y + size/2)
                        ]
                        msp.add_lwpolyline(
                            points=points,
                            close=True,
                            dxfattribs={'layer': 'ELEMENTS'}
                        )
                        
                        # Add column label
                        msp.add_text(
                            "COL",
                            dxfattribs={'layer': 'TEXT', 'height': 0.2}
                        ).set_pos((x - 0.2, y - 0.1))
                        elements_drawn += 1
            
            if hasattr(group_elements, 'walls') and group_elements.walls:
                for wall in group_elements.walls:
                    if len(wall) >= 2 and isinstance(wall[1], list) and len(wall[1]) >= 2:
                        wall_points = wall[1]
                        if len(wall_points) >= 2:
                            for i in range(len(wall_points) - 1):
                                start = wall_points[i]
                                end = wall_points[i + 1]
                                if (isinstance(start, (list, tuple)) and len(start) >= 2 and
                                    isinstance(end, (list, tuple)) and len(end) >= 2):
                                    msp.add_line(
                                        start=(start[0], start[1]),
                                        end=(end[0], end[1]),
                                        dxfattribs={'layer': 'ELEMENTS'}
                                    )
                            
                            # Add wall label at center
                            start = wall_points[0]
                            end = wall_points[-1]
                            if (isinstance(start, (list, tuple)) and len(start) >= 2 and
                                isinstance(end, (list, tuple)) and len(end) >= 2):
                                center_x = (start[0] + end[0]) / 2
                                center_y = (start[1] + end[1]) / 2
                                msp.add_text(
                                    "WALL",
                                    dxfattribs={'layer': 'TEXT', 'height': 0.2}
                                ).set_pos((center_x, center_y + 0.3))
                                elements_drawn += 1
            
            log_calculation_result(log_callback, "Structural elements drawn", elements_drawn)
            
            # Calculate and draw load center
            enhanced_log(log_callback, "Drawing load center", 'DEBUG')
            load_center = _calculate_load_center_from_elements(group_elements)
            if load_center:
                # Draw load center as cross
                size = 0.4
                msp.add_line(
                    start=(load_center[0] - size, load_center[1]),
                    end=(load_center[0] + size, load_center[1]),
                    dxfattribs={'layer': 'LOAD_CENTER'}
                )
                msp.add_line(
                    start=(load_center[0], load_center[1] - size),
                    end=(load_center[0], load_center[1] + size),
                    dxfattribs={'layer': 'LOAD_CENTER'}
                )
                
                # Add label
                msp.add_text(
                    "Load Center",
                    dxfattribs={'layer': 'TEXT', 'height': 0.3}
                ).set_pos((load_center[0] + 0.6, load_center[1] + 0.6))
                
                enhanced_log(log_callback, f"Load center at ({load_center[0]:.2f}, {load_center[1]:.2f})", 'DEBUG')
            
            # Add title and legend
            enhanced_log(log_callback, "Adding title and legend", 'DEBUG')
            title_y = 10
            if enlarged_pile_cap and not enlarged_pile_cap.is_empty:
                bounds = enlarged_pile_cap.bounds
                title_y = bounds[3] + 3  # Top of enlarged pile cap + 3m
            
            msp.add_text(
                "Pile Type Pre-Selection Analysis",
                dxfattribs={'layer': 'TEXT', 'height': 0.8}
            ).set_pos((-5, title_y))
            
            # Add legend
            legend_x = -5
            legend_y = title_y - 1.5
            
            legend_items = [
                ("Initial Pile Cap", 'PILE_CAP_INITIAL'),
                ("Enlarged Pile Cap (+1m)", 'PILE_CAP_ENLARGED'),
                ("DHP Possible Boundary", 'POSSIBLE_BOUNDARY_DHP'),
                ("SHP Possible Boundary", 'POSSIBLE_BOUNDARY_SHP'), 
                ("BP Possible Boundary", 'POSSIBLE_BOUNDARY_BP'),
                ("DHP Piles", 'PILES_DHP'),
                ("SHP Piles", 'PILES_SHP'),
                ("BP Piles", 'PILES_BP'),
                ("Selected Piles", 'SELECTED_PILES'),
                ("Structural Elements", 'ELEMENTS'),
                ("Load Center", 'LOAD_CENTER')
            ]
            
            for i, (label, layer) in enumerate(legend_items):
                y_pos = legend_y - i * 0.4
                # Draw sample line
                msp.add_line(
                    start=(legend_x, y_pos),
                    end=(legend_x + 1, y_pos),
                    dxfattribs={'layer': layer}
                )
                # Add label
                msp.add_text(
                    label,
                    dxfattribs={'layer': 'TEXT', 'height': 0.25}
                ).set_pos((legend_x + 1.5, y_pos - 0.1))
            
            # Generate filename and save
            enhanced_log(log_callback, "Saving DXF file", 'INFO')
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            if output_dir:
                output_path = Path(output_dir) / f"pile_preselection_analysis_{timestamp}.dxf"
                output_path.parent.mkdir(parents=True, exist_ok=True)
            else:
                output_path = Path(f"pile_preselection_analysis_{timestamp}.dxf")
            
            # Save DXF file
            doc.saveas(str(output_path))
            
            file_size = os.path.getsize(str(output_path))
            log_performance_metric(log_callback, "Preselection DXF file size", file_size, "bytes")
            enhanced_log(log_callback, f"Preselection DXF saved: {output_path}", 'INFO')
        
        log_function_exit(log_callback, "create_comprehensive_pile_preselection_dxf", 
                         result_path=str(output_path),
                         file_size=file_size)
        
        return str(output_path)
        
    except Exception as e:
        log_error_with_context(log_callback, "create_comprehensive_pile_preselection_dxf", e,
                              num_pile_types=len(all_pile_type_positions) if all_pile_type_positions else 0,
                              selected_type=selected_pile_type)
        enhanced_log(log_callback, f"Error creating comprehensive preselection DXF: {e}", 'ERROR')
        return None


def _generate_file_path(save_path: Optional[str], prefix: str, log_callback: Optional[Callable] = None) -> str:
    """
    Generate appropriate file path for saving DXF files.
    
    Args:
        save_path: User-provided save path (file or directory)
        prefix: File prefix for generated names
        log_callback: Logging callback function
        
    Returns:
        Complete file path for saving
    """
    log_function_entry(log_callback, "_generate_file_path", 
                      save_path=save_path, prefix=prefix)
    
    try:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        if save_path:
            save_path_obj = Path(save_path)
            
            # If it's a directory or looks like a directory path
            if save_path_obj.is_dir() or (not save_path_obj.suffix and not save_path_obj.exists()):
                result_path = str(save_path_obj / f"{prefix}_{timestamp}.dxf")
                enhanced_log(log_callback, f"Generated path in directory: {result_path}", 'DEBUG')
            else:
                # It's already a complete file path
                result_path = save_path
                enhanced_log(log_callback, f"Using provided file path: {result_path}", 'DEBUG')
        else:
            # No path provided, use current directory
            result_path = f"{prefix}_{timestamp}.dxf"
            enhanced_log(log_callback, f"Generated path in current directory: {result_path}", 'DEBUG')
        
        log_function_exit(log_callback, "_generate_file_path", result_path=result_path)
        return result_path
        
    except Exception as e:
        log_error_with_context(log_callback, "_generate_file_path", e,
                              save_path=save_path, prefix=prefix)
        raise


def _save_dxf_file(doc, file_path: str, log_callback: Optional[Callable] = None) -> None:
    """
    Save DXF document to file with proper error handling.
    
    Args:
        doc: DXF document to save
        file_path: Path to save the file
        log_callback: Logging callback function
        
    Raises:
        VisualizationError: If saving fails
    """
    log_function_entry(log_callback, "_save_dxf_file", file_path=file_path)
    
    try:
        with create_timed_logger(log_callback, "DXF file save") as save_timer:
            # Ensure directory exists
            file_path_obj = Path(file_path)
            parent_dir = file_path_obj.parent
            
            enhanced_log(log_callback, f"Creating directory: {parent_dir}", 'DEBUG')
            parent_dir.mkdir(parents=True, exist_ok=True)
            
            # Save the DXF file
            enhanced_log(log_callback, f"Saving DXF document to: {file_path}", 'DEBUG')
            doc.saveas(file_path)
            
            # Log file size
            file_size = os.path.getsize(file_path)
            log_performance_metric(log_callback, "DXF file size", file_size, "bytes")
            enhanced_log(log_callback, f"DXF file saved successfully: {file_path}", 'INFO')
        
        log_function_exit(log_callback, "_save_dxf_file", file_size=file_size)
            
    except Exception as save_error:
        error_msg = f"Failed to save DXF file: {save_error}"
        log_error_with_context(log_callback, "_save_dxf_file", save_error, file_path=file_path)
        raise VisualizationError(error_msg) from save_error


def _calculate_load_center_from_elements(group_elements, log_callback: Optional[Callable] = None) -> Optional[Point2D]:
    """
    Calculate load center from structural elements.
    
    Args:
        group_elements: Structural elements containing columns and walls
        log_callback: Logging callback function
        
    Returns:
        Load center coordinates or None if no elements found
    """
    log_function_entry(log_callback, "_calculate_load_center_from_elements",
                      has_elements=group_elements is not None)
    
    try:
        enhanced_log(log_callback, "Calculating load center from structural elements", 'DEBUG')
        
        # Check columns first
        if hasattr(group_elements, 'columns') and group_elements.columns:
            enhanced_log(log_callback, f"Found {len(group_elements.columns)} columns", 'DEBUG')
            for column in group_elements.columns:
                if len(column) >= 2 and isinstance(column[1], (list, tuple)) and len(column[1]) >= 2:
                    load_center = (column[1][0], column[1][1])
                    enhanced_log(log_callback, f"Load center from column: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
                    log_function_exit(log_callback, "_calculate_load_center_from_elements", 
                                    load_center=load_center)
                    return load_center
        
        # Check walls if no valid columns
        if hasattr(group_elements, 'walls') and group_elements.walls:
            enhanced_log(log_callback, f"Found {len(group_elements.walls)} walls", 'DEBUG')
            for wall in group_elements.walls:
                if len(wall) >= 2 and isinstance(wall[1], list) and len(wall[1]) >= 2:
                    wall_points = wall[1]
                    if len(wall_points) >= 2:
                        start = wall_points[0]
                        end = wall_points[-1]
                        if (isinstance(start, (list, tuple)) and len(start) >= 2 and
                            isinstance(end, (list, tuple)) and len(end) >= 2):
                            load_center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
                            enhanced_log(log_callback, f"Load center from wall: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
                            log_function_exit(log_callback, "_calculate_load_center_from_elements", 
                                            load_center=load_center)
                            return load_center
        
        enhanced_log(log_callback, "No valid structural elements found for load center", 'WARNING')
        log_function_exit(log_callback, "_calculate_load_center_from_elements", load_center=None)
        return None
        
    except Exception as e:
        log_error_with_context(log_callback, "_calculate_load_center_from_elements", e,
                              has_elements=group_elements is not None)
        return None

