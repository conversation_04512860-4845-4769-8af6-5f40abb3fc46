﻿"""
Coordinate system and geometric type definitions for the pile estimation system.

This module defines types related to coordinate systems, geometric data,
and spatial relationships used in pile layout optimization.
"""

from typing import NamedTuple, Tuple, List, Optional


class OptimalCoordinateSystem(NamedTuple):
    """
    Optimal coordinate system definition for pile cap layout.
    
    Attributes:
        origin: (x, y) coordinates of the local origin in global coordinates
        rotation_angle: Rotation angle in radians (counterclockwise from global X-axis)
        long_axis_length: Length of the long axis of the bounding rectangle
        short_axis_length: Length of the short axis of the bounding rectangle
        bounding_rect_corners: Optional list of corner coordinates of the bounding rectangle
        is_global_aligned: True if the optimal rectangle aligns with global axes
    """
    origin: Tuple[float, float]
    rotation_angle: float
    long_axis_length: float
    short_axis_length: float
    bounding_rect_corners: Optional[List[Tuple[float, float]]] = None
    is_global_aligned: bool = False


# Type aliases for commonly used coordinate data structures
ColumnData = Tuple[str, float, float, float]  # (name, x, y, base_level)
WallData = Tuple[str, List[Tuple[float, float]], float]  # (name, points_list, base_level)
PilePosition = Tuple[float, float]  # (x, y) coordinates
LoadData = Tuple[float, float, float, float, float, float]  # (Fx, Fy, Fz, Mx, My, Mz)
PileCapData = Tuple[str, List[PilePosition], float]  # (name, pile_positions, base_level)


class OptimalRectangleResult(NamedTuple):
    """
    Result of optimal rectangle analysis with comparison information.
    
    Attributes:
        local_system: The optimal local coordinate system
        rotated_area: Area of the rotated optimal rectangle
        global_aligned_area: Area of the global axis-aligned bounding box
        area_improvement: Percentage improvement over global alignment (negative if worse)
        use_global_alignment: Recommendation to use global alignment if areas are similar
        tolerance_threshold: The tolerance used for comparison
    """
    local_system: OptimalCoordinateSystem
    rotated_area: float
    global_aligned_area: float
    area_improvement: float
    use_global_alignment: bool
    tolerance_threshold: float = 0.05  # 5% tolerance by default


class GeometricBounds(NamedTuple):
    """
    Geometric bounds information for a polygon.
    
    Attributes:
        min_x: Minimum X coordinate
        max_x: Maximum X coordinate  
        min_y: Minimum Y coordinate
        max_y: Maximum Y coordinate
        width: Width (max_x - min_x)
        height: Height (max_y - min_y)
        center: Center point (x, y)
        area: Bounding box area
    """
    min_x: float
    max_x: float
    min_y: float
    max_y: float
    width: float
    height: float
    center: Tuple[float, float]
    area: float

