from datetime import datetime

import numpy as np
import pandas as pd

from build_fem import build_fem_config as config
from build_fem.functions import cal_line_length, cal_beam_length, cal_wall_length, cal_corewall_length
from read.functions import _load_sheet_data
from read.read_geometry import _identity_process


def _process_load_pattern_data(df):
    if df.empty:
        return df
    df['Type (Text)'] = 'OTHER'
    df['SelfWtMult (Unitless)'] = 0

    if 'Load Type' in df.columns:
        condition_self_weight = df['Load Type'] == 'SELF WEIGHT'
        df.loc[condition_self_weight, 'Type (Text)'] = 'DEAD'
        df.loc[condition_self_weight, 'SelfWtMult (Unitless)'] = 1

        condition_dead = df['Load Type'] == 'DEAD'
        df.loc[condition_dead, 'Type (Text)'] = 'DEAD'
        df.loc[condition_dead, 'SelfWtMult (Unitless)'] = 1

        condition_super_dead = df['Load Type'] == 'SUPERIMPOSED DEAD'
        df.loc[condition_super_dead, 'Type (Text)'] = 'SUPER DEAD'

        condition_live = df['Load Type'] == 'LIVE'
        df.loc[condition_live, 'Type (Text)'] = 'LIVE'

        condition_wind = df['Load Type'] == 'WIND'
        df.loc[condition_wind, 'Type (Text)'] = 'WIND'
    else:
        print(f"Warning: 'Load Type' column missing in LoadPat sheet. Cannot fully process load patterns.")
    return df


def _process_load_case_initial_data(df):
    if df.empty:
        return df

    column_start = df.columns.get_loc('LoadCase (Text)')
    df.insert(column_start + 1, column='DesignType (Text)', value='OTHER')
    df.insert(column_start + 2, column='Type (Text)', value='LinStatic')
    df.insert(column_start + 3, column='DesignOpt (Text)', value='Auto')
    df.insert(column_start + 4, column='InitialCond (Text)', value='Zero')
    df.insert(column_start + 5, column='AType (Text)', value='Linear')

    return df


def _process_fillna_zero_data(df):
    if not df.empty:
        df.fillna(0, inplace=True)
    return df


def read_input_loading(excel_inputs, file_paths):
    path_excel_input = file_paths.ExcelLoading
    loading_attributes = [
        'LoadPat', 'LoadCase', 'LoadComb',
        'InputLoadPoint', 'InputLoadLine', 'InputLoadPile', 'InputLoadLKP', 'InputLoadSlab',
        'InputLoadBeam', 'InputLoadColumn', 'InputLoadWall', 'InputLoadCoreWall'
    ]

    try:
        header_single = [0]
        _load_sheet_data(path_excel_input, config.SHEET_LOAD_PAT, excel_inputs, 'LoadPat', _process_load_pattern_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, config.SHEET_LOAD_CASE, excel_inputs, 'LoadCase', _process_load_case_initial_data,
                         header=header_single)
        _load_sheet_data(path_excel_input, config.SHEET_LOAD_COMB, excel_inputs, 'LoadComb', _process_fillna_zero_data,
                         header=header_single)

        header_multi = [0, 1]
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_POINT_LOAD, excel_inputs, 'InputLoadPoint',
                         _process_fillna_zero_data, header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_LINE_LOAD, excel_inputs, 'InputLoadLine',
                         _process_fillna_zero_data, header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_PILE, excel_inputs, 'InputLoadPile', _process_fillna_zero_data,
                         header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_LKP, excel_inputs, 'InputLoadLKP', _identity_process,
                         header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_SLAB, excel_inputs, 'InputLoadSlab', _process_fillna_zero_data,
                         header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_BEAM, excel_inputs, 'InputLoadBeam', _process_fillna_zero_data,
                         header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_COLUMN, excel_inputs, 'InputLoadColumn',
                         _process_fillna_zero_data, header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_WALL, excel_inputs, 'InputLoadWall', _process_fillna_zero_data,
                         header=header_multi)
        _load_sheet_data(path_excel_input, config.SHEET_INPUT_LOAD_CORE_WALL, excel_inputs, 'InputLoadCoreWall',
                         _process_fillna_zero_data, header=header_multi)

        df_load_case = getattr(excel_inputs, 'LoadCase', pd.DataFrame())
        df_load_pattern = getattr(excel_inputs, 'LoadPat', pd.DataFrame())
        if not df_load_case.empty and not df_load_pattern.empty and \
                'AType (Text)' in df_load_case.columns and \
                'LoadPat (Text)' in df_load_pattern.columns and 'Type (Text)' in df_load_pattern.columns:

            col_start_index = df_load_case.columns.get_loc('AType (Text)') + 1
            for i in df_load_case.index:
                load_type_for_case = 'N/A'
                for j in range(col_start_index, len(df_load_case.columns)):
                    if pd.notna(df_load_case.iloc[i, j]) and df_load_case.iloc[i, j] != 0:
                        load_pat_name = df_load_case.columns[j]
                        pat_series = df_load_pattern[df_load_pattern['LoadPat (Text)'] == load_pat_name]['Type (Text)']
                        if not pat_series.empty:
                            load_type_for_case = pat_series.values[0]
                            if load_type_for_case == 'WIND':
                                break
                df_load_case.loc[i, 'DesignType (Text)'] = load_type_for_case
            setattr(excel_inputs, 'LoadCase', df_load_case)
        elif not df_load_case.empty:
            print("Warning: LoadPat data is missing or incomplete. Cannot fully update LoadCase DesignType.")

        df_input_load_point = getattr(excel_inputs, 'InputLoadPoint', pd.DataFrame())
        df_pointload = getattr(excel_inputs, 'PointLoad', pd.DataFrame())
        if not df_input_load_point.empty and ('Point Data', 'Point Load') in df_input_load_point.columns:
            if not df_pointload.empty and 'Point Load (Text)' in df_pointload.columns and 'Point (Text)' in df_pointload.columns:
                temp_df = pd.DataFrame({'Point Load (Text)': df_input_load_point[('Point Data', 'Point Load')]})
                merged_df = pd.merge(temp_df, df_pointload, on='Point Load (Text)', how='left')
                df_input_load_point[('Point Data', 'Point (Text)')] = merged_df[
                    'Point (Text)'].values if 'Point (Text)' in merged_df else np.nan
            else:
                df_input_load_point[('Point Data', 'Point (Text)')] = np.nan
                if df_pointload.empty: print(
                    "Warning: PointLoad data from geometry is missing for InputLoad_PointLoad merge.")
            setattr(excel_inputs, 'InputLoadPoint', df_input_load_point)

        df_input_load_line = getattr(excel_inputs, 'InputLoadLine', pd.DataFrame())
        if not df_input_load_line.empty and ('Line Data', 'Line Load') in df_input_load_line.columns:
            try:
                df_line_length = cal_line_length(excel_inputs)
                if not df_line_length.empty and 'Line Load' in df_line_length.columns:
                    temp_df = pd.DataFrame({'Line Load': df_input_load_line[('Line Data', 'Line Load')]})
                    merged_df = pd.merge(temp_df, df_line_length, on='Line Load', how='left')
                    df_input_load_line[('Line Data', 'Line Length (m)')] = merged_df[
                        'Line Length (m)'].values if 'Line Length (m)' in merged_df else np.nan
                    df_input_load_line[('Line Data', 'Line Theta (deg)')] = merged_df[
                        'Line Theta (deg)'].values if 'Line Theta (deg)' in merged_df else np.nan
                else:
                    df_input_load_line[('Line Data', 'Line Length (m)')] = np.nan
                    df_input_load_line[('Line Data', 'Line Theta (deg)')] = np.nan
                    if df_line_length.empty: print(
                        "Warning: Calculated line length data is missing for InputLoad_LineLoad merge.")
            except Exception as e:
                print(f"Error calculating line lengths for InputLoad_LineLoad merge: {e}")
                df_input_load_line[('Line Data', 'Line Length (m)')] = np.nan
                df_input_load_line[('Line Data', 'Line Theta (deg)')] = np.nan
            setattr(excel_inputs, 'InputLoadLine', df_input_load_line)

        df_input_load_beam = getattr(excel_inputs, 'InputLoadBeam', pd.DataFrame())
        if not df_input_load_beam.empty and ('Beam Data', 'Beam Mark') in df_input_load_beam.columns:
            try:
                df_beam_length = cal_beam_length(excel_inputs)
                if not df_beam_length.empty and 'Beam Mark' in df_beam_length.columns:
                    temp_df = pd.DataFrame({'Beam Mark': df_input_load_beam[('Beam Data', 'Beam Mark')]})
                    merged_df = pd.merge(temp_df, df_beam_length, on='Beam Mark', how='left')
                    df_input_load_beam[('Beam Data', 'Beam Length (m)')] = merged_df[
                        'Beam Length (m)'].values if 'Beam Length (m)' in merged_df else np.nan
                    df_input_load_beam[('Beam Data', 'Beam Theta (deg)')] = merged_df[
                        'Beam Theta (deg)'].values if 'Beam Theta (deg)' in merged_df else np.nan
                else:
                    df_input_load_beam[('Beam Data', 'Beam Length (m)')] = np.nan
                    df_input_load_beam[('Beam Data', 'Beam Theta (deg)')] = np.nan
                    if df_beam_length.empty: print(
                        "Warning: Calculated beam length data is missing for InputLoad_Beam merge.")
            except Exception as e:
                print(f"Error calculating beam lengths for InputLoad_Beam merge: {e}")
                df_input_load_beam[('Beam Data', 'Beam Length (m)')] = np.nan
                df_input_load_beam[('Beam Data', 'Beam Theta (deg)')] = np.nan
            setattr(excel_inputs, 'InputLoadBeam', df_input_load_beam)

        df_input_load_column = getattr(excel_inputs, 'InputLoadColumn', pd.DataFrame())
        df_column_geom = getattr(excel_inputs, 'Column', pd.DataFrame())  # From geometry's ColumnData sheet
        if not df_input_load_column.empty and ('Column Data', 'Column Mark') in df_input_load_column.columns:
            if not df_column_geom.empty and 'Area (Text)' in df_column_geom.columns:
                # Column Mark in loading sheet corresponds to Area (Text) in geometry ColumnData sheet
                temp_df = pd.DataFrame({'Area (Text)': df_input_load_column[('Column Data', 'Column Mark')]})
                merged_df = pd.merge(temp_df, df_column_geom, on='Area (Text)', how='left')
                df_input_load_column[('Column Data', 'Area (m2)')] = merged_df[
                    'Area (m2)'].values if 'Area (m2)' in merged_df else np.nan
                df_input_load_column[('Column Data', 'Center Point (Text)')] = merged_df[
                    'Center Point (Text)'].values if 'Center Point (Text)' in merged_df else np.nan
            else:
                df_input_load_column[('Column Data', 'Area (m2)')] = np.nan
                df_input_load_column[('Column Data', 'Center Point (Text)')] = np.nan
                if df_column_geom.empty: print(
                    "Warning: Column geometry data (ColumnData) is missing for InputLoad_Column merge.")
            setattr(excel_inputs, 'InputLoadColumn', df_input_load_column)

        df_input_load_wall = getattr(excel_inputs, 'InputLoadWall', pd.DataFrame())
        if not df_input_load_wall.empty and ('Wall Data', 'Wall Mark') in df_input_load_wall.columns:
            try:
                df_wall_length_calc = cal_wall_length(excel_inputs)
                if not df_wall_length_calc.empty and 'Wall Name' in df_wall_length_calc.columns:
                    temp_df = pd.DataFrame({'Wall Name': df_input_load_wall[('Wall Data', 'Wall Mark')]})
                    merged_df = pd.merge(temp_df, df_wall_length_calc, on='Wall Name', how='left')
                    df_input_load_wall[('Wall Data', 'Wall Length (m)')] = merged_df[
                        'Wall Length (m)'].values if 'Wall Length (m)' in merged_df else np.nan
                    df_input_load_wall[('Wall Data', 'Wall Theta (deg)')] = merged_df[
                        'Wall Theta (deg)'].values if 'Wall Theta (deg)' in merged_df else np.nan
                else:
                    df_input_load_wall[('Wall Data', 'Wall Length (m)')] = np.nan
                    df_input_load_wall[('Wall Data', 'Wall Theta (deg)')] = np.nan
                    if df_wall_length_calc.empty: print(
                        "Warning: Calculated wall length data is missing for InputLoad_Wall merge.")
            except Exception as e:
                print(f"Error calculating wall lengths for InputLoad_Wall merge: {e}")
                df_input_load_wall[('Wall Data', 'Wall Length (m)')] = np.nan
                df_input_load_wall[('Wall Data', 'Wall Theta (deg)')] = np.nan
            setattr(excel_inputs, 'InputLoadWall', df_input_load_wall)

        df_input_load_corewall = getattr(excel_inputs, 'InputLoadCoreWall', pd.DataFrame())
        if not df_input_load_corewall.empty and ('CoreWall Data', 'CoreWall Mark') in df_input_load_corewall.columns:
            try:
                df_wall_length_for_corewall = cal_wall_length(excel_inputs)
                df_corewall_length_calc = cal_corewall_length(excel_inputs, df_wall_length_for_corewall)
                if not df_corewall_length_calc.empty and 'CoreWall Name' in df_corewall_length_calc.columns:
                    temp_df = pd.DataFrame(
                        {'CoreWall Name': df_input_load_corewall[('CoreWall Data', 'CoreWall Mark')]})
                    merged_df = pd.merge(temp_df, df_corewall_length_calc, on='CoreWall Name', how='left')
                    df_input_load_corewall[('CoreWall Data', 'CoreWall Length (m)')] = merged_df[
                        'CoreWall Length (m)'].values if 'CoreWall Length (m)' in merged_df else np.nan
                else:
                    df_input_load_corewall[('CoreWall Data', 'CoreWall Length (m)')] = np.nan
                    if df_corewall_length_calc.empty: print(
                        "Warning: Calculated corewall length data is missing for InputLoad_CoreWall merge.")
            except Exception as e:
                print(f"Error calculating corewall lengths for InputLoad_CoreWall merge: {e}")
                df_input_load_corewall[('CoreWall Data', 'CoreWall Length (m)')] = np.nan
            setattr(excel_inputs, 'InputLoadCoreWall', df_input_load_corewall)

    except FileNotFoundError:
        print(
            f"Halting loading reading: Excel file at '{path_excel_input}' not found. All loading attributes will be empty.")
        for attr_name in loading_attributes:
            setattr(excel_inputs, attr_name, pd.DataFrame())
        return excel_inputs

    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Read Excel Input (Loading)!')
    return excel_inputs
