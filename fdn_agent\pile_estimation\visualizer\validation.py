﻿"""
Validation and Debugging Functions

Functions for validating pile estimation results and debugging visualization issues.
"""

from typing import Dict, Any, Optional, Tu<PERSON>, Callable
from shapely.geometry import Polygon

from ..data_types import PileGroupResult
from .utils import safe_get
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


def validate_pile_estimation_results(results: Dict[str, PileGroupResult], 
                                   log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Validate pile estimation results and generate comprehensive summary statistics.
    
    Args:
        results: Dictionary of group results from pile estimation
        log_callback: Optional callback for logging validation progress
    
    Returns:
        Dictionary with validation summary including:
        - total_groups: Number of pile groups
        - total_piles: Total number of piles across all groups
        - groups_with_warnings: Number of groups with warnings
        - groups_with_errors: Number of groups with errors
        - invalid_groups: List of group IDs with invalid data
        - all_warnings: List of all warnings from all groups
        - validation_passed: <PERSON><PERSON><PERSON> indicating if validation passed
    
    Raises:
        ValidationError: If critical validation issues are found
    """
    log_function_entry(log_callback, "validate_pile_estimation_results", 
                      num_groups=len(results) if results else 0)
    
    with create_timed_logger(log_callback, "pile_estimation_results_validation"):
        try:
            enhanced_log(log_callback, "Starting pile estimation results validation", 'INFO')
            
            if not results:
                error_msg = "No pile estimation results provided for validation"
                enhanced_log(log_callback, error_msg, 'ERROR')
                log_validation_result(log_callback, "Results Present", False, error_msg)
                log_error_with_context(log_callback, ValidationError(error_msg), "validate_pile_estimation_results")
                raise ValidationError(error_msg)
            
            log_validation_result(log_callback, "Results Present", True, 
                                f"{len(results)} pile groups provided")
            
            validation = {
                'total_groups': len(results),
                'total_piles': 0,
                'groups_with_warnings': 0,
                'groups_with_errors': 0,
                'invalid_groups': [],
                'all_warnings': [],
                'validation_passed': True
            }

            enhanced_log(log_callback, f"Processing {len(results)} pile groups for validation", 'INFO')
            
            processed_groups = 0
            valid_groups = 0
            
            for group_id, data in results.items():
                try:
                    enhanced_log(log_callback, f"Validating group: {group_id}", 'DEBUG')
                    processed_groups += 1
                    
                    # Validate pile locations
                    pile_locations = safe_get(data, 'pile_locations', [])
                    enhanced_log(log_callback, f"Group {group_id}: Found {len(pile_locations) if isinstance(pile_locations, list) else 'invalid'} pile locations", 'DEBUG')
                    
                    if not isinstance(pile_locations, list):
                        error_msg = f"Invalid pile_locations type: {type(pile_locations)}"
                        validation['invalid_groups'].append(f"{group_id}: {error_msg}")
                        validation['validation_passed'] = False
                        log_validation_result(log_callback, f"Group {group_id} Pile Locations Type", False, error_msg)
                        enhanced_log(log_callback, f"Group {group_id}: {error_msg}", 'ERROR')
                        continue
                    
                    num_piles = len(pile_locations)
                    validation['total_piles'] += num_piles
                    log_calculation_result(log_callback, f"Group_{group_id}_pile_count", str(num_piles))
                    
                    if num_piles == 0:
                        error_msg = "No piles found"
                        validation['invalid_groups'].append(f"{group_id}: {error_msg}")
                        validation['validation_passed'] = False
                        log_validation_result(log_callback, f"Group {group_id} Has Piles", False, error_msg)
                        enhanced_log(log_callback, f"Group {group_id}: {error_msg}", 'WARNING')
                    else:
                        log_validation_result(log_callback, f"Group {group_id} Has Piles", True, 
                                            f"{num_piles} piles found")
                    
                    # Check warnings
                    warnings_list = safe_get(data, 'warnings', [])
                    if warnings_list:
                        validation['groups_with_warnings'] += 1
                        validation['all_warnings'].extend([f"{group_id}: {w}" for w in warnings_list])
                        enhanced_log(log_callback, f"Group {group_id}: {len(warnings_list)} warnings found", 'WARNING')
                        for warning in warnings_list:
                            enhanced_log(log_callback, f"Group {group_id} warning: {warning}", 'WARNING')
                    else:
                        enhanced_log(log_callback, f"Group {group_id}: No warnings", 'DEBUG')
                    
                    # Check errors
                    errors_list = safe_get(data, 'errors', [])
                    if errors_list:
                        validation['groups_with_errors'] += 1
                        validation['validation_passed'] = False
                        enhanced_log(log_callback, f"Group {group_id}: {len(errors_list)} errors found", 'ERROR')
                        for error in errors_list:
                            enhanced_log(log_callback, f"Group {group_id} error: {error}", 'ERROR')
                    else:
                        enhanced_log(log_callback, f"Group {group_id}: No errors", 'DEBUG')
                    
                    # Validate pile cap polygon
                    pile_cap = safe_get(data, 'pile_cap_polygon')
                    if pile_cap is not None:
                        enhanced_log(log_callback, f"Group {group_id}: Validating pile cap polygon", 'DEBUG')
                        validation_result = validate_pile_cap_polygon(pile_cap, group_id, log_callback)
                        if not validation_result[0]:  # If validation failed
                            validation['invalid_groups'].append(f"{group_id}: {validation_result[1]}")
                            validation['validation_passed'] = False
                            log_validation_result(log_callback, f"Group {group_id} Pile Cap Polygon", False, 
                                                validation_result[1])
                            enhanced_log(log_callback, f"Group {group_id}: Pile cap validation failed - {validation_result[1]}", 'ERROR')
                        else:
                            log_validation_result(log_callback, f"Group {group_id} Pile Cap Polygon", True, 
                                                validation_result[1])
                            enhanced_log(log_callback, f"Group {group_id}: Pile cap validation passed", 'DEBUG')
                    else:
                        enhanced_log(log_callback, f"Group {group_id}: No pile cap polygon to validate", 'DEBUG')
                    
                    # If we get here without major issues, count as valid
                    if not errors_list and isinstance(pile_locations, list):
                        valid_groups += 1
                        
                except Exception as e:
                    error_msg = f"Validation error - {str(e)}"
                    validation['invalid_groups'].append(f"{group_id}: {error_msg}")
                    validation['validation_passed'] = False
                    log_error_with_context(log_callback, e, f"validate_pile_estimation_results group {group_id}")
                    enhanced_log(log_callback, f"Group {group_id}: Unexpected validation error - {str(e)}", 'ERROR')

            # Log validation summary statistics
            log_performance_metric(log_callback, "Total_groups_processed", processed_groups, "groups")
            log_performance_metric(log_callback, "Valid_groups", valid_groups, "groups")
            log_performance_metric(log_callback, "Total_piles", validation['total_piles'], "piles")
            log_performance_metric(log_callback, "Groups_with_warnings", validation['groups_with_warnings'], "groups")
            log_performance_metric(log_callback, "Groups_with_errors", validation['groups_with_errors'], "groups")
            log_performance_metric(log_callback, "Invalid_groups", len(validation['invalid_groups']), "groups")
            log_performance_metric(log_callback, "Total_warnings", len(validation['all_warnings']), "warnings")
            
            # Log final validation results
            if validation['validation_passed']:
                enhanced_log(log_callback, f"Pile estimation results validation PASSED: {valid_groups}/{processed_groups} groups valid, {validation['total_piles']} total piles", 'INFO')
                log_validation_result(log_callback, "Overall Pile Estimation Results", True, 
                                    f"{valid_groups}/{processed_groups} groups valid")
            else:
                enhanced_log(log_callback, f"Pile estimation results validation FAILED: {len(validation['invalid_groups'])} invalid groups, {validation['groups_with_errors']} groups with errors", 'ERROR')
                log_validation_result(log_callback, "Overall Pile Estimation Results", False, 
                                    f"{len(validation['invalid_groups'])} invalid groups")
            
            if validation['groups_with_warnings'] > 0:
                enhanced_log(log_callback, f"Validation completed with {validation['groups_with_warnings']} groups having warnings", 'WARNING')
            
            log_function_exit(log_callback, "validate_pile_estimation_results", validation)
            return validation
            
        except ValidationError:
            # Re-raise ValidationError as-is
            log_function_exit(log_callback, "validate_pile_estimation_results", "ValidationError raised")
            raise
        except Exception as e:
            log_error_with_context(log_callback, e, "validate_pile_estimation_results")
            log_function_exit(log_callback, "validate_pile_estimation_results", "Unexpected error")
            raise


def debug_group_results(group_results: Dict[str, PileGroupResult], 
                       log_callback: Optional[Callable] = None) -> None:
    """
    Debug function to analyze group results and identify potential issues.
    
    Args:
        group_results: Dictionary of group results from pile estimation
        log_callback: Optional callback function for logging debug information
    """
    log_function_entry(log_callback, "debug_group_results", 
                      num_groups=len(group_results) if group_results else 0)
    
    with create_timed_logger(log_callback, "group_results_debugging"):
        try:
            enhanced_log(log_callback, "Starting group results debugging analysis", 'INFO')
            
            if not group_results:
                enhanced_log(log_callback, "No group results provided for debugging", 'WARNING')
                log_validation_result(log_callback, "Group Results Present", False, "Empty group results")
                # Maintain backward compatibility with existing log_callback
                if log_callback:
                    log_callback("No group results provided for debugging")
                log_function_exit(log_callback, "debug_group_results", "No data to debug")
                return
            
            log_validation_result(log_callback, "Group Results Present", True, 
                                f"{len(group_results)} groups to debug")
            enhanced_log(log_callback, f"Debugging {len(group_results)} pile groups", 'INFO')
            
            # Maintain backward compatibility with existing log_callback format
            if log_callback:
                log_callback(" DEBUGGING GROUP RESULTS")
                log_callback("=" * 50)
            
            groups_processed = 0
            groups_with_issues = 0
            total_fields_analyzed = 0
            
            for group_id, data in group_results.items():
                enhanced_log(log_callback, f"Debugging group: {group_id}", 'DEBUG')
                groups_processed += 1
                
                # Maintain backward compatibility with existing log_callback format
                if log_callback:
                    log_callback(f"\n Group: {group_id}")
                    log_callback("-" * 30)
                
                # Check data type and structure
                data_type = type(data)
                enhanced_log(log_callback, f"Group {group_id}: Data type = {data_type}", 'DEBUG')
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"Data type: {data_type}")
                
                group_has_issues = False
                
                if isinstance(data, dict):
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback("Data format: Dictionary")
                        log_callback(f"Available keys: {list(data.keys())}")
                    
                    enhanced_log(log_callback, f"Group {group_id}: Dictionary format with {len(data.keys())} keys", 'DEBUG')
                    log_calculation_result(log_callback, f"Group_{group_id}_dict_keys", str(len(data.keys())))
                    
                else:
                    # Maintain backward compatibility
                    if log_callback:
                        log_callback("Data format: Object")
                    
                    enhanced_log(log_callback, f"Group {group_id}: Object format", 'DEBUG')
                    
                    if hasattr(data, '__dict__'):
                        attrs = list(data.__dict__.keys())
                        # Maintain backward compatibility
                        if log_callback:
                            log_callback(f"Available attributes: {attrs}")
                        
                        enhanced_log(log_callback, f"Group {group_id}: Object with {len(attrs)} attributes", 'DEBUG')
                        log_calculation_result(log_callback, f"Group_{group_id}_object_attrs", str(len(attrs)))
                
                # Analyze pile cap polygon
                enhanced_log(log_callback, f"Group {group_id}: Analyzing pile cap polygon", 'DEBUG')
                if _debug_pile_cap_polygon(data, group_id, log_callback):
                    group_has_issues = True
                total_fields_analyzed += 1
                
                # Analyze pile locations
                enhanced_log(log_callback, f"Group {group_id}: Analyzing pile locations", 'DEBUG')
                if _debug_pile_locations(data, group_id, log_callback):
                    group_has_issues = True
                total_fields_analyzed += 1
                
                # Analyze load centroid
                enhanced_log(log_callback, f"Group {group_id}: Analyzing load centroid", 'DEBUG')
                if _debug_load_centroid(data, group_id, log_callback):
                    group_has_issues = True
                total_fields_analyzed += 1
                
                # Check other important fields
                enhanced_log(log_callback, f"Group {group_id}: Analyzing other important fields", 'DEBUG')
                if _debug_other_fields(data, group_id, log_callback):
                    group_has_issues = True
                total_fields_analyzed += 1
                
                if group_has_issues:
                    groups_with_issues += 1
                    enhanced_log(log_callback, f"Group {group_id}: Issues detected during debugging", 'WARNING')
                else:
                    enhanced_log(log_callback, f"Group {group_id}: No issues detected", 'DEBUG')
            
            # Log debugging summary
            log_performance_metric(log_callback, "Groups_processed", groups_processed, "groups")
            log_performance_metric(log_callback, "Groups_with_issues", groups_with_issues, "groups")
            log_performance_metric(log_callback, "Total_fields_analyzed", total_fields_analyzed, "fields")
            
            if groups_with_issues == 0:
                enhanced_log(log_callback, f"Group results debugging completed: {groups_processed} groups analyzed, no issues detected", 'INFO')
            else:
                enhanced_log(log_callback, f"Group results debugging completed: {groups_with_issues}/{groups_processed} groups have issues", 'WARNING')
                
        except Exception as e:
            log_error_with_context(log_callback, e, "debug_group_results")
            enhanced_log(log_callback, f"Error during group results debugging: {str(e)}", 'ERROR')
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"Error during debugging: {str(e)}")
    
    log_function_exit(log_callback, "debug_group_results", f"{len(group_results)} groups debugged")


def validate_pile_cap_polygon(polygon: Any, group_id: str, 
                             log_callback: Optional[Callable] = None) -> Tuple[bool, str]:
    """
    Validate a pile cap polygon and return detailed validation results.
    
    Args:
        polygon: The polygon to validate
        group_id: Group identifier for error messages
        log_callback: Optional callback for logging validation progress
        
    Returns:
        Tuple of (is_valid: bool, message: str)
    """
    log_function_entry(log_callback, "validate_pile_cap_polygon", 
                      polygon_type=type(polygon).__name__, group_id=group_id)
    
    with create_timed_logger(log_callback, f"pile_cap_polygon_validation_{group_id}"):
        try:
            enhanced_log(log_callback, f"Validating pile cap polygon for group {group_id}", 'DEBUG')
            
            if polygon is None:
                result_msg = "Polygon is None"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_validation_result(log_callback, f"Group {group_id} Polygon Existence", False, result_msg)
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            log_validation_result(log_callback, f"Group {group_id} Polygon Existence", True, "Polygon is not None")
            
            if not isinstance(polygon, Polygon):
                result_msg = f"Not a Polygon type (got {type(polygon)})"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_validation_result(log_callback, f"Group {group_id} Polygon Type", False, result_msg)
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            log_validation_result(log_callback, f"Group {group_id} Polygon Type", True, "Valid Polygon type")
            
            if polygon.is_empty:
                result_msg = "Polygon is empty"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_validation_result(log_callback, f"Group {group_id} Polygon Empty Check", False, result_msg)
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            log_validation_result(log_callback, f"Group {group_id} Polygon Empty Check", True, "Polygon is not empty")
            
            if not polygon.is_valid:
                invalid_reason = polygon.invalid_reason if hasattr(polygon, 'invalid_reason') else 'Unknown reason'
                result_msg = f"Polygon is geometrically invalid: {invalid_reason}"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_validation_result(log_callback, f"Group {group_id} Polygon Validity", False, result_msg)
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            log_validation_result(log_callback, f"Group {group_id} Polygon Validity", True, "Polygon is geometrically valid")
            
            # Check if polygon has sufficient points
            try:
                coords = list(polygon.exterior.coords)
                num_coords = len(coords)
                enhanced_log(log_callback, f"Group {group_id}: Polygon has {num_coords} coordinate points", 'DEBUG')
                log_calculation_result(log_callback, f"Group_{group_id}_polygon_coords", str(num_coords))
                
                if num_coords < 4:  # Need at least 3 unique points (4 with closure)
                    result_msg = f"Insufficient coordinates ({num_coords}, need at least 4)"
                    enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                    log_constraint_check(log_callback, f"Group {group_id} Sufficient Coordinates", 
                                       num_coords, ">= 4", False)
                    log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                    return False, result_msg
                else:
                    log_constraint_check(log_callback, f"Group {group_id} Sufficient Coordinates", 
                                       num_coords, ">= 4", True)
                    
            except Exception as e:
                result_msg = f"Error accessing polygon coordinates: {e}"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_error_with_context(log_callback, e, f"validate_pile_cap_polygon coords access {group_id}")
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            # Check polygon area
            try:
                area = polygon.area
                enhanced_log(log_callback, f"Group {group_id}: Polygon area = {area:.6f}", 'DEBUG')
                log_calculation_result(log_callback, f"Group_{group_id}_polygon_area", f"{area:.6f}")
                
                if area <= 0:
                    result_msg = f"Invalid polygon area: {area}"
                    enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                    log_constraint_check(log_callback, f"Group {group_id} Positive Area", area, "> 0", False)
                    log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                    return False, result_msg
                else:
                    log_constraint_check(log_callback, f"Group {group_id} Positive Area", area, "> 0", True)
                    
            except Exception as e:
                result_msg = f"Error calculating polygon area: {e}"
                enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
                log_error_with_context(log_callback, e, f"validate_pile_cap_polygon area calculation {group_id}")
                log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
                return False, result_msg
            
            # All validations passed
            result_msg = f"Valid polygon (area: {polygon.area:.3f})"
            enhanced_log(log_callback, f"Group {group_id}: Pile cap polygon validation PASSED - {result_msg}", 'INFO')
            log_validation_result(log_callback, f"Group {group_id} Overall Polygon Validation", True, result_msg)
            
            log_function_exit(log_callback, "validate_pile_cap_polygon", (True, result_msg))
            return True, result_msg
            
        except Exception as e:
            result_msg = f"Unexpected error during polygon validation: {str(e)}"
            enhanced_log(log_callback, f"Group {group_id}: {result_msg}", 'ERROR')
            log_error_with_context(log_callback, e, f"validate_pile_cap_polygon {group_id}")
            log_function_exit(log_callback, "validate_pile_cap_polygon", (False, result_msg))
            return False, result_msg


def _debug_pile_cap_polygon(data: Any, group_id: str, 
                           log_callback: Optional[Callable] = None) -> bool:
    """
    Debug pile cap polygon for a group.
    
    Args:
        data: Group data to analyze
        group_id: Group identifier
        log_callback: Optional callback for logging debug information
        
    Returns:
        bool: True if issues were detected, False otherwise
    """
    log_function_entry(log_callback, "_debug_pile_cap_polygon", group_id=group_id)
    
    try:
        pile_cap = safe_get(data, 'pile_cap_polygon')
        pile_cap_type = type(pile_cap)
        
        enhanced_log(log_callback, f"Group {group_id}: Pile cap polygon type = {pile_cap_type}", 'DEBUG')
        
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"Pile cap polygon: {pile_cap_type}")
        
        has_issues = False
        
        if pile_cap is None:
            enhanced_log(log_callback, f"Group {group_id}: Pile cap is None", 'WARNING')
            # Maintain backward compatibility
            if log_callback:
                log_callback("  Pile cap is None")
            has_issues = True
            log_function_exit(log_callback, "_debug_pile_cap_polygon", has_issues)
            return has_issues
        
        # Validate the polygon
        validation_result = validate_pile_cap_polygon(pile_cap, group_id, log_callback)
        
        if validation_result[0]:
            enhanced_log(log_callback, f"Group {group_id}: Pile cap polygon is valid - {validation_result[1]}", 'DEBUG')
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"  {validation_result[1]}")
        else:
            enhanced_log(log_callback, f"Group {group_id}: Pile cap polygon has issues - {validation_result[1]}", 'WARNING')
            # Maintain backward compatibility
            if log_callback:
                log_callback(f"  {validation_result[1]}")
            has_issues = True
        
        log_function_exit(log_callback, "_debug_pile_cap_polygon", has_issues)
        return has_issues
        
    except Exception as e:
        enhanced_log(log_callback, f"Group {group_id}: Error debugging pile cap polygon - {str(e)}", 'ERROR')
        log_error_with_context(log_callback, e, f"_debug_pile_cap_polygon {group_id}")
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"  Error debugging pile cap: {str(e)}")
        log_function_exit(log_callback, "_debug_pile_cap_polygon", True)
        return True


def _debug_pile_locations(data: Any, group_id: str, 
                         log_callback: Optional[Callable] = None) -> bool:
    """
    Debug pile locations for a group.
    
    Args:
        data: Group data to analyze
        group_id: Group identifier
        log_callback: Optional callback for logging debug information
        
    Returns:
        bool: True if issues were detected, False otherwise
    """
    log_function_entry(log_callback, "_debug_pile_locations", group_id=group_id)
    
    try:
        pile_locations = safe_get(data, 'pile_locations', [])
        num_locations = len(pile_locations) if isinstance(pile_locations, list) else 'invalid'
        
        enhanced_log(log_callback, f"Group {group_id}: Found {num_locations} pile locations", 'DEBUG')
        
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"Pile locations: {len(pile_locations)} items")
        
        has_issues = False
        
        if not isinstance(pile_locations, list):
            enhanced_log(log_callback, f"Group {group_id}: Pile locations is not a list (type: {type(pile_locations)})", 'WARNING')
            has_issues = True
            log_function_exit(log_callback, "_debug_pile_locations", has_issues)
            return has_issues
        
        if len(pile_locations) == 0:
            enhanced_log(log_callback, f"Group {group_id}: No pile locations found", 'WARNING')
            # Maintain backward compatibility
            if log_callback:
                log_callback("   No pile locations found")
            has_issues = True
            log_function_exit(log_callback, "_debug_pile_locations", has_issues)
            return has_issues
        
        log_calculation_result(log_callback, f"Group_{group_id}_pile_locations_count", str(len(pile_locations)))
        
        # Analyze first pile location as sample
        sample_pile = pile_locations[0]
        enhanced_log(log_callback, f"Group {group_id}: Sample pile location: {sample_pile} (type: {type(sample_pile)})", 'DEBUG')
        
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"  Sample pile: {sample_pile}")
            log_callback(f"  Sample type: {type(sample_pile)}")
        
        # Validate sample coordinates
        if pile_locations and len(pile_locations[0]) >= 2:
            try:
                x, y = float(pile_locations[0][0]), float(pile_locations[0][1])
                enhanced_log(log_callback, f"Group {group_id}: Sample coordinates are valid: ({x:.3f}, {y:.3f})", 'DEBUG')
                log_calculation_result(log_callback, f"Group_{group_id}_sample_coordinates", f"({x:.3f}, {y:.3f})")
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"  Sample coordinates: ({x:.3f}, {y:.3f})")
                    
            except (ValueError, TypeError) as e:
                enhanced_log(log_callback, f"Group {group_id}: Invalid sample coordinates - {str(e)}", 'WARNING')
                log_error_with_context(log_callback, e, f"_debug_pile_locations sample coords {group_id}")
                has_issues = True
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"  Invalid sample coordinates: {e}")
        else:
            enhanced_log(log_callback, f"Group {group_id}: Sample pile location has insufficient coordinate data", 'WARNING')
            has_issues = True
        
        log_function_exit(log_callback, "_debug_pile_locations", has_issues)
        return has_issues
        
    except Exception as e:
        enhanced_log(log_callback, f"Group {group_id}: Error debugging pile locations - {str(e)}", 'ERROR')
        log_error_with_context(log_callback, e, f"_debug_pile_locations {group_id}")
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"  Error debugging pile locations: {str(e)}")
        log_function_exit(log_callback, "_debug_pile_locations", True)
        return True


def _debug_load_centroid(data: Any, group_id: str, 
                        log_callback: Optional[Callable] = None) -> bool:
    """
    Debug load centroid for a group.
    
    Args:
        data: Group data to analyze
        group_id: Group identifier
        log_callback: Optional callback for logging debug information
        
    Returns:
        bool: True if issues were detected, False otherwise
    """
    log_function_entry(log_callback, "_debug_load_centroid", group_id=group_id)
    
    try:
        load_centroid = safe_get(data, 'load_centroid')
        
        enhanced_log(log_callback, f"Group {group_id}: Load centroid = {load_centroid} (type: {type(load_centroid)})", 'DEBUG')
        
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"Load centroid: {load_centroid}")
        
        has_issues = False
        
        if load_centroid is None:
            enhanced_log(log_callback, f"Group {group_id}: Load centroid is None", 'WARNING')
            has_issues = True
            log_function_exit(log_callback, "_debug_load_centroid", has_issues)
            return has_issues
        
        if load_centroid and len(load_centroid) >= 2:
            try:
                x, y = float(load_centroid[0]), float(load_centroid[1])
                enhanced_log(log_callback, f"Group {group_id}: Load centroid coordinates are valid: ({x:.3f}, {y:.3f})", 'DEBUG')
                log_calculation_result(log_callback, f"Group_{group_id}_load_centroid", f"({x:.3f}, {y:.3f})")
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"  Coordinates: ({x:.3f}, {y:.3f})")
                    
            except (ValueError, TypeError) as e:
                enhanced_log(log_callback, f"Group {group_id}: Invalid load centroid coordinates - {str(e)}", 'WARNING')
                log_error_with_context(log_callback, e, f"_debug_load_centroid coords {group_id}")
                has_issues = True
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"  Invalid load centroid coordinates: {e}")
        else:
            enhanced_log(log_callback, f"Group {group_id}: Load centroid has insufficient coordinate data", 'WARNING')
            has_issues = True
        
        log_function_exit(log_callback, "_debug_load_centroid", has_issues)
        return has_issues
        
    except Exception as e:
        enhanced_log(log_callback, f"Group {group_id}: Error debugging load centroid - {str(e)}", 'ERROR')
        log_error_with_context(log_callback, e, f"_debug_load_centroid {group_id}")
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"  Error debugging load centroid: {str(e)}")
        log_function_exit(log_callback, "_debug_load_centroid", True)
        return True


def _debug_other_fields(data: Any, group_id: str, 
                       log_callback: Optional[Callable] = None) -> bool:
    """
    Debug other important fields for a group.
    
    Args:
        data: Group data to analyze
        group_id: Group identifier
        log_callback: Optional callback for logging debug information
        
    Returns:
        bool: True if issues were detected, False otherwise
    """
    log_function_entry(log_callback, "_debug_other_fields", group_id=group_id)
    
    try:
        important_fields = ['num_piles', 'warnings', 'errors', 'is_valid', 'selected_pile_spec']
        
        enhanced_log(log_callback, f"Group {group_id}: Analyzing {len(important_fields)} important fields", 'DEBUG')
        
        has_issues = False
        found_fields = 0
        missing_fields = []
        
        for field in important_fields:
            value = safe_get(data, field)
            
            if value is not None:
                found_fields += 1
                enhanced_log(log_callback, f"Group {group_id}: {field} = {value} (type: {type(value)})", 'DEBUG')
                log_calculation_result(log_callback, f"Group_{group_id}_{field}", str(value))
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"{field}: {value}")
                    
                # Check for specific field issues
                if field == 'num_piles' and (not isinstance(value, (int, float)) or value <= 0):
                    enhanced_log(log_callback, f"Group {group_id}: Invalid {field} value: {value}", 'WARNING')
                    has_issues = True
                elif field == 'warnings' and isinstance(value, list) and len(value) > 0:
                    enhanced_log(log_callback, f"Group {group_id}: Has {len(value)} warnings", 'WARNING')
                elif field == 'errors' and isinstance(value, list) and len(value) > 0:
                    enhanced_log(log_callback, f"Group {group_id}: Has {len(value)} errors", 'ERROR')
                    has_issues = True
                elif field == 'is_valid' and value is False:
                    enhanced_log(log_callback, f"Group {group_id}: Marked as invalid", 'WARNING')
                    has_issues = True
            else:
                missing_fields.append(field)
                enhanced_log(log_callback, f"Group {group_id}: {field} not found", 'DEBUG')
                
                # Maintain backward compatibility
                if log_callback:
                    log_callback(f"{field}: Not found")
        
        # Log field analysis summary
        log_performance_metric(log_callback, f"Group_{group_id}_found_fields", found_fields, "fields")
        log_performance_metric(log_callback, f"Group_{group_id}_missing_fields", len(missing_fields), "fields")
        
        if missing_fields:
            enhanced_log(log_callback, f"Group {group_id}: Missing fields: {missing_fields}", 'DEBUG')
        
        if found_fields == len(important_fields):
            enhanced_log(log_callback, f"Group {group_id}: All important fields present", 'DEBUG')
        elif found_fields == 0:
            enhanced_log(log_callback, f"Group {group_id}: No important fields found", 'WARNING')
            has_issues = True
        
        log_function_exit(log_callback, "_debug_other_fields", has_issues)
        return has_issues
        
    except Exception as e:
        enhanced_log(log_callback, f"Group {group_id}: Error debugging other fields - {str(e)}", 'ERROR')
        log_error_with_context(log_callback, e, f"_debug_other_fields {group_id}")
        # Maintain backward compatibility
        if log_callback:
            log_callback(f"  Error debugging other fields: {str(e)}")
        log_function_exit(log_callback, "_debug_other_fields", True)
        return True

