"""Email notifications package for Foundation Automation."""

# Import specific functions instead of using import *
try:
    from .notification import (
        send_password_email,
        send_email_log,
        generate_password_key,
        create_smtp_ssl_context
    )
except ImportError:
    send_password_email = None
    send_email_log = None
    generate_password_key = None
    create_smtp_ssl_context = None

__all__ = [
    'send_password_email',
    'send_email_log', 
    'generate_password_key',
    'create_smtp_ssl_context'
]
