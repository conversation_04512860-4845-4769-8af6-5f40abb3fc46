"""
PROKON C13 File Generation for Ultimate Limit State (ULS) Bored Pile Foundation Design

This module provides comprehensive integration with PROKON structural analysis software for
Ultimate Limit State design verification of bored pile foundations. The module generates
PROKON C13 column design files containing reinforcement layouts, load combinations, and
design parameters required for detailed structural analysis and code compliance verification.

Key Features:
- Automated PROKON C13 file generation for bored pile foundation analysis
- Multi-layer reinforcement pattern generation with precise coordinate calculations
- Load combination processing and transformation for ULS design verification
- Parallel processing capabilities for efficient batch generation of multiple pile configurations
- Integration with structural analysis results and foundation design workflows
- Comprehensive material property and design parameter management

Author: Foundation-Automation Development Team
Version: Compatible with PROKON structural analysis software and ULS design workflows
"""

import multiprocessing
import os
from datetime import datetime
from math import ceil, floor
from typing import Dict, List, Tuple, Optional, Callable, Any
import numpy as np
import pandas as pd
from tqdm import tqdm
from functools import partial


def gen_prokon_rebar(dia: float, 
                    cover: float, 
                    rebar_num1: int, 
                    rebar_dia1: float, 
                    rebar_num2: int, 
                    rebar_dia2: float, 
                    rebar_num3: int, 
                    rebar_dia3: float,
                    clear_spacing1to2: float, 
                    clear_spacing2to3: float) -> pd.DataFrame:
    """
    Generate PROKON-compatible reinforcement pattern with precise bar coordinates for circular columns.
    
    This function creates a comprehensive reinforcement layout for circular bored pile sections
    compatible with PROKON C13 file format. It calculates precise bar coordinates for up to
    three reinforcement layers using vectorized operations for optimal performance, ensuring
    proper spacing, constructability, and compliance with design code requirements.

    The reinforcement generation process includes:
    - Multi-layer coordinate calculation with proper radial positioning
    - Vectorized angular distribution for uniform bar spacing around perimeter
    - Cover and clear spacing verification for code compliance
    - PROKON-specific format generation for seamless software integration
    """
    # Convert diameter to millimeters for consistent units in PROKON format
    dia_mm = dia * 1000  # Convert from meters to millimeters
    
    # Initialize reinforcement data with section definition
    # PROKON format requires '+' for coordinates and 'C' for circular section
    rebar_data = [['+', ' ', ' '], ['C', dia_mm / 2, ' ']]
    
    # Generate Layer 1 reinforcement coordinates (outermost layer)
    if rebar_num1 > 0:
        # Create uniform angular distribution around column perimeter
        # endpoint=False ensures bars don't overlap at 0° and 360°
        angles = np.linspace(0, 360, rebar_num1, endpoint=False)
        
        # Calculate radial position: column radius minus cover minus half bar diameter
        # This positions bar centerline at correct distance from column surface
        r_layer1 = dia_mm / 2 - cover - rebar_dia1 / 2
        
        # Convert polar coordinates to Cartesian using vectorized operations
        # Optimized calculation for all bars simultaneously
        x_coords = r_layer1 * np.cos(np.radians(angles))
        y_coords = r_layer1 * np.sin(np.radians(angles))
        
        # Add coordinate and bar data for each layer 1 bar
        for x, y in zip(x_coords, y_coords):
            rebar_data.extend([['+', x, y], ['B', rebar_dia1, ' ']])
    
    # Generate Layer 2 reinforcement coordinates (intermediate layer)
    if rebar_num2 > 0:
        # Uniform angular distribution for intermediate layer
        angles = np.linspace(0, 360, rebar_num2, endpoint=False)
        
        # Calculate radial position accounting for layer 1 and clear spacing
        # Position: radius - cover - layer1_dia - spacing - half_layer2_dia
        r_layer2 = dia_mm / 2 - cover - rebar_dia1 - clear_spacing1to2 - rebar_dia2 / 2
        
        # Vectorized coordinate conversion for layer 2 bars
        x_coords = r_layer2 * np.cos(np.radians(angles))
        y_coords = r_layer2 * np.sin(np.radians(angles))
        
        # Add coordinate and bar data for each layer 2 bar
        for x, y in zip(x_coords, y_coords):
            rebar_data.extend([['+', x, y], ['B', rebar_dia2, ' ']])
    
    # Generate Layer 3 reinforcement coordinates (innermost layer)
    if rebar_num3 > 0:
        # Uniform angular distribution for innermost layer
        angles = np.linspace(0, 360, rebar_num3, endpoint=False)
        
        # Calculate radial position accounting for both outer layers and spacing
        # Position: radius - cover - layer1_dia - spacing1 - layer2_dia - spacing2 - half_layer3_dia
        r_layer3 = (dia_mm / 2 - cover - rebar_dia1 - clear_spacing1to2 - 
                    rebar_dia2 - clear_spacing2to3 - rebar_dia3 / 2)
        
        # Vectorized coordinate conversion for layer 3 bars
        x_coords = r_layer3 * np.cos(np.radians(angles))
        y_coords = r_layer3 * np.sin(np.radians(angles))
        
        # Add coordinate and bar data for each layer 3 bar
        for x, y in zip(x_coords, y_coords):
            rebar_data.extend([['+', x, y], ['B', rebar_dia3, ' ']])
    
    # Return structured DataFrame compatible with PROKON C13 format
    return pd.DataFrame(rebar_data, columns=['Code', 'X/Radius or Bar dia (mm)', 'Y (mm) Angle (deg)'])


def gen_prokon_load(df_pile_forces: pd.DataFrame, 
                   pile_mark: str, 
                   bp_part_start: float,
                   bp_part_length: float, 
                   part: str = 'A') -> pd.DataFrame:
    """
    Generate PROKON-compatible load combinations from structural analysis results for ULS design.
    
    This function processes structural analysis load data and transforms it into PROKON C13
    format for Ultimate Limit State column design verification. It handles load combination
    filtering, sign convention conversion, and moment pairing for accurate representation
    of bored pile loading conditions in PROKON analysis.

    The load processing includes:
    - Intelligent filtering of relevant load combinations for specific pile parts
    - Sign convention transformation between structural analysis and PROKON formats
    - Load averaging and moment pairing for top and bottom pile section analysis
    - Systematic load case organization for comprehensive design verification
    """
    # Filter pile forces for the target pile using optimized string operations
    # Extract only relevant load combinations to minimize processing overhead
    df_target = df_pile_forces[df_pile_forces['Line'].str.startswith(pile_mark + '_')].copy()
    
    # Handle empty dataset case with consistent return format
    # Ensures robust operation even when specified pile has no analysis data
    if df_target.empty:
        return pd.DataFrame(columns=['Load Case', 'Designation', 'P (kN)', 'Mx top (kNm)', 
                                   'My top (kNm)', 'Mx bot (kNm)', 'My bot (kNm)'])
    bp_part_start = floor(bp_part_start)  # Ensure start is an integer

    # Ceiling function ensures conservative boundary definition
    bp_part_length = ceil(bp_part_length)

    # Filter load combinations based on part selection
    if part == 'A':
        # Define Part A stations: pile top, mid-station, and integer stations up to boundary
        target_pile = [f'{pile_mark}_T', f'{pile_mark}_0.5']
        target_pile.extend(f'{pile_mark}_{i}' for i in range(1, bp_part_length))
        filtered_pile_load = df_target[df_target['Line'].isin(target_pile)]
    else:
        # Part B
        target_pile = [f'{pile_mark}_B']
        target_pile.extend(f'{pile_mark}_{i}' for i in range(bp_part_start, bp_part_length))
        filtered_pile_load = df_target[df_target['Line'].isin(target_pile)]

    # Reset index for systematic processing of load pairs
    # Enables efficient iteration through consecutive load points
    filtered_pile_load = filtered_pile_load.reset_index(drop=True)
    
    # Process load data in pairs for top/bottom section analysis
    # Each pair represents load conditions at adjacent pile stations
    prokon_loads = []
    for i in range(0, len(filtered_pile_load), 2):
        if i + 1 < len(filtered_pile_load):
            # Extract consecutive load points for section analysis
            row1, row2 = filtered_pile_load.iloc[i], filtered_pile_load.iloc[i + 1]
            
            # Calculate averaged axial force and apply PROKON sign convention
            # Negative sign converts analysis convention to compression-positive
            avg_axial = -(row1['P'] + row2['P']) / 2
            
            # Process moments with proper sign convention for PROKON
            # Top section moments use first station values with sign adjustment
            mx_top = -row1['M2']  # Convert to PROKON X-moment convention
            my_top = -row1['M3']  # Convert to PROKON Y-moment convention
            
            # Bottom section moments use second station values as absolute values
            # Absolute values ensure consistent moment direction representation
            mx_bot = abs(row2['M2'])
            my_bot = abs(row2['M3'])
            
            # Create load case entry with systematic numbering and identification
            prokon_loads.append([
                len(prokon_loads) + 1,  # Sequential load case numbering
                f"{row1['Line']}-{row1['OutputCase']}",  # Descriptive case identifier
                avg_axial,              # Averaged axial force
                mx_top,                 # Top section X-moment
                my_top,                 # Top section Y-moment  
                mx_bot,                 # Bottom section X-moment
                my_bot                  # Bottom section Y-moment
            ])
    
    # Return structured DataFrame compatible with PROKON C13 format
    return pd.DataFrame(prokon_loads, columns=['Load Case', 'Designation', 'P (kN)', 
                                              'Mx top (kNm)', 'My top (kNm)', 'Mx bot (kNm)', 'My bot (kNm)'])


def write_c13_file(file_path: str, 
                  file_name_prokon: str, 
                  df_rebar_pattern: pd.DataFrame, 
                  df_prokon_load: pd.DataFrame, 
                  length: float, 
                  fcu: float, 
                  fy: float, 
                  es: float, 
                  angle: Any, 
                  top_condition: str, 
                  bottom_condition: str, 
                  beta: float,
                  column_braced: Any, 
                  omit_slenderness_moments: Any, 
                  allow_angled_neutral_axis: Any, 
                  code: str) -> None:
    """
    Generate complete PROKON C13 column design file with all required sections and parameters.
    
    This function creates a comprehensive PROKON C13 file containing all necessary data for
    Ultimate Limit State column design analysis including reinforcement layout, load combinations,
    material properties, and design parameters. The file format follows PROKON specifications
    exactly, ensuring seamless import and analysis within the PROKON software environment.

    The file generation process includes:
    - Complete file header with timestamp and project identification
    - Reinforcement section with precise bar coordinates and section definition
    - Material properties and design parameters per specified design codes
    - Load combinations formatted for Ultimate Limit State analysis
    - Analysis options and boundary conditions for accurate structural modeling
    """
    # Handle NaN and empty values for PROKON compatibility
    # PROKON requires specific formatting for optional parameters
    angle = ' ' if pd.isna(angle) else angle
    column_braced = ' ' if pd.isna(column_braced) else column_braced
    omit_slenderness_moments = ' ' if pd.isna(omit_slenderness_moments) else omit_slenderness_moments
    allow_angled_neutral_axis = ' ' if pd.isna(allow_angled_neutral_axis) else allow_angled_neutral_axis
    
    # Build complete file content in memory for efficient writing
    # This approach minimizes I/O operations and improves performance
    lines = []
    now = datetime.now()
    
    # Generate file header with PROKON version and timestamp information
    # Header format must match PROKON specifications exactly
    lines.extend([
        'General Column Design - Ver W3.1.04 - 13 May 2019,  File Version:2\n',
        '------------------------------------------------------------------\n',
        f'Created: {now.strftime("%#m/%#d/%Y %#I:%#M:%S %p")}\n',
        '\n',
        f'TITLE : {file_name_prokon}\n',
        'COLUMN Section\n',
        ' -------------- ------------- ------------\n',
        '|COLUMN Section|             |            |\n',
        '|Code          |X/Radius or  |Y (mm)      |\n',
        '|              |Bar dia. (mm)|Angle (°)   |\n',
        ' -------------- ------------- ------------\n'
    ])
    
    # Add reinforcement pattern data with optimized formatting
    # Each row represents either coordinates or bar specifications
    for _, row in df_rebar_pattern.iterrows():
        lines.append(f"|{row['Code']}|{row['X/Radius or Bar dia (mm)']}|{row['Y (mm) Angle (deg)']}|\n")
    
    # Generate general design parameters section
    # Contains material properties and analysis options critical for accurate design
    lines.extend([
        ' -------------- ------------- ------------\n',
        'GENERAL\n',
        ' ------------------------- ----------------- ---\n',
        '|GENERAL                  |                 |   |\n',
        f'|Lo ~u(m)                 |{length}|   |\n',
        f'|fcu ~u(MPa)              |{fcu}|   |\n',
        f'|fy ~u(MPa)               |{fy}|   |\n',
        f'|Es ~u(GPa)               |{es}|   |\n',
        f'|Rot. angle               |{angle}|   |\n',
        f'|Column braced            |                 |{column_braced}  |\n',
        f'|Omit slenderness moments |                 |{omit_slenderness_moments}  |\n',
        f'|Allow angled neutral axis|                 |{allow_angled_neutral_axis}  |\n',
        '|End conditions           |                 |  |\n',
        f'|Top                      |{top_condition}|   |\n',
        f'|Bottom                   |{bottom_condition}|   |\n',
        '|Effective length factor  |                 |   |\n',
        f'|#b                       |{beta}|   |\n',
        ' ------------------------- ----------------- ---\n',
        ' ---------------------------- ---\n',
        '|ßd - see ACI318 10.0        |  0|\n',
        '|% of moments that are as a  ¦  0|\n',
        '|result of sway - X direction|   |\n',
        '|% of moments that are as a  ¦  0|\n',
        '|result of sway - Y direction|   |\n',
        '|ds  -  see ACI318 10.11.5.1 |  1|\n',
        ' ---------------------------- ---\n',
        ' --------------------------- -----\n',
        '|Building inclination (rad.)|0.005|\n',
        '|Eff. creep ratio to 5.8.4  |    0|\n',
        ' --------------------------- -----\n',
        '\n',
        'Load CASES:\n',
        ' ---------------------------- --------------- ------- ------------ ------------ ------------ ------------\n',
        '|LoadS (ULTIMATE LIMIT STATE)|               |       |            |            |            |            |\n',
        '|Load case                   |Designation    |P (kN) |Mx top (kNm)|My top (kNm)|Mx bot (kNm)|My bot (kNm)|\n',
        ' ---------------------------- --------------- ------- ------------ ------------ ------------ ------------\n'
    ])
    
    # Load cases - vectorized string formatting
    for _, row in df_prokon_load.iterrows():
        lines.append(f"|{row['Load Case']}|{row['Designation']}|{row['P (kN)']}|"
                    f"{row['Mx top (kNm)']}|{row['My top (kNm)']}|{row['Mx bot (kNm)']}|{row['My bot (kNm)']}|\n")
    
    lines.extend([
        ' ---------------------------- --------------- ------- ------------ ------------ ------------ ------------\n',
        '\n',
        f'Design Code:{code}'
    ])
    
    # Write all at once
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)


def _gen_c13_worker(args: Tuple[str, str, Dict[str, Any], pd.DataFrame, str]) -> str:
    """
    Worker function for parallel PROKON C13 file generation in multiprocessing environment.
    
    This function serves as the execution unit for concurrent C13 file generation, handling
    the complete workflow from reinforcement pattern generation through final file writing.
    It is designed for robust operation within multiprocessing pools, providing comprehensive
    error handling and progress reporting for batch generation workflows.

    The worker process encompasses:
    - Reinforcement pattern generation with vectorized coordinate calculations
    - Load combination processing and PROKON format transformation
    - Complete C13 file assembly with proper formatting and structure
    - Error handling and result reporting for multiprocessing coordination
    """
    try:
        # Unpack arguments tuple for individual processing
        # Each worker receives complete parameter set for independent operation
        pile_mark, part, params, df_pile_forces, output_folder = args        
        # Generate reinforcement pattern using optimized coordinate calculations
        # Creates complete multi-layer bar arrangement compatible with PROKON format
        df_rebar_pattern = gen_prokon_rebar(
            params['dia'], params['cover'], params['rebar_num1'], params['rebar_dia1'],
            params['rebar_num2'], params['rebar_dia2'], params['rebar_num3'], params['rebar_dia3'],
            params['clear_spacing1to2'], params['clear_spacing2to3']
        )
        
        # Process structural analysis loads for PROKON compatibility
        # Transforms load combinations to proper format and sign conventions
        df_prokon_load = gen_prokon_load(df_pile_forces, pile_mark, params['bp_part_start'], params['bp_part_length'], part)

        # Generate systematic file naming for organized output management
        # Format: PileMark_PartSection for clear identification and tracking
        file_name = f"{pile_mark}_Part{part}"
        file_path = os.path.join(output_folder, f"{file_name}.C13")
        
        # Create complete PROKON C13 file with all required sections
        # Includes reinforcement, materials, loads, and analysis parameters
        write_c13_file(
            file_path, file_name, df_rebar_pattern, df_prokon_load,
            params['length'], params['fcu'], params['fy'], params['es'],
            params['angle'], params['top_condition'], params['bottom_condition'],
            params['beta'], params['column_braced'], params['omit_slenderness_moments'],
            params['allow_angled_neutral_axis'], params['code']
        )
        
        # Return success confirmation with file identifier for progress tracking
        return f"Successfully generated: {file_name}.C13"
        
    except Exception as e:
        # Comprehensive error handling with detailed diagnostic information
        # Enables identification and resolution of specific processing issues
        return f"Error generating {pile_mark}_Part{part}: {str(e)}"


def gen_all_c13(file_paths: Any, 
                safe_mdbs: Any, 
                excel_inputs: Any, 
                excel_outputs: Any, 
                design_results: Any, 
                log_callback: Optional[Callable[[str], None]] = None) -> None:
    """
    Generate comprehensive batch of PROKON C13 files with optimized parallel processing.
    
    This function orchestrates the complete batch generation workflow for PROKON C13 column
    design files, utilizing multiprocessing for optimal performance and comprehensive progress
    monitoring. It processes entire foundation systems efficiently, handling hundreds of pile
    configurations with robust error handling and detailed progress reporting.
    """
    # Extract design schedule and output configuration from input data structures
    # BPRebar contains complete reinforcement schedule for all foundation elements
    df_bp_schedule = excel_inputs.BPRebar.copy()
    output_folder = file_paths.ResultProkonFolder
    df_pile_forces = excel_outputs.PileULS.copy()
    
    # Ensure output directory exists with proper permissions for file writing
    # Creates complete directory structure if intermediate directories missing
    os.makedirs(output_folder, exist_ok=True)
    
    # Initialize progress monitoring and logging system
    # Total task count enables accurate progress reporting and time estimation
    total_tasks = len(df_bp_schedule)
    if log_callback:
        log_callback(f"Starting generation of {total_tasks} Bored Pile Prokon files...")
    
    # Prepare task list with optimized data structure for parallel processing
    # Each task contains complete parameter set for independent worker execution
    tasks = []
    for _, row in df_bp_schedule.iterrows():
        # Organize design parameters into structured dictionary for worker processing
        # Comprehensive parameter set ensures independent worker operation
        params = {
            # Geometric properties for section definition and coordinate calculations
            'dia': row['Diameter (m)'],
            'cover': row['Cover (mm)'],
            'length': row['Length (m)'],
            
            # Multi-layer reinforcement configuration for complete bar arrangement
            'rebar_num1': row['Layer 1 Rebar Num'],
            'rebar_dia1': row['Layer 1 Rebar Dia (mm)'],
            'rebar_num2': row['Layer 2 Rebar Num'],
            'rebar_dia2': row['Layer 2 Rebar Dia (mm)'],
            'rebar_num3': row['Layer 3 Rebar Num'],
            'rebar_dia3': row['Layer 3 Rebar Dia (mm)'],
            'clear_spacing1to2': row['Rebar Clear Spacing (mm)'],
            'clear_spacing2to3': row['Rebar Clear Spacing (mm)'],
            
            # Material properties for strength calculations and code compliance
            'fcu': row['Fcu (MPa)'],
            'fy': row['Fy (MPa)'],
            'es': row['Es (GPa)'],
            
            # Analysis parameters for structural modeling and design verification
            'angle': row['Rotational Angle (Empty or Deg)'],
            'top_condition': row['Top Condition'],
            'bottom_condition': row['Bottom Condition'],
            'beta': row['Effective Length Factor (Beta)'],
            'column_braced': row['Column Braced (Empty or "x")'],
            'omit_slenderness_moments': row['Omit Slenderness Moment (Empty or "x")'],
            'allow_angled_neutral_axis': row['Allow Angled Neutral Axis (Empty or "x")'],
            'code': row['Code'],
            'bp_part_start': row['BP Part Start (m)'],
            'bp_part_length': row['BP Part Length (m)']
        }
        
        # Create task tuple with all parameters for independent worker processing
        # Format optimized for multiprocessing serialization and worker execution
        tasks.append((row['Pile Mark'], row['Part'], params, df_pile_forces, output_folder))
    
    # Configure multiprocessing with optimal core utilization
    # Balance between performance and system resource availability
    num_processes = min(os.cpu_count() or 1, 8)  # Cap at 8 processes for system stability
    
    # Execute parallel processing with comprehensive progress monitoring
    # Multiprocessing pool ensures efficient resource utilization and error isolation
    with multiprocessing.Pool(processes=num_processes) as pool:
        # Initialize progress bar for real-time visual feedback
        with tqdm(total=total_tasks, desc='Generating Prokon C13 Files') as pbar:
            # Process tasks with unordered completion for optimal throughput
            # imap_unordered enables immediate processing of completed tasks
            for i, result in enumerate(pool.imap_unordered(_gen_c13_worker, tasks)):
                pbar.update(1)  # Update progress bar for each completed task
                
                # Provide periodic status updates through custom logging system
                # Balances informative feedback with reasonable logging frequency
                if log_callback and (i + 1) % max(1, total_tasks // 20) == 0:
                    percent = int((i + 1) * 100 / total_tasks)
                    log_callback(f"Progress: {i + 1}/{total_tasks} ({percent}%)")
    
    # Generate completion confirmation with timestamp for audit trail
    # Final status message confirms successful batch processing completion
    if log_callback:
        log_callback(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}: Generated all {total_tasks} Prokon files!')