﻿"""
Pile Drawing Functions

Specialized functions for drawing different pile types (DHP, SHP, BP) with accurate dimensions
and proper DXF layer assignments.
"""

import math
from typing import Optional, Tuple, Callable
from ezdxf.enums import TextEntityAlignment

from .text_manager import TextPlacementManager
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)


def draw_dhp_pile(msp, x: float, y: float, pile_number: int = 1, 
                  text_manager: Optional[TextPlacementManager] = None,
                  log_callback: Optional[Callable] = None) -> bool:
    """
    Draw a DHP (Driven H-Pile) as an I-section with specified dimensions.
    
    DHP Specifications:
    - Height: 337.9mm
    - Width: 325.4mm  
    - Top & Bottom Flange Thickness: 30.4mm
    - Web Thickness: 30.3mm
    
    Args:
        msp: DXF model space
        x, y: Pile center coordinates in meters
        pile_number: Pile number for labeling
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
        
    Returns:
        bool: True if successfully drawn, False otherwise
    """
    log_function_entry(log_callback, "draw_dhp_pile", 
                      x=x, y=y, pile_number=pile_number)
    
    try:
        enhanced_log(log_callback, f"=== DRAWING DHP PILE P{pile_number} ===", 'INFO')
        
        with create_timed_logger(log_callback, f"DHP pile P{pile_number} drawing") as dhp_timer:
            # Validate input parameters
            if msp is None:
                enhanced_log(log_callback, "Invalid DXF model space provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", False)
                raise ValueError("Invalid DXF model space provided")
            
            log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", True)
            log_constraint_check(log_callback, "Valid pile number", pile_number, "> 0", pile_number > 0)
            
            enhanced_log(log_callback, f"Drawing DHP pile P{pile_number} at coordinates ({x:.3f}, {y:.3f})", 'INFO')
            
            # Convert mm to meters for drawing
            log_algorithm_step(log_callback, "DHP pile drawing", "Converting specifications to meters")
            height = 0.3379      # 337.9mm
            width = 0.3254       # 325.4mm (corrected from user specs)
            flange_thickness = 0.0304  # 30.4mm
            web_thickness = 0.0303     # 30.3mm
            
            log_calculation_result(log_callback, "DHP height", height, "m")
            log_calculation_result(log_callback, "DHP width", width, "m")
            log_calculation_result(log_callback, "Flange thickness", flange_thickness, "m")
            log_calculation_result(log_callback, "Web thickness", web_thickness, "m")
            
            # Calculate half dimensions for centering
            log_algorithm_step(log_callback, "DHP pile drawing", "Calculating half dimensions for centering")
            half_height = height / 2
            half_width = width / 2
            half_web = web_thickness / 2
            
            log_calculation_result(log_callback, "Half height", half_height, "m")
            log_calculation_result(log_callback, "Half width", half_width, "m")
            log_calculation_result(log_callback, "Half web", half_web, "m")
            
            # Create I-section profile with correct topology
            log_algorithm_step(log_callback, "DHP pile drawing", "Creating I-section profile points")
            # Start from top-left, go clockwise
            i_section_points = [
                # Top flange - left side
                (x - half_width, y + half_height),
                (x + half_width, y + half_height),
                (x + half_width, y + half_height - flange_thickness),
                # Web - right side
                (x + half_web, y + half_height - flange_thickness),
                (x + half_web, y - half_height + flange_thickness),
                # Bottom flange - right side  
                (x + half_width, y - half_height + flange_thickness),
                (x + half_width, y - half_height),
                (x - half_width, y - half_height),
                # Bottom flange - left side
                (x - half_width, y - half_height + flange_thickness),
                # Web - left side
                (x - half_web, y - half_height + flange_thickness),
                (x - half_web, y + half_height - flange_thickness),
                # Back to top flange
                (x - half_width, y + half_height - flange_thickness)
            ]
            
            log_calculation_result(log_callback, "I-section profile points", len(i_section_points))
            enhanced_log(log_callback, f"Generated {len(i_section_points)} points for I-section profile", 'DEBUG')
            
            # Create the I-section polyline
            log_algorithm_step(log_callback, "DHP pile drawing", "Creating I-section polyline in DXF")
            i_section = msp.add_lwpolyline(i_section_points)
            i_section.close()
            i_section.dxf.layer = 'PILES_DHP'
            
            enhanced_log(log_callback, f"I-section polyline created on layer 'PILES_DHP'", 'DEBUG')
            log_validation_result(log_callback, "I-section polyline creation", True, 
                                "Successfully created closed polyline")
            
            # Add pile label if text manager is provided
            if text_manager:
                log_algorithm_step(log_callback, "DHP pile drawing", "Adding pile labels")
                _add_pile_labels(msp, x, y, pile_number, "DHP", text_manager, log_callback)
                enhanced_log(log_callback, f"Pile labels added for DHP P{pile_number}", 'DEBUG')
            else:
                enhanced_log(log_callback, "No text manager provided, skipping pile labels", 'DEBUG')
            
            enhanced_log(log_callback, f"DHP pile P{pile_number} successfully drawn at ({x:.3f}, {y:.3f})", 'INFO')
            log_performance_metric(log_callback, "DHP pile drawing success rate", 100, "%")
        
        log_function_exit(log_callback, "draw_dhp_pile", result=True)
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"draw_dhp_pile for P{pile_number}")
        enhanced_log(log_callback, f"Failed to draw DHP pile P{pile_number}: {e}", 'ERROR')
        log_performance_metric(log_callback, "DHP pile drawing success rate", 0, "%")
        log_function_exit(log_callback, "draw_dhp_pile", result=False)
        return False


def draw_shp_pile(msp, x: float, y: float, pile_number: int = 1,
                  text_manager: Optional[TextPlacementManager] = None,
                  log_callback: Optional[Callable] = None) -> bool:
    """
    Draw a SHP (Socket H-Pile) as an I-section with circular socket.
    
    SHP Specifications:
    - I-section: Same as DHP (Height: 337.9mm, Width: 325.4mm, etc.)
    - Socket: Circle with diameter 610mm around the I-section
    
    Args:
        msp: DXF model space
        x, y: Pile center coordinates in meters
        pile_number: Pile number for labeling  
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
        
    Returns:
        bool: True if successfully drawn, False otherwise
    """
    log_function_entry(log_callback, "draw_shp_pile", 
                      x=x, y=y, pile_number=pile_number)
    
    try:
        enhanced_log(log_callback, f"=== DRAWING SHP PILE P{pile_number} ===", 'INFO')
        
        with create_timed_logger(log_callback, f"SHP pile P{pile_number} drawing") as shp_timer:
            # Validate input parameters
            if msp is None:
                enhanced_log(log_callback, "Invalid DXF model space provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", False)
                raise ValueError("Invalid DXF model space provided")
            
            log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", True)
            log_constraint_check(log_callback, "Valid pile number", pile_number, "> 0", pile_number > 0)
            
            enhanced_log(log_callback, f"Drawing SHP pile P{pile_number} at coordinates ({x:.3f}, {y:.3f})", 'INFO')
            
            # First draw the I-section (same as DHP)
            log_algorithm_step(log_callback, "SHP pile drawing", "Setting up I-section specifications")
            height = 0.3379      # 337.9mm
            width = 0.3254       # 325.4mm
            flange_thickness = 0.0304  # 30.4mm
            web_thickness = 0.0303     # 30.3mm
            
            log_calculation_result(log_callback, "SHP I-section height", height, "m")
            log_calculation_result(log_callback, "SHP I-section width", width, "m")
            log_calculation_result(log_callback, "Flange thickness", flange_thickness, "m")
            log_calculation_result(log_callback, "Web thickness", web_thickness, "m")
            
            # Calculate half dimensions for centering
            log_algorithm_step(log_callback, "SHP pile drawing", "Calculating half dimensions for I-section")
            half_height = height / 2
            half_width = width / 2
            half_web = web_thickness / 2
            
            log_calculation_result(log_callback, "Half height", half_height, "m")
            log_calculation_result(log_callback, "Half width", half_width, "m")
            log_calculation_result(log_callback, "Half web", half_web, "m")
            
            # Create I-section profile
            log_algorithm_step(log_callback, "SHP pile drawing", "Creating I-section profile points")
            i_section_points = [
                # Top flange - left side
                (x - half_width, y + half_height),
                (x + half_width, y + half_height),
                (x + half_width, y + half_height - flange_thickness),
                # Web - right side
                (x + half_web, y + half_height - flange_thickness),
                (x + half_web, y - half_height + flange_thickness),
                # Bottom flange - right side
                (x + half_width, y - half_height + flange_thickness),
                (x + half_width, y - half_height),
                (x - half_width, y - half_height),
                # Bottom flange - left side
                (x - half_width, y - half_height + flange_thickness),
                # Web - left side
                (x - half_web, y - half_height + flange_thickness),
                (x - half_web, y + half_height - flange_thickness),
                # Back to top flange
                (x - half_width, y + half_height - flange_thickness)
            ]
            
            log_calculation_result(log_callback, "I-section profile points", len(i_section_points))
            enhanced_log(log_callback, f"Generated {len(i_section_points)} points for I-section profile", 'DEBUG')
            
            # Create the I-section polyline
            log_algorithm_step(log_callback, "SHP pile drawing", "Creating I-section polyline in DXF")
            i_section = msp.add_lwpolyline(i_section_points)
            i_section.close()
            i_section.dxf.layer = 'PILES_SHP'
            
            enhanced_log(log_callback, f"I-section polyline created on layer 'PILES_SHP'", 'DEBUG')
            log_validation_result(log_callback, "I-section polyline creation", True, 
                                "Successfully created closed polyline")

            # Draw circular socket (610mm diameter = 0.61m) - consolidated to PILES_SHP layer
            log_algorithm_step(log_callback, "SHP pile drawing", "Creating circular socket")
            socket_radius = 0.305  # 610mm / 2 = 305mm = 0.305m
            log_calculation_result(log_callback, "Socket radius", socket_radius, "m")
            log_calculation_result(log_callback, "Socket diameter", socket_radius * 2, "m")
            
            socket_circle = msp.add_circle((x, y), socket_radius)
            socket_circle.dxf.layer = 'PILES_SHP'  # Use same layer as I-section
            
            enhanced_log(log_callback, f"Socket circle created with radius {socket_radius:.3f}m on layer 'PILES_SHP'", 'DEBUG')
            log_validation_result(log_callback, "Socket circle creation", True, 
                                f"Successfully created circle with radius {socket_radius:.3f}m")
            
            # Add pile label if text manager is provided
            if text_manager:
                log_algorithm_step(log_callback, "SHP pile drawing", "Adding pile labels")
                _add_pile_labels(msp, x, y, pile_number, "SHP", text_manager, log_callback)
                enhanced_log(log_callback, f"Pile labels added for SHP P{pile_number}", 'DEBUG')
            else:
                enhanced_log(log_callback, "No text manager provided, skipping pile labels", 'DEBUG')
            
            enhanced_log(log_callback, f"SHP pile P{pile_number} successfully drawn at ({x:.3f}, {y:.3f})", 'INFO')
            log_performance_metric(log_callback, "SHP pile drawing success rate", 100, "%")
        
        log_function_exit(log_callback, "draw_shp_pile", result=True)
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"draw_shp_pile for P{pile_number}")
        enhanced_log(log_callback, f"Failed to draw SHP pile P{pile_number}: {e}", 'ERROR')
        log_performance_metric(log_callback, "SHP pile drawing success rate", 0, "%")
        log_function_exit(log_callback, "draw_shp_pile", result=False)
        return False


def draw_bp_pile(msp, x: float, y: float, diameter: float, pile_number: int = 1,
                 text_manager: Optional[TextPlacementManager] = None,
                 log_callback: Optional[Callable] = None) -> bool:
    """
    Draw a BP (Bored Pile) as a circle with specified diameter.
    
    Args:
        msp: DXF model space
        x, y: Pile center coordinates in meters
        diameter: Pile diameter in meters
        pile_number: Pile number for labeling
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
        
    Returns:
        bool: True if successfully drawn, False otherwise
    """
    log_function_entry(log_callback, "draw_bp_pile", 
                      x=x, y=y, diameter=diameter, pile_number=pile_number)
    
    try:
        enhanced_log(log_callback, f"=== DRAWING BP PILE P{pile_number} ===", 'INFO')
        
        with create_timed_logger(log_callback, f"BP pile P{pile_number} drawing") as bp_timer:
            # Validate input parameters
            if msp is None:
                enhanced_log(log_callback, "Invalid DXF model space provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", False)
                raise ValueError("Invalid DXF model space provided")
            
            log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", True)
            log_constraint_check(log_callback, "Valid pile number", pile_number, "> 0", pile_number > 0)
            log_constraint_check(log_callback, "Valid diameter", diameter, "> 0", diameter > 0)
            
            if diameter <= 0:
                enhanced_log(log_callback, f"Invalid diameter value: {diameter}", 'ERROR')
                raise ValueError(f"Diameter must be positive, got {diameter}")
            
            enhanced_log(log_callback, f"Drawing BP pile P{pile_number} at coordinates ({x:.3f}, {y:.3f}) with diameter {diameter:.3f}m", 'INFO')
            
            # Calculate radius
            log_algorithm_step(log_callback, "BP pile drawing", "Calculating pile radius")
            radius = diameter / 2.0
            log_calculation_result(log_callback, "BP pile radius", radius, "m")
            log_calculation_result(log_callback, "BP pile diameter", diameter, "m")
            log_calculation_result(log_callback, "BP pile diameter (mm)", diameter * 1000, "mm")
            
            # Create circle
            log_algorithm_step(log_callback, "BP pile drawing", "Creating circular pile in DXF")
            circle = msp.add_circle((x, y), radius)
            circle.dxf.layer = 'PILES_BP'
            
            enhanced_log(log_callback, f"BP circle created with radius {radius:.3f}m on layer 'PILES_BP'", 'DEBUG')
            log_validation_result(log_callback, "BP circle creation", True, 
                                f"Successfully created circle with radius {radius:.3f}m")
            
            # Add pile label if text manager is provided
            if text_manager:
                log_algorithm_step(log_callback, "BP pile drawing", "Adding pile labels")
                pile_label = f"BP-{diameter*1000:.0f}"
                _add_pile_labels(msp, x, y, pile_number, pile_label, text_manager, log_callback)
                enhanced_log(log_callback, f"Pile labels added for BP P{pile_number} ({pile_label})", 'DEBUG')
            else:
                enhanced_log(log_callback, "No text manager provided, skipping pile labels", 'DEBUG')
            
            enhanced_log(log_callback, f"BP pile P{pile_number} (D={diameter*1000:.0f}mm) successfully drawn at ({x:.3f}, {y:.3f})", 'INFO')
            log_performance_metric(log_callback, "BP pile drawing success rate", 100, "%")        
        log_function_exit(log_callback, "draw_bp_pile", result=True)
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"draw_bp_pile for P{pile_number}")
        enhanced_log(log_callback, f"Failed to draw BP pile P{pile_number}: {e}", 'ERROR')
        log_performance_metric(log_callback, "BP pile drawing success rate", 0, "%")
        log_function_exit(log_callback, "draw_bp_pile", result=False)
        return False


def draw_pile_by_type(msp, x: float, y: float, pile_type: str, diameter: Optional[float] = None,
                      pile_number: int = 1, text_manager: Optional[TextPlacementManager] = None,
                      log_callback: Optional[Callable] = None) -> bool:
    """
    Draw a pile based on its type specification.
    
    Args:
        msp: DXF model space
        x, y: Pile center coordinates in meters
        pile_type: Pile type string ('DHP', 'SHP', 'BP', etc.)
        diameter: Pile diameter in meters (required for BP piles)
        pile_number: Pile number for labeling
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
        
    Returns:
        bool: True if successfully drawn, False otherwise
    """
    log_function_entry(log_callback, "draw_pile_by_type", 
                      x=x, y=y, pile_type=pile_type, diameter=diameter, pile_number=pile_number)
    
    try:
        enhanced_log(log_callback, f"=== DRAWING PILE BY TYPE ===", 'INFO')
        
        with create_timed_logger(log_callback, f"Pile type drawing P{pile_number}") as type_timer:
            # Validate input parameters
            if msp is None:
                enhanced_log(log_callback, "Invalid DXF model space provided", 'ERROR')
                log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", False)
                raise ValueError("Invalid DXF model space provided")
            
            log_constraint_check(log_callback, "Valid DXF model space", msp, "not None", True)
            log_constraint_check(log_callback, "Valid pile number", pile_number, "> 0", pile_number > 0)
            
            if not isinstance(pile_type, str) or not pile_type.strip():
                enhanced_log(log_callback, f"Invalid pile type: {pile_type}", 'ERROR')
                log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", False)
                raise ValueError(f"Invalid pile type: {pile_type}")
            
            log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", True)
            
            pile_type_upper = pile_type.upper().strip()
            enhanced_log(log_callback, f"Drawing pile P{pile_number} of type '{pile_type_upper}' at ({x:.3f}, {y:.3f})", 'INFO')
            log_calculation_result(log_callback, "Normalized pile type", pile_type_upper)
            
            if pile_type_upper == 'DHP':
                log_algorithm_step(log_callback, "Pile type drawing", "Delegating to DHP drawing function")
                result = draw_dhp_pile(msp, x, y, pile_number, text_manager, log_callback)
                log_validation_result(log_callback, "DHP pile drawing", result, 
                                    f"DHP pile P{pile_number} drawing completed")
                
            elif pile_type_upper == 'SHP':
                log_algorithm_step(log_callback, "Pile type drawing", "Delegating to SHP drawing function")
                result = draw_shp_pile(msp, x, y, pile_number, text_manager, log_callback)
                log_validation_result(log_callback, "SHP pile drawing", result, 
                                    f"SHP pile P{pile_number} drawing completed")
                
            elif pile_type_upper == 'BP':
                log_algorithm_step(log_callback, "Pile type drawing", "Validating BP diameter and delegating to BP drawing function")
                if diameter is None:
                    enhanced_log(log_callback, f"BP pile P{pile_number} requires diameter specification", 'ERROR')
                    log_constraint_check(log_callback, "BP diameter provided", diameter, "not None", False)
                    raise ValueError(f"BP pile P{pile_number} requires diameter specification")
                
                log_constraint_check(log_callback, "BP diameter provided", diameter, "not None", True)
                log_constraint_check(log_callback, "BP diameter valid", diameter, "> 0", diameter > 0)
                
                result = draw_bp_pile(msp, x, y, diameter, pile_number, text_manager, log_callback)
                log_validation_result(log_callback, "BP pile drawing", result, 
                                    f"BP pile P{pile_number} (D={diameter*1000:.0f}mm) drawing completed")
                
            else:
                # Unknown pile type - cannot draw
                enhanced_log(log_callback, f"Unknown pile type '{pile_type_upper}', cannot draw", 'ERROR')
                log_constraint_check(log_callback, "Supported pile type", pile_type_upper, "in [DHP, SHP, BP]", False)
                raise ValueError(f"Unknown pile type: {pile_type}")
            
            if result:
                enhanced_log(log_callback, f"Successfully drew pile P{pile_number} of type '{pile_type_upper}'", 'INFO')
                log_performance_metric(log_callback, "Pile type drawing success rate", 100, "%")
            else:
                enhanced_log(log_callback, f"Failed to draw pile P{pile_number} of type '{pile_type_upper}'", 'WARNING')
                log_performance_metric(log_callback, "Pile type drawing success rate", 0, "%")
        
        log_function_exit(log_callback, "draw_pile_by_type", result=result)
        return result
            
    except Exception as e:
        log_error_with_context(log_callback, e, f"draw_pile_by_type for P{pile_number} type '{pile_type}'")
        enhanced_log(log_callback, f"Failed to draw pile P{pile_number} of type '{pile_type}': {e}", 'ERROR')
        log_performance_metric(log_callback, "Pile type drawing success rate", 0, "%")
        log_function_exit(log_callback, "draw_pile_by_type", result=False)
        return False


def _add_pile_labels(msp, x: float, y: float, pile_number: int, pile_type: str,
                     text_manager: TextPlacementManager, 
                     log_callback: Optional[Callable] = None) -> None:
    """
    Add pile number and type labels next to the pile.
    
    Args:
        msp: DXF model space
        x, y: Pile center coordinates
        pile_number: Pile number for labeling
        pile_type: Pile type string
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
        
    Raises:
        ValueError: If required parameters are invalid
    """
    log_function_entry(log_callback, "_add_pile_labels", 
                      x=x, y=y, pile_number=pile_number, pile_type=pile_type)
    
    try:
        enhanced_log(log_callback, f"=== ADDING PILE LABELS ===", 'DEBUG')
        
        with create_timed_logger(log_callback, f"Pile labeling P{pile_number}") as label_timer:
            # Validate input parameters
            if not isinstance(pile_number, int) or pile_number <= 0:
                enhanced_log(log_callback, f"Invalid pile number: {pile_number}", 'ERROR')
                log_constraint_check(log_callback, "Valid pile number", pile_number, "int > 0", False)
                raise ValueError(f"Invalid pile number: {pile_number}")
            
            log_constraint_check(log_callback, "Valid pile number", pile_number, "int > 0", True)
            
            if not isinstance(pile_type, str) or not pile_type.strip():
                enhanced_log(log_callback, f"Invalid pile type: {pile_type}", 'ERROR')
                log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", False)
                raise ValueError(f"Invalid pile type: {pile_type}")
            
            log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", True)
            
            if text_manager is None:
                enhanced_log(log_callback, "TextPlacementManager is required for pile labeling", 'ERROR')
                log_constraint_check(log_callback, "Valid text manager", text_manager, "not None", False)
                raise ValueError("TextPlacementManager is required for pile labeling")
            
            log_constraint_check(log_callback, "Valid text manager", text_manager, "not None", True)
            
            enhanced_log(log_callback, f"Adding labels for pile P{pile_number} ({pile_type}) at ({x:.3f}, {y:.3f})", 'DEBUG')
            
            # Calculate label offset based on pile type to avoid overlap
            log_algorithm_step(log_callback, "Pile labeling", "Calculating label offset based on pile type")
            if pile_type in ['DHP', 'SHP']:
                label_offset = 0.4  # 40cm to the right for H-piles
            else:
                label_offset = 0.35  # 35cm to the right for circular piles
            
            log_calculation_result(log_callback, "Label offset", label_offset, "m")
            
            # Add pile number label
            log_algorithm_step(log_callback, "Pile labeling", "Adding pile number label")
            pile_label = f"P{pile_number}"
            final_label_x, final_label_y = text_manager.add_text_position(
                x + label_offset, y, pile_label, 0.25
            )
            
            log_calculation_result(log_callback, "Pile number label position", (final_label_x, final_label_y))
            enhanced_log(log_callback, f"Pile number label '{pile_label}' positioned at ({final_label_x:.3f}, {final_label_y:.3f})", 'DEBUG')
            
            text = msp.add_text(
                pile_label,
                dxfattribs={
                    'layer': 'TEXT',
                    'height': 0.25
                }
            )
            text.set_placement((final_label_x, final_label_y), align=TextEntityAlignment.LEFT)
            
            log_validation_result(log_callback, "Pile number label creation", True, 
                                f"Created text '{pile_label}' on layer 'TEXT'")
            
            # Add pile type label below pile number
            log_algorithm_step(log_callback, "Pile labeling", "Adding pile type label")
            type_final_x, type_final_y = text_manager.add_text_position(
                x + label_offset, y - 0.3, pile_type, 0.2
            )
            
            log_calculation_result(log_callback, "Pile type label position", (type_final_x, type_final_y))
            enhanced_log(log_callback, f"Pile type label '{pile_type}' positioned at ({type_final_x:.3f}, {type_final_y:.3f})", 'DEBUG')
            
            type_text = msp.add_text(
                pile_type,
                dxfattribs={
                    'layer': 'TEXT',
                    'height': 0.2,
                    'color': 3  # Green color for pile type
                }
            )
            type_text.set_placement((type_final_x, type_final_y), align=TextEntityAlignment.LEFT)
            
            log_validation_result(log_callback, "Pile type label creation", True, 
                                f"Created text '{pile_type}' on layer 'TEXT' with green color")
            
            enhanced_log(log_callback, f"Successfully added labels for pile P{pile_number} ({pile_type})", 'DEBUG')
            log_performance_metric(log_callback, "Pile labeling success rate", 100, "%")
        
        log_function_exit(log_callback, "_add_pile_labels")
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"_add_pile_labels for P{pile_number}")
        enhanced_log(log_callback, f"Failed to add labels for pile P{pile_number}: {e}", 'ERROR')
        log_performance_metric(log_callback, "Pile labeling success rate", 0, "%")
        log_function_exit(log_callback, "_add_pile_labels")
        raise


def get_pile_drawing_bounds(pile_type: str, diameter: Optional[float] = None,
                           log_callback: Optional[Callable] = None) -> Tuple[float, float]:
    """
    Get the approximate drawing bounds (width, height) for a pile type.
    
    Args:
        pile_type: Pile type string ('DHP', 'SHP', 'BP', etc.)
        diameter: Pile diameter in meters (for BP piles)
        log_callback: Logging callback
        
    Returns:
        Tuple of (width, height) in meters
    """
    log_function_entry(log_callback, "get_pile_drawing_bounds", 
                      pile_type=pile_type, diameter=diameter)
    
    try:
        enhanced_log(log_callback, f"=== CALCULATING PILE DRAWING BOUNDS ===", 'DEBUG')
        
        with create_timed_logger(log_callback, f"Pile bounds calculation") as bounds_timer:
            # Validate input parameters
            if not isinstance(pile_type, str) or not pile_type.strip():
                enhanced_log(log_callback, f"Invalid pile type: {pile_type}", 'ERROR')
                log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", False)
                raise ValueError(f"Invalid pile type: {pile_type}")
            
            log_constraint_check(log_callback, "Valid pile type", pile_type, "non-empty string", True)
            
            pile_type_upper = pile_type.upper().strip()
            enhanced_log(log_callback, f"Calculating drawing bounds for pile type '{pile_type_upper}'", 'DEBUG')
            log_calculation_result(log_callback, "Normalized pile type", pile_type_upper)
            
            if pile_type_upper == 'DHP':
                log_algorithm_step(log_callback, "Pile bounds calculation", "Using DHP specifications")
                bounds = (0.3254, 0.3379)  # Width, Height from spec
                log_calculation_result(log_callback, "DHP width", bounds[0], "m")
                log_calculation_result(log_callback, "DHP height", bounds[1], "m")
                enhanced_log(log_callback, f"DHP bounds: {bounds[0]:.4f}m x {bounds[1]:.4f}m", 'DEBUG')
                
            elif pile_type_upper == 'SHP':
                log_algorithm_step(log_callback, "Pile bounds calculation", "Using SHP socket specifications")
                # Socket diameter is larger than I-section
                bounds = (0.61, 0.61)  # Socket diameter 610mm
                log_calculation_result(log_callback, "SHP socket diameter", bounds[0], "m")
                enhanced_log(log_callback, f"SHP bounds: {bounds[0]:.2f}m x {bounds[1]:.2f}m", 'DEBUG')
                
            elif pile_type_upper == 'BP':
                log_algorithm_step(log_callback, "Pile bounds calculation", "Using BP diameter specifications")
                if diameter is None:
                    enhanced_log(log_callback, "Diameter is required for BP pile bounds calculation", 'ERROR')
                    log_constraint_check(log_callback, "BP diameter provided", diameter, "not None", False)
                    raise ValueError("Diameter is required for BP pile bounds calculation")
                
                log_constraint_check(log_callback, "BP diameter provided", diameter, "not None", True)
                log_constraint_check(log_callback, "BP diameter valid", diameter, "> 0", diameter > 0)
                
                if diameter <= 0:
                    enhanced_log(log_callback, f"Invalid diameter value: {diameter}", 'ERROR')
                    raise ValueError(f"Diameter must be positive, got {diameter}")
                
                bounds = (diameter, diameter)
                log_calculation_result(log_callback, "BP diameter", diameter, "m")
                enhanced_log(log_callback, f"BP bounds: {bounds[0]:.3f}m x {bounds[1]:.3f}m", 'DEBUG')
                
            else:
                enhanced_log(log_callback, f"Unknown pile type '{pile_type_upper}' for bounds calculation", 'ERROR')
                log_constraint_check(log_callback, "Supported pile type", pile_type_upper, "in [DHP, SHP, BP]", False)
                raise ValueError(f"Unknown pile type for bounds calculation: {pile_type}")
            
            log_validation_result(log_callback, "Pile bounds calculation", True, 
                                f"Successfully calculated bounds: {bounds[0]:.3f}m x {bounds[1]:.3f}m")
            log_performance_metric(log_callback, "Pile bounds calculation success rate", 100, "%")
        
        log_function_exit(log_callback, "get_pile_drawing_bounds", result=bounds)
        return bounds
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"get_pile_drawing_bounds for type '{pile_type}'")
        enhanced_log(log_callback, f"Failed to calculate pile drawing bounds for type '{pile_type}': {e}", 'ERROR')
        log_performance_metric(log_callback, "Pile bounds calculation success rate", 0, "%")
        log_function_exit(log_callback, "get_pile_drawing_bounds", result=None)
        raise

