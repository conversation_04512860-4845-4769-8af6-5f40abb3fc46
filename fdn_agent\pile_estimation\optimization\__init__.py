﻿"""
High-Performance Pile Layout Optimization

This module provides fast, production-optimized pile layout optimization:
- DEAPNSGA3Optimizer: DEAP-based NSGA-III genetic algorithm
- GeneticFitnessEvaluator: Streamlined pile evaluation
- PileRepairOperators: Efficient genetic operators
- DEAPNSGA3Config: Simple configuration

"""

from .nsga3_optimizer import (
    DEAPNSGA3Optimizer,
    DEAPNSGA3Config,
    PileRepairOperators
)

# Import evaluator from layout_generation module
from ..layout_generation.genetic_fitness import (
    GeneticFitnessEvaluator,
    GeneticAlgorithmConfig,
    create_genetic_fitness_evaluator,
    collect_structural_sites
)

# Main exports
__all__ = [
    # Core classes
    'DEAPNSGA3Optimizer',
    'DEAPNSGA3Config',
    'PileRepairOperators',
    'GeneticFitnessEvaluator',
    'GeneticAlgorithmConfig',
    
    # Utility functions
    'collect_structural_sites',
    'create_genetic_fitness_evaluator'
]

