import os
import sqlite3
import sys

import numpy as np
import pandas as pd

# Add the parent directory (workspace root) to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

import config
import _main_class

import os
from read.read_geology import read_input_geology
from read.read_geometry import read_input_geometry
from read.read_loading import read_input_loading
from read.read_property import read_input_property
from read.read_steel import read_input_steel

def setup_safe_api_environment():
    _UseNETCore = False  # True #

    _Marshal_obj = None  # Initialize to None

    if _UseNETCore:
        from pythonnet import load
        load("coreclr")
        import clr as _clr_module
    else:
        import clr as _clr_module
        _clr_module.AddReference("System.Runtime.InteropServices")
        from System.Runtime.InteropServices import Marshal as _Marshal_obj_temp
        _Marshal_obj = _Marshal_obj_temp  # Assign if .NET Framework

    _clr_module.AddReference(config.SAFE_DLL_PATH)

    # Import SAFEv1 as a module
    import SAFEv1 as _SAFEv1_module

    # Configuration variables
    _Remote = config.REMOTE_CONNECTION
    _RemoteComputer = config.REMOTE_COMPUTER_HOSTNAME
    _AttachToInstance = config.ATTACH_TO_INSTANCE
    _SpecifyPath = config.SPECIFY_PROGRAM_PATH
    _ProgramPath = config.SAFE_PROGRAM_PATH

    # Helper object, accessed via the imported SAFEv1 module
    _helper = _SAFEv1_module.cHelper(_SAFEv1_module.Helper())

    return _clr_module, _Marshal_obj, _SAFEv1_module, _Remote, _RemoteComputer, _AttachToInstance, _SpecifyPath, _ProgramPath, _helper


# Call the setup function and unpack its results into module-level variables
clr, Marshal, SAFEv1, Remote, RemoteComputer, AttachToInstance, SpecifyPath, ProgramPath, helper = setup_safe_api_environment()


# --- Function Definitions ---


def connect_to_safe(AttachToInstance, Remote, RemoteComputer, ProgramPath, SpecifyPath, helper_obj):
    """Establishes connection with SAFE application."""
    safe_object = None
    if AttachToInstance:
        try:
            if Remote:
                safe_object = SAFEv1.cOAPI(helper_obj.GetObjectHost(
                    RemoteComputer, "CSI.SAFE.API.ETABSObject"))
            else:
                safe_object = SAFEv1.cOAPI(helper_obj.GetObject(
                    "CSI.SAFE.API.ETABSObject"))
        except Exception as e:
            print(f"Error attaching to SAFE instance: {e}")
            print("No running instance of the program found or failed to attach.")
            sys.exit(-1)
    else:
        if SpecifyPath:
            try:
                if Remote:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectHost(
                        RemoteComputer, ProgramPath))
                else:
                    safe_object = SAFEv1.cOAPI(
                        helper_obj.CreateObject(ProgramPath))
            except Exception as e:
                print(f"Error creating SAFE instance from path: {e}")
                print(
                    f"Cannot start a new instance of the program from {ProgramPath}")
                sys.exit(-1)
        else:
            try:
                if Remote:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectProgIDHost(
                        RemoteComputer, "CSI.SAFE.API.ETABSObject"))
                else:
                    safe_object = SAFEv1.cOAPI(helper_obj.CreateObjectProgID(
                        "CSI.SAFE.API.ETABSObject"))
            except Exception as e:
                print(f"Error creating SAFE instance: {e}")
                print("Cannot start a new instance of the program.")
                sys.exit(-1)

        if safe_object:
            app_start_ret = safe_object.ApplicationStart()
            print(
                f"mySAFEObject.ApplicationStart() returned: {app_start_ret}")
        else:
            print("Failed to create SAFE object.")
            sys.exit(-1)
    return safe_object


def extract_safe22_dataframe(db_tables, selected_table="Point Object Connectivity"):
    """Extracts data from SAFE database tables and returns a DataFrame."""
    print(f"Attempting to extract table: '{selected_table}'")

    table_version = 0
    number_records = 0
    table_data = []
    field_key_list = []
    fields_keys_included = []
    group_name = ""
    ret = -1  # Initialize ret to a value indicating failure

    try:
        [ret, group_name, table_version, fields_keys_included, number_records,
         table_data] = db_tables.GetTableForDisplayArray(
            selected_table, field_key_list, group_name, table_version, fields_keys_included, number_records, table_data)

        print(f"GetTableForDisplayArray for '{selected_table}' returned: {ret}")

        if ret != 0:
            print(
                f"ERROR: SAFE API call GetTableForDisplayArray for table '{selected_table}' failed with return code: {ret}.")
            print(f"       Number of records reported: {number_records}, Fields: {list(fields_keys_included)}")
            return pd.DataFrame()  # Return empty DataFrame if API call was not successful

        print(f"Number of records for '{selected_table}': {number_records}")
        print(f"Fields for '{selected_table}': {list(fields_keys_included)}")

    except Exception as e:
        print(f"ERROR: Failed to get table '{selected_table}': {e}")
        return pd.DataFrame()

    fields_keys_included = list(fields_keys_included)
    table_data = list(table_data)

    num_columns = len(fields_keys_included)

    if not table_data or len(table_data) % num_columns != 0:
        print(f"ERROR: Data for table '{selected_table}' is empty or malformed.")
        print(f"       Data length: {len(table_data)}, Columns: {num_columns}")
        return pd.DataFrame()

    num_rows = len(table_data) // num_columns
    reshaped_data = np.array(table_data).reshape(num_rows, num_columns)
    df = pd.DataFrame(data=reshaped_data, columns=fields_keys_included)
    print(f"Successfully created DataFrame with shape: {df.shape}")
    return df


def setup_file_paths_and_read_inputs(config_obj, main_class_module, main_read_module):
    """Sets up file paths and reads input from Excel files."""
    file_paths = main_class_module.FilePaths()
    file_paths.ExcelProperty = config_obj.EXCEL_PROPERTY_FILE
    file_paths.ExcelGeometry = config_obj.EXCEL_GEOMETRY_FILE
    file_paths.ExcelGeology = config_obj.EXCEL_GEOLOGY_FILE
    file_paths.ExcelLoading = config_obj.EXCEL_LOADING_FILE

    excel_inputs = main_class_module.ExcelInputs()
    if os.path.exists(file_paths.ExcelProperty):
        excel_inputs = read_input_property(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelGeometry):
        excel_inputs = read_input_geometry(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelGeology):
        excel_inputs = read_input_geology(excel_inputs, file_paths)
    if os.path.exists(file_paths.ExcelLoading):
        excel_inputs = read_input_loading(excel_inputs, file_paths)
    if os.path.exists(file_paths.DataSteel):
        excel_inputs = read_input_steel(excel_inputs, file_paths)
    return excel_inputs, file_paths


def initialize_model(sap_model_obj):
    """Initializes the SAFE model and sets units."""
    init_ret = sap_model_obj.InitializeNewModel(SAFEv1.eUnits.kN_m_C)
    print(f"SapModel.InitializeNewModel() returned: {init_ret}")

    _force_units = SAFEv1.eForce.NotApplicable
    _length_units = SAFEv1.eLength.NotApplicable
    _temperature_units = SAFEv1.eTemperature.NotApplicable

    [ret_present_units, _force_units, _length_units, _temperature_units] = sap_model_obj.GetPresentUnits_2(
        _force_units, _length_units, _temperature_units)
    print(
        f"Present units: {_force_units}, {_length_units}, {_temperature_units} (ret: {ret_present_units})")

    _db_force_units = SAFEv1.eForce.NotApplicable
    _db_length_units = SAFEv1.eLength.NotApplicable
    _db_temperature_units = SAFEv1.eTemperature.NotApplicable

    [ret_db_units, _db_force_units, _db_length_units, _db_temperature_units] = sap_model_obj.GetDatabaseUnits_2(
        _db_force_units, _db_length_units, _db_temperature_units)
    print(
        f"Database units: {_db_force_units}, {_db_length_units}, {_db_temperature_units} (ret: {ret_db_units})")
    return init_ret


def import_and_run_analysis(sap_model_obj, model_import_path, model_save_path):
    """Imports data, saves the model, and runs the analysis."""
    file_type = SAFEv1.eFileTypeIO.DBTablesExcel
    import_type = 1  # Assuming Type=1 is a constant for this import operation
    ret_import = sap_model_obj.File.ImportFile(
        model_import_path, file_type, import_type)
    print(f"SapModel.File.ImportFile() returned: {ret_import}")

    db_tables = SAFEv1.cDatabaseTables(sap_model_obj.DatabaseTables)

    analyze_obj = SAFEv1.cAnalyze(sap_model_obj.Analyze)
    ret_save = sap_model_obj.File.Save(model_save_path)
    print(f"SapModel.File.Save() returned: {ret_save}")

    ret_analysis = analyze_obj.RunAnalysis()
    print(f"Analyze.RunAnalysis() returned: {ret_analysis}")
    return db_tables, analyze_obj


def save_df_to_sqlite(df, table_name, db_path):
    """Saves a DataFrame to a SQLite database table with optimized performance."""
    if df.empty:
        print(f"DataFrame for table '{table_name}' is empty. Skipping save.")
        return

    try:
        abs_db_path = os.path.abspath(db_path)
        db_dir = os.path.dirname(abs_db_path)
        os.makedirs(db_dir, exist_ok=True)  # Ensure directory exists

        print(f"Saving to SQLite database: {abs_db_path}")

        conn = sqlite3.connect(abs_db_path)
        cursor = conn.cursor()

        # Performance optimizations for SQLite
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")
        cursor.execute("PRAGMA temp_store=MEMORY")

        cursor.execute(f"DROP TABLE IF EXISTS [{table_name}]")

        columns = []
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                if pd.api.types.is_integer_dtype(df[col]):
                    col_type = "INTEGER"
                else:
                    col_type = "REAL"
            else:
                col_type = "TEXT"
            columns.append(f"[{col}] {col_type}")

        create_table_sql = f"CREATE TABLE [{table_name}] ({', '.join(columns)})"
        cursor.execute(create_table_sql)

        placeholders = ", ".join(["?" for _ in df.columns])
        insert_sql = f"INSERT INTO [{table_name}] VALUES ({placeholders})"

        data_tuples = [tuple(row) for row in df.values]

        cursor.executemany(insert_sql, data_tuples)

        conn.commit()
        cursor.close()
        conn.close()

        print(f"DataFrame saved to SQLite table '{table_name}' in {abs_db_path}")

    except Exception as e:
        print(f"Error saving DataFrame to SQLite table '{table_name}': {e}")
        # Consider re-raising the exception if saving is critical
        # raise


def save_df_to_database(df, table_name, db_path):
    """Saves a DataFrame to a SQLite database."""
    if df.empty:
        print(f"DataFrame for table '{table_name}' is empty. Skipping save.")
        return

    print(f"\n--- Attempting to save table: {table_name} to SQLite ---")
    try:
        save_df_to_sqlite(df, table_name, db_path)
        print(f"Successfully saved '{table_name}' to SQLite database: {db_path}")
    except Exception as sqlite_error:
        print(f"SQLite database save failed for '{table_name}': {sqlite_error}")
        # Optionally re-raise or handle as critical error if SQLite must succeed
        raise
    print(f"--- Completed save attempt for table: {table_name} ---\n")


def main():
    """Main function to run the SAFE automation script."""
    print("Starting SAFE automation script...")

    # Establish SAFE connection
    safe_object = connect_to_safe(
        AttachToInstance, Remote, RemoteComputer, ProgramPath, SpecifyPath, helper)
    if not safe_object:
        print("Failed to connect to SAFE. Exiting.")
        return
    print("SAFE connection established.")

    # Setup file paths and read Excel inputs
    excel_inputs, file_paths_obj = setup_file_paths_and_read_inputs(
        config, _main_class, _main_read)
    print(
        f"File paths set up and inputs read. Excel Properties Path: {file_paths_obj.ExcelProperty}")

    # Get SapModel object
    sap_model_obj = SAFEv1.cSapModel(safe_object.SapModel)
    print("SapModel object obtained.")

    # Initialize model
    initialize_model(sap_model_obj)
    print("Model initialized.")

    # Construct the SQLite database path dynamically
    model_excel_filename = os.path.basename(config.MODEL_IMPORT_EXCEL_FILE)
    db_name_base = os.path.splitext(model_excel_filename)[0]
    sqlite_db_filename = f"{db_name_base}.sqlite"

    # Use ACCESS_DB_DIR from config for the SQLite database directory
    # This keeps the database location consistent with the previous setup,
    # but uses SQLite instead of Access.
    sqlite_db_dir = config.ACCESS_DB_DIR
    if not os.path.exists(sqlite_db_dir):
        os.makedirs(sqlite_db_dir)
        print(f"Created directory for SQLite database: {sqlite_db_dir}")
    dynamic_sqlite_db_path = os.path.join(sqlite_db_dir, sqlite_db_filename)
    print(f"SQLite database will be saved to: {dynamic_sqlite_db_path}")

    # Import data, save model, and run analysis
    db_tables, analyze_obj = import_and_run_analysis(sap_model_obj, config.MODEL_IMPORT_EXCEL_FILE,
                                                     config.MODEL_SAVE_FILE)
    print("Data imported, model saved, and analysis run.")

    # Process Point Object Connectivity
    selected_table = "Point Object Connectivity"
    df_point_object_connectivity = extract_safe22_dataframe(
        db_tables, selected_table)
    if not df_point_object_connectivity.empty:
        print(
            df_point_object_connectivity.head() if not df_point_object_connectivity.empty else "Processed DataFrame is empty.")
        if not df_point_object_connectivity.empty:
            print("Saving to database...")
            save_df_to_database(df_point_object_connectivity, selected_table,
                                dynamic_sqlite_db_path)
    else:
        print(
            f"\nDataFrame for {selected_table} is empty. Skipping processing.")

    # Process Frame Assignments - Local Axes
    selected_table = "Frame Assignments - Local Axes"
    df_frame_assigns_local_axes = extract_safe22_dataframe(
        db_tables, selected_table)
    if not df_frame_assigns_local_axes.empty:
        print(
            df_frame_assigns_local_axes.head() if not df_frame_assigns_local_axes.empty else "Processed DataFrame is empty.")
        if not df_frame_assigns_local_axes.empty:
            save_df_to_database(df_frame_assigns_local_axes,
                                selected_table, dynamic_sqlite_db_path)
    else:
        print(
            f"\nDataFrame for {selected_table} is empty. Skipping processing.")

    # Set load combinations for display
    ret_load_combo = db_tables.SetLoadCombinationsSelectedForDisplay([])
    print(
        f"\nSetLoadCombinationsSelectedForDisplay returned: {ret_load_combo}")

    # Process Joint Displacements
    selected_table = "Joint Displacements"
    df_joint_displacements = extract_safe22_dataframe(
        db_tables, selected_table)
    df_joint_displacements = df_joint_displacements.rename(columns={"UniqueName": "Unique Name",
                                                                    "OutputCase": "Output Case",
                                                                    "CaseType": "Case Type"})
    if not df_joint_displacements.empty:
        print(df_joint_displacements.head() if not df_joint_displacements.empty else "Processed DataFrame is empty.")
        if not df_joint_displacements.empty:
            save_df_to_database(df_joint_displacements,
                                selected_table, dynamic_sqlite_db_path)
    else:
        print(
            f"\nDataFrame for {selected_table} is empty. Skipping processing.")

    # Process Element Forces - Columns
    selected_table = "Element Forces - Columns"
    df_element_forces_columns = extract_safe22_dataframe(
        db_tables, selected_table)
    df_element_forces_columns = df_element_forces_columns.rename(columns={"UniqueName": "Unique Name",
                                                                          "OutputCase": "Output Case",
                                                                          "CaseType": "Case Type",
                                                                          "ElemStation": "Elem Station",})

    if not df_element_forces_columns.empty:
        print(
            df_element_forces_columns.head() if not df_element_forces_columns.empty else "Processed DataFrame is empty.")
        if not df_element_forces_columns.empty:
            save_df_to_database(df_element_forces_columns,
                                selected_table, dynamic_sqlite_db_path)


if __name__ == "__main__":
    main()
    print("SAFE automation script completed successfully.")