﻿"""
Pile Layout Engine - High-Performance Layout Generation System

This module serves as the main layout generation engine that coordinates different
layout strategies based on the complexity of the foundation design.

MAIN PURPOSE: Layout Generation Coordination
- Determines appropriate layout strategy (Case 1, 2, or 4)
- Coordinates simple calculated layouts (Cases 1 & 2)
- Orchestrates complex genetic algorithm optimization (Case 4)
- Provides unified interface for all layout generation needs

LAYOUT CASES:
- Case 1: Single Column + Single Sub-Load Cluster (calculated layout)
- Case 2: Single Wall + Single Sub-Load Cluster (calculated layout)  
- Case 4: Complex layouts (genetic algorithm with multi-objective optimization)

PERFORMANCE FEATURES:
- Automatic case determination and routing
- Fast calculated layouts for simple cases
- High-performance genetic algorithms for complex cases
- Vectorized operations and memory-efficient processing

Used by: pile_workflow_coordinator.py for all layout generation needs
Uses: case_*_layouts.py for specific layout strategies, genetic_fitness.py for evaluation

Author: Foundation Automation System
Date: June 14, 2025
"""

from typing import List, Tuple, Optional, Dict, Any, Callable
from shapely.geometry import Polygon

# Import types and utilities
from ..data_types import Point2D, PileLocation
from ..exceptions import PileLayoutError
from ..pile_cap_geometry.pile_cap_geometry import LocalCoordinateSystem
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_algorithm_step,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)

# Module-level logger configuration
MODULE_NAME = "layout_engine"
enhanced_log(None, f"Loading {MODULE_NAME} module with enhanced logging support", 'INFO')

# Import case-specific handlers
from .case_1_layouts import handle_case_1
from .case_2_layouts import handle_case_2
from .case_4_layouts import handle_case_4, handle_case_4_with_possible_positions
from ..utils.layout_utils import (
    determine_layout_case,
    apply_site_boundary_filter,
    generate_layout_summary
)


def calculate_required_piles(total_load: float, pile_capacity: float, safety_factor: float, log_callback: Optional[Callable] = None) -> int:
    """
    Calculates the minimum number of piles required for a foundation based on the total load,
    individual pile capacity, and a specified safety factor.

    Args:
        total_load (float): The total load (in kN) to be supported by the pile group.
        pile_capacity (float): The load capacity of a single pile (in kN/pile).
        safety_factor (float): A safety factor to apply to the calculation,
                               ensuring the number of piles is sufficient.
        log_callback: Optional callback function for logging

    Returns:
        int: The minimum number of piles required, rounded up to the nearest whole number,
             and ensuring a minimum of 1 pile if inputs are valid.
    """
    log_function_entry(log_callback, "calculate_required_piles", 
                      total_load=total_load, pile_capacity=pile_capacity, safety_factor=safety_factor)
    
    try:
        from math import ceil
        
        enhanced_log(log_callback, f"Calculating required piles for {total_load:.2f} kN total load", 'INFO')
        enhanced_log(log_callback, f"Pile parameters: capacity={pile_capacity:.2f} kN/pile, safety_factor={safety_factor:.2f}", 'DEBUG')
        
        # Input validation
        if pile_capacity <= 0:
            enhanced_log(log_callback, f"Invalid pile capacity: {pile_capacity} kN/pile", 'WARNING')
            log_validation_result(log_callback, "pile_capacity_validation", False, f"Invalid capacity: {pile_capacity}")
            result = 1
            log_function_exit(log_callback, "calculate_required_piles", result)
            return result
            
        if total_load <= 0:
            enhanced_log(log_callback, f"Invalid total load: {total_load} kN", 'WARNING')
            log_validation_result(log_callback, "total_load_validation", False, f"Invalid load: {total_load}")
            result = 1
            log_function_exit(log_callback, "calculate_required_piles", result)
            return result
        
        # Calculate theoretical pile requirement
        theoretical_piles = total_load / pile_capacity
        enhanced_log(log_callback, f"Theoretical pile requirement: {theoretical_piles:.3f} piles", 'DEBUG')
        
        # Apply safety factor
        safety_adjusted_piles = theoretical_piles * safety_factor
        enhanced_log(log_callback, f"Safety-adjusted requirement: {safety_adjusted_piles:.3f} piles", 'DEBUG')
        
        # Round up to nearest whole number
        required_piles = max(1, ceil(safety_adjusted_piles))
        
        # Log calculation results
        enhanced_log(log_callback, f"Required piles calculation: {required_piles} piles", 'INFO')
        log_calculation_result(log_callback, "required_piles", required_piles, "piles")
        log_calculation_result(log_callback, "pile_utilization", f"{(total_load / (required_piles * pile_capacity)):.2%}", "percent")
        
        # Constraint validation
        pile_utilization = total_load / (required_piles * pile_capacity)
        log_constraint_check(log_callback, "pile_utilization", pile_utilization, 1.0 / safety_factor, pile_utilization <= 1.0 / safety_factor)
        
        log_validation_result(log_callback, "required_piles_calculation", True, 
                            f"Calculated {required_piles} piles for {total_load:.2f} kN")
        
        log_function_exit(log_callback, "calculate_required_piles", required_piles)
        return required_piles
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating required piles")
        log_function_exit(log_callback, "calculate_required_piles", "error")
        return 1  # Safe fallback


def generate_pile_layout(sub_clusters: Dict[str, Dict[str, Any]],
                        pile_distribution: Dict[str, int],
                        pile_capacity: float,
                        pile_diameter: float = 0.6,
                        min_spacing: float = 1.5,
                        edge_dist: float = 0.3,
                        site_boundary: Optional[Polygon] = None,
                        initial_local_system: Optional[LocalCoordinateSystem] = None,
                        excel_inputs: Optional[Any] = None,
                        config_overrides: Optional[Dict[str, Any]] = None,
                        log_callback: Optional[Callable] = None) -> Tuple[List[PileLocation], int, List[str]]:
    """
    Main pile layout generation engine that coordinates different layout strategies.
    
    This function serves as the central dispatcher that:
    1. Analyzes the problem complexity
    2. Routes to appropriate layout strategy
    3. Coordinates the generation process
    4. Returns unified results
    
    LAYOUT STRATEGY SELECTION:
    - Case 1: Single column only Simple calculated layout
    - Case 2: Single wall only Simple calculated layout  
    - Case 4: Everything else Advanced genetic algorithm optimization
    
    PERFORMANCE OPTIMIZATION:
    - Automatically detects large-scale problems (5+ clusters or 50+ total piles)
    - Applies production speed optimizations for complex cases
    - Uses vectorized operations and memory-efficient processing
    
    Args:
        sub_clusters: Dictionary of sub-cluster data containing structural elements
        pile_distribution: Dictionary mapping cluster names to required pile counts
        pile_capacity: Load capacity per pile (kN)
        pile_diameter: Diameter of piles (m)
        min_spacing: Minimum spacing between piles (m)
        edge_dist: Minimum distance from pile center to cap edge (m)
        site_boundary: Optional site boundary polygon
        initial_local_system: Optional coordinate system for alignment
        excel_inputs: Optional Excel input data
        config_overrides: Optional configuration overrides for optimization control
        log_callback: Optional logging callback function
        
    Returns:
        Tuple containing:
        - List of pile locations
        - Total number of piles
        - List of warnings/messages
    """
    log_function_entry(log_callback, "generate_pile_layout",
                      sub_clusters_count=len(sub_clusters),
                      total_required_piles=sum(pile_distribution.values()),
                      pile_capacity=pile_capacity, pile_diameter=pile_diameter, min_spacing=min_spacing)

    warnings_list: List[str] = []
    all_pile_locations = []
    total_required_piles = sum(pile_distribution.values())

    enhanced_log(log_callback, f"Generating pile layout for {len(sub_clusters)} sub-clusters", 'INFO')
    enhanced_log(log_callback, f"Total required piles: {total_required_piles}", 'INFO')
    enhanced_log(log_callback, f"Pile parameters - Capacity: {pile_capacity:.0f} kN, Diameter: {pile_diameter:.2f}m, Min spacing: {min_spacing:.2f}m", 'INFO')
    
    # Log detailed parameters
    enhanced_log(log_callback, f"Edge distance: {edge_dist:.2f}m", 'DEBUG')
    enhanced_log(log_callback, f"Site boundary provided: {site_boundary is not None}", 'DEBUG')
    enhanced_log(log_callback, f"Initial local system: {initial_local_system is not None}", 'DEBUG')
    enhanced_log(log_callback, f"Config overrides: {config_overrides is not None}", 'DEBUG')
    
    # Log pile distribution details
    for cluster_name, required_piles in pile_distribution.items():
        enhanced_log(log_callback, f"Cluster '{cluster_name}': {required_piles} required piles", 'DEBUG')

    # Use timed logger for performance tracking
    with create_timed_logger(log_callback, "generate_pile_layout"):
        try:
            log_algorithm_step(log_callback, "PileLayoutEngine", "layout_case_determination", "Analyzing problem complexity")
            
            # Determine layout case using simplified logic
            enhanced_log(log_callback, "Determining layout case strategy", 'DEBUG')
            layout_case = determine_layout_case(sub_clusters)
            warnings_list.append(f"Layout strategy: {layout_case}")

            enhanced_log(log_callback, f"Selected layout strategy: {layout_case}", 'INFO')
            log_algorithm_step(log_callback, "PileLayoutEngine", "strategy_selection", f"Selected {layout_case}")
            
            # Log strategy selection rationale
            if layout_case == "CASE_1":
                enhanced_log(log_callback, "Strategy rationale: Single column detected, using calculated layout", 'DEBUG')
            elif layout_case == "CASE_2":
                enhanced_log(log_callback, "Strategy rationale: Single wall detected, using calculated layout", 'DEBUG')
            else:
                enhanced_log(log_callback, "Strategy rationale: Complex layout detected, using genetic algorithm optimization", 'DEBUG')

            # Route to appropriate layout strategy
            if layout_case == "CASE_1":
                # Case 1: Single column only - use calculated layout
                warnings_list.append("Using Case 1 calculated layout for single column")
                enhanced_log(log_callback, "Executing Case 1 layout (single column calculated method)", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_1_execution", "Processing single column layout")

                for cluster_name, required_piles in pile_distribution.items():
                    enhanced_log(log_callback, f"Processing cluster '{cluster_name}' with {required_piles} required piles", 'DEBUG')
                    cluster_data = sub_clusters.get(cluster_name, {})

                    enhanced_log(log_callback, f"Calling handle_case_1 for cluster '{cluster_name}'", 'DEBUG')
                    positions = handle_case_1(cluster_data, required_piles, min_spacing, initial_local_system, log_callback)
                    all_pile_locations.extend(positions)

                    enhanced_log(log_callback, f"Generated {len(positions)} pile positions for cluster '{cluster_name}'", 'INFO')
                    log_validation_result(log_callback, f"case_1_cluster_{cluster_name}", len(positions) > 0,
                                        f"Generated {len(positions)} positions")

            elif layout_case == "CASE_2":
                # Case 2: Single wall only - use calculated layout
                warnings_list.append("Using Case 2 calculated layout for single wall")
                enhanced_log(log_callback, "Executing Case 2 layout (single wall calculated method)", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_2_execution", "Processing single wall layout")

                for cluster_name, required_piles in pile_distribution.items():
                    enhanced_log(log_callback, f"Processing cluster '{cluster_name}' with {required_piles} required piles", 'DEBUG')
                    cluster_data = sub_clusters.get(cluster_name, {})

                    enhanced_log(log_callback, f"Calling handle_case_2 for cluster '{cluster_name}'", 'DEBUG')
                    positions = handle_case_2(cluster_data, required_piles, min_spacing, initial_local_system, None, log_callback)
                    all_pile_locations.extend(positions)

                    enhanced_log(log_callback, f"Generated {len(positions)} pile positions for cluster '{cluster_name}'", 'INFO')
                    log_validation_result(log_callback, f"case_2_cluster_{cluster_name}", len(positions) > 0,
                                        f"Generated {len(positions)} positions")

            else:
                # Case 4: Complex layouts - use genetic algorithm optimization
                warnings_list.append("Using Case 4 genetic algorithm optimization for complex layouts")
                enhanced_log(log_callback, "Executing Case 4 layout (complex optimization with NSGA-III)", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_4_execution", "Starting genetic algorithm optimization")

                enhanced_log(log_callback, "Starting genetic algorithm optimization for complex layout", 'DEBUG')
                positions = handle_case_4(
                    sub_clusters=sub_clusters,
                    pile_distribution=pile_distribution,
                    min_spacing=min_spacing,
                    edge_dist=edge_dist,
                    initial_local_system=initial_local_system,
                    excel_inputs=excel_inputs,
                    pile_diameter=pile_diameter,
                    site_boundary=site_boundary,
                    config_overrides=config_overrides,
                    log_callback=log_callback
                )
                all_pile_locations.extend(positions)
                warnings_list.append("Case 4 genetic algorithm optimization completed successfully")
                enhanced_log(log_callback, f"Case 4 optimization generated {len(positions)} pile positions", 'INFO')
                log_validation_result(log_callback, "case_4_optimization", len(positions) > 0,
                                    f"Generated {len(positions)} positions")

            # Apply site boundary filter if provided
            log_algorithm_step(log_callback, "PileLayoutEngine", "boundary_filtering", "Applying site boundary constraints")
            if site_boundary:
                enhanced_log(log_callback, "Applying site boundary filter to pile positions", 'INFO')
                original_count = len(all_pile_locations)
                enhanced_log(log_callback, f"Filtering {original_count} pile positions against site boundary", 'DEBUG')

                filtered_locations = apply_site_boundary_filter(all_pile_locations, site_boundary)
                removed_count = original_count - len(filtered_locations)

                if removed_count > 0:
                    warnings_list.append(f"Removed {removed_count} piles outside site boundary")
                    all_pile_locations = filtered_locations
                    enhanced_log(log_callback, f"Removed {removed_count} piles outside site boundary", 'WARNING')
                    log_validation_result(log_callback, "site_boundary_filter", True,
                                        f"Removed {removed_count} out-of-bounds piles")
                    log_performance_metric(log_callback, "boundary_filter_removed", removed_count, "piles")
                else:
                    warnings_list.append("All piles within site boundary")
                    enhanced_log(log_callback, "All piles within site boundary", 'INFO')
                    log_validation_result(log_callback, "site_boundary_filter", True,
                                        "All piles within boundary")
            else:
                enhanced_log(log_callback, "No site boundary provided, skipping boundary filter", 'DEBUG')

            # Generate layout summary
            log_algorithm_step(log_callback, "PileLayoutEngine", "layout_validation", "Validating final layout")
            if all_pile_locations:
                enhanced_log(log_callback, "Generating layout summary and validation", 'INFO')
                summary = generate_layout_summary(all_pile_locations, (0.0, 0.0), min_spacing)
                warnings_list.append(f"Generated {summary['pile_count']} piles")
                warnings_list.append(f"Spacing validation: {summary['spacing_valid']}")

                enhanced_log(log_callback, f"Layout summary - {summary['pile_count']} piles, spacing valid: {summary['spacing_valid']}", 'INFO')
                log_validation_result(log_callback, "final_layout_validation", summary['spacing_valid'],
                                    f"Generated {summary['pile_count']} piles with valid spacing")
                log_calculation_result(log_callback, "final_pile_count", summary['pile_count'], "piles")

                # Log performance metrics
                log_performance_metric(log_callback, "total_piles_generated", len(all_pile_locations), "piles")
                log_performance_metric(log_callback, "pile_density", len(all_pile_locations) / max(1, len(sub_clusters)), "piles/cluster")
                log_performance_metric(log_callback, "layout_efficiency", len(all_pile_locations) / total_required_piles if total_required_piles > 0 else 1.0, "ratio")
            else:
                enhanced_log(log_callback, "No pile positions generated", 'WARNING')
                log_validation_result(log_callback, "final_layout_validation", False, "No pile positions generated")

            enhanced_log(log_callback, f"Layout generation completed successfully with {len(all_pile_locations)} pile positions", 'INFO')
            log_function_exit(log_callback, "generate_pile_layout", f"{len(all_pile_locations)} positions")

            return all_pile_locations, len(all_pile_locations), warnings_list

        except Exception as e:
            error_msg = f"Layout engine error: {e}"
            warnings_list.append(error_msg)
            enhanced_log(log_callback, f"Layout engine error: {error_msg}", 'ERROR')
            log_error_with_context(log_callback, e, "generating pile layout")
            log_function_exit(log_callback, "generate_pile_layout", "error")
            raise PileLayoutError(error_msg) from e


def generate_pile_layout_with_possible_positions(sub_clusters: Dict[str, Dict[str, Any]],
                                               pile_distribution: Dict[str, int],
                                               pile_capacity: float,
                                               pile_diameter: float = 0.6,
                                               min_spacing: float = 1.5,
                                               edge_dist: float = 0.3,
                                               site_boundary: Optional[Polygon] = None,
                                               initial_local_system: Optional[LocalCoordinateSystem] = None,
                                               excel_inputs: Optional[Any] = None,
                                               config_overrides: Optional[Dict[str, Any]] = None,
                                               log_callback: Optional[Callable] = None) -> Tuple[List[PileLocation], List[Point2D], int, List[str]]:
    """
    Generate pile layout with possible positions for visualization purposes.
    
    This function extends the main layout generation to also return possible
    pile positions for DXF visualization and analysis purposes.
    
    BEHAVIOR BY CASE:
    - Cases 1 & 2: Use calculated positions, no possible positions returned (empty list)
    - Case 4: Uses genetic algorithm optimization, returns both actual and possible positions
    
    Args:
        Same as generate_pile_layout()
        
    Returns:
        Tuple containing:
        - List of actual pile locations
        - List of possible pile positions (empty for Cases 1 & 2, populated for Case 4)
        - Total number of piles
        - List of warnings/messages
    """
    log_function_entry(log_callback, "generate_pile_layout_with_possible_positions",
                      sub_clusters_count=len(sub_clusters),
                      total_required_piles=sum(pile_distribution.values()),
                      pile_diameter=pile_diameter, min_spacing=min_spacing)

    warnings_list: List[str] = []
    all_pile_locations = []
    all_possible_positions = []
    total_required_piles = sum(pile_distribution.values())

    enhanced_log(log_callback, f"Generating pile layout with possible positions for {len(sub_clusters)} sub-clusters", 'INFO')
    enhanced_log(log_callback, f"Total required piles: {total_required_piles}", 'INFO')
    enhanced_log(log_callback, f"Pile parameters - Capacity: {pile_capacity:.0f} kN, Diameter: {pile_diameter:.2f}m, Min spacing: {min_spacing:.2f}m", 'INFO')
    
    # Log additional parameters for possible positions
    enhanced_log(log_callback, f"Possible positions generation enabled for visualization", 'DEBUG')
    enhanced_log(log_callback, f"Site boundary provided: {site_boundary is not None}", 'DEBUG')

    # Use timed logger for performance tracking
    with create_timed_logger(log_callback, "generate_pile_layout_with_possible_positions"):
        try:
            log_algorithm_step(log_callback, "PileLayoutEngine", "possible_positions_determination", "Analyzing layout case for visualization")
            
            # Determine layout case
            enhanced_log(log_callback, "Determining layout case for possible positions generation", 'DEBUG')
            layout_case = determine_layout_case(sub_clusters)
            warnings_list.append(f"Layout strategy: {layout_case}")
            enhanced_log(log_callback, f"Selected layout strategy: {layout_case}", 'INFO')
            log_algorithm_step(log_callback, "PileLayoutEngine", "strategy_selection", f"Selected {layout_case} with possible positions")
        
            # Route to appropriate strategy with possible positions support
            if layout_case == "CASE_1":
                # Case 1: Calculated positions only
                warnings_list.append("Using Case 1 calculated layout (no possible positions)")
                enhanced_log(log_callback, "Executing Case 1 layout - calculated method without possible positions", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_1_execution", "Processing calculated layout (no possible positions)")
                
                for cluster_name, required_piles in pile_distribution.items():
                    enhanced_log(log_callback, f"Processing cluster '{cluster_name}' with {required_piles} required piles", 'DEBUG')
                    cluster_data = sub_clusters.get(cluster_name, {})
                    positions = handle_case_1(cluster_data, required_piles, min_spacing, initial_local_system, log_callback)
                    all_pile_locations.extend(positions)
                    
                    enhanced_log(log_callback, f"Generated {len(positions)} positions for cluster '{cluster_name}'", 'INFO')
                    log_validation_result(log_callback, f"case_1_cluster_{cluster_name}_with_possible", len(positions) > 0,
                                        f"Generated {len(positions)} calculated positions")
                    
                # No possible positions for calculated methods
                warnings_list.append("Case 1 completed - no possible positions (calculated method)")
                enhanced_log(log_callback, "Case 1 completed: calculated layout only, no possible positions generated", 'INFO')
                    
            elif layout_case == "CASE_2":
                # Case 2: Calculated positions only
                warnings_list.append("Using Case 2 calculated layout (no possible positions)")
                enhanced_log(log_callback, "Executing Case 2 layout - calculated method without possible positions", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_2_execution", "Processing calculated layout (no possible positions)")
                
                for cluster_name, required_piles in pile_distribution.items():
                    enhanced_log(log_callback, f"Processing cluster '{cluster_name}' with {required_piles} required piles", 'DEBUG')
                    cluster_data = sub_clusters.get(cluster_name, {})
                    positions = handle_case_2(cluster_data, required_piles, min_spacing, initial_local_system, None, log_callback)
                    all_pile_locations.extend(positions)
                    
                    enhanced_log(log_callback, f"Generated {len(positions)} positions for cluster '{cluster_name}'", 'INFO')
                    log_validation_result(log_callback, f"case_2_cluster_{cluster_name}_with_possible", len(positions) > 0,
                                        f"Generated {len(positions)} calculated positions")
                    
                # No possible positions for calculated methods
                warnings_list.append("Case 2 completed - no possible positions (calculated method)")
                enhanced_log(log_callback, "Case 2 completed: calculated layout only, no possible positions generated", 'INFO')
                    
            else:
                # Case 4: Genetic algorithm with possible positions
                warnings_list.append("Using Case 4 genetic algorithm with possible positions")
                enhanced_log(log_callback, "Executing Case 4 layout with possible positions for visualization", 'INFO')
                log_algorithm_step(log_callback, "PileLayoutEngine", "case_4_execution_with_possible", "Starting optimization with possible positions")
                
                positions, possible_positions = handle_case_4_with_possible_positions(
                    sub_clusters=sub_clusters,
                    pile_distribution=pile_distribution,
                    min_spacing=min_spacing,
                    edge_dist=edge_dist,
                    initial_local_system=initial_local_system,
                    excel_inputs=excel_inputs,
                    pile_diameter=pile_diameter,
                    site_boundary=site_boundary,
                    config_overrides=config_overrides,
                    log_callback=log_callback
                )
                all_pile_locations.extend(positions)
                all_possible_positions.extend(possible_positions)
                warnings_list.append("Case 4 completed with possible positions for visualization")
                
                enhanced_log(log_callback, f"Case 4 completed: {len(positions)} actual, {len(possible_positions)} possible positions", 'INFO')
                log_calculation_result(log_callback, "actual_positions_generated", len(positions), "positions")
                log_calculation_result(log_callback, "possible_positions_generated", len(possible_positions), "positions")
                log_validation_result(log_callback, "case_4_with_possible_positions", len(positions) > 0 and len(possible_positions) > 0,
                                    f"Generated {len(positions)} actual and {len(possible_positions)} possible positions")

            # Apply site boundary filter
            log_algorithm_step(log_callback, "PileLayoutEngine", "boundary_filtering_with_possible", "Applying site boundary to actual positions")
            if site_boundary:
                enhanced_log(log_callback, "Applying site boundary filter to actual pile positions", 'INFO')
                original_count = len(all_pile_locations)
                enhanced_log(log_callback, f"Filtering {original_count} actual pile positions against site boundary", 'DEBUG')
                
                filtered_locations = apply_site_boundary_filter(all_pile_locations, site_boundary)
                removed_count = original_count - len(filtered_locations)
                
                if removed_count > 0:
                    warnings_list.append(f"Removed {removed_count} piles outside site boundary")
                    all_pile_locations = filtered_locations
                    enhanced_log(log_callback, f"Removed {removed_count} actual piles outside site boundary", 'WARNING')
                    log_performance_metric(log_callback, "boundary_filter_removed_actual", removed_count, "piles")
                else:
                    warnings_list.append("All piles within site boundary")
                    enhanced_log(log_callback, "All actual piles within site boundary", 'INFO')
                    
                log_validation_result(log_callback, "site_boundary_filter_actual", True,
                                    f"Processed {original_count} positions, removed {removed_count}")
            else:
                enhanced_log(log_callback, "No site boundary provided for actual positions", 'DEBUG')

            # Generate layout summary
            log_algorithm_step(log_callback, "PileLayoutEngine", "layout_validation_with_possible", "Validating final layout with possible positions")
            if all_pile_locations:
                # Calculate load center from all sub-clusters
                total_load = 0.0
                weighted_x = 0.0
                weighted_y = 0.0
                
                enhanced_log(log_callback, "Calculating overall load center for layout summary", 'DEBUG')
                for cluster_data in sub_clusters.values():
                    for x, y, load in cluster_data.get('load_points', []):
                        total_load += load
                        weighted_x += x * load
                        weighted_y += y * load
                
                if total_load > 0:
                    load_center = (weighted_x / total_load, weighted_y / total_load)
                    enhanced_log(log_callback, f"Load center calculated: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
                else:
                    load_center = (0.0, 0.0)
                    enhanced_log(log_callback, "No loads found, using origin as load center", 'DEBUG')
                
                layout_summary = generate_layout_summary(all_pile_locations, load_center, min_spacing)
                warnings_list.append(f"Generated {layout_summary['pile_count']} piles")
                warnings_list.append(f"Spacing validation: {layout_summary['spacing_valid']}")
                
                enhanced_log(log_callback, f"Layout summary - {layout_summary['pile_count']} actual piles, spacing valid: {layout_summary['spacing_valid']}", 'INFO')
                log_calculation_result(log_callback, "final_actual_pile_count", layout_summary['pile_count'], "piles")
                log_validation_result(log_callback, "final_layout_validation_with_possible", layout_summary['spacing_valid'],
                                    f"Generated {layout_summary['pile_count']} actual piles with valid spacing")
                
                if all_possible_positions:
                    warnings_list.append(f"Generated {len(all_possible_positions)} possible positions for visualization")
                    enhanced_log(log_callback, f"Visualization: {len(all_possible_positions)} possible positions available", 'INFO')
                    log_calculation_result(log_callback, "visualization_possible_positions", len(all_possible_positions), "positions")
                else:
                    warnings_list.append("No possible positions (calculated layout method)")
                    enhanced_log(log_callback, "No possible positions available (calculated layout method)", 'DEBUG')
                
                # Log performance metrics
                log_performance_metric(log_callback, "total_actual_piles_generated", len(all_pile_locations), "piles")
                log_performance_metric(log_callback, "total_possible_positions_generated", len(all_possible_positions), "positions")
                log_performance_metric(log_callback, "actual_pile_density", len(all_pile_locations) / max(1, len(sub_clusters)), "piles/cluster")
                
                if len(all_possible_positions) > 0:
                    log_performance_metric(log_callback, "possible_position_ratio", len(all_possible_positions) / max(1, len(all_pile_locations)), "ratio")
                    
            else:
                enhanced_log(log_callback, "No actual pile positions generated", 'WARNING')
                log_validation_result(log_callback, "final_layout_validation_with_possible", False, "No actual pile positions generated")

            total_piles = len(all_pile_locations)

            enhanced_log(log_callback, f"Layout with possible positions completed: {len(all_pile_locations)} actual, {len(all_possible_positions)} possible", 'INFO')
            log_calculation_result(log_callback, "final_results", f"{len(all_pile_locations)} actual, {len(all_possible_positions)} possible", "positions")
            log_function_exit(log_callback, "generate_pile_layout_with_possible_positions",
                             f"{len(all_pile_locations)} actual, {len(all_possible_positions)} possible")

            return all_pile_locations, all_possible_positions, total_piles, warnings_list

        except Exception as e:
            error_msg = f"Layout engine with possible positions failed: {str(e)}"
            warnings_list.append(error_msg)
            enhanced_log(log_callback, f"Layout engine error: {error_msg}", 'ERROR')
            log_error_with_context(log_callback, e, "generating pile layout with possible positions")
            log_function_exit(log_callback, "generate_pile_layout_with_possible_positions", "error")
            raise PileLayoutError(error_msg) from e


# NOTE: ZERO FALLBACK POLICY ENFORCED - All legacy fallbacks permanently removed.
# Use generate_pile_layout directly for all pile layout generation needs.
# System MUST fail completely if any errors occur - NO FALLBACKS ALLOWED.

