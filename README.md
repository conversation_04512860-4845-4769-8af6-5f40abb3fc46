# Foundation-Automation

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Requirements](#requirements)
- [Installation](#installation)
- [Getting Started](#getting-started)
- [Package Structure](#package-structure)
- [Documentation](#documentation)
- [Contributing](#contributing)
- [Support](#support)
- [License](#license)

## Overview

Foundation-Automation is a comprehensive Python-based system designed to automate and streamline various aspects of foundation engineering design and analysis. It integrates with structural analysis software (such as SAFE and ETABS) to provide a complete workflow for foundation engineering projects, built on a modular and scalable architecture.

## Features

### Foundation Design & Analysis
- **Comprehensive Pile Design**: Includes Ultimate Limit State (ULS) and Serviceability Limit State (SLS) design for bored piles, socketed steel H piles, driven steel H piles, and mini piles.
- **Pile Capacity Calculations**: Automated assessment of pile load-bearing capacities.
- **Load Analysis**: Capabilities for load combination analysis and transformation of load schedules.
- **Soil Interaction**: Functions for soil resistance and soil spring calculations.
- **Performance Checks**: Deflection analysis and differential settlement checks.
- **RC Optimization**: Reinforced concrete optimization for bored piles.
- **Pile Estimation**: Automated pile estimation capabilities for grouping structural elements, calculating loads, generating pile layouts, and designing pile caps.
- **Intelligent Pile Cap Generation**: Advanced pile cap sizing that adapts to pile type and diameter for optimal layout boundaries.
- **Multi-Objective Optimization**: Leverages advanced algorithms (e.g., NSGA-III) for complex pile layout optimization.

### Modeling & Data Processing
- **Finite Element Model Building**: Automated generation of SAFE models.
- **Geometric Data Extraction**: Tools for extracting geometric data from various inputs.
- **Design Strip Generation**: Automated creation of design strips for analysis.

### Integration & Export
- **Software Integration**: Seamless integration with structural analysis software (SAFE and ETABS).
- **File Generation**: Outputs include SAFE files, Prokon files, Excel reports, and DXF drawings for CAD visualization.
- **Revit Export**: Capabilities for exporting data to Revit.

### User Interface & Management
- **User-Friendly Interface**: Provides a Graphical User Interface (GUI) for interactive workflows.
- **Configuration Management**: Centralized management of system settings and file paths.
- **Comprehensive Documentation**: Detailed README files for each package.

## Requirements

### Software Prerequisites
- Python 3.8+
- Structural analysis software (SAFE and ETABS)

### Python Package Dependencies
Required Python packages can be found in `requirements.txt`. Key packages include:
- `matplotlib` for visualization
- `numpy`, `pandas`, `scipy` for scientific computing and data manipulation
- `openpyxl` for Excel file handling
- `pyodbc` for database connectivity
- `pythonnet` for .NET integration (if applicable)
- `requests` for API calls
- `ezdxf` for DXF file handling
- `scikit-learn` for machine learning capabilities (e.g., clustering)
- `deap` for multi-objective optimization

## Installation

1.  **Clone the repository**:
    ```bash
    git clone https://github.com/your-username/Foundation-Automation.git
    ```
2.  **Navigate to the project directory**:
    ```bash
    cd Foundation-Automation
    ```
3.  **Install the required Python packages**:
    ```bash
    pip install -r requirements.txt
    ```

## Getting Started

### GUI Application

To launch the main Foundation Agent GUI application:
```bash
python fdn_agent/agent_gui.py
```

The GUI provides an intuitive interface for:
- Project initialization and data import
- Pile type selection and parameter configuration
- AI-powered optimization execution
- Results visualization and export

### Programmatic Usage

#### Basic Pile Estimation Workflow

```python
from fdn_agent.agent_main import (
    automated_initialize_setup,
    run_pile_estimation_with_multi_type_optimization
)

# 1. Initialize project with Excel data
file_paths, inputs, outputs = automated_initialize_setup(
    input_folder="./project_data",
    log_callback=print
)

# 2. Define pile types for optimization
pile_types = [
    {
        'type': 'DHP',           # Driven H-Pile
        'capacity': 3663.0,      # kN
        'section': 'UBP_305x305x223',
        'min_spacing': 1.2       # meters
    },
    {
        'type': 'SHP',           # Socket H-Pile
        'capacity': 6106.0,      # kN
        'section': 'UBP_305x305x223',
        'min_spacing': 1.85      # meters
    },
    {
        'type': 'BP',            # Bored Pile
        'capacity': 5000.0,      # kN
        'diameter': 0.6,         # meters
        'min_spacing': 1.8       # meters
    }
]

# 3. Run AI-powered optimization
results = run_pile_estimation_with_multi_type_optimization(
    excel_inputs=inputs,
    selected_pile_types=pile_types,
    edge_dist=0.4,
    optimization_method="cost_efficiency",
    output_dir="./results",
    log_callback=print
)

# 4. Process results
if results['success']:
    print(f"✅ Optimization completed!")
    print(f"📊 Groups processed: {results['summary']['total_groups']}")
    print(f"🏗️ Total piles: {results['summary']['total_piles']}")
    print(f"📄 DXF file: {results['dxf_file']}")
else:
    print(f"❌ Errors: {results['errors']}")
```

#### Advanced Configuration

```python
# Advanced optimization with custom parameters
results = run_pile_estimation_with_multi_type_optimization(
    excel_inputs=inputs,
    selected_pile_types=pile_types,
    edge_dist=0.5,
    optimization_method="balanced",  # or "performance"
    output_dir="./advanced_results",
    log_callback=logger.info,
    # Advanced parameters
    grouping_min_threshold=0.5,
    grouping_max_threshold=2.0,
    use_multiprocessing=True,
    safety_factor=1.25
)
```

### Pile Cap Generation and Boundary Calculation

The system features intelligent pile cap generation that adapts to different pile types and their physical characteristics:

#### Key Improvements (v5.6.9+)

**Pile Type-Specific Maximum Pile Caps**:
- **H-piles (DHP/SHP)**: Use 0.6m equivalent diameter for cap sizing
- **Bored Piles (BP)**: Use actual pile diameter for precise cap sizing
- **Formula**: `Maximum Pile Cap = Initial Pile Cap + (1 × pile_diameter)`

**Accurate Boundary Calculations**:
- **Formula**: `Possible Pile Boundary = Maximum Pile Cap - pile_radius - edge_distance`
- Ensures proper clearance for pile installation and structural requirements
- Intelligent handling of edge cases and small pile caps

#### Example Usage

```python
from fdn_agent.pile_estimation.pile_type_selection.pile_type_preselection import PileTypePreselector
from fdn_agent.pile_estimation.data_types import PileTypeCandidate, PileType

# Create pile type candidates
pile_types = [
    PileTypeCandidate(
        pile_type=PileType.DHP,
        capacity_per_pile=3663.0,
        min_spacing=1.2,
        section='UBP_305x305x223',
        diameter=None  # H-pile uses equivalent diameter
    ),
    PileTypeCandidate(
        pile_type=PileType.BP,
        capacity_per_pile=41155.0,
        min_spacing=6.0,
        section=None,
        diameter=2.0  # Bored pile with 2m diameter
    )
]

# Generate pile type-specific maximum caps
preselector = PileTypePreselector()
maximum_pile_caps = preselector._generate_maximum_pile_caps_by_type(
    initial_pile_cap, pile_types
)

# Calculate boundaries with correct formula
for pile_type in pile_types:
    max_cap = maximum_pile_caps[pile_type.pile_type.name]
    boundary = preselector._calculate_possible_pile_boundary(
        max_cap, pile_type, user_edge_distance=0.4
    )
```

### Project Initialization

For new projects, you can also initialize programmatically:
```python
from initialization import initialize_project
settings = initialize_project()
```

## Package Structure

The system is organized into several key packages:

- [`auth/`](./auth/README.md) - Authentication and security.
- [`build_fem/`](./build_fem/README.md) - Finite element model building.
- [`config/`](./config/README.md) - Configuration management.
- [`design_fdn/`](./design_fdn/README.md) - Foundation design.
- [`etabs_cwls/`](./etabs_cwls/README.md) - ETABS wall loading schedule.
- [`fdn_agent/`](./fdn_agent/README.md) - Core foundation agent system.
- [`initialization/`](./initialization/README.md) - System initialization routines.
- [`read/`](./read/README.md) - Data reading operations.
- [`safe_api/`](./safe_api/README.md) - SAFE software integration.
- [`ui/`](./ui/README.md) - User interface components.

## Documentation

For detailed documentation on each package and its modules, please refer to their respective `README.md` files located within each directory.

## Contributing

1.  Fork the repository.
2.  Create a feature branch.
3.  Commit your changes.
4.  Push to the branch.
5.  Create a Pull Request.

## Support

For support, please contact the development <NAME_EMAIL>

## License

This project is licensed under the MIT License - see the `LICENSE.md` file for details.
