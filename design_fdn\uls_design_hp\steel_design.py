"""
Steel Design Strength Calculations for H-Pile Foundation Systems

This module provides fundamental steel design strength calculations for H-pile (HP) 
foundation systems as part of Ultimate Limit State (ULS) and Allowable Stress Design 
(ASD) verification procedures. It implements steel material property determination 
based on section geometry and steel grade specifications according to structural 
steel design codes and standards.

The module performs steel design strength calculations for:
- H-pile steel sections: Socket H-piles (SHP) and Driven H-piles (DHP)
- Steel grade classification: Various steel grades with thickness-dependent properties
- Yield strength determination: Thickness-based yield strength calculation
- Material property extraction: From standardized steel property databases

Key Features:
- Thickness-dependent yield strength calculation following steel design codes
- Steel grade property lookup with interpolation capabilities
- Support for various H-pile section geometries and steel specifications
- Integration with foundation design workflow for capacity calculations
- Compliance with structural steel design standards (AISC, Eurocode, etc.)

Structural Steel Design Context:
Steel design strength calculations are fundamental to H-pile foundation design,
ensuring adequate structural capacity under applied loads. This module implements
industry-standard methods for determining steel material properties considering:
- Steel grade specifications and chemical composition effects
- Section thickness effects on mechanical properties
- Heat treatment and manufacturing process influences
- Code-based material property reduction factors
- Temperature and environmental condition adjustments

Foundation Engineering Integration:
H-pile foundations rely on structural steel sections to transfer loads from
superstructure to bearing soil layers. The steel design strength calculations
provided by this module are essential for:
- Ultimate limit state (ULS) capacity verification
- Allowable stress design (ASD) safety factor application
- Moment-axial-shear (N-M-V) interaction analysis
- Section optimization and pile sizing procedures
- Construction feasibility and cost optimization

Usage Example:
    ```python
    # Calculate design strength for H-pile steel section
    yield_strength = design_strength(
        excel_inputs=project_inputs,
        steel_section='HP310x110',
        steel_grade='S355'
    )
    
    # Use in capacity calculations
    section_capacity = yield_strength * section_area
    ```

Dependencies:
- pandas: Data manipulation and Excel I/O operations
- Standard steel property databases and material specifications

Author: Foundation Design Automation Team
Version: 1.0
"""

import pandas as pd
from typing import Any, Union


def design_strength(
    excel_inputs: Any, 
    steel_section: str, 
    steel_grade: str
) -> float:
    """
    Calculate design yield strength for H-pile steel section based on thickness and grade.
    
    This function determines the characteristic yield strength (py1) for structural steel
    H-pile sections by considering both the steel grade specification and the controlling
    thickness of the section (maximum of web and flange thickness). The calculation
    follows structural steel design code provisions where yield strength varies with
    section thickness due to manufacturing and heat treatment effects.
    
    The function implements thickness-dependent yield strength reduction as specified
    in steel design codes, where thicker sections typically have lower yield strengths
    due to cooling rate effects during manufacturing and reduced effectiveness of
    heat treatment processes.
    
    Args:
        excel_inputs (Any): Design input data object containing:
            - SteelSectionH: DataFrame with H-pile section properties including:
                * 'Steel Section': Section designation (e.g., 'HP310x110')
                * 't (mm)': Web thickness in millimeters
                * 'T (mm)': Flange thickness in millimeters
            - SteelGrade: DataFrame with steel grade specifications including:
                * 'Steel Grade': Grade designation (e.g., 'S355', 'S275')
                * 'Thickness <= (mm)': Thickness limit columns
                * Corresponding yield strength values in N/mm²
        steel_section (str): Steel section designation matching SteelSectionH database
            (e.g., 'HP310x110', 'HP200x63', 'HP250x85')
        steel_grade (str): Steel grade designation matching SteelGrade database
            (e.g., 'S355', 'S275', 'S420', 'Grade 50')
    
    Returns:
        float: Characteristic yield strength (py1) in N/mm² for the specified
            steel section and grade combination, accounting for thickness effects
    
    Raises:
        IndexError: If steel section or grade is not found in input databases
        KeyError: If required columns are missing from input dataframes
        ValueError: If thickness data is invalid or inconsistent
    
    Notes:
        - Controlling thickness is the maximum of web and flange thickness
        - Yield strength decreases with increasing section thickness per steel codes
        - Function implements iterative lookup through thickness ranges
        - Returns the last valid yield strength if section exceeds maximum thickness
        - Steel grade databases typically follow EN 10025, ASTM A572, or similar standards
    
    Structural Steel Design Context:
        The thickness-dependent yield strength calculation reflects physical metallurgy
        principles where:
        - Thinner sections cool faster during rolling, achieving higher strengths
        - Thicker sections have reduced hardenability and lower yield strengths
        - Heat treatment effectiveness decreases with increasing section size
        - Code provisions ensure conservative design values for structural safety
    
    Foundation Engineering Application:
        In H-pile foundation design, accurate yield strength determination is critical for:
        - Ultimate limit state capacity calculations (N-M-V interaction)
        - Allowable stress design safety factor application
        - Section optimization and pile sizing procedures
        - Construction cost estimation and material specification
        - Structural integrity verification under foundation loads
    
    Example:
        ```python
        # Calculate yield strength for HP310x110 section in S355 steel
        py1 = design_strength(
            excel_inputs=project_data,
            steel_section='HP310x110',
            steel_grade='S355'
        )
        print(f"Yield strength: {py1} N/mm²")
        
        # Use in capacity calculations
        section_area = 140.0  # cm² for HP310x110
        axial_capacity = py1 * section_area * 100  # Convert to N
        ```
    """
    # Initialize yield strength variable
    py1 = 0
    
    # Extract section geometry data from steel section database
    df_section_h = excel_inputs.SteelSectionH
    df_steel_grade = excel_inputs.SteelGrade
    
    # Find section properties matching the specified steel section designation
    condition = df_section_h['Steel Section'] == steel_section
    thickness_web = df_section_h[condition]['t (mm)'].values[0]      # Web thickness
    thickness_flange = df_section_h[condition]['T (mm)'].values[0]  # Flange thickness

    # Determine controlling thickness for yield strength calculation
    # Steel design codes specify that the maximum thickness controls material properties
    # This reflects the manufacturing process where thicker elements cool slower
    # and achieve lower strength due to reduced cooling rate effects
    thickness = max(thickness_web, thickness_flange)

    # Extract steel grade properties and thickness-strength relationships
    condition = df_steel_grade['Steel Grade'] == steel_grade
    start = df_steel_grade[condition].columns.get_loc('Thickness <= (mm)')
    end = df_steel_grade[condition].size - 1

    # Iterate through thickness ranges to find applicable yield strength
    # Steel grade databases typically have alternating columns:
    # - Even indices: Thickness limits (mm)
    # - Odd indices: Corresponding yield strengths (N/mm²)
    for i in range(start, end, 2):
        # Check if controlling thickness falls within current thickness range
        thickness_limit = df_steel_grade[condition].iloc[:, i].values[0]
        
        if thickness <= thickness_limit:
            # Section thickness is within current range - use corresponding yield strength
            py1 = df_steel_grade[condition].iloc[:, i + 1].values[0]
            break
        else:
            # Section exceeds current thickness limit - continue to next range
            # Store current yield strength as fallback for sections exceeding maximum thickness
            if not pd.isnull(thickness_limit):
                py1 = df_steel_grade[condition].iloc[:, i + 1].values[0]
    
    # Return characteristic yield strength in N/mm²
    # This value is used directly in ULS calculations or with appropriate safety factors for ASD
    return py1
