"""
Data Processing Module for 3D Pile Volume Analysis

This module handles reading Excel data, validating inputs, and preparing
pile data for geometric analysis. It integrates with the existing read
module to leverage established data loading infrastructure.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path

# Import existing read functionality
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from read.read_geometry import read_input_geometry

logger = logging.getLogger(__name__)


class DataProcessor:
    """
    Handles data processing for pile volume analysis including Excel reading,
    validation, and data preparation.
    """
    
    def __init__(self):
        """Initialize the DataProcessor."""
        self.required_columns = {
            'pile_id': str,
            'x_coord': float,
            'y_coord': float, 
            'top_elevation': float,
            'bottom_elevation': float,
            'diameter': float,
            'material_type': str  # 'soil' or 'rock'
        }
        
    def read_pile_data(self, excel_file: Union[str, Path],
                      file_paths: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Read pile data from Excel file using existing read infrastructure.

        Args:
            excel_file: Path to Excel file containing pile data
            file_paths: Optional dictionary of file paths for read_input_geometry

        Returns:
            DataFrame with standardized pile data columns

        Raises:
            FileNotFoundError: If Excel file doesn't exist
            ValueError: If required data is missing or invalid
        """
        try:
            # Import the main_class to create proper objects
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            import main_class

            # Create proper objects for the existing read infrastructure
            excel_inputs = main_class.ExcelInputs()
            file_paths_obj = main_class.FilePaths()
            file_paths_obj.ExcelGeometry = str(excel_file)

            excel_inputs = read_input_geometry(excel_inputs, file_paths_obj)

            # Extract pile data from the excel_inputs object
            if hasattr(excel_inputs, 'Pile') and not excel_inputs.Pile.empty:
                pile_data = excel_inputs.Pile
            else:
                raise ValueError("No pile data found in Excel file")

            # Standardize column names and validate
            standardized_data = self._standardize_pile_data(pile_data)

            # Validate data integrity
            self._validate_pile_data(standardized_data)

            logger.info(f"Successfully loaded {len(standardized_data)} pile records")
            return standardized_data
            
        except Exception as e:
            logger.error(f"Error reading pile data from {excel_file}: {str(e)}")
            raise
            
    def _standardize_pile_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize pile data column names and types.
        
        Args:
            raw_data: Raw DataFrame from Excel reading
            
        Returns:
            Standardized DataFrame with consistent column names
        """
        # Create mapping for actual Excel column names and common variations
        column_mapping = {
            'pile mark': 'pile_id',
            'pile_name': 'pile_id',
            'pile_no': 'pile_id',
            'id': 'pile_id',
            'x (m)': 'x_coord',
            'x': 'x_coord',
            'easting': 'x_coord',
            'y (m)': 'y_coord',
            'y': 'y_coord',
            'northing': 'y_coord',
            'pile cap bottom level (mpd)': 'top_elevation',
            'top_el': 'top_elevation',
            'top_elev': 'top_elevation',
            'founding level (mpd)': 'bottom_elevation',
            'bot_el': 'bottom_elevation',
            'bot_elev': 'bottom_elevation',
            'bottom_el': 'bottom_elevation',
            'pile shaft diameter (m)': 'diameter',
            'dia': 'diameter',
            'diam': 'diameter',
            'material': 'material_type'
        }
        
        # Apply column mapping
        standardized = raw_data.copy()
        standardized.columns = standardized.columns.str.lower().str.strip()
        
        for old_name, new_name in column_mapping.items():
            if old_name in standardized.columns:
                standardized = standardized.rename(columns={old_name: new_name})
                
        # Handle missing columns
        for col_name in self.required_columns.keys():
            if col_name not in standardized.columns:
                if col_name == 'material_type':
                    # Determine material type based on pile type or pile_id
                    material_assigned = False

                    # Check if we have a Pile Type column in the original data
                    if 'Pile Type' in raw_data.columns:
                        pile_type_mapping = {
                            'DHP': 'rock',  # Driven H-Pile typically for rock
                            'BP': 'soil',   # Bored Pile typically for soil
                            'SHP': 'soil',  # Steel H-Pile
                            'MP': 'soil'    # Micropile
                        }
                        standardized[col_name] = raw_data['Pile Type'].map(
                            pile_type_mapping
                        ).fillna('soil')
                        material_assigned = True

                    # If not assigned yet, try to infer from pile_id prefix
                    if not material_assigned and 'pile_id' in standardized.columns:
                        def infer_material_type(pile_id):
                            if isinstance(pile_id, str):
                                if pile_id.startswith('DHP'):
                                    return 'rock'
                                elif pile_id.startswith('BP'):
                                    return 'soil'
                                elif pile_id.startswith('SHP'):
                                    return 'soil'
                                elif pile_id.startswith('MP'):
                                    return 'soil'
                            return 'soil'

                        standardized[col_name] = standardized['pile_id'].apply(infer_material_type)
                        material_assigned = True

                    # Default to 'soil' if still not assigned
                    if not material_assigned:
                        standardized[col_name] = 'soil'
                else:
                    raise ValueError(f"Required column '{col_name}' not found in data")
            
        # Convert data types
        for col, dtype in self.required_columns.items():
            if col in standardized.columns:
                try:
                    if dtype == str:
                        standardized[col] = standardized[col].astype(str).str.strip()
                    else:
                        standardized[col] = pd.to_numeric(standardized[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Error converting column {col} to {dtype}: {e}")

        # Filter to only required columns and remove rows with missing critical data
        result = standardized[list(self.required_columns.keys())].copy()
        result = result.dropna(subset=['pile_id', 'x_coord', 'y_coord', 'top_elevation', 'bottom_elevation', 'diameter'])

        return result
        
    def _validate_pile_data(self, data: pd.DataFrame) -> None:
        """
        Validate pile data for completeness and logical consistency.
        
        Args:
            data: Standardized pile DataFrame
            
        Raises:
            ValueError: If validation fails
        """
        # Check for missing values
        missing_data = data.isnull().sum()
        if missing_data.any():
            logger.warning(f"Missing data found: {missing_data[missing_data > 0].to_dict()}")
            
        # Check for duplicate pile IDs
        duplicates = data['pile_id'].duplicated().sum()
        if duplicates > 0:
            raise ValueError(f"Found {duplicates} duplicate pile IDs")
            
        # Validate elevation logic (top > bottom)
        invalid_elevations = data['top_elevation'] <= data['bottom_elevation']
        if invalid_elevations.any():
            invalid_piles = data.loc[invalid_elevations, 'pile_id'].tolist()
            raise ValueError(f"Invalid elevations (top <= bottom) for piles: {invalid_piles}")
            
        # Validate positive diameters
        invalid_diameters = data['diameter'] <= 0
        if invalid_diameters.any():
            invalid_piles = data.loc[invalid_diameters, 'pile_id'].tolist()
            raise ValueError(f"Invalid diameters (<=0) for piles: {invalid_piles}")
            
        # Validate material types
        valid_materials = {'soil', 'rock'}
        invalid_materials = ~data['material_type'].str.lower().isin(valid_materials)
        if invalid_materials.any():
            invalid_piles = data.loc[invalid_materials, 'pile_id'].tolist()
            raise ValueError(f"Invalid material types for piles: {invalid_piles}. Must be 'soil' or 'rock'")
            
        logger.info("Pile data validation completed successfully")
        
    def calculate_pile_properties(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate additional pile properties needed for volume analysis.
        
        Args:
            data: Validated pile DataFrame
            
        Returns:
            DataFrame with additional calculated properties
        """
        result = data.copy()
        
        # Calculate pile length
        result['pile_length'] = result['top_elevation'] - result['bottom_elevation']
        
        # Calculate pile radius
        result['radius'] = result['diameter'] / 2.0
        
        # Calculate pile volume (cylinder)
        result['pile_volume'] = np.pi * result['radius']**2 * result['pile_length']
        
        # Determine projection angle based on material type
        result['projection_angle'] = result['material_type'].str.lower().map({
            'soil': 15.0,  # degrees
            'rock': 30.0   # degrees
        })
        
        # Calculate frustum base radius at bottom elevation
        result['frustum_base_radius'] = (
            result['radius'] + 
            result['pile_length'] * np.tan(np.radians(result['projection_angle']))
        )
        
        logger.info("Pile properties calculated successfully")
        return result
        
    def filter_piles_by_boundary(self, data: pd.DataFrame, 
                                boundary_coords: List[Tuple[float, float]]) -> pd.DataFrame:
        """
        Filter piles to only include those within the site boundary.
        
        Args:
            data: Pile DataFrame with coordinates
            boundary_coords: List of (x, y) coordinates defining site boundary
            
        Returns:
            Filtered DataFrame containing only piles within boundary
        """
        from shapely.geometry import Point, Polygon
        
        # Create boundary polygon
        boundary_polygon = Polygon(boundary_coords)
        
        # Check which piles are within boundary
        pile_points = [Point(x, y) for x, y in zip(data['x_coord'], data['y_coord'])]
        within_boundary = [boundary_polygon.contains(point) for point in pile_points]
        
        filtered_data = data[within_boundary].copy()
        
        excluded_count = len(data) - len(filtered_data)
        if excluded_count > 0:
            logger.info(f"Excluded {excluded_count} piles outside site boundary")
            
        return filtered_data
