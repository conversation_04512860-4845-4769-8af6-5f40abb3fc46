"""
Main Analyzer Module for 3D Pile Volume Analysis

This module orchestrates the entire pile volume analysis workflow,
providing the main API for users to perform complete analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
from datetime import datetime

from .data_processor import DataProcessor
from .geometry_engine import GeometryEngine, PileGeometry
from .boundary_clipper import BoundaryClipper
from .volume_calculator import VolumeCalculator, VolumeResult
from .overlap_analyzer import OverlapAnalyzer, OverlapResult
from .report_generator import ReportGenerator
from .visualizer import Visualizer

logger = logging.getLogger(__name__)


class AnalysisResults:
    """Container for complete analysis results."""
    
    def __init__(self):
        self.pile_data: Optional[pd.DataFrame] = None
        self.geometries: Dict[str, PileGeometry] = {}
        self.clipped_geometries: Dict[str, PileGeometry] = {}
        self.volume_results: Dict[str, VolumeResult] = {}
        self.overlap_results: List[OverlapResult] = []
        self.overlap_volumes: Dict[Tuple[str, str], float] = {}
        self.individual_report: Optional[pd.DataFrame] = None
        self.overlap_report: Optional[pd.DataFrame] = None
        self.summary_report: Dict = {}
        self.boundary_info: Dict = {}


class MainAnalyzer:
    """
    Main orchestrator for 3D pile volume analysis workflow.
    """
    
    def __init__(self, 
                 mesh_resolution: int = 32,
                 spatial_tolerance: float = 0.1,
                 enable_visualization: bool = True):
        """
        Initialize the MainAnalyzer.
        
        Args:
            mesh_resolution: Resolution for 3D mesh generation
            spatial_tolerance: Tolerance for spatial operations
            enable_visualization: Whether to enable visualization capabilities
        """
        self.mesh_resolution = mesh_resolution
        self.spatial_tolerance = spatial_tolerance
        self.enable_visualization = enable_visualization
        
        # Initialize components
        self.data_processor = DataProcessor()
        self.geometry_engine = GeometryEngine(mesh_resolution=mesh_resolution)
        self.volume_calculator = VolumeCalculator()
        self.overlap_analyzer = OverlapAnalyzer(spatial_tolerance=spatial_tolerance)
        self.report_generator = ReportGenerator()
        
        if enable_visualization:
            try:
                self.visualizer = Visualizer()
            except ImportError:
                logger.warning("PyVista not available, visualization disabled")
                self.visualizer = None
        else:
            self.visualizer = None
            
        logger.info("MainAnalyzer initialized successfully")
        
    def analyze_pile_volumes(self, 
                           excel_file: Union[str, Path],
                           site_boundary_coords: List[Tuple[float, float]],
                           file_paths: Optional[Dict[str, str]] = None,
                           output_dir: Union[str, Path] = "output",
                           export_reports: bool = True,
                           create_visualizations: bool = True) -> AnalysisResults:
        """
        Perform complete pile volume analysis.
        
        Args:
            excel_file: Path to Excel file with pile data
            site_boundary_coords: List of (x, y) coordinates defining site boundary
            file_paths: Optional file paths for data reading
            output_dir: Directory for output files
            export_reports: Whether to export CSV reports
            create_visualizations: Whether to create visualizations
            
        Returns:
            AnalysisResults object with complete analysis
        """
        logger.info("Starting pile volume analysis")
        results = AnalysisResults()
        
        try:
            # Step 1: Read and process pile data
            logger.info("Step 1: Reading pile data")
            pile_data = self.data_processor.read_pile_data(excel_file, file_paths)
            pile_data = self.data_processor.filter_piles_by_boundary(pile_data, site_boundary_coords)
            pile_data = self.data_processor.calculate_pile_properties(pile_data)
            results.pile_data = pile_data
            
            logger.info(f"Processed {len(pile_data)} piles")
            
            # Step 2: Create 3D geometries
            logger.info("Step 2: Creating 3D geometries")
            geometries = self.geometry_engine.create_all_pile_geometries(pile_data)
            results.geometries = geometries
            
            # Step 3: Set up boundary clipping
            logger.info("Step 3: Setting up boundary clipping")
            elevation_range = self._determine_elevation_range(pile_data)
            boundary_clipper = BoundaryClipper(site_boundary_coords, elevation_range)
            results.boundary_info = boundary_clipper.get_boundary_info()
            
            # Step 4: Clip geometries to boundary
            logger.info("Step 4: Clipping geometries to site boundary")
            clipped_geometries = boundary_clipper.clip_all_geometries(geometries)
            results.clipped_geometries = clipped_geometries
            
            # Step 5: Calculate volumes
            logger.info("Step 5: Calculating volumes")
            volume_results = self.volume_calculator.calculate_all_volumes(clipped_geometries, is_clipped=True)
            
            # Step 6: Analyze overlaps
            logger.info("Step 6: Analyzing overlaps")
            overlap_volumes = self.overlap_analyzer.analyze_all_overlaps(clipped_geometries)
            results.overlap_volumes = overlap_volumes
            
            # Step 7: Distribute overlap volumes
            logger.info("Step 7: Distributing overlap volumes")
            final_volume_results = self.volume_calculator.distribute_overlap_volumes(
                volume_results, overlap_volumes
            )
            results.volume_results = final_volume_results
            
            # Step 8: Generate reports
            logger.info("Step 8: Generating reports")
            theoretical_volumes = self.volume_calculator.calculate_theoretical_volumes(pile_data)
            
            individual_report = self.report_generator.generate_individual_pile_report(
                pile_data, final_volume_results, theoretical_volumes
            )
            results.individual_report = individual_report
            
            # Create overlap results for detailed reporting
            overlap_results = []
            for (pile1_id, pile2_id), volume in overlap_volumes.items():
                overlap_results.append(OverlapResult(
                    pile1_id=pile1_id,
                    pile2_id=pile2_id,
                    overlap_volume=volume,
                    overlap_type='combined'
                ))
            results.overlap_results = overlap_results
            
            overlap_report = self.report_generator.generate_overlap_analysis_report(
                overlap_results, final_volume_results
            )
            results.overlap_report = overlap_report
            
            summary_report = self.report_generator.generate_summary_report(
                pile_data, final_volume_results, overlap_volumes, results.boundary_info
            )
            results.summary_report = summary_report
            
            # Step 9: Export reports
            if export_reports:
                logger.info("Step 9: Exporting reports")
                file_paths = self.report_generator.export_reports_to_csv(
                    individual_report, overlap_report, summary_report, output_dir
                )
                logger.info(f"Reports exported to: {file_paths}")
                
            # Step 10: Create visualizations
            if create_visualizations and self.visualizer:
                logger.info("Step 10: Creating visualizations")
                self._create_visualizations(results, output_dir)
                
            logger.info("Pile volume analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error during pile volume analysis: {e}")
            raise
            
    def _determine_elevation_range(self, pile_data: pd.DataFrame) -> Tuple[float, float]:
        """Determine elevation range from pile data."""
        min_elev = pile_data['bottom_elevation'].min() - 10  # Add buffer
        max_elev = pile_data['top_elevation'].max() + 10
        return min_elev, max_elev
        
    def _create_visualizations(self, results: AnalysisResults, output_dir: Union[str, Path]) -> None:
        """Create and save visualizations."""
        if not self.visualizer:
            return
            
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        try:
            # Pile geometries visualization
            plotter = self.visualizer.visualize_pile_geometries(
                results.clipped_geometries, results.volume_results, show_parts=False
            )
            plotter.show(screenshot=str(output_path / "pile_geometries.png"))
            
            # Overlap visualization
            if results.overlap_results:
                plotter = self.visualizer.visualize_overlaps(
                    results.clipped_geometries, results.overlap_results
                )
                plotter.show(screenshot=str(output_path / "pile_overlaps.png"))
                
            # Site boundary visualization
            if results.pile_data is not None:
                elevation_range = self._determine_elevation_range(results.pile_data)
                boundary_coords = [(coord[0], coord[1]) for coord in results.boundary_info.get('coordinates', [])]
                
                if boundary_coords:
                    plotter = self.visualizer.visualize_site_boundary(
                        boundary_coords, elevation_range, results.clipped_geometries
                    )
                    plotter.show(screenshot=str(output_path / "site_boundary.png"))
                    
            logger.info("Visualizations created successfully")
            
        except Exception as e:
            logger.warning(f"Error creating visualizations: {e}")
            
    def validate_analysis(self, results: AnalysisResults, tolerance: float = 0.05) -> Dict[str, bool]:
        """
        Validate analysis results.
        
        Args:
            results: Analysis results to validate
            tolerance: Tolerance for validation checks
            
        Returns:
            Dictionary with validation results
        """
        validation = {}
        
        try:
            # Validate volume calculations
            if results.pile_data is not None and results.volume_results:
                theoretical_volumes = self.volume_calculator.calculate_theoretical_volumes(results.pile_data)
                volume_validation = self.volume_calculator.validate_volume_calculations(
                    results.volume_results, theoretical_volumes, tolerance
                )
                validation['volume_calculations'] = all(volume_validation.values())
                validation['volume_details'] = volume_validation
                
            # Validate geometry integrity
            geometry_validation = {}
            for pile_id, geometry in results.geometries.items():
                geometry_validation[pile_id] = self.geometry_engine.validate_mesh(geometry.combined_mesh)
            validation['geometry_integrity'] = all(geometry_validation.values())
            validation['geometry_details'] = geometry_validation
            
            # Validate boundary clipping
            if results.boundary_info:
                validation['boundary_valid'] = results.boundary_info.get('is_valid', False)
                
            logger.info(f"Analysis validation completed: {validation}")
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            validation['error'] = str(e)
            
        return validation
        
    def get_analysis_summary(self, results: AnalysisResults) -> str:
        """
        Get a text summary of analysis results.
        
        Args:
            results: Analysis results
            
        Returns:
            Formatted summary string
        """
        if not results.summary_report:
            return "No analysis results available"
            
        summary = results.summary_report
        
        text = f"""
3D Pile Volume Analysis Summary
==============================

Analysis Date: {summary.get('Analysis_Date', 'Unknown')}
Total Piles Analyzed: {summary.get('Total_Piles_Analyzed', 0)}

Site Information:
- Boundary Area: {summary.get('Site_Boundary_Area', 0):.2f} sq units
- Boundary Perimeter: {summary.get('Site_Boundary_Perimeter', 0):.2f} units

Volume Results:
- Total Theoretical Volume: {summary.get('Total_Theoretical_Volume', 0):.3f} cubic units
- Total Final Volume: {summary.get('Total_Final_Volume', 0):.3f} cubic units
- Volume Lost to Overlaps: {summary.get('Total_Volume_Lost_to_Overlaps', 0):.3f} cubic units
- Overall Efficiency: {summary.get('Overall_Volume_Efficiency', 0)*100:.1f}%

Overlap Statistics:
- Total Overlapping Pairs: {summary.get('Total_Overlapping_Pairs', 0)}
- Total Overlap Volume: {summary.get('Total_Overlap_Volume', 0):.3f} cubic units
- Average Overlap Loss: {summary.get('Average_Overlap_Loss_Percentage', 0):.1f}%

Material Breakdown:
- Soil Piles: {summary.get('Soil_Piles_Count', 0)}
- Rock Piles: {summary.get('Rock_Piles_Count', 0)}
"""
        
        return text
