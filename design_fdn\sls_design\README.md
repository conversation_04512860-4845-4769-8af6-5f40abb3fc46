# Foundation Automation - Serviceability Limit State Design Package

The sls_design package provides Serviceability Limit State (SLS) design calculations for foundation engineering, focusing on deformation and movement criteria to ensure structural performance under service loads.

## Package Structure

The package is organized into several key modules:

### Core Modules

1. `__init__.py`
   - Package initialization
   - Exports main design functions
   - Defines package interface

2. `angular_rotation.py`
   - Handles angular rotation checks for slabs
   - Implements `check_slab_angular_rotation`
   - Ensures slab rotation limits are met

3. `deflection.py`
   - Manages pile deflection calculations
   - Implements:
     - `cal_pile_local_deflection`: Local pile deflection calculation
     - `cal_lateral_displacement`: Lateral displacement calculation
     - `check_pile_deflection`: Pile deflection verification
   - Ensures pile movement limits are met

4. `diff_settlement.py`
   - Handles differential settlement checks
   - Implements `check_differential_settlement`
   - Ensures uniform settlement across foundation elements

5. `settlement.py`
   - Manages settlement calculations
   - Implements settlement analysis functions
   - Ensures foundation settlement criteria are met

## Key Functions

### Angular Rotation
- `check_slab_angular_rotation`: Verifies slab rotation limits
  - Input: Slab geometry and loading conditions
  - Output: Rotation check results and compliance status

### Deflection
- `cal_pile_local_deflection`: Calculates local pile deflection
  - Input: Pile properties and loading conditions
  - Output: Local deflection values

- `cal_lateral_displacement`: Calculates lateral displacement
  - Input: Foundation geometry and lateral loads
  - Output: Lateral displacement values

- `check_pile_deflection`: Verifies pile deflection limits
  - Input: Calculated deflections and limit criteria
  - Output: Deflection compliance status

### Settlement
- `check_differential_settlement`: Verifies differential settlement criteria
  - Input: Foundation geometry and soil properties
  - Output: Settlement check results and compliance status

## Design Criteria

The package implements SLS design criteria for:
1. Angular Rotation Limits
   - Maximum allowable slab rotation
   - Rotation distribution criteria
   - Impact on structural performance

2. Deflection Limits
   - Maximum pile deflection
   - Lateral displacement limits
   - Movement compatibility requirements

3. Settlement Criteria
   - Maximum differential settlement
   - Uniform settlement requirements
   - Foundation stability considerations

## Usage Examples

### Angular Rotation Check
```python
from sls_design import check_slab_angular_rotation

# Check slab rotation
rotation_result = check_slab_angular_rotation(
    slab_geometry=slab_geometry,
    loading_conditions=loading_conditions
)
```

### Deflection Analysis
```python
from sls_design import cal_pile_local_deflection, check_pile_deflection

# Calculate and check pile deflection
local_deflection = cal_pile_local_deflection(
    pile_properties=pile_properties,
    loading_conditions=loading_conditions
)
deflection_check = check_pile_deflection(
    calculated_deflection=local_deflection,
    limit_criteria=limit_criteria
)
```

### Settlement Analysis
```python
from sls_design import check_differential_settlement

# Check differential settlement
settlement_result = check_differential_settlement(
    foundation_geometry=foundation_geometry,
    soil_properties=soil_properties
)
```

## Best Practices

1. Always verify input data accuracy
2. Consider site-specific conditions
3. Follow local building codes
4. Document assumptions and calculations
5. Perform sensitivity analysis

## Error Handling

The package includes error handling for:
- Invalid input data
- Calculation failures
- Limit criteria violations
- Geometric constraints

## Integration Points

This package integrates with:
- Foundation Automation design system
- Structural analysis modules
- Soil property databases
- Load combination modules

## Design Standards

The package implements SLS design standards for:
- Serviceability criteria
- Movement limits
- Structural performance
- Foundation behavior

## Version

Current version aligns with Foundation Automation system version V5.3
