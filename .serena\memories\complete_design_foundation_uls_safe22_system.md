# Complete Design Foundation ULS System

## Ultimate Limit State Design for Bored Pile Foundations (`design_fdn/uls_design_bp/`)

### N-M Interaction Curve Calculation (`nm_curve_calc.py`)
- **Complete N-M Interaction Analysis**: Ultimate limit state capacity calculations for circular reinforced concrete bored pile sections
- **Comprehensive Design Code Compliance**: Eurocode 2 (EN 1992-1-1) and international standards
- **Advanced Material Modeling**:
  - Concrete: Parabolic-rectangular stress-strain relationship per design codes
  - Steel Reinforcement: Elastic-perfectly plastic behavior with yield plateau
  - Ultimate strain limits: εcu = 0.0035 (adjustable for high-strength concrete)
  - Material safety factors: γm = 1.5 for concrete incorporated in calculations

- **N-M Interaction Analysis Cases**:
  - **Case 0**: Pure tension condition (neutral axis outside section)
  - **Case 1**: Combined tension-compression with neutral axis within section
  - **Case 2**: Combined loading with partial section in compression
  - **Case 3**: Pure compression condition (entire section in compression)

- **Technical Implementation Features**:
  - Strain compatibility analysis using plane sections remain plane assumption
  - Concrete stress block modeling with parabolic-rectangular stress distribution
  - Steel reinforcement modeling with elastic-perfectly plastic behavior
  - Numerical integration for complex stress distribution calculations
  - Vectorized operations for computational efficiency with multiple reinforcement bars
  - Case-based analysis for different neutral axis positions and loading conditions

- **Reinforcement Bar Stress Calculation**:
  - `calculate_rebar_stress()`: Individual bar stress determination
  - Strain distribution calculation based on plane sections assumption
  - Steel stress calculation using elastic-perfectly plastic material model
  - Stress limitation according to design yield strength (0.87 * fy)
  - Force and moment contribution calculation for each reinforcement bar

### Structural Design Applications
- **Foundation Design Optimization**: Axial loads and lateral moments with combined loading conditions
- **Pile Group Analysis**: Varying load combinations and eccentricities
- **Seismic Design Verification**: Dynamic loading for pile foundations
- **Performance-Based Design**: Critical foundation systems validation
- **Bored Pile Capacity Verification**: Combined loading conditions under ultimate limit state

### Integration with Circular Column System
- **`circular_column.py`**: Column geometry and reinforcement coordinate generation
- **`CircularColumn`**: Complete circular section analysis and design
- **`calculate_rebar_coordinates()`**: Precise reinforcement bar positioning

## Complete SAFE 22 Integration System (`build_fem/write_safe/safe22/`)

### Advanced Data Structure Management (`safe22_class.py`)
- **`Safe22DataFrames`**: Comprehensive data structure class for SAFE 22 analysis
- **Multi-Level DataFrame Management**: All SAFE 22 data tables with pandas DataFrames
- **Structured Finite Element Data**: Complex analysis data through multi-level indexing

### Core Data Categories

#### Analysis Options and Settings
- **`AnalysisCrackingAnalysis`**: Cracking analysis settings and parameters
- **`AnalysisDsgnandRecOptions`**: Design and recovery options configuration
- **`AnalysisFloorMeshSettings`**: Floor meshing parameters and controls
- **`AnalysisSAPFireOptions`**: SAPFire solver settings and optimization
- **`AnalysisWallMeshSettings`**: Wall meshing parameters and refinement
- **`AnalysisModelingOptions`**: General modeling options and preferences

#### Material Properties Management
- **`MatPropGeneral`**: General material properties and classifications
- **`MatPropBasicMechProps`**: Basic mechanical properties (E, ν, density)
- **`MatPropSteelData`**: Steel material data (fy, fu, E, thermal properties)
- **`MatPropConcreteData`**: Concrete material data (fc, Ec, tensile strength)
- **`MatPropRebarData`**: Reinforcement material data (fy, Es, ultimate strain)

#### Structural Elements and Connectivity
- **`FramePropSummary`**: Frame property definitions and cross-sections
- **`AreaSectionPropsSummary`**: Area section properties for slabs and walls
- **`BeamObjectConnectivity`**: Beam connectivity data and node relationships
- **`ColumnObjectConnectivity`**: Column connectivity data and assignments
- **`FloorObjectConnectivity`**: Floor connectivity data and mesh information

#### Load Definitions and Combinations
- **`LoadPatternDefinitions`**: Load pattern specifications and types
- **`LoadCombinationDefinitions`**: Load combination rules and factors
- **`AreaLoadsUniform`**: Uniform area loads on slabs and surfaces
- **`FrameLoadsDistributed`**: Distributed frame loads on beams and columns

### Data Structure Features
- **Multi-Level Column Indexing**: (Table Name, Column Name, Units) structure
- **SAFE 22 Compatibility**: Direct compatibility with CSI SAFE 22 file formats
- **Comprehensive Data Coverage**: All required tables for complete FEM analysis
- **DataFrame Initialization**: Appropriate column structures with default settings
- **Scalable Architecture**: Handles complex finite element models efficiently

### Integration Capabilities
- **CSI SAFE 22 Software Integration**: Direct data exchange with SAFE software
- **Analysis Model Management**: Complete finite element model data handling
- **Design Result Processing**: Integration with design calculation modules
- **File Format Compatibility**: Import/export capabilities for SAFE project files

This system provides complete ultimate limit state design capabilities for bored pile foundations with professional-grade N-M interaction analysis and comprehensive SAFE 22 finite element integration for advanced structural analysis workflows.