﻿"""
Pile Geometry Utility Functions

This module contains utility functions for pile geometry operations,
including pile cap creation, enlargement, and grid generation.
"""

from typing import List, Optional, Dict, Any, Callable
from shapely.geometry import Polygon, Point

from ..data_types import Point2D, LocalCoordinateSystem
from ..data_types.pile_preselection_types import PileTypeCandidate, PileTypePreselectionCriteria
from ..pile_cap_geometry.pile_cap_geometry import create_pile_cap_polygon
from ..utils.coordinate_utils import (
    find_minimum_area_bounding_rectangle,
    generate_local_grid_positions,
    local_to_global_coordinates,
    generate_optimized_grid_positions
)
from ..exceptions import GeometryError
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def create_initial_pile_cap(criteria: PileTypePreselectionCriteria, log_callback: Optional[Callable] = None) -> Polygon:
    """Create initial pile cap using user-defined edge distance."""
    log_function_entry(log_callback, "create_initial_pile_cap", 
                      edge_distance=criteria.user_edge_distance)
    
    try:
        enhanced_log(log_callback, f"Creating initial pile cap with edge distance: {criteria.user_edge_distance}m", 'DEBUG')
        
        with create_timed_logger(log_callback, "pile cap creation") as timer:
            pile_cap_result = create_pile_cap_polygon(
                group_elements=criteria.group_elements,
                excel_inputs=criteria.excel_inputs,
                site_poly=criteria.site_boundary,
                edge_dist=criteria.user_edge_distance
            )
        
        # Validate result
        is_valid = pile_cap_result.get('is_valid', False)
        polygon = pile_cap_result.get('polygon')
        
        log_validation_result(log_callback, "Pile cap creation", is_valid, 
                            f"Valid: {is_valid}, Empty: {polygon.is_empty if polygon else True}")
        
        if not is_valid or (polygon and polygon.is_empty):
            errors = pile_cap_result.get('errors', [])
            enhanced_log(log_callback, f"Pile cap creation failed: {errors}", 'ERROR')
            raise GeometryError(f"Failed to create valid initial pile cap: {errors}")
        
        # Log pile cap properties
        if polygon:
            area = polygon.area
            bounds = polygon.bounds
            log_calculation_result(log_callback, "Pile cap area", f"{area:.3f} m²")
            log_calculation_result(log_callback, "Pile cap bounds", 
                                 f"[{bounds[0]:.2f}, {bounds[1]:.2f}, {bounds[2]:.2f}, {bounds[3]:.2f}]")
        
        enhanced_log(log_callback, "Initial pile cap created successfully", 'INFO')
        log_function_exit(log_callback, "create_initial_pile_cap", polygon)
        return polygon
        
    except Exception as e:
        log_error_with_context(log_callback, e, "create_initial_pile_cap")
        raise


def enlarge_pile_cap(initial_cap: Polygon, enlargement_offset: float, log_callback: Optional[Callable] = None) -> Polygon:
    """
    Enlarge pile cap by offsetting all edges by specified amount.

    DEPRECATED: This function is deprecated. The new pile type preselection system
    generates pile type-specific maximum pile caps based on pile diameter instead
    of using a fixed enlargement offset.
    """
    log_function_entry(log_callback, "enlarge_pile_cap",
                      original_area=initial_cap.area, enlargement_offset=enlargement_offset)

    enhanced_log(log_callback, "WARNING: enlarge_pile_cap is deprecated. Use pile type-specific maximum pile cap generation instead.", 'WARNING')
    
    try:
        enhanced_log(log_callback, f"Enlarging pile cap by {enlargement_offset}m offset", 'DEBUG')
        
        # Validate inputs
        if initial_cap.is_empty or not initial_cap.is_valid:
            enhanced_log(log_callback, "Initial pile cap is invalid or empty", 'ERROR')
            log_constraint_check(log_callback, "Valid initial pile cap", initial_cap.is_valid and not initial_cap.is_empty, 
                               "valid and non-empty", False)
            raise GeometryError("Initial pile cap is invalid or empty")
        
        log_constraint_check(log_callback, "Valid initial pile cap", initial_cap.is_valid and not initial_cap.is_empty,
                           "valid and non-empty", True)
        
        # Log original properties
        original_area = initial_cap.area
        original_bounds = initial_cap.bounds
        enhanced_log(log_callback, f"Original pile cap area: {original_area:.3f} m²", 'DEBUG')
        
        with create_timed_logger(log_callback, "pile cap enlargement") as timer:
            # Buffer the pile cap outward by the enlargement offset
            enlarged_cap = initial_cap.buffer(enlargement_offset, join_style=2)  # join_style=2 for mitered joins
        
        # Validate result
        if enlarged_cap.is_empty or not enlarged_cap.is_valid:
            enhanced_log(log_callback, "Enlarged pile cap is invalid or empty", 'ERROR')
            log_validation_result(log_callback, "Pile cap enlargement", False, "Result is invalid or empty")
            raise GeometryError("Enlarged pile cap is invalid or empty")
        
        # Log enlarged properties
        enlarged_area = enlarged_cap.area
        enlarged_bounds = enlarged_cap.bounds
        area_increase = enlarged_area - original_area
        area_ratio = enlarged_area / original_area if original_area > 0 else float('inf')
        
        log_calculation_result(log_callback, "Enlarged pile cap area", f"{enlarged_area:.3f} m²")
        log_calculation_result(log_callback, "Area increase", f"{area_increase:.3f} m² ({area_ratio:.2f}x)")
        log_performance_metric(log_callback, "Area expansion factor", area_ratio, "x")
        
        enhanced_log(log_callback, "Pile cap enlarged successfully", 'INFO')
        log_validation_result(log_callback, "Pile cap enlargement", True, 
                            f"Area increased by {area_increase:.3f} m²")
        
        log_function_exit(log_callback, "enlarge_pile_cap", enlarged_cap)
        return enlarged_cap
        
    except Exception as e:
        log_error_with_context(log_callback, e, "enlarge_pile_cap")
        raise


def establish_local_coordinate_system(pile_cap: Polygon, criteria: PileTypePreselectionCriteria, 
                                    log_callback: Optional[Callable] = None) -> LocalCoordinateSystem:
    """Establish local coordinate system using minimum area bounding rectangle."""
    log_function_entry(log_callback, "establish_local_coordinate_system",
                      pile_cap_area=pile_cap.area)
    
    try:
        enhanced_log(log_callback, "Establishing local coordinate system using minimum area bounding rectangle", 'INFO')
        
        # Get all structural points for optimal rectangle calculation
        from ..utils.geometry_utils import extract_all_structural_points, create_wall_polygons_from_excel
        from shapely.geometry import MultiPoint
        from shapely.ops import unary_union
        
        with create_timed_logger(log_callback, "coordinate system establishment") as timer:
            # Extract structural points
            warnings_list = []
            structural_points = []
            
            enhanced_log(log_callback, "Extracting structural points from elements", 'DEBUG')
            
            # Extract column points
            columns = criteria.group_elements.get('columns', [])
            for i, column in enumerate(columns):
                if column and len(column) >= 3:
                    point = (float(column[1]), float(column[2]))
                    structural_points.append(point)
                    enhanced_log(log_callback, f"Column {i}: ({point[0]:.3f}, {point[1]:.3f})", 'DEBUG')
            
            log_calculation_result(log_callback, "Columns extracted", len(structural_points))
            
            # Handle walls - try to get actual wall polygons
            wall_polygons = []
            walls_list = None
            if hasattr(criteria.group_elements, 'walls'):
                walls_list = criteria.group_elements.walls
            elif isinstance(criteria.group_elements, dict) and 'walls' in criteria.group_elements:
                walls_list = criteria.group_elements['walls']
            
            if walls_list:
                enhanced_log(log_callback, f"Processing {len(walls_list)} walls", 'DEBUG')
                wall_names = [wall[0] for wall in walls_list if wall and len(wall) >= 1]
                if wall_names and criteria.excel_inputs:
                    enhanced_log(log_callback, f"Creating wall polygons for: {wall_names}", 'DEBUG')
                    wall_polygon_dict = create_wall_polygons_from_excel(
                        criteria.excel_inputs, 
                        wall_names
                    )
                    wall_polygons = [data['polygon'] for data in wall_polygon_dict.values()]
                    log_calculation_result(log_callback, "Wall polygons created", len(wall_polygons))
            
            # Create the polygon for optimal rectangle calculation
            if wall_polygons:
                enhanced_log(log_callback, "Using geometry-based approach with wall polygons", 'INFO')
                # If we have wall polygons, create a union of all geometric elements
                all_geometries = []
                
                # Add column points as small polygons
                if structural_points:
                    column_multipoint = MultiPoint(structural_points)
                    column_buffer = column_multipoint.buffer(0.1)
                    all_geometries.append(column_buffer)
                    enhanced_log(log_callback, f"Added {len(structural_points)} column points as buffered geometry", 'DEBUG')
                
                # Add wall polygons
                all_geometries.extend(wall_polygons)
                enhanced_log(log_callback, f"Added {len(wall_polygons)} wall polygons", 'DEBUG')
                
                # Add pile cap
                all_geometries.append(pile_cap)
                enhanced_log(log_callback, "Added pile cap to geometry union", 'DEBUG')
                
                # Create union of all geometries
                combined_geometry = unary_union(all_geometries)
                optimal_polygon = combined_geometry.convex_hull
                log_calculation_result(log_callback, "Combined geometry area", f"{optimal_polygon.area:.3f} m²")
            else:
                enhanced_log(log_callback, "Using point-based approach", 'INFO')
                # Point-based approach
                if structural_points:
                    multipoint = MultiPoint(structural_points)
                    structural_convex_hull = multipoint.convex_hull
                    
                    hull_area = structural_convex_hull.area
                    pile_cap_area = pile_cap.area
                    enhanced_log(log_callback, f"Structural hull area: {hull_area:.3f} m², Pile cap area: {pile_cap_area:.3f} m²", 'DEBUG')
                    
                    if hull_area > pile_cap_area:
                        optimal_polygon = structural_convex_hull
                        enhanced_log(log_callback, "Using structural convex hull (larger than pile cap)", 'INFO')
                    else:
                        optimal_polygon = pile_cap
                        enhanced_log(log_callback, "Using pile cap (larger than structural hull)", 'INFO')
                else:
                    optimal_polygon = pile_cap
                    enhanced_log(log_callback, "Using pile cap (no structural points)", 'INFO')
            
            enhanced_log(log_callback, "Computing minimum area bounding rectangle", 'DEBUG')
            local_system_result = find_minimum_area_bounding_rectangle(optimal_polygon)
            local_system = local_system_result.local_system
        
        # Log coordinate system properties
        if local_system:
            # Access origin as tuple (x, y) since OptimalCoordinateSystem.origin is a tuple
            origin_x, origin_y = local_system.origin
            enhanced_log(log_callback, f"Local system origin: ({origin_x:.3f}, {origin_y:.3f})", 'DEBUG')
            enhanced_log(log_callback, f"Local system rotation: {local_system.rotation_angle:.3f} rad ({local_system.rotation_angle * 180 / 3.14159:.1f}°)", 'DEBUG')
            enhanced_log(log_callback, f"Local system dimensions: {local_system.long_axis_length:.3f} x {local_system.short_axis_length:.3f}", 'DEBUG')
            enhanced_log(log_callback, f"Local system global aligned: {local_system.is_global_aligned}", 'DEBUG')
            log_calculation_result(log_callback, "Local coordinate system", "Successfully established")
        
        log_validation_result(log_callback, "Coordinate system establishment", local_system is not None,
                            "Local coordinate system created")
        
        enhanced_log(log_callback, "Local coordinate system established successfully", 'INFO')
        log_function_exit(log_callback, "establish_local_coordinate_system", local_system)
        return local_system
        
    except Exception as e:
        log_error_with_context(log_callback, e, "establish_local_coordinate_system")
        raise


def generate_aligned_grid(pile_candidate: PileTypeCandidate, 
                         local_system: LocalCoordinateSystem,
                         pile_cap: Polygon,
                         is_case_4: bool = True,
                         log_callback: Optional[Callable] = None) -> List[Point2D]:
    """Generate grid positions aligned with local coordinate system."""
    log_function_entry(log_callback, "generate_aligned_grid",
                      pile_diameter=pile_candidate.diameter, 
                      min_spacing=pile_candidate.min_spacing,
                      is_case_4=is_case_4)
    
    try:
        enhanced_log(log_callback, f"Generating aligned grid for {'Case 4' if is_case_4 else 'Case 1/2'}", 'INFO')
        
        # Use pile diameter or default for H-piles
        pile_diameter = pile_candidate.diameter if pile_candidate.diameter is not None else 0.6
        enhanced_log(log_callback, f"Using pile diameter: {pile_diameter}m", 'DEBUG')
        
        # Validate inputs
        if pile_cap is None or pile_cap.is_empty:
            enhanced_log(log_callback, "Pile cap is None or empty", 'ERROR')
            log_constraint_check(log_callback, "Valid pile cap", pile_cap is not None and not pile_cap.is_empty,
                               "non-null and non-empty", False)
            raise GeometryError("Pile cap is None or empty")
        
        if local_system is None:
            enhanced_log(log_callback, "Local system is None", 'ERROR')
            log_constraint_check(log_callback, "Valid local system", local_system is not None, "non-null", False)
            raise GeometryError("Local system is None")
        
        log_constraint_check(log_callback, "Valid pile cap", pile_cap is not None and not pile_cap.is_empty,
                           "non-null and non-empty", True)
        log_constraint_check(log_callback, "Valid local system", local_system is not None, "non-null", True)
        
        enhanced_log(log_callback, f"Pile cap area: {pile_cap.area:.3f} m²", 'DEBUG')
        enhanced_log(log_callback, f"Min spacing: {pile_candidate.min_spacing}m", 'DEBUG')
        
        with create_timed_logger(log_callback, "grid generation") as timer:
            if is_case_4:
                # Case 4: Use optimized grid positions with shifting to maximize pile count
                enhanced_log(log_callback, "Using optimized grid generation for Case 4", 'DEBUG')
                local_positions = generate_optimized_grid_positions(
                    local_system=local_system,
                    pile_diameter=pile_diameter,
                    min_spacing=pile_candidate.min_spacing,
                    pile_cap=pile_cap,
                    edge_clearance=0.3
                )
            else:
                # Case 1 and 2: Use basic grid without shifting (deterministic)
                enhanced_log(log_callback, "Using basic grid generation for Case 1/2", 'DEBUG')
                local_positions = generate_local_grid_positions(
                    local_system=local_system,
                    pile_diameter=pile_diameter,
                    min_spacing=pile_candidate.min_spacing,
                    edge_clearance=0.3
                )
            
            log_calculation_result(log_callback, "Local positions generated", len(local_positions))
            
            # Convert to global coordinates
            enhanced_log(log_callback, "Converting local to global coordinates", 'DEBUG')
            global_positions = []
            for i, local_pos in enumerate(local_positions):
                global_pos = local_to_global_coordinates(local_pos, local_system)
                global_positions.append(global_pos)
                if i < 5:  # Log first few positions for debugging
                    enhanced_log(log_callback, f"Local {local_pos} → Global {global_pos}", 'DEBUG')
        
        log_calculation_result(log_callback, "Global positions generated", len(global_positions))
        
        # Calculate grid efficiency
        if pile_cap.area > 0:
            pile_area = 3.14159 * (pile_diameter/2)**2 * len(global_positions)
            efficiency = (pile_area / pile_cap.area) * 100
            log_performance_metric(log_callback, "Grid efficiency", efficiency, "%")
        
        enhanced_log(log_callback, f"Generated {len(global_positions)} aligned grid positions", 'INFO')
        log_validation_result(log_callback, "Grid generation", len(global_positions) > 0,
                            f"{len(global_positions)} positions generated")
        
        log_function_exit(log_callback, "generate_aligned_grid", global_positions)
        return global_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generate_aligned_grid")
        raise


def filter_by_pile_cap_with_geometry(positions: List[Point2D], 
                                    pile_cap: Polygon,
                                    pile_candidate: PileTypeCandidate,
                                    edge_distance: float = 0.3,
                                    log_callback: Optional[Callable] = None) -> List[Point2D]:
    """Filter positions ensuring the actual pile geometry fits within pile cap."""
    log_function_entry(log_callback, "filter_by_pile_cap_with_geometry",
                      num_positions=len(positions), 
                      pile_diameter=pile_candidate.diameter,
                      edge_distance=edge_distance)
    
    try:
        enhanced_log(log_callback, f"Filtering {len(positions)} positions by pile cap geometry", 'DEBUG')
        
        pile_radius = (pile_candidate.diameter or 0.6) / 2.0
        total_clearance = pile_radius + edge_distance
        
        enhanced_log(log_callback, f"Pile radius: {pile_radius:.3f}m, Total clearance: {total_clearance:.3f}m", 'DEBUG')
        
        with create_timed_logger(log_callback, "pile cap filtering") as timer:
            filtered_positions = []
            for i, pos in enumerate(positions):
                pile_center = Point(pos[0], pos[1])
                pile_geometry = pile_center.buffer(total_clearance)
                
                if pile_cap.contains(pile_geometry):
                    filtered_positions.append(pos)
                    enhanced_log(log_callback, f"Position {i} accepted: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
                else:
                    enhanced_log(log_callback, f"Position {i} rejected: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
        
        filtered_count = len(filtered_positions)
        rejected_count = len(positions) - filtered_count
        
        log_calculation_result(log_callback, "Positions filtered", f"{filtered_count}/{len(positions)}")
        
        if rejected_count > 0:
            enhanced_log(log_callback, f"Rejected {rejected_count} positions due to pile cap constraints", 'INFO')
            filter_efficiency = (filtered_count / len(positions)) * 100 if positions else 0
            log_performance_metric(log_callback, "Filter efficiency", filter_efficiency, "%")
        else:
            enhanced_log(log_callback, "All positions passed pile cap filtering", 'INFO')
        
        log_constraint_check(log_callback, "Pile geometry containment", filtered_count, f"> 0", filtered_count > 0)
        log_validation_result(log_callback, "Pile cap filtering", True, 
                            f"{filtered_count} positions within pile cap")
        
        log_function_exit(log_callback, "filter_by_pile_cap_with_geometry", filtered_positions)
        return filtered_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "filter_by_pile_cap_with_geometry")
        # Return original positions as fallback
        enhanced_log(log_callback, "Returning original positions due to filtering error", 'WARNING')
        log_function_exit(log_callback, "filter_by_pile_cap_with_geometry", positions)
        return positions


def filter_by_site_boundary(positions: List[Point2D], site_boundary: Optional[Polygon], 
                          log_callback: Optional[Callable] = None) -> List[Point2D]:
    """Filter positions by site boundary if provided."""
    log_function_entry(log_callback, "filter_by_site_boundary",
                      num_positions=len(positions), 
                      has_boundary=site_boundary is not None)
    
    try:
        if site_boundary is None or site_boundary.is_empty:
            enhanced_log(log_callback, "No site boundary provided, returning all positions", 'DEBUG')
            log_function_exit(log_callback, "filter_by_site_boundary", positions)
            return positions
        
        enhanced_log(log_callback, f"Filtering {len(positions)} positions by site boundary", 'DEBUG')
        enhanced_log(log_callback, f"Site boundary area: {site_boundary.area:.3f} m²", 'DEBUG')
        
        with create_timed_logger(log_callback, "site boundary filtering") as timer:
            filtered_positions = []
            for i, pos in enumerate(positions):
                point = Point(pos[0], pos[1])
                if site_boundary.contains(point) or site_boundary.touches(point):
                    filtered_positions.append(pos)
                    enhanced_log(log_callback, f"Position {i} accepted: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
                else:
                    enhanced_log(log_callback, f"Position {i} rejected (outside boundary): ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
        
        filtered_count = len(filtered_positions)
        rejected_count = len(positions) - filtered_count
        
        log_calculation_result(log_callback, "Boundary filtering result", f"{filtered_count}/{len(positions)}")
        
        if rejected_count > 0:
            enhanced_log(log_callback, f"Rejected {rejected_count} positions outside site boundary", 'INFO')
            boundary_efficiency = (filtered_count / len(positions)) * 100 if positions else 0
            log_performance_metric(log_callback, "Boundary filter efficiency", boundary_efficiency, "%")
        else:
            enhanced_log(log_callback, "All positions within site boundary", 'INFO')
        
        log_validation_result(log_callback, "Site boundary filtering", True,
                            f"{filtered_count} positions within boundary")
        
        log_function_exit(log_callback, "filter_by_site_boundary", filtered_positions)
        return filtered_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "filter_by_site_boundary")
        # Return original positions as fallback
        enhanced_log(log_callback, "Returning original positions due to filtering error", 'WARNING')
        log_function_exit(log_callback, "filter_by_site_boundary", positions)
        return positions


def trim_pile_cap_by_site_boundary(pile_cap: Polygon, site_boundary: Optional[Polygon],
                                 log_callback: Optional[Callable] = None) -> Polygon:
    """Trim pile cap by site boundary intersection for Case 4 optimization."""
    log_function_entry(log_callback, "trim_pile_cap_by_site_boundary",
                      pile_cap_area=pile_cap.area,
                      has_boundary=site_boundary is not None)
    
    try:
        if site_boundary is None or site_boundary.is_empty:
            enhanced_log(log_callback, "No site boundary provided, returning original pile cap", 'DEBUG')
            log_function_exit(log_callback, "trim_pile_cap_by_site_boundary", pile_cap)
            return pile_cap
        
        enhanced_log(log_callback, "Trimming pile cap by site boundary intersection", 'DEBUG')
        original_area = pile_cap.area
        boundary_area = site_boundary.area
        enhanced_log(log_callback, f"Original pile cap area: {original_area:.3f} m²", 'DEBUG')
        enhanced_log(log_callback, f"Site boundary area: {boundary_area:.3f} m²", 'DEBUG')
        
        with create_timed_logger(log_callback, "pile cap trimming") as timer:
            # Calculate intersection of pile cap and site boundary
            trimmed_cap = pile_cap.intersection(site_boundary)
        
        if trimmed_cap.is_empty:
            enhanced_log(log_callback, "Intersection is empty, returning original pile cap", 'WARNING')
            log_validation_result(log_callback, "Pile cap trimming", False, "Empty intersection")
            log_function_exit(log_callback, "trim_pile_cap_by_site_boundary", pile_cap)
            return pile_cap
        
        # Handle MultiPolygon result - take the largest polygon
        if hasattr(trimmed_cap, 'geoms'):
            enhanced_log(log_callback, f"Intersection resulted in {len(trimmed_cap.geoms)} geometries", 'DEBUG')
            largest_area = 0
            largest_polygon = None
            for i, geom in enumerate(trimmed_cap.geoms):
                if hasattr(geom, 'area') and geom.area > largest_area:
                    largest_area = geom.area
                    largest_polygon = geom
                    enhanced_log(log_callback, f"Geometry {i}: area = {geom.area:.3f} m²", 'DEBUG')
            
            if largest_polygon:
                trimmed_cap = largest_polygon
                enhanced_log(log_callback, f"Selected largest geometry with area {largest_area:.3f} m²", 'INFO')
            else:
                trimmed_cap = pile_cap
                enhanced_log(log_callback, "No valid geometry found, using original pile cap", 'WARNING')
        
        # Calculate trimming results
        trimmed_area = trimmed_cap.area
        area_reduction = original_area - trimmed_area
        area_retention = (trimmed_area / original_area) * 100 if original_area > 0 else 0
        
        log_calculation_result(log_callback, "Trimmed pile cap area", f"{trimmed_area:.3f} m²")
        log_calculation_result(log_callback, "Area reduction", f"{area_reduction:.3f} m²")
        log_performance_metric(log_callback, "Area retention", area_retention, "%")
        
        if area_reduction > 0.001:  # Significant reduction
            enhanced_log(log_callback, f"Pile cap trimmed by {area_reduction:.3f} m² ({100-area_retention:.1f}%)", 'INFO')
        else:
            enhanced_log(log_callback, "Pile cap minimally affected by site boundary", 'DEBUG')
        
        log_validation_result(log_callback, "Pile cap trimming", True,
                            f"Trimmed to {area_retention:.1f}% of original area")
        
        log_function_exit(log_callback, "trim_pile_cap_by_site_boundary", trimmed_cap)
        return trimmed_cap
        
    except Exception as e:
        log_error_with_context(log_callback, e, "trim_pile_cap_by_site_boundary")
        # Return original pile cap as fallback
        enhanced_log(log_callback, "Returning original pile cap due to trimming error", 'WARNING')
        log_function_exit(log_callback, "trim_pile_cap_by_site_boundary", pile_cap)
        return pile_cap

