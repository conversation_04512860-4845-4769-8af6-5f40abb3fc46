"""
File Path Manager Module for ETABS CWLS

This module handles file path validation, configuration, and management for
the ETABS Core Wall Loading Schedule system. It provides comprehensive
file system operations with enhanced error handling and validation.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional file management patterns

Key Functions:
    filepath_selection: Validate and configure file paths for operations
    validate_file_access: Validate file accessibility and permissions
    configure_output_paths: Set up output directory and file paths
    ensure_directory_exists: Create directories with proper error handling

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import os
from typing import Optional, Callable

from etabs_cwls.core import _class
from etabs_cwls.config.exceptions import DataValidationError, FileOperationError, ConfigurationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger
)


def filepath_selection(
    file_path: _class.file_path, 
    program: int, 
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> _class.file_path:
    """
    Validate and configure file paths for the selected program operation.
    
    This function validates that required files are available for the selected
    operation and configures output paths and sheet names. It implements the
    zero fallback policy by requiring explicit file selection.
    
    Args:
        file_path: File path management object
        program: Program operation number (1=init, 2=pier force, 3=joint reaction)
        ETABS_converter: ETABS converter object (for validation)
        log_callback: Optional logging callback function
        
    Returns:
        Validated and configured file path object
        
    Raises:
        DataValidationError: If input parameters are invalid
        FileOperationError: If required files are not available
        ConfigurationError: If path configuration fails
        
    Example:
        >>> file_path = _class.file_path(accessfile1="model.accdb")
        >>> configured_path = filepath_selection(file_path, 1, converter)
    """
    log_function_entry(log_callback, "filepath_selection", 
                      program=program, has_accessfile1=bool(file_path.accessfile1))
    
    try:
        # Input validation
        if not isinstance(file_path, _class.file_path):
            raise DataValidationError(
                f"file_path must be a file_path instance, got {type(file_path).__name__}",
                field_name="file_path",
                invalid_value=type(file_path).__name__,
                expected_type="file_path",
                error_code="INVALID_FILE_PATH_TYPE"
            )
        
        if not isinstance(program, int):
            raise DataValidationError(
                f"program must be an integer, got {type(program).__name__}",
                field_name="program",
                invalid_value=program,
                expected_type="int",
                error_code="INVALID_PROGRAM_TYPE"
            )
        
        if program not in [1, 2, 3]:
            raise DataValidationError(
                f"program must be 1, 2, or 3, got {program}",
                field_name="program",
                invalid_value=program,
                error_code="INVALID_PROGRAM_VALUE"
            )
        
        enhanced_log(log_callback, f"Configuring file paths for program {program}", 'INFO')
        
        # Validate required files based on program
        program_names = {1: "initialization", 2: "pier force schedule", 3: "joint reaction schedule"}
        program_name = program_names.get(program, f"program {program}")
        
        if not file_path.accessfile1:
            raise FileOperationError(
                f"Primary ETABS file is required for {program_name}",
                operation="validate",
                error_code="MISSING_PRIMARY_FILE"
            )
        
        enhanced_log(log_callback, f"Primary ETABS file validated: {file_path.accessfile1}", 'DEBUG')
        
        # Validate file existence and accessibility
        file_path.validate_all_paths(log_callback)
        
        # Configure output paths with validation
        configure_output_paths(file_path, log_callback)
        
        # Configure sheet names
        configure_sheet_names(file_path, log_callback)
        
        # Log configuration summary
        file_info = file_path.get_file_info()
        log_calculation_result(log_callback, "configured_files", 
                             sum(1 for info in file_info.values() if info['path']), "files")
        
        log_validation_result(log_callback, "file_path_configuration", True,
                            f"Successfully configured paths for {program_name}")
        log_function_exit(log_callback, "filepath_selection", "Configured file paths")
        
        return file_path
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"filepath_selection(program={program})")
        log_validation_result(log_callback, "file_path_configuration", False, str(e))
        
        # Convert generic exceptions to specific types
        if isinstance(e, (DataValidationError, FileOperationError, ConfigurationError)):
            raise
        else:
            raise ConfigurationError(
                f"Failed to configure file paths: {str(e)}",
                error_code="FILE_PATH_CONFIGURATION_FAILED"
            ) from e


def configure_output_paths(
    file_path: _class.file_path,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Configure output directory and file paths.
    
    Args:
        file_path: File path object to configure
        log_callback: Optional logging callback function
        
    Raises:
        FileOperationError: If output path configuration fails
    """
    log_function_entry(log_callback, "configure_output_paths")
    
    try:
        with create_timed_logger(log_callback, "output_path_configuration") as timer:
            if file_path.excel_outputfolder is None:
                # Use directory of primary file as default output location
                primary_dir = os.path.dirname(file_path.accessfile1)
                file_path.excel_outputfolder = primary_dir
                enhanced_log(log_callback, f"Output folder set to: {primary_dir}", 'DEBUG')
            
            if file_path.design_excel_path is None:
                # Generate output Excel file name
                base_name = os.path.splitext(os.path.basename(file_path.accessfile1))[0]
                excel_filename = f"{base_name}_loading_schedule.xlsx"
                file_path.design_excel_path = os.path.join(file_path.excel_outputfolder, excel_filename)
                enhanced_log(log_callback, f"Excel output path set to: {file_path.design_excel_path}", 'DEBUG')
            
            # Ensure output directory exists
            ensure_directory_exists(os.path.dirname(file_path.design_excel_path), log_callback)
        
        log_function_exit(log_callback, "configure_output_paths", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "configure_output_paths")
        raise


def configure_sheet_names(
    file_path: _class.file_path,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Configure standardized sheet names for Excel output.
    
    Args:
        file_path: File path object to configure
        log_callback: Optional logging callback function
    """
    log_function_entry(log_callback, "configure_sheet_names")
    
    try:
        # Configure sheet names (these are standardized)
        sheet_config = {
            'parameter': 'Parameter',
            'loadschedule': 'Combined Load Schedule',
            'loadschedule_C': 'Column Load Schedule',
            'loadschedule_W': 'Wall Load Schedule',
            'loadschedule_raw': 'Raw_loadschedule',
            'storylist': 'Story',
            'mapping': 'Load Mapping'
        }
        
        for attr_name, sheet_name in sheet_config.items():
            setattr(file_path, attr_name, sheet_name)
        
        enhanced_log(log_callback, f"Configured {len(sheet_config)} sheet names", 'DEBUG')
        log_calculation_result(log_callback, "configured_sheets", len(sheet_config), "sheets")
        
        log_function_exit(log_callback, "configure_sheet_names", f"{len(sheet_config)} sheets")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "configure_sheet_names")
        raise


def ensure_directory_exists(
    directory_path: str,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory_path: Path to the directory to create
        log_callback: Optional logging callback function
        
    Raises:
        FileOperationError: If directory creation fails
    """
    log_function_entry(log_callback, "ensure_directory_exists", directory=directory_path)
    
    try:
        if not os.path.exists(directory_path):
            try:
                os.makedirs(directory_path, exist_ok=True)
                enhanced_log(log_callback, f"Created directory: {directory_path}", 'INFO')
            except OSError as e:
                raise FileOperationError(
                    f"Cannot create directory: {directory_path}",
                    file_path=directory_path,
                    operation="create_directory",
                    error_code="DIRECTORY_CREATE_FAILED"
                ) from e
        else:
            enhanced_log(log_callback, f"Directory already exists: {directory_path}", 'DEBUG')
        
        log_function_exit(log_callback, "ensure_directory_exists", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"ensure_directory_exists({directory_path})")
        raise


def validate_file_access(
    file_path: str,
    operation: str = "read",
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate file accessibility for the specified operation.
    
    Args:
        file_path: Path to the file to validate
        operation: Type of operation ("read", "write", "execute")
        log_callback: Optional logging callback function
        
    Returns:
        True if file is accessible for the operation
        
    Raises:
        FileOperationError: If file access validation fails
    """
    log_function_entry(log_callback, "validate_file_access", 
                      file_path=file_path, operation=operation)
    
    try:
        if not os.path.exists(file_path):
            raise FileOperationError(
                f"File not found: {file_path}",
                file_path=file_path,
                operation=operation,
                error_code="FILE_NOT_FOUND"
            )
        
        # Check appropriate permissions
        if operation == "read" and not os.access(file_path, os.R_OK):
            raise FileOperationError(
                f"No read permission for file: {file_path}",
                file_path=file_path,
                operation=operation,
                error_code="FILE_READ_PERMISSION_DENIED"
            )
        
        if operation == "write" and not os.access(file_path, os.W_OK):
            raise FileOperationError(
                f"No write permission for file: {file_path}",
                file_path=file_path,
                operation=operation,
                error_code="FILE_WRITE_PERMISSION_DENIED"
            )
        
        if operation == "execute" and not os.access(file_path, os.X_OK):
            raise FileOperationError(
                f"No execute permission for file: {file_path}",
                file_path=file_path,
                operation=operation,
                error_code="FILE_EXECUTE_PERMISSION_DENIED"
            )
        
        enhanced_log(log_callback, f"File access validated for {operation}: {file_path}", 'DEBUG')
        log_function_exit(log_callback, "validate_file_access", True)
        
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"validate_file_access({file_path}, {operation})")
        raise


# Export all functions
__all__ = [
    'filepath_selection',
    'configure_output_paths',
    'configure_sheet_names',
    'ensure_directory_exists',
    'validate_file_access'
]
