"""
Enhanced Logging Utilities for Foundation Automation System

This module provides a comprehensive, production-ready logging framework specifically designed
for the Foundation Automation System's pile estimation and analysis workflows. It implements
advanced logging capabilities with explicit log levels, backward compatibility, performance
tracking, and specialized logging patterns for engineering applications.

Core Features:
    - Multi-level logging with explicit level support (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    - Backward compatibility with legacy log_callback functions
    - Specialized logging patterns for engineering calculations and validations
    - Performance metrics tracking with timing and efficiency measurements
    - Function entry/exit logging for comprehensive debugging
    - Progress tracking for long-running optimization operations
    - Constraint checking and validation result logging
    - Error context logging with detailed diagnostic information
    - Timed operation logging with automatic duration calculation

Display Mode Support:
    - GUI Mode: Displays all log levels for comprehensive user feedback
    - Terminal Mode: Displays only ERROR/WARNING/CRITICAL for focused output
    - Automatic detection and adaptation to different callback interfaces

Engineering-Specific Logging:
    - Algorithm step tracking for complex workflows
    - Calculation result logging with units and precision
    - Validation result logging with pass/fail status
    - Constraint checking with limit comparisons
    - Performance metrics with engineering units
    - Load analysis and structural calculation logging

Integration Patterns:
    - Foundation Agent main workflows
    - Pile estimation and optimization algorithms
    - NSGA-III multi-objective optimization
    - AI pile type pre-selection systems
    - DXF visualization and CAD integration
    - Excel data processing and validation

Usage Examples:
    Basic Logging:
        >>> from logging_utils import enhanced_log
        >>> enhanced_log(log_callback, "Starting pile analysis", 'INFO')
        >>> enhanced_log(log_callback, "Critical error in calculation", 'ERROR')

    Function Tracking:
        >>> from logging_utils import log_function_entry, log_function_exit
        >>> log_function_entry(log_callback, "calculate_pile_capacity", pile_count=10)
        >>> # ... function implementation ...
        >>> log_function_exit(log_callback, "calculate_pile_capacity", "Success")

    Performance Monitoring:
        >>> from logging_utils import log_performance_metric, create_timed_logger
        >>> with create_timed_logger(log_callback, "optimization") as timer:
        ...     # ... optimization code ...
        ...     log_performance_metric(log_callback, "piles_per_second", 15.3, "piles/s")

    Validation and Constraints:
        >>> from logging_utils import log_validation_result, log_constraint_check
        >>> log_validation_result(log_callback, "input_validation", True, "All inputs valid")
        >>> log_constraint_check(log_callback, "pile_spacing", 1.8, ">= 1.5", True)

    Engineering Calculations:
        >>> from logging_utils import log_calculation_result, log_algorithm_step
        >>> log_algorithm_step(log_callback, "NSGA-III", "Initializing population")
        >>> log_calculation_result(log_callback, "total_pile_capacity", 15000, "kN")

Backward Compatibility:
    The module automatically detects legacy log_callback functions that only accept
    message strings and adapts the interface accordingly. This ensures seamless
    integration with existing Foundation Automation components while providing
    enhanced functionality for new implementations.

Thread Safety:
    All logging functions are designed to be thread-safe and can be used safely
    in multi-threaded environments such as parallel pile optimization workflows.

Author: Foundation Automation System
Date: June 2025
Version: 5.6.9
License: MIT
"""

import inspect

import time

from typing import Optional, Callable, Any, Union

def enhanced_log(log_callback: Optional[Callable], message: str, level: str = 'INFO') -> None:

    """

    Enhanced logging function with backward compatibility.

    This function provides enhanced logging that works with both:

    - New enhanced log_to_console() method that accepts log levels

    - Legacy log callback functions that only accept message strings

    The logging system is designed to:

    - Display all messages in GUI applications

    - Only display ERROR/WARNING/CRITICAL messages in terminal

    - Provide clear log level formatting for better debugging

    Args:

        log_callback: Optional callback function for logging

        message: The log message to display

        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

    """

    if not log_callback:

        return

    try:

        # Check if the callback accepts a second parameter (log level)

        sig = inspect.signature(log_callback)

        params = list(sig.parameters.keys())

        if len(params) >= 2:

            # Enhanced callback that accepts log level

            log_callback(message, level)

        else:

            # Legacy callback that only accepts message

            # Format message with log level for legacy callbacks

            formatted_message = f"[{level}] {message}"

            log_callback(formatted_message)

    except (TypeError, ValueError, AttributeError):

        # Fallback: try with just the message

        try:

            log_callback(message)

        except Exception:

            # If all else fails, silently continue

            pass

def log_function_entry(log_callback: Optional[Callable], function_name: str, **kwargs) -> None:

    """

    Log function entry with parameters.

    Args:

        log_callback: Optional callback function for logging

        function_name: Name of the function being entered

        **kwargs: Function parameters to log

    """

    if not kwargs:

        enhanced_log(log_callback, f"! Entering {function_name}()", 'DEBUG')

    else:

        # Format parameters for logging (limit length for readability)

        params_str = ", ".join(f"{k}={_format_param_value(v)}" for k, v in kwargs.items())

        if len(params_str) > 100:

            params_str = params_str[:97] + "..."

        enhanced_log(log_callback, f"! Entering {function_name}({params_str})", 'DEBUG')

def log_function_exit(log_callback: Optional[Callable], function_name: str, result: Any = None, **kwargs) -> None:

    """

    Log function exit with optional result and additional parameters.

    Args:

        log_callback: Optional callback function for logging

        function_name: Name of the function being exited

        result: Optional result to log

        **kwargs: Additional parameters to log (e.g., processed_count=5, centroid_x=10.5)

    """

    # Build the exit message
    message_parts = [f"! Exiting {function_name}()"]

    # Add result if provided
    if result is not None:
        result_str = _format_param_value(result)
        message_parts.append(f"! {result_str}")

    # Add additional parameters if provided
    if kwargs:
        params_str = ", ".join(f"{k}={_format_param_value(v)}" for k, v in kwargs.items())
        if len(params_str) > 100:
            params_str = params_str[:97] + "..."
        message_parts.append(f"({params_str})")

    # Join all parts
    full_message = " ".join(message_parts)
    enhanced_log(log_callback, full_message, 'DEBUG')

def log_progress(log_callback: Optional[Callable], current: int, total: int, operation: str) -> None:

    """

    Log progress for long-running operations.

    Args:

        log_callback: Optional callback function for logging

        current: Current progress count

        total: Total count

        operation: Description of the operation

    """

    # Type validation to prevent TypeError in division
    try:
        current_int = int(current) if current is not None else 0
        total_int = int(total) if total is not None else 0
        operation_str = str(operation) if operation is not None else "operation"
    except (ValueError, TypeError) as e:
        # Fallback to safe values if conversion fails
        enhanced_log(log_callback, f"Warning: Invalid parameters in log_progress - current: {current} ({type(current)}), total: {total} ({type(total)}), operation: {operation} ({type(operation)}). Error: {e}", 'WARNING')
        current_int = 0
        total_int = 1
        operation_str = "operation"

    if total_int > 0:

        percentage = (current_int / total_int) * 100

        enhanced_log(log_callback, f"Progress - {operation_str}: {current_int}/{total_int} ({percentage:.1f}%)", 'INFO')

    else:

        enhanced_log(log_callback, f"Progress - {operation_str}: {current_int} completed", 'INFO')

def log_performance_metric(log_callback: Optional[Callable], metric_name: str, value: Union[float, int], unit: str = '') -> None:

    """

    Log performance metrics.

    Args:

        log_callback: Optional callback function for logging

        metric_name: Name of the metric

        value: Metric value

        unit: Optional unit string

    """

    unit_str = f" {unit}" if unit else ""

    if isinstance(value, float):

        enhanced_log(log_callback, f"Performance - {metric_name}: {value:.3f}{unit_str}", 'DEBUG')

    else:

        enhanced_log(log_callback, f"Performance - {metric_name}: {value}{unit_str}", 'DEBUG')

def log_validation_result(log_callback: Optional[Callable], validation_name: str, passed: bool, details: str = '') -> None:

    """

    Log validation results.

    Args:

        log_callback: Optional callback function for logging

        validation_name: Name of the validation

        passed: Whether validation passed

        details: Optional details about the validation

    """

    status = "PASSED" if passed else "FAILED"

    level = 'INFO' if passed else 'WARNING'

    message = f"Validation - {validation_name}: {status}"

    if details:

        message += f" - {details}"

    enhanced_log(log_callback, message, level)

def log_algorithm_step(log_callback: Optional[Callable], algorithm_name: str, step_name: str, details: str = '') -> None:

    """

    Log algorithm execution steps.

    Args:

        log_callback: Optional callback function for logging

        algorithm_name: Name of the algorithm

        step_name: Name of the current step

        details: Optional step details

    """

    message = f"Algorithm - {algorithm_name}: {step_name}"

    if details:

        message += f" - {details}"

    enhanced_log(log_callback, message, 'INFO')

def log_calculation_result(log_callback: Optional[Callable], calculation_name: str, result: Any, unit: str = '') -> None:

    """

    Log calculation results.

    Args:

        log_callback: Optional callback function for logging

        calculation_name: Name of the calculation

        result: Calculation result

        unit: Optional unit string

    """

    result_str = _format_param_value(result)

    unit_str = f" {unit}" if unit else ""

    enhanced_log(log_callback, f"Calculation - {calculation_name}: {result_str}{unit_str}", 'INFO')

def log_constraint_check(log_callback: Optional[Callable], constraint_name: str, value: Any, limit: Any, satisfied: bool) -> None:

    """

    Log constraint check results.

    Args:

        log_callback: Optional callback function for logging

        constraint_name: Name of the constraint

        value: Current value

        limit: Constraint limit

        satisfied: Whether constraint is satisfied

    """

    status = "OK" if satisfied else "VIOLATED"

    level = 'DEBUG' if satisfied else 'WARNING'

    value_str = _format_param_value(value)

    limit_str = _format_param_value(limit)

    enhanced_log(log_callback, f"Constraint - {constraint_name}: {value_str} vs {limit_str} [{status}]", level)

def log_error_with_context(log_callback: Optional[Callable], error: Exception, context: str) -> None:

    """

    Log errors with contextual information.

    Args:

        log_callback: Optional callback function for logging

        error: The exception that occurred

        context: Contextual information about where the error occurred

    """

    error_msg = f"Error in {context}: {type(error).__name__}: {str(error)}"

    enhanced_log(log_callback, error_msg, 'ERROR')

def create_timed_logger(log_callback: Optional[Callable], operation_name: str) -> 'TimedLogger':

    """

    Create a timed logger for measuring operation duration.

    Args:

        log_callback: Optional callback function for logging

        operation_name: Name of the operation being timed

    Returns:

        TimedLogger instance

    """

    return TimedLogger(log_callback, operation_name)

def _format_param_value(value: Any) -> str:

    """

    Format parameter values for logging display.

    Args:

        value: Value to format

    Returns:

        Formatted string representation

    """

    if value is None:

        return "None"

    elif isinstance(value, (int, float)):

        if isinstance(value, float):

            return f"{value:.3f}"

        else:

            return str(value)

    elif isinstance(value, str):

        if len(value) > 50:

            return f'"{value[:47]}..."'

        else:

            return f'"{value}"'

    elif isinstance(value, (list, tuple)):

        if len(value) > 5:

            return f"{type(value).__name__}[{len(value)} items]"

        else:

            return str(value)

    elif isinstance(value, dict):

        if len(value) > 3:

            return f"dict[{len(value)} items]"

        else:

            return str(value)

    else:

        return f"{type(value).__name__}(...)"

class TimedLogger:

    """

    Context manager for timing operations and logging results.

    """

    def __init__(self, log_callback: Optional[Callable], operation_name: str):

        self.log_callback = log_callback

        self.operation_name = operation_name

        self.start_time = None

        self.end_time = None

    def __enter__(self):

        self.start_time = time.time()

        enhanced_log(self.log_callback, f"Starting {self.operation_name}", 'DEBUG')

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):

        self.end_time = time.time()

        duration = self.end_time - self.start_time

        if exc_type is None:

            log_performance_metric(self.log_callback, f"{self.operation_name}_duration", duration, "seconds")

            enhanced_log(self.log_callback, f"Completed {self.operation_name} in {duration:.3f}s", 'INFO')

        else:

            enhanced_log(self.log_callback, f"Failed {self.operation_name} after {duration:.3f}s: {exc_val}", 'ERROR')

    def get_duration(self) -> Optional[float]:

        """Get the duration of the timed operation."""

        if self.start_time and self.end_time:

            return self.end_time - self.start_time

        return None

def setup_module_logger(module_name: str) -> Any:

    """

    Set up a module logger that uses the enhanced logging system.

    Args:

        module_name: Name of the module for logging identification

    Returns:

        Logger-like object that can be used for module-specific logging

    """

    import logging

    # Create a standard logger but we'll typically use the enhanced_log system

    logger = logging.getLogger(module_name)

    if not logger.handlers:

        # Set up basic configuration if not already configured

        logging.basicConfig(level=logging.INFO)

    return logger