# FEM Building Module Structure (`build_fem/`)

## Main FEM Building Directory
```
build_fem/
├── 📄 builder_main.py           # Core automation functions
├── 📄 builder_gui.py            # SAFE Model Builder GUI
├── 📄 builder_safe.py           # SAFE model generation
├── 📄 builder_soil_spring.py    # Soil spring calculations
├── 📄 build_fem_config.py       # Configuration (70+ constants)
├── 📄 functions.py              # Utility functions
├── 📄 README.md                 # FEM building documentation
├── 📄 __init__.py               # Module initialization
├── 📁 __pycache__/              # Compiled Python files
└── 📁 write_safe/               # SAFE model writing system
```

## 📝 SAFE Writing System (`write_safe/`)
```
write_safe/
├── 📄 write_geometry.py         # Geometry writing functions
├── 📄 write_load.py             # Load writing functions
├── 📄 write_load_comb.py        # Load combination writing
├── 📄 write_load_output.py      # Load output formatting
├── 📄 write_material.py         # Material property writing
├── 📄 write_str_prop.py         # Structural property writing
├── 📄 write_soil.py             # Soil property writing
├── 📄 write_mp.py               # Micropile writing
├── 📄 write_bp_shp_dhp.py       # Pile element writing
├── 📄 write_design_strip.py     # Design strip generation
├── 📄 README.md                 # Write system documentation
├── 📄 __init__.py               # Module initialization
├── 📁 __pycache__/              # Compiled Python files
├── 📁 safe16/                   # SAFE16 specific functions
└── 📁 safe22/                   # SAFE22 specific functions
```

## 🔧 SAFE16 Support (`write_safe/safe16/`)
```
safe16/
├── 📄 safe16_class.py           # Safe16DataFrames class
├── 📄 safe16_export.py          # F2K and Excel export
├── 📄 safe16_geometry.py        # Geometry definitions
├── 📄 safe16_load.py            # Load definitions
├── 📄 safe16_load_comb.py       # Load combinations
├── 📄 safe16_material.py        # Material definitions
├── 📄 safe16_str_prop.py        # Structural properties
├── 📄 safe16_bp_shp_dhp.py      # Pile elements
├── 📄 README.md                 # SAFE16 documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## ⚡ SAFE22 Support (`write_safe/safe22/`)
```
safe22/
├── 📄 safe22_class.py           # Safe22DataFrames class
├── 📄 safe22_export.py          # Enhanced export with GUIDs
├── 📄 safe22_geometry.py        # Advanced geometry with connectivity
├── 📄 safe22_load.py            # Enhanced load definitions
├── 📄 safe22_load_comb.py       # Advanced load combinations
├── 📄 safe22_material.py        # Updated material properties
├── 📄 safe22_str_prop.py        # Enhanced structural properties
├── 📄 safe22_bp_shp_dhp.py      # Advanced pile elements
├── 📄 README.md                 # SAFE22 documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```