# Types Package for Pile Estimation

This package defines all custom data types, structures, and protocols used throughout the pile foundation estimation system. It ensures type consistency and clarity across various modules.

## Overview

The `types` package serves as the central repository for all data model definitions, including representations for structural elements, pile configurations, load data, and results. By standardizing these types, it improves code readability, maintainability, and interoperability between different components of the system.

## Key Modules

- `basic_types.py`: Defines fundamental data types such as `Point2D`, `Point3D`, and `BoundingBox` that are commonly used for geometric representations.
- `element_types.py`: Specifies types related to structural elements, including `ColumnData`, `WallData`, and `GroupElements` for clustered structures.
- `pile_types.py`: Contains data types and configuration classes specific to piles, such as `PileLocation` and `PileConfig`.
- `result_types.py`: Defines structures for various output results, including `LoadData`, `PileCapResult`, and `PileGroupResult`.
- `__init__.py`: Aggregates and exports all relevant types from its sub-modules, making them easily importable across the project. 