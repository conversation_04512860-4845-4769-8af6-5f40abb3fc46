# Foundation Automation Agent (fdn_agent)

The Foundation Agent is the core AI-powered engine of the Foundation Automation System, responsible for intelligent pile estimation, multi-objective optimization, and automated foundation design workflows. It combines artificial intelligence with proven engineering principles to deliver optimal foundation solutions.

## Overview

The `fdn_agent` module provides the central AI-driven control and coordination layer for the entire automation suite. It orchestrates sophisticated workflows that include:

- **AI-Driven Pile Type Pre-Selection**: Intelligent analysis of load patterns, soil conditions, and structural requirements
- **NSGA-III Multi-Objective Optimization**: Advanced genetic algorithm optimization balancing cost, performance, and constructability
- **Automated Element Grouping**: Smart clustering algorithms with auto-threshold optimization and silhouette analysis
- **Professional Visualization**: AutoCAD DXF generation with detailed annotations and comprehensive layer organization
- **Enhanced Logging Framework**: Multi-level logging with performance metrics and comprehensive debugging capabilities

The system enables engineers to perform complex foundation design tasks with unprecedented efficiency and intelligence.

## Key Capabilities

- **Automated Setup and Data Initialization**: Handles the initialization of file paths and loads input data from Excel files.
- **Pile Estimation and Design**: Integrates a comprehensive pile estimation system for grouping structural elements, calculating loads, generating pile layouts, and designing pile caps.
- **Data Transformation and Calculations**: Contains utility functions for various engineering calculations and data transformations.
- **Configuration Management**: Manages file paths and Excel sheet name constants.
- **User Interface**: Provides a graphical user interface (GUI) for interacting with the automation system.
- **Visualization**: Generates detailed CAD (DXF) outputs for design review.

## Sub-Packages and Key Files

- [`pile_estimation/`](./pile_estimation/README.md): A comprehensive sub-module dedicated to the estimation and design of pile foundations, including analysis, load calculation, geometry, layout, optimization, and visualization.
- `agent_main.py`: The primary backend logic orchestrator, responsible for initializing the system, running core analyses like pile estimation, and managing the overall workflow.
- `agent_gui.py`: Implements the graphical user interface (GUI) for the Foundation Automation Agent, allowing users to select inputs, run analyses, and view progress.
- `fdn_agent_config.py`: Contains global configuration constants, such as standard file names and Excel sheet names, used throughout the system.
- `functions.py`: Provides a collection of general utility functions for various calculations and data manipulations within the broader agent framework.
- `exceptions.py`: Defines custom exception classes used across the pile estimation system for more specific error handling.

## Architecture

```
fdn_agent/
├── agent_main.py                    # 🤖 Main orchestration and workflow coordination
├── agent_gui.py                     # 🖥️ Graphical user interface
├── pile_estimation/                 # 🧮 Core pile estimation system
│   ├── pile_workflow_coordinator.py # Workflow management and coordination
│   ├── pile_type_selection/         # AI pile type selection engine
│   ├── optimization/                # NSGA-III optimization algorithms
│   ├── clustering/                  # Element grouping algorithms
│   ├── load_calculator/             # Load analysis and calculations
│   └── utils/                       # Utilities and enhanced logging
├── fdn_agent_config.py             # Configuration constants
├── functions.py                     # General utility functions
├── exceptions.py                    # Custom exception classes
└── helpers.py                       # Helper functions and utilities
```

## Usage

### GUI Application

To launch the Foundation Agent GUI:
```bash
python fdn_agent/agent_gui.py
```

The GUI provides an intuitive interface for:
- Project initialization and Excel data import
- Pile type selection and parameter configuration
- AI-powered optimization execution with real-time progress
- Results visualization and professional DXF export

### Programmatic Usage

#### Basic AI-Powered Pile Estimation

```python
from fdn_agent.agent_main import (
    automated_initialize_setup,
    run_pile_estimation_with_multi_type_optimization
)

# 1. Initialize project with comprehensive validation
file_paths, inputs, outputs = automated_initialize_setup(
    input_folder="./project_data",
    existing_folder="./previous_project",  # Optional for data continuity
    log_callback=print
)

# 2. Define pile types for AI optimization
pile_types = [
    {
        'type': 'DHP',           # Driven H-Pile
        'capacity': 3663.0,      # kN
        'section': 'UBP_305x305x223',
        'min_spacing': 1.2       # meters
    },
    {
        'type': 'SHP',           # Socket H-Pile
        'capacity': 6106.0,      # kN
        'section': 'UBP_305x305x223',
        'min_spacing': 1.85      # meters
    },
    {
        'type': 'BP',            # Bored Pile
        'capacity': 5000.0,      # kN
        'diameter': 0.6,         # meters
        'min_spacing': 1.8       # meters
    }
]

# 3. Execute AI-powered optimization
results = run_pile_estimation_with_multi_type_optimization(
    excel_inputs=inputs,
    selected_pile_types=pile_types,
    edge_dist=0.4,                    # meters
    optimization_method="cost_efficiency",  # or "performance", "balanced"
    output_dir="./results",
    log_callback=print
)

# 4. Process comprehensive results
if results['success']:
    print(f"✅ AI optimization completed successfully!")
    print(f"📊 Groups processed: {results['summary']['total_groups']}")
    print(f"🏗️ Total piles required: {results['summary']['total_piles']}")
    print(f"🎯 Pile types used: {list(results['optimization']['pile_type_usage'].keys())}")
    print(f"📄 Professional DXF: {results['dxf_file']}")
    print(f"💾 Results directory: {results['output_dir']}")
else:
    print(f"❌ Optimization failed: {results['errors']}")
```

#### Advanced Workflow Coordination

```python
from fdn_agent.pile_estimation.pile_workflow_coordinator import (
    coordinate_pile_estimation_with_multi_type_optimization
)

# Advanced optimization with expert parameters
results = coordinate_pile_estimation_with_multi_type_optimization(
    excel_inputs=inputs,
    selected_pile_types=pile_types,
    edge_dist=0.5,
    optimization_method="balanced",
    output_dir="./advanced_results",
    visualize=True,
    # Expert configuration parameters
    grouping_min_threshold=0.5,      # Minimum clustering threshold
    grouping_max_threshold=2.0,      # Maximum clustering threshold
    use_multiprocessing=True,        # Enable parallel processing
    safety_factor=1.25,              # Design safety factor
    use_sub_clustering=True          # Enable load-based sub-clustering
)
```

## Key Features

### 1. AI Pile Type Pre-Selection Engine

The AI system intelligently evaluates multiple factors:
- Load magnitude and distribution patterns
- Soil conditions and bearing capacity requirements
- Construction constraints and site accessibility
- Cost-effectiveness and material availability
- Structural performance and safety factors

### 2. NSGA-III Multi-Objective Optimization

Advanced genetic algorithm optimization providing:
- Globally optimal solutions for complex design problems
- Multi-objective balancing of cost, performance, and constructability
- Constraint handling for structural and geometric requirements
- Real-time progress tracking and convergence monitoring

### 3. Enhanced Logging Framework

Comprehensive logging with specialized engineering patterns:
- Function entry/exit tracking for debugging
- Performance metrics with engineering units
- Validation results with pass/fail status
- Algorithm step tracking for complex workflows
- Error context logging with detailed diagnostics

### 4. Professional Visualization

AutoCAD DXF generation includes:
- Detailed pile layouts with precise dimensions
- Layer organization for different design elements
- Comprehensive annotations and construction notes
- Pre-selection analysis visualization
- Final optimized results with performance metrics

## Integration Points

The Foundation Agent integrates with:
- **Excel Data Sources**: Bidirectional data exchange with project spreadsheets
- **CAD Software**: AutoCAD, DraftSight, FreeCAD for professional visualization
- **Structural Analysis**: SAFE, ETABS integration for comprehensive analysis
- **Build FEM Module**: Structural analysis and load calculations
- **Initialization Module**: Project setup and data validation