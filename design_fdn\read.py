from math import pi
import os
import sqlite3
from typing import Dict, List, Optional, Callable

import pandas as pd
import numpy as np
import pyodbc


def read_safe_mdbs(safe_mdbs, file_paths, log_callback: Optional[Callable] = None):
    """Read SAFE database with optimized table reading and type conversion."""
    file_extension = os.path.splitext(file_paths.ResultMdbSAFE)[1].lower()
    is_sqlite = file_extension == '.sqlite'
    
    # Flag to track if SAFE22 tables are detected
    safe22_detected = False
    
    # Establish connection
    if is_sqlite:
        conn = sqlite3.connect(file_paths.ResultMdbSAFE)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        table_names = {row[0] for row in cursor.fetchall()}
    else:
        conn = pyodbc.connect(
            f'Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={file_paths.ResultMdbSAFE};')
        cursor = conn.cursor()
        table_names = {row.table_name for row in cursor.tables(tableType='TABLE')}
    
    # Check for SAFE22-specific tables to determine the software version
    safe22_tables = {'Joint Displacements', 'Frame Assignments - Local Axes'}
    if safe22_tables & table_names:  # Intersection check for any common elements
        safe22_detected = True
        if log_callback:
            log_callback("SAFE22 tables detected - coordinate transformations will use SAFE22 system")
    
    # Define table mappings with column renaming and numeric conversions
    table_config = {
        'Object Geometry - Point Coordinates': {
            'attr': 'PointCoord',
            'numeric_cols': ['GlobalX', 'GlobalY', 'GlobalZ'] if is_sqlite else []
        },
        'Object Geometry - Lines 01 - General': {
            'attr': 'Line',
            'numeric_cols': []
        },
        'Column Local Axes': {
            'attr': 'ColumnLocalAxes',
            'numeric_cols': ['Angle'] if is_sqlite else []
        },
        'Element Forces - Columns And Braces': {
            'attr': 'ElementForcesColumnsAndBraces',
            'numeric_cols': []
        },
        'Nodal Displacements': {
            'attr': 'NodalDisplacements',
            'numeric_cols': ['Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz'] if is_sqlite else []
        },
        'Point Object Connectivity': {
            'attr': 'PointCoord',
            'columns': ['UniqueName', 'X', 'Y', 'Z', 'IsSpecial'],
            'rename': {
                'UniqueName': 'Point',
                'X': 'GlobalX',
                'Y': 'GlobalY',
                'Z': 'GlobalZ',
                'IsSpecial': 'SpecialPt'
            },
            'numeric_cols': ['GlobalX', 'GlobalY', 'GlobalZ'] if is_sqlite else []
        },
        'Frame Assignments - Local Axes': {
            'attr': 'ColumnLocalAxes',
            'columns': ['UniqueName', 'Angle'],
            'rename': {'UniqueName': 'Line'},
            'numeric_cols': ['Angle'] if is_sqlite else []
        },
        'Joint Displacements': {
            'attr': 'NodalDisplacements',
            'columns': ['Label', 'Unique Name', 'Output Case', 'Case Type', 'Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz'],
            'rename': {
                'Label': 'Node',
                'Unique Name': 'Point',
                'Output Case': 'OutputCase',
                'Case Type': 'CaseType'
            },
            'numeric_cols': ['Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz'] if is_sqlite else [],
            'post_process': lambda df: df.assign(Node=df['Point'])
        },
        'Element Forces - Columns': {
            'attr': 'ElementForcesColumnsAndBraces',
            'columns': ['Unique Name', 'Station', 'Output Case', 'Case Type', 'P', 'V2', 'V3', 'T', 'M2', 'M3',
                       'Element', 'Elem Station'],
            'rename': {
                'Unique Name': 'Line',
                'Output Case': 'OutputCase',
                'Case Type': 'CaseType',
                'Element': 'LineElem',
                'Elem Station': 'ElemStation'
            },
            'numeric_cols': ['P', 'V2', 'V3', 'T', 'M2', 'M3', 'Station', 'ElemStation'],
            'post_process': 'station_filter'
        }
    }
    
    # Helper function to read and process tables
    def read_table(table_name: str, config: Dict):
        if table_name not in table_names:
            return
        
        # Format table name for SQL
        formatted_name = f'"{table_name}"' if is_sqlite else f'[{table_name}]'
        df = pd.read_sql(f'SELECT * FROM {formatted_name}', conn)
        
        # Select columns if specified
        if 'columns' in config:
            df = df[config['columns']]
        
        # Rename columns
        if 'rename' in config:
            df.rename(columns=config['rename'], inplace=True)
        
        # Convert to numeric
        for col in config.get('numeric_cols', []):
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Apply post-processing
        if 'post_process' in config:
            if config['post_process'] == 'station_filter':
                df = filter_station_data(df)
            elif callable(config['post_process']):
                df = config['post_process'](df)
        
        # Set attribute
        setattr(safe_mdbs, config['attr'], df)
    
    # Process all tables
    for table_name, config in table_config.items():
        read_table(table_name, config)
    
    # Set SAFE version in excel_inputs based on detected tables
    # This tracks whether SAFE22 to SAFE16 conversion was performed
    if safe22_detected:
        safe_mdbs.safe_version = "SAFE22"
    else:
        safe_mdbs.safe_version = "SAFE16"
    
    conn.close()
    
    if log_callback:
        db_type = "SQLite" if is_sqlite else "MDB"
        version = "SAFE22" if safe22_detected else "SAFE16"
        log_callback(f'Read SAFE Result ({db_type}) - Detected version: {version}')
    
    return safe_mdbs


def filter_station_data(df: pd.DataFrame) -> pd.DataFrame:
    """Filter element forces to keep only min and max station values."""
    # Group by Line and OutputCase, get indices for min and max stations
    grouped = df.groupby(['Line', 'OutputCase'])['Station']
    min_idx = grouped.idxmin()
    max_idx = grouped.idxmax()
    
    # Keep only min and max station rows
    df_filtered = df.loc[pd.Index(min_idx).union(pd.Index(max_idx))].copy()
    
    # Swap station values for proper ordering
    station_bounds = grouped.agg(['min', 'max'])
    df_filtered = df_filtered.join(station_bounds, on=['Line', 'OutputCase'])
    
    mask_min = df_filtered['Station'] == df_filtered['min']
    mask_max = df_filtered['Station'] == df_filtered['max']
    
    df_filtered.loc[mask_min, 'Station'] = df_filtered.loc[mask_min, 'max']
    df_filtered.loc[mask_max, 'Station'] = df_filtered.loc[mask_max, 'min']
    
    return df_filtered[df.columns]  # Return only original columns


def read_excel_outputs(excel_outputs, file_paths, log_callback: Optional[Callable] = None):
    """Read all Excel output files efficiently."""
    file_mappings = [
        ('PileULS', file_paths.ExcelOutputPileULS),
        ('PileSLS', file_paths.ExcelOutputPileSLS),
        ('PileLocalXY', file_paths.ExcelOutputPileLocalXY),
        ('Settlement', file_paths.ExcelOutputSettlement),
        ('LateralDisplacement', file_paths.ExcelOutputLateralDisplacement)
    ]
    
    for attr_name, file_path in file_mappings:
        setattr(excel_outputs, attr_name, pd.read_csv(file_path))
    
    if log_callback:
        log_callback('Read Excel Outputs')
    
    return excel_outputs


def read_pile_base_load(df_pile: pd.DataFrame, load_patterns: List[str], 
                       df_load_pattern: pd.DataFrame, df_section_h: pd.DataFrame, 
                       df_element_forces: pd.DataFrame, log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """Calculate pile base loads with optimized vectorized operations."""
    
    # Create result DataFrame
    df_init_fz = pd.DataFrame(index=df_pile['Pile Mark'], columns=load_patterns, dtype=float)
    df_init_fz.index.name = 'Pile'
    df_init_fz.fillna(0, inplace=True)
    
    # Vectorized pile data calculations
    df_pile_calc = df_pile.copy()
    df_pile_calc['length_cl2tl'] = (df_pile_calc['Pile Cap Bottom Level (mPD)'].astype(float) - 
                                    df_pile_calc['Target Level (mPD)'].astype(float))    
    df_pile_calc['length_t12fl'] = (df_pile_calc['Target Level (mPD)'].astype(float) - 
                                    df_pile_calc['Founding Level (mPD)'].astype(float))
    df_pile_calc['dia_shaft'] = df_pile_calc['Pile Shaft Diameter (m)'].astype(float)
    
    if 'Pile Socket Diameter (m)' in df_pile_calc.columns:
        df_pile_calc['dia_socket'] = df_pile_calc['Pile Socket Diameter (m)'].astype(float)
    else:
        # Default to shaft diameter if socket diameter is not available
        df_pile_calc['dia_socket'] = df_pile_calc['dia_shaft']
    
    # Calculate volumes
    df_pile_calc['volume'] = (pi/4 * df_pile_calc['dia_shaft']**2 * df_pile_calc['length_cl2tl'] + 
                             pi/4 * df_pile_calc['dia_socket']**2 * df_pile_calc['length_t12fl'])
    df_pile_calc['total_length'] = df_pile_calc['length_cl2tl'] + df_pile_calc['length_t12fl']
    
    # Create point names
    df_pile_calc['point_name'] = df_pile_calc.apply(
        lambda x: f"{x['Pile Mark']}_T" if x['Pile Type'] == 'MP' else f"{x['Pile Mark']}_B", 
        axis=1
    )
    # Create lookups
    load_pattern_types = df_load_pattern.set_index('LoadPat (Text)')['Load Type'].to_dict()
    section_areas = (df_section_h.set_index('Steel Section')['A (cm2)'] / 10000).to_dict()
    
    # Process each load pattern
    for load_pattern in load_patterns:
        if pd.isna(load_pattern):
            continue
        
        load_type = load_pattern_types.get(load_pattern)
        
        if load_type == 'SELF WEIGHT':
            # Vectorized self-weight calculations
            mask_bp = df_pile_calc['Pile Type'] == 'BP'
            mask_mp = df_pile_calc['Pile Type'] == 'MP'
            mask_steel = df_pile_calc['Pile Type'].isin(['SHP', 'DHP'])
            
            # Use pile marks for indexing df_init_fz instead of boolean masks
            bp_piles = df_pile_calc.loc[mask_bp, 'Pile Mark']
            mp_piles = df_pile_calc.loc[mask_mp, 'Pile Mark']
            
            if not bp_piles.empty:
                df_init_fz.loc[bp_piles, load_pattern] = df_pile_calc.loc[mask_bp, 'volume'].values * 24.5
            if not mp_piles.empty:
                df_init_fz.loc[mp_piles, load_pattern] = df_pile_calc.loc[mask_mp, 'volume'].values * 24.5
            
            # Steel piles
            if mask_steel.any():
                steel_piles = df_pile_calc[mask_steel]
                steel_pile_marks = steel_piles['Pile Mark']
                steel_weights = steel_piles.apply(
                    lambda x: section_areas.get(x['Pile Section'], 0) * x['total_length'] * 78.5,
                    axis=1
                )
                df_init_fz.loc[steel_pile_marks, load_pattern] = steel_weights.values
        else:
            # Get forces from element forces DataFrame
            forces_filtered = df_element_forces[
                (df_element_forces['OutputCase'] == load_pattern) &
                (df_element_forces['Line'].isin(df_pile_calc['point_name']) &
                (df_element_forces['Station'] == 0))
            ]
            
            # Create a mapping from Line to P value
            forces_dict = forces_filtered.groupby('Line')['P'].first().to_dict()
            
            # Map forces to piles
            for idx, row in df_pile_calc.iterrows():
                pile_mark = row['Pile Mark']
                point_name = row['point_name']
                if point_name in forces_dict:
                    df_init_fz.loc[pile_mark, load_pattern] = -float(forces_dict[point_name])
    
    if log_callback:
        log_callback('Read Pile Base Load')
    
    return df_init_fz
