"""Process load combinations, patterns, and cases for SAFE16 format.

Creates ULS/SLS combinations, envelope combinations, and wind-specific envelopes
from Excel input data with proper design flags and scaling factors."""

import numpy as np
import pandas as pd
from datetime import datetime

from build_fem import functions


def _write_load_pattern_safe16(excel_inputs, safe16_dfs):
    """Convert load pattern definitions from Excel to SAFE16 format."""
    load_pat_input_df = excel_inputs.LoadPat

    # Select only the columns needed for SAFE16 load pattern definition
    relevant_columns = ['LoadPat (Text)', 'Type (Text)', 'SelfWtMult (Unitless)']
    source_data_df = load_pat_input_df[relevant_columns].copy()

    # Create DataFrame with SAFE16 column structure while preserving data values
    data_to_add_to_safe16 = pd.DataFrame(
        source_data_df.values,
        columns=safe16_dfs.LoadPatterns.columns,
        index=source_data_df.index  # Maintain index consistency
    )
    
    # Append new load patterns to existing SAFE16 structure
    safe16_dfs.LoadPatterns = pd.concat(
        [safe16_dfs.LoadPatterns, data_to_add_to_safe16], ignore_index=True
    )
    return safe16_dfs


def _write_load_case_safe16(excel_inputs, safe16_dfs):
    """Convert load case matrix from Excel to SAFE16 format with scale factors."""
    load_pattern_df = excel_inputs.LoadPat.copy()
    load_case_df = excel_inputs.LoadCase.copy()

    # Get list of load pattern names for column identification
    load_patterns = load_pattern_df['LoadPat (Text)'].values

    # Find where load pattern columns start in the load case matrix
    search_columns = load_patterns
    # Create a boolean Series indicating which columns match the search columns
    matches = [col in search_columns for col in load_case_df.columns]

    # Use idxmax() method to find the index of the first maximum value in the boolean Series
    # This identifies the starting column index for load pattern data
    column_start = pd.Series(matches).idxmax()

    # Extract load case names and reshape for matrix operations
    load_cases = load_case_df['LoadCase (Text)'].values.reshape(-1, 1)
    
    # Extract the matrix of scale factors (load cases x load patterns)
    data = load_case_df.iloc[:, column_start:]
    columns = data.columns
    
    # Create SAFE16 format DataFrame using numpy array operations for efficiency
    # np.repeat: repeats each load case name for each load pattern column
    # np.tile: tiles the load pattern names for each load case row
    # flatten(): converts matrix to 1D array for DataFrame creation
    safe16_append_df = pd.DataFrame({
        ('TABLE:  Load Cases 06 - Loads Applied', 'LoadCase', 'Text'): np.repeat(load_cases,
                                                                                 data.shape[1]).flatten(),
        ('TABLE:  Load Cases 06 - Loads Applied', 'LoadPat', 'Text'): np.tile(columns, data.shape[0]),
        ('TABLE:  Load Cases 06 - Loads Applied', 'SF', 'Unitless'): data.values.flatten()
    })
    
    # Filter out zero scale factors to optimize SAFE16 file size and processing
    condition = safe16_append_df[('TABLE:  Load Cases 06 - Loads Applied', 'SF', 'Unitless')] != 0
    safe16_append_df = safe16_append_df[condition]
    
    # Append to existing SAFE16 load case applications
    safe16_dfs.LoadCases06LoadsApplied = pd.concat([safe16_dfs.LoadCases06LoadsApplied, safe16_append_df],
                                                   ignore_index=True)

    return safe16_dfs


def _process_primary_load_combinations_safe16(load_comb_df, load_case_df, target_columns, safe16_dfs):
    """Process primary load combinations from Excel input to SAFE16 format.
    Handles ULS/SLS classification and creates individual combination entries."""
    # Get load case names for column identification
    load_case_names = load_case_df['LoadCase (Text)'].values

    # Find the index where load case columns start in the combination matrix
    matches = [col in load_case_names for col in load_comb_df.columns]
    if not any(matches):
        # Return empty DataFrame if no load case columns found
        return pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    column_start = pd.Series(matches).idxmax()

    # Process each load combination row
    new_data = []
    for i in range(load_comb_df.shape[0]):
        # Determine design settings based on ULS/SLS flag
        # This function returns appropriate Yes/No values for design flags
        design_settings = functions._get_limit_state_settings(load_comb_df.loc[i, 'ULS/SLS'])

        # Extract combination properties
        load_comb = load_comb_df.loc[i, 'Combo (Text)']
        load_type = load_comb_df.loc[i, 'Type (Text)']

        # Process each load case column within this combination
        for j in range(column_start, load_comb_df.columns.size):
            load = load_comb_df.columns[j]  # Load case name
            scale_factor = load_comb_df.iloc[i, j]  # Scale factor for this load case

            # Skip zero scale factors to optimize combination definitions
            if scale_factor != 0:
                # Create a new combination entry with all required properties
                # Column order must match target_columns structure
                new_data.append([
                    load_comb,  # Combination name
                    load,       # Load case name
                    scale_factor,  # Scale factor
                    load_type,  # Combination type
                    design_settings['strength'],      # DSStrength flag
                    design_settings['initialization'], # DSServInit flag  
                    design_settings['norm'],          # DSServNorm flag
                    design_settings['long'],          # DSServLong flag
                    'No'        # AutoDesign flag
                ])

    # Create DataFrame with proper SAFE16 MultiIndex column structure
    if new_data:
        df_to_append = pd.DataFrame(new_data, columns=pd.MultiIndex.from_tuples(target_columns))

        # Add to SAFE16 load combinations
        if not df_to_append.empty:  # Additional safety check
            if safe16_dfs.LoadCombinations.empty:
                safe16_dfs.LoadCombinations = df_to_append.copy()
            else:
                safe16_dfs.LoadCombinations = pd.concat(
                    [safe16_dfs.LoadCombinations, df_to_append], ignore_index=True
                )
        return safe16_dfs, df_to_append

    # Return empty result if no valid combinations processed
    return safe16_dfs, pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))


def _create_uls_envelope_safe16(load_comb_df, empty_template, safe16_dfs):
    """Create Ultimate Limit State envelope combination for SAFE16.
    Includes all ULS combinations for strength design optimization."""
    uls_envelope = empty_template.copy()

    # Select ULS load combinations only
    condition = load_comb_df['ULS/SLS'] == 'ULS'
    if not any(condition):
        return

    # Set envelope properties for ULS (Ultimate Limit State) design
    # Load: The combinations to be included in the envelope
    uls_envelope[('TABLE:  Load Combinations', 'Load', 'Text')] = load_comb_df.loc[condition, 'Combo (Text)'].values
    # Combo: The name of the envelope combination
    uls_envelope[('TABLE:  Load Combinations', 'Combo', 'Text')] = '_Envelope_ULS'
    # SF: Scale factor is always 1.0 for envelope combinations
    uls_envelope[('TABLE:  Load Combinations', 'SF', 'Unitless')] = 1
    # Type: Specifies this is an envelope combination
    uls_envelope[('TABLE:  Load Combinations', 'Type', 'Text')] = 'Envelope'
    
    # Design flags for Ultimate Limit State (strength design)
    uls_envelope[('TABLE:  Load Combinations', 'DSStrength', 'Yes/No')] = 'Yes'  # Enable strength design
    uls_envelope[('TABLE:  Load Combinations', 'DSServInit', 'Yes/No')] = 'No'   # Disable serviceability checks
    uls_envelope[('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No')] = 'No'   # Disable serviceability checks
    uls_envelope[('TABLE:  Load Combinations', 'DSServLong', 'Yes/No')] = 'No'   # Disable serviceability checks
    uls_envelope[('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No')] = 'No'   # Manual envelope control

    # Add ULS envelope to SAFE16 load combinations
    safe16_dfs.LoadCombinations = pd.concat(
        [safe16_dfs.LoadCombinations, uls_envelope], ignore_index=True
    )
    return safe16_dfs


def _create_sls_envelope_safe16(safe_load_combs, empty_template, safe16_dfs):
    """Create Serviceability Limit State envelope combination for SAFE16.
    Includes all SLS combinations for serviceability design checks."""
    # Find combinations containing '_SLS' in their names
    condition = safe_load_combs[('TABLE:  Load Combinations', 'Combo', 'Text')].str.contains('_SLS')
    if not condition.any():
        return

    # Extract unique SLS combination names
    sls_combos = safe_load_combs.loc[
        condition, ('TABLE:  Load Combinations', 'Combo', 'Text')].drop_duplicates().tolist()

    if not sls_combos:
        return

    # Define envelope data structure for SLS combinations
    sls_envelope_data = {
        ('TABLE:  Load Combinations', 'Load', 'Text'): sls_combos,
        ('TABLE:  Load Combinations', 'Combo', 'Text'): '_Envelope_SLS',
        ('TABLE:  Load Combinations', 'SF', 'Unitless'): 1,
        ('TABLE:  Load Combinations', 'Type', 'Text'): 'Envelope',
        # Design flags for Serviceability Limit State
        ('TABLE:  Load Combinations', 'DSStrength', 'Yes/No'): 'No',   # Not for strength design
        ('TABLE:  Load Combinations', 'DSServInit', 'Yes/No'): 'Yes',  # Initial serviceability
        ('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No'): 'Yes',  # Normal serviceability
        ('TABLE:  Load Combinations', 'DSServLong', 'Yes/No'): 'Yes',  # Long-term serviceability
        ('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No'): 'No'    # Manual envelope control
    }

    # Ensure all data columns have the same length (one row per SLS combination)
    num_combos = len(sls_combos)
    for key, value in sls_envelope_data.items():
        if not isinstance(value, list):
            sls_envelope_data[key] = [value] * num_combos

    # Create SLS envelope DataFrame
    sls_envelope = pd.DataFrame(sls_envelope_data)

    # Add SLS envelope to SAFE16 load combinations
    safe16_dfs.LoadCombinations = pd.concat(
        [safe16_dfs.LoadCombinations, sls_envelope], ignore_index=True
    )
    return safe16_dfs


def _create_wind_envelopes_safe16(safe_load_combs, empty_template, wind_patterns, safe16_dfs):
    """Create wind-specific and non-wind envelope combinations for SAFE16.
    Creates four separate envelopes for ULS/SLS with and without wind loads."""
    # Create ULS wind envelopes (combinations containing wind loads)
    _create_wind_specific_envelope_safe16(
        safe_load_combs, empty_template, wind_patterns,
        '_ULS', '_Envelope_ULS_Wind', True, safe16_dfs
    )

    # Create ULS non-wind envelopes (combinations without wind loads)
    _create_non_wind_envelope_safe16(
        safe_load_combs, empty_template, wind_patterns,
        '_ULS', '_Envelope_ULS_NoWind', True, safe16_dfs
    )

    # Create SLS wind envelopes (combinations containing wind loads)
    _create_wind_specific_envelope_safe16(
        safe_load_combs, empty_template, wind_patterns,
        '_SLS', '_Envelope_SLS_Wind', False, safe16_dfs
    )

    # Create SLS non-wind envelopes (combinations without wind loads)
    _create_non_wind_envelope_safe16(
        safe_load_combs, empty_template, wind_patterns,
        '_SLS', '_Envelope_SLS_NoWind', False, safe16_dfs
    )
    return safe16_dfs


def _create_wind_specific_envelope_safe16(safe_load_combs, empty_template, wind_patterns,
                                          suffix, envelope_name, is_uls, safe16_dfs):
    """Create envelope combination that includes only combinations with wind loads."""
    # Find combinations that contain wind loads AND match the limit state suffix
    condition = (
            safe_load_combs[('TABLE:  Load Combinations', 'Load', 'Text')].isin(wind_patterns) &
            safe_load_combs[('TABLE:  Load Combinations', 'Combo', 'Text')].str.contains(suffix)
    )

    # Extract unique combination names that contain wind loads
    wind_combos = safe_load_combs.loc[
        condition, [('TABLE:  Load Combinations', 'Combo', 'Text')]
    ].drop_duplicates()[('TABLE:  Load Combinations', 'Combo', 'Text')].tolist()

    # Define base envelope data structure
    envelope_data = {
        ('TABLE:  Load Combinations', 'Load', 'Text'): wind_combos,
        ('TABLE:  Load Combinations', 'Combo', 'Text'): envelope_name,
        ('TABLE:  Load Combinations', 'SF', 'Unitless'): 1,
        ('TABLE:  Load Combinations', 'Type', 'Text'): 'Envelope',
        ('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No'): 'No'
    }

    # Set design flags based on limit state type
    if is_uls:
        # Ultimate Limit State: Enable strength design only
        envelope_data.update({
            ('TABLE:  Load Combinations', 'DSStrength', 'Yes/No'): 'Yes',
            ('TABLE:  Load Combinations', 'DSServInit', 'Yes/No'): 'No',
            ('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No'): 'No',
            ('TABLE:  Load Combinations', 'DSServLong', 'Yes/No'): 'No'
        })
    else:
        # Serviceability Limit State: Enable all serviceability checks
        envelope_data.update({
            ('TABLE:  Load Combinations', 'DSStrength', 'Yes/No'): 'No',
            ('TABLE:  Load Combinations', 'DSServInit', 'Yes/No'): 'Yes',
            ('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No'): 'Yes',
            ('TABLE:  Load Combinations', 'DSServLong', 'Yes/No'): 'Yes'
        })

    # Ensure all data columns have the same length (one row per wind combination)
    num_combos = len(wind_combos)
    for key, value in envelope_data.items():
        if not isinstance(value, list):
            envelope_data[key] = [value] * num_combos

    # Create wind-specific envelope DataFrame
    envelope = pd.DataFrame(envelope_data)

    # Add wind envelope to SAFE16 load combinations
    safe16_dfs.LoadCombinations = pd.concat(
        [safe16_dfs.LoadCombinations, envelope], ignore_index=True
    )
    return safe16_dfs


def _create_non_wind_envelope_safe16(safe_load_combs, empty_template, wind_patterns,
                                     suffix, envelope_name, is_uls, safe16_dfs):
    """Create envelope combination that excludes combinations with wind loads."""
    # Find all combinations with the specified suffix (ULS or SLS)
    all_condition = safe_load_combs[
        ('TABLE:  Load Combinations', 'Combo', 'Text')
    ].str.contains(suffix)

    # Get all combination names with the suffix
    all_combos = safe_load_combs.loc[
        all_condition, [('TABLE:  Load Combinations', 'Combo', 'Text')]
    ].drop_duplicates().reset_index(drop=True)

    # Find combinations that contain wind patterns
    wind_condition = (
            safe_load_combs[('TABLE:  Load Combinations', 'Load', 'Text')].isin(wind_patterns) &
            safe_load_combs[('TABLE:  Load Combinations', 'Combo', 'Text')].str.contains(suffix)
    )

    wind_combos = safe_load_combs.loc[
        wind_condition, [('TABLE:  Load Combinations', 'Combo', 'Text')]
    ].drop_duplicates().reset_index(drop=True)

    # Use numpy set operations to filter out wind combinations from all combinations
    # This gives us combinations that do NOT contain wind loads
    all_values = np.array(all_combos.values)
    wind_values = np.array(wind_combos.values)
    non_wind_values = all_values[~np.isin(all_values, wind_values)]

    # Create non-wind envelope using the template structure
    envelope = empty_template.copy()
    envelope[('TABLE:  Load Combinations', 'Load', 'Text')] = non_wind_values
    envelope[('TABLE:  Load Combinations', 'Combo', 'Text')] = envelope_name
    envelope[('TABLE:  Load Combinations', 'SF', 'Unitless')] = 1
    envelope[('TABLE:  Load Combinations', 'Type', 'Text')] = 'Envelope'

    # Set design flags based on limit state type (ULS vs SLS)
    if is_uls:
        # Ultimate Limit State: Enable strength design only
        envelope[('TABLE:  Load Combinations', 'DSStrength', 'Yes/No')] = 'Yes'
        envelope[('TABLE:  Load Combinations', 'DSServInit', 'Yes/No')] = 'No'
        envelope[('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No')] = 'No'
        envelope[('TABLE:  Load Combinations', 'DSServLong', 'Yes/No')] = 'No'
    else:
        # Serviceability Limit State: Enable all serviceability checks
        envelope[('TABLE:  Load Combinations', 'DSStrength', 'Yes/No')] = 'No'
        envelope[('TABLE:  Load Combinations', 'DSServInit', 'Yes/No')] = 'Yes'
        envelope[('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No')] = 'Yes'
        envelope[('TABLE:  Load Combinations', 'DSServLong', 'Yes/No')] = 'Yes'

    envelope[('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No')] = 'No'

    # Add non-wind envelope to SAFE16 load combinations
    safe16_dfs.LoadCombinations = pd.concat(
        [safe16_dfs.LoadCombinations, envelope], ignore_index=True
    )
    return safe16_dfs


def _create_settlement_envelope_safe16(load_comb_df, empty_template, safe16_dfs):
    """Create settlement envelope combination for differential settlement analysis."""
    # Check if there are any settlement combinations (identified by '_SETT' suffix)
    condition = load_comb_df['Combo (Text)'].str.contains('_SETT')
    if not any(condition):
        return

    # Create settlement envelope using the template structure
    settlement_envelope = empty_template.copy()
    settlement_envelope[('TABLE:  Load Combinations', 'Load', 'Text')] = load_comb_df.loc[condition, 'Combo (Text)']
    settlement_envelope[('TABLE:  Load Combinations', 'Combo', 'Text')] = '_Envelope_SETT'
    settlement_envelope[('TABLE:  Load Combinations', 'SF', 'Unitless')] = 1
    settlement_envelope[('TABLE:  Load Combinations', 'Type', 'Text')] = 'Envelope'
    
    # Design flags for settlement analysis (serviceability-focused)
    settlement_envelope[('TABLE:  Load Combinations', 'DSStrength', 'Yes/No')] = 'No'   # Not for strength design
    settlement_envelope[('TABLE:  Load Combinations', 'DSServInit', 'Yes/No')] = 'Yes'  # Initial serviceability
    settlement_envelope[('TABLE:  Load Combinations', 'DSServNorm', 'Yes/No')] = 'Yes'  # Normal serviceability
    settlement_envelope[('TABLE:  Load Combinations', 'DSServLong', 'Yes/No')] = 'Yes'  # Long-term serviceability
    settlement_envelope[('TABLE:  Load Combinations', 'AutoDesign', 'Yes/No')] = 'No'   # Manual envelope control

    # Add settlement envelope to SAFE16 load combinations
    safe16_dfs.LoadCombinations = pd.concat(
        [safe16_dfs.LoadCombinations, settlement_envelope], ignore_index=True
    )
    return safe16_dfs


def _write_load_comb_to_safe16(excel_inputs, safe16_dfs):
    """Main coordination function to process all load combinations for SAFE16.
    Handles complete workflow from Excel inputs to SAFE16 format with automatic envelope generation."""
    # Create deep copies to avoid modifying original input data
    load_pattern_df = excel_inputs.LoadPat.copy()
    load_case_df = excel_inputs.LoadCase.copy()
    load_comb_df = excel_inputs.LoadComb.copy()

    # Get target column structure for load combinations from existing SAFE16 structure
    target_columns = safe16_dfs.LoadCombinations.columns.tolist()
    empty_load_comb_df = pd.DataFrame(columns=pd.MultiIndex.from_tuples(target_columns))

    # Process primary load combinations from Excel input
    # This creates the basic user-defined combinations with proper design flags
    safe16_dfs, safe_load_combs = _process_primary_load_combinations_safe16(
        load_comb_df, load_case_df, target_columns, safe16_dfs)

    # Identify wind load patterns for wind-specific envelope creation
    # Uses utility function to detect wind patterns by naming convention
    wind_load_patterns = functions._get_wind_load_patterns(load_pattern_df)

    # Create standard envelope combinations for design optimization
    safe16_dfs = _create_uls_envelope_safe16(load_comb_df, empty_load_comb_df, safe16_dfs)
    safe16_dfs = _create_sls_envelope_safe16(safe_load_combs, empty_load_comb_df, safe16_dfs)

    # Create wind-specific envelopes if wind patterns are detected
    # This provides separate design envelopes for wind vs. non-wind conditions
    if len(wind_load_patterns) > 0:
        safe16_dfs = _create_wind_envelopes_safe16(safe_load_combs, empty_load_comb_df, wind_load_patterns, safe16_dfs)

    # Create settlement envelope for differential settlement analysis
    # Only created if settlement combinations (with '_SETT' suffix) exist
    safe16_dfs = _create_settlement_envelope_safe16(load_comb_df, empty_load_comb_df, safe16_dfs)
    return safe16_dfs


def _write_general_load_cases_safe16(excel_inputs, safe16_dfs):
    """Process and add general load case properties to SAFE16 format."""

    load_case_df = excel_inputs.LoadCase.copy()
    
    # Extract relevant columns for general load case properties
    general_case_data = load_case_df[[
        'LoadCase (Text)', 'Type (Text)', 'DesignOpt (Text)', 'DesignType (Text)'
    ]].copy()

    if not general_case_data.empty:
        # Get column structure from target DataFrame to ensure compatibility
        target_columns = safe16_dfs.LoadCases01General.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_to_append = pd.DataFrame(
            general_case_data.values,
            columns=pd.MultiIndex.from_tuples(target_columns)
        )

        # Append to existing SAFE16 general load case data
        safe16_dfs.LoadCases01General = pd.concat(
            [safe16_dfs.LoadCases01General, df_to_append],
            ignore_index=True
        )
    return safe16_dfs


def _write_static_load_cases_safe16(excel_inputs, safe16_dfs):
    """Process and add static load case properties to SAFE16 format."""
    load_case_df = excel_inputs.LoadCase.copy()
    
    # Extract relevant columns for static load case properties
    static_case_data = load_case_df[[
        'LoadCase (Text)', 'InitialCond (Text)', 'AType (Text)'
    ]].copy()

    if not static_case_data.empty:
        # Get column structure from target DataFrame to ensure compatibility
        target_columns = safe16_dfs.LoadCases02Static.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_to_append = pd.DataFrame(
            static_case_data.values,
            columns=pd.MultiIndex.from_tuples(target_columns)
        )

        # Append to existing SAFE16 static load case data
        safe16_dfs.LoadCases02Static = pd.concat(
            [safe16_dfs.LoadCases02Static, df_to_append],
            ignore_index=True
        )
    return safe16_dfs
