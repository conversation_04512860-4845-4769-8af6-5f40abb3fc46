﻿"""
Structural element type definitions
"""

from typing import Tuple, List, TypedDict
from .basic_types import Point2D, Point3D

# Structural element types
ColumnData = Tuple[str, float, float, float]  # (name, x, y, base_level)
WallData = Tuple[str, List[Point2D], float]   # (name, points_list, base_level)
ElementPoints = List[Point3D]

class GroupElements(TypedDict):
    """Dictionary containing grouped structural elements."""
    columns: List[ColumnData]
    walls: List[WallData] 

