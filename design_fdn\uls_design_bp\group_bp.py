"""
Pile Group File Processing Module for Ultimate Limit State (ULS) Design

This module provides comprehensive file processing capabilities for bored pile (BP) 
group analysis results as part of Ultimate Limit State (ULS) design verification 
in foundation engineering. It handles the consolidation and management of pile 
segment analysis outputs generated during detailed pile design calculations.

The module performs file processing operations for:
- Pile segment PNG image combination for visual design verification
- Pile segment Excel report consolidation for numerical analysis results
- Multiprocessing-based batch operations for efficient large-scale processing
- File cleanup and workspace management for design workflow optimization

Key Features:
- Automated pile segment file identification and grouping by pile mark
- Intelligent segment ordering (Top → Numeric → Bottom) following design conventions
- Parallel processing capabilities for handling multiple pile groups simultaneously
- Robust error handling with detailed logging and progress tracking
- File deletion management with retry mechanisms for locked files
- Image composition with segment labels for design documentation

Author: Foundation Design Automation Team
Version: 1.0
"""

import glob
import multiprocessing
import os
import re
import time  # Added for retry delay
from collections import defaultdict
from typing import List, Tuple, Dict, Any, Optional, Callable, Union

import pandas as pd
from PIL import Image, ImageDraw, ImageFont


def _get_segment_sort_key(segment_id_str: str) -> Tuple[Union[int, float], Union[str, float]]:
    """
    Generate sort key for pile segment identifiers following foundation design conventions.
    
    This function creates a sorting hierarchy that follows standard pile design practices
    where segments are ordered from top to bottom of the pile. The sorting ensures
    proper sequence for design review and construction documentation.
    
    Sorting Priority:
    1. 'T' (Top) - Priority -1 (always first)
    2. Numeric segments (0.5, 1, 2, etc.) - Priority 0 (middle, numerical order)
    3. Other alphabetic (A, MIDDLE, etc.) - Priority 1 (lexicographic order)
    4. 'B' (Bottom) - Priority infinity (always last)
    """
    # Handle Top segment - highest priority (first in order)
    if segment_id_str.upper() == 'T':
        return (-1, '')  # Sort 'T' (Top) first
    
    # Handle Bottom segment - lowest priority (last in order)
    if segment_id_str.upper() == 'B':
        return (float('inf'), '')  # Sort 'B' (Bottom) last
    
    try:
        # Attempt numeric conversion for standard segment numbering
        # Supports both integer (1, 2, 3) and decimal (0.5, 1.5) segments
        return (0, float(segment_id_str))
    except ValueError:
        # Handle non-numeric segment identifiers (e.g., "A", "MIDDLE")
        # These get intermediate priority with lexicographic ordering
        return (1, segment_id_str)


def _parse_filename(filepath: str) -> Tuple[Optional[str], Optional[str], str]:
    """
    Parse pile analysis file path to extract pile mark and segment identifier.
    
    This function decomposes pile analysis file names following standard naming
    conventions used in foundation design workflows. It handles various naming
    patterns while maintaining compatibility with existing file structures.
    
    The parsing supports pile marks containing underscores while correctly
    identifying the segment identifier as the final component before the extension.
    """
    # Extract filename from full path
    filename = os.path.basename(filepath)
    name_part, ext = os.path.splitext(filename)

    # Regular expression to capture PILEMARK_SEGMENTID pattern
    # Pattern allows for pile marks containing underscores while identifying
    # the segment ID as the final component after the last underscore
    match = re.match(r'(.+)_([^_]+)$', name_part)
    
    if match:
        pile_mark = match.group(1)    # Everything before the last underscore
        segment_id = match.group(2)   # Part after the last underscore
        return pile_mark, segment_id, ext
    else:
        return name_part, None, ext


def _attempt_delete_file(
    file_path: str, 
    log_list: List[str], 
    pile_mark_context: str, 
    max_retries: int = 3, 
    delay: float = 0.1
) -> Tuple[bool, str]:
    """
    Attempt to delete a file with retry mechanism for handling locked files.
    
    This function provides robust file deletion with automatic retry for common
    Windows file locking issues that can occur during batch processing operations.
    It implements exponential backoff and detailed logging for troubleshooting.
    
    File locking often occurs when:
    - Files are still open in Excel or image viewers
    - Antivirus software is scanning files
    - Background processes have temporary file handles
    - Network storage systems have delayed release
    """
    filename = os.path.basename(file_path)
    
    # Attempt deletion with retry mechanism
    for attempt in range(max_retries):
        try:
            os.remove(file_path)
            # Success - file deleted without issues
            return True, filename
            
        except PermissionError as e_perm:
            # File is locked - this is retryable
            log_list.append(
                f"Worker ({pile_mark_context}):   Attempt {attempt + 1}/{max_retries} "
                f"to delete {filename} failed (in use): {e_perm}"
            )
            
            if attempt < max_retries - 1:
                # Wait before next attempt with brief delay
                time.sleep(delay)
            else:
                # Final attempt failed - log and give up
                log_list.append(
                    f"Worker ({pile_mark_context}):   Giving up on deleting {filename} "
                    f"after {max_retries} attempts due to persistent PermissionError."
                )
                return False, filename
                
        except Exception as e_del:
            # Other exceptions are not retryable - fail immediately
            log_list.append(
                f"Worker ({pile_mark_context}):   Error deleting {filename}: {e_del}. "
                f"Will not retry for this error type."
            )
            return False, filename
    
    return False, filename


def _worker_process_single_pile_pngs(args: Tuple[str, List[Dict[str, str]], str]) -> Tuple[str, bool, List[str]]:
    """
    Worker function to process PNG images for a single pile using multiprocessing.
    
    This function combines multiple pile segment PNG images into a single composite
    image with segment labels for comprehensive visual design verification. It operates
    as a worker process in the multiprocessing pool for efficient batch operations.
    
    The composite image creation process:
    1. Load and validate all segment images for the pile
    2. Create title labels for each segment identifier
    3. Calculate optimal layout dimensions for vertical stacking
    4. Compose segments vertically with centered alignment
    5. Save the combined image and clean up source files
    """
    # Unpack worker arguments
    pile_mark, segments_for_pile, source_folder = args
    logs = []
    logs.append(f"Worker PNG: Starting pile {pile_mark} with {len(segments_for_pile)} segments.")

    # Initialize image processing variables
    images_with_titles = []  # Store processed segment images with titles
    total_height = 0         # Accumulated height for composite image
    max_width = 0           # Maximum width among all segments
    title_height = 40       # Standard height for segment title labels
    
    # Load font with cross-platform
    font = None
    try:
        # Primary font choice - Arial (Windows standard)
        font = ImageFont.truetype("arial.ttf", 20)
    except IOError:
        try:
            # Secondary font choice - Liberation Sans (Linux standard)
            font = ImageFont.truetype("LiberationSans-Regular.ttf", 20)
        except IOError:
            logs.append(f"Worker PNG ({pile_mark}): Arial and LiberationSans fonts not found. Using default font.")
            font = ImageFont.load_default()

    # Track successfully processed files for cleanup
    processed_segment_paths = []

    # Process each segment image for the current pile
    for seg_info in segments_for_pile:
        try:
            # Use context manager to ensure proper resource management
            with Image.open(seg_info['path']) as img:
                # Create title image for segment identification
                title_img = Image.new('RGB', (img.width, title_height), color=(255, 255, 255))
                draw = ImageDraw.Draw(title_img)
                text = f"Segment: {seg_info['segment_id']}"
                
                # Calculate text positioning for centered alignment
                try:
                    # Modern PIL method for text measurement
                    text_bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                except AttributeError:
                    text_width, text_height = draw.textsize(text, font=font)
                
                # Draw centered text on title image
                x_pos = (img.width - text_width) / 2
                y_pos = (title_height - text_height) / 2
                draw.text((x_pos, y_pos), text, fill=(0, 0, 0), font=font)

                # Store images for composition (create copies to persist beyond context manager)
                images_with_titles.append((title_img, img.copy()))
                processed_segment_paths.append(seg_info['path'])

            # Update composite image dimensions
            segment_img = images_with_titles[-1][1]  # Get the copied segment image
            total_height += segment_img.height + title_height
            if segment_img.width > max_width:
                max_width = segment_img.width

        except Exception as e:
            # Log individual segment processing errors but continue with other segments
            logs.append(f"Worker PNG ({pile_mark}): Error opening/processing image {seg_info['path']}: {e}")
            continue

    # Validate that at least one segment was processed successfully
    if not images_with_titles:
        logs.append(f"Worker PNG ({pile_mark}): No valid images to combine.")
        return pile_mark, False, logs

    # Create composite image and save result
    composite_image = None  # Initialize for finally block cleanup
    combined_image_path = os.path.join(source_folder, f"{pile_mark}_Combined_Segments.png")
    
    try:
        # Create composite image canvas with calculated dimensions
        composite_image = Image.new('RGB', (max(1, max_width), total_height), color=(255, 255, 255))
        current_y = 0  # Track vertical position for image placement
        
        # Compose all segments vertically with titles
        for title_img, img_data in images_with_titles:
            # Paste title image (resize if necessary for width consistency)
            if title_img.width != max_width:
                title_resized = title_img.resize((max_width, title_img.height), Image.LANCZOS)
                composite_image.paste(title_resized, (0, current_y))
            else:
                composite_image.paste(title_img, (0, current_y))
            current_y += title_img.height
            
            # Paste segment image with horizontal centering
            x_offset = (max_width - img_data.width) // 2
            composite_image.paste(img_data, (x_offset, current_y))
            current_y += img_data.height

        # Save the composite image
        composite_image.save(combined_image_path)
        logs.append(f"Worker PNG ({pile_mark}): Saved combined PNG: {combined_image_path}")

        # Attempt cleanup of successfully processed source files
        if processed_segment_paths:
            logs.append(
                f"Worker PNG ({pile_mark}): Attempting to delete {len(processed_segment_paths)} "
                f"source PNG(s) used in successful combination..."
            )
            
            deleted_pngs_successfully = []
            failed_to_delete_pngs = []

            # Process each source file for deletion
            for seg_path in processed_segment_paths:
                success, fname = _attempt_delete_file(seg_path, logs, f"PNG {pile_mark} (delete)")
                if success:
                    deleted_pngs_successfully.append(fname)
                else:
                    failed_to_delete_pngs.append(fname)

            # Log cleanup results
            if deleted_pngs_successfully:
                logs.append(
                    f"Worker PNG ({pile_mark}):   Successfully deleted {len(deleted_pngs_successfully)} "
                    f"source PNG(s): {', '.join(deleted_pngs_successfully)}."
                )
            if failed_to_delete_pngs:
                logs.append(
                    f"Worker PNG ({pile_mark}):   Failed to delete {len(failed_to_delete_pngs)} "
                    f"source PNG(s): {', '.join(failed_to_delete_pngs)}. See worker logs above for details/reasons."
                )
        else:
            logs.append(f"Worker PNG ({pile_mark}): No source PNGs to delete as none were processed.")

        return pile_mark, True, logs
        
    except Exception as e:
        # Handle major errors in composite image creation or saving
        logs.append(f"Worker PNG ({pile_mark}): Error creating/saving combined PNG: {e}")
        return pile_mark, False, logs
        
    finally:
        # Ensure proper cleanup of all image resources
        if composite_image:
            composite_image.close()
        
        # Close all intermediate images to prevent memory leaks
        for title_img, img_data in images_with_titles:
            if title_img:
                title_img.close()
            if img_data:
                img_data.close()


def combine_segment_pngs_for_pile(
    source_folder: str, 
    log_callback: Optional[Callable[[str], None]] = None
) -> None:
    """
    Combine PNG images of pile segments into composite images for each pile group.
    
    This function orchestrates the complete PNG combination workflow for pile foundation
    design documentation. It processes all segment PNG files in the specified directory,
    groups them by pile mark, and creates composite visualizations showing the complete
    pile design from top to bottom with appropriate segment labeling.
    
    The function implements parallel processing using multiprocessing to handle large
    numbers of piles efficiently, making it suitable for major foundation projects
    with hundreds of pile elements.
    
    Processing Workflow:
    1. Scan directory for segment PNG files (excluding existing combined files)
    2. Parse filenames to extract pile marks and segment identifiers
    3. Group segments by pile mark and sort by design sequence
    4. Create parallel processing tasks for efficient batch operations
    5. Execute multiprocessing workers for PNG combination
    6. Collect and report results with detailed logging
    """
    if log_callback:
        log_callback(f"Starting PNG combination in: {source_folder} using multiprocessing...")

    # Discover all PNG files in the source directory
    all_png_files = glob.glob(os.path.join(source_folder, "*.png"))
    
    # Filter out existing combined files to avoid reprocessing
    png_files = [f for f in all_png_files if "_Combined_Segments" not in f]

    # Validate that source files exist for processing
    if not png_files:
        if log_callback:
            log_callback("No segment PNG files found to combine.")
        return

    # Group segment files by pile mark for batch processing
    grouped_files = defaultdict(list)
    for f_path in png_files:
        pile_mark, segment_id, _ = _parse_filename(f_path)
        if pile_mark and segment_id:
            grouped_files[pile_mark].append({'path': f_path, 'segment_id': segment_id})

    # Prepare multiprocessing tasks with sorted segments
    tasks = []
    for pile_mark, segments in grouped_files.items():
        if not segments:
            continue
            
        # Sort segments following foundation design sequence conventions
        segments.sort(key=lambda s: _get_segment_sort_key(s['segment_id']))
        tasks.append((pile_mark, segments, source_folder))

    # Validate that processing tasks were created successfully
    if not tasks:
        if log_callback:
            log_callback("No tasks to process for PNG combination.")
        return

    # Configure multiprocessing parameters for optimal performance
    num_cpus = os.cpu_count()
    num_processes = min(len(tasks), num_cpus if num_cpus else 1)
    if num_processes == 0:
        num_processes = 1  # Ensure at least one process

    if log_callback:
        log_callback(f"Processing {len(tasks)} piles for PNG combination using {num_processes} processes.")

    # Execute parallel processing of pile PNG combinations
    with multiprocessing.Pool(processes=num_processes) as pool:
        results = pool.map(_worker_process_single_pile_pngs, tasks)

    # Process and report results from all worker processes
    if log_callback:
        for pile_mark_processed, success, worker_logs in results:
            # Report detailed worker logs for troubleshooting
            for log_msg in worker_logs:
                log_callback(log_msg)
                
            # Report overall success/failure status for each pile
            if success:
                log_callback(f"PNG combination for pile {pile_mark_processed} completed successfully.")
            else:
                log_callback(f"PNG combination for pile {pile_mark_processed} FAILED.")
                
        log_callback("PNG combination process finished.")


def _worker_process_single_pile_excels(args: Tuple[str, List[Dict[str, str]], str]) -> Tuple[str, bool, List[str]]:
    """
    Worker function to process Excel files for a single pile using multiprocessing.
    
    This function combines multiple pile segment Excel files into a single consolidated
    workbook with appropriately prefixed sheet names for comprehensive numerical analysis
    review. It operates as a worker process in the multiprocessing pool for efficient
    batch operations on large foundation projects.
    
    The Excel combination process:
    1. Create a new Excel writer for the combined workbook
    2. Process each segment file and extract all worksheets
    3. Copy worksheets with segment-prefixed names to avoid conflicts
    4. Handle duplicate sheet names with automatic numbering
    5. Save the consolidated workbook and clean up source files
    """
    # Unpack worker arguments
    pile_mark, segments_for_pile, source_folder = args
    logs = []
    logs.append(f"Worker Excel: Starting pile {pile_mark} with {len(segments_for_pile)} segments.")

    # Initialize tracking variables for batch logging
    processed_segment_paths_for_deletion = []  # Successfully processed files for cleanup
    successfully_processed_excel_basenames = []  # Files included in combined workbook
    failed_to_process_excel_segments = []  # Files that failed processing

    # Create combined Excel file path
    combined_excel_path = os.path.join(source_folder, f"{pile_mark}_Combined_Segments.xlsx")
    
    try:
        # Use Excel writer context manager for proper resource management
        with pd.ExcelWriter(combined_excel_path, engine='openpyxl') as writer:
            # Handle empty segment list case
            if not segments_for_pile:
                logs.append(f"Worker Excel ({pile_mark}): No segments provided for pile {pile_mark}.")

            # Process each segment Excel file
            for seg_info in segments_for_pile:
                segment_file_path = seg_info['path']
                segment_basename = os.path.basename(segment_file_path)
                segment_id = seg_info['segment_id']
                
                try:
                    # Read all sheets from the segment Excel file
                    with pd.ExcelFile(segment_file_path) as xls:
                        for sheet_name in xls.sheet_names:
                            # Read worksheet data
                            df = pd.read_excel(xls, sheet_name=sheet_name)
                            
                            # Create prefixed sheet name to avoid conflicts
                            new_sheet_name = f"{segment_id}_{sheet_name}"[:31]  # Excel 31-char limit
                            original_new_sheet_name = new_sheet_name
                            
                            # Handle duplicate sheet names with sequential numbering
                            sheet_counter = 1
                            while new_sheet_name in writer.sheets:
                                suffix = f"_{sheet_counter}"
                                # Ensure total length stays within Excel limits
                                new_sheet_name = original_new_sheet_name[:31 - len(suffix)] + suffix
                                sheet_counter += 1
                            
                            # Write worksheet to combined file
                            df.to_excel(writer, sheet_name=new_sheet_name, index=False)
                    
                    # Track successful processing for batch logging and cleanup
                    successfully_processed_excel_basenames.append(segment_basename)
                    processed_segment_paths_for_deletion.append(segment_file_path)
                    
                except Exception as e:
                    # Log individual segment processing errors but continue with others
                    error_msg = f"Error processing sheets from {segment_basename}: {e}"
                    logs.append(f"Worker Excel ({pile_mark}):   {error_msg}")
                    failed_to_process_excel_segments.append(f"{segment_basename} (Reason: {e})")
                    continue

        # Generate batch processing summary logs
        if successfully_processed_excel_basenames:
            logs.append(
                f"Worker Excel ({pile_mark}):   Successfully processed sheets from "
                f"{len(successfully_processed_excel_basenames)} segment file(s): "
                f"{', '.join(successfully_processed_excel_basenames)} into {os.path.basename(combined_excel_path)}."
            )
        elif not segments_for_pile:
            # No action needed - already logged no segments case above
            pass
        elif not failed_to_process_excel_segments:
            # Edge case: had segments but none processed without specific errors recorded
            logs.append(
                f"Worker Excel ({pile_mark}):   No segment Excel files were processed for pile {pile_mark}, "
                f"though segments were listed. Combined file {os.path.basename(combined_excel_path)} may be empty."
            )

        # Log processing failures if any occurred
        if failed_to_process_excel_segments:
            logs.append(
                f"Worker Excel ({pile_mark}):   Failed to process sheets from "
                f"{len(failed_to_process_excel_segments)} segment file(s): "
                f"{'; '.join(failed_to_process_excel_segments)}. These were not included in the combined file."
            )

        # Log successful save of combined file
        logs.append(f"Worker Excel ({pile_mark}): Saved combined Excel: {combined_excel_path}")

        # Attempt cleanup of successfully processed source files
        if processed_segment_paths_for_deletion:
            logs.append(
                f"Worker Excel ({pile_mark}): Attempting to delete {len(processed_segment_paths_for_deletion)} "
                f"source Excel file(s) that were successfully processed and included..."
            )
            
            deleted_excels_successfully = []
            failed_to_delete_excels = []
            
            # Process each source file for deletion
            for seg_path in processed_segment_paths_for_deletion:
                success, fname = _attempt_delete_file(seg_path, logs, f"Excel {pile_mark} (delete)")
                if success:
                    deleted_excels_successfully.append(fname)
                else:
                    failed_to_delete_excels.append(fname)

            # Log cleanup results
            if deleted_excels_successfully:
                logs.append(
                    f"Worker Excel ({pile_mark}):   Successfully deleted {len(deleted_excels_successfully)} "
                    f"source Excel file(s): {', '.join(deleted_excels_successfully)}."
                )
            if failed_to_delete_excels:
                logs.append(
                    f"Worker Excel ({pile_mark}):   Failed to delete {len(failed_to_delete_excels)} "
                    f"source Excel file(s): {', '.join(failed_to_delete_excels)}. "
                    f"See worker logs above for details/reasons."
                )
        elif segments_for_pile:
            # Had files to process, but none made it to deletion stage (all failed processing)
            logs.append(
                f"Worker Excel ({pile_mark}): No source Excel files to delete as none were "
                f"successfully processed and included in the combined file."
            )
        else:
            # No segments provided to begin with
            logs.append(
                f"Worker Excel ({pile_mark}): No source Excel files to delete as no segments "
                f"were provided for processing."
            )

        return pile_mark, True, logs
        
    except Exception as e:
        # Handle major errors during Excel writer operations or file saving
        logs.append(
            f"Worker Excel ({pile_mark}): Major error creating/saving combined Excel {combined_excel_path}: {e}"
        )
        
        # Log intended files for troubleshooting major failures
        if segments_for_pile:
            intended_files = [os.path.basename(s['path']) for s in segments_for_pile]
            logs.append(
                f"Worker Excel ({pile_mark}):   Intended segments for processing were: "
                f"{', '.join(intended_files)}"
            )
        
        return pile_mark, False, logs


def combine_segment_excels_for_pile(
    source_folder: str, 
    log_callback: Optional[Callable[[str], None]] = None
) -> None:
    """
    Combine Excel files of pile segments into consolidated workbooks for each pile group.
    
    This function orchestrates the complete Excel combination workflow for pile foundation
    design documentation. It processes all segment Excel files in the specified directory,
    groups them by pile mark, and creates consolidated workbooks containing all numerical
    analysis results with appropriately prefixed sheet names for easy navigation.
    """
    if log_callback:
        log_callback(f"Starting Excel combination in: {source_folder} using multiprocessing...")

    # Discover all Excel files in the source directory
    all_excel_files = glob.glob(os.path.join(source_folder, "*.xlsx"))
    
    # Filter out existing combined files to avoid reprocessing
    excel_files = [f for f in all_excel_files if "_Combined_Segments" not in f]

    # Validate that source files exist for processing
    if not excel_files:
        if log_callback:
            log_callback("No segment Excel files found to combine.")
        return

    # Group segment files by pile mark for batch processing
    grouped_files = defaultdict(list)
    for f_path in excel_files:
        pile_mark, segment_id, _ = _parse_filename(f_path)
        if pile_mark and segment_id:
            grouped_files[pile_mark].append({'path': f_path, 'segment_id': segment_id})

    # Prepare multiprocessing tasks with sorted segments
    tasks = []
    for pile_mark, segments in grouped_files.items():
        if not segments:
            continue
            
        # Sort segments following foundation design sequence conventions
        segments.sort(key=lambda s: _get_segment_sort_key(s['segment_id']))
        tasks.append((pile_mark, segments, source_folder))

    # Validate that processing tasks were created successfully
    if not tasks:
        if log_callback:
            log_callback("No tasks to process for Excel combination.")
        return

    # Configure multiprocessing parameters for optimal performance
    num_cpus = os.cpu_count()
    num_processes = min(len(tasks), num_cpus if num_cpus else 1)
    if num_processes == 0:
        num_processes = 1  # Ensure at least one process

    if log_callback:
        log_callback(f"Processing {len(tasks)} piles for Excel combination using {num_processes} processes.")

    # Execute parallel processing of pile Excel combinations
    with multiprocessing.Pool(processes=num_processes) as pool:
        results = pool.map(_worker_process_single_pile_excels, tasks)

    # Process and report results from all worker processes
    if log_callback:
        for pile_mark_processed, success, worker_logs in results:
            # Report detailed worker logs for troubleshooting
            for log_msg in worker_logs:
                log_callback(log_msg)
                
            # Report overall success/failure status for each pile
            if success:
                log_callback(f"Excel combination for pile {pile_mark_processed} completed successfully.")
            else:
                log_callback(f"Excel combination for pile {pile_mark_processed} FAILED.")
                
        log_callback("Excel combination process finished.")
