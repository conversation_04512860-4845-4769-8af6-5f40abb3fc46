"""
Foundation Automation System

A comprehensive system for foundation design automation including:
- FEM model building for SAFE
- Foundation design calculations
- Pile estimation and layout
- Structural analysis integration
- Email notifications and security management
"""

# Version information
__version__ = "1.0.0"
__author__ = "Foundation Automation Team"

# Core modules (always available)
from . import app_controller
from . import main_class

# Authentication and security
try:
    from .auth import SecurityManager
except ImportError:
    SecurityManager = None

# Configuration
try:
    from .config import get_ssl_context, configure_requests_ssl
except ImportError:
    get_ssl_context = None
    configure_requests_ssl = None

# FEM Building
try:
    from .build_fem import (
        SafeModelBuilderGUI, safe_model_builder,
        automated_initialize_setup, automated_generate_soil_spring, 
        automated_generate_safe_model, gen_safe, gen_soil_spring
    )
except ImportError:
    SafeModelBuilderGUI = None
    safe_model_builder = None
    automated_initialize_setup = None
    automated_generate_soil_spring = None
    automated_generate_safe_model = None
    gen_safe = None
    gen_soil_spring = None

# Foundation Design
try:
    from .design_fdn import (
        SafeDesignCheckerGUI, initialize_file_paths, read_input_data,
        run_uls_sls_calculations, run_design_automation,
        cal_pile_sls, cal_pile_uls, cal_capacity_piles, gen_piling_schedule
    )
except ImportError:
    SafeDesignCheckerGUI = None
    initialize_file_paths = None
    read_input_data = None
    run_uls_sls_calculations = None
    run_design_automation = None
    cal_pile_sls = None
    cal_pile_uls = None
    cal_capacity_piles = None
    gen_piling_schedule = None

# Slab Design
try:
    from .design_slab import safe_slab_design, FilePath, DesignData
except ImportError:
    safe_slab_design = None
    FilePath = None
    DesignData = None

# Foundation Agent (AI-powered pile estimation)
try:
    from .fdn_agent import (
        FdnAgentGUI, fdn_agent, run_pile_estimation,
        run_pile_estimation_basic, run_pile_estimation_with_visualization
    )
except ImportError:
    FdnAgentGUI = None
    fdn_agent = None
    run_pile_estimation = None
    run_pile_estimation_basic = None
    run_pile_estimation_with_visualization = None

# ETABS Integration
try:
    from .etabs_cwls import LoadingScheduleGUI, ETABS_converter
except ImportError:
    LoadingScheduleGUI = None
    ETABS_converter = None

# Email Notifications
try:
    from .email_notifications import (
        send_password_email, send_email_log, 
        generate_password_key, create_smtp_ssl_context
    )
except ImportError:
    send_password_email = None
    send_email_log = None
    generate_password_key = None
    create_smtp_ssl_context = None

# Data Initialization and File Operations
try:
    from .initialization import (
        init_input, init_output, init_file_paths, init_existing_file_paths,
        update_excel_geology, update_excel_loading, update_excel_property
    )
except ImportError:
    init_input = None
    init_output = None
    init_file_paths = None
    init_existing_file_paths = None
    update_excel_geology = None
    update_excel_loading = None
    update_excel_property = None

# Data Reading
try:
    from .read import (
        read_input_geology, read_input_geometry, read_input_loading,
        read_input_property, read_input_steel
    )
except ImportError:
    read_input_geology = None
    read_input_geometry = None
    read_input_loading = None
    read_input_property = None
    read_input_steel = None

# SAFE API Integration
try:
    from .safe_api import (
        automate_build_fem, automate_foundation_design, 
        automate_complete_workflow, AutomationLogger
    )
except ImportError:
    automate_build_fem = None
    automate_foundation_design = None
    automate_complete_workflow = None
    AutomationLogger = None

# UI Components (keep as module import for now)
from . import ui

# Complete exports list
__all__ = [
    # Core modules
    'app_controller',
    'main_class',
    
    # Authentication
    'SecurityManager',
    
    # Configuration
    'get_ssl_context',
    'configure_requests_ssl',
    
    # FEM Building
    'SafeModelBuilderGUI',
    'safe_model_builder',
    'automated_initialize_setup',
    'automated_generate_soil_spring',
    'automated_generate_safe_model',
    'gen_safe',
    'gen_soil_spring',
    
    # Foundation Design
    'SafeDesignCheckerGUI',
    'initialize_file_paths',
    'read_input_data',
    'run_uls_sls_calculations',
    'run_design_automation',
    'cal_pile_sls',
    'cal_pile_uls',
    'cal_capacity_piles',
    'gen_piling_schedule',
    
    # Slab Design
    'safe_slab_design',
    'FilePath',
    'DesignData',
    
    # Foundation Agent
    'FdnAgentGUI',
    'fdn_agent',
    'run_pile_estimation',
    'run_pile_estimation_basic',
    'run_pile_estimation_with_visualization',
    
    # ETABS Integration
    'LoadingScheduleGUI',
    'ETABS_converter',
    
    # Email Notifications
    'send_password_email',
    'send_email_log',
    'generate_password_key',
    'create_smtp_ssl_context',
    
    # Initialization
    'init_input',
    'init_output',
    'init_file_paths',
    'init_existing_file_paths',
    'update_excel_geology',
    'update_excel_loading',
    'update_excel_property',
    
    # Data Reading
    'read_input_geology',
    'read_input_geometry',
    'read_input_loading',
    'read_input_property',
    'read_input_steel',
    
    # SAFE API
    'automate_build_fem',
    'automate_foundation_design',
    'automate_complete_workflow',
    'AutomationLogger',
    
    # UI
    'ui'
]

# Module-level convenience functions
def get_available_modules():
    """Return a list of successfully imported modules."""
    available = []
    for name in __all__:
        if globals().get(name) is not None:
            available.append(name)
    return available

def check_dependencies():
    """Check which optional dependencies are available."""
    dependencies = {
        'GUI Components': SafeModelBuilderGUI is not None,
        'Foundation Design': SafeDesignCheckerGUI is not None,
        'Foundation Agent': FdnAgentGUI is not None,
        'ETABS Integration': LoadingScheduleGUI is not None,
        'Email Notifications': send_password_email is not None,
        'SAFE API': automate_build_fem is not None,
        'Security Manager': SecurityManager is not None,
    }
    return dependencies
