# Foundation Agent Module Structure (`fdn_agent/`)

## Main Foundation Agent Directory
```
fdn_agent/
├── 📄 agent_main.py             # Main automation workflow
├── 📄 agent_gui.py              # Foundation Agent GUI
├── 📄 fdn_agent_config.py       # Agent configuration
├── 📄 functions.py              # Utility functions
├── 📄 automated_pile_estimation_test.py # Automated testing
├── 📄 README.md                 # Agent documentation
├── 📄 Rules.md                  # Design rules and guidelines
├── 📄 __init__.py               # Module initialization
├── 📁 __pycache__/              # Compiled Python files
├── 📁 components/               # GUI components
└── 📁 pile_estimation/          # Advanced pile estimation system
```

## 🎨 GUI Components (`components/`)
```
components/
├── 📄 base_gui.py               # Base GUI framework
├── 📄 file_management.py        # File management interface
├── 📄 pile_types.py             # Pile type management
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🤖 Advanced Pile Estimation System (`pile_estimation/`)
```
pile_estimation/
├── 📄 pile_workflow_coordinator.py # Main workflow coordination
├── 📄 exceptions.py             # Exception hierarchy
├── 📄 Pile_Layout_Rules.md      # Layout rules documentation
├── 📄 README.md                 # Pile estimation documentation
├── 📄 __init__.py               # Module initialization
├── 📁 __pycache__/              # Compiled Python files
├── 📁 clustering/               # Element clustering algorithms
├── 📁 data_types/               # Type definitions
├── 📁 layout_generation/        # Layout generation algorithms
├── 📁 load_calculator/          # Load calculation system
├── 📁 optimization/             # Multi-objective optimization
├── 📁 pile_cap_geometry/        # Pile cap geometry
├── 📁 pile_type_selection/      # Intelligent pile selection
├── 📁 utils/                    # Utility functions
└── 📁 visualizer/               # DXF visualization system
```

## 🔬 Clustering System (`clustering/`)
```
clustering/
├── 📄 core_grouping.py          # Element grouping algorithms
├── 📄 enhanced_clustering.py    # Enhanced clustering engine
├── 📄 load_clustering.py        # Load-based clustering
├── 📄 element.py                # Core element class
├── 📄 helpers.py                # Clustering utilities
├── 📄 README.md                 # Clustering documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 📊 Data Types System (`data_types/`)
```
data_types/
├── 📄 basic_types.py            # Basic geometric types
├── 📄 config_types.py           # Configuration types
├── 📄 coordinate_types.py       # Coordinate system types
├── 📄 element_types.py          # Structural element types
├── 📄 pile_preselection_types.py # Preselection types
├── 📄 pile_types.py             # Pile definition types
├── 📄 result_types.py           # Result data types
├── 📄 README.md                 # Data types documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```