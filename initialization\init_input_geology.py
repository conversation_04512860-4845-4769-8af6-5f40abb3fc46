from datetime import datetime

import pandas as pd

from build_fem import build_fem_config as config


def init_input_geology(excel_inputs, file_paths):
    path_excel_output = file_paths.ExcelGeology
    df_borehole_spt = pd.DataFrame(columns=['Borehole', 'SPT Level (mPD)', 'SPTN Value', 'Soil Type (GS/NCC/OS)'])

    # Check if borehole exists and is not empty
    if hasattr(excel_inputs, 'Borehole') and not excel_inputs.Borehole.empty:
        df_borehole_spt['Borehole'] = excel_inputs.Borehole['Borehole']
    df_borehole_spt['SPT Level (mPD)'] = 0
    df_borehole_spt['SPTN Value'] = 50
    df_borehole_spt['Soil Type (GS/NCC/OS)'] = 'GS'

    df_pile_soil_spring_setting = pd.DataFrame(columns=['Pile Mark', 'Borehole', 'Rx', 'Ry'])

    # Check if <PERSON>le exists and is not empty before filtering
    if hasattr(excel_inputs, 'Pile') and not excel_inputs.Pile.empty:
        condition = excel_inputs.Pile['Pile Type'] != 'MP'
        df_pile_soil_spring_setting['Pile Mark'] = excel_inputs.Pile.loc[condition, 'Pile Mark']

    df_lateral_soil_spring = pd.DataFrame(
        columns=['Point Name', 'Pile Diameter (m)', 'X (m)', 'Y (m)', 'Z (m)', 'Ground Level (mPD)', 'Nearest Pile X',
                 'Nearest Pile X Distance (m)', 'Nearest Pile Y', 'Nearest Pile Y Distance (m)', 'Borehole',
                 'True Z (mPD)', 'Segment_Z (mPD)', 'SPTN Value', 'Soil Type (GS/NCC/OS)', 'Subgrade Reaction (kN/m3)',
                 'Rx', 'Ry', 'Factor Dia', 'Segment Center (mPD)', 'Distance from Ground Level (m)', 'Delta Z (m)',                 'Spring X (kN/m)', 'Spring Y (kN/m)'])

    with pd.ExcelWriter(path_excel_output) as writer:
        df_borehole_spt.to_excel(writer, sheet_name=config.SHEET_BOREHOLE_SPT, index=False)
        df_pile_soil_spring_setting.to_excel(writer, sheet_name=config.SHEET_PILE_SOIL_SPRING_SETTING, index=False)
        df_lateral_soil_spring.to_excel(writer, sheet_name=config.SHEET_LATERAL_SOIL_SPRING, index=False)
    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Initialized Excel Input (Geology)!')
    return excel_inputs
