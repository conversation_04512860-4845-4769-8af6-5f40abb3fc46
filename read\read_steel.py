from datetime import datetime

import pandas as pd

from read.functions import _load_sheet_data
from read.read_geometry import _identity_process


def read_input_steel(excel_inputs, file_paths):
    path_excel_input = file_paths.DataSteel
    steel_attributes = ['SteelSectionH', 'SteelGrade']

    try:
        header_single = [0]
        _load_sheet_data(path_excel_input, 'H Section', excel_inputs, 'SteelSectionH', _identity_process,
                         header=header_single)
        _load_sheet_data(path_excel_input, 'Steel Grade', excel_inputs, 'SteelGrade', _identity_process,
                         header=header_single)

    except FileNotFoundError:
        # _load_sheet_data would have printed an error for the first sheet it failed to read from the non-existent file.
        print(
            f"Halting steel data reading: Excel file at '{path_excel_input}' not found. All steel attributes will be empty.")
        for attr_name in steel_attributes:
            setattr(excel_inputs, attr_name, pd.DataFrame())  # Ensure attributes are empty DataFrames
        return excel_inputs

    # Print message indicating successful (or partially successful if sheets were missing but file found)
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Read Excel Input (Steel Data)!')
    return excel_inputs
