﻿"""
Pile Calculations

Functions for calculating pile requirements based on loads.
"""

from typing import List, Tu<PERSON>, Dict, Union, Any
from math import ceil
import numpy as np
import pandas as pd

from ..data_types import ColumnData, WallData
from ..data_types import ExcelInputs
from ..exceptions import LoadCalculationError, InputDataError
from .basic_calculations import calculate_group_loads, calculate_element_load
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)


def calculate_required_piles(group_elements: Dict[str, List[Union[ColumnData, WallData]]],
                           excel_inputs: ExcelInputs, 
                           pile_capacity: float, log_callback=None) -> Tuple[int, float, List[str]]:
    """
    Calculate required number of piles based on total loads and pile capacity.
    
    Args:
        group_elements: Dictionary with 'columns' and 'walls' keys
        excel_inputs: ExcelInputs object with load data
        pile_capacity: Capacity of single pile in kN (must be > 0)
        log_callback: Optional callback for logging progress
        
    Returns:
        Tuple of (number_of_piles, load_per_pile, warnings)
        
    Raises:
        ValueError: If pile capacity is invalid
        LoadCalculationError: If load calculation fails
    """
    log_function_entry(log_callback, "calculate_required_piles",
                      pile_capacity=pile_capacity, num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])))

    try:
        enhanced_log(log_callback, f"Starting pile requirement calculation with capacity {pile_capacity} kN", 'INFO')

        # Validate pile capacity
        log_constraint_check(log_callback, "pile_capacity_positive", pile_capacity, 0, pile_capacity > 0)
        if not isinstance(pile_capacity, (int, float)) or pile_capacity <= 0:
            error_msg = f"Pile capacity must be positive number, got {pile_capacity}"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_required_piles", "error")
            raise ValueError(error_msg)

        if not group_elements or not any(group_elements.values()):
            error_msg = "No structural elements provided for pile calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_required_piles", "error")
            raise InputDataError(error_msg)

        if excel_inputs is None:
            error_msg = "ExcelInputs object is required for pile calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_required_piles", "error")
            raise InputDataError(error_msg)

        # Validate that excel_inputs has the required load data attributes
        if not hasattr(excel_inputs, 'InputLoadColumn') or not hasattr(excel_inputs, 'InputLoadWall'):
            error_msg = "ExcelInputs object must have InputLoadColumn and InputLoadWall attributes"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_required_piles", "error")
            raise InputDataError(error_msg)

        num_columns = len(group_elements.get('columns', []))
        num_walls = len(group_elements.get('walls', []))
        enhanced_log(log_callback, f"Pile calculation inputs: {num_columns} columns, {num_walls} walls, pile capacity: {pile_capacity} kN", 'INFO')

        warnings = []

        # Calculate total load
        enhanced_log(log_callback, "Calculating total load for pile requirements", 'DEBUG')
        with create_timed_logger(log_callback, "total_load_calculation"):
            total_load, load_warnings = calculate_group_loads(group_elements, excel_inputs, log_callback)
        warnings.extend(load_warnings)

        log_calculation_result(log_callback, "total_load", total_load, "kN")
        log_constraint_check(log_callback, "total_load_positive", total_load, 0, total_load > 0)

        if total_load <= 0:
            error_msg = "Total load is zero or negative - cannot calculate pile requirements"
            enhanced_log(log_callback, f"ERROR: {error_msg} (calculated load: {total_load} kN)", 'ERROR')
            log_function_exit(log_callback, "calculate_required_piles", "error")
            raise LoadCalculationError(error_msg)

        # Calculate required piles (always round up)
        enhanced_log(log_callback, "Calculating required pile count", 'DEBUG')
        enhanced_log(log_callback, f"Calculation: {total_load:.2f} kN ÷ {pile_capacity:.2f} kN/pile", 'DEBUG')
        required_piles_exact = total_load / pile_capacity
        required_piles = max(1, int(np.ceil(required_piles_exact)))

        log_calculation_result(log_callback, "required_piles_exact", required_piles_exact, "piles")
        log_calculation_result(log_callback, "required_piles_rounded", required_piles, "piles")
        enhanced_log(log_callback, f"Exact piles needed: {required_piles_exact:.3f}, rounded up to: {required_piles}", 'DEBUG')

        # Calculate actual load per pile
        load_per_pile = total_load / required_piles
        log_calculation_result(log_callback, "load_per_pile", load_per_pile, "kN")

        # Calculate efficiency metrics
        efficiency = (load_per_pile / pile_capacity) * 100
        over_capacity = load_per_pile > pile_capacity

        log_performance_metric(log_callback, "pile_efficiency", efficiency, "%")
        log_constraint_check(log_callback, "pile_capacity_not_exceeded", load_per_pile, pile_capacity, not over_capacity)

        if over_capacity:
            enhanced_log(log_callback, f"WARNING: Load per pile ({load_per_pile:.2f} kN) exceeds capacity ({pile_capacity:.2f} kN) by {load_per_pile - pile_capacity:.2f} kN", 'WARNING')
        else:
            enhanced_log(log_callback, f"Pile capacity check: {load_per_pile:.2f} kN ≤ {pile_capacity:.2f} kN (OK)", 'DEBUG')

        enhanced_log(log_callback, f"Pile requirement calculation completed:", 'INFO')
        enhanced_log(log_callback, f"  Total load: {total_load:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"  Pile capacity: {pile_capacity:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"  Exact piles needed: {required_piles_exact:.2f}", 'DEBUG')
        enhanced_log(log_callback, f"  Required piles: {required_piles}", 'INFO')
        enhanced_log(log_callback, f"  Load per pile: {load_per_pile:.2f} kN", 'INFO')
        enhanced_log(log_callback, f"  Pile efficiency: {efficiency:.1f}%", 'INFO')

        if over_capacity:
            enhanced_log(log_callback, f"WARNING: Load per pile ({load_per_pile:.2f} kN) exceeds pile capacity ({pile_capacity:.2f} kN)!", 'WARNING')

        log_validation_result(log_callback, "pile_calculation", True, f"{required_piles} piles required for {total_load:.2f} kN")
        log_function_exit(log_callback, "calculate_required_piles", f"{required_piles} piles")
        return required_piles, load_per_pile, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_required_piles")
        log_function_exit(log_callback, "calculate_required_piles", "error")
        raise


def calculate_individual_pile_requirements(group_elements: Dict[str, List[Union[ColumnData, WallData]]],
                                         excel_inputs: ExcelInputs,
                                         pile_capacity: float,
                                         max_column_pile_distance: float = 3.0,
                                         max_wall_end_pile_distance: float = 3.0,
                                         log_callback=None) -> Tuple[Dict[str, Dict[str, Any]], List[str]]:
    """
    Calculate pile requirements for each individual element.
    
    Args:
        group_elements: Dictionary with 'columns' and 'walls' keys
        excel_inputs: ExcelInputs object with load data
        pile_capacity: Capacity of single pile in kN
        max_column_pile_distance: Max distance from column to nearest pile (meters)
        max_wall_end_pile_distance: Max distance from wall endpoint to nearest pile (meters)
        log_callback: Optional callback for logging progress
        
    Returns:
        Tuple of (element_pile_reqs, warnings)
        
    Raises:
        ValueError: If parameters are invalid
        LoadCalculationError: If calculation fails
    """
    log_function_entry(log_callback, "calculate_individual_pile_requirements",
                      pile_capacity=pile_capacity, max_column_pile_distance=max_column_pile_distance,
                      max_wall_end_pile_distance=max_wall_end_pile_distance,
                      num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])))

    try:
        enhanced_log(log_callback, f"Starting individual pile requirements calculation", 'INFO')
        enhanced_log(log_callback, f"Parameters: pile_capacity={pile_capacity} kN, max_column_dist={max_column_pile_distance}m, max_wall_dist={max_wall_end_pile_distance}m", 'DEBUG')
    
        # Validate input parameters
        log_constraint_check(log_callback, "pile_capacity_positive", pile_capacity, 0, pile_capacity > 0)
        if not isinstance(pile_capacity, (int, float)) or pile_capacity <= 0:
            error_msg = "pile_capacity must be positive number"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise ValueError(error_msg)

        log_constraint_check(log_callback, "max_column_distance_positive", max_column_pile_distance, 0, max_column_pile_distance > 0)
        if not isinstance(max_column_pile_distance, (int, float)) or max_column_pile_distance <= 0:
            error_msg = "max_column_pile_distance must be positive number"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise ValueError(error_msg)

        log_constraint_check(log_callback, "max_wall_distance_positive", max_wall_end_pile_distance, 0, max_wall_end_pile_distance > 0)
        if not isinstance(max_wall_end_pile_distance, (int, float)) or max_wall_end_pile_distance <= 0:
            error_msg = "max_wall_end_pile_distance must be positive number"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise ValueError(error_msg)

        if not group_elements or not any(group_elements.values()):
            error_msg = "No structural elements provided for individual pile calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise InputDataError(error_msg)

        if excel_inputs is None:
            error_msg = "ExcelInputs object is required for individual pile calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise InputDataError(error_msg)

        # Validate that excel_inputs has the required load data attributes
        if not hasattr(excel_inputs, 'InputLoadColumn') or not hasattr(excel_inputs, 'InputLoadWall'):
            error_msg = "ExcelInputs object must have InputLoadColumn and InputLoadWall attributes"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise InputDataError(error_msg)

        num_columns = len(group_elements.get('columns', []))
        num_walls = len(group_elements.get('walls', []))
        enhanced_log(log_callback, f"Individual pile calculation for {num_columns} columns and {num_walls} walls", 'INFO')

        warnings = []
        element_pile_reqs = {}

        # Process columns
        if 'columns' in group_elements and group_elements['columns']:
            if excel_inputs.InputLoadColumn is None:
                error_msg = "Column load DataFrame not available in excel_inputs"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
                raise InputDataError(error_msg)

            enhanced_log(log_callback, f"Processing {num_columns} columns for individual pile requirements", 'INFO')
            col_reqs, col_warnings = _process_column_requirements(
                group_elements['columns'],
                excel_inputs.InputLoadColumn,
                pile_capacity,
                max_column_pile_distance,
                log_callback
            )
            element_pile_reqs.update(col_reqs)
            warnings.extend(col_warnings)
            enhanced_log(log_callback, f"Processed {len(col_reqs)} column requirements successfully", 'INFO')
        else:
            enhanced_log(log_callback, "No columns to process for individual pile requirements", 'DEBUG')

        # Process walls
        if 'walls' in group_elements and group_elements['walls']:
            if excel_inputs.InputLoadWall is None:
                error_msg = "Wall load DataFrame not available in excel_inputs"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
                raise InputDataError(error_msg)

            enhanced_log(log_callback, f"Processing {num_walls} walls for individual pile requirements", 'INFO')
            wall_reqs, wall_warnings = _process_wall_requirements(
                group_elements['walls'],
                excel_inputs.InputLoadWall,
                pile_capacity,
                max_wall_end_pile_distance,
                log_callback
            )
            element_pile_reqs.update(wall_reqs)
            warnings.extend(wall_warnings)
            enhanced_log(log_callback, f"Processed {len(wall_reqs)} wall requirements successfully", 'INFO')
        else:
            enhanced_log(log_callback, "No walls to process for individual pile requirements", 'DEBUG')

        if not element_pile_reqs:
            error_msg = "No valid elements could be processed for pile requirements"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
            raise LoadCalculationError(error_msg)

        # Calculate summary statistics
        enhanced_log(log_callback, "Calculating summary statistics for individual pile requirements", 'DEBUG')
        total_individual_piles = sum(req.get('n_piles_required', 0) for req in element_pile_reqs.values())
        total_individual_load = sum(req.get('load_kn', 0) for req in element_pile_reqs.values())

        log_performance_metric(log_callback, "total_individual_piles", total_individual_piles, "piles")
        log_performance_metric(log_callback, "total_individual_load", total_individual_load, "kN")
        log_performance_metric(log_callback, "elements_processed", len(element_pile_reqs), "elements")

        enhanced_log(log_callback, f"Individual pile requirements calculation completed:", 'INFO')
        enhanced_log(log_callback, f"  Total elements processed: {len(element_pile_reqs)}", 'INFO')
        enhanced_log(log_callback, f"  Total individual piles required: {total_individual_piles}", 'INFO')
        enhanced_log(log_callback, f"  Total individual load: {total_individual_load:.2f} kN", 'INFO')

        if warnings:
            enhanced_log(log_callback, f"Individual pile calculation completed with {len(warnings)} warnings", 'WARNING')

        log_validation_result(log_callback, "individual_pile_calculation", True,
                            f"{len(element_pile_reqs)} elements processed successfully")
        log_function_exit(log_callback, "calculate_individual_pile_requirements",
                         f"{len(element_pile_reqs)} elements")
        return element_pile_reqs, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_individual_pile_requirements")
        log_function_exit(log_callback, "calculate_individual_pile_requirements", "error")
        raise


def _process_column_requirements(columns: List, df, pile_capacity: float,
                               max_distance: float, log_callback=None) -> Tuple[Dict[str, Dict], List[str]]:
    """Process column pile requirements."""
    log_function_entry(log_callback, "_process_column_requirements",
                      columns_count=len(columns), pile_capacity=pile_capacity, max_distance=max_distance)

    try:
        enhanced_log(log_callback, f"Processing {len(columns)} columns for pile requirements", 'DEBUG')

        if not columns:
            error_msg = "No columns provided for processing"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_column_requirements", "error")
            raise LoadCalculationError(error_msg)

        if df.empty:
            error_msg = "Column DataFrame is empty"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_column_requirements", "error")
            raise LoadCalculationError(error_msg)

        warnings = []
        reqs = {}
        processed_count = 0
        failed_columns = []

        for i, col_data in enumerate(columns):
            if not col_data or len(col_data) < 4:
                warning_msg = f"Invalid column data format at index {i} - insufficient data"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            col_name, x, y, z = col_data[:4]

            if not col_name or not col_name.strip():
                warning_msg = f"Empty column name found at index {i}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            # Validate coordinates
            try:
                x_coord = float(x)
                y_coord = float(y)
                z_coord = float(z)
            except (ValueError, TypeError):
                warning_msg = f"Invalid coordinates for column {col_name}: ({x}, {y}, {z})"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            try:
                enhanced_log(log_callback, f"Processing column {i+1}/{len(columns)}: '{col_name}' at ({x_coord:.2f}, {y_coord:.2f})", 'DEBUG')

                load_kn, load_warnings = calculate_element_load(df, col_name, log_callback=log_callback)
                warnings.extend(load_warnings)

                n_piles_required = max(1, ceil(load_kn / pile_capacity))
                load_per_pile = load_kn / n_piles_required
                efficiency = (load_per_pile / pile_capacity) * 100

                # Log detailed calculation steps
                enhanced_log(log_callback, f"Column calculation: {load_kn:.2f} kN ÷ {pile_capacity:.2f} kN = {load_kn/pile_capacity:.3f} → {n_piles_required} piles", 'DEBUG')

                reqs[col_name] = {
                    'type': 'column',
                    'position': (x_coord, y_coord),
                    'load_kn': load_kn,
                    'n_piles_required': n_piles_required,
                    'load_per_pile': load_per_pile,
                    'efficiency_percent': efficiency,
                    'max_distance': max_distance
                }
                processed_count += 1

                # Log efficiency warning if needed
                if efficiency > 100:
                    enhanced_log(log_callback, f"WARNING: Column '{col_name}' efficiency {efficiency:.1f}% exceeds 100%", 'WARNING')

                enhanced_log(log_callback, f"Column '{col_name}': {load_kn:.2f} kN load, {n_piles_required} piles required, {efficiency:.1f}% efficiency", 'DEBUG')

            except LoadCalculationError as e:
                failed_columns.append(col_name)
                error_msg = f"Failed to process column {col_name}: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "_process_column_requirements", "error")
                raise LoadCalculationError(error_msg)

        if processed_count == 0:
            error_msg = f"No valid columns could be processed from {len(columns)} provided"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_column_requirements", "error")
            raise LoadCalculationError(error_msg)

        success_rate = (processed_count / len(columns)) * 100
        total_column_load = sum(req.get('load_kn', 0) for req in reqs.values())
        total_column_piles = sum(req.get('n_piles_required', 0) for req in reqs.values())
        avg_efficiency = sum(req.get('efficiency_percent', 0) for req in reqs.values()) / len(reqs) if reqs else 0

        log_performance_metric(log_callback, "column_processing_success_rate", success_rate, "%")
        log_performance_metric(log_callback, "total_column_load", total_column_load, "kN")
        log_performance_metric(log_callback, "total_column_piles", total_column_piles, "piles")
        log_performance_metric(log_callback, "average_column_efficiency", avg_efficiency, "%")

        enhanced_log(log_callback, f"Column requirements processing completed: {processed_count}/{len(columns)} columns processed successfully ({success_rate:.1f}% success rate)", 'INFO')
        enhanced_log(log_callback, f"Column summary: {total_column_load:.2f} kN total load, {total_column_piles} total piles, {avg_efficiency:.1f}% avg efficiency", 'INFO')

        if failed_columns:
            enhanced_log(log_callback, f"Failed column requirements: {', '.join(failed_columns)}", 'WARNING')

        log_validation_result(log_callback, "column_requirements_processing", processed_count > 0,
                            f"Processed {processed_count} columns successfully")
        log_function_exit(log_callback, "_process_column_requirements", f"{processed_count} columns")
        return reqs, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "_process_column_requirements")
        log_function_exit(log_callback, "_process_column_requirements", "error")
        raise


def _process_wall_requirements(walls: List, df, pile_capacity: float,
                             max_distance: float, log_callback=None) -> Tuple[Dict[str, Dict], List[str]]:
    """Process wall pile requirements."""
    log_function_entry(log_callback, "_process_wall_requirements",
                      walls_count=len(walls), pile_capacity=pile_capacity, max_distance=max_distance)

    try:
        enhanced_log(log_callback, f"Processing {len(walls)} walls for pile requirements", 'DEBUG')

        if not walls:
            error_msg = "No walls provided for processing"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_wall_requirements", "error")
            raise LoadCalculationError(error_msg)

        if df.empty:
            error_msg = "Wall DataFrame is empty"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_wall_requirements", "error")
            raise LoadCalculationError(error_msg)

        warnings = []
        reqs = {}
        processed_count = 0
        failed_walls = []

        for i, wall_data in enumerate(walls):
            if not wall_data or len(wall_data) < 3:
                warning_msg = f"Invalid wall data format at index {i} - insufficient data"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            wall_name = wall_data[0]
            wall_points = wall_data[1]

            if not wall_name or not wall_name.strip():
                warning_msg = f"Empty wall name found at index {i}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            if not wall_points or not isinstance(wall_points, list):
                warning_msg = f"Invalid or missing points for wall {wall_name}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            # Validate wall points
            try:
                validated_points = []
                for j, point in enumerate(wall_points):
                    if not isinstance(point, (list, tuple)) or len(point) < 2:
                        raise ValueError(f"Invalid point format at index {j}")
                    x_coord = float(point[0])
                    y_coord = float(point[1])
                    validated_points.append((x_coord, y_coord))

                # Calculate wall length for logging
                wall_length = 0.0
                for j in range(len(validated_points) - 1):
                    x1, y1 = validated_points[j]
                    x2, y2 = validated_points[j + 1]
                    segment_length = ((x2 - x1)**2 + (y2 - y1)**2)**0.5
                    wall_length += segment_length

            except (ValueError, TypeError) as e:
                warning_msg = f"Invalid wall points for {wall_name}: {e}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            try:
                enhanced_log(log_callback, f"Processing wall {i+1}/{len(walls)}: '{wall_name}' with {len(validated_points)} points, length {wall_length:.2f}m", 'DEBUG')

                load_kn, load_warnings = calculate_element_load(df, wall_name, log_callback=log_callback)
                warnings.extend(load_warnings)

                n_piles_required = max(1, ceil(load_kn / pile_capacity))
                load_per_pile = load_kn / n_piles_required
                efficiency = (load_per_pile / pile_capacity) * 100

                # Log detailed calculation steps
                enhanced_log(log_callback, f"Wall calculation: {load_kn:.2f} kN ÷ {pile_capacity:.2f} kN = {load_kn/pile_capacity:.3f} → {n_piles_required} piles", 'DEBUG')
                enhanced_log(log_callback, f"Wall density: {n_piles_required} piles / {wall_length:.2f}m = {n_piles_required/wall_length:.2f} piles/m", 'DEBUG')

                reqs[wall_name] = {
                    'type': 'wall',
                    'points': validated_points,
                    'length': wall_length,
                    'load_kn': load_kn,
                    'n_piles_required': n_piles_required,
                    'load_per_pile': load_per_pile,
                    'efficiency_percent': efficiency,
                    'max_distance': max_distance
                }
                processed_count += 1

                # Log efficiency warning if needed
                if efficiency > 100:
                    enhanced_log(log_callback, f"WARNING: Wall '{wall_name}' efficiency {efficiency:.1f}% exceeds 100%", 'WARNING')

                enhanced_log(log_callback, f"Wall '{wall_name}': {load_kn:.2f} kN load, {n_piles_required} piles required, {efficiency:.1f}% efficiency", 'DEBUG')

            except LoadCalculationError as e:
                failed_walls.append(wall_name)
                error_msg = f"Failed to process wall {wall_name}: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "_process_wall_requirements", "error")
                raise LoadCalculationError(error_msg)

        if processed_count == 0:
            error_msg = f"No valid walls could be processed from {len(walls)} provided"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_wall_requirements", "error")
            raise LoadCalculationError(error_msg)

        success_rate = (processed_count / len(walls)) * 100
        total_wall_load = sum(req.get('load_kn', 0) for req in reqs.values())
        total_wall_piles = sum(req.get('n_piles_required', 0) for req in reqs.values())
        total_wall_length = sum(req.get('length', 0) for req in reqs.values())
        avg_efficiency = sum(req.get('efficiency_percent', 0) for req in reqs.values()) / len(reqs) if reqs else 0
        pile_density = total_wall_piles / total_wall_length if total_wall_length > 0 else 0

        log_performance_metric(log_callback, "wall_processing_success_rate", success_rate, "%")
        log_performance_metric(log_callback, "total_wall_load", total_wall_load, "kN")
        log_performance_metric(log_callback, "total_wall_piles", total_wall_piles, "piles")
        log_performance_metric(log_callback, "total_wall_length", total_wall_length, "m")
        log_performance_metric(log_callback, "average_wall_efficiency", avg_efficiency, "%")
        log_performance_metric(log_callback, "wall_pile_density", pile_density, "piles/m")

        enhanced_log(log_callback, f"Wall requirements processing completed: {processed_count}/{len(walls)} walls processed successfully ({success_rate:.1f}% success rate)", 'INFO')
        enhanced_log(log_callback, f"Wall summary: {total_wall_load:.2f} kN total load, {total_wall_piles} total piles, {total_wall_length:.2f}m total length", 'INFO')
        enhanced_log(log_callback, f"Wall metrics: {avg_efficiency:.1f}% avg efficiency, {pile_density:.2f} piles/m density", 'INFO')

        if failed_walls:
            enhanced_log(log_callback, f"Failed wall requirements: {', '.join(failed_walls)}", 'WARNING')

        log_validation_result(log_callback, "wall_requirements_processing", processed_count > 0,
                            f"Processed {processed_count} walls successfully")
        log_function_exit(log_callback, "_process_wall_requirements", f"{processed_count} walls")
        return reqs, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "_process_wall_requirements")
        log_function_exit(log_callback, "_process_wall_requirements", "error")
        raise

