﻿"""
Text Placement Manager

Manages text placement to prevent overlaps in DXF drawings.
"""

from typing import List, Tuple, Optional, Callable

from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    log_algorithm_step,
    create_timed_logger
)


class TextPlacementManager:
    """Manages text placement to prevent overlaps in DXF drawings."""
    
    def __init__(self, text_height: float = 0.4, min_spacing: float = 0.2, 
                 log_callback: Optional[Callable] = None):
        log_function_entry(log_callback, "TextPlacementManager.__init__",
                          text_height=text_height, min_spacing=min_spacing)
        
        try:
            enhanced_log(log_callback, "=== INITIALIZING TEXT PLACEMENT MANAGER ===", 'INFO')
            
            # Validate input parameters
            if text_height <= 0:
                enhanced_log(log_callback, f"Invalid text height: {text_height}, using default 0.4", 'WARNING')
                log_constraint_check(log_callback, "Valid text height", text_height, "> 0", False)
                text_height = 0.4
            else:
                log_constraint_check(log_callback, "Valid text height", text_height, "> 0", True)
            
            if min_spacing < 0:
                enhanced_log(log_callback, f"Invalid min spacing: {min_spacing}, using default 0.2", 'WARNING')
                log_constraint_check(log_callback, "Valid min spacing", min_spacing, ">= 0", False)
                min_spacing = 0.2
            else:
                log_constraint_check(log_callback, "Valid min spacing", min_spacing, ">= 0", True)
            
            self.text_positions = []  # List of (x, y, width, height) tuples
            self.text_height = text_height
            self.min_spacing = min_spacing
            self.log_callback = log_callback
            
            log_calculation_result(log_callback, "Default text height", self.text_height, "m")
            log_calculation_result(log_callback, "Minimum spacing", self.min_spacing, "m")
            log_calculation_result(log_callback, "Initial text positions count", len(self.text_positions))
            
            enhanced_log(log_callback, f"TextPlacementManager initialized with height={text_height:.3f}m, spacing={min_spacing:.3f}m", 'INFO')
            log_validation_result(log_callback, "TextPlacementManager initialization", True,
                                "Successfully initialized with validated parameters")
            
            log_function_exit(log_callback, "TextPlacementManager.__init__")
            
        except Exception as e:
            log_error_with_context(log_callback, e, "TextPlacementManager.__init__")
            enhanced_log(log_callback, f"Failed to initialize TextPlacementManager: {e}", 'ERROR')
            raise
    
    def estimate_text_width(self, text: str, height: float) -> float:
        """Estimate text width based on character count and height."""
        log_function_entry(self.log_callback, "estimate_text_width", 
                          text=text, height=height)
        
        try:
            enhanced_log(self.log_callback, f"=== ESTIMATING TEXT WIDTH ===", 'DEBUG')
            
            # Validate input parameters
            if not isinstance(text, str):
                enhanced_log(self.log_callback, f"Invalid text type: {type(text)}, converting to string", 'WARNING')
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", False)
                text = str(text)
            else:
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", True)
            
            if height <= 0:
                enhanced_log(self.log_callback, f"Invalid height: {height}, using default text height", 'WARNING')
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", False)
                height = self.text_height
            else:
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", True)
            
            log_calculation_result(self.log_callback, "Text content", f"'{text}'")
            log_calculation_result(self.log_callback, "Text length", len(text), "characters")
            log_calculation_result(self.log_callback, "Text height", height, "m")
            
            # Rough estimation: width = height * 0.6 * character_count
            log_algorithm_step(self.log_callback, "Text width estimation", "Calculating width using formula: height * 0.6 * character_count")
            width_factor = 0.6
            estimated_width = height * width_factor * len(text)
            
            log_calculation_result(self.log_callback, "Width factor", width_factor)
            log_calculation_result(self.log_callback, "Estimated width", estimated_width, "m")
            
            enhanced_log(self.log_callback, f"Estimated text width: {estimated_width:.4f}m for '{text}' (height: {height:.3f}m)", 'DEBUG')
            log_validation_result(self.log_callback, "Text width estimation", True,
                                f"Successfully estimated width for {len(text)} characters")
            
            log_function_exit(self.log_callback, "estimate_text_width", result=estimated_width)
            return estimated_width
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "estimate_text_width")
            enhanced_log(self.log_callback, f"Failed to estimate text width: {e}", 'ERROR')
            log_function_exit(self.log_callback, "estimate_text_width", result=0.0)
            return 0.0
    
    def check_overlap(self, x: float, y: float, text: str, height: float) -> bool:
        """Check if text at given position would overlap with existing text."""
        log_function_entry(self.log_callback, "check_overlap", 
                          x=x, y=y, text=text, height=height)
        
        try:
            enhanced_log(self.log_callback, f"=== CHECKING TEXT OVERLAP ===", 'DEBUG')
            
            # Validate input parameters
            if not isinstance(text, str):
                enhanced_log(self.log_callback, f"Invalid text type: {type(text)}, converting to string", 'WARNING')
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", False)
                text = str(text)
            else:
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", True)
            
            if height <= 0:
                enhanced_log(self.log_callback, f"Invalid height: {height}, using default text height", 'WARNING')
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", False)
                height = self.text_height
            else:
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", True)
            
            enhanced_log(self.log_callback, f"Checking overlap for text '{text}' at ({x:.3f}, {y:.3f}) with height {height:.3f}m", 'DEBUG')
            
            # Estimate text width and calculate bounding box
            log_algorithm_step(self.log_callback, "Overlap checking", "Estimating text width")
            width = self.estimate_text_width(text, height)
            log_calculation_result(self.log_callback, "Estimated text width", width, "m")
            
            # Define bounding box for new text (with spacing buffer)
            log_algorithm_step(self.log_callback, "Overlap checking", "Calculating bounding box with spacing buffer")
            new_left = x - width/2 - self.min_spacing
            new_right = x + width/2 + self.min_spacing
            new_bottom = y - height/2 - self.min_spacing
            new_top = y + height/2 + self.min_spacing
            
            log_calculation_result(self.log_callback, "New text bounding box", 
                                 f"left={new_left:.3f}, right={new_right:.3f}, bottom={new_bottom:.3f}, top={new_top:.3f}")
            
            # Check against all existing text positions
            log_algorithm_step(self.log_callback, "Overlap checking", f"Checking against {len(self.text_positions)} existing text positions")
            overlaps_found = 0
            
            for i, (pos_x, pos_y, pos_width, pos_height) in enumerate(self.text_positions):
                existing_left = pos_x - pos_width/2
                existing_right = pos_x + pos_width/2
                existing_bottom = pos_y - pos_height/2
                existing_top = pos_y + pos_height/2
                
                enhanced_log(self.log_callback, 
                           f"  Checking against text {i}: position=({pos_x:.3f}, {pos_y:.3f}), "
                           f"bounds=[{existing_left:.3f}, {existing_right:.3f}, {existing_bottom:.3f}, {existing_top:.3f}]", 'DEBUG')
                
                # Check for overlap
                has_overlap = not (new_right < existing_left or new_left > existing_right or 
                                 new_top < existing_bottom or new_bottom > existing_top)
                
                if has_overlap:
                    overlaps_found += 1
                    enhanced_log(self.log_callback, f"  OVERLAP DETECTED with existing text {i}", 'DEBUG')
                    log_validation_result(self.log_callback, f"Text overlap check {i}", False, 
                                        "Overlap detected with existing text")
                    log_function_exit(self.log_callback, "check_overlap", result=True)
                    return True
                else:
                    enhanced_log(self.log_callback, f"  No overlap with existing text {i}", 'DEBUG')
            
            log_calculation_result(self.log_callback, "Total overlaps found", overlaps_found)
            enhanced_log(self.log_callback, f"No overlaps found for text '{text}' at ({x:.3f}, {y:.3f})", 'DEBUG')
            log_validation_result(self.log_callback, "Text overlap check", True, 
                                f"No overlaps detected among {len(self.text_positions)} existing texts")
            
            log_function_exit(self.log_callback, "check_overlap", result=False)
            return False
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "check_overlap")
            enhanced_log(self.log_callback, f"Failed to check overlap: {e}", 'ERROR')
            log_function_exit(self.log_callback, "check_overlap", result=True)
            return True  # Return True (overlap) on error to be safe
    
    def find_non_overlapping_position(self, x: float, y: float, text: str, height: float) -> Tuple[float, float]:
        """Find a non-overlapping position near the desired position."""
        log_function_entry(self.log_callback, "find_non_overlapping_position", 
                          x=x, y=y, text=text, height=height)
        
        try:
            enhanced_log(self.log_callback, f"=== FINDING NON-OVERLAPPING POSITION ===", 'DEBUG')
            
            # Validate input parameters
            if not isinstance(text, str):
                enhanced_log(self.log_callback, f"Invalid text type: {type(text)}, converting to string", 'WARNING')
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", False)
                text = str(text)
            else:
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", True)
            
            if height <= 0:
                enhanced_log(self.log_callback, f"Invalid height: {height}, using default text height", 'WARNING')
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", False)
                height = self.text_height
            else:
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", True)
            
            enhanced_log(self.log_callback, f"Finding position for text '{text}' near ({x:.3f}, {y:.3f}) with height {height:.3f}m", 'DEBUG')
            
            # Calculate width for offset calculations
            log_algorithm_step(self.log_callback, "Position finding", "Calculating text width for offset calculations")
            width = self.estimate_text_width(text, height)
            log_calculation_result(self.log_callback, "Text width for offsets", width, "m")
            
            # Try the original position first
            log_algorithm_step(self.log_callback, "Position finding", "Testing original position")
            if not self.check_overlap(x, y, text, height):
                enhanced_log(self.log_callback, f"Original position ({x:.3f}, {y:.3f}) is available", 'DEBUG')
                log_validation_result(self.log_callback, "Original position available", True,
                                    f"No overlap at original position")
                log_function_exit(self.log_callback, "find_non_overlapping_position", result=(x, y))
                return x, y
            
            enhanced_log(self.log_callback, f"Original position ({x:.3f}, {y:.3f}) overlaps, trying alternative positions", 'DEBUG')
            
            # Try positions in a spiral pattern around the original point
            log_algorithm_step(self.log_callback, "Position finding", "Generating spiral pattern offsets")
            offsets = [
                (0, height + self.min_spacing),      # Above
                (0, -(height + self.min_spacing)),   # Below
                (width + self.min_spacing, 0),       # Right
                (-(width + self.min_spacing), 0),    # Left
                (width + self.min_spacing, height + self.min_spacing),    # Top-right
                (-(width + self.min_spacing), height + self.min_spacing), # Top-left
                (width + self.min_spacing, -(height + self.min_spacing)), # Bottom-right
                (-(width + self.min_spacing), -(height + self.min_spacing)), # Bottom-left
            ]
            
            log_calculation_result(self.log_callback, "Total offset positions to test", len(offsets))
            enhanced_log(self.log_callback, f"Testing {len(offsets)} alternative positions in spiral pattern", 'DEBUG')
            
            positions_tested = 0
            for i, (dx, dy) in enumerate(offsets):
                test_x, test_y = x + dx, y + dy
                positions_tested += 1
                
                enhanced_log(self.log_callback, f"  Testing position {i+1}: ({test_x:.3f}, {test_y:.3f}) [offset: ({dx:.3f}, {dy:.3f})]", 'DEBUG')
                
                if not self.check_overlap(test_x, test_y, text, height):
                    enhanced_log(self.log_callback, f"Found non-overlapping position at ({test_x:.3f}, {test_y:.3f}) after {positions_tested} attempts", 'DEBUG')
                    log_calculation_result(self.log_callback, "Positions tested", positions_tested)
                    log_validation_result(self.log_callback, "Alternative position found", True,
                                        f"Position {i+1} successful: ({test_x:.3f}, {test_y:.3f})")
                    log_function_exit(self.log_callback, "find_non_overlapping_position", result=(test_x, test_y))
                    return test_x, test_y
                else:
                    enhanced_log(self.log_callback, f"  Position {i+1} also overlaps", 'DEBUG')
            
            # If no good position found, use the original position anyway
            enhanced_log(self.log_callback, f"No non-overlapping position found after {positions_tested} attempts, using original position", 'WARNING')
            log_calculation_result(self.log_callback, "Total positions tested", positions_tested)
            log_validation_result(self.log_callback, "Alternative position search", False,
                                f"No non-overlapping position found, falling back to original")
            log_performance_metric(self.log_callback, "Position finding success rate", 0, "%")
            
            log_function_exit(self.log_callback, "find_non_overlapping_position", result=(x, y))
            return x, y
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "find_non_overlapping_position")
            enhanced_log(self.log_callback, f"Failed to find non-overlapping position: {e}", 'ERROR')
            log_function_exit(self.log_callback, "find_non_overlapping_position", result=(x, y))
            return x, y  # Return original position on error
    
    def add_text_position(self, x: float, y: float, text: str, height: float) -> Tuple[float, float]:
        """Add a text position and return the final (possibly adjusted) coordinates."""
        log_function_entry(self.log_callback, "add_text_position", 
                          x=x, y=y, text=text, height=height)
        
        try:
            enhanced_log(self.log_callback, f"=== ADDING TEXT POSITION ===", 'DEBUG')
            
            # Validate input parameters
            if not isinstance(text, str):
                enhanced_log(self.log_callback, f"Invalid text type: {type(text)}, converting to string", 'WARNING')
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", False)
                text = str(text)
            else:
                log_constraint_check(self.log_callback, "Valid text type", type(text).__name__, "str", True)
            
            if height <= 0:
                enhanced_log(self.log_callback, f"Invalid height: {height}, using default text height", 'WARNING')
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", False)
                height = self.text_height
            else:
                log_constraint_check(self.log_callback, "Valid height", height, "> 0", True)
            
            enhanced_log(self.log_callback, f"Adding text position for '{text}' at ({x:.3f}, {y:.3f}) with height {height:.3f}m", 'DEBUG')
            log_calculation_result(self.log_callback, "Current text positions count", len(self.text_positions))
            
            with create_timed_logger(self.log_callback, f"Text position addition") as position_timer:
                # Find non-overlapping position
                log_algorithm_step(self.log_callback, "Text position addition", "Finding non-overlapping position")
                final_x, final_y = self.find_non_overlapping_position(x, y, text, height)
                
                log_calculation_result(self.log_callback, "Final position", (final_x, final_y))
                
                # Calculate final width and add to positions list
                log_algorithm_step(self.log_callback, "Text position addition", "Calculating final width and registering position")
                width = self.estimate_text_width(text, height)
                
                # Check if position was adjusted
                position_adjusted = (final_x != x or final_y != y)
                if position_adjusted:
                    offset_x = final_x - x
                    offset_y = final_y - y
                    enhanced_log(self.log_callback, f"Position adjusted by offset ({offset_x:.3f}, {offset_y:.3f})", 'DEBUG')
                    log_calculation_result(self.log_callback, "Position offset", (offset_x, offset_y))
                    log_validation_result(self.log_callback, "Position adjustment", True,
                                        f"Adjusted from ({x:.3f}, {y:.3f}) to ({final_x:.3f}, {final_y:.3f})")
                else:
                    enhanced_log(self.log_callback, f"Original position ({x:.3f}, {y:.3f}) used without adjustment", 'DEBUG')
                    log_validation_result(self.log_callback, "Position adjustment", False,
                                        "Original position used without modification")
                
                # Register the position
                self.text_positions.append((final_x, final_y, width, height))
                new_position_count = len(self.text_positions)
                
                log_calculation_result(self.log_callback, "Registered text width", width, "m")
                log_calculation_result(self.log_callback, "Registered text height", height, "m")
                log_calculation_result(self.log_callback, "New text positions count", new_position_count)
                
                enhanced_log(self.log_callback, f"Text position registered: '{text}' at ({final_x:.3f}, {final_y:.3f}), "
                           f"size: {width:.3f}m x {height:.3f}m", 'DEBUG')
                
                log_validation_result(self.log_callback, "Text position registration", True,
                                    f"Successfully registered position {new_position_count}")
                log_performance_metric(self.log_callback, "Text position success rate", 100, "%")
            
            log_function_exit(self.log_callback, "add_text_position", result=(final_x, final_y))
            return final_x, final_y
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "add_text_position")
            enhanced_log(self.log_callback, f"Failed to add text position: {e}", 'ERROR')
            log_performance_metric(self.log_callback, "Text position success rate", 0, "%")
            log_function_exit(self.log_callback, "add_text_position", result=(x, y))
            return x, y  # Return original position on error

