---
type: "always_apply"
---

General Workflow
1. Use to-do lists for tasks to stay organized and break down complex projects into manageable steps.
2. Utilize the Serena MCP for managing, reading, and writing project memory.
3. Leverage the functions in Serena MCP whenever possible to streamline your development process.
4. Employ the Sequential Thinking MCP for planning and executing tasks to ensure a logical and structured approach.
5. Use Context7 MCP for reading Python library documentation to avoid building functions that are in the library.
6. Always update the Serena MCP memory with new information and delete old information that is no longer relevant.

Code Quality and Validation
1. Always debug your code thoroughly.
2. Strive to make your code modular, readable, maintainable, and efficient by following good coding practices.
3. Delete test files after testing to keep your project directory clean and organized.