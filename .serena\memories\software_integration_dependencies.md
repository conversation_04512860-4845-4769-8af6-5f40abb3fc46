# Software Integration & External Dependencies

## SAFE Software Integration
The system supports two versions of SAFE (CSI's foundation analysis software):
- **SAFE16**: Legacy version with F2K file format support
- **SAFE22**: Current version with enhanced capabilities and GUID-based object management

### Integration Methods
1. **API Integration**: Direct connection to SAFE through .NET API
2. **File-based**: F2K and Excel file import/export
3. **Database**: Direct SQLite database reading from SAFE results

## ETABS Software Integration
- **Version Support**: Compatible with multiple ETABS versions
- **Data Exchange**: Joint reactions, pier forces, story data extraction
- **Load Transfer**: Automatic conversion of ETABS loads to foundation loads

## Key External Libraries
- **pandas**: DataFrame operations for all data processing
- **numpy**: Numerical computations for engineering calculations
- **scipy**: Scientific computing for optimization algorithms
- **matplotlib**: Plotting for analysis visualization
- **openpyxl**: Excel file operations
- **sqlite3**: Database operations for results storage
- **tkinter**: GUI framework for all user interfaces
- **requests**: HTTP operations for user authorization
- **smtplib**: Email operations for authentication
- **ezdxf**: DXF file generation for CAD visualization

## File Format Support
- **Excel**: Primary data exchange format (.xlsx)
- **SQLite**: Results database storage
- **F2K**: SAFE model import/export format
- **DXF**: CAD visualization output
- **CSV**: Alternative data export format
- **JSON**: Configuration and logging data

## Optimization Libraries
- **DEAP**: Genetic algorithms for pile layout optimization
- **NSGA-III**: Multi-objective optimization implementation
- **Custom algorithms**: Enhanced clustering and layout generation

## Operating System Support
- **Primary**: Windows (PowerShell integration)
- **Dependencies**: .NET Framework for SAFE API
- **File paths**: Windows-style path handling throughout