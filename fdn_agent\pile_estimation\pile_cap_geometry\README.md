# Pile Cap Geometry Package

This package is responsible for the generation, manipulation, and validation of pile cap geometries within the pile foundation estimation system.

## Overview

The `pile_cap_geometry` package handles all aspects related to defining the physical boundaries and characteristics of pile caps. It includes functionalities for creating geometric representations, transforming coordinates, extracting structural data, and ensuring the validity of generated shapes.

## **Code Organization (Post-Integration)**

**This package has been streamlined and integrated with the centralized utils and types modules:**

### **Functions Now Located in `fdn_agent.pile_estimation.types`:**
- `PileCapConfig` - Configuration class for pile cap parameters
- `LocalCoordinateSystem` - Local coordinate system data structure

### **Functions Now Located in `fdn_agent.pile_estimation.utils.coordinate_utils`:**
- `find_minimum_area_bounding_rectangle()` - Minimum bounding rectangle calculation
- `global_to_local_coordinates()` - Global to local coordinate transformation
- `local_to_global_coordinates()` - Local to global coordinate transformation  
- `generate_local_grid_positions()` - Grid-based pile position generation

### **Functions Now Located in `fdn_agent.pile_estimation.utils.validation_utils`:**
- `validate_pile_cap_polygon()` - Pile cap polygon validation
- `clip_to_site_boundary()` - Site boundary clipping

### **Functions Now Located in `fdn_agent.pile_estimation.utils.geometry_utils`:**
- `extract_all_structural_points()` - Extract structural element points
- `extract_wall_segments_from_excel()` - Extract wall segments from Excel
- `group_continuous_walls()` - Group continuous wall segments
- `create_continuous_path()` - Create continuous paths from wall groups

## Current Package Structure

- `core_geometry.py`: Contains the main business logic functions for creating pile cap polygons based on structural elements and pile locations
- `pile_cap_geometry.py`: Direct imports from the integrated modules (no longer a compatibility wrapper)
- `__init__.py`: Exports key functions from the integrated modules
- `README.md`: This documentation file

## Usage

**Import directly from centralized locations (recommended):**

```python
# Import from centralized locations
from fdn_agent.pile_estimation.types import PileCapConfig, LocalCoordinateSystem
from fdn_agent.pile_estimation.utils.coordinate_utils import find_minimum_area_bounding_rectangle
from fdn_agent.pile_estimation.utils.validation_utils import validate_pile_cap_polygon
from fdn_agent.pile_estimation.utils.geometry_utils import extract_all_structural_points
```

**Or import through this package (also works):**

```python
# Import through pile_cap_geometry package
from fdn_agent.pile_estimation.pile_cap_geometry import (
    PileCapConfig,
    LocalCoordinateSystem,
    find_minimum_area_bounding_rectangle,
    validate_pile_cap_polygon,
    extract_all_structural_points
)
```

## Benefits of the Integration

- **Improved Code Organization**: Functions are in logical, centralized locations
- **Reduced Duplication**: Single source of truth for each function
- **Better Maintainability**: Related functions grouped together in utils modules
- **Cleaner Dependencies**: Clear separation between types, utilities, and business logic