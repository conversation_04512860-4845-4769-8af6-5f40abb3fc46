"""
Soil Spring Modeling Module for Pile Foundation Analysis

This module provides comprehensive functionality for generating lateral soil spring
coefficients for pile-soil interaction modeling in foundation engineering. It implements
established geotechnical engineering principles to calculate soil resistance and spring
stiffness values based on Standard Penetration Test (SPT) data, soil classification,
and pile geometry parameters.

The module is designed for integration with SAFE 22 finite element analysis software
and supports automated generation of soil spring properties for large pile groups
using parallel processing techniques. The soil springs represent the lateral resistance
of soil against pile deflection and are essential for accurate pile behavior prediction
under lateral loading conditions.

Key Features:
- SPT-based subgrade reaction coefficient calculation using established correlations
- Automatic nearest borehole assignment for pile locations without specified soil data
- Group reduction factor calculation accounting for pile spacing and interaction effects
- Pile geometry analysis supporting both vertical and raking (inclined) pile configurations
- Parallel processing capability for efficient handling of large pile groups
- Integration with SAFE 22 model generation workflow and Excel-based data management

Soil Mechanics Principles:
- Subgrade Reaction Method: Uses Winkler foundation model with discrete springs
- SPT Correlations: Implements standard correlations for granular soils (GS),
  normally consolidated clay (NCC), and organic soils (OS)
- Group Effects: Accounts for pile-to-pile interaction through reduction factors
- Depth Variation: Considers increasing soil stiffness with depth and confining pressure

Pile-Soil Interaction Modeling:
- Lateral Spring Generation: Creates discrete springs along pile length at specified intervals
- Soil Segmentation: Divides pile length into segments based on soil layer boundaries
- Spring Stiffness Calculation: Combines subgrade reaction, pile diameter, depth effects,
  and group reduction factors using the formula: k = nh × R × D × depth × Δz
- Coordinate System: Supports both global and local coordinate transformations for raking piles

Usage:
    This module is typically used as part of the automated pile foundation analysis workflow:

    >>> from build_fem.builder_soil_spring import gen_soil_spring
    >>> soil_springs = gen_soil_spring(excel_inputs, file_paths, progress_callback=callback)
    >>> print(f"Generated {len(soil_springs)} soil spring elements")

Dependencies:
    - numpy: Used for efficient numerical calculations and array operations
    - pandas: Used for DataFrame operations and geotechnical data management
    - multiprocessing: Used for parallel processing of large pile groups
    - tkinter: Used for GUI progress tracking and user interface integration
    - tqdm: Used for command-line progress visualization
    - build_fem_config: Contains configuration constants for Excel sheet names and file paths

Note:
    The module implements the subgrade reaction method (Winkler foundation model) which
    represents soil as a series of independent elastic springs. While this approach
    has limitations compared to continuum methods, it provides a practical and widely
    accepted solution for pile foundation analysis in structural engineering practice.

    All calculations follow established geotechnical engineering standards and are
    validated against industry practice for pile foundation design. The module
    maintains compatibility with SAFE 22 requirements and Excel-based workflows
    commonly used in foundation engineering projects.

Author: Foundation Automation System
Date: 2025
Version: Compatible with SAFE 22 and pile foundation automation workflows
"""

import tkinter as tk
from tkinter import ttk
import multiprocessing
from typing import Any, List, Tuple, Optional, Union

import numpy as np
import pandas as pd
import tqdm

from build_fem import build_fem_config


def nearest_borehole(x: float, y: float, df_borehole_loc: pd.DataFrame) -> str:
    """
    Find the nearest borehole to a given pile location using Euclidean distance.

    This function identifies the closest borehole to a pile location when no specific
    borehole is assigned to the pile. It uses efficient NumPy array operations to
    calculate distances and is essential for assigning appropriate soil properties
    to pile elements in the soil spring generation process.
    """
    # Convert borehole coordinates to NumPy array for efficient vectorized calculation
    borehole_coords = df_borehole_loc[['X (m)', 'Y (m)']].values
    point_coords = np.array([x, y])

    # Calculate squared Euclidean distances (avoiding square root for efficiency)
    # Distance² = (x₂-x₁)² + (y₂-y₁)²
    distances_sq = np.sum((borehole_coords - point_coords) ** 2, axis=1)

    # Find the index of the borehole with minimum distance
    nearest_idx = np.argmin(distances_sq)

    # Extract and return the name of the nearest borehole
    target_borehole = df_borehole_loc.iloc[nearest_idx]['Borehole']
    return target_borehole


def nearest_pile(x: float, y: float, true_z: float, pile_mark: str, pile_dia: float,
                df_pile: pd.DataFrame, direction: str = 'X') -> Tuple[Union[str, str], Union[float, str]]:
    """
    Find the nearest pile in a specified direction for group reduction factor calculation.

    This function identifies the closest pile to a target pile in either X or Y direction,
    accounting for raking (inclined) pile geometry through coordinate interpolation.
    The nearest pile distance is used to calculate group reduction factors that account
    for pile-to-pile interaction effects in soil spring stiffness calculations.
    """
    # Create working copy and handle missing bottom coordinates for vertical piles
    df_pile_copy = df_pile.copy()
    df_pile_copy['BX (m)'] = df_pile_copy['BX (m)'].fillna(df_pile_copy['X (m)'])
    df_pile_copy['BY (m)'] = df_pile_copy['BY (m)'].fillna(df_pile_copy['Y (m)'])

    # Calculate pile length for interpolation (avoid division by zero)
    delta_z = df_pile_copy['Pile Cap Bottom Level (mPD)'] - df_pile_copy['Founding Level (mPD)']

    # Initialize interpolated coordinates with top coordinates
    interp_x = df_pile_copy['X (m)'].values.copy()
    interp_y = df_pile_copy['Y (m)'].values.copy()

    # Perform linear interpolation for raking piles at the specified elevation
    mask_interp = delta_z != 0  # Only interpolate for piles with non-zero length
    ratio = (df_pile_copy['Pile Cap Bottom Level (mPD)'][mask_interp] - true_z) / delta_z[mask_interp]

    # Linear interpolation: coord = top + ratio × (bottom - top)
    interp_x[mask_interp] = (ratio * (df_pile_copy['BX (m)'][mask_interp] - df_pile_copy['X (m)'][mask_interp]) +
                            df_pile_copy['X (m)'][mask_interp])
    interp_y[mask_interp] = (ratio * (df_pile_copy['BY (m)'][mask_interp] - df_pile_copy['Y (m)'][mask_interp]) +
                            df_pile_copy['Y (m)'][mask_interp])

    # Calculate squared distances in horizontal plane (z-component is zero at same elevation)
    dist_sq = (interp_x - x) ** 2 + (interp_y - y) ** 2

    # Create DataFrame for distance analysis
    df_dist = pd.DataFrame({
        'Pile Mark': df_pile_copy['Pile Mark'],
        'DistanceSq': dist_sq,
        'InterpX': interp_x,
        'InterpY': interp_y
    })

    # Exclude the target pile from consideration
    df_dist = df_dist[df_dist['Pile Mark'] != pile_mark]

    # Apply direction-specific filtering based on influence zone
    if direction == 'X':
        # For X-direction search, find piles with similar Y-coordinate (within 2.5D)
        condition = ((df_dist['DistanceSq'] > 0) &
                    (np.abs(df_dist['InterpY'] - y) <= 2.5 * pile_dia))
    elif direction == 'Y':
        # For Y-direction search, find piles with similar X-coordinate (within 2.5D)
        condition = ((df_dist['DistanceSq'] > 0) &
                    (np.abs(df_dist['InterpX'] - x) <= 2.5 * pile_dia))
    else:
        # Invalid direction parameter
        return 'N/A', 'N/A'

    filtered_df = df_dist[condition]

    # Return results based on search outcome
    if filtered_df.empty:
        return 'N/A', 'N/A'
    else:
        # Find pile with minimum distance and calculate actual distance
        nearest_idx = filtered_df['DistanceSq'].idxmin()
        nearest_pile_mark = filtered_df.loc[nearest_idx, 'Pile Mark']
        nearest_pile_distance = np.sqrt(filtered_df.loc[nearest_idx, 'DistanceSq'])
        return nearest_pile_mark, nearest_pile_distance


def read_nh(borehole: str, df_borehole: pd.DataFrame, ground_lv: float, zero_lv: float,
           true_z_top: float, true_z_bot: float) -> Tuple[List, List, List, List, List, List, List]:
    """
    Extract subgrade reaction coefficients from borehole data for pile segment analysis.

    This function processes Standard Penetration Test (SPT) data from a specific borehole
    to generate soil property segments along a pile length. It creates discrete segments
    based on soil layer boundaries and calculates subgrade reaction coefficients (nh)
    for each segment using established SPT correlations.
    """
    # Filter borehole data for the target borehole
    df_target_bh = df_borehole[df_borehole['Borehole'] == borehole].copy()

    # Initialize elevation list with pile segment boundaries
    list_z = [true_z_top]

    # Add zero effective stress level if it falls within the pile segment
    if true_z_top > zero_lv > true_z_bot:
        list_z.append(zero_lv)
    list_z.append(true_z_bot)

    # Add SPT test levels that fall within the pile segment range
    spt_levels = df_target_bh['Start SPT Level (mPD)'].values
    relevant_spt_levels = spt_levels[(spt_levels < true_z_top) & (spt_levels > true_z_bot)]
    list_z.extend(relevant_spt_levels)

    # Sort unique elevations in descending order (top to bottom)
    list_z = sorted(list(set(list_z)), reverse=True)

    # Validate that sufficient points exist to create segments
    if len(list_z) < 2:
        # Return empty lists if insufficient data for segmentation
        return [], [], [], [], [], [], []

    # Process each segment between consecutive elevation points
    seg_data = []
    for i in range(len(list_z) - 1):
        true_z_start = list_z[i]      # Top of segment
        true_z_end = list_z[i+1]      # Bottom of segment
        true_z_center = (true_z_start + true_z_end) / 2  # Segment center

        # Calculate effective segment length (zero above zero effective stress level)
        dz = 0 if true_z_center >= zero_lv else true_z_start - true_z_end

        # Find matching soil properties for this segment
        # Match segments that fall within SPT layer boundaries
        condition = ((df_target_bh['Start SPT Level (mPD)'] >= true_z_start) &
                    (df_target_bh['End SPT Level (mPD)'] <= true_z_end))
        matching_row = df_target_bh[condition]

        # Extract soil properties or set to None if no match found
        if not matching_row.empty:
            soil_type = matching_row['Soil Type (GS/NCC/OS)'].iloc[0]
            spt_n_val = matching_row['SPTN Value'].iloc[0]
            # Calculate subgrade reaction coefficient (assuming saturated conditions)
            nh_val = subgrade_reaction(soil_type, spt_n_val, is_dry=False)
        else:
            # Handle missing soil data gracefully
            soil_type, spt_n_val, nh_val = None, None, None

        # Store segment data
        seg_data.append({
            'Seg_Z Start (mPD)': true_z_start,
            'Seg_Z End (mPD)': true_z_end,
            'Seg_Z Center (mPD)': true_z_center,
            'Ground Level (mPD)': ground_lv,
            'Ground Distance (m)': ground_lv - true_z_center,  # Depth below ground
            'dZ (m)': dz,
            'Soil Type (GS/NCC/OS)': soil_type,
            'SPTN Value': spt_n_val,
            'nh (kN/m3)': nh_val
        })

    # Return empty lists if no segments were created
    if not seg_data:
        return [], [], [], [], [], [], []

    # Convert to DataFrame for easier data manipulation
    df_seg = pd.DataFrame(seg_data)

    # Extract and format output lists with appropriate precision
    list_seg_out = [(round(float(s), 5), round(float(e), 5))
                   for s, e in zip(df_seg['Seg_Z Start (mPD)'], df_seg['Seg_Z End (mPD)'])]
    list_z_center_out = [round(float(item), 5) for item in df_seg['Seg_Z Center (mPD)'].values]
    list_dist_gl_out = [round(float(item), 5) for item in df_seg['Ground Distance (m)'].values]
    list_dz_out = [round(float(item), 5) for item in df_seg['dZ (m)'].values]
    list_soil_type_out = df_seg['Soil Type (GS/NCC/OS)'].values
    list_spt_n_out = [round(float(item), 5) if pd.notnull(item) else None
                     for item in df_seg['SPTN Value'].values]
    list_nh_out = df_seg['nh (kN/m3)'].values

    return (list_seg_out, list_z_center_out, list_dist_gl_out, list_dz_out,
            list_soil_type_out, list_spt_n_out, list_nh_out)


def subgrade_reaction(soil_type: str, spt_n: float, is_dry: bool = False) -> float:
    """
    Calculate subgrade reaction coefficient based on soil type and SPT N-value.

    This function implements established correlations between Standard Penetration Test
    (SPT) N-values and subgrade reaction coefficients (nh) for different soil types.
    The subgrade reaction coefficient represents the soil's resistance to lateral
    displacement and is fundamental to the Winkler foundation model used in pile
    analysis.
    """
    if soil_type == 'GS':  # Granular Soil (sand, gravel)
        # SPT N-value ranges for granular soil classification
        limits = [0, 4, 10.5, 30.5]

        # Subgrade reaction coefficients based on moisture condition
        if is_dry:
            # Dry granular soil coefficients (kN/m³)
            coefficients = [0, 2200, 6600, 17600]
        else:
            # Saturated granular soil coefficients (kN/m³)
            coefficients = [0, 1300, 4400, 10700]

        # Find appropriate coefficient based on SPT N-value
        index = len(limits) - 1  # Default to highest category
        for i in range(len(limits) - 1, -1, -1):
            if spt_n >= limits[i]:
                index = i
                break

        nh = coefficients[index]

    elif soil_type == 'NCC':  # Normally Consolidated Clay
        # Constant value for normally consolidated clay (independent of SPT N)
        nh = 350

    elif soil_type == 'OS':  # Organic Soil
        # Reduced value for organic soils due to high compressibility
        nh = 150

    else:
        # Unknown soil type - return zero for safety
        nh = 0

    return nh


def group_reduction_factor(pile_spacing: float, pile_dia: float) -> float:
    """
    Calculate group reduction factor for pile-to-pile interaction effects.

    This function calculates the reduction factor applied to soil spring stiffness
    to account for pile group interaction effects. When piles are closely spaced,
    they create overlapping stress zones in the soil, reducing the effective soil
    resistance compared to isolated pile behavior. This phenomenon is known as
    the "group effect" or "shadowing effect" in pile foundation engineering.
    """
    # Calculate spacing-to-diameter ratio
    ratio = pile_spacing / pile_dia

    # Apply group reduction criteria based on spacing ratio
    if ratio < 3:
        # Severe interaction: piles too close for effective individual resistance
        factor = 0
    elif ratio >= 8:
        # Minimal interaction: piles behave independently
        factor = 1
    else:
        # Moderate interaction: linear interpolation between limits
        # Formula: factor = 0.15 × ratio - 0.2
        # At ratio = 3: factor = 0.15 × 3 - 0.2 = 0.25 (but capped at 0)
        # At ratio = 8: factor = 0.15 × 8 - 0.2 = 1.0
        factor = ratio * 0.15 - 0.2

    return factor


def cal_spring(pile_name: str, df_pile: pd.DataFrame, borehole: str, df_borehole: pd.DataFrame,
              r_x: float, r_y: float, point_name: str, x: float, y: float, z: float,
              true_z: float, true_z_top: float, true_z_bot: float, ground_lv: float,
              zero_lv: float) -> List:
    """
    Calculate soil spring stiffness for a pile point using the Winkler foundation model.

    This function implements the core soil spring calculation for a single point along
    a pile length. It combines subgrade reaction coefficients, group reduction factors,
    pile geometry, and depth effects to calculate lateral spring stiffness values in
    both X and Y directions. The calculation follows the Winkler foundation model
    where soil is represented as independent elastic springs.
    """
    # Extract pile shaft diameter from pile database
    condition = df_pile['Pile Mark'] == pile_name
    dia_pile = float(df_pile.loc[condition, 'Pile Shaft Diameter (m)'].iloc[0])

    # Assign nearest borehole if not specified
    if pd.isna(borehole) or borehole == ' ':
        borehole = nearest_borehole(x, y, df_borehole)

    # Extract soil properties and segment data for the pile segment
    (list_seg, list_z_center, list_dist_gl, list_dz,
     list_soil_type, list_spt_n, list_nh) = read_nh(
        borehole, df_borehole, ground_lv, zero_lv, true_z_top, true_z_bot)

    # Find nearest pile in X-direction for group reduction factor
    npx_pile_mark, npx_dist = nearest_pile(x, y, true_z, pile_name, dia_pile, df_pile, direction='X')

    # Calculate or use provided reduction factor for X-direction
    if pd.isna(r_x) or r_x == ' ':
        if npx_dist != 'N/A':
            npx_dist = float(npx_dist)

        if str(npx_dist) == 'N/A':
            r_x = 1  # No nearby pile, full soil resistance
        else:
            r_x = group_reduction_factor(npx_dist, dia_pile)

    # Find nearest pile in Y-direction for group reduction factor
    npy_pile_mark, npy_dist = nearest_pile(x, y, true_z, pile_name, dia_pile, df_pile, direction='Y')

    # Calculate or use provided reduction factor for Y-direction
    if pd.isna(r_y) or r_y == ' ':
        if npy_dist != 'N/A':
            npy_dist = float(npy_dist)

        if str(npy_dist) == 'N/A':
            r_y = 1  # No nearby pile, full soil resistance
        else:
            r_y = group_reduction_factor(npy_dist, dia_pile)

    # Diameter factor (currently set to 1.0, legacy code shows potential scaling)
    # Historical note: factor_dia = (dia_pile / 1) ** 0.25 for diameter > 1m
    factor_dia = 1

    # Calculate spring stiffness by summing contributions from all soil segments
    spring_x = 0
    spring_y = 0
    for i in range(len(list_nh)):
        n_h = list_nh[i]              # Subgrade reaction coefficient (kN/m³)
        dist_from_gl = list_dist_gl[i] # Depth from ground level (m)
        dz = list_dz[i]               # Segment length (m)

        # Spring stiffness formula: k = nh × R × D_factor × depth × Δz
        spring_x += n_h * r_x * factor_dia * dist_from_gl * dz
        spring_y += n_h * r_y * factor_dia * dist_from_gl * dz

    # Return comprehensive analysis results
    return [point_name, dia_pile, x, y, z, ground_lv, npx_pile_mark, npx_dist,
            npy_pile_mark, npy_dist, borehole, true_z, list_seg, list_spt_n,
            list_soil_type, list_nh, r_x, r_y, factor_dia, list_z_center,
            list_dist_gl, list_dz, spring_x, spring_y]


def soil_spring_top(pile_name, x1, y1, z1, x2, y2, z2, d_step, cap_bot_lv, ground_lv, founding_lv, sleeve_lv, borehole,
                    r_x, r_y, df_borehole, df_pile, z, soil_spring_list):
    # FIRST POINT
    point_name = pile_name + '_T'
    x, y, z = x1, y1, z1
    true_z = cap_bot_lv

    soil_spring_list.append(
        [point_name, 'N/A', x, y, z, ground_lv, 'N/A', 'N/A', 'N/A', 'N/A', 'N/A',
         true_z, 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 0, 0])

    for step in [0.5, 1]:
        point_name = f'{pile_name}_{step}'
        # CONSIDERED RAKING PILES
        x = x1 - (x1 - x2) / (z1 - z2) * step
        y = y1 - (y1 - y2) / (z1 - z2) * step
        z = round(z1 - step, 1)
        true_z = float(cap_bot_lv) + z

        if step == 0.5:
            true_z_top = true_z + 0.5
            true_z_bot = true_z - 0.25
        else:
            true_z_top = true_z + 0.25
            true_z_bot = true_z - 0.5

        zero_lv = min(float(sleeve_lv), float(ground_lv))

        list_spring = cal_spring(pile_name, df_pile, borehole, df_borehole, r_x, r_y, point_name, x, y, z, true_z,
                                 true_z_top, true_z_bot, ground_lv, zero_lv)

        soil_spring_list.append(list_spring)

    return soil_spring_list


def soil_spring_middle(pile_name, x1, y1, z1, x2, y2, z2, d_step, cap_bot_lv, ground_lv, founding_lv, sleeve_lv,
                       borehole, r_x, r_y, df_borehole, df_pile, z, soil_spring_list):
    # MIDDLE POINTS
    # Define SPT data of manual specified Borehole to avoid reading spt data again and again
    # step = 0 + d_step
    step = 2
    d_step = 1
    z = z1
    while (z - z2) > d_step * 2:
        point_name = pile_name + '_' + str(step)
        # CONSIDERED RAKING PILES
        x = x1 - (x1 - x2) / (z1 - z2) * step
        y = y1 - (y1 - y2) / (z1 - z2) * step
        z = round(z1 - step, 1)

        true_z = float(cap_bot_lv) + z
        true_z_top = true_z + d_step / 2
        true_z_bot = true_z - d_step / 2

        zero_lv = min(float(sleeve_lv), float(ground_lv))

        list_spring = cal_spring(pile_name, df_pile, borehole, df_borehole, r_x, r_y, point_name, x, y, z, true_z,
                                 true_z_top, true_z_bot, ground_lv, zero_lv)
        soil_spring_list.append(list_spring)

        step = step + d_step

    return soil_spring_list, z


def soil_spring_base(pile_name, x1, y1, z1, x2, y2, z2, d_step, cap_bot_lv, ground_lv, founding_lv, sleeve_lv, borehole,
                     r_x, r_y, df_borehole, df_pile, z, soil_spring_list):
    # Second last point
    d_step = 1
    step = abs(z) + d_step
    point_name = pile_name + '_' + str(step)
    # CONSIDERED RAKING PILES
    x = x1 - (x1 - x2) / (z1 - z2) * step
    y = y1 - (y1 - y2) / (z1 - z2) * step
    z = round(z1 - step, 1)

    true_z = float(cap_bot_lv) + z
    true_z_top = true_z + d_step / 2
    true_z_bot = founding_lv

    zero_lv = min(float(sleeve_lv), float(ground_lv))

    list_spring = cal_spring(pile_name, df_pile, borehole, df_borehole, r_x, r_y, point_name, x, y, z, true_z,
                             true_z_top, true_z_bot, ground_lv, zero_lv)
    soil_spring_list.append(list_spring)

    # BASE POINT
    if z - d_step <= z2:
        point_name = pile_name + '_B'
        x, y, z = x2, y2, round(z2, 1)
        # PointTrueZ = (RL+FL)/2 #Pin at the middle between RL & FL
        point_true_z = founding_lv

        soil_spring_list.append(
            [point_name, 'N/A', x, y, z, ground_lv, 'N/A', 'N/A', 'N/A', 'N/A', 'N/A',
             point_true_z, 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 0, 0])

    return soil_spring_list


def transform_borehole_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Transform point-based borehole data into segment-based format for soil spring analysis.

    This function converts borehole data from individual SPT test points to soil layer
    segments defined by start and end elevations. This transformation is essential for
    soil spring generation as it creates the soil layer boundaries needed for segment-based
    spring calculations along pile lengths.
    """
    # Group borehole data by borehole identifier
    grouped = df.groupby('Borehole')

    transformed_segments = []

    # Process each borehole separately
    for borehole, group in grouped:
        n = len(group) - 1  # Number of segments = number of points - 1

        # Create segments between consecutive SPT points
        for i in range(0, n, 1):
            # Get consecutive SPT test points
            start_row = group.iloc[i]      # Upper point (start of segment)
            end_row = group.iloc[i + 1]    # Lower point (end of segment)

            # Extract coordinates and soil properties from upper point
            x = start_row['X (m)']
            y = start_row['Y (m)']
            sptn_value = start_row['SPTN Value']
            soil_type = start_row['Soil Type (GS/NCC/OS)']

            # Define segment boundaries
            spt_level_start = start_row['SPT Level (mPD)']  # Top of segment
            spt_level_end = end_row['SPT Level (mPD)']      # Bottom of segment

            # Create segment record with properties from upper point
            transformed_segments.append({
                'Borehole': borehole,
                'X (m)': x,
                'Y (m)': y,
                'Soil Type (GS/NCC/OS)': soil_type,
                'SPTN Value': sptn_value,
                'Start SPT Level (mPD)': spt_level_start,
                'End SPT Level (mPD)': spt_level_end
            })

    # Convert to DataFrame and return
    df_bh = pd.DataFrame(transformed_segments)
    return df_bh


def _process_single_pile(args: Tuple) -> List:
    """
    Process a single pile for soil spring generation using multiprocessing.

    This helper function is designed for parallel processing of individual piles
    in large pile groups. It orchestrates the complete soil spring generation
    workflow for a single pile, including top, middle, and base point processing
    with appropriate coordinate interpolation for raking (inclined) piles.
    """
    # Unpack arguments for pile processing
    (pile_name, x1, y1, z1, x2, y2, z2, borehole, r_x, r_y,
     cap_bot_lv, ground_lv, founding_lv, sleeve_length,
     df_borehole_processed, df_pile_all) = args

    # Calculate zero effective stress level
    sleeve_lv = cap_bot_lv - sleeve_length
    d_step = 1  # Standard 1-meter step size for spring generation
    z = z1      # Initialize relative coordinate at pile top
    pile_soil_spring_list = []

    # Process top portion of pile (pile cap vicinity)
    # Includes pile cap level and points at 0.5m and 1.0m below cap
    pile_soil_spring_list = soil_spring_top(
        pile_name, x1, y1, z1, x2, y2, z2, d_step,
        cap_bot_lv, ground_lv, founding_lv, sleeve_lv, borehole,
        r_x, r_y, df_borehole_processed, df_pile_all, z, pile_soil_spring_list)

    # Process middle portion of pile (regular intervals)
    # Generates springs at 1-meter intervals along pile length
    current_z_for_middle = z1  # soil_spring_middle will adjust internally
    pile_soil_spring_list, last_z_middle = soil_spring_middle(
        pile_name, x1, y1, z1, x2, y2, z2, d_step,
        cap_bot_lv, ground_lv, founding_lv, sleeve_lv, borehole,
        r_x, r_y, df_borehole_processed, df_pile_all, current_z_for_middle, pile_soil_spring_list)

    # Process base portion of pile (near pile tip)
    # Handles special conditions at pile founding level
    pile_soil_spring_list = soil_spring_base(
        pile_name, x1, y1, z1, x2, y2, z2, d_step,
        cap_bot_lv, ground_lv, founding_lv, sleeve_lv, borehole,
        r_x, r_y, df_borehole_processed, df_pile_all, last_z_middle, pile_soil_spring_list)

    return pile_soil_spring_list


def gen_soil_spring(excel_inputs: Any, file_paths: Any, root: Optional[tk.Tk] = None,
                   log_callback: Optional[callable] = None,
                   progress_callback: Optional[callable] = None) -> Optional[pd.DataFrame]:
    """
    Generate lateral soil spring coefficients for pile foundation analysis.

    This is the main function for automated generation of lateral soil spring properties
    for pile-soil interaction modeling. It processes pile geometry, borehole data, and
    soil properties to create discrete spring elements along pile lengths using parallel
    processing for efficiency. The generated springs are essential for accurate lateral
    load analysis of pile foundations in SAFE 22 finite element models.
    """
    # Helper function to handle output logging with flexible callback support
    def log_message(msg: str) -> None:
        if log_callback:
            log_callback(msg)
        else:
            print(msg)

    # Validate input data - skip processing if no pile data available
    if excel_inputs.Pile.empty:
        log_message('No pile data found. Soil Spring generation is skipped.')
        return None

    df_pile_soil_spring_setting = excel_inputs.PileSoilSpringSetting
    condition = excel_inputs.Pile['Pile Type'] != 'MP'
    df_pile = excel_inputs.Pile[condition].copy().reset_index(drop=True)
    df_pile = df_pile.merge(df_pile_soil_spring_setting, how='left', on='Pile Mark')
    df_borehole_orig = excel_inputs.Borehole.copy() # Renamed to avoid confusion
    path_excel_output = file_paths.ExcelGeology

    # Update progress
    if progress_callback:
        progress_callback(5, "Preparing soil spring generation")

    # Create status bar if running from UI (Tkinter parts are not ideal with multiprocessing,
    # consider updating progress less frequently or after the pool finishes)
    status_bar = None
    progress_var = None
    if root:
        # First, remove any existing status frame to prevent stacking
        for widget in root.winfo_children():
            if isinstance(widget, tk.Frame) and hasattr(widget, 'is_status_frame'):
                widget.destroy()

        status_frame = tk.Frame(root)
        status_frame.is_status_frame = True  # Add attribute to identify it later
        status_frame.pack(side="bottom", fill="x")

        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(status_frame, variable=progress_var, maximum=100)
        progress_bar.pack(side="bottom", fill="x", padx=10, pady=5)

        status_var = tk.StringVar()
        status_var.set("Preparing soil spring generation...")
        status_label = tk.Label(status_frame, textvariable=status_var, anchor="w",
                                height=2, justify="left", wraplength=500)
        status_label.pack(side="bottom", fill="x", padx=10, pady=(5, 0))
        root.update()

    # sort df_borehole_orig by Borehole Name and then SPT Level in descending order
    # Sorting borehole data...
    df_borehole_orig.sort_values(by=['Borehole', 'SPT Level (mPD)'], ascending=[True, False], inplace=True)
    df_borehole_orig.reset_index(drop=True, inplace=True)

    # form df_bh to store the df_borehole_orig data for each borehole with 'SPT Level Start (mPD)' and 'SPT Level End (mPD)'
    # Transforming borehole data...
    df_borehole_processed = transform_borehole_data(df_borehole_orig) # Use the transformed data

    if progress_callback:
        progress_callback(10, "Borehole data prepared")

    # Preparing pile data for parallel processing...
    df_pile['BX (m)'] = df_pile['BX (m)'].fillna(df_pile['X (m)'])
    df_pile['BY (m)'] = df_pile['BY (m)'].fillna(df_pile['Y (m)'])
    df_pile['Z (m)'] = 0

    z2_series = df_pile['Z (m)'] - (df_pile['Pile Cap Bottom Level (mPD)'] - df_pile['Founding Level (mPD)'])
    df_pile.insert(df_pile.columns.get_loc('BY (m)') + 1, 'BZ (m)', z2_series)

    if progress_callback:
        progress_callback(15, "Pile data prepared, starting parallel processing...")
    # Starting parallel processing of piles...

    tasks = []
    for idx in df_pile.index:
        pile_name = df_pile.loc[idx, 'Pile Mark']
        x1 = df_pile.loc[idx, 'X (m)']
        y1 = df_pile.loc[idx, 'Y (m)']
        z1 = round(df_pile.loc[idx, 'Z (m)'], 1)
        x2_val = df_pile.loc[idx, 'BX (m)']
        y2_val = df_pile.loc[idx, 'BY (m)']
        z2_val = round(df_pile.loc[idx, 'BZ (m)'], 1)
        borehole = df_pile.loc[idx, 'Borehole']
        r_x = df_pile.loc[idx, 'Rx']
        r_y = df_pile.loc[idx, 'Ry']
        cap_bot_lv = df_pile.loc[idx, 'Pile Cap Bottom Level (mPD)']
        ground_lv = df_pile.loc[idx, 'Ground Level (mPD)']
        founding_lv = df_pile.loc[idx, 'Founding Level (mPD)']
        sleeve_length = df_pile.loc[idx, 'Sleeve Length (m)']
        # Pass the globally prepared df_borehole_processed and the full df_pile
        tasks.append((pile_name, x1, y1, z1, x2_val, y2_val, z2_val, borehole, r_x, r_y,
                      cap_bot_lv, ground_lv, founding_lv, sleeve_length,
                      df_borehole_processed, df_pile.copy())) # Pass copy of df_pile if it's modified inside worker, or ensure it's read-only

    soil_spring_list_of_lists = []
    # Using try-finally to ensure pool is closed
    pool = None
    try:
        # Create a pool of worker processes. Defaults to cpu_count().
        pool = multiprocessing.Pool()
        
        # Use tqdm for progress if not in UI mode and no specific progress_callback
        if not root and not progress_callback:
            log_message(f"Processing {len(tasks)} piles...")
            # For multiprocessing, it's often better to map directly and let tqdm handle the iterable
            # results = pool.map(_process_single_pile, tasks) # This was the original plan
            # Using tqdm with pool.imap for better progress visibility
            results_iterator = pool.imap(_process_single_pile, tasks)
            soil_spring_list_of_lists = list(tqdm.tqdm(results_iterator, total=len(tasks), desc="Generating soil springs", unit="pile", ncols=100))

        else: # UI mode or custom callback
            log_message(f"Processing {len(tasks)} piles...")
            # For UI, update progress less frequently or after completion
            # We can use map and then update progress based on completion.
            soil_spring_list_of_lists = pool.map(_process_single_pile, tasks)
            if root: # Update Tkinter progress bar after all tasks are done
                progress_var.set(90) # Assuming 15% setup, 75% for processing
                status_var.set(f"Completed processing {len(tasks)} piles.")
                root.update()
            elif progress_callback:
                progress_callback(90, f"Completed processing {len(tasks)} piles.")
                
    finally:
        if pool:
            pool.close()
            pool.join()

    # Finished. Aggregating results...

    # Flatten the list of lists
    soil_spring_list = [item for sublist in soil_spring_list_of_lists for item in sublist]

    if progress_callback:
        progress_callback(90, "Creating soil spring dataframe") # Progress was already set to 90 if UI

    log_message("Finalizing soil spring dataframe...")
    
    cols = ['Point Name', 'Pile Diameter (m)', 'X (m)', 'Y (m)', 'Z (m)', 'Ground Level (mPD)', 'Nearest Pile X',
            'Nearest Pile X Distance (m)', 'Nearest Pile Y', 'Nearest Pile Y Distance (m)', 'Borehole', 'True Z (mPD)',
            'Segment_Z (mPD)', 'SPTN Value', 'Soil Type (GS/NCC/OS)', 'Subgrade Reaction (kN/m3)', 'Rx', 'Ry',
            'Factor Dia', 'Segment Center (mPD)', 'Distance from Ground Level (m)',
            'Delta Z (m)', 'Spring X (kN/m)', 'Spring Y (kN/m)']
    
    df_lateral_soil_spring = pd.DataFrame(soil_spring_list, columns=cols)

    if progress_callback:
        progress_callback(95, "Writing soil spring data to Excel")
    
    log_message(f"Writing soil spring data to {path_excel_output}")
    with pd.ExcelWriter(path_excel_output, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
        df_lateral_soil_spring.to_excel(writer, index=False, sheet_name=build_fem_config.SHEET_LATERAL_SOIL_SPRING)

    if progress_callback:
        progress_callback(100, "Soil spring generation completed")

    if root:
        progress_var.set(100)
        status_var.set("Soil spring generation completed.")
        root.update()
        # Can optionally destroy the status frame after a delay or leave it as completion indicator

    log_message(f"Soil spring generation completed with {len(df_lateral_soil_spring)} data points")
    return df_lateral_soil_spring