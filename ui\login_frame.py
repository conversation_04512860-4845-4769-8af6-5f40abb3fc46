"""Login frame UI component."""

import tkinter as tk
from tkinter import messagebox
from typing import Callable

from ui.components import BaseFrame, HeaderFrame, create_menu_bar, show_about


class LoginFrame(BaseFrame):
    """Login interface frame."""
    
    def __init__(self, parent, on_send_password: Callable, on_login: Callable, 
                 on_dev_login: Callable = None, is_locked: bool = False):
        super().__init__(parent)
          # Clear window and add header
        self.clear_window()
        # Recreate menu bar after clearing window
        create_menu_bar(parent, show_about)
        HeaderFrame(parent, show_user=False)
        
        # Main frame
        main_frame = tk.Frame(parent)
        main_frame.pack(expand=True, fill="both", padx=40, pady=20)
        
        # Username entry
        tk.Label(main_frame, text="User Name (e.g. john.doe):", font=("Arial", 10)).pack(anchor="w", pady=(10, 0))
        tk.Label(main_frame, text="User Name = <EMAIL>", font=("Arial", 8, "italic")).pack(anchor="w")
        
        self.username_entry = tk.Entry(main_frame, width=40)
        self.username_entry.pack(pady=(5, 20), fill="x")
        
        # Send password button
        tk.Button(
            main_frame,
            text="Send Password to Email",
            command=lambda: on_send_password(self.username_entry.get().strip()),
            width=20,
            height=2
        ).pack(pady=10)
        
        # Password entry
        tk.Label(main_frame, text="Enter the 30-unit password:", font=("Arial", 10)).pack(anchor="w", pady=(10, 0))
        
        self.password_entry = tk.Entry(main_frame, width=40, show="*")
        self.password_entry.pack(pady=(5, 20), fill="x")
        
        # Login button
        tk.Button(
            main_frame,
            text="Login",
            command=lambda: on_login(self.password_entry.get().strip()),
            width=20,
            height=2
        ).pack(pady=10)
        
        # Lockout message
        if is_locked:
            tk.Label(
                main_frame,
                text="Account temporarily locked due to too many failed attempts.\nPlease try again later.",
                fg="red",
                font=("Arial", 10, "bold")
            ).pack(pady=(10, 0))
        
        # Dev login (remove in production)
        if on_dev_login:
            tk.Button(
                main_frame,
                text="Development Login (Skip)",
                command=on_dev_login,
                width=20
            ).pack(pady=10)
    
    def clear_password(self):
        """Clear password field."""
        self.password_entry.delete(0, 'end')
