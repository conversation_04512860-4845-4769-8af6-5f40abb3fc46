"""
SAFE 22 Data Structures Module

This module provides the core data structure class for managing SAFE 22 analysis data.
It implements comprehensive DataFrame management for all SAFE 22 data tables including
analysis options, modeling settings, material properties, and structural assignments.

The module is designed to work with CSI SAFE 22 software and provides a structured
approach to handling complex finite element analysis data through pandas DataFrames
with multi-level indexing.

Classes:
    Safe22DataFrames: Main data structure class containing all SAFE 22 data tables

Example:
    >>> from safe22_class import Safe22DataFrames
    >>> safe_data = Safe22DataFrames()
    >>> analysis_options = safe_data.AnalysisModelingOptions
    >>> load_patterns = safe_data.LoadPatternDefinitions
"""

import pandas as pd
from typing import Optional, Dict, Any, Tuple, List


class Safe22DataFrames:
    """
    Main data structure class for managing SAFE 22 analysis data.

    This class initializes and manages all the DataFrame structures required for
    SAFE 22 finite element analysis. Each DataFrame corresponds to a specific
    table in the SAFE 22 data model and uses multi-level column indexing to
    maintain compatibility with SAFE 22 file formats.

    The class provides comprehensive data management for:
    - Analysis options and modeling settings
    - Material properties and definitions
    - Structural element assignments
    - Load patterns and combinations
    - Design preferences and overwrites
    - Geometry and connectivity data

    Attributes:
        All attributes are pandas DataFrames with multi-level column indexing
        following SAFE 22 table structure conventions. Key categories include:

        Analysis Options:
            - AnalysisCrackingAnalysis: Cracking analysis settings
            - AnalysisDsgnandRecOptions: Design and recovery options
            - AnalysisFloorMeshSettings: Floor meshing parameters
            - AnalysisSAPFireOptions: SAPFire solver settings
            - AnalysisWallMeshSettings: Wall meshing parameters
            - AnalysisModelingOptions: General modeling options

        Material Properties:
            - MatPropGeneral: General material properties
            - MatPropBasicMechProps: Basic mechanical properties
            - MatPropSteelData: Steel material data
            - MatPropConcreteData: Concrete material data
            - MatPropRebarData: Reinforcement material data

        Structural Elements:
            - FramePropSummary: Frame property definitions
            - AreaSectionPropsSummary: Area section properties
            - BeamObjectConnectivity: Beam connectivity data
            - ColumnObjectConnectivity: Column connectivity data
            - FloorObjectConnectivity: Floor connectivity data

        Load Definitions:
            - LoadPatternDefinitions: Load pattern specifications
            - LoadCombinationDefinitions: Load combination rules
            - AreaLoadsUniform: Uniform area loads
            - FrameLoadsDistributed: Distributed frame loads

    Example:
        >>> safe_data = Safe22DataFrames()
        >>> # Access analysis options
        >>> modeling_opts = safe_data.AnalysisModelingOptions
        >>> # Check if two-dimensional analysis is enabled
        >>> is_2d = modeling_opts.iloc[0][('TABLE:  Analysis Modeling Options', 'Two Dimensional Only', '')]
        >>> # Access material properties
        >>> materials = safe_data.MatPropGeneral
        >>> print(f"Number of materials defined: {len(materials)}")

    Note:
        All DataFrames are initialized with appropriate column structures but
        may be empty until populated by other modules in the safe22 package.
        The multi-level indexing follows the pattern:
        (Table Name, Column Name, Units)
    """
    def __init__(self) -> None:
        """
        Initialize all SAFE 22 DataFrame structures.

        Creates and initializes all required DataFrames with appropriate column
        structures and default data. Each DataFrame uses multi-level column
        indexing following SAFE 22 table format conventions.

        The initialization process sets up:
        - Analysis option tables with default settings
        - Empty structural element tables ready for population
        - Material property tables with standard configurations
        - Load definition tables with default patterns
        - Design preference tables with code-specific settings

        All DataFrames are created with proper column structures but may
        contain only default or empty data until populated by other functions.

        Raises:
            MemoryError: If insufficient memory is available to create all DataFrames

        Example:
            >>> safe_data = Safe22DataFrames()
            >>> print(f"Analysis options initialized: {len(safe_data.AnalysisCrackingAnalysis.columns)}")
        """
        # AnalysisCrackingAnalysis - Cracking analysis configuration options
        tuples = [
            ('TABLE:  Analysis Options - Cracking Analysis Options',
             'Reinforcement Source', ''),
            ('TABLE:  Analysis Options - Cracking Analysis Options',
             'Minimum Tension Ratio', ''),
            ('TABLE:  Analysis Options - Cracking Analysis Options', 'Minimum Compression Ratio', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('User and Designed', 0.0018, 0)]
        self.AnalysisCrackingAnalysis = pd.DataFrame(data, columns=col)

        # AnalysisDsgnandRecOptions
        tuples = [
            ('TABLE:  Analysis Options - Design and Response Recovery Options',
             'Number Design Threads', ''),
            ('TABLE:  Analysis Options - Design and Response Recovery Options',
             'Number Recovery Threads', ''),
            ('TABLE:  Analysis Options - Design and Response Recovery Options',
             'Use Memory Mapped Files', ''),
            ('TABLE:  Analysis Options - Design and Response Recovery Options', 'Allow Model Differences', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [(0, 0, 'Program Determined', 'No')]
        self.AnalysisDsgnandRecOptions = pd.DataFrame(data, columns=col)

        # AnalysisFloorMeshSettings
        tuples = [
            ('TABLE:  Analysis Options - Automatic Mesh Settings for Floors',
             'Mesh Option', ''),
            ('TABLE:  Analysis Options - Automatic Mesh Settings for Floors',
             'Use Localized Meshing', ''),
            ('TABLE:  Analysis Options - Automatic Mesh Settings for Floors', 'Maximum Mesh Size', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Rectangular', 'No', 1)]
        self.AnalysisFloorMeshSettings = pd.DataFrame(data, columns=col)

        # AnalysisSAPFireOptions
        tuples = [
            ('TABLE:  Analysis Options - SAPFire Options', 'Solver Option', ''),
            ('TABLE:  Analysis Options - SAPFire Options', 'Analysis Process', ''),
            ('TABLE:  Analysis Options - SAPFire Options', 'Number Parallel', ''),
            ('TABLE:  Analysis Options - SAPFire Options',
             'Number Analysis Threads', ''),
            ('TABLE:  Analysis Options - SAPFire Options', 'Max File Size', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Advanced', 'Auto', '', 0, 0)]
        self.AnalysisSAPFireOptions = pd.DataFrame(data, columns=col)

        # AnalysisWallMeshSettings
        tuples = [
            ('TABLE:  Analysis Options - Automatic Rectangular Mesh Options for Walls', 'Maximum Mesh Size', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [(1)]
        self.AnalysisWallMeshSettings = pd.DataFrame(data, columns=col)

        # AnalysisModelingOptions
        tuples = [
            ('TABLE:  Analysis Modeling Options', 'Two Dimensional Only', ''),
            ('TABLE:  Analysis Modeling Options', 'Rigid Diaphragm At Top', ''),
            ('TABLE:  Analysis Modeling Options', 'Ignore Vertical Offsets', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('No', 'No', 'Yes')]
        self.AnalysisModelingOptions = pd.DataFrame(data, columns=col)

        # AreaAssignsAdditionalMass
        tuples = [
            ('TABLE:  Area Assignments - Additional Mass', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Additional Mass', 'Added Mass', 'kg/m²')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsAdditionalMass = pd.DataFrame(data, columns=col)

        # AreaAssignsAreaSprings
        tuples = [
            ('TABLE:  Area Assignments - Area Springs', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Area Springs', 'Spring Property', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsAreaSprings = pd.DataFrame(data, columns=col)

        # AreaAssignsEdgeConstraints
        tuples = [
            ('TABLE:  Area Assignments - Auto Edge Constraints', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Auto Edge Constraints', 'Constraint', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsEdgeConstraints = pd.DataFrame(data, columns=col)

        # AreaAssignsEdgeReleases
        tuples = [
            ('TABLE:  Area Assignments - Edge Releases', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Edge Releases', 'Edge Number', ''),
            ('TABLE:  Area Assignments - Edge Releases', 'Point Labels', ''),
            ('TABLE:  Area Assignments - Edge Releases',
             'Release In Plane Shear', ''),
            ('TABLE:  Area Assignments - Edge Releases',
             'Release In Plane Direct Force', ''),
            ('TABLE:  Area Assignments - Edge Releases',
             'Release Out of Plane Shear', ''),
            ('TABLE:  Area Assignments - Edge Releases', 'Release Bending', ''),
            ('TABLE:  Area Assignments - Edge Releases', 'Release Twisting', ''),
            ('TABLE:  Area Assignments - Edge Releases',
             'Spring In Plane Shear', 'kN/m/m'),
            ('TABLE:  Area Assignments - Edge Releases',
             'Spring In Plane Direct Force', 'kN/m/m'),
            ('TABLE:  Area Assignments - Edge Releases',
             'Spring Out of plane Shear', 'kN/m/m'),
            ('TABLE:  Area Assignments - Edge Releases', 'Spring Bending', 'kN/rad'),
            ('TABLE:  Area Assignments - Edge Releases', 'Spring Twisting', 'kN/rad')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsEdgeReleases = pd.DataFrame(data, columns=col)

        # AreaAssignsFloorAutoMesh
        tuples = [
            ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Mesh Option', ''),
            ('TABLE:  Area Assignments - Floor Auto Mesh Options', 'Add Restraints', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsFloorAutoMesh = pd.DataFrame(data, columns=col)

        # AreaAssignsFloorCracking
        tuples = [
            ('TABLE:  Area Assignments - Floor Cracking', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Floor Cracking', 'Consider for Cracking', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsFloorCracking = pd.DataFrame(data, columns=col)
        # AreaAssignsInsertionPoint
        tuples = [
            ('TABLE:  Area Assignments - Insertion Point', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Insertion Point', 'Cardinal Point', ''),
            ('TABLE:  Area Assignments - Insertion Point', 'Transform', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsInsertionPoint = pd.DataFrame(data, columns=col)

        # AreaAssignsLocalAxes
        tuples = [
            ('TABLE:  Area Assignments - Local Axes', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Local Axes', 'Angle', 'deg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsLocalAxes = pd.DataFrame(data, columns=col)

        # AreaAssignsSectProp
        tuples = [
            ('TABLE:  Area Assignments - Section Properties', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Section Properties', 'Section Property', ''),
            ('TABLE:  Area Assignments - Section Properties', 'Property Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsSectProp = pd.DataFrame(data, columns=col)

        # AreaAssignsSlabLineRel
        tuples = [
            ('TABLE:  Area Assignments - Slab Line Releases', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Slab Line Releases', 'Shear Left', ''),
            ('TABLE:  Area Assignments - Slab Line Releases', 'Moment Left', ''),
            ('TABLE:  Area Assignments - Slab Line Releases', 'Shear Right', ''),
            ('TABLE:  Area Assignments - Slab Line Releases', 'Moment Right', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsSlabLineRel = pd.DataFrame(data, columns=col)
        # AreaAssignsSlabRibLocs
        tuples = [
            ('TABLE:  Area Assignments - Slab Rib Locations', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Slab Rib Locations', 'X', 'm'),
            ('TABLE:  Area Assignments - Slab Rib Locations', 'Y', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsSlabRibLocs = pd.DataFrame(data, columns=col)
        # AreaAssignsStiffModifiers
        tuples = [
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'f11 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'f22 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'f12 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'm11 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'm22 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'm12 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'v13 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'v23 Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'Mass Modifier', ''),
            ('TABLE:  Area Assignments - Stiffness Modifiers', 'Weight Modifier', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsStiffModifiers = pd.DataFrame(data, columns=col)
        # AreaAssignsSummary
        tuples = [
            ('TABLE:  Area Assignments - Summary', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Summary', 'Section Property', ''),
            ('TABLE:  Area Assignments - Summary', 'Property Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsSummary = pd.DataFrame(data, columns=col)
        # AreaAssignsThickOverwrites
        tuples = [
            ('TABLE:  Area Assignments - Thickness Overwrites', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Thickness Overwrites', 'Point Number', ''),
            ('TABLE:  Area Assignments - Thickness Overwrites', 'Point Label', ''),
            ('TABLE:  Area Assignments - Thickness Overwrites', 'Point Unique Name', ''),
            ('TABLE:  Area Assignments - Thickness Overwrites', 'Thickness Overwrite', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsThickOverwrites = pd.DataFrame(data, columns=col)
        # AreaAssignsWallAutoMesh
        tuples = [
            ('TABLE:  Area Assignments - Wall Auto Mesh Options', 'UniqueName', ''),
            ('TABLE:  Area Assignments - Wall Auto Mesh Options', 'Mesh Option', ''),
            ('TABLE:  Area Assignments - Wall Auto Mesh Options', 'Add Restraints', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaAssignsWallAutoMesh = pd.DataFrame(data, columns=col)
        # AreaLoadsNonuniform
        tuples = [
            ('TABLE:  Area Load Assignments - Non-uniform', 'UniqueName', ''),
            ('TABLE:  Area Load Assignments - Non-uniform', 'Load Pattern', ''),
            ('TABLE:  Area Load Assignments - Non-uniform', 'Direction', ''),
            ('TABLE:  Area Load Assignments - Non-uniform', 'A', 'kN/m³'),
            ('TABLE:  Area Load Assignments - Non-uniform', 'B', 'kN/m³'),
            ('TABLE:  Area Load Assignments - Non-uniform', 'C', 'kN/m³'),
            ('TABLE:  Area Load Assignments - Non-uniform', 'D', 'kN/m²'),
            ('TABLE:  Area Load Assignments - Non-uniform', 'Restrictions', ''),
            ('TABLE:  Area Load Assignments - Non-uniform', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaLoadsNonuniform = pd.DataFrame(data, columns=col)
        # AreaLoadsTemperature
        tuples = [
            ('TABLE:  Area Load Assignments - Temperature', 'UniqueName', ''),
            ('TABLE:  Area Load Assignments - Temperature', 'Load Pattern', ''),
            ('TABLE:  Area Load Assignments - Temperature', 'Top Temperature', 'C'),
            ('TABLE:  Area Load Assignments - Temperature', 'Bottom Temperature', 'C'),
            ('TABLE:  Area Load Assignments - Temperature', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaLoadsTemperature = pd.DataFrame(data, columns=col)
        # AreaLoadsUniform
        tuples = [
            ('TABLE:  Area Load Assignments - Uniform', 'UniqueName', ''),
            ('TABLE:  Area Load Assignments - Uniform', 'Load Pattern', ''),
            ('TABLE:  Area Load Assignments - Uniform', 'Direction', ''),
            ('TABLE:  Area Load Assignments - Uniform', 'Load', 'kN/m²'),
            ('TABLE:  Area Load Assignments - Uniform', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaLoadsUniform = pd.DataFrame(data, columns=col)

        # AreaLoadsUniformLoadSets
        tuples = [
            ('TABLE:  Area Load Assignments - Uniform Load Sets', 'UniqueName', ''),
            ('TABLE:  Area Load Assignments - Uniform Load Sets', 'Load Set', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaLoadsUniformLoadSets = pd.DataFrame(data, columns=col)
        # AreaLoadsWindCoefficients
        tuples = [
            ('TABLE:  Area Load Assignments - Wind Pressure Coefficients', 'UniqueName', ''),
            ('TABLE:  Area Load Assignments - Wind Pressure Coefficients',
             'Load Pattern', ''),
            ('TABLE:  Area Load Assignments - Wind Pressure Coefficients', 'Direction', ''),
            ('TABLE:  Area Load Assignments - Wind Pressure Coefficients', 'Cp', ''),
            ('TABLE:  Area Load Assignments - Wind Pressure Coefficients', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaLoadsWindCoefficients = pd.DataFrame(data, columns=col)
        # AreaSectionPropsSummary
        tuples = [
            ('TABLE:  Area Section Property Definitions - Summary', 'Name', ''),
            ('TABLE:  Area Section Property Definitions - Summary', 'Type', ''),
            ('TABLE:  Area Section Property Definitions - Summary', 'Element Type', ''),
            ('TABLE:  Area Section Property Definitions - Summary', 'Material', ''),
            ('TABLE:  Area Section Property Definitions - Summary', 'Total Thickness', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.AreaSectionPropsSummary = pd.DataFrame(data, columns=col)
        # BeamObjectConnectivity
        tuples = [
            ('TABLE:  Beam Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Beam Object Connectivity', 'UniquePtI', ''),
            ('TABLE:  Beam Object Connectivity', 'UniquePtJ', ''),
            ('TABLE:  Beam Object Connectivity', 'Length', 'm'),
            ('TABLE:  Beam Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.BeamObjectConnectivity = pd.DataFrame(data, columns=col)

        # BeamObjectCurveData
        tuples = [
            ('TABLE:  Beam Object Curve Data', 'Unique Name', ''),
            ('TABLE:  Beam Object Curve Data', 'Curve Type', ''),
            ('TABLE:  Beam Object Curve Data', 'ICP Number', ''),
            ('TABLE:  Beam Object Curve Data', 'Global X', 'm'),
            ('TABLE:  Beam Object Curve Data', 'Global Y', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.BeamObjectCurveData = pd.DataFrame(data, columns=col)
        # BraceObjectConnectivity
        tuples = [
            ('TABLE:  Brace Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Brace Object Connectivity', 'UniquePtI', ''),
            ('TABLE:  Brace Object Connectivity', 'UniquePtJ', ''),
            ('TABLE:  Brace Object Connectivity', 'Length', 'm'),
            ('TABLE:  Brace Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.BraceObjectConnectivity = pd.DataFrame(data, columns=col)
        # ColumnObjectConnectivity
        tuples = [
            ('TABLE:  Column Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Column Object Connectivity', 'UniquePtI', ''),
            ('TABLE:  Column Object Connectivity', 'UniquePtJ', ''),
            ('TABLE:  Column Object Connectivity', 'Length', 'm'),
            ('TABLE:  Column Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ColumnObjectConnectivity = pd.DataFrame(data, columns=col)

        # CompBmAddSectionsWithStuds
        tuples = [
            ('TABLE:  Composite Beam Additional Sections With Studs', 'UniqueName', ''),
            ('TABLE:  Composite Beam Additional Sections With Studs', 'Distance Type', ''),
            ('TABLE:  Composite Beam Additional Sections With Studs', 'Number of Studs', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmAddSectionsWithStuds = pd.DataFrame(data, columns=col)
        # CompBmConstBracingPoint
        tuples = [
            ('TABLE:  Composite Beam Construction Bracing - Point', 'UniqueName', ''),
            ('TABLE:  Composite Beam Construction Bracing - Point', 'Bracing Type', ''),
            ('TABLE:  Composite Beam Construction Bracing - Point', 'Distance Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmConstBracingPoint = pd.DataFrame(data, columns=col)
        # CompBmConstBracingUniform
        tuples = [
            ('TABLE:  Composite Beam Construction Bracing - Uniform', 'UniqueName', ''),
            ('TABLE:  Composite Beam Construction Bracing - Uniform', 'Bracing Type', ''),
            ('TABLE:  Composite Beam Construction Bracing - Uniform', 'Distance Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmConstBracingUniform = pd.DataFrame(data, columns=col)
        # CompBmOverAISC36005
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-05', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverAISC36005 = pd.DataFrame(data, columns=col)
        # CompBmOverAISC36010
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-10', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverAISC36010 = pd.DataFrame(data, columns=col)
        # CompBmOverAISC36016
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-16', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverAISC36016 = pd.DataFrame(data, columns=col)
        # CompBmOverAISC36022
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - AISC 360-22', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverAISC36022 = pd.DataFrame(data, columns=col)
        # CompBmOverBS59501990
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'n Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'n Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990',
             'Neff Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - BS 5950-1990', 'Beam Spacing', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverBS59501990 = pd.DataFrame(data, columns=col)
        # CompBmOverBSChinese2010
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010',
             'Neff Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2010', 'Beam Spacing', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverBSChinese2010 = pd.DataFrame(data, columns=col)
        # CompBmOverChinese2018
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Long Term Live Load Ratio', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Cb Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Cb Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Chinese 2018', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverChinese2018 = pd.DataFrame(data, columns=col)
        # CompBmOverCSAS1609
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Omega2 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Omega2 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Aging Coefficient x of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09',
             'Creep Coefficient Phi of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-09', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverCSAS1609 = pd.DataFrame(data, columns=col)
        # CompBmOverCSAS1614
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Omega2 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Omega2 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Aging Coefficient x of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14',
             'Creep Coefficient Phi of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-14', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverCSAS1614 = pd.DataFrame(data, columns=col)
        # CompBmOverCSAS1619
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Omega2 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Omega2 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Aging Coefficient x of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19',
             'Creep Coefficient Phi of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-19', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverCSAS1619 = pd.DataFrame(data, columns=col)
        # CompBmOverCSAS1624
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Omega2 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Omega2 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Aging Coefficient x of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24',
             'Creep Coefficient Phi of Concrete', ''),
            ('TABLE:  Composite Beam Design Overwrites - CSA S16-24', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverCSAS1624 = pd.DataFrame(data, columns=col)
        # CompBmOverEurocode42004
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'C1 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'C1 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - Eurocode 4-2004', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverEurocode42004 = pd.DataFrame(data, columns=col)
        # CompBmOverIS11384_2022
        tuples = [
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022', 'Unique Name', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Design Section', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Restrict Beam Depth?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Restrict Beam Width?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Shored Construction?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022', 'Beam Fy', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022', 'Beam Fu', 'MPa'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Cover Plate Present?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'LL Reduction Factor Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'LL Reduction Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Long Term Live Load Ratio', '%'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'C1 Factor (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Bracing Condition Lb (C)', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'C1 Factor (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Bracing Condition (S)', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck ID Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck ID Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Direction Left Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Direction Left', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Left Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Width Left', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Direction Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck ID Right Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck ID Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Direction Right', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Right Deck Width Condition', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deck Width Right', 'm'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022', 'Beam Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'User Stud Pattern?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Maximum Studs in Row', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Stud Capacity, Qn', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Deflection Check Type', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Calculate Camber?', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Overwrites - IS 11384_2022', 'Vibration Criterion', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmOverIS11384_2022 = pd.DataFrame(data, columns=col)
        # CompBmPrefAISC36005
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'phi-b', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'phi-bcpp', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'phi-v', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-05', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefAISC36005 = pd.DataFrame(data, columns=col)
        # CompBmPrefAISC36010
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'phi-b', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'phi-bcpp', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'phi-v', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-10', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefAISC36010 = pd.DataFrame(data, columns=col)
        # CompBmPrefAISC36016
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Consider Axial Force?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'phi-b', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'phi-bcpp', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'phi-v', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-16', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefAISC36016 = pd.DataFrame(data, columns=col)
        # CompBmPrefAISC36022
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Consider Axial Force?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'phi-b', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'phi-bcpp', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'phi-v', ''),
            ('TABLE:  Composite Beam Design Preferences - AISC 360-22', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefAISC36022 = pd.DataFrame(data, columns=col)
        # CompBmPrefBS595090
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Vibration LL', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Consider Frequency?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Min Frequency', 'cyc/sec'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Murray Damping?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Inherent Damping', '%'),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - BS 5950-90', 'Camber Price ($)', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefBS595090 = pd.DataFrame(data, columns=col)
        # CompBmPrefChinese2010
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Vibration LL', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Consider Frequency?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Min Frequency', 'cyc/sec'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Murray Damping?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Inherent Damping', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2010', 'Camber Price ($)', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefChinese2010 = pd.DataFrame(data, columns=col)
        # CompBmPrefChinese2018
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Long Term Live Load Ratio', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Ieff reduction Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Chinese 2018', 'Camber Price ($)', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefChinese2018 = pd.DataFrame(data, columns=col)
        # CompBmPrefCSAS1609
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Strain Ec Chi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Strain Ec Phi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Phi-Concrete', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Phi-Steel', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Phi-Rebar', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Phi-Connector', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-09', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefCSAS1609 = pd.DataFrame(data, columns=col)
        # CompBmPrefCSAS1614
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Strain Ec Chi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Strain Ec Phi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Phi-Concrete', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Phi-Steel', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Phi-Rebar', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Phi-Connector', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-14', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefCSAS1614 = pd.DataFrame(data, columns=col)
        # CompBmPrefCSAS1619
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Strain Ec Chi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Strain Ec Phi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Phi-Concrete', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Phi-Steel', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Phi-Rebar', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Phi-Connector', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-19', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefCSAS1619 = pd.DataFrame(data, columns=col)
        # CompBmPrefCSAS1624
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Position of Studs', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Creep Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Strain Ec Chi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Strain Ec Phi Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Phi-Concrete', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Phi-Steel', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Phi-Rebar', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Phi-Connector', ''),
            ('TABLE:  Composite Beam Design Preferences - CSA S16-24', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefCSAS1624 = pd.DataFrame(data, columns=col)
        # CompBmPrefEurocode42004
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Studs welded through?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Free Shrinkage Strain', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Country', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Combination Equation', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004',
             'Reliability Class', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'GammaM0', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'GammaM1', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'GammaV', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'GammaC', ''),
            ('TABLE:  Composite Beam Design Preferences - Eurocode 4-2004', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefEurocode42004 = pd.DataFrame(data, columns=col)
        # CompBmPrefIS11384_2022
        tuples = [
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'Shored?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Middle Range', '%'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'D/C Ratio Limit', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Long Term Live Load Ratio', '%'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Minimum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Maximum PCC', '%'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Single Segment?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Max. Long. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Trans. Spacing', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Max. Studs Per Row', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Studs welded through?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'Camber?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'Camber DL', '%'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Beam Depth', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Web Thick.', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Beam Span', 'm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Min. Camber, abs', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Minimum Camber, L/', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Camber Abs. Max Limit', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Camber Max Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Camber Interval', 'mm'),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Round Camber Down?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Pre-Comp DL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'SDL+LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'LL Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'Net Ratio', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Vibration Criterion', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Occupancy Category', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Walking Acceleration Limit, a0/g', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Damping Ratio - Walking', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Optimize Price?', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Steel Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Stud Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022',
             'Camber Price ($)', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'GammaM0', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'GammaM1', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'GammaV', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'GammaC', ''),
            ('TABLE:  Composite Beam Design Preferences - IS 11384_2022', 'Reaction Factor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmPrefIS11384_2022 = pd.DataFrame(data, columns=col)
        # CompBmStrBracingPoint
        tuples = [
            ('TABLE:  Composite Beam Strength Bracing - Point', 'UniqueName', ''),
            ('TABLE:  Composite Beam Strength Bracing - Point', 'Bracing Type', ''),
            ('TABLE:  Composite Beam Strength Bracing - Point', 'Distance Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmStrBracingPoint = pd.DataFrame(data, columns=col)
        # CompBmStrBracingUniform
        tuples = [
            ('TABLE:  Composite Beam Strength Bracing - Uniform', 'UniqueName', ''),
            ('TABLE:  Composite Beam Strength Bracing - Uniform', 'Bracing Type', ''),
            ('TABLE:  Composite Beam Strength Bracing - Uniform', 'Distance Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.CompBmStrBracingUniform = pd.DataFrame(data, columns=col)
        # ConcBmOverACI31808
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - ACI 318-08', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-08', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-08', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-08', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverACI31808 = pd.DataFrame(data, columns=col)
        # ConcBmOverACI31811
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - ACI 318-11', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-11', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-11', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-11', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverACI31811 = pd.DataFrame(data, columns=col)
        # ConcBmOverACI31814
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - ACI 318-14', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-14', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-14', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-14', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverACI31814 = pd.DataFrame(data, columns=col)
        # ConcBmOverACI31819
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - ACI 318-19', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-19', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-19', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - ACI 318-19', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverACI31819 = pd.DataFrame(data, columns=col)
        # ConcBmOverAS360009
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - AS 3600-09', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-09', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-09', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-09', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverAS360009 = pd.DataFrame(data, columns=col)
        # ConcBmOverAS36002018
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - AS 3600-2018', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-2018', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-2018', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - AS 3600-2018', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverAS36002018 = pd.DataFrame(data, columns=col)
        # ConcBmOverBS811097
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - BS 8110-97', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - BS 8110-97', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - BS 8110-97', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - BS 8110-97', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverBS811097 = pd.DataFrame(data, columns=col)
        # ConcBmOverChinese2010
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Chinese 2010', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Chinese 2010', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Chinese 2010', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - Chinese 2010', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverChinese2010 = pd.DataFrame(data, columns=col)
        # ConcBmOverCSAA23314
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - CSA A233-14', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-14', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-14', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-14', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverCSAA23314 = pd.DataFrame(data, columns=col)
        # ConcBmOverCSAA23319
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - CSA A233-19', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-19', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-19', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - CSA A233-19', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverCSAA23319 = pd.DataFrame(data, columns=col)
        # ConcBmOverEurocode22004
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Eurocode 2-2004', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Eurocode 2-2004', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Eurocode 2-2004', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - Eurocode 2-2004', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverEurocode22004 = pd.DataFrame(data, columns=col)
        # ConcBmOverHongKongCP2013
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Hong Kong CP 2013', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Hong Kong CP 2013', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Hong Kong CP 2013', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - Hong Kong CP 2013', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverHongKongCP2013 = pd.DataFrame(data, columns=col)
        # ConcBmOverIS4562000
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - IS 456-2000', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - IS 456-2000', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - IS 456-2000', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - IS 456-2000', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverIS4562000 = pd.DataFrame(data, columns=col)
        # ConcBmOverItalianNTC2008
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Italian NTC 2008', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Italian NTC 2008', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Italian NTC 2008', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - Italian NTC 2008', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverItalianNTC2008 = pd.DataFrame(data, columns=col)
        # ConcBmOverKBC2009
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2009', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverKBC2009 = pd.DataFrame(data, columns=col)
        # ConcBmOverKBC2016
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - KBC 2016', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverKBC2016 = pd.DataFrame(data, columns=col)
        # ConcBmOverMexicanRCDF2004
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004',
             'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2004', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverMexicanRCDF2004 = pd.DataFrame(data, columns=col)
        # ConcBmOverMexicanRCDF2017
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017',
             'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - Mexican RCDF 2017', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverMexicanRCDF2017 = pd.DataFrame(data, columns=col)
        # ConcBmOverNZS31012006
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - NZS 3101-2006', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - NZS 3101-2006', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - NZS 3101-2006', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - NZS 3101-2006', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverNZS31012006 = pd.DataFrame(data, columns=col)
        # ConcBmOverSingaporeCP6599
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - Singapore CP 65-99', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - Singapore CP 65-99', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - Singapore CP 65-99', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - Singapore CP 65-99', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverSingaporeCP6599 = pd.DataFrame(data, columns=col)
        # ConcBmOverSP63133302012
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - SP 63-13330-2012', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - SP 63-13330-2012', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - SP 63-13330-2012', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - SP 63-13330-2012', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverSP63133302012 = pd.DataFrame(data, columns=col)
        # ConcBmOverTCVN55742012
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             '(qsw,1*Z1)/(Rs*As,1)', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             'Corner Rebar Fraction Top', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012',
             'Corner Rebar Fraction Bottom', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Gamma_b3', ''),
            ('TABLE:  Concrete Beam Overwrites - TCVN 5574-2012', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverTCVN55742012 = pd.DataFrame(data, columns=col)
        # ConcBmOverTS5002000
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000', 'Cover Type', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000', 'RLLF', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000', 'Ignore PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverTS5002000 = pd.DataFrame(data, columns=col)
        # ConcBmOverTS5002000R2018
        tuples = [
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)', 'Unique Name', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)', 'Design Type', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)',
             'Design Section', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)', 'Frame Type', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)', 'LLRF', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)',
             'Unbraced Length Ratio (Major)', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)',
             'Unbraced Length Ratio (Minor)', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)',
             'Consider Torsion?', ''),
            ('TABLE:  Concrete Beam Overwrites - TS 500-2000(R2018)', 'Ignore Beneficial Pu for Beam Design?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcBmOverTS5002000R2018 = pd.DataFrame(data, columns=col)
        # ConcDesPrefACI31808
        tuples = [
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-08', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefACI31808 = pd.DataFrame(data, columns=col)
        # ConcDesPrefACI31811
        tuples = [
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-11', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefACI31811 = pd.DataFrame(data, columns=col)
        # ConcDesPrefACI31814
        tuples = [
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-14', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefACI31814 = pd.DataFrame(data, columns=col)
        # ConcDesPrefACI31819
        tuples = [
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - ACI 318-19', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefACI31819 = pd.DataFrame(data, columns=col)
        # ConcDesPrefAS360009
        tuples = [
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'RebarClass', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'Min. As Criteria', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-09', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefAS360009 = pd.DataFrame(data, columns=col)
        # ConcDesPrefAS36002018
        tuples = [
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'RebarClass', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PhiTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PhiComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Min. As Criteria', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - AS 3600-2018', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefAS36002018 = pd.DataFrame(data, columns=col)
        # ConcDesPrefBS811097
        tuples = [
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'GammaShear', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - BS 8110-97', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefBS811097 = pd.DataFrame(data, columns=col)
        # ConcDesPrefChinese2010
        tuples = [
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'Gamma0', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'NegMomModifier', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'MinNegRebarRatio', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'MinPosRebarRatio', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'InitExtTen', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'InitExtComp', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'FinExtTen', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'SusExtTen', ''),
            ('TABLE:  Concrete Design Preferences - Chinese 2010', 'LLCoeff', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefChinese2010 = pd.DataFrame(data, columns=col)
        # ConcDesPrefCSAA23314
        tuples = [
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PhiSteel', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PhiConc', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-14', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefCSAA23314 = pd.DataFrame(data, columns=col)
        # ConcDesPrefCSAA23319
        tuples = [
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PhiSteel', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PhiConc', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - CSA A233-19', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefCSAA23319 = pd.DataFrame(data, columns=col)
        # ConcDesPrefEurocode22004
        tuples = [
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'Country', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'ComboSet', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'ReliabilityClass', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'Theta', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'AlphaCC', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'AlphaCT', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'AlphaCCLW', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'AlphaCTLW', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - Eurocode 2-2004', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefEurocode22004 = pd.DataFrame(data, columns=col)
        # ConcDesPrefHKongCP2013
        tuples = [
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'GammaShear', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - Hong Kong CP 2013', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefHKongCP2013 = pd.DataFrame(data, columns=col)
        # ConcDesPrefIS4562000
        tuples = [
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'IncreaseFlexRebar', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - IS 456-2000', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefIS4562000 = pd.DataFrame(data, columns=col)
        # ConcDesPrefItalNTC2008
        tuples = [
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'ComboSet', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'AlphaCC', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'AlphaCT', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'AlphaCCLW', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'AlphaCTLW', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - Italian NTC 2008', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefItalNTC2008 = pd.DataFrame(data, columns=col)
        # ConcDesPrefNZS31012006
        tuples = [
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'PhiBend', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'PhiShear', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - NZS 3101-2006', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefNZS31012006 = pd.DataFrame(data, columns=col)
        # ConcDesPrefSingCP6599
        tuples = [
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'GammaShear', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - Singapore CP 65-99', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefSingCP6599 = pd.DataFrame(data, columns=col)
        # ConcDesPrefSP631333012
        tuples = [
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'RelativeHumidity', '%'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'MoistureContent', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_bt', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b1ST', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_bLT', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b2', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b3Bm', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b3Col', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b4', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_b5', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_S', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Gamma_S1', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Live Load Duration Factor', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Snow Load Duration Factor', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - SP 63-13330-2012', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefSP631333012 = pd.DataFrame(data, columns=col)
        # ConcDesPrefTS5002000
        tuples = [
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'GammaSteel', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'GammaConc', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'GammaShear', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'Ignore Pu?', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000',
             'Pattern Live Load Factor', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'CoverTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'CoverBot', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'BarSize', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'InnerLayer', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'PTCGSTop', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'PTCGSBotExt', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'PTCGSBotInt', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'SlabType', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'Cover Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000',
             'Cover Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000',
             'Bar Size Beam Flexure', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000',
             'Bar Size Beam Shear', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'PTCGS Beam Top', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000',
             'PTCGS Beam Bottom', 'mm'),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'UserStress', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'InitConcRat', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'InitTopTen', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'InitBotTen', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'InitExComp', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'FinTopTen', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'FinBotTen', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'FinExComp', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'SusExComp', ''),
            ('TABLE:  Concrete Design Preferences - TS 500-2000', 'LLFraction', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcDesPrefTS5002000 = pd.DataFrame(data, columns=col)
        # ConcFrameDesignComboData
        tuples = [
            ('TABLE:  Concrete Frame Design Load Combination Data', 'Combo Type', ''),
            ('TABLE:  Concrete Frame Design Load Combination Data', 'Combo Name', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcFrameDesignComboData = pd.DataFrame(data, columns=col)

        # ConcSlabDesignComboData
        tuples = [
            ('TABLE:  Concrete Slab Design Load Combination Data', 'Combo Type', ''),
            ('TABLE:  Concrete Slab Design Load Combination Data', 'Combo Name', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlabDesignComboData = pd.DataFrame(data, columns=col)

        # ConcSlbOverFEBased
        tuples = [
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based',
             'Unique Name', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based',
             'Rebar Material', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based',
             'Cover Specification Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'LLRF', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Design?', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Finite Element Based', 'Ignote PT?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlbOverFEBased = pd.DataFrame(data, columns=col)

        # ConcSlbOverPunchShrGen
        tuples = [
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Unique Name', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Check Punching Shear?', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Location Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Perimeter Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Effective Depth Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General',
             'Opening Definition', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - General', 'Reinforcement Type', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlbOverPunchShrGen = pd.DataFrame(data, columns=col)
        # ConcSlbOverPunchShrOpn
        tuples = [
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Openings',
             'Unique Name', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Openings',
             'Opening Number', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Openings',
             'Opening Shape', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Openings',
             'X Offset', 'mm'),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Openings', 'Y Offset', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlbOverPunchShrOpn = pd.DataFrame(data, columns=col)
        # ConcSlbOverPunchShrPer
        tuples = [
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter',
             'Unique Name', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter',
             'Point Number', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter', 'X Coord', 'mm'),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter', 'Y Coord', 'mm'),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter', 'Radius', 'mm'),
            ('TABLE:  Concrete Slab Design Overwrites - Punching Shear - Perimeter', 'Is Null?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlbOverPunchShrPer = pd.DataFrame(data, columns=col)
        # ConcSlbOverStripBased
        tuples = [
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based', 'Strip Name', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based', 'Strip Layer', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based',
             'Strip Design Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based', 'Design?', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based', 'Ignote PT?', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based',
             'Rebar Material', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based',
             'Cover Specification Type', ''),
            ('TABLE:  Concrete Slab Design Overwrites - Strip Based', 'LLRF', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ConcSlbOverStripBased = pd.DataFrame(data, columns=col)

        # DatabaseExcelNames
        tuples = [
            ('TABLE:  Database Excel Names', 'TableKey', ''),
            ('TABLE:  Database Excel Names', 'ExcelName', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DatabaseExcelNames = pd.DataFrame(data, columns=col)
        # DatabaseFieldNames
        tuples = [
            ('TABLE:  Database Field Names', 'TableKey', ''),
            ('TABLE:  Database Field Names', 'FieldKey', ''),
            ('TABLE:  Database Field Names', 'FieldName', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DatabaseFieldNames = pd.DataFrame(data, columns=col)
        # DatabaseTableNamedSetDefs
        tuples = [
            ('TABLE:  Database Table Named Set Definitions', 'Name', ''),
            ('TABLE:  Database Table Named Set Definitions',
             'Base Reaction Option', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Mode Option', ''),
            ('TABLE:  Database Table Named Set Definitions',
             'Buckling Mode Option', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Multistep', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Nonlinear Static', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Modal History', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Direct History', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Load Combination', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Show Unformatted', ''),
            ('TABLE:  Database Table Named Set Definitions', 'Expose All', ''),
            ('TABLE:  Database Table Named Set Definitions', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DatabaseTableNamedSetDefs = pd.DataFrame(data, columns=col)
        # DatabaseTableNames
        tuples = [
            ('TABLE:  Database Table Names', 'TableKey', ''),
            ('TABLE:  Database Table Names', 'TableName', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DatabaseTableNames = pd.DataFrame(data, columns=col)
        # DeckPropertyDefinitions
        tuples = [
            ('TABLE:  Deck Property Definitions', 'Name', ''),
            ('TABLE:  Deck Property Definitions', 'Deck Type', ''),
            ('TABLE:  Deck Property Definitions', 'Slab Material', ''),
            ('TABLE:  Deck Property Definitions', 'Deck Material', ''),
            ('TABLE:  Deck Property Definitions', 'Slab Depth', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Rib Depth', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Rib Width Top', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Rib Width Bottom', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Rib Spacing', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Deck Shear Thickness', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Deck Unit Weight', 'kN/m²'),
            ('TABLE:  Deck Property Definitions', 'Shear Stud Diameter', 'mm'),
            ('TABLE:  Deck Property Definitions', 'Shear Stud Height', 'mm'),
            ('TABLE:  Deck Property Definitions',
             'Shear Stud Tensile Strength', 'MPa'),
            ('TABLE:  Deck Property Definitions', 'f11 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'f22 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'f12 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'm11 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'm22 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'm12 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'v13 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'v23 Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'Mass Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'Weight Modifier', ''),
            ('TABLE:  Deck Property Definitions', 'Color', ''),
            ('TABLE:  Deck Property Definitions', 'GUID', ''),
            ('TABLE:  Deck Property Definitions', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DeckPropertyDefinitions = pd.DataFrame(data, columns=col)
        # DimensionLineObjectGeometry
        tuples = [
            ('TABLE:  Dimension Line Object Geometry', 'Type', ''),
            ('TABLE:  Dimension Line Object Geometry', 'Drawn X1', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Drawn Y1', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Drawn X2', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Drawn Y2', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Display X1', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Display Y1', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Display X2', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Display Y2', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'Length', 'm'),
            ('TABLE:  Dimension Line Object Geometry', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.DimensionLineObjectGeometry = pd.DataFrame(data, columns=col)
        # FloorObjectConnectivity
        tuples = [
            ('TABLE:  Floor Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Floor Object Connectivity', 'UniquePt1', ''),
            ('TABLE:  Floor Object Connectivity', 'UniquePt2', ''),
            ('TABLE:  Floor Object Connectivity', 'UniquePt3', ''),
            ('TABLE:  Floor Object Connectivity', 'UniquePt4', ''),
            ('TABLE:  Floor Object Connectivity', 'Perimeter', 'm'),
            ('TABLE:  Floor Object Connectivity', 'Area', 'm²'),
            ('TABLE:  Floor Object Connectivity', 'Order', ''),
            ('TABLE:  Floor Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FloorObjectConnectivity = pd.DataFrame(data, columns=col)

        # FloorObjectCurvedEdgeData
        tuples = [
            ('TABLE:  Floor Object Curved Edge Data', 'Unique Name', ''),
            ('TABLE:  Floor Object Curved Edge Data', 'Edge Number', ''),
            ('TABLE:  Floor Object Curved Edge Data', 'Curve Type', ''),
            ('TABLE:  Floor Object Curved Edge Data', 'ICP Number', ''),
            ('TABLE:  Floor Object Curved Edge Data', 'Global X', 'm'),
            ('TABLE:  Floor Object Curved Edge Data', 'Global Y', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FloorObjectCurvedEdgeData = pd.DataFrame(data, columns=col)
        # FloorVibrationExcitationSets
        tuples = [
            ('TABLE:  Floor Vibration Excitation Sets', 'Name', ''),
            ('TABLE:  Floor Vibration Excitation Sets', 'Excitation Type', ''),
            ('TABLE:  Floor Vibration Excitation Sets', 'Excitation Joint', ''),
            ('TABLE:  Floor Vibration Excitation Sets', 'Response Joint', ''),
            ('TABLE:  Floor Vibration Excitation Sets', 'Modal Case', ''),
            ('TABLE:  Floor Vibration Excitation Sets', 'Damping', 'sec'),
            ('TABLE:  Floor Vibration Excitation Sets', 'Last Frequency', 'cyc/sec'),
            ('TABLE:  Floor Vibration Excitation Sets', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FloorVibrationExcitationSets = pd.DataFrame(data, columns=col)
        # FrameAssignsAdditionalMass
        tuples = [
            ('TABLE:  Frame Assignments - Additional Mass', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Additional Mass', 'Added Mass', 'kg/m')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsAdditionalMass = pd.DataFrame(data, columns=col)
        # FrameAssignsEndLenOffsets
        tuples = [
            ('TABLE:  Frame Assignments - End Length Offsets', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - End Length Offsets', 'Offset Option', ''),
            ('TABLE:  Frame Assignments - End Length Offsets', 'Offset I', 'mm'),
            ('TABLE:  Frame Assignments - End Length Offsets', 'Offset J', 'mm'),
            ('TABLE:  Frame Assignments - End Length Offsets', 'Rigid Factor', ''),
            ('TABLE:  Frame Assignments - End Length Offsets', 'Self Weight Option', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsEndLenOffsets = pd.DataFrame(data, columns=col)

        # FrameAssignsFloorCracking
        tuples = [
            ('TABLE:  Frame Assignments - Floor Cracking', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Floor Cracking', 'Consider for Cracking', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsFloorCracking = pd.DataFrame(data, columns=col)
        # FrameAssignsFloorMeshOpt
        tuples = [
            ('TABLE:  Frame Assignments - Frame Floor Meshing Option', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Frame Floor Meshing Option', 'Offset Option', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsFloorMeshOpt = pd.DataFrame(data, columns=col)
        # FrameAssignsFrameAutoMesh
        tuples = [
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Auto Mesh', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options',
             'At Intermediate Joints', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options',
             'At Intersections', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Min Number?', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Max Length?', ''),
            ('TABLE:  Frame Assignments - Frame Auto Mesh Options', 'Segment Length', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsFrameAutoMesh = pd.DataFrame(data, columns=col)

        # FrameAssignsInsertionPoint
        tuples = [
            ('TABLE:  Frame Assignments - Insertion Point', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Insertion Point', 'Cardinal Point', ''),
            ('TABLE:  Frame Assignments - Insertion Point', 'Mirror2', ''),
            ('TABLE:  Frame Assignments - Insertion Point', 'Mirror3', ''),
            ('TABLE:  Frame Assignments - Insertion Point', 'No Transform Stiffness', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsInsertionPoint = pd.DataFrame(data, columns=col)

        # FrameAssignsLineSprings
        tuples = [
            ('TABLE:  Frame Assignments - Line Springs', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Line Springs', 'Spring Property', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsLineSprings = pd.DataFrame(data, columns=col)
        # FrameAssignsLocalAxes
        tuples = [
            ('TABLE:  Frame Assignments - Local Axes', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Local Axes', 'Angle', 'deg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsLocalAxes = pd.DataFrame(data, columns=col)
        # FrameAssignsNonprisParams
        tuples = [
            ('TABLE:  Frame Assignments - Advanced Nonprismatic Section Parameters',
             'UniqueName', ''),
            ('TABLE:  Frame Assignments - Advanced Nonprismatic Section Parameters',
             'Clear Length', 'm'),
            ('TABLE:  Frame Assignments - Advanced Nonprismatic Section Parameters', 'Relative Distance', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsNonprisParams = pd.DataFrame(data, columns=col)
        # FrameAssignsOutputStations
        tuples = [
            ('TABLE:  Frame Assignments - Output Stations', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Output Stations', 'Station Option', ''),
            ('TABLE:  Frame Assignments - Output Stations', 'Min Stations', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsOutputStations = pd.DataFrame(data, columns=col)

        # FrameAssignsPropModifiers
        tuples = [
            ('TABLE:  Frame Assignments - Property Modifiers', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'Area Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'As2 Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'As3 Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'J Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'I22 Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'I33 Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'Mass Modifier', ''),
            ('TABLE:  Frame Assignments - Property Modifiers', 'Weight Modifier', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsPropModifiers = pd.DataFrame(data, columns=col)
        # FrameAssignsReleases
        tuples = [
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'PI', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'PJ', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'V2I', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'V2J', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'V3I', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'V3J', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'TI', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'TJ', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'M2I', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'M2J', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'M3I', ''),
            ('TABLE:  Frame Assignments - Releases and Partial Fixity', 'M3J', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsReleases = pd.DataFrame(data, columns=col)

        # FrameAssignsSectProp
        tuples = [
            ('TABLE:  Frame Assignments - Section Properties', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Section Properties', 'Shape', ''),
            ('TABLE:  Frame Assignments - Section Properties', 'Auto Select List', ''),
            ('TABLE:  Frame Assignments - Section Properties', 'Section Property', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsSectProp = pd.DataFrame(data, columns=col)

        # FrameAssignsSummary
        tuples = [
            ('TABLE:  Frame Assignments - Summary', 'UniqueName', ''),
            ('TABLE:  Frame Assignments - Summary', 'Design Type', ''),
            ('TABLE:  Frame Assignments - Summary', 'Length', 'm'),
            ('TABLE:  Frame Assignments - Summary', 'Analysis Section', ''),
            ('TABLE:  Frame Assignments - Summary', 'Design Section', ''),
            ('TABLE:  Frame Assignments - Summary', 'Axis Angle', 'deg'),
            ('TABLE:  Frame Assignments - Summary', 'Min Number Stations', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameAssignsSummary = pd.DataFrame(data, columns=col)
        # FrameLoadsDistributed
        tuples = [
            ('TABLE:  Frame Loads Assignments - Distributed', 'UniqueName', ''),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Load Pattern', ''),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Load Type', ''),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Direction', ''),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Distance Type', ''),
            ('TABLE:  Frame Loads Assignments - Distributed',
             'Relative Distance A', ''),
            ('TABLE:  Frame Loads Assignments - Distributed',
             'Relative Distance B', ''),
            ('TABLE:  Frame Loads Assignments - Distributed',
             'Absolute Distance A', 'm'),
            ('TABLE:  Frame Loads Assignments - Distributed',
             'Absolute Distance B', 'm'),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Force A', 'kN/m'),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Force B', 'kN/m'),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Moment A', 'kN-m/m'),
            ('TABLE:  Frame Loads Assignments - Distributed', 'Moment B', 'kN-m/m'),
            ('TABLE:  Frame Loads Assignments - Distributed', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameLoadsDistributed = pd.DataFrame(data, columns=col)

        # FrameLoadsPoint
        tuples = [
            ('TABLE:  Frame Loads Assignments - Point', 'UniqueName', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Load Pattern', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Load Type', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Direction', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Distance Type', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Relative Distance', ''),
            ('TABLE:  Frame Loads Assignments - Point', 'Absolute Distance', 'm'),
            ('TABLE:  Frame Loads Assignments - Point', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameLoadsPoint = pd.DataFrame(data, columns=col)
        # FrameLoadsTemperature
        tuples = [
            ('TABLE:  Frame Loads Assignments - Temperature', 'UniqueName', ''),
            ('TABLE:  Frame Loads Assignments - Temperature', 'Load Pattern', ''),
            ('TABLE:  Frame Loads Assignments - Temperature', 'Top Temperature', 'C'),
            ('TABLE:  Frame Loads Assignments - Temperature',
             'Bottom Temperature', 'C'),
            ('TABLE:  Frame Loads Assignments - Temperature', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameLoadsTemperature = pd.DataFrame(data, columns=col)
        # FramePropSummary
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Summary', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Shape', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Area', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'J', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'I33', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'I22', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'I23', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'As2', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'As3', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'S33Pos', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'S33Neg', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'S22Pos', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'S22Neg', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Z33', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Z22', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'R33', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'R22', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'CG Offset 3', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'CG Offset 2', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'PNA Offset 3', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'PNA Offset 2', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Summary', 'Weight Modifier', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FramePropSummary = pd.DataFrame(data, columns=col)
        # FrameSecDefAutoSelList
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Auto Select List', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List',
             'DesignType', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List',
             'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List',
             'Starting Section', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List',
             'Included Section', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Auto Select List', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefAutoSelList = pd.DataFrame(data, columns=col)
        # FrameSecDefConcCircle
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Diameter', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Rigid Zone?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Column Drop Panel?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Include Column Capital?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Notional Size Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Notional User Size', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Circle', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefConcCircle = pd.DataFrame(data, columns=col)

        # FrameSecDefConcL
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Total Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Total Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Horizontal Leg Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Vertical Leg Thickness at Corner', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Vertical Leg Thickness at Tip', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Ignore Flange Area', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Mirror About 2-axis', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Mirror About 3-axis', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Rigid Zone?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Column Drop Panel?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Automatic Drop Panel Column 2-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Automatic Drop Panel Column 3-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Column Drop Panel Slab Property', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Notional Size Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Notional User Size', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Section Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete L', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefConcL = pd.DataFrame(data, columns=col)
        # FrameSecDefConcRect
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Rigid Zone?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Column Drop Panel?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Include Column Capital?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Notional Size Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Notional User Size', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Section Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Longitudinal Rebar Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Shear Rebar Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Flange Dimension Option', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Cover Top', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Cover Bottom', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Rectangular', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefConcRect = pd.DataFrame(data, columns=col)

        # FrameSecDefConcTee
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Total Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Total Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Flange Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Web Thickness at Flange', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Web Thickness at Tip', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Ignore Flange Area', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Mirror About 3-axis', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'Rigid Zone?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Column Drop Panel?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Automatic Drop Panel Column 2-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Automatic Drop Panel Column 3-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Column Drop Panel Slab Property', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Notional Size Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Notional User Size', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Section Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Tee', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefConcTee = pd.DataFrame(data, columns=col)
        # FrameSecDefConcTrapezoid
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'WidthTop', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'WidthBot', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Rigid Zone?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Column Drop Panel?', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Automatic Drop Panel Column 2-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Automatic Drop Panel Column 3-Axis', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Column Drop Panel Slab Property', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Notional Size Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Notional User Size', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Section Type', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Concrete Trapezoid', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefConcTrapezoid = pd.DataFrame(data, columns=col)
        # FrameSecDefGeneral
        tuples = [
            ('TABLE:  Frame Section Property Definitions - General', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'Area', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - General', 'As2', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - General', 'As3', 'cm²'),
            ('TABLE:  Frame Section Property Definitions - General', 'I33', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - General', 'I22', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - General', 'I23', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - General', 'S33Pos', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'S33Neg', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'S22Pos', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'S22Neg', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'R33', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'R22', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'Z33', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'Z22', 'cm³'),
            ('TABLE:  Frame Section Property Definitions - General', 'J', 'cm⁴'),
            ('TABLE:  Frame Section Property Definitions - General', 'CG Offset3', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'CG Offset2', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'PNA Offset3', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'PNA Offset2', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'SC Offset3', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'SC Offset2', 'mm'),
            ('TABLE:  Frame Section Property Definitions - General', 'Section Type', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - General', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefGeneral = pd.DataFrame(data, columns=col)
        # FrameSecDefSteelCastelltd
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Steel Castellated', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Top Root I-Section', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Bottom Root I-Section', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Hole Spacing', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Hole Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Hole Height', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Castellated', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefSteelCastelltd = pd.DataFrame(data, columns=col)
        # FrameSecDefSteelCellular
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Steel Cellular', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Top Root I-Section', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Bottom Root I-Section', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Hole Spacing', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Hole Diameter', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Cellular', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefSteelCellular = pd.DataFrame(data, columns=col)
        # FrameSecDefSteelChannel
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Total Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Flange Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Flange Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Web Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Fillet Radius', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Mirror About 2-axis', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Steel Channel', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefSteelChannel = pd.DataFrame(data, columns=col)
        # FrameSecDefSteelI
        tuples = [
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Name', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Material', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'From File?', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Total Depth', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Top Flange Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Top Flange Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Web Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Bottom Flange Width', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Bottom Flange Thickness', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Fillet Radius', 'mm'),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Area Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'As2 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'As3 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'J Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'I22 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'I33 Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Mass Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange',
             'Weight Modifier', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Color', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'GUID', ''),
            ('TABLE:  Frame Section Property Definitions - Steel I/Wide Flange', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.FrameSecDefSteelI = pd.DataFrame(data, columns=col)

        # GridDefinitionsGeneral
        tuples = [
            ('TABLE:  Grid Definitions - General', 'Name', ''),
            ('TABLE:  Grid Definitions - General', 'Type', ''),
            ('TABLE:  Grid Definitions - General', 'Ux', 'm'),
            ('TABLE:  Grid Definitions - General', 'Uy', 'm'),
            ('TABLE:  Grid Definitions - General', 'Rz', 'deg'),
            ('TABLE:  Grid Definitions - General', 'Datum Name', ''),
            ('TABLE:  Grid Definitions - General', 'Story Name Above', ''),
            ('TABLE:  Grid Definitions - General', 'Story Name Below', ''),
            ('TABLE:  Grid Definitions - General', 'Model Datum', 'm'),
            ('TABLE:  Grid Definitions - General', 'Story Height Above', 'm'),
            ('TABLE:  Grid Definitions - General', 'Story Height Below', 'm'),
            ('TABLE:  Grid Definitions - General', 'Bubble Size', 'mm'),
            ('TABLE:  Grid Definitions - General', 'Color', ''),
            ('TABLE:  Grid Definitions - General', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('GLOBAL', 'Cartesian', 0, 0, 0, 'Story1',
                 '', 'Base', 0, '', 3, 1250, 10461087, '')]
        self.GridDefinitionsGeneral = pd.DataFrame(data, columns=col)
        # GridDefinitionsGridLines
        tuples = [
            ('TABLE:  Grid Definitions - Grid Lines', 'Name', ''),
            ('TABLE:  Grid Definitions - Grid Lines', 'Grid Line Type', ''),
            ('TABLE:  Grid Definitions - Grid Lines', 'ID', ''),
            ('TABLE:  Grid Definitions - Grid Lines', 'Bubble Location', ''),
            ('TABLE:  Grid Definitions - Grid Lines', 'Visible', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.GridDefinitionsGridLines = pd.DataFrame(data, columns=col)
        # GridDefinitionsRefPoints
        tuples = [
            ('TABLE:  Grid Definitions - Reference Points', 'Name', ''),
            ('TABLE:  Grid Definitions - Reference Points', 'XorR', 'm'),
            ('TABLE:  Grid Definitions - Reference Points', 'Y', 'm'),
            ('TABLE:  Grid Definitions - Reference Points', 'Visible', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.GridDefinitionsRefPoints = pd.DataFrame(data, columns=col)

        # GroupAssignments
        tuples = [
            ('TABLE:  Group Assignments', 'Group Name', ''),
            ('TABLE:  Group Assignments', 'Object Type', ''),
            ('TABLE:  Group Assignments', 'Object Unique Name', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.GroupAssignments = pd.DataFrame(data, columns=col)

        # GroupDefinitions
        tuples = [
            ('TABLE:  Group Definitions', 'Name', ''),
            ('TABLE:  Group Definitions', 'Color', ''),
            ('TABLE:  Group Definitions', 'Steel Design?', ''),
            ('TABLE:  Group Definitions', 'Concrete Design?', ''),
            ('TABLE:  Group Definitions', 'Composite Design?', ''),
            ('TABLE:  Group Definitions', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.GroupDefinitions = pd.DataFrame(data, columns=col)

        # ImpLoadCasesRS
        tuples = [
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Name', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Coordinate System', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Angle', 'deg'),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Modal Case', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum',
             'Modal Combo Method', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Rigid Response?', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum',
             'Directional Combo Type', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Constant Damping', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Eccentricity Case', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Design Type', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'GUID', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ImpLoadCasesRS = pd.DataFrame(data, columns=col)
        # ImpLoadCasesRSFuncData
        tuples = [
            ('TABLE:  Imported Load Cases - Response Spectrum - Function Data', 'Name', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum - Function Data',
             'Period', 'sec'),
            ('TABLE:  Imported Load Cases - Response Spectrum - Function Data', 'Value', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum - Function Data',
             'Damping Ratio', ''),
            ('TABLE:  Imported Load Cases - Response Spectrum - Function Data', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ImpLoadCasesRSFuncData = pd.DataFrame(data, columns=col)
        # ImportedLoadCasesModal
        tuples = [
            ('TABLE:  Imported Load Cases - Modal', 'Name', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Number Modes', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Damping Elements', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Comp Modal Damping', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Missing Mass?', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Modal Mass', 'kN-m-s²'),
            ('TABLE:  Imported Load Cases - Modal', 'Modal Stiffness', 'kN-m-s²'),
            ('TABLE:  Imported Load Cases - Modal', 'PFUX', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'PFUY', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'PFUZ', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'PFRX', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'PFRY', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'PFRZ', 'kN-m'),
            ('TABLE:  Imported Load Cases - Modal', 'SRUX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'SRUY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'SRUZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'SRRX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'SRRY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'SRRZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRUX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRUY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRUZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRRX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRRY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DRRZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFUX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFUY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFUZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFRX', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFRY', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'DFRZ', '%'),
            ('TABLE:  Imported Load Cases - Modal', 'Design Type', ''),
            ('TABLE:  Imported Load Cases - Modal', 'GUID', ''),
            ('TABLE:  Imported Load Cases - Modal', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ImportedLoadCasesModal = pd.DataFrame(data, columns=col)
        # JointAssignsFloorMeshOpt
        tuples = [
            ('TABLE:  Joint Assignments - Floor Meshing Option', 'UniqueName', ''),
            ('TABLE:  Joint Assignments - Floor Meshing Option', 'IncludeInMesh', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointAssignsFloorMeshOpt = pd.DataFrame(data, columns=col)

        # JointAssignsRestraints
        tuples = [
            ('TABLE:  Joint Assignments - Restraints', 'UniqueName', ''),
            ('TABLE:  Joint Assignments - Restraints', 'UX', ''),
            ('TABLE:  Joint Assignments - Restraints', 'UY', ''),
            ('TABLE:  Joint Assignments - Restraints', 'UZ', ''),
            ('TABLE:  Joint Assignments - Restraints', 'RX', ''),
            ('TABLE:  Joint Assignments - Restraints', 'RY', ''),
            ('TABLE:  Joint Assignments - Restraints', 'RZ', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointAssignsRestraints = pd.DataFrame(data, columns=col)

        # JointAssignsSprings
        tuples = [
            ('TABLE:  Joint Assignments - Springs', 'UniqueName', ''),
            ('TABLE:  Joint Assignments - Springs', 'SpringProp', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointAssignsSprings = pd.DataFrame(data, columns=col)

        # JointLoadsForce
        tuples = [
            ('TABLE:  Joint Loads Assignments - Force', 'UniqueName', ''),
            ('TABLE:  Joint Loads Assignments - Force', 'Load Pattern', ''),
            ('TABLE:  Joint Loads Assignments - Force', 'FX', 'kN'),
            ('TABLE:  Joint Loads Assignments - Force', 'FY', 'kN'),
            ('TABLE:  Joint Loads Assignments - Force', 'FZ', 'kN'),
            ('TABLE:  Joint Loads Assignments - Force', 'MX', 'kN-m'),
            ('TABLE:  Joint Loads Assignments - Force', 'MY', 'kN-m'),
            ('TABLE:  Joint Loads Assignments - Force', 'MZ', 'kN-m'),
            ('TABLE:  Joint Loads Assignments - Force', 'X Dimension', 'mm'),
            ('TABLE:  Joint Loads Assignments - Force', 'Y Dimension', 'mm'),
            ('TABLE:  Joint Loads Assignments - Force', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointLoadsForce = pd.DataFrame(data, columns=col)

        # JointLoadsGrndDisplacement
        tuples = [
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'UniqueName', ''),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'Load Pattern', ''),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'UX', 'mm'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'UY', 'mm'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'UZ', 'mm'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'RX', 'rad'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'RY', 'rad'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'RZ', 'rad'),
            ('TABLE:  Joint Loads Assignments - Ground Displacement', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointLoadsGrndDisplacement = pd.DataFrame(data, columns=col)
        # JointLoadsTemperature
        tuples = [
            ('TABLE:  Joint Loads Assignments - Temperature', 'UniqueName', ''),
            ('TABLE:  Joint Loads Assignments - Temperature', 'Load Pattern', ''),
            ('TABLE:  Joint Loads Assignments - Temperature', 'T', 'C'),
            ('TABLE:  Joint Loads Assignments - Temperature', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JointLoadsTemperature = pd.DataFrame(data, columns=col)
        # JtAssignsAdditionalMass
        tuples = [
            ('TABLE:  Joint Assignments - Additional Mass', 'UniqueName', ''),
            ('TABLE:  Joint Assignments - Additional Mass', 'Mass X and Y', 'kg'),
            ('TABLE:  Joint Assignments - Additional Mass', 'MassZ', 'kg'),
            ('TABLE:  Joint Assignments - Additional Mass', 'MMI X', 'ton-m²'),
            ('TABLE:  Joint Assignments - Additional Mass', 'MMI Y', 'ton-m²'),
            ('TABLE:  Joint Assignments - Additional Mass', 'MMI Z', 'ton-m²')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JtAssignsAdditionalMass = pd.DataFrame(data, columns=col)
        # JtAssignsSummary
        tuples = [
            ('TABLE:  Joint Assignments - Summary', 'UniqueName', ''),
            ('TABLE:  Joint Assignments - Summary', 'Diaphragm', ''),
            ('TABLE:  Joint Assignments - Summary', 'Restraints', ''),
            ('TABLE:  Joint Assignments - Summary', 'Spring', ''),
            ('TABLE:  Joint Assignments - Summary', 'Include in Mesh', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.JtAssignsSummary = pd.DataFrame(data, columns=col)
        # LinkPropDefGap
        tuples = [
            ('TABLE:  Link Property Definitions - Gap', 'Name', ''),
            ('TABLE:  Link Property Definitions - Gap', 'Direction', ''),
            ('TABLE:  Link Property Definitions - Gap', 'Fixed', ''),
            ('TABLE:  Link Property Definitions - Gap', 'Mass', 'kg'),
            ('TABLE:  Link Property Definitions - Gap', 'Weight', 'kN'),
            ('TABLE:  Link Property Definitions - Gap',
             'Rotational Inertia 1', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Gap',
             'Rotational Inertia 2', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Gap',
             'Rotational Inertia 3', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Gap',
             'Length for Line Spring', 'm'),
            ('TABLE:  Link Property Definitions - Gap', 'Area for Area Spring', 'm²'),
            ('TABLE:  Link Property Definitions - Gap', 'P-Delta M2I', ''),
            ('TABLE:  Link Property Definitions - Gap', 'P-Delta M2J', ''),
            ('TABLE:  Link Property Definitions - Gap', 'P-Delta M3I', ''),
            ('TABLE:  Link Property Definitions - Gap', 'P-Delta M3J', ''),
            ('TABLE:  Link Property Definitions - Gap',
             'Stiffness Damping Factor', ''),
            ('TABLE:  Link Property Definitions - Gap', 'GUID', ''),
            ('TABLE:  Link Property Definitions - Gap', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LinkPropDefGap = pd.DataFrame(data, columns=col)
        # LinkPropDefHook
        tuples = [
            ('TABLE:  Link Property Definitions - Hook', 'Name', ''),
            ('TABLE:  Link Property Definitions - Hook', 'Direction', ''),
            ('TABLE:  Link Property Definitions - Hook', 'Fixed', ''),
            ('TABLE:  Link Property Definitions - Hook', 'Mass', 'kg'),
            ('TABLE:  Link Property Definitions - Hook', 'Weight', 'kN'),
            ('TABLE:  Link Property Definitions - Hook',
             'Rotational Inertia 1', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Hook',
             'Rotational Inertia 2', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Hook',
             'Rotational Inertia 3', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Hook',
             'Length for Line Spring', 'm'),
            ('TABLE:  Link Property Definitions - Hook',
             'Area for Area Spring', 'm²'),
            ('TABLE:  Link Property Definitions - Hook', 'P-Delta M2I', ''),
            ('TABLE:  Link Property Definitions - Hook', 'P-Delta M2J', ''),
            ('TABLE:  Link Property Definitions - Hook', 'P-Delta M3I', ''),
            ('TABLE:  Link Property Definitions - Hook', 'P-Delta M3J', ''),
            ('TABLE:  Link Property Definitions - Hook',
             'Stiffness Damping Factor', ''),
            ('TABLE:  Link Property Definitions - Hook', 'GUID', ''),
            ('TABLE:  Link Property Definitions - Hook', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LinkPropDefHook = pd.DataFrame(data, columns=col)

        # LinkPropDefLinear
        tuples = [
            ('TABLE:  Link Property Definitions - Linear', 'Name', ''),
            ('TABLE:  Link Property Definitions - Linear', 'U1 Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'U2 Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'U3 Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'R1 Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'R2Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'R3 Status', ''),
            ('TABLE:  Link Property Definitions - Linear', 'Stiffness U1', 'kN/m'),
            ('TABLE:  Link Property Definitions - Linear', 'Damping U1', 'kN-s/m'),
            ('TABLE:  Link Property Definitions - Linear', 'Mass', 'kg'),
            ('TABLE:  Link Property Definitions - Linear', 'Weight', 'kN'),
            ('TABLE:  Link Property Definitions - Linear',
             'Rotational Inertia 1', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Linear',
             'Rotational Inertia 2', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Linear',
             'Rotational Inertia 3', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Linear',
             'Length for Line Spring', 'm'),
            ('TABLE:  Link Property Definitions - Linear',
             'Area for Area Spring', 'm²'),
            ('TABLE:  Link Property Definitions - Linear', 'P-Delta M2I', ''),
            ('TABLE:  Link Property Definitions - Linear', 'P-Delta M2J', ''),
            ('TABLE:  Link Property Definitions - Linear', 'P-Delta M3I', ''),
            ('TABLE:  Link Property Definitions - Linear', 'P-Delta M3J', ''),
            ('TABLE:  Link Property Definitions - Linear',
             'Stiffness Damping Factor', ''),
            ('TABLE:  Link Property Definitions - Linear', 'GUID', ''),
            ('TABLE:  Link Property Definitions - Linear', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Link1', 'Active', 'Inactive', 'Inactive', 'Inactive', 'Inactive',
                 'Inactive', 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, '', '')]
        self.LinkPropDefLinear = pd.DataFrame(data, columns=col)

        # LinkPropDefMultiElastic
        tuples = [
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Name', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Direction', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Fixed', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Mass', 'kg'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Weight', 'kN'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Rotational Inertia 1', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Rotational Inertia 2', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Rotational Inertia 3', 'ton-m²'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Length for Line Spring', 'm'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Area for Area Spring', 'm²'),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'P-Delta M2I', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'P-Delta M2J', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'P-Delta M3I', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'P-Delta M3J', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic',
             'Stiffness Damping Factor', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'GUID', ''),
            ('TABLE:  Link Property Definitions - Multilinear - Elastic', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LinkPropDefMultiElastic = pd.DataFrame(data, columns=col)
        # LinkPropDefSummary
        tuples = [
            ('TABLE:  Link Property Definitions - Summary', 'Name', ''),
            ('TABLE:  Link Property Definitions - Summary', 'Type', ''),
            ('TABLE:  Link Property Definitions - Summary', 'Degrees of Freedom', ''),
            ('TABLE:  Link Property Definitions - Summary', 'Mass', 'kg'),
            ('TABLE:  Link Property Definitions - Summary', 'Weight', 'kN'),
            ('TABLE:  Link Property Definitions - Summary', 'Defined Length', 'm'),
            ('TABLE:  Link Property Definitions - Summary', 'Defined Area', 'm²')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LinkPropDefSummary = pd.DataFrame(data, columns=col)
        # LoadCasesHyperstatic
        tuples = [
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Name', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Mass Source', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Base Case', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Support Type', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Design Type', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'GUID', ''),
            ('TABLE:  Load Case Definitions - Hyperstatic', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCasesHyperstatic = pd.DataFrame(data, columns=col)
        # LoadCasesLinearStatic
        tuples = [
            ('TABLE:  Load Case Definitions - Linear Static', 'Name', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Exclude Group', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Mass Source', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Initial Condition', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Nonlinear Case', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Load Type', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Load Name', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Load SF', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Design Type', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'GUID', ''),
            ('TABLE:  Load Case Definitions - Linear Static', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCasesLinearStatic = pd.DataFrame(data, columns=col)

        # LoadCasesNonlinearStaged
        tuples = [
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'Name', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Mass Source', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Initial Condition', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'Stage', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Stage Name', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'Duration', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'Output?', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Geometric Nonlinearity', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Results Type', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Max Total Steps', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Max Null Steps', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Materials Time Dep?', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction',
             'Design Type', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'GUID', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Staged Construction', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCasesNonlinearStaged = pd.DataFrame(data, columns=col)
        # LoadCasesNonlinearStatic
        tuples = [
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Name', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Mass Source', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static',
             'Initial Condition', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static',
             'Geometric Nonlinearity', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Results Type', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Max Total Steps', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Max Null Steps', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Design Type', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'GUID', ''),
            ('TABLE:  Load Case Definitions - Nonlinear Static', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCasesNonlinearStatic = pd.DataFrame(data, columns=col)
        # LoadCasesSummary
        tuples = [
            ('TABLE:  Load Case Definitions - Summary', 'Name', ''),
            ('TABLE:  Load Case Definitions - Summary', 'Type', ''),
            ('TABLE:  Load Case Definitions - Summary', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCasesSummary = pd.DataFrame(data, columns=col)
        # LoadCombinationDefinitions
        tuples = [
            ('TABLE:  Load Combination Definitions', 'Name', ''),
            ('TABLE:  Load Combination Definitions', 'Type', ''),
            ('TABLE:  Load Combination Definitions', 'Is Auto', ''),
            ('TABLE:  Load Combination Definitions', 'Load Name', ''),
            ('TABLE:  Load Combination Definitions', 'SF', ''),
            ('TABLE:  Load Combination Definitions', 'GUID', ''),
            ('TABLE:  Load Combination Definitions', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadCombinationDefinitions = pd.DataFrame(data, columns=col)

        # LoadPatternDefinitions
        tuples = [
            ('TABLE:  Load Pattern Definitions', 'Name', ''),
            ('TABLE:  Load Pattern Definitions', 'Is Auto Load', ''),
            ('TABLE:  Load Pattern Definitions', 'Type', ''),
            ('TABLE:  Load Pattern Definitions', 'Self Weight Multiplier', ''),
            ('TABLE:  Load Pattern Definitions', 'Auto Load', ''),
            ('TABLE:  Load Pattern Definitions', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.LoadPatternDefinitions = pd.DataFrame(data, columns=col)

        # MassSourceDefinition
        tuples = [
            ('TABLE:  Mass Source Definition', 'Name', ''),
            ('TABLE:  Mass Source Definition', 'Is Default', ''),
            ('TABLE:  Mass Source Definition', 'Include Lateral Mass?', ''),
            ('TABLE:  Mass Source Definition', 'Include Vertical Mass?', ''),
            ('TABLE:  Mass Source Definition', 'Lump Mass?', ''),
            ('TABLE:  Mass Source Definition', 'Source Self Mass?', ''),
            ('TABLE:  Mass Source Definition', 'Source Added Mass?', ''),
            ('TABLE:  Mass Source Definition', 'Source Load Patterns?', ''),
            ('TABLE:  Mass Source Definition', 'Move Mass Centroid?', ''),
            ('TABLE:  Mass Source Definition', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('MsSrc1', 'Yes', 'No', 'Yes',
                 'Yes', 'Yes', 'Yes', 'No', 'No', '')]
        self.MassSourceDefinition = pd.DataFrame(data, columns=col)

        # MassSummarybyGroup
        tuples = [
            ('TABLE:  Mass Summary by Group', 'Group', ''),
            ('TABLE:  Mass Summary by Group', 'Self Mass', 'kg'),
            ('TABLE:  Mass Summary by Group', 'Self Weight', 'kN'),
            ('TABLE:  Mass Summary by Group', 'Mass X', 'kg'),
            ('TABLE:  Mass Summary by Group', 'Mass Y', 'kg'),
            ('TABLE:  Mass Summary by Group', 'Mass Z', 'kg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MassSummarybyGroup = pd.DataFrame(data, columns=col)

        # MatPropBasicMechProps
        tuples = [
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'Material', ''),
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'DensityType', ''),
            ('TABLE:  Material Properties - Basic Mechanical Properties',
             'UnitWeight', 'kN/m³'),
            ('TABLE:  Material Properties - Basic Mechanical Properties',
             'UnitMass', 'kg/m³'),
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'E1', 'MPa'),
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'G12', 'MPa'),
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'U12', ''),
            ('TABLE:  Material Properties - Basic Mechanical Properties', 'A1', '1/C')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropBasicMechProps = pd.DataFrame(data, columns=col)

        # MatPropConcreteData
        tuples = [
            ('TABLE:  Material Properties - Concrete Data', 'Material', ''),
            ('TABLE:  Material Properties - Concrete Data', 'Fc', 'MPa'),
            ('TABLE:  Material Properties - Concrete Data', 'LtWtConc', ''),
            ('TABLE:  Material Properties - Concrete Data', 'IsUserFr', ''),
            ('TABLE:  Material Properties - Concrete Data', 'SSCurveOpt', ''),
            ('TABLE:  Material Properties - Concrete Data', 'SSHysType', ''),
            ('TABLE:  Material Properties - Concrete Data', 'SFc', 'mm/mm'),
            ('TABLE:  Material Properties - Concrete Data', 'SCap', 'mm/mm'),
            ('TABLE:  Material Properties - Concrete Data', 'FinalSlope', ''),
            ('TABLE:  Material Properties - Concrete Data', 'FAngle', 'deg'),
            ('TABLE:  Material Properties - Concrete Data', 'DAngle', 'deg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropConcreteData = pd.DataFrame(data, columns=col)

        # MatPropGeneral
        tuples = [
            ('TABLE:  Material Properties - General', 'Material', ''),
            ('TABLE:  Material Properties - General', 'Type', ''),
            ('TABLE:  Material Properties - General', 'SymType', ''),
            ('TABLE:  Material Properties - General', 'Grade', ''),
            ('TABLE:  Material Properties - General', 'Color', ''),
            ('TABLE:  Material Properties - General', 'GUID', ''),
            ('TABLE:  Material Properties - General', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropGeneral = pd.DataFrame(data, columns=col)

        # MatPropOtherData
        tuples = [
            ('TABLE:  Material Properties - Other Data', 'Material', ''),
            ('TABLE:  Material Properties - Other Data', 'SSHysType', ''),
            ('TABLE:  Material Properties - Other Data', 'FAngle', 'deg'),
            ('TABLE:  Material Properties - Other Data', 'DAngle', 'deg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropOtherData = pd.DataFrame(data, columns=col)

        # MatPropRebarData
        tuples = [
            ('TABLE:  Material Properties - Rebar Data', 'Material', ''),
            ('TABLE:  Material Properties - Rebar Data', 'Fy', 'MPa'),
            ('TABLE:  Material Properties - Rebar Data', 'Fu', 'MPa'),
            ('TABLE:  Material Properties - Rebar Data', 'Fye', 'MPa'),
            ('TABLE:  Material Properties - Rebar Data', 'Fue', 'MPa'),
            ('TABLE:  Material Properties - Rebar Data', 'SSCurveOpt', ''),
            ('TABLE:  Material Properties - Rebar Data', 'SSHysType', ''),
            ('TABLE:  Material Properties - Rebar Data', 'SHard', 'mm/mm'),
            ('TABLE:  Material Properties - Rebar Data', 'SCap', 'mm/mm'),
            ('TABLE:  Material Properties - Rebar Data', 'FinalSlope', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropRebarData = pd.DataFrame(data, columns=col)

        # MatPropSteelData
        tuples = [
            ('TABLE:  Material Properties - Steel Data', 'Material', ''),
            ('TABLE:  Material Properties - Steel Data', 'Fy', 'MPa'),
            ('TABLE:  Material Properties - Steel Data', 'Fu', 'MPa'),
            ('TABLE:  Material Properties - Steel Data', 'Fye', 'MPa'),
            ('TABLE:  Material Properties - Steel Data', 'Fue', 'MPa'),
            ('TABLE:  Material Properties - Steel Data', 'SSCurveOpt', ''),
            ('TABLE:  Material Properties - Steel Data', 'SSHysType', ''),
            ('TABLE:  Material Properties - Steel Data', 'SHard', 'mm/mm'),
            ('TABLE:  Material Properties - Steel Data', 'SMax', 'mm/mm'),
            ('TABLE:  Material Properties - Steel Data', 'SRup', 'mm/mm'),
            ('TABLE:  Material Properties - Steel Data', 'FinalSlope', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropSteelData = pd.DataFrame(data, columns=col)

        # MatPropTendonData
        tuples = [
            ('TABLE:  Material Properties - Tendon Data', 'Material', ''),
            ('TABLE:  Material Properties - Tendon Data', 'Fy', 'MPa'),
            ('TABLE:  Material Properties - Tendon Data', 'Fu', 'MPa'),
            ('TABLE:  Material Properties - Tendon Data', 'SSCurveOpt', ''),
            ('TABLE:  Material Properties - Tendon Data', 'SSHysType', ''),
            ('TABLE:  Material Properties - Tendon Data', 'FinalSlope', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MatPropTendonData = pd.DataFrame(data, columns=col)

        # MaterialListbyObjectType
        tuples = [
            ('TABLE:  Material List by Object Type', 'Object Type', ''),
            ('TABLE:  Material List by Object Type', 'Material', ''),
            ('TABLE:  Material List by Object Type', 'Weight', 'kN'),
            ('TABLE:  Material List by Object Type', 'Number Pieces', ''),
            ('TABLE:  Material List by Object Type', 'Number Studs', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MaterialListbyObjectType = pd.DataFrame(data, columns=col)
        # MaterialListbySectionProp
        tuples = [
            ('TABLE:  Material List by Section Property', 'Section', ''),
            ('TABLE:  Material List by Section Property', 'Object Type', ''),
            ('TABLE:  Material List by Section Property', 'Number Pieces', ''),
            ('TABLE:  Material List by Section Property', 'Length', 'm'),
            ('TABLE:  Material List by Section Property', 'Weight', 'kN'),
            ('TABLE:  Material List by Section Property', 'Number Studs', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.MaterialListbySectionProp = pd.DataFrame(data, columns=col)

        # MiscellaneousOptions
        tuples = [
            ('TABLE:  Miscellaneous Options', 'Start Page', ''),
            ('TABLE:  Miscellaneous Options', 'Value at Cursor', ''),
            ('TABLE:  Miscellaneous Options', 'Frame Info', ''),
            ('TABLE:  Miscellaneous Options', 'Bounding Plane', ''),
            ('TABLE:  Miscellaneous Options', 'Architectural Dimensioning', ''),
            ('TABLE:  Miscellaneous Options', 'Moments on Tension Side', ''),
            ('TABLE:  Miscellaneous Options', 'Animation Sound', ''),
            ('TABLE:  Miscellaneous Options', 'Auto Save', ''),
            ('TABLE:  Miscellaneous Options', 'Crosshairs', ''),
            ('TABLE:  Miscellaneous Options', 'Model Explorer', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Yes', 'Yes', 'Yes', 'No', 'No',
                 'Yes', 'Yes', 'No', 'No', 'Yes')]
        self.MiscellaneousOptions = pd.DataFrame(data, columns=col)
        # ModalCasesEigen
        tuples = [
            ('TABLE:  Modal Case Definitions - Eigen', 'Name', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Exclude Group', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Mass Source', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Initial Condition', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Max Modes', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Min Modes', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Freq Shift', 'cyc/sec'),
            ('TABLE:  Modal Case Definitions - Eigen', 'Cutoff Freq', 'cyc/sec'),
            ('TABLE:  Modal Case Definitions - Eigen', 'Convergence Tol', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Auto Shift?', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Design Type', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'GUID', ''),
            ('TABLE:  Modal Case Definitions - Eigen', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ModalCasesEigen = pd.DataFrame(data, columns=col)
        # ModalCasesRitz
        tuples = [
            ('TABLE:  Modal Case Definitions - Ritz', 'Name', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Exclude Group', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Mass Source', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Initial Condition', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Load Type', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Load Name', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Max Cycles', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Target Ratio', '%'),
            ('TABLE:  Modal Case Definitions - Ritz', 'Max Modes', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Min Modes', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Design Type', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'GUID', ''),
            ('TABLE:  Modal Case Definitions - Ritz', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ModalCasesRitz = pd.DataFrame(data, columns=col)
        # NullAreaObjectConnectivity
        tuples = [
            ('TABLE:  Null Area Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Null Area Object Connectivity', 'UniquePt1', ''),
            ('TABLE:  Null Area Object Connectivity', 'UniquePt2', ''),
            ('TABLE:  Null Area Object Connectivity', 'UniquePt3', ''),
            ('TABLE:  Null Area Object Connectivity', 'UniquePt4', ''),
            ('TABLE:  Null Area Object Connectivity', 'Perimeter', 'm'),
            ('TABLE:  Null Area Object Connectivity', 'Area', 'm²'),
            ('TABLE:  Null Area Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.NullAreaObjectConnectivity = pd.DataFrame(data, columns=col)

        # NullLineObjectConnectivity
        tuples = [
            ('TABLE:  Null Line Object Connectivity', 'Unique Name', ''),
            ('TABLE:  Null Line Object Connectivity', 'UniquePtI', ''),
            ('TABLE:  Null Line Object Connectivity', 'UniquePtJ', ''),
            ('TABLE:  Null Line Object Connectivity', 'Length', 'm'),
            ('TABLE:  Null Line Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.NullLineObjectConnectivity = pd.DataFrame(data, columns=col)

        # OptionsColorsDisplay
        tuples = [
            ('TABLE:  Options - Colors - Display', 'Device Type', ''),
            ('TABLE:  Options - Colors - Display', 'ColorColumns', ''),
            ('TABLE:  Options - Colors - Display', 'ColorBeams', ''),
            ('TABLE:  Options - Colors - Display', 'ColorBraces', ''),
            ('TABLE:  Options - Colors - Display', 'ColorLinks', ''),
            ('TABLE:  Options - Colors - Display', 'ColorSprings', ''),
            ('TABLE:  Options - Colors - Display', 'ColorTendons', ''),
            ('TABLE:  Options - Colors - Display', 'ColorSlabPanels', ''),
            ('TABLE:  Options - Colors - Display', 'ColorWalls', ''),
            ('TABLE:  Options - Colors - Display', 'ColorFloors', ''),
            ('TABLE:  Options - Colors - Display', 'ColorNullObjects', ''),
            ('TABLE:  Options - Colors - Display', 'ColorOpenings', ''),
            ('TABLE:  Options - Colors - Display', 'ColorColStripA', ''),
            ('TABLE:  Options - Colors - Display', 'ColorColStripB', ''),
            ('TABLE:  Options - Colors - Display', 'ColorMidStripA', ''),
            ('TABLE:  Options - Colors - Display', 'ColorMidStripB', ''),
            ('TABLE:  Options - Colors - Display', 'ColorStripEdge', ''),
            ('TABLE:  Options - Colors - Display', 'ColorOtherStripFill', ''),
            ('TABLE:  Options - Colors - Display', 'ColorSupportLines', ''),
            ('TABLE:  Options - Colors - Display', 'ColorDimLines', ''),
            ('TABLE:  Options - Colors - Display', 'ColorText', ''),
            ('TABLE:  Options - Colors - Display', 'ColorGridLines', ''),
            ('TABLE:  Options - Colors - Display', 'ColorBackground', ''),
            ('TABLE:  Options - Colors - Display', 'ColorUserMeshEdge', ''),
            ('TABLE:  Options - Colors - Display', 'ColorStlDesign', ''),
            ('TABLE:  Options - Colors - Display', 'ColorCompDesign', ''),
            ('TABLE:  Options - Colors - Display', 'ColorConcDesign', ''),
            ('TABLE:  Options - Colors - Display', 'ColorNoDesign', ''),
            ('TABLE:  Options - Colors - Display', 'TransColumns', ''),
            ('TABLE:  Options - Colors - Display', 'TransBeams', ''),
            ('TABLE:  Options - Colors - Display', 'TransBraces', ''),
            ('TABLE:  Options - Colors - Display', 'TransFloors', ''),
            ('TABLE:  Options - Colors - Display', 'TransWalls', ''),
            ('TABLE:  Options - Colors - Display', 'TransLinks', ''),
            ('TABLE:  Options - Colors - Display', 'Darkness', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Screen', 10507331, 10507331, 10507331, 10507331, 'Black', 'DarkCyan', 'Green', 'Red', 'Gray4',
                 'Gray8Dark', 'Black', 'Red', 'Blue', 'Yellow', 'Cyan', 16744703, 'Green', 13816320, 10461087, 2105376,
                 'Gray6', 'White', 5065984, 'DarkCyan', 'Cyan', 4227327, 4227327, 0.5, 0.5, 0.5, 0.5, 0.4, 0.5, 0.3),
                ('Printer', 'Black', 'Black', 'Black', 'Gray8Dark', 'Gray8Dark', 'Black', 'Green', 'Gray4', 'Gray4',
                 'Gray4', 'Gray8Dark', 'Black', 'Black',
                 'Black', 'Black', 'Black', 'Black', 'Black', 'Gray4', 'Black', 'Gray4', 'White', 'Gray4', 'Gray8Dark',
                 'Gray4', 0.5, 0.5, 0.5, 0.5, 0.4, 0.5, 0.5),
                ('Color Printer', 'Blue', 'Blue', 'Blue', 10516060, 'DarkCyan', 'DarkCyan', 'Green', 14865869, 'Gray4',
                 'Gray8Dark', 'Black', 'DarkCyan', 'DarkCyan', 'DarkCyan',
                 'DarkCyan', 'DarkCyan', 'DarkCyan', 'DarkCyan', 10461087, 'Black', 'Gray8Dark', 'White', 5065984,
                 'DarkCyan', 'Cyan', 4227327, 4227327, 0.5, 0.5, 0.5, 0.5, 0.4, 0.5, 0.3)
                ]
        self.OptionsColorsDisplay = pd.DataFrame(data, columns=col)
        # OptionsColorsOutput
        tuples = [
            ('TABLE:  Options - Colors - Output', 'Device Type', ''),
            ('TABLE:  Options - Colors - Output', 'Contour1', ''),
            ('TABLE:  Options - Colors - Output', 'Contour2', ''),
            ('TABLE:  Options - Colors - Output', 'Contour3', ''),
            ('TABLE:  Options - Colors - Output', 'Contour4', ''),
            ('TABLE:  Options - Colors - Output', 'Contour5', ''),
            ('TABLE:  Options - Colors - Output', 'Contour6', ''),
            ('TABLE:  Options - Colors - Output', 'Contour7', ''),
            ('TABLE:  Options - Colors - Output', 'Contour8', ''),
            ('TABLE:  Options - Colors - Output', 'Contour9', ''),
            ('TABLE:  Options - Colors - Output', 'Contour10', ''),
            ('TABLE:  Options - Colors - Output', 'Contour11', ''),
            ('TABLE:  Options - Colors - Output', 'Contour12', ''),
            ('TABLE:  Options - Colors - Output', 'Contour13', ''),
            ('TABLE:  Options - Colors - Output', 'Contour14', ''),
            ('TABLE:  Options - Colors - Output', 'Contour15', ''),
            ('TABLE:  Options - Colors - Output', 'TransContours', ''),
            ('TABLE:  Options - Colors - Output', 'Ratio1', ''),
            ('TABLE:  Options - Colors - Output', 'Ratio2', ''),
            ('TABLE:  Options - Colors - Output', 'Ratio3', ''),
            ('TABLE:  Options - Colors - Output', 'Ratio4', ''),
            ('TABLE:  Options - Colors - Output', 'Ratio5', ''),
            ('TABLE:  Options - Colors - Output', 'RatioNotD', ''),
            ('TABLE:  Options - Colors - Output', 'RatioVal1', ''),
            ('TABLE:  Options - Colors - Output', 'RatioVal2', ''),
            ('TABLE:  Options - Colors - Output', 'RatioVal3', ''),
            ('TABLE:  Options - Colors - Output', 'RatioVal4', ''),
            ('TABLE:  Options - Colors - Output', 'HState1', ''),
            ('TABLE:  Options - Colors - Output', 'HState2', ''),
            ('TABLE:  Options - Colors - Output', 'HState3', ''),
            ('TABLE:  Options - Colors - Output', 'HState4', ''),
            ('TABLE:  Options - Colors - Output', 'HState5', ''),
            ('TABLE:  Options - Colors - Output', 'HStatus1', ''),
            ('TABLE:  Options - Colors - Output', 'HStatus2', ''),
            ('TABLE:  Options - Colors - Output', 'HStatus3', ''),
            ('TABLE:  Options - Colors - Output', 'HStatus4', ''),
            ('TABLE:  Options - Colors - Output', 'DFillPos', ''),
            ('TABLE:  Options - Colors - Output', 'DFillNeg', ''),
            ('TABLE:  Options - Colors - Output', 'DFillRange', ''),
            ('TABLE:  Options - Colors - Output', 'TransDiag', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('Screen Classic', 13107400, 6553828, 'Red', 16639, 'Orange', 43775, 54527, 'Yellow', 65408, 'Green',
                 8453888, 'Cyan', 16755200, 16733440, 'Blue', 0, 'Cyan', 'Green', 'Yellow', 'Magenta', 'Red', 'Gray4',
                 0.5, 0.7, 0.9, 1, 'Gray2', 'Green', 'Yellow', 'Cyan', 'Blue', 'Gray2', 'Green', 'Cyan', 'Red',
                 'Yellow', 'Red', 'Blue', 0.2),
                ('Screen GrayScale', 'Black', 3158064, 4210752, 5263440, 6316128, 7368816, 'Gray8Dark', 'Gray7',
                 'Gray6', 'Gray5', 'Gray4', 'Gray3', 'Gray2', 'Gray1Light', 'White', 0, 'Gray2', 'Gray4',
                 'Gray8Dark', 4210752, 'Black', 'Gray4', 0.5, 0.7, 0.9, 1, 'Gray3', 'Gray5', 'Gray7', 7368816, 5263440,
                 'Gray2', 'Gray4', 'Gray8Dark', 4210752, 'Gray8Dark', 'Gray8Dark', 4210752, 0.2),
                ('Printer', 'Black', 3158064, 4210752, 5263440, 6316128, 7368816, 'Gray8Dark', 'Gray7', 'Gray6',
                 'Gray5', 'Gray4', 'Gray3', 'Gray2', 'Gray1Light', 'White', 0, 'Gray2', 'Gray4',
                 'Gray8Dark', 4210752, 'Black', 'Gray4', 0.5, 0.7, 0.9, 1, 'Gray3', 'Gray5', 'Gray7', 7368816, 5263440,
                 'Gray2', 'Gray4', 'Gray8Dark', 4210752, 'Gray8Dark', 'Gray8Dark', 4210752, 0.2),
                ('Color Printer', 13107400, 6553828, 'Red', 16639, 'Orange', 43775, 54527, 'Yellow', 65408, 'Green',
                 8453888, 'Cyan', 16755200, 16733440, 'Blue', 0, 'Cyan', 'Green',
                 'Yellow', 'Magenta', 'Red', 'Gray4', 0.5, 0.7, 0.9, 1, 'Gray2', 'Green', 'Yellow', 'Cyan', 'Blue',
                 'Gray2', 'Green', 'Cyan', 'Red', 'Yellow', 'Red', 'Blue', 0.2)
                ]
        self.OptionsColorsOutput = pd.DataFrame(data, columns=col)
        # PierSectionProperties
        tuples = [
            ('TABLE:  Pier Section Properties', 'Story', ''),
            ('TABLE:  Pier Section Properties', 'Pier', ''),
            ('TABLE:  Pier Section Properties', 'AxisAngle', 'deg'),
            ('TABLE:  Pier Section Properties', '# Area Objects', ''),
            ('TABLE:  Pier Section Properties', '# Line Objects', ''),
            ('TABLE:  Pier Section Properties', 'Width Bottom', 'mm'),
            ('TABLE:  Pier Section Properties', 'Thickness Bottom', 'mm'),
            ('TABLE:  Pier Section Properties', 'Width Top', 'mm'),
            ('TABLE:  Pier Section Properties', 'Thickness Top', 'mm'),
            ('TABLE:  Pier Section Properties', 'Material', ''),
            ('TABLE:  Pier Section Properties', 'CG Bottom X', 'm'),
            ('TABLE:  Pier Section Properties', 'CG Bottom Y', 'm'),
            ('TABLE:  Pier Section Properties', 'CG Bottom Z', 'm'),
            ('TABLE:  Pier Section Properties', 'CG Top X', 'm'),
            ('TABLE:  Pier Section Properties', 'CG Top Y', 'm'),
            ('TABLE:  Pier Section Properties', 'CG Top Z', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.PierSectionProperties = pd.DataFrame(data, columns=col)
        # PointObjectConnectivity
        tuples = [
            ('TABLE:  Point Object Connectivity', 'UniqueName', ''),
            ('TABLE:  Point Object Connectivity', 'Is Auto Point', ''),
            ('TABLE:  Point Object Connectivity', 'IsSpecial', ''),
            ('TABLE:  Point Object Connectivity', 'X', 'm'),
            ('TABLE:  Point Object Connectivity', 'Y', 'm'),
            ('TABLE:  Point Object Connectivity', 'Z', 'm'),
            ('TABLE:  Point Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.PointObjectConnectivity = pd.DataFrame(data, columns=col)

        # PreferencesGraphics
        tuples = [
            ('TABLE:  Preferences - Graphics', 'MaxFontSize', ''),
            ('TABLE:  Preferences - Graphics', 'MinFontSize', ''),
            ('TABLE:  Preferences - Graphics', 'MinSLineThick', ''),
            ('TABLE:  Preferences - Graphics', 'MinPLineThick', ''),
            ('TABLE:  Preferences - Graphics', 'AntiAliasing?', ''),
            ('TABLE:  Preferences - Graphics', 'ConstantLineThick?', ''),
            ('TABLE:  Preferences - Graphics', 'ShadingWithinElement?', ''),
            ('TABLE:  Preferences - Graphics', 'AutoZoomStep', ''),
            ('TABLE:  Preferences - Graphics', 'ShrinkFactor', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [(8, 3, 1, 4, 'Yes', 'Yes', 'No', 10, 70)]
        self.PreferencesGraphics = pd.DataFrame(data, columns=col)

        # PreferencesTolerance
        tuples = [
            ('TABLE:  Preferences - Tolerance', 'MergeTol', 'mm'),
            ('TABLE:  Preferences - Tolerance', 'MaxInclVertCol', 'deg'),
            ('TABLE:  Preferences - Tolerance', 'MaxInclHorzBmFlr', 'deg')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [(2.5, 1, 20)]
        self.PreferencesTolerance = pd.DataFrame(data, columns=col)

        # ProgramControl
        tuples = [
            ('TABLE:  Program Control', 'ProgramName', ''),
            ('TABLE:  Program Control', 'Version', ''),
            ('TABLE:  Program Control', 'ProgLevel', ''),
            ('TABLE:  Program Control', 'LicenseNum', ''),
            ('TABLE:  Program Control', 'CurrUnits', ''),
            ('TABLE:  Program Control', 'CompBmCode', ''),
            ('TABLE:  Program Control', 'ConcFrmCode', ''),
            ('TABLE:  Program Control', 'ConcSlbCode', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('SAFE', '22.0.0', 'Standard', '', '',
                 'Eurocode 4-2004', 'Hong Kong CP 2013', 'Hong Kong CP 2013')]
        self.ProgramControl = pd.DataFrame(data, columns=col)

        # ProjectInformation
        tuples = [
            ('TABLE:  Project Information', 'Client Name', ''),
            ('TABLE:  Project Information', 'Project Name', ''),
            ('TABLE:  Project Information', 'Project Number', ''),
            ('TABLE:  Project Information', 'Company Name', ''),
            ('TABLE:  Project Information', 'Company Logo', ''),
            ('TABLE:  Project Information', 'Engineer', ''),
            ('TABLE:  Project Information', 'Checker', ''),
            ('TABLE:  Project Information', 'Supervisor', ''),
            ('TABLE:  Project Information', 'Model Name', ''),
            ('TABLE:  Project Information', 'Model Description', ''),
            ('TABLE:  Project Information', 'Revision Number', ''),
            ('TABLE:  Project Information', 'Issue Number', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [('', '', '', '', '', '', '', '', '', '', '0', '')]
        self.ProjectInformation = pd.DataFrame(data, columns=col)

        # ReinforcingBarSizes
        tuples = [
            ('TABLE:  Reinforcing Bar Sizes', 'Name', ''),
            ('TABLE:  Reinforcing Bar Sizes', 'Diameter', 'mm'),
            ('TABLE:  Reinforcing Bar Sizes', 'Area', 'cm²'),
            ('TABLE:  Reinforcing Bar Sizes', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = [(6, 6, 0.282999992, ''),
                (8, 8, 0.502999992, ''),
                (10, 10, 0.785, ''),
                (12, 12, 1.130999985, ''),
                (14, 14, 1.538999939, ''),
                (16, 16, 2.011000061, ''),
                (18, 18, 2.545, ''),
                (20, 20, 3.142000122, ''),
                (22, 22, 3.801000061, ''),
                (25, 25, 4.908999939, ''),
                (26, 26, 5.309000244, ''),
                (28, 28, 6.157999878, ''),
                (32, 32, 8.042000122, ''),
                (36, 36, 10.17900024, ''),
                (40, 40, 12.56599976, ''),
                (50, 50, 19.635, '')]
        self.ReinforcingBarSizes = pd.DataFrame(data, columns=col)

        # SectionCutDefinitions
        tuples = [
            ('TABLE:  Section Cut Definitions', 'Name', ''),
            ('TABLE:  Section Cut Definitions', 'Defined By', ''),
            ('TABLE:  Section Cut Definitions', 'Group', ''),
            ('TABLE:  Section Cut Definitions', 'Result Type', ''),
            ('TABLE:  Section Cut Definitions', 'Result Location', ''),
            ('TABLE:  Section Cut Definitions', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SectionCutDefinitions = pd.DataFrame(data, columns=col)
        # ShellUniformLoadSets
        tuples = [
            ('TABLE:  Shell Uniform Load Sets', 'Name', ''),
            ('TABLE:  Shell Uniform Load Sets', 'Load Pattern', ''),
            ('TABLE:  Shell Uniform Load Sets', 'Load Value', 'kN/m²'),
            ('TABLE:  Shell Uniform Load Sets', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.ShellUniformLoadSets = pd.DataFrame(data, columns=col)
        # SlabPropertyDefinitions
        tuples = [
            ('TABLE:  Slab Property Definitions', 'Name', ''),
            ('TABLE:  Slab Property Definitions', 'Modeling Type', ''),
            ('TABLE:  Slab Property Definitions', 'Property Type', ''),
            ('TABLE:  Slab Property Definitions', 'Material', ''),
            ('TABLE:  Slab Property Definitions', 'Slab Thickness', 'mm'),
            ('TABLE:  Slab Property Definitions', 'Notional Size Type', ''),
            ('TABLE:  Slab Property Definitions', 'Notional User Size', 'mm'),
            ('TABLE:  Slab Property Definitions', 'f11 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'f22 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'f12 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'm11 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'm22 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'm12 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'v13 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'v23 Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'Mass Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'Weight Modifier', ''),
            ('TABLE:  Slab Property Definitions', 'Color', ''),
            ('TABLE:  Slab Property Definitions', 'GUID', ''),
            ('TABLE:  Slab Property Definitions', 'Notes', ''),
            ('TABLE:  Slab Property Definitions', 'Orthotropic?', ''),
            ('TABLE:  Slab Property Definitions', 'Effective Thickness-11', 'mm'),
            ('TABLE:  Slab Property Definitions', 'Effective Thickness-22', 'mm'),
            ('TABLE:  Slab Property Definitions', 'Effective Thickness-12', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SlabPropertyDefinitions = pd.DataFrame(data, columns=col)

        # SlabRebarObjectGeometry
        tuples = [
            ('TABLE:  Slab Rebar Object Geometry', 'Name', ''),
            ('TABLE:  Slab Rebar Object Geometry', 'X1', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Y1', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'X2', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Y2', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Vertical Offset', 'mm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Width Left', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Width Right', 'm'),
            ('TABLE:  Slab Rebar Object Geometry', 'Layer', ''),
            ('TABLE:  Slab Rebar Object Geometry', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SlabRebarObjectGeometry = pd.DataFrame(data, columns=col)
        # SlabRebarPropertyAssignments
        tuples = [
            ('TABLE:  Slab Rebar Property Assignments', 'Name', ''),
            ('TABLE:  Slab Rebar Property Assignments', 'Rebar Size', ''),
            ('TABLE:  Slab Rebar Property Assignments', 'Material', ''),
            ('TABLE:  Slab Rebar Property Assignments', 'Bar Number Type', ''),
            ('TABLE:  Slab Rebar Property Assignments', 'Total Bars', ''),
            ('TABLE:  Slab Rebar Property Assignments', 'Max Spacing', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SlabRebarPropertyAssignments = pd.DataFrame(data, columns=col)
        # SpandrelSectionProperties
        tuples = [
            ('TABLE:  Spandrel Section Properties', 'Story', ''),
            ('TABLE:  Spandrel Section Properties', 'Spandrel', ''),
            ('TABLE:  Spandrel Section Properties', '# Area Objects', ''),
            ('TABLE:  Spandrel Section Properties', '# Line Objects', ''),
            ('TABLE:  Spandrel Section Properties', 'Length', 'mm'),
            ('TABLE:  Spandrel Section Properties', 'Depth Left', 'mm'),
            ('TABLE:  Spandrel Section Properties', 'Thickness Left', 'mm'),
            ('TABLE:  Spandrel Section Properties', 'Depth Right', 'mm'),
            ('TABLE:  Spandrel Section Properties', 'Thickness Right', 'mm'),
            ('TABLE:  Spandrel Section Properties', 'Material', ''),
            ('TABLE:  Spandrel Section Properties', 'CG Left X', 'm'),
            ('TABLE:  Spandrel Section Properties', 'CG Left Y', 'm'),
            ('TABLE:  Spandrel Section Properties', 'CG Left Z', 'm'),
            ('TABLE:  Spandrel Section Properties', 'CG Right X', 'm'),
            ('TABLE:  Spandrel Section Properties', 'CG Right Y', 'm'),
            ('TABLE:  Spandrel Section Properties', 'CG Right Z', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SpandrelSectionProperties = pd.DataFrame(data, columns=col)
        # SpringPropsAreaSprings
        tuples = [
            ('TABLE:  Spring Property Definitions - Area Springs', 'Name', ''),
            ('TABLE:  Spring Property Definitions - Area Springs',
             'Stiffness Option', ''),
            ('TABLE:  Spring Property Definitions - Area Springs',
             'Nonlinear Option for U3', ''),
            ('TABLE:  Spring Property Definitions - Area Springs', 'Color', ''),
            ('TABLE:  Spring Property Definitions - Area Springs', 'GUID', ''),
            ('TABLE:  Spring Property Definitions - Area Springs', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SpringPropsAreaSprings = pd.DataFrame(data, columns=col)
        # SpringPropsLineSprings
        tuples = [
            ('TABLE:  Spring Property Definitions - Line Springs', 'Name', ''),
            ('TABLE:  Spring Property Definitions - Line Springs',
             'Stiffness Option', ''),
            ('TABLE:  Spring Property Definitions - Line Springs', 'Color', ''),
            ('TABLE:  Spring Property Definitions - Line Springs', 'GUID', ''),
            ('TABLE:  Spring Property Definitions - Line Springs', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SpringPropsLineSprings = pd.DataFrame(data, columns=col)
        # SpringPropsPointSprings
        tuples = [
            ('TABLE:  Spring Property Definitions - Point Springs', 'Name', ''),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness UX', 'kN/m'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness UY', 'kN/m'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness UZ', 'kN/m'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness RX', 'kN-m/rad'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness RY', 'kN-m/rad'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Stiffness RZ', 'kN-m/rad'),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Nonlinearity Specification', ''),
            ('TABLE:  Spring Property Definitions - Point Springs',
             'Nonlinear Option', ''),
            ('TABLE:  Spring Property Definitions - Point Springs', 'Color', ''),
            ('TABLE:  Spring Property Definitions - Point Springs', 'GUID', ''),
            ('TABLE:  Spring Property Definitions - Point Springs', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.SpringPropsPointSprings = pd.DataFrame(data, columns=col)

        # StlFrmOverChinese2018
        tuples = [
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Story', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Label', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Unique Name', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Design Type', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Design Section', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Frame Type', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Element Type', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Seismic Design Grade', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Energy Dissipation Member?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Is Transfer Member?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Seismic Magnification Factor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Dual System SMF', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Ignore B/T Check?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Beam Flexo-Compression Member?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Rolled Section?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Gas Cut?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Both Ends Pinned?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Ignore Beam PhiB?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Beam Top Loaded?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Check Deflection?', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'LL Ratio', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Total Ratio', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Camber Ratio', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Specified Camber', 'mm'),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Net Area Ratio', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'LLRF', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Unbraced Length Ratio Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Unbraced Length Ratio Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Effective Length Factor Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Effective Length Factor Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Sway M Amplification Factor Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Sway M Amplification Factor Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Effective Stability Coefficient Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Effective Stability Coefficient Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Flexural Stability Coefficient Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Flexural Stability Coefficient Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Moment Coefficient Beta_m, Major,', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Moment Coefficient Beta_m, Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Moment Coefficient Beta_t, Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Moment Coefficient Beta_t, Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Plasticity Factor Gamma Major', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Plasticity Factor Gamma Minor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Section Influence Coefficient', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'B/C Capacity Factor', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Compression lo/r', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Tension l/r', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Yield stress, Fy', 'MPa'),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Allowable Normal Stress, f', ''),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018',
             'Allowable Shear Stress, fv', 'kN'),
            ('TABLE:  Steel Frame Design Overwrites - Chinese 2018', 'Consider Fictitious Shear?', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.StlFrmOverChinese2018 = pd.DataFrame(data, columns=col)
        # StripObjectConnectivity
        tuples = [
            ('TABLE:  Strip Object Connectivity', 'Name', ''),
            ('TABLE:  Strip Object Connectivity', 'Number Segments', ''),
            ('TABLE:  Strip Object Connectivity', 'Strip Start Point', ''),
            ('TABLE:  Strip Object Connectivity', 'Segment End Point', ''),
            ('TABLE:  Strip Object Connectivity', 'Start Width Left', 'm'),
            ('TABLE:  Strip Object Connectivity', 'Start Width Right', 'm'),
            ('TABLE:  Strip Object Connectivity', 'End Width Left', 'm'),
            ('TABLE:  Strip Object Connectivity', 'End Width Right', 'm'),
            ('TABLE:  Strip Object Connectivity', 'Auto Widen', ''),
            ('TABLE:  Strip Object Connectivity', 'Layer', ''),
            ('TABLE:  Strip Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.StripObjectConnectivity = pd.DataFrame(data, columns=col)

        # TendonTotalElongation
        tuples = [
            ('TABLE:  Tendon - Total Elongation', 'Name', ''),
            ('TABLE:  Tendon - Total Elongation', 'Transfer Load Pattern', ''),
            ('TABLE:  Tendon - Total Elongation', 'Jacking Location', ''),
            ('TABLE:  Tendon - Total Elongation', 'Force - End I', 'kN'),
            ('TABLE:  Tendon - Total Elongation', 'Force - End J', 'kN'),
            ('TABLE:  Tendon - Total Elongation', 'Elongation - End I', 'mm'),
            ('TABLE:  Tendon - Total Elongation', 'Elongation - End J', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonTotalElongation = pd.DataFrame(data, columns=col)
        # TendonAssignsProperties
        tuples = [
            ('TABLE:  Tendon Assignments - Properties', 'Name', ''),
            ('TABLE:  Tendon Assignments - Properties', 'Tendon Property', ''),
            ('TABLE:  Tendon Assignments - Properties', 'Number Strands', ''),
            ('TABLE:  Tendon Assignments - Properties', 'Bonding Options', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonAssignsProperties = pd.DataFrame(data, columns=col)
        # TendonDiscretizedPoints
        tuples = [
            ('TABLE:  Tendon Discretized Points', 'Name', ''),
            ('TABLE:  Tendon Discretized Points', 'Point Number', ''),
            ('TABLE:  Tendon Discretized Points', 'Global X', 'm'),
            ('TABLE:  Tendon Discretized Points', 'Global Y', 'm'),
            ('TABLE:  Tendon Discretized Points', 'Global Z', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonDiscretizedPoints = pd.DataFrame(data, columns=col)
        # TendonLoadsJackingStress
        tuples = [
            ('TABLE:  Tendon Load Assignments - Jacking Stress', 'Name', ''),
            ('TABLE:  Tendon Load Assignments - Jacking Stress',
             'Transfer Load Pattern', ''),
            ('TABLE:  Tendon Load Assignments - Jacking Stress',
             'Final Load Pattern', ''),
            ('TABLE:  Tendon Load Assignments - Jacking Stress',
             'Jacking Stress', 'MPa'),
            ('TABLE:  Tendon Load Assignments - Jacking Stress', 'Jacking Location', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonLoadsJackingStress = pd.DataFrame(data, columns=col)
        # TendonLoadsLossOptions
        tuples = [
            ('TABLE:  Tendon Load Assignments - Loss Options', 'Name', ''),
            ('TABLE:  Tendon Load Assignments - Loss Options', 'Loss Method', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonLoadsLossOptions = pd.DataFrame(data, columns=col)
        # TendonObjectConnectivity
        tuples = [
            ('TABLE:  Tendon Object Connectivity', 'Name', ''),
            ('TABLE:  Tendon Object Connectivity', 'Number Points', ''),
            ('TABLE:  Tendon Object Connectivity', 'Point Name', ''),
            ('TABLE:  Tendon Object Connectivity', 'Horizontal Segment Number', ''),
            ('TABLE:  Tendon Object Connectivity', 'Horizontal Curve Type', ''),
            ('TABLE:  Tendon Object Connectivity', 'Number Control Points', ''),
            ('TABLE:  Tendon Object Connectivity', 'Curve Point Number', ''),
            ('TABLE:  Tendon Object Connectivity', 'Global X', 'm'),
            ('TABLE:  Tendon Object Connectivity', 'Global Y', 'm'),
            ('TABLE:  Tendon Object Connectivity', 'Number Vertical Spans', ''),
            ('TABLE:  Tendon Object Connectivity', 'Datum Offset', 'mm'),
            ('TABLE:  Tendon Object Connectivity', 'First Support Type', ''),
            ('TABLE:  Tendon Object Connectivity', 'Span Number', ''),
            ('TABLE:  Tendon Object Connectivity', 'Span ID', ''),
            ('TABLE:  Tendon Object Connectivity', 'End Support Type', ''),
            ('TABLE:  Tendon Object Connectivity', 'Span Profile', ''),
            ('TABLE:  Tendon Object Connectivity', 'RD', ''),
            ('TABLE:  Tendon Object Connectivity', 'L', 'm'),
            ('TABLE:  Tendon Object Connectivity', 'ZL', 'mm'),
            ('TABLE:  Tendon Object Connectivity', 'ZR', 'mm'),
            ('TABLE:  Tendon Object Connectivity', 'Layer', ''),
            ('TABLE:  Tendon Object Connectivity', 'GUID', ''),
            ('TABLE:  Tendon Object Connectivity', 'MinCurvatureRadius', 'mm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonObjectConnectivity = pd.DataFrame(data, columns=col)

        # TendonSectionProperties
        tuples = [
            ('TABLE:  Tendon Section Properties', 'Name', ''),
            ('TABLE:  Tendon Section Properties', 'Material', ''),
            ('TABLE:  Tendon Section Properties', 'StrandArea', 'cm²'),
            ('TABLE:  Tendon Section Properties', 'Color', ''),
            ('TABLE:  Tendon Section Properties', 'GUID', ''),
            ('TABLE:  Tendon Section Properties', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.TendonSectionProperties = pd.DataFrame(data, columns=col)

        # WallObjectConnectivity
        tuples = [
            ('TABLE:  Wall Object Connectivity', 'UniqueName', ''),
            ('TABLE:  Wall Object Connectivity', 'UniquePt1', ''),
            ('TABLE:  Wall Object Connectivity', 'UniquePt2', ''),
            ('TABLE:  Wall Object Connectivity', 'UniquePt3', ''),
            ('TABLE:  Wall Object Connectivity', 'Perimeter', 'm'),
            ('TABLE:  Wall Object Connectivity', 'Area', 'm²'),
            ('TABLE:  Wall Object Connectivity', 'GUID', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.WallObjectConnectivity = pd.DataFrame(data, columns=col)
        # WallObjectCurveData
        tuples = [
            ('TABLE:  Wall Object Curve Data', 'Unique Name', ''),
            ('TABLE:  Wall Object Curve Data', 'Curve Type', ''),
            ('TABLE:  Wall Object Curve Data', 'ICP Number', ''),
            ('TABLE:  Wall Object Curve Data', 'Global X', 'm'),
            ('TABLE:  Wall Object Curve Data', 'Global Y', 'm')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.WallObjectCurveData = pd.DataFrame(data, columns=col)
        # WallPropertyDefAutoSelect
        tuples = [
            ('TABLE:  Wall Property Definitions - Auto Select List', 'Name', ''),
            ('TABLE:  Wall Property Definitions - Auto Select List',
             'Included Property', ''),
            ('TABLE:  Wall Property Definitions - Auto Select List',
             'Starting Property', ''),
            ('TABLE:  Wall Property Definitions - Auto Select List', 'Color', ''),
            ('TABLE:  Wall Property Definitions - Auto Select List', 'GUID', ''),
            ('TABLE:  Wall Property Definitions - Auto Select List', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.WallPropertyDefAutoSelect = pd.DataFrame(data, columns=col)
        # WallPropertyDefSpecified
        tuples = [
            ('TABLE:  Wall Property Definitions - Specified', 'Name', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'Modeling Type', ''),
            ('TABLE:  Wall Property Definitions - Specified',
             'Include Auto Rigid Zone?', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'f11 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'f22 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'f12 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'm11 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'm22 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'm12 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'v13 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'v23 Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'Mass Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'Weight Modifier', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'Color', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'GUID', ''),
            ('TABLE:  Wall Property Definitions - Specified', 'Notes', '')]
        col = pd.MultiIndex.from_tuples(tuples)
        data = []
        self.WallPropertyDefSpecified = pd.DataFrame(data, columns=col)
