# Foundation Automation - Ultimate Limit State Design for H-Piles

The uls_design_hp package provides Ultimate Limit State (ULS) design calculations for H-piles, focusing on structural safety and capacity verification for steel H-section piles in foundation engineering.

## Package Structure

The package is organized into several key modules:

### Core Modules

1. `__init__.py`
   - Package initialization
   - Exports main ULS design functions
   - Defines package interface

2. `steel_design.py`
   - Handles steel section design
   - Implements:
     - `design_strength`: Ultimate strength design
     - Steel section property calculations
     - Material property verification

3. `uls_hp_nmv.py`
   - Main ULS design for H-piles
   - Implements:
     - `check_steel_h`: Comprehensive H-pile verification
     - N-M-V interaction checks
     - Shear capacity verification
     - Buckling analysis

## Key Functions

### Steel Design
- `design_strength`: Ultimate strength design for steel sections
  - Input: Section properties, loading conditions, material properties
  - Output: Strength verification results and safety factors

### H-Pile Verification
- `check_steel_h`: Comprehensive H-pile verification
  - Input: Pile properties, loading conditions, material properties
  - Output: Verification results for:
    - Axial capacity
    - Bending capacity
    - Shear capacity
    - Buckling resistance

## Design Criteria

The package implements ULS design criteria for H-piles:
1. Steel Section Properties
   - Section classification
   - Plastic section modulus
   - Elastic section modulus

2. Strength Verification
   - Axial capacity
   - Flexural capacity
   - Shear capacity
   - Combined loading effects

3. Stability
   - Buckling resistance
   - Local buckling
   - Overall stability

## Usage Examples

### Steel Section Design
```python
from uls_design_hp import design_strength

# Design steel section strength
strength_result = design_strength(
    section_properties=section_properties,
    loading_conditions=loading_conditions,
    material_properties=material_properties
)
```

### H-Pile Verification
```python
from uls_design_hp import check_steel_h

# Verify H-pile capacity
pile_check = check_steel_h(
    pile_properties=pile_properties,
    loading_conditions=loading_conditions,
    material_properties=material_properties
)
```

## Best Practices

1. Verify input data accuracy
2. Consider site-specific conditions
3. Follow local building codes and steel design standards
4. Document assumptions and calculations
5. Perform sensitivity analysis
6. Validate results against manual calculations
7. Consider fabrication and erection limitations

## Error Handling

The package includes error handling for:
- Invalid input data
- Calculation failures
- Limit criteria violations
- Geometric constraints
- Material property validation

## Integration Points

This package integrates with:
- Foundation Automation design system
- Structural analysis modules
- Material property databases
- Load combination modules
- Visualization tools

## Design Standards

The package implements ULS design standards for:
- Structural safety
- Capacity verification
- Material utilization
- Foundation behavior
- Steel design codes

## Dependencies

The package relies on external dependencies:
- numpy: Numerical calculations
- pandas: Data handling
- matplotlib: Visualization

## Version

Current version aligns with Foundation Automation system version V5.3
