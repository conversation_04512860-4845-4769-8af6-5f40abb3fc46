﻿"""
Mathematical utility functions for pile estimation
"""

import numpy as np
from math import ceil, floor, sqrt, isclose
from typing import List, Tuple, Optional, Callable
from .logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)


def safe_divide(numerator: float, denominator: float, log_callback: Optional[Callable] = None) -> float:
    """
    Perform safe division with proper error handling.
    
    Args:
        numerator: The numerator
        denominator: The denominator
        log_callback: Optional callback for logging
        
    Returns:
        Result of division
        
    Raises:
        ZeroDivisionError: If denominator is zero
    """
    log_function_entry(log_callback, "safe_divide", 
                      numerator=numerator, denominator=denominator)
    
    try:
        enhanced_log(log_callback, f"Performing division: {numerator} ÷ {denominator}", 'DEBUG')
        
        # Check for zero denominator
        if denominator == 0:
            enhanced_log(log_callback, "Division by zero detected", 'ERROR')
            log_constraint_check(log_callback, "Non-zero denominator", denominator, "!= 0", False)
            raise ZeroDivisionError("Cannot divide by zero")
        
        log_constraint_check(log_callback, "Non-zero denominator", denominator, "!= 0", True)
        
        with create_timed_logger(log_callback, "division calculation") as timer:
            result = numerator / denominator
        
        log_calculation_result(log_callback, "Division result", f"{result:.6f}")
        
        # Check for potential precision issues
        if abs(result) < 1e-12 and numerator != 0:
            enhanced_log(log_callback, "Very small result detected - potential precision issue", 'WARNING')
        elif abs(result) > 1e12:
            enhanced_log(log_callback, "Very large result detected", 'WARNING')
        
        log_function_exit(log_callback, "safe_divide", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "safe_divide")
        raise


def clamp(value: float, min_val: float, max_val: float, log_callback: Optional[Callable] = None) -> float:
    """
    Clamp a value between minimum and maximum bounds.
    
    Args:
        value: Value to clamp
        min_val: Minimum allowed value
        max_val: Maximum allowed value
        log_callback: Optional callback for logging
        
    Returns:
        Clamped value
    """
    log_function_entry(log_callback, "clamp", 
                      value=value, min_val=min_val, max_val=max_val)
    
    try:
        enhanced_log(log_callback, f"Clamping value {value} to range [{min_val}, {max_val}]", 'DEBUG')
        
        # Validate input bounds
        if min_val > max_val:
            enhanced_log(log_callback, f"Invalid bounds: min_val ({min_val}) > max_val ({max_val})", 'WARNING')
            # Swap values to make it work
            min_val, max_val = max_val, min_val
            enhanced_log(log_callback, f"Swapped bounds to [{min_val}, {max_val}]", 'WARNING')
        
        log_constraint_check(log_callback, "Valid bounds", min_val, f"<= {max_val}", min_val <= max_val)
        
        with create_timed_logger(log_callback, "clamping operation") as timer:
            result = max(min_val, min(value, max_val))
        
        # Log clamping behavior
        if result != value:
            if result == min_val:
                enhanced_log(log_callback, f"Value clamped to minimum: {value} → {result}", 'INFO')
            elif result == max_val:
                enhanced_log(log_callback, f"Value clamped to maximum: {value} → {result}", 'INFO')
        else:
            enhanced_log(log_callback, "Value within bounds, no clamping needed", 'DEBUG')
        
        log_calculation_result(log_callback, "Clamped value", f"{result:.6f}")
        log_function_exit(log_callback, "clamp", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "clamp")
        # Return original value as fallback
        enhanced_log(log_callback, f"Returning original value {value} due to error", 'WARNING')
        log_function_exit(log_callback, "clamp", value)
        return value


def round_up_to_nearest(value: float, increment: float, log_callback: Optional[Callable] = None) -> float:
    """
    Round a value up to the nearest increment.
    
    Args:
        value: Value to round
        increment: Increment to round to
        log_callback: Optional callback for logging
        
    Returns:
        Rounded value
        
    Raises:
        ValueError: If increment is zero or negative
    """
    log_function_entry(log_callback, "round_up_to_nearest", 
                      value=value, increment=increment)
    
    try:
        enhanced_log(log_callback, f"Rounding {value} up to nearest {increment}", 'DEBUG')
        
        # Validate increment
        if increment <= 0:
            enhanced_log(log_callback, f"Invalid increment: {increment} <= 0", 'ERROR')
            log_constraint_check(log_callback, "Positive increment", increment, "> 0", False)
            raise ValueError("Increment must be positive")
        
        log_constraint_check(log_callback, "Positive increment", increment, "> 0", True)
        
        with create_timed_logger(log_callback, "round up calculation") as timer:
            result = ceil(value / increment) * increment
        
        # Calculate the adjustment made
        adjustment = result - value
        enhanced_log(log_callback, f"Rounded up by {adjustment:.6f}", 'DEBUG')
        
        log_calculation_result(log_callback, "Rounded up value", f"{result:.6f}")
        
        # Log efficiency metrics
        if adjustment == 0:
            enhanced_log(log_callback, "Value already at increment boundary", 'DEBUG')
        else:
            efficiency = (value / result) * 100
            log_performance_metric(log_callback, "Rounding efficiency", efficiency, "%")
        
        log_function_exit(log_callback, "round_up_to_nearest", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "round_up_to_nearest")
        raise


def round_to_nearest(value: float, increment: float, log_callback: Optional[Callable] = None) -> float:
    """
    Round a value to the nearest increment.
    
    Args:
        value: Value to round
        increment: Increment to round to
        log_callback: Optional callback for logging
        
    Returns:
        Rounded value
        
    Raises:
        ValueError: If increment is zero or negative
    """
    log_function_entry(log_callback, "round_to_nearest", 
                      value=value, increment=increment)
    
    try:
        enhanced_log(log_callback, f"Rounding {value} to nearest {increment}", 'DEBUG')
        
        # Validate increment
        if increment <= 0:
            enhanced_log(log_callback, f"Invalid increment: {increment} <= 0", 'ERROR')
            log_constraint_check(log_callback, "Positive increment", increment, "> 0", False)
            raise ValueError("Increment must be positive")
        
        log_constraint_check(log_callback, "Positive increment", increment, "> 0", True)
        
        with create_timed_logger(log_callback, "round to nearest calculation") as timer:
            result = round(value / increment) * increment
        
        # Calculate the adjustment made
        adjustment = abs(result - value)
        enhanced_log(log_callback, f"Adjustment made: {adjustment:.6f}", 'DEBUG')
        
        log_calculation_result(log_callback, "Rounded value", f"{result:.6f}")
        
        # Log direction of rounding
        if result > value:
            enhanced_log(log_callback, "Rounded up", 'DEBUG')
        elif result < value:
            enhanced_log(log_callback, "Rounded down", 'DEBUG')
        else:
            enhanced_log(log_callback, "Value already at increment boundary", 'DEBUG')
        
        # Log precision efficiency
        if adjustment > 0:
            precision_loss = (adjustment / abs(value)) * 100 if value != 0 else 0
            log_performance_metric(log_callback, "Precision loss", precision_loss, "%")
        
        log_function_exit(log_callback, "round_to_nearest", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "round_to_nearest")
        raise


def calculate_weighted_average(values: List[float], weights: List[float], log_callback: Optional[Callable] = None) -> float:
    """
    Calculate weighted average of values.
    
    Args:
        values: List of values
        weights: List of corresponding weights
        log_callback: Optional callback for logging
        
    Returns:
        Weighted average
        
    Raises:
        ValueError: If lists have different lengths or all weights are zero
    """
    log_function_entry(log_callback, "calculate_weighted_average", 
                      num_values=len(values), num_weights=len(weights))
    
    try:
        enhanced_log(log_callback, f"Calculating weighted average for {len(values)} values", 'DEBUG')
        
        # Validate input lengths
        if len(values) != len(weights):
            enhanced_log(log_callback, f"Length mismatch: values({len(values)}) != weights({len(weights)})", 'ERROR')
            log_constraint_check(log_callback, "Equal list lengths", len(values), f"== {len(weights)}", False)
            raise ValueError("Values and weights must have the same length")
        
        log_constraint_check(log_callback, "Equal list lengths", len(values), f"== {len(weights)}", True)
        
        # Validate non-empty lists
        if not values:
            enhanced_log(log_callback, "Empty input lists provided", 'ERROR')
            log_constraint_check(log_callback, "Non-empty lists", len(values), "> 0", False)
            raise ValueError("Cannot calculate weighted average of empty list")
        
        log_constraint_check(log_callback, "Non-empty lists", len(values), "> 0", True)
        
        enhanced_log(log_callback, f"Value range: [{min(values):.3f}, {max(values):.3f}]", 'DEBUG')
        enhanced_log(log_callback, f"Weight range: [{min(weights):.3f}, {max(weights):.3f}]", 'DEBUG')
        
        with create_timed_logger(log_callback, "weighted average calculation") as timer:
            total_weight = sum(weights)
            
            # Validate total weight
            if total_weight == 0:
                enhanced_log(log_callback, "Total weight is zero", 'ERROR')
                log_constraint_check(log_callback, "Non-zero total weight", total_weight, "!= 0", False)
                raise ValueError("Total weight cannot be zero")
            
            log_constraint_check(log_callback, "Non-zero total weight", total_weight, "!= 0", True)
            log_calculation_result(log_callback, "Total weight", f"{total_weight:.6f}")
            
            # Check for negative weights
            negative_weights = [w for w in weights if w < 0]
            if negative_weights:
                enhanced_log(log_callback, f"Found {len(negative_weights)} negative weights", 'WARNING')
            
            weighted_sum = sum(v * w for v, w in zip(values, weights))
            log_calculation_result(log_callback, "Weighted sum", f"{weighted_sum:.6f}")
            
            result = weighted_sum / total_weight
        
        log_calculation_result(log_callback, "Weighted average", f"{result:.6f}")
        
        # Log weight distribution analysis
        weight_std = np.std(weights)
        log_performance_metric(log_callback, "Weight standard deviation", weight_std, "")
        
        if weight_std < 1e-6:
            enhanced_log(log_callback, "Uniform weighting detected (equivalent to simple average)", 'INFO')
        
        # Compare with simple average for reference
        simple_avg = sum(values) / len(values)
        avg_difference = abs(result - simple_avg)
        enhanced_log(log_callback, f"Difference from simple average: {avg_difference:.6f}", 'DEBUG')
        
        log_function_exit(log_callback, "calculate_weighted_average", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_weighted_average")
        raise


def interpolate_linear(x: float, x1: float, y1: float, x2: float, y2: float, log_callback: Optional[Callable] = None) -> float:
    """
    Perform linear interpolation between two points.
    
    Args:
        x: Input value to interpolate
        x1, y1: First point
        x2, y2: Second point
        log_callback: Optional callback for logging
        
    Returns:
        Interpolated value
        
    Raises:
        ValueError: If x1 equals x2
    """
    log_function_entry(log_callback, "interpolate_linear", 
                      x=x, x1=x1, y1=y1, x2=x2, y2=y2)
    
    try:
        enhanced_log(log_callback, f"Interpolating at x={x} between points ({x1},{y1}) and ({x2},{y2})", 'DEBUG')
        
        # Validate input points
        if x1 == x2:
            enhanced_log(log_callback, f"Invalid interpolation: x1({x1}) == x2({x2})", 'ERROR')
            log_constraint_check(log_callback, "Distinct x coordinates", x1, f"!= {x2}", False)
            raise ValueError("Cannot interpolate when x1 equals x2")
        
        log_constraint_check(log_callback, "Distinct x coordinates", x1, f"!= {x2}", True)
        
        # Log interpolation bounds checking
        x_range = [min(x1, x2), max(x1, x2)]
        if x < x_range[0] or x > x_range[1]:
            enhanced_log(log_callback, f"Extrapolation: x={x} outside range [{x_range[0]}, {x_range[1]}]", 'WARNING')
        else:
            enhanced_log(log_callback, f"Interpolation: x={x} within range [{x_range[0]}, {x_range[1]}]", 'DEBUG')
        
        with create_timed_logger(log_callback, "linear interpolation") as timer:
            t = (x - x1) / (x2 - x1)
            result = y1 + t * (y2 - y1)
        
        log_calculation_result(log_callback, "Interpolation parameter t", f"{t:.6f}")
        log_calculation_result(log_callback, "Interpolated value", f"{result:.6f}")
        
        # Log interpolation characteristics
        y_range = abs(y2 - y1)
        enhanced_log(log_callback, f"Y-range span: {y_range:.6f}", 'DEBUG')
        
        if t < 0:
            enhanced_log(log_callback, "Extrapolating backwards", 'INFO')
        elif t > 1:
            enhanced_log(log_callback, "Extrapolating forwards", 'INFO')
        else:
            enhanced_log(log_callback, f"Interpolating at {t*100:.1f}% between points", 'DEBUG')
        
        # Check for potential precision issues
        if abs(result) > 1e12:
            enhanced_log(log_callback, "Very large interpolated value detected", 'WARNING')
        
        log_function_exit(log_callback, "interpolate_linear", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "interpolate_linear")
        raise


def is_approximately_equal(a: float, b: float, tolerance: float = 1e-9, log_callback: Optional[Callable] = None) -> bool:
    """
    Check if two floating point numbers are approximately equal.
    
    Args:
        a: First number
        b: Second number
        tolerance: Tolerance for comparison
        log_callback: Optional callback for logging
        
    Returns:
        True if numbers are approximately equal
    """
    log_function_entry(log_callback, "is_approximately_equal", 
                      a=a, b=b, tolerance=tolerance)
    
    try:
        enhanced_log(log_callback, f"Comparing {a} ≈ {b} with tolerance {tolerance}", 'DEBUG')
        
        # Validate tolerance
        if tolerance < 0:
            enhanced_log(log_callback, f"Negative tolerance {tolerance}, using absolute value", 'WARNING')
            tolerance = abs(tolerance)
        
        log_constraint_check(log_callback, "Non-negative tolerance", tolerance, ">= 0", tolerance >= 0)
        
        with create_timed_logger(log_callback, "equality comparison") as timer:
            difference = abs(a - b)
            result = difference <= tolerance
        
        log_calculation_result(log_callback, "Absolute difference", f"{difference:.2e}")
        log_constraint_check(log_callback, "Within tolerance", difference, f"<= {tolerance:.2e}", result)
        
        if result:
            enhanced_log(log_callback, "Numbers are approximately equal", 'DEBUG')
        else:
            enhanced_log(log_callback, f"Numbers differ by {difference:.2e} (exceeds tolerance)", 'DEBUG')
        
        # Log relative difference for context
        if a != 0 or b != 0:
            avg_magnitude = (abs(a) + abs(b)) / 2
            if avg_magnitude > 0:
                relative_diff = (difference / avg_magnitude) * 100
                log_performance_metric(log_callback, "Relative difference", relative_diff, "%")
        
        log_function_exit(log_callback, "is_approximately_equal", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "is_approximately_equal")
        # Conservative fallback - exact equality
        fallback_result = (a == b)
        enhanced_log(log_callback, f"Falling back to exact equality: {fallback_result}", 'WARNING')
        log_function_exit(log_callback, "is_approximately_equal", fallback_result)
        return fallback_result


def calculate_statistics(values: List[float], log_callback: Optional[Callable] = None) -> dict:
    """
    Calculate basic statistics for a list of values.
    
    Args:
        values: List of numerical values
        log_callback: Optional callback for logging
        
    Returns:
        Dictionary with min, max, mean, median, std statistics
        
    Raises:
        ValueError: If values list is empty
    """
    log_function_entry(log_callback, "calculate_statistics", 
                      num_values=len(values))
    
    try:
        enhanced_log(log_callback, f"Calculating statistics for {len(values)} values", 'DEBUG')
        
        # Validate input
        if not values:
            enhanced_log(log_callback, "Empty values list provided", 'ERROR')
            log_constraint_check(log_callback, "Non-empty list", len(values), "> 0", False)
            raise ValueError("Cannot calculate statistics for empty list")
        
        log_constraint_check(log_callback, "Non-empty list", len(values), "> 0", True)
        
        with create_timed_logger(log_callback, "statistics calculation") as timer:
            values_array = np.array(values)
            
            # Check for invalid values
            if np.any(np.isnan(values_array)):
                nan_count = np.sum(np.isnan(values_array))
                enhanced_log(log_callback, f"Found {nan_count} NaN values", 'WARNING')
            
            if np.any(np.isinf(values_array)):
                inf_count = np.sum(np.isinf(values_array))
                enhanced_log(log_callback, f"Found {inf_count} infinite values", 'WARNING')
            
            stats = {
                'count': len(values),
                'min': float(np.min(values_array)),
                'max': float(np.max(values_array)),
                'mean': float(np.mean(values_array)),
                'median': float(np.median(values_array)),
                'std': float(np.std(values_array))
            }
        
        # Log detailed statistics
        enhanced_log(log_callback, "=== STATISTICS SUMMARY ===", 'INFO')
        log_calculation_result(log_callback, "Count", stats['count'])
        log_calculation_result(log_callback, "Minimum", f"{stats['min']:.6f}")
        log_calculation_result(log_callback, "Maximum", f"{stats['max']:.6f}")
        log_calculation_result(log_callback, "Mean", f"{stats['mean']:.6f}")
        log_calculation_result(log_callback, "Median", f"{stats['median']:.6f}")
        log_calculation_result(log_callback, "Standard deviation", f"{stats['std']:.6f}")
        
        # Calculate additional insights
        value_range = stats['max'] - stats['min']
        log_calculation_result(log_callback, "Range", f"{value_range:.6f}")
        
        # Coefficient of variation
        if stats['mean'] != 0:
            cv = (stats['std'] / abs(stats['mean'])) * 100
            log_performance_metric(log_callback, "Coefficient of variation", cv, "%")
            
            if cv < 10:
                enhanced_log(log_callback, "Low variability in data (CV < 10%)", 'INFO')
            elif cv > 50:
                enhanced_log(log_callback, "High variability in data (CV > 50%)", 'INFO')
        
        # Skewness indicator (mean vs median)
        if stats['std'] > 0:
            skew_indicator = (stats['mean'] - stats['median']) / stats['std']
            if abs(skew_indicator) > 0.5:
                skew_direction = "right" if skew_indicator > 0 else "left"
                enhanced_log(log_callback, f"Data appears skewed {skew_direction}", 'INFO')
        
        enhanced_log(log_callback, "=== END STATISTICS ===", 'INFO')
        
        log_validation_result(log_callback, "Statistics calculation", True, 
                            f"Successfully computed statistics for {len(values)} values")
        
        log_function_exit(log_callback, "calculate_statistics", stats)
        return stats
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_statistics")
        raise


def find_nearest_value(target: float, candidates: List[float], log_callback: Optional[Callable] = None) -> float:
    """
    Find the value in candidates list that is closest to target.
    
    Args:
        target: Target value
        candidates: List of candidate values
        log_callback: Optional callback for logging
        
    Returns:
        Nearest value
        
    Raises:
        ValueError: If candidates list is empty
    """
    log_function_entry(log_callback, "find_nearest_value", 
                      target=target, num_candidates=len(candidates))
    
    try:
        enhanced_log(log_callback, f"Finding nearest value to {target} among {len(candidates)} candidates", 'DEBUG')
        
        # Validate input
        if not candidates:
            enhanced_log(log_callback, "Empty candidates list provided", 'ERROR')
            log_constraint_check(log_callback, "Non-empty candidates", len(candidates), "> 0", False)
            raise ValueError("Candidates list cannot be empty")
        
        log_constraint_check(log_callback, "Non-empty candidates", len(candidates), "> 0", True)
        
        # Log candidate range for context
        min_candidate = min(candidates)
        max_candidate = max(candidates)
        enhanced_log(log_callback, f"Candidate range: [{min_candidate:.6f}, {max_candidate:.6f}]", 'DEBUG')
        
        with create_timed_logger(log_callback, "nearest value search") as timer:
            nearest_value = min(candidates, key=lambda x: abs(x - target))
            min_distance = abs(nearest_value - target)
        
        log_calculation_result(log_callback, "Nearest value", f"{nearest_value:.6f}")
        log_calculation_result(log_callback, "Distance to target", f"{min_distance:.6f}")
        
        # Check if target is within candidate bounds
        if target < min_candidate:
            enhanced_log(log_callback, f"Target below candidate range by {min_candidate - target:.6f}", 'INFO')
        elif target > max_candidate:
            enhanced_log(log_callback, f"Target above candidate range by {target - max_candidate:.6f}", 'INFO')
        else:
            enhanced_log(log_callback, "Target within candidate range", 'DEBUG')
        
        # Log search efficiency metrics
        log_performance_metric(log_callback, "Candidates evaluated", len(candidates), "values")
        
        # Check for exact match
        if min_distance == 0:
            enhanced_log(log_callback, "Exact match found", 'INFO')
        else:
            # Calculate relative error
            if target != 0:
                relative_error = (min_distance / abs(target)) * 100
                log_performance_metric(log_callback, "Relative error", relative_error, "%")
        
        # Check for multiple equally close values
        equally_close = [c for c in candidates if abs(c - target) == min_distance]
        if len(equally_close) > 1:
            enhanced_log(log_callback, f"Found {len(equally_close)} equally close values", 'INFO')
        
        log_function_exit(log_callback, "find_nearest_value", nearest_value)
        return nearest_value
        
    except Exception as e:
        log_error_with_context(log_callback, e, "find_nearest_value")
        raise


def generate_range(start: float, end: float, step: float, log_callback: Optional[Callable] = None) -> List[float]:
    """
    Generate a range of values with given step size.
    
    Args:
        start: Starting value
        end: Ending value (exclusive)
        step: Step size
        log_callback: Optional callback for logging
        
    Returns:
        List of values in the range
        
    Raises:
        ValueError: If step size is zero or negative
    """
    log_function_entry(log_callback, "generate_range", 
                      start=start, end=end, step=step)
    
    try:
        enhanced_log(log_callback, f"Generating range from {start} to {end} with step {step}", 'DEBUG')
        
        # Validate step size
        if step <= 0:
            enhanced_log(log_callback, f"Invalid step size: {step} <= 0", 'ERROR')
            log_constraint_check(log_callback, "Positive step size", step, "> 0", False)
            raise ValueError("Step size must be positive")
        
        log_constraint_check(log_callback, "Positive step size", step, "> 0", True)
        
        # Calculate expected number of steps
        if start < end:
            expected_count = int((end - start) / step)
            enhanced_log(log_callback, f"Expected approximately {expected_count} values", 'DEBUG')
        else:
            enhanced_log(log_callback, "Start >= end, will generate empty range", 'WARNING')
        
        with create_timed_logger(log_callback, "range generation") as timer:
            result = []
            current = start
            step_count = 0
            
            while current < end:
                result.append(current)
                current += step
                step_count += 1
                
                # Safety check for infinite loops
                if step_count > 1000000:  # Arbitrary large limit
                    enhanced_log(log_callback, "Range generation exceeds safety limit (1M values)", 'ERROR')
                    break
        
        actual_count = len(result)
        log_calculation_result(log_callback, "Generated values", actual_count)
        
        if actual_count > 0:
            actual_end = result[-1]
            log_calculation_result(log_callback, "Actual range", f"[{start:.6f}, {actual_end:.6f}]")
            
            # Check step consistency
            if actual_count > 1:
                actual_step = result[1] - result[0]
                step_error = abs(actual_step - step)
                if step_error > 1e-12:
                    enhanced_log(log_callback, f"Step size deviation: {step_error:.2e}", 'WARNING')
                
                log_performance_metric(log_callback, "Step consistency error", step_error, "")
        
        # Log memory efficiency
        if actual_count > 10000:
            enhanced_log(log_callback, f"Large range generated ({actual_count} values)", 'INFO')
        
        log_performance_metric(log_callback, "Range generation rate", 
                             actual_count / (timer.get_duration() or 1e-6), "values/sec")
        
        log_validation_result(log_callback, "Range generation", True, 
                            f"Successfully generated {actual_count} values")
        
        log_function_exit(log_callback, "generate_range", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generate_range")
        raise

