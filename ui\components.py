"""Reusable UI components."""

import tkinter as tk
from tkinter import ttk, Menu, messagebox
from typing import Callable, Optional

from config.app_config import APP_TITLE, ICON_PATH, SOFTWARE_VERSION


def show_about():
    """Display information about the application"""
    about_text = f"""
=====================================
        {APP_TITLE}
             Version: {SOFTWARE_VERSION}
=====================================

Copyright © 2022 Alex Sze
All rights reserved.

The software provided is protected under copyright law.
Any reproduction, distribution, or modification of this 
software without prior written permission from the owner 
is strictly prohibited.

-------------------------------------
            DISCLAIMER
-------------------------------------
The foundation design automation program is intended to 
assist in automating calculations and providing design 
suggestions.

However, it is crucial for users to recognize that they 
bear full responsibility for verifying all aspects of 
the designs generated by this software.

Users must ensure compliance with applicable codes, 
standards, and regulations.
"""
    messagebox.showinfo(f"About {APP_TITLE}", about_text)


class BaseFrame(tk.Frame):
    """Base frame with common functionality."""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.parent = parent

    def clear_window(self):
        """Clear all widgets from parent window."""
        for widget in self.parent.winfo_children():
            widget.destroy()


class HeaderFrame(tk.Frame):
    """Reusable header component."""

    def __init__(self, parent, title: str = APP_TITLE, show_user: bool = False, 
                 username: str = "", user_type: str = "", on_logout: Optional[Callable] = None):
        super().__init__(parent)
        self.pack(fill="x", padx=20, pady=10)
          # Title
        tk.Label(self, text=title, font=("Arial", 16, "bold")).pack(anchor="w")
        
        # User info and logout
        if show_user and username:
            user_frame = tk.Frame(self)
            user_frame.pack(anchor="e")
            
            tk.Label(
                user_frame,
                text=f"Logged in as: {username} ({user_type} Version)",
                font=("Arial", 10, "italic")
            ).pack()
            
            if on_logout:
                tk.Button(
                    user_frame,
                    text="Logout",
                    command=on_logout,
                    width=10,
                    height=1
                ).pack(pady=(5, 0))
          # Separator
        ttk.Separator(parent, orient="horizontal").pack(fill="x", padx=20, pady=5)


class TimerLabel(tk.Label):
    """Timer display label."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, font=("Arial", 10), **kwargs)
        self.active = False
        
    def update_time(self, time_string: str):
        """Update displayed time with formatted string."""
        if self.winfo_exists():
            self.config(text=time_string)
    
    def update_time_components(self, hours: int, minutes: int, seconds: int):
        """Update displayed time with individual components."""
        if self.winfo_exists():
            self.config(text=f"Time remaining: {hours}:{minutes:02d}:{seconds:02d}")
    
    def activate(self):
        """Activate timer updates."""
        self.active = True
    
    def deactivate(self):
        """Deactivate timer updates."""
        self.active = False


def create_menu_bar(root: tk.Tk, on_about: Callable):
    """Create application menu bar."""
    menubar = Menu(root)
    root.config(menu=menubar)
    
    # File menu
    file_menu = Menu(menubar, tearoff=0)
    menubar.add_cascade(label="File", menu=file_menu)
    file_menu.add_command(label="Exit", command=root.quit)
    
    # Help menu
    help_menu = Menu(menubar, tearoff=0)
    menubar.add_cascade(label="Help", menu=help_menu)
    help_menu.add_command(label="About", command=on_about)


def setup_window_icon(window: tk.Tk | tk.Toplevel):
    """Set window icon if available."""
    try:
        if ICON_PATH.exists():
            window.iconbitmap(str(ICON_PATH))
    except Exception as e:
        pass  # Silently ignore icon errors
