# ETABS Integration System (`etabs_cwls/`)

## Core ETABS Components
- **`cwls_gui.py`**: LoadingScheduleGUI class for creating loading schedules from ETABS
- **`_main.py`**: Primary workflow coordination and exception handling
- **`_class.py`**: Core data classes (file_path, design_data, ETABS_converter)

## Data Processing Pipeline
- **`initialization_processor.py`**: ETABS model initialization:
  - `initialization()` for model setup
  - `read_load_cases()`, `read_story_data()` for model data extraction
  - Excel template creation for load mapping
- **`joint_reaction_processor.py`**: Joint reaction processing for foundation loads
- **`pier_force_processor.py`**: Comprehensive pier force processing:
  - Gravity and lateral force separation
  - Column/wall schedule differentiation
  - Multi-story force coordination

## Advanced Features
- **`coordinate_transformer.py`**: Coordinate system transformation:
  - `transform_pier_forces()`, `transform_pier_moments()` for force transformation
  - `integer_round()` for precision control
- **`schedule_generator.py`**: Final schedule generation:
  - Load factor application and matrix operations
  - Schedule merging and validation
- **`configuration_manager.py`**: System configuration and validation:
  - ETABS converter assignment
  - Default load factor management
  - Configuration summary generation

## File & Path Management
- **`file_path_manager.py`**: Comprehensive file path management:
  - `filepath_selection()` for interactive file selection
  - `configure_output_paths()` and `configure_sheet_names()` for setup
  - File access validation and directory management

## Quality Assurance
- **`test_integration.py`**: IntegrationTester class for system validation
- **`validate_refactoring.py`**: RefactoringValidator for code quality
- **`test_basic_imports.py`**: Basic functionality testing
- **Enhanced logging**: Performance metrics, validation results, error tracking

## Utilities
- **`console_interface.py`**: Command-line interface for automation
- **`exceptions.py`**: Comprehensive exception hierarchy for ETABS operations
- **`logging_config.py`**: Advanced logging with performance monitoring