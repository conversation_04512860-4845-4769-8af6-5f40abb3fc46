"""Export structural analysis data to SAFE16 F2K and Excel formats.

Supports F2K text format for SAFE16 model interchange and Excel format for manual review.
Handles complex MultiIndex DataFrames with automatic data cleaning and formatting."""

from datetime import datetime

import openpyxl as oxl
import pandas as pd


def append_safe16_f2k(f2k, df):
    """Append a pandas DataFrame to SAFE16 F2K format file with proper table formatting."""
    # Handle cases where df might not have the expected structure or is empty
    if not isinstance(df.columns, pd.MultiIndex) or df.columns.nlevels < 1 or df.shape[1] == 0:
        # Skip completely empty DataFrames to avoid writing empty tables
        if df.shape[1] == 0:
            return

        # Handle DataFrames with unexpected column structure
        # This provides robustness for edge cases where MultiIndex is not present
        # or has fewer levels than expected from typical SAFE16 data structure
        raw_title_text = "UNTITLED"
        split_title_parts = [raw_title_text.upper()]
    else:
        # Extract table name and description from MultiIndex column structure
        # SAFE16 data typically has table names in format "TABLE_NAME  Description"
        # Access first level of the first column's name
        raw_title_text = str(df.columns[0][0])
        # Split at most once on double spaces to separate table name from description
        split_title_parts = raw_title_text.upper().split("  ", 1)  # Split at most once

    # Parse table name and description components
    table_name_part = split_title_parts[0]
    table_desc_part = split_title_parts[1] if len(
        split_title_parts) > 1 else ""

    # Determine simplified columns for iteration without modifying the original df
    # This handles the complex MultiIndex structure common in SAFE16 DataFrames
    # Ensure original df has enough levels to drop, otherwise use existing columns or handle error
    simplified_columns = df.columns
    if isinstance(df.columns, pd.MultiIndex) and df.columns.nlevels >= 3:
        # For standard SAFE16 structure, drop levels 0 and 2, keeping level 1 (parameter names)
        simplified_columns = df.columns.droplevel((0, 2))
    # if less than 3 levels but still multiindex
    elif isinstance(df.columns, pd.MultiIndex) and df.columns.nlevels > 0:
        # Use the last level as parameter names for output
        simplified_columns = df.columns.get_level_values(
            df.columns.nlevels - 1)  # use last level
    # if not multiindex, simplified_columns remains df.columns (single level)

    # Write table header in F2K format
    f2k.write(' \n')  # Blank line separator
    f2k.write(f'{table_name_part}  "{table_desc_part}"\n')

    # Skip writing data rows for empty DataFrames
    if df.empty:  # No rows to write
        return

    # Process each data row and write in F2K parameter format
    for _, r_series in df.iterrows():
        # Build row data efficiently
        # r_series.index is original (potentially MultiIndex)
        # simplified_columns holds the desired names for output
        # r_series.values are the actual data for the row
        item_strings = []
        
        # Check for column count mismatch and handle appropriately
        if len(simplified_columns) == len(r_series.values):
            # Standard case: column names match data values
            for i in range(len(simplified_columns)):
                col_name = simplified_columns[i]
                value = r_series.values[i]
                # Format as F2K parameter assignment: param="value"
                item_strings.append(f'   {col_name}="{value}"')
        else:
            # This might happen if droplevel logic isn't perfect for all df structures
            # Use original series items with index-based column names
            for col_name, value in r_series.items():  # col_name could be a tuple if MultiIndex
                # Ensure col_name is a simple string for output
                # Extract last element if tuple (from MultiIndex)
                name_str = str(
                    col_name[-1] if isinstance(col_name, tuple) else col_name)
                item_strings.append(f'   {name_str}="{value}"')

        # Combine all parameter assignments for this row
        data_line_content = "".join(item_strings)

        # Apply SAFE16-specific data cleaning replacements
        # Convert various representations of None/NaN to empty strings
        # This ensures SAFE16 can properly parse the file without errors
        data_line_content = data_line_content.replace('"None"', '""')
        data_line_content = data_line_content.replace('"Nan"', '""')
        data_line_content = data_line_content.replace('"none"', '""')
        data_line_content = data_line_content.replace('"nan"', '""')

        # Write the formatted data line to file
        f2k.write(f'{data_line_content}\n')


def export_safe16_f2k(file_paths, safe16_dfs):
    """Export SAFE16 model data to F2K format file with timestamped header."""
    # Define comprehensive mapping of sheet names to DataFrame objects
    # Order is important - matches SAFE16 import sequence requirements
    sheets_df = {
        # Program and project settings
        'Program Control': safe16_dfs.ProgramControl,
        'Mass Source': safe16_dfs.MassSource,
        'Project Information': safe16_dfs.ProjectInformation,
        
        # Analysis and modeling options
        'Automatic Slab Mesh Options': safe16_dfs.AutomaticSlabMeshOptions,
        'Cracking Analysis Reinforcement': safe16_dfs.CrackingAnalysisReinforcement,
        'Advanced SapFire Options': safe16_dfs.AdvancedSapFireOptions,
        'Advanced Modeling Options': safe16_dfs.AdvancedModelingOptions,
        
        # Object geometry definitions
        'Obj Geom - Point Coordinates': safe16_dfs.ObjGeomPointCoordinates,
        'Obj Geom - Lines 01 - General': safe16_dfs.ObjGeomLines01General,
        'Obj Geom - Lines 02 - Curved Bm': safe16_dfs.ObjGeomLines02CurvedBm,
        'Obj Geom - Areas 01 - General': safe16_dfs.ObjGeomAreas01General,
        'Obj Geom - Areas 02 - Curved Sl': safe16_dfs.ObjGeomAreas02CurvedSl,
        'Obj Geom - Areas 03 - Curved Wl': safe16_dfs.ObjGeomAreas03CurvedWl,
        'Obj Geom - Areas 04 - Wall Pnls': safe16_dfs.ObjGeomAreas04WallPnls,
        'Obj Geom - Tendons 01 - General': safe16_dfs.ObjGeomTendons01General,
        'Obj Geom - Tendons 02 - HLayout': safe16_dfs.ObjGeomTendons02HLayout,
        'Obj Geom - Tendons 03 - Vert Pr': safe16_dfs.ObjGeomTendons03VertPr,
        'Obj Geom - Tendons 04 - Disc Pt': safe16_dfs.ObjGeomTendons04DiscPt,
        'Obj Geom - Tendons 05 - Support': safe16_dfs.ObjGeomTendons05Support,
        'Object Geometry - Slab Rebar': safe16_dfs.ObjectGeometrySlabRebar,
        'Obj Geom - Design Strips': safe16_dfs.ObjGeomDesignStrips,
        'Object Geometry - Dimension Lns': safe16_dfs.ObjectGeometryDimensionLns,
        
        # Coordinate and grid systems
        'Coordinate Systems': safe16_dfs.CoordinateSystems,
        'Grid Lines': safe16_dfs.GridLines,
        
        # Material properties
        'Material Prop 01 - General': safe16_dfs.MaterialProp01General,
        'Material Prop 02 - Steel': safe16_dfs.MaterialProp02Steel,
        'Material Prop 03 - Concrete': safe16_dfs.MaterialProp03Concrete,
        'Material Prop 04 - Rebar': safe16_dfs.MaterialProp04Rebar,
        'Material Prop 05 - Tendon': safe16_dfs.MaterialProp05Tendon,
        'Material Prop 06 - Other': safe16_dfs.MaterialProp06Other,
        
        # Structural element properties
        'Beam Properties 01 - General': safe16_dfs.BeamProperties01General,
        'Beam Properties 02 - Rectangle': safe16_dfs.BeamProperties02Rectangle,
        'Beam Properties 03 - T Beam': safe16_dfs.BeamProperties03TBeam,
        'Beam Properties 04 - L Beam': safe16_dfs.BeamProperties04LBeam,
        # Note: Beam Properties 05 and 06 are commented out in original mapping
        # This may indicate they are not always present or are optional
        
        'Slab Properties 01 - General': safe16_dfs.SlabProperties01General,
        'Slab Prop 02 - Solid Slabs': safe16_dfs.SlabProp02SolidSlabs,
        'Slab Prop 03 - Ribs And Waffles': safe16_dfs.SlabProp03RibsAndWaffles,
        'Tendon Properties': safe16_dfs.TendonProperties,
        'Reinforcing Bar Sizes': safe16_dfs.ReinforcingBarSizes,
        
        # Column properties
        'Column Properties 01 - General': safe16_dfs.ColumnProperties01General,
        'Column Props 02 - Rectangular': safe16_dfs.ColumnProps02Rectangular,
        'Column Properties 03 - Circular': safe16_dfs.ColumnProperties03Circular,
        'Column Properties 04 - T Shape': safe16_dfs.ColumnProperties04TShape,
        'Column Properties 05 - L Shape': safe16_dfs.ColumnProperties05LShape,
        'Column Props 06 - General Shape': safe16_dfs.ColumnProps06GeneralShape,
        
        # Other structural properties
        'Wall Properties': safe16_dfs.WallProperties,
        'Soil Properties': safe16_dfs.SoilProperties,
        'Spring Properties - Line': safe16_dfs.SpringPropertiesLine,
        'Spring Properties - Point': safe16_dfs.SpringPropertiesPoint,
        
        # Loading definitions
        'Load Patterns': safe16_dfs.LoadPatterns,
        'Load Cases 01 - General': safe16_dfs.LoadCases01General,
        'Load Cases 02 - Static': safe16_dfs.LoadCases02Static,
        'Load Cases 03 - MultistepStatic': safe16_dfs.LoadCases03MultistepStatic,
        'Load Cases 04 - Modal': safe16_dfs.LoadCases04Modal,
        'Load Cases 05 - Hyperstatic': safe16_dfs.LoadCases05Hyperstatic,
        'Load Cases 06 - Loads Applied': safe16_dfs.LoadCases06LoadsApplied,
        'Load Cases 07 - Resp Spec - Gen': safe16_dfs.LoadCases07RespSpecGen,
        'Load Cases 08 - Resp Spec - LA': safe16_dfs.LoadCases08RespSpecLA,
        'Load Cases 09 - External MD': safe16_dfs.LoadCases09ExternalMD,
        'Load Combinations': safe16_dfs.LoadCombinations,
        'Function - Response Spectrum': safe16_dfs.FunctionResponseSpectrum,
        
        # Groups and assignments
        'Group Definitions': safe16_dfs.GroupDefinitions,
        'Group Assignments': safe16_dfs.GroupAssignments,
        
        # Property assignments
        'Slab Property Assignments': safe16_dfs.SlabPropertyAssignments,
        'Slab Property Modifiers': safe16_dfs.SlabPropertyModifiers,
        'Slab Vertical Offsets': safe16_dfs.SlabVerticalOffsets,
        'Slab Local Axes': safe16_dfs.SlabLocalAxes,
        'Slab Edge Releases': safe16_dfs.SlabEdgeReleases,
        'Slab Line Releases': safe16_dfs.SlabLineReleases,
        'Slab Rib Locations': safe16_dfs.SlabRibLocations,
        'Beam Property Assignments': safe16_dfs.BeamPropertyAssignments,
        'Beam Property Modifiers': safe16_dfs.BeamPropertyModifiers,
        'Beam End Releases': safe16_dfs.BeamEndReleases,
        'Beam Insertion Point': safe16_dfs.BeamInsertionPoint,
        'Tendon Property Assignments': safe16_dfs.TendonPropertyAssignments,
        'Slab Rebar Property Assignments': safe16_dfs.SlabRebarPropertyAssignments,
        'Column Property Assignments': safe16_dfs.ColumnPropertyAssignments,
        'Column Property Modifiers': safe16_dfs.ColumnPropertyModifiers,
        'Column Local Axes': safe16_dfs.ColumnLocalAxes,
        'Column End Releases': safe16_dfs.ColumnEndReleases,
        'Column Insertion Point': safe16_dfs.ColumnInsertionPoint,
        'Wall Property Assignments': safe16_dfs.WallPropertyAssignments,
        'Wall Property Modifiers': safe16_dfs.WallPropertyModifiers,
        'Wall Openings': safe16_dfs.WallOpenings,
        'Wall Normal Offsets': safe16_dfs.WallNormalOffsets,
        'Soil Property Assignments': safe16_dfs.SoilPropertyAssignments,
        
        # Restraints and springs
        'Point Restraint Assignments': safe16_dfs.PointRestraintAssignments,
        'Point Spring Assignments': safe16_dfs.PointSpringAssignments,
        'Line Spring Assignments': safe16_dfs.LineSpringAssignments,
        
        # Load assignments
        'Load Assigns - Surface Loads': safe16_dfs.LoadAssignsSurfaceLoads,
        'Load Assigns - Slab Temp Loads': safe16_dfs.LoadAssignsSlabTempLoads,
        'Loads - Lines - Point': safe16_dfs.LoadsLinesPoint,
        'Loads - Lines - Distributed': safe16_dfs.LoadsLinesDistributed,
        'Load Assignments - Point Loads': safe16_dfs.LoadAssignmentsPointLoads,
        'Load Assignments - Point Displs': safe16_dfs.LoadAssignmentsPointDispls,
        'Load Assignments - Tendon Loads': safe16_dfs.LoadAssignmentsTendonLoads,
        'Load Assignments - Tendon Loss': safe16_dfs.LoadAssignmentsTendonLoss,
        
        # Analysis mesh
        'Objs Analysis Mesh 01 - Points': safe16_dfs.ObjsAnalysisMesh01Points,
        'Objs Analysis Mesh 02 - Lines': safe16_dfs.ObjsAnalysisMesh02Lines,
        
        # Design preferences and overwrites
        'Design Pref 01 - Resist Factors': safe16_dfs.DesignPref01ResistFactors,
        'Design Pref 02 - Cover - Slabs': safe16_dfs.DesignPref02CoverSlabs,
        'Design Pref 03 - Cover - Beams': safe16_dfs.DesignPref03CoverBeams,
        'Design Pref 04 - Prestress Data': safe16_dfs.DesignPref04PrestressData,
        'Slab Design Overwrites 01 - Str': safe16_dfs.SlabDesignOverwrites01Str,
        'Slab Design Overwrites 02 - FE': safe16_dfs.SlabDesignOverwrites02FE,
        'Beam Design Overwrites': safe16_dfs.BeamDesignOverwrites,
        
        # Punching shear analysis
        'Punching Shear 01 - General': safe16_dfs.PunchingShear01General,
        'Punching Shear 02 - User Perim': safe16_dfs.PunchingShear02UserPerim,
        'Punching Shear 03 - User Opens': safe16_dfs.PunchingShear03UserOpens,
    }    
    
    # Open the output file for writing with UTF-8 encoding
    with open(file_paths.f2kSAFE16Model, 'w+', encoding='utf-8') as f2k:
        # Write file header with timestamp for version tracking
        now = datetime.now()
        # Use Windows-style date formatting with # for no leading zeros
        timestamp = f'File {file_paths.f2kSAFE16Model} was saved on {now.strftime("%#m/%#d/%#Y")} at {now.strftime("%#I:%#M:%S")}\n'
        f2k.write(timestamp)

        # Iterate over the data frames and write them to the output file
        # Process each DataFrame in the defined order
        for df_name, df in sheets_df.items():
            if isinstance(df, pd.DataFrame):  # Ensure it's a DataFrame
                append_safe16_f2k(f2k, df)
            # Note: Non-DataFrame items are silently skipped
            # Consider adding logging for debugging if needed

        # Write F2K file terminator
        f2k.write(' \n')
        f2k.write('END TABLE DATA\n')

    # The data cleaning replacements are now handled within append_safe16_f2k
    # This eliminates the need for redundant file read/write operations
    # and improves performance for large models

    # Read back the complete file content for return value
    # This maintains compatibility with calling code that may expect the content
    final_content = ""
    try:
        with open(file_paths.f2kSAFE16Model, 'r') as file_to_read:
            final_content = file_to_read.read()
    except Exception as e:
        print(f"Error reading back the file for return value: {e}")

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} SAFE16 Model saved to f2k file')
    return final_content


def export_safe16_excel(file_paths, safe16_dfs):
    """Export SAFE16 model data to Excel format with each component in separate worksheets.
    """
    # Define comprehensive mapping of sheet names to DataFrame objects
    # Includes all components from F2K export plus Excel-specific additions
    sheet_dfs = {
        # Program and project settings
        'Program Control': safe16_dfs.ProgramControl,
        'Mass Source': safe16_dfs.MassSource,
        'Project Information': safe16_dfs.ProjectInformation,
        
        # Analysis options
        'Automatic Slab Mesh Options': safe16_dfs.AutomaticSlabMeshOptions,
        'Advanced SapFire Options': safe16_dfs.AdvancedSapFireOptions,
        'Advanced Modeling Options': safe16_dfs.AdvancedModelingOptions,
        'Cracking Analysis Reinforcement': safe16_dfs.CrackingAnalysisReinforcement,
        
        # Object geometry definitions
        'Obj Geom - Point Coordinates': safe16_dfs.ObjGeomPointCoordinates,
        'Obj Geom - Lines 01 - General': safe16_dfs.ObjGeomLines01General,
        'Obj Geom - Lines 02 - Curved Bm': safe16_dfs.ObjGeomLines02CurvedBm,
        'Obj Geom - Areas 01 - General': safe16_dfs.ObjGeomAreas01General,
        'Obj Geom - Areas 02 - Curved Sl': safe16_dfs.ObjGeomAreas02CurvedSl,
        'Obj Geom - Areas 03 - Curved Wl': safe16_dfs.ObjGeomAreas03CurvedWl,
        'Obj Geom - Areas 04 - Wall Pnls': safe16_dfs.ObjGeomAreas04WallPnls,
        'Obj Geom - Tendons 01 - General': safe16_dfs.ObjGeomTendons01General,
        'Obj Geom - Tendons 02 - HLayout': safe16_dfs.ObjGeomTendons02HLayout,
        'Obj Geom - Tendons 03 - Vert Pr': safe16_dfs.ObjGeomTendons03VertPr,
        'Obj Geom - Tendons 04 - Disc Pt': safe16_dfs.ObjGeomTendons04DiscPt,
        'Obj Geom - Tendons 05 - Support': safe16_dfs.ObjGeomTendons05Support,
        'Object Geometry - Slab Rebar': safe16_dfs.ObjectGeometrySlabRebar,
        'Obj Geom - Design Strips': safe16_dfs.ObjGeomDesignStrips,
        'Object Geometry - Dimension Lns': safe16_dfs.ObjectGeometryDimensionLns,
        
        # Coordinate systems
        'Coordinate Systems': safe16_dfs.CoordinateSystems,
        'Grid Lines': safe16_dfs.GridLines,
        
        # Material properties - comprehensive material definitions
        'Material Prop 01 - General': safe16_dfs.MaterialProp01General,
        'Material Prop 02 - Steel': safe16_dfs.MaterialProp02Steel,
        'Material Prop 03 - Concrete': safe16_dfs.MaterialProp03Concrete,
        'Material Prop 04 - Rebar': safe16_dfs.MaterialProp04Rebar,
        'Material Prop 05 - Tendon': safe16_dfs.MaterialProp05Tendon,
        'Material Prop 06 - Other': safe16_dfs.MaterialProp06Other,
        
        # Beam properties - includes Excel-specific beam components
        'Beam Properties 01 - General': safe16_dfs.BeamProperties01General,
        'Beam Properties 02 - Rectangle': safe16_dfs.BeamProperties02Rectangle,
        'Beam Properties 03 - T Beam': safe16_dfs.BeamProperties03TBeam,
        'Beam Properties 04 - L Beam': safe16_dfs.BeamProperties04LBeam,
        'Beam Props 05 - General Beam': safe16_dfs.BeamProps05GeneralBeam,  # Excel-specific
        'Beam Props 06 - Design Data': safe16_dfs.BeamProps06DesignData,      # Excel-specific
        
        # Slab properties
        'Slab Properties 01 - General': safe16_dfs.SlabProperties01General,
        'Slab Prop 02 - Solid Slabs': safe16_dfs.SlabProp02SolidSlabs,
        'Slab Prop 03 - Ribs And Waffles': safe16_dfs.SlabProp03RibsAndWaffles,
        'Tendon Properties': safe16_dfs.TendonProperties,
        'Reinforcing Bar Sizes': safe16_dfs.ReinforcingBarSizes,
        
        # Column properties - all standard column types
        'Column Properties 01 - General': safe16_dfs.ColumnProperties01General,
        'Column Props 02 - Rectangular': safe16_dfs.ColumnProps02Rectangular,
        'Column Properties 03 - Circular': safe16_dfs.ColumnProperties03Circular,
        'Column Properties 04 - T Shape': safe16_dfs.ColumnProperties04TShape,
        'Column Properties 05 - L Shape': safe16_dfs.ColumnProperties05LShape,
        'Column Props 06 - General Shape': safe16_dfs.ColumnProps06GeneralShape,
        
        # Other structural properties
        'Wall Properties': safe16_dfs.WallProperties,
        'Soil Properties': safe16_dfs.SoilProperties,
        'Spring Properties - Line': safe16_dfs.SpringPropertiesLine,
        'Spring Properties - Point': safe16_dfs.SpringPropertiesPoint,
        
        # Loading definitions - comprehensive load specification
        'Load Patterns': safe16_dfs.LoadPatterns,
        'Load Cases 01 - General': safe16_dfs.LoadCases01General,
        'Load Cases 02 - Static': safe16_dfs.LoadCases02Static,
        'Load Cases 03 - MultistepStatic': safe16_dfs.LoadCases03MultistepStatic,
        'Load Cases 04 - Modal': safe16_dfs.LoadCases04Modal,
        'Load Cases 05 - Hyperstatic': safe16_dfs.LoadCases05Hyperstatic,
        'Load Cases 06 - Loads Applied': safe16_dfs.LoadCases06LoadsApplied,
        'Load Cases 07 - Resp Spec - Gen': safe16_dfs.LoadCases07RespSpecGen,
        'Load Cases 08 - Resp Spec - LA': safe16_dfs.LoadCases08RespSpecLA,
        'Load Cases 09 - External MD': safe16_dfs.LoadCases09ExternalMD,
        'Load Combinations': safe16_dfs.LoadCombinations,
        'Function - Response Spectrum': safe16_dfs.FunctionResponseSpectrum,
        
        # Groups and assignments
        'Group Definitions': safe16_dfs.GroupDefinitions,
        'Group Assignments': safe16_dfs.GroupAssignments,
        
        # Property assignments - element-specific assignments
        'Slab Property Assignments': safe16_dfs.SlabPropertyAssignments,
        'Slab Property Modifiers': safe16_dfs.SlabPropertyModifiers,
        'Slab Vertical Offsets': safe16_dfs.SlabVerticalOffsets,
        'Slab Local Axes': safe16_dfs.SlabLocalAxes,
        'Slab Edge Releases': safe16_dfs.SlabEdgeReleases,
        'Slab Line Releases': safe16_dfs.SlabLineReleases,
        'Slab Rib Locations': safe16_dfs.SlabRibLocations,
        'Beam Property Assignments': safe16_dfs.BeamPropertyAssignments,
        'Beam Property Modifiers': safe16_dfs.BeamPropertyModifiers,
        'Beam End Releases': safe16_dfs.BeamEndReleases,
        'Beam Insertion Point': safe16_dfs.BeamInsertionPoint,
        'Tendon Property Assignments': safe16_dfs.TendonPropertyAssignments,
        'Slab Rebar Property Assignments': safe16_dfs.SlabRebarPropertyAssignments,
        'Column Property Assignments': safe16_dfs.ColumnPropertyAssignments,
        'Column Property Modifiers': safe16_dfs.ColumnPropertyModifiers,
        'Column Local Axes': safe16_dfs.ColumnLocalAxes,
        'Column End Releases': safe16_dfs.ColumnEndReleases,
        'Column Insertion Point': safe16_dfs.ColumnInsertionPoint,
        'Wall Property Assignments': safe16_dfs.WallPropertyAssignments,
        'Wall Property Modifiers': safe16_dfs.WallPropertyModifiers,
        'Wall Openings': safe16_dfs.WallOpenings,
        'Wall Normal Offsets': safe16_dfs.WallNormalOffsets,
        'Soil Property Assignments': safe16_dfs.SoilPropertyAssignments,
        
        # Restraints and spring assignments
        'Point Restraint Assignments': safe16_dfs.PointRestraintAssignments,
        'Point Spring Assignments': safe16_dfs.PointSpringAssignments,
        'Line Spring Assignments': safe16_dfs.LineSpringAssignments,
        
        # Load assignments - detailed load application
        'Load Assigns - Surface Loads': safe16_dfs.LoadAssignsSurfaceLoads,
        'Load Assigns - Slab Temp Loads': safe16_dfs.LoadAssignsSlabTempLoads,
        'Loads - Lines - Point': safe16_dfs.LoadsLinesPoint,
        'Loads - Lines - Distributed': safe16_dfs.LoadsLinesDistributed,
        'Load Assignments - Point Loads': safe16_dfs.LoadAssignmentsPointLoads,
        'Load Assignments - Point Displs': safe16_dfs.LoadAssignmentsPointDispls,
        'Load Assignments - Tendon Loads': safe16_dfs.LoadAssignmentsTendonLoads,
        'Load Assignments - Tendon Loss': safe16_dfs.LoadAssignmentsTendonLoss,
        
        # Analysis mesh definitions
        'Objs Analysis Mesh 01 - Points': safe16_dfs.ObjsAnalysisMesh01Points,
        'Objs Analysis Mesh 02 - Lines': safe16_dfs.ObjsAnalysisMesh02Lines,
        
        # Design preferences and overwrites
        'Design Pref 01 - Resist Factors': safe16_dfs.DesignPref01ResistFactors,
        'Design Pref 02 - Cover - Slabs': safe16_dfs.DesignPref02CoverSlabs,
        'Design Pref 03 - Cover - Beams': safe16_dfs.DesignPref03CoverBeams,
        'Design Pref 04 - Prestress Data': safe16_dfs.DesignPref04PrestressData,
        'Slab Design Overwrites 01 - Str': safe16_dfs.SlabDesignOverwrites01Str,
        'Slab Design Overwrites 02 - FE': safe16_dfs.SlabDesignOverwrites02FE,
        'Beam Design Overwrites': safe16_dfs.BeamDesignOverwrites,
        
        # Punching shear analysis components
        'Punching Shear 01 - General': safe16_dfs.PunchingShear01General,
        'Punching Shear 02 - User Perim': safe16_dfs.PunchingShear02UserPerim,
        'Punching Shear 03 - User Opens': safe16_dfs.PunchingShear03UserOpens
    }

    # Create Excel writer object and export all DataFrames
    # Using pandas ExcelWriter with openpyxl engine for xlsx format
    with pd.ExcelWriter(file_paths.ExcelSAFE16Model) as writer:
        for sheet_name, df in sheet_dfs.items():
            # Export each DataFrame to its own worksheet
            # Default parameters preserve MultiIndex structure
            df.to_excel(writer, sheet_name=sheet_name)

    # Post-process Excel file to clean up formatting
    # Remove index columns and header rows that are artifacts of MultiIndex structure
    wb = oxl.load_workbook(file_paths.ExcelSAFE16Model)
    for ws in wb:
        # Delete the first column (pandas index column)
        ws.delete_cols(1)
        # Delete row 4 (MultiIndex formatting artifact)
        # This removes the middle level of MultiIndex which is typically empty
        ws.delete_rows(4)
    wb.save(file_paths.ExcelSAFE16Model)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} SAFE16 Model saved to Excel file!')
    
    # Return the original dataframes object for method chaining
    return safe16_dfs
