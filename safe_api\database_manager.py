"""
Database Management Module

This module handles all database operations including SQLite database
creation, table management, and DataFrame storage with optimized performance.
"""

import os
import sqlite3
import pandas as pd


def save_df_to_sqlite(df, table_name, db_path):
    """
    Saves a DataFrame to a SQLite database table with optimized performance.
    
    Args:
        df (pandas.DataFrame): DataFrame to save
        table_name (str): Name of the table to create/update
        db_path (str): Path to SQLite database file
    """
    if df.empty:
        print(f"DataFrame for table '{table_name}' is empty. Skipping save.")
        return

    try:
        abs_db_path = os.path.abspath(db_path)
        db_dir = os.path.dirname(abs_db_path)
        os.makedirs(db_dir, exist_ok=True)  # Ensure directory exists

        print(f"Saving to SQLite database: {abs_db_path}")

        conn = sqlite3.connect(abs_db_path)
        cursor = conn.cursor()

        # Performance optimizations for SQLite
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")
        cursor.execute("PRAGMA temp_store=MEMORY")

        cursor.execute(f"DROP TABLE IF EXISTS [{table_name}]")

        columns = []
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                if pd.api.types.is_integer_dtype(df[col]):
                    col_type = "INTEGER"
                else:
                    col_type = "REAL"
            else:
                col_type = "TEXT"
            columns.append(f"[{col}] {col_type}")

        create_table_sql = f"CREATE TABLE [{table_name}] ({', '.join(columns)})"
        cursor.execute(create_table_sql)

        placeholders = ", ".join(["?" for _ in df.columns])
        insert_sql = f"INSERT INTO [{table_name}] VALUES ({placeholders})"

        data_tuples = [tuple(row) for row in df.values]

        cursor.executemany(insert_sql, data_tuples)

        conn.commit()
        cursor.close()
        conn.close()

        print(f"DataFrame saved to SQLite table '{table_name}' in {abs_db_path}")

    except Exception as e:
        print(f"Error saving DataFrame to SQLite table '{table_name}': {e}")
        # Consider re-raising the exception if saving is critical
        # raise


def save_df_to_database(df, table_name, db_path):
    """
    Saves a DataFrame to a SQLite database with error handling and logging.
    
    Args:
        df (pandas.DataFrame): DataFrame to save
        table_name (str): Name of the table to create/update
        db_path (str): Path to SQLite database file
    """
    if df.empty:
        print(f"DataFrame for table '{table_name}' is empty. Skipping save.")
        return

    print(f"\n--- Attempting to save table: {table_name} to SQLite ---")
    try:
        save_df_to_sqlite(df, table_name, db_path)
        print(f"Successfully saved '{table_name}' to SQLite database: {db_path}")
    except Exception as sqlite_error:
        print(f"SQLite database save failed for '{table_name}': {sqlite_error}")
        # Optionally re-raise or handle as critical error if SQLite must succeed
        raise
    print(f"--- Completed save attempt for table: {table_name} ---\n")


def setup_database_path(config_obj):
    """
    Setup the SQLite database path based on configuration.
    
    Args:
        config_obj: Configuration object containing file paths
        
    Returns:
        str: Path to the SQLite database file
    """
    # Construct the SQLite database path dynamically
    model_excel_filename = os.path.basename(config_obj.MODEL_IMPORT_EXCEL_FILE)
    db_name_base = os.path.splitext(model_excel_filename)[0]
    sqlite_db_filename = f"{db_name_base}.sqlite"

    # Use ACCESS_DB_DIR from config for the SQLite database directory
    # This keeps the database location consistent with the previous setup,
    # but uses SQLite instead of Access.
    sqlite_db_dir = config_obj.ACCESS_DB_DIR
    if not os.path.exists(sqlite_db_dir):
        os.makedirs(sqlite_db_dir)
        print(f"Created directory for SQLite database: {sqlite_db_dir}")
    
    dynamic_sqlite_db_path = os.path.join(sqlite_db_dir, sqlite_db_filename)
    print(f"SQLite database will be saved to: {dynamic_sqlite_db_path}")
    
    return dynamic_sqlite_db_path


def process_and_save_dataframe(df, table_name, db_path):
    """
    Process and save a DataFrame if it contains data.
    
    Args:
        df (pandas.DataFrame): DataFrame to process and save
        table_name (str): Name of the table
        db_path (str): Path to database
    """
    if not df.empty:
        print(df.head() if not df.empty else "Processed DataFrame is empty.")
        if not df.empty:
            save_df_to_database(df, table_name, db_path)
    else:
        print(f"\nDataFrame for {table_name} is empty. Skipping processing.")
