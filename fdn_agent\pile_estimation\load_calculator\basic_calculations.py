﻿"""
Basic Load Calculations

Core functions for calculating loads from Excel input data.
"""

from typing import List, <PERSON><PERSON>, Dict, Union
import pandas as pd
import numpy as np

from ..data_types import ColumnData, WallData
from ..data_types import ExcelInputs
from ..exceptions import LoadCalculationError, InputDataError
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)


def calculate_element_load(df: pd.DataFrame, element_name: str,
                          load_types: List[str] = None, log_callback=None) -> Tuple[float, List[str]]:
    """
    Calculate total axial load for a single element.

    Args:
        df: Multi-index DataFrame with load data
        element_name: Name of the element
        load_types: List of load types to include (default: ['DL', 'SDL', 'LL'])
        log_callback: Optional callback for logging load calculation progress

    Returns:
        Tuple of (total_load, warnings)

    Raises:
        LoadCalculationError: If element cannot be found or load calculation fails
    """
    if load_types is None:
        load_types = ['DL', 'SDL', 'LL']

    log_function_entry(log_callback, "calculate_element_load",
                      element_name=element_name, load_types=load_types, df_shape=(len(df), len(df.columns)))

    try:
        enhanced_log(log_callback, f"Calculating load for element '{element_name}' with load types {load_types}", 'DEBUG')

        # Validate inputs
        log_constraint_check(log_callback, "dataframe_not_empty", len(df), 0, not df.empty)
        if df.empty:
            error_msg = "Empty DataFrame provided for load calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_element_load", "error")
            raise LoadCalculationError(error_msg)

        log_constraint_check(log_callback, "element_name_valid", len(element_name.strip()) if element_name else 0, 0,
                           element_name and element_name.strip())
        if not element_name or not element_name.strip():
            error_msg = "Invalid element name provided for load calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_element_load", "error")
            raise LoadCalculationError(error_msg)

        warnings = []
        total_load = 0.0

        # Find element mark column
        enhanced_log(log_callback, f"Searching for element mark column in DataFrame with {len(df.columns)} columns", 'DEBUG')
        mark_col = _find_element_mark_column(df, log_callback)
        if not mark_col:
            error_msg = "No element mark column found in DataFrame"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            enhanced_log(log_callback, f"Available columns: {list(df.columns)}", 'DEBUG')
            log_function_exit(log_callback, "calculate_element_load", "error")
            raise LoadCalculationError(error_msg)

        enhanced_log(log_callback, f"Found element mark column: {mark_col}", 'DEBUG')

        # Find element row
        enhanced_log(log_callback, f"Searching for element '{element_name}' in DataFrame with {len(df)} rows", 'DEBUG')
        element_row = _find_element_row(df, mark_col, element_name, log_callback)
        if element_row is None:
            error_msg = f"Element '{element_name}' not found in DataFrame"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            available_elements = df[mark_col].dropna().unique()[:10]  # Show first 10 available elements
            enhanced_log(log_callback, f"Available elements (first 10): {list(available_elements)}", 'DEBUG')
            log_function_exit(log_callback, "calculate_element_load", "error")
            raise LoadCalculationError(error_msg)

        enhanced_log(log_callback, f"Found element '{element_name}' at row index {element_row}, processing {len(load_types)} load types", 'INFO')

        # Sum loads for each type
        loads_found = False
        load_breakdown = []
        for load_type in load_types:
            enhanced_log(log_callback, f"Processing load type '{load_type}' for element '{element_name}'", 'DEBUG')
            load_value = _get_load_value(df, element_row, load_type, log_callback)
            if load_value is not None:
                # Ensure load_value is numeric before adding to total
                try:
                    numeric_load_value = float(load_value)
                    if not isinstance(numeric_load_value, (int, float)):
                        raise ValueError(f"Load value is not numeric: {load_value}")
                    total_load += numeric_load_value
                    loads_found = True
                    load_breakdown.append(f"{load_type}: {numeric_load_value:.2f} kN")
                    enhanced_log(log_callback, f"{load_type} load for '{element_name}': {numeric_load_value:.2f} kN", 'DEBUG')
                except (ValueError, TypeError) as e:
                    warning_msg = f"Invalid load value for element '{element_name}', load type '{load_type}': {load_value} (error: {e})"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                    continue
            else:
                warning_msg = f"No valid load found for element '{element_name}', load type '{load_type}'"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')

        if not loads_found:
            error_msg = f"No valid loads found for element '{element_name}' with load types {load_types}"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_element_load", "error")
            raise LoadCalculationError(error_msg)

        enhanced_log(log_callback, f"Load calculation completed for '{element_name}': {total_load:.2f} kN (breakdown: {', '.join(load_breakdown)})", 'INFO')

        final_load = max(0.0, total_load)
        log_calculation_result(log_callback, f"element_load_{element_name}", final_load, "kN")
        log_validation_result(log_callback, "element_load_calculation", final_load > 0,
                            f"Calculated {final_load:.2f} kN for element '{element_name}'")

        log_function_exit(log_callback, "calculate_element_load", f"{final_load:.2f} kN")
        return final_load, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_element_load")
        log_function_exit(log_callback, "calculate_element_load", "error")
        raise


def calculate_group_loads(group_elements: Dict[str, List[Union[ColumnData, WallData]]],
                         excel_inputs: ExcelInputs, log_callback=None) -> Tuple[float, List[str]]:
    """
    Calculate total load for a group of elements.

    Args:
        group_elements: Dictionary with 'columns' and 'walls' keys
        excel_inputs: ExcelInputs object with load data
        log_callback: Optional callback for logging load calculation progress

    Returns:
        Tuple of (total_load, warnings)

    Raises:
        LoadCalculationError: If calculation fails or no valid elements provided
    """
    log_function_entry(log_callback, "calculate_group_loads",
                      num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])))

    try:
        enhanced_log(log_callback, "Starting group load calculation", 'INFO')

        if not group_elements or not any(group_elements.values()):
            error_msg = "No elements provided in group_elements for load calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_group_loads", "error")
            raise LoadCalculationError(error_msg)

        if excel_inputs is None:
            error_msg = "ExcelInputs object is required for load calculation"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_group_loads", "error")
            raise LoadCalculationError(error_msg)

        # Validate that excel_inputs has the required load data attributes
        if not hasattr(excel_inputs, 'InputLoadColumn') or not hasattr(excel_inputs, 'InputLoadWall'):
            error_msg = "ExcelInputs object must have InputLoadColumn and InputLoadWall attributes"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "calculate_group_loads", "error")
            raise LoadCalculationError(error_msg)

        num_columns = len(group_elements.get('columns', []))
        num_walls = len(group_elements.get('walls', []))

        enhanced_log(log_callback, f"Group composition: {num_columns} columns, {num_walls} walls", 'INFO')
        enhanced_log(log_callback, f"Excel inputs validation: Column DF size {len(excel_inputs.InputLoadColumn) if excel_inputs.InputLoadColumn is not None else 0}, Wall DF size {len(excel_inputs.InputLoadWall) if excel_inputs.InputLoadWall is not None else 0}", 'DEBUG')

        warnings = []
        total_load = 0.0

        # Process columns
        if 'columns' in group_elements and group_elements['columns']:
            if excel_inputs.InputLoadColumn is None:
                error_msg = "Column load DataFrame not available in excel_inputs"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "calculate_group_loads", "error")
                raise LoadCalculationError(error_msg)

            enhanced_log(log_callback, f"Processing {num_columns} columns for load calculation", 'INFO')

            col_load, col_warnings = _process_elements(
                group_elements['columns'],
                excel_inputs.InputLoadColumn,
                'column',
                log_callback
            )
            total_load += col_load
            warnings.extend(col_warnings)

            enhanced_log(log_callback, f"Column loads subtotal: {col_load:.2f} kN (from {num_columns} columns)", 'INFO')
        else:
            enhanced_log(log_callback, "No columns found in group elements", 'DEBUG')

        # Process walls
        if 'walls' in group_elements and group_elements['walls']:
            if excel_inputs.InputLoadWall is None:
                error_msg = "Wall load DataFrame not available in excel_inputs"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "calculate_group_loads", "error")
                raise LoadCalculationError(error_msg)

            enhanced_log(log_callback, f"Processing {num_walls} walls for load calculation", 'INFO')

            wall_load, wall_warnings = _process_elements(
                group_elements['walls'],
                excel_inputs.InputLoadWall,
                'wall',
                log_callback
            )
            total_load += wall_load
            warnings.extend(wall_warnings)

            enhanced_log(log_callback, f"Wall loads subtotal: {wall_load:.2f} kN (from {num_walls} walls)", 'INFO')
        else:
            enhanced_log(log_callback, "No walls found in group elements", 'DEBUG')

        if total_load <= 0:
            error_msg = "Total calculated load is zero or negative - invalid for structural design"
            enhanced_log(log_callback, f"ERROR: {error_msg} (calculated: {total_load:.2f} kN)", 'ERROR')
            log_function_exit(log_callback, "calculate_group_loads", "error")
            raise LoadCalculationError(error_msg)

        enhanced_log(log_callback, f"Group load calculation completed successfully: {total_load:.2f} kN total load from {num_columns + num_walls} elements", 'INFO')

        final_total_load = max(0.0, total_load)
        log_calculation_result(log_callback, "group_total_load", final_total_load, "kN")
        log_performance_metric(log_callback, "total_elements_processed", num_columns + num_walls, "elements")

        if warnings:
            enhanced_log(log_callback, f"Load calculation completed with {len(warnings)} warnings", 'WARNING')
            for warning in warnings[:5]:  # Log first 5 warnings
                enhanced_log(log_callback, f"Warning: {warning}", 'WARNING')
            if len(warnings) > 5:
                enhanced_log(log_callback, f"... and {len(warnings) - 5} more warnings", 'WARNING')

        log_validation_result(log_callback, "group_load_calculation", final_total_load > 0,
                            f"Calculated {final_total_load:.2f} kN from {num_columns + num_walls} elements")
        log_function_exit(log_callback, "calculate_group_loads", f"{final_total_load:.2f} kN")
        return final_total_load, warnings

    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_group_loads")
        log_function_exit(log_callback, "calculate_group_loads", "error")
        raise


def _find_element_mark_column(df: pd.DataFrame, log_callback=None):
    """Find the element mark column in multi-index DataFrame."""
    log_function_entry(log_callback, "_find_element_mark_column", df_shape=(len(df), len(df.columns)))

    if df.empty:
        enhanced_log(log_callback, "DataFrame is empty, cannot find mark column", 'WARNING')
        log_function_exit(log_callback, "_find_element_mark_column", None)
        return None

    enhanced_log(log_callback, f"Searching for mark column in {len(df.columns)} columns", 'DEBUG')

    for col in df.columns:
        if isinstance(col, tuple) and len(col) >= 2:
            enhanced_log(log_callback, f"Checking column: {col}", 'DEBUG')
            if (col[0] in ['Wall Data', 'Column Data'] and 'Mark' in str(col[1])):
                enhanced_log(log_callback, f"Found mark column: {col}", 'DEBUG')
                log_function_exit(log_callback, "_find_element_mark_column", str(col))
                return col

    enhanced_log(log_callback, "No mark column found in DataFrame", 'WARNING')
    log_function_exit(log_callback, "_find_element_mark_column", None)
    return None


def _find_element_row(df: pd.DataFrame, mark_col, element_name, log_callback=None):
    """Find the row index for the given element name."""
    log_function_entry(log_callback, "_find_element_row",
                      df_shape=(len(df), len(df.columns)),
                      mark_col=str(mark_col),
                      element_name=element_name)

    if df.empty or mark_col is None:
        enhanced_log(log_callback, f"Cannot search: DataFrame empty={df.empty}, mark_col={mark_col}", 'WARNING')
        log_function_exit(log_callback, "_find_element_row", None)
        return None

    enhanced_log(log_callback, f"Searching for element '{element_name}' in column {mark_col}", 'DEBUG')

    element_marks = df[mark_col]
    matching_rows = element_marks == element_name

    if matching_rows.any():
        row_index = matching_rows.idxmax()
        enhanced_log(log_callback, f"Found element '{element_name}' at row index {row_index}", 'DEBUG')
        log_function_exit(log_callback, "_find_element_row", row_index)
        return row_index

    enhanced_log(log_callback, f"Element '{element_name}' not found in DataFrame", 'WARNING')
    log_function_exit(log_callback, "_find_element_row", None)
    return None


def _get_load_value(df: pd.DataFrame, row_index, load_type: str, log_callback=None):
    """Get load value for specific load type."""
    log_function_entry(log_callback, "_get_load_value",
                      df_shape=(len(df), len(df.columns)),
                      row_index=row_index,
                      load_type=load_type)

    if df.empty or row_index is None:
        enhanced_log(log_callback, f"Cannot get load value: DataFrame empty={df.empty}, row_index={row_index}", 'WARNING')
        log_function_exit(log_callback, "_get_load_value", None)
        return None

    enhanced_log(log_callback, f"Getting {load_type} load value from row {row_index}", 'DEBUG')

    element_row = df.loc[row_index]

    # Try standard axial column format
    axial_col = (load_type, 'Axial (kN)')
    enhanced_log(log_callback, f"Trying standard column format: {axial_col}", 'DEBUG')

    if axial_col in df.columns:
        value = element_row[axial_col]
        enhanced_log(log_callback, f"Found value in standard column: {value}", 'DEBUG')
        if pd.notna(value) and value != '':
            try:
                float_value = float(value)
                enhanced_log(log_callback, f"Successfully converted to float: {float_value}", 'DEBUG')
                log_function_exit(log_callback, "_get_load_value", float_value)
                return float_value
            except (ValueError, TypeError) as e:
                enhanced_log(log_callback, f"Failed to convert '{value}' to float: {e}", 'WARNING')

    # Try alternative column formats
    enhanced_log(log_callback, f"Trying alternative column formats for load type '{load_type}'", 'DEBUG')
    for col in df.columns:
        if (isinstance(col, tuple) and len(col) >= 2 and
            str(col[0]).strip() == load_type and 'Axial' in str(col[1])):
            value = element_row[col]
            enhanced_log(log_callback, f"Found value in alternative column {col}: {value}", 'DEBUG')
            if pd.notna(value) and value != '':
                try:
                    float_value = float(value)
                    enhanced_log(log_callback, f"Successfully converted alternative value to float: {float_value}", 'DEBUG')
                    log_function_exit(log_callback, "_get_load_value", float_value)
                    return float_value
                except (ValueError, TypeError) as e:
                    enhanced_log(log_callback, f"Failed to convert alternative value '{value}' to float: {e}", 'WARNING')
                    continue

    enhanced_log(log_callback, f"No valid load value found for load type '{load_type}'", 'WARNING')
    log_function_exit(log_callback, "_get_load_value", None)
    return None


def _process_elements(elements: List, df: pd.DataFrame, element_type: str, log_callback=None) -> Tuple[float, List[str]]:
    """Process a list of elements and calculate total load."""
    log_function_entry(log_callback, "_process_elements",
                      num_elements=len(elements),
                      element_type=element_type,
                      df_shape=(len(df), len(df.columns)))

    with create_timed_logger(log_callback, f"{element_type}_processing") as timer:
        enhanced_log(log_callback, f"Processing {len(elements)} {element_type} elements for load calculation", 'INFO')

        if not elements:
            error_msg = f"No {element_type} elements provided for processing"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_elements", "error")
            raise LoadCalculationError(error_msg)

        if df.empty:
            error_msg = f"{element_type.title()} DataFrame is empty"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_elements", "error")
            raise LoadCalculationError(error_msg)

        enhanced_log(log_callback, f"Starting {element_type} processing: {len(elements)} elements, DataFrame size: {len(df)} rows x {len(df.columns)} columns", 'DEBUG')

        warnings = []
        total_load = 0.0
        processed_count = 0
        failed_elements = []

        for i, element_data in enumerate(elements):
            if not element_data or len(element_data) < 1:
                warning_msg = f"Invalid {element_type} data format at index {i}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            element_name = element_data[0]
            if not element_name or not element_name.strip():
                warning_msg = f"Empty {element_type} name at index {i}"
                warnings.append(warning_msg)
                enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                continue

            try:
                enhanced_log(log_callback, f"Processing {element_type} {i+1}/{len(elements)}: '{element_name}'", 'DEBUG')                
                load, element_warnings = calculate_element_load(df, element_name, log_callback=log_callback)
                total_load += load
                warnings.extend(element_warnings)
                
                # Debug: Log processed_count before increment
                enhanced_log(log_callback, f"DEBUG: Before increment - processed_count={processed_count} (type: {type(processed_count)})", 'DEBUG')
                processed_count += 1

                # Validate processed_count remains an integer after increment
                if not isinstance(processed_count, int):
                    error_msg = f"processed_count became non-integer after increment: {processed_count} (type: {type(processed_count)})"
                    enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                    log_error_with_context(log_callback, LoadCalculationError(error_msg), f"_process_elements {element_type} processed_count type validation")
                    raise LoadCalculationError(error_msg)

                # Debug: Log processed_count after increment
                enhanced_log(log_callback, f"DEBUG: After increment - processed_count={processed_count} (type: {type(processed_count)})", 'DEBUG')

                enhanced_log(log_callback, f"Successfully processed {element_type} '{element_name}': {load:.2f} kN", 'DEBUG')

            except LoadCalculationError as e:
                failed_elements.append(element_name)
                error_msg = f"Failed to calculate load for {element_type} '{element_name}': {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_function_exit(log_callback, "_process_elements", "error")
                raise LoadCalculationError(error_msg)

        if processed_count == 0:
            error_msg = f"No valid {element_type} elements could be processed from {len(elements)} provided"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_function_exit(log_callback, "_process_elements", "error")
            raise LoadCalculationError(error_msg)

        # Ensure processed_count is numeric for division with proper type validation
        try:
            if not isinstance(processed_count, (int, float)):
                enhanced_log(log_callback, f"WARNING: processed_count is not numeric (type: {type(processed_count)}, value: {processed_count}). Converting to int.", 'WARNING')
                processed_count = int(float(str(processed_count))) if processed_count else 0
            else:
                processed_count = int(processed_count)

            # Validate that processed_count is now a proper integer
            if not isinstance(processed_count, int) or processed_count < 0:
                raise ValueError(f"Invalid processed_count after conversion: {processed_count} (type: {type(processed_count)})")

            elements_count = int(len(elements))
            if elements_count <= 0:
                raise ValueError(f"Invalid elements count: {elements_count}")

        except (ValueError, TypeError) as e:
            error_msg = f"Failed to convert processed_count to integer: {processed_count} (type: {type(processed_count)}), error: {e}"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_error_with_context(log_callback, LoadCalculationError(error_msg), f"_process_elements {element_type} processed_count conversion")
            raise LoadCalculationError(error_msg)

        try:
            enhanced_log(log_callback, f"DEBUG: About to calculate {element_type} success_rate. processed_count={processed_count} (type: {type(processed_count)}), elements_count={elements_count} (type: {type(elements_count)})", 'DEBUG')
            success_rate = (processed_count / elements_count) * 100
            enhanced_log(log_callback, f"DEBUG: Successfully calculated {element_type} success_rate={success_rate}", 'DEBUG')
        except (TypeError, ValueError, ZeroDivisionError) as e:
            error_msg = f"Division error in {element_type} success_rate calculation: processed_count={processed_count} (type: {type(processed_count)}), elements_count={elements_count} (type: {type(elements_count)}), error: {e}"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            log_error_with_context(log_callback, LoadCalculationError(error_msg), f"_process_elements {element_type} success_rate calculation")
            raise LoadCalculationError(error_msg)
        
        enhanced_log(log_callback, f"{element_type.title()} processing completed: {processed_count}/{len(elements)} elements processed successfully ({success_rate:.1f}% success rate)", 'INFO')

        # Log performance metrics
        log_performance_metric(log_callback, f"{element_type}_elements_processed", processed_count, "elements")
        log_performance_metric(log_callback, f"{element_type}_success_rate", success_rate, "%")
        log_calculation_result(log_callback, f"{element_type}_total_load", total_load, "kN")

        if failed_elements:
            enhanced_log(log_callback, f"Failed {element_type} elements: {', '.join(failed_elements)}", 'WARNING')
            log_performance_metric(log_callback, f"{element_type}_failed_count", len(failed_elements), "elements")

        log_validation_result(log_callback, f"{element_type}_processing", processed_count > 0,
                            f"Successfully processed {processed_count}/{len(elements)} {element_type} elements")

        log_function_exit(log_callback, "_process_elements", f"{total_load:.2f} kN from {processed_count} elements")
        return total_load, warnings

