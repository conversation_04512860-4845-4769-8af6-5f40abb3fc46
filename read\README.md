# Foundation Automation - Data Reading Package

The read package provides essential data reading functionality for the Foundation Automation system, handling input data from various sources including geology, geometry, loading, property, and steel data.

## Package Structure

The package is organized into several specialized modules:

### Core Modules

1. `__init__.py`
   - Package initialization
   - Exports main reading functions
   - Defines package interface

2. `functions.py`
   - General reading functions
   - Data validation utilities
   - Input processing

### Data Reading Modules

1. `read_geology.py`
   - Geology data reading
   - Implements:
     - `read_input_geology`: Read geology input data
     - Soil property parsing
     - Geotechnical data handling

2. `read_geometry.py`
   - Geometry data reading
   - Implements:
     - `read_input_geometry`: Read geometry input data
     - Foundation geometry parsing
     - Spatial data handling

3. `read_loading.py`
   - Loading data reading
   - Implements:
     - `read_input_loading`: Read loading input data
     - Load combination parsing
     - Force/moment data handling

4. `read_property.py`
   - Property data reading
   - Implements:
     - `read_input_property`: Read property input data
     - Material property parsing
     - Design parameter handling

5. `read_steel.py`
   - Steel data reading
   - Implements:
     - `read_input_steel`: Read steel input data
     - Steel section property parsing
     - Material specification handling

## Key Functions

### Geology Reading
- `read_input_geology`: Read geotechnical input data
  - Input: Excel/CSV files
  - Output: Processed geology data
  - Features: Soil property validation, layer parsing

### Geometry Reading
- `read_input_geometry`: Read geometry input data
  - Input: Foundation dimensions, coordinates
  - Output: Processed geometry data
  - Features: Spatial validation, coordinate transformation

### Loading Reading
- `read_input_loading`: Read loading input data
  - Input: Force/moment data, load combinations
  - Output: Processed loading data
  - Features: Load combination validation, unit conversion

### Property Reading
- `read_input_property`: Read property input data
  - Input: Material properties, design parameters
  - Output: Processed property data
  - Features: Property validation, unit checking

### Steel Reading
- `read_input_steel`: Read steel input data
  - Input: Steel section properties, material specs
  - Output: Processed steel data
  - Features: Section validation, specification checking

## Usage Examples

### Read Geology Data
```python
from read.read_geology import read_input_geology

# Read geology data
geology_data = read_input_geology(
    input_file=input_file,
    sheet_name='Geology'
)
```

### Read Loading Data
```python
from read.read_loading import read_input_loading

# Read loading data
loading_data = read_input_loading(
    input_file=input_file,
    load_combinations=load_combinations
)
```

## Best Practices

1. Verify input data format
2. Use appropriate reading functions for data type
3. Handle errors gracefully
4. Validate data after reading
5. Document data sources
6. Follow data structure conventions

## Error Handling

The package includes error handling for:
- Invalid input files
- Data format errors
- Missing required data
- Unit conversion failures
- Data validation failures

## Integration Points

This package integrates with:
- Foundation Automation core system
- Excel data management
- Data validation
- Unit conversion
- Logging system

## Dependencies

The package relies on external dependencies:
- pandas: Data handling
- numpy: Numerical calculations
- openpyxl: Excel file operations
- logging: System logging

## Version

Current version aligns with Foundation Automation system version V5.3

## Related Documentation

For more detailed documentation on related packages:
- [initialization/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/initialization/README.md:0:0-0:0): System initialization
- [design_fdn/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/design_fdn/README.md:0:0-0:0): Foundation design
- [config/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/config/README.md:0:0-0:0): Configuration management

## Notes

1. All reading functions include comprehensive error handling
2. Data validation is performed during reading
3. Unit conversion is handled automatically
4. Logging is implemented for debugging and tracking
5. Data structure is consistent across modules
