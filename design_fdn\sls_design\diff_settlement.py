"""
Differential Settlement Analysis Module for Foundation Design

This module provides comprehensive differential settlement analysis capabilities for pile 
foundation systems as part of Serviceability Limit State (SLS) design verification. 
It calculates and evaluates differential settlements between foundation elements to 
ensure structural serviceability requirements are met according to foundation design 
standards and codes of practice.

The module performs differential settlement analysis for:
- Vertical Elements (VE): Columns and structural walls
- Pile foundations: Individual pile elements and pile groups
- Distance-based settlement calculations using spatial coordinates
- SLS compliance checking against acceptance criteria (typically 1/500)

Key Features:
- Automated differential settlement matrix calculations
- Integration with SAFE structural analysis results
- Excel-based input/output processing for design workflows
- Comprehensive logging and progress tracking
- Failure identification and reporting for non-compliant elements

Author: Foundation Design Automation Team
Version: 1.0
"""

import numpy as np
import pandas as pd
from scipy.spatial import distance
from typing import Dict, List, Optional, Callable, Tuple, Any, Union
from design_fdn.design_fdn_config import (
    SHEET_VE_DSETT_FAIL, SHEET_PILE_DSETT_FAIL, SHEET_VE_DSETT_CHECK, 
    SHEET_VE_DSETT, SHEET_VE_DS, SHEET_VE_DISTANCE, SHEET_PILE_DSETT_CHECK,
    SHEET_PILE_DSETT, SHEET_PILE_DS, SHEET_PILE_DISTANCE,
    DIFF_SETTLEMENT_MIN_DISTANCE_THRESHOLD,
    DIFF_SETTLEMENT_PERFECT_ALIGNMENT_VALUE
)


def check_differential_settlement(
    file_paths: Any,
    safe_mdbs: Any,
    excel_inputs: Any,
    excel_outputs: Any,
    design_results: Dict[str, Any],
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Perform comprehensive differential settlement analysis for pile foundation system.
    
    This function orchestrates the complete differential settlement analysis workflow,
    including processing of vertical elements (columns/walls) and pile foundations,
    calculating distance-based differential settlements, and generating design check
    reports with SLS compliance verification.
    
    The analysis implements industry-standard differential settlement calculation methods
    considering spatial relationships between foundation elements and applies code-based
    acceptance criteria (typically 1/500 angular distortion limit) for serviceability
    limit state design verification.
    """
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("📐 DIFFERENTIAL SETTLEMENT ANALYSIS - SLS VERIFICATION")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • Analyzing settlement differences between foundation elements")
        log_callback("   • Evaluating angular distortion compliance (SLS limit: 1/500)")
        log_callback("   • Processing vertical elements (columns & walls) and pile foundations")
        log_callback("   • Generating comprehensive serviceability assessment reports")
        log_callback("")
    
    # Create VE point mapping for columns and walls
    # This mapping associates design elements with their corresponding SAFE analysis points
    ve_mappings = _create_ve_mappings(excel_inputs)

    # Extract coordinate data for analysis points at foundation level (Z=0)
    # Filter for ground level points to focus on foundation elements
    df_point_coord = safe_mdbs.PointCoord[safe_mdbs.PointCoord['GlobalZ'] == 0].copy()
    
    # Identify pile points using naming convention (contains '_T' suffix)
    # This follows standard SAFE modeling conventions for pile elements
    df_point_coord_pile = df_point_coord[df_point_coord['Point'].astype(str).str.contains('_T')].copy()

    # Process VE (Vertical Element) points for columns and walls
    # Extract unique SAFE points associated with vertical structural elements
    ve_points = list(ve_mappings['SAFE_Point'].unique())
    df_point_coord_ve = df_point_coord[df_point_coord['Point'].isin(ve_points)].copy()
    df_point_coord_ve = df_point_coord_ve.drop_duplicates(subset=['Point'])

    # Get settlement displacement data from SAFE analysis results
    # This contains nodal displacements for all load cases and points
    df_nodal_displacements = excel_outputs.Settlement.copy()

    # Calculate differential settlement for VE points (columns and walls)
    # This analyzes settlement differences between vertical structural elements
    ve_results = _calculate_differential_settlement(
        df_point_coord_ve, df_nodal_displacements, ve_points, 'VE', log_callback
    )

    # Apply VE mappings to transform SAFE point names to design element names
    # This ensures results are reported using meaningful design nomenclature
    ve_mapping_dict = ve_mappings.set_index('SAFE_Point')['VE_Point'].to_dict()
    for df in ve_results.values():
        if hasattr(df, 'index'):
            # Handle multi-index dataframes (differential settlement matrices)
            if isinstance(df.index, pd.MultiIndex):
                df.index = df.index.set_levels(
                    df.index.levels[1].map(lambda x: ve_mapping_dict.get(x, x)), level=1
                )
            else:
                # Handle single-index dataframes
                df.index = df.index.map(lambda x: ve_mapping_dict.get(x, x))
        if hasattr(df, 'columns'):
            # Update column names to use design element names
            df.columns = df.columns.map(lambda x: ve_mapping_dict.get(x, x))

    # Calculate differential settlement for pile points
    # This analyzes settlement differences between individual pile elements
    pile_points = df_point_coord_pile['Point'].unique().tolist()
    pile_results = _calculate_differential_settlement(
        df_point_coord_pile, df_nodal_displacements, pile_points, 'Pile', log_callback
    )

    # Create SLS compliance check matrices
    # Apply 1/500 differential settlement limit as acceptance criterion
    ve_check = ve_results['diff_settlement'].copy()
    ve_check[ve_check >= 500] = 'OK'  # Mark compliant elements as 'OK'

    pile_check = pile_results['diff_settlement'].copy()
    pile_check[pile_check >= 500] = 'OK'  # Mark compliant elements as 'OK'

    # Create failure identification matrices
    # Isolate non-compliant elements for detailed reporting
    ve_fail = ve_results['diff_settlement'].copy()
    ve_fail[ve_fail >= 500] = None  # Remove compliant elements from failure report

    pile_fail = pile_results['diff_settlement'].copy()
    pile_fail[pile_fail >= 500] = None  # Remove compliant elements from failure report

    # Generate Excel output files with comprehensive analysis results
    # Save failure report for non-compliant elements
    with pd.ExcelWriter(file_paths.ResultDifferentialSettlementFail) as writer:
        ve_fail.to_excel(writer, sheet_name=SHEET_VE_DSETT_FAIL, index=True)
        pile_fail.to_excel(writer, sheet_name=SHEET_PILE_DSETT_FAIL, index=True)

    # Save complete analysis results with all calculation matrices
    with pd.ExcelWriter(file_paths.ResultDifferentialSettlement) as writer:
        # VE analysis results
        ve_check.to_excel(writer, sheet_name=SHEET_VE_DSETT_CHECK, index=True)
        ve_results['diff_settlement'].to_excel(writer, sheet_name=SHEET_VE_DSETT, index=True)
        ve_results['abs_ds'].to_excel(writer, sheet_name=SHEET_VE_DS, index=True)
        ve_results['dist'].to_excel(writer, sheet_name=SHEET_VE_DISTANCE, index=True)
        
        # Pile analysis results
        pile_check.to_excel(writer, sheet_name=SHEET_PILE_DSETT_CHECK, index=True)
        pile_results['diff_settlement'].to_excel(writer, sheet_name=SHEET_PILE_DSETT, index=True)
        pile_results['abs_ds'].to_excel(writer, sheet_name=SHEET_PILE_DS, index=True)
        pile_results['dist'].to_excel(writer, sheet_name=SHEET_PILE_DISTANCE, index=True)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ DIFFERENTIAL SETTLEMENT ANALYSIS - COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • VE (Vertical Elements) differential settlement evaluation completed")
        log_callback("   • Pile foundation differential settlement analysis completed")
        log_callback("   • SLS compliance verification matrices generated")
        log_callback("   • Comprehensive design check reports saved to output files")
        log_callback("")

    # Perform final SLS compliance assessment and reporting
    # Evaluate overall foundation performance against serviceability criteria
    for name, results in [('Column or Wall', ve_results), ('Piles', pile_results)]:
        # Find maximum (most critical) differential settlement
        max_ds = results['diff_settlement'].min().min()
        if log_callback:
            log_callback(f"📊 {name} Analysis Results:")
            log_callback(f"   • Maximum Differential Settlement: 1/{max_ds:.0f}")
            if max_ds < 500:
                log_callback(f"   ❌ FAILED: Differential Settlement > 1/500 (Exceeds SLS Limit)")
                log_callback(f"   • Action Required: Review foundation layout and pile spacing")
            else:
                log_callback(f"   ✅ PASSED: Differential Settlement ≤ 1/500 (Within SLS Limit)")
                log_callback(f"   • Foundation performance meets serviceability requirements")

    return design_results


def _create_ve_mappings(excel_inputs: Any) -> pd.DataFrame:
    """
    Create vertical element (VE) point mappings for differential settlement analysis.
    
    This function establishes the relationship between design elements (columns and walls)
    and their corresponding SAFE analysis points, enabling proper result mapping and
    reporting using meaningful design nomenclature rather than analysis point names.
    """
    ve_data = []

    # Process column element mappings
    # Each column is associated with its center point for settlement analysis
    for _, row in excel_inputs.Column.iterrows():
        ve_data.append({
            'VE_Point': row['Column'],        # Design column identifier
            'SAFE_Point': row['Center Point'] # Corresponding analysis point
        })

    # Process wall element mappings
    # Each wall is associated with its center point for settlement analysis
    for _, row in excel_inputs.Wall.iterrows():
        ve_data.append({
            'VE_Point': row['Wall'],          # Design wall identifier
            'SAFE_Point': row['Center Point'] # Corresponding analysis point
        })

    return pd.DataFrame(ve_data)


def _calculate_differential_settlement(
    df_coords: pd.DataFrame,
    df_displacements: pd.DataFrame,
    points: Union[List[str], pd.Series, np.ndarray],
    element_type: str,
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, pd.DataFrame]:
    """
    Calculate differential settlement matrices for foundation elements.
    
    This function performs the core differential settlement calculations using spatial
    coordinates and displacement data. It implements distance-based differential
    settlement analysis following established geotechnical engineering methods for
    foundation serviceability assessment.
    """
    # Input validation with robust type handling
    # Handle both list and pandas Series inputs for points
    if hasattr(points, 'empty'):  # pandas Series
        if points.empty:
            raise ValueError("Points series cannot be empty")
        points_list = points.tolist()
    elif isinstance(points, (list, tuple, np.ndarray)):
        if len(points) == 0:
            raise ValueError("Points list cannot be empty")
        points_list = list(points)
    else:
        raise TypeError(f"Points must be a list, tuple, array, or pandas Series, got {type(points).__name__}")
    
    if df_coords.empty:
        raise ValueError("Coordinate data cannot be empty")
    if df_displacements.empty:
        raise ValueError("Displacement data cannot be empty")
    
    # Use the validated points list for subsequent operations
    points = points_list
    
    # Filter displacement data for specified points
    # Ensure only relevant points are included in the analysis
    df_disp_filtered = df_displacements[df_displacements['Point'].isin(points)].copy()
    
    # Create categorical ordering to maintain consistent point sequence
    # This ensures reproducible results and proper matrix alignment
    df_disp_filtered['Point'] = pd.Categorical(df_disp_filtered['Point'], categories=points, ordered=True)
    df_disp_filtered = df_disp_filtered.sort_values('Point')

    # Ensure coordinate data follows the same sequence as specified points
    # This is critical for proper matrix operations and result interpretation
    df_coords_ordered = df_coords.set_index('Point').reindex(points).reset_index()
    
    # Create coordinate array in the correct order for distance calculations
    # Extract (x, y) coordinates for each point in the specified sequence
    coordinates = [(row['GlobalX'], row['GlobalY']) for _, row in df_coords_ordered.iterrows()]

    # Calculate Euclidean distance matrix between all point pairs
    # This forms the basis for differential settlement normalization
    dist_matrix = distance.cdist(coordinates, coordinates, 'euclidean')
    df_dist = pd.DataFrame(dist_matrix, index=points, columns=points)

    # Extract unique load cases from displacement data
    # Each load case requires separate differential settlement analysis
    sls_cases = df_disp_filtered['OutputCase'].unique().tolist()
    sls_cases = sorted(sls_cases)  # Ensure load cases are processed in a consistent order

    # Initialize settlement matrix for all load cases
    # Organize settlement data by point and load case for efficient processing
    df_settlement = pd.DataFrame(index=points, columns=sls_cases)
    
    # Process settlement data for each load case
    for sls in sls_cases:
        # Extract settlements for current load case
        mask = df_disp_filtered['OutputCase'] == sls
        sls_data = df_disp_filtered[mask].set_index('Point')['Uz (mm)'] / 1000  # Convert to meters
        
        # Ensure settlements are ordered according to points sequence
        # This maintains consistency with coordinate and distance matrices
        ordered_settlements = sls_data.reindex(points)
        
        df_settlement[sls] = ordered_settlements

    # Initialize differential settlement calculation variables
    diff_settlement_list = []
    abs_ds_list = []
    dist_list = []

    # Progress tracking for long calculations
    total_cases = len(sls_cases)

    # Calculate differential settlement for each load case
    for i, sls in enumerate(sls_cases):
        # Extract settlement values in correct point order
        settlement_values = df_settlement[sls].values
        # Handle potential NaN values in settlement data
        settlement_values_clean = np.where(np.isnan(settlement_values), 0.0, settlement_values)
        # Create settlement difference matrices using broadcasting
        # s1: settlement matrix with rows = point i, columns = point j
        # s2: transposed settlement matrix for pairwise differences
        s1 = pd.DataFrame(np.tile(settlement_values_clean.reshape(-1, 1), (1, len(points))),
                         index=points, columns=points)
        s2 = s1.T

        # Calculate absolute differential settlement matrix
        # |Settlement_i - Settlement_j| for all point pairs
        abs_ds = np.abs(s1 - s2)
        abs_ds = pd.DataFrame(abs_ds, index=points, columns=points)

        # Store abs_ds for this load case (copy before modifying)
        abs_ds_for_storage = abs_ds.copy()
        # Add load case identifier for multi-case compilation
        abs_ds_for_storage.insert(0, 'LOAD CASE', sls)
        abs_ds_list.append(abs_ds_for_storage)

        # Store distance matrix for this load case (copy before modifying)
        dist_for_storage = df_dist.copy()
        # Add load case identifier for multi-case compilation
        dist_for_storage.insert(0, 'LOAD CASE', sls)
        dist_list.append(dist_for_storage)

        # Initialize result matrix with NaN
        diff_settlement = pd.DataFrame(np.full_like(abs_ds, np.nan), 
                              index=abs_ds.index, columns=abs_ds.columns)
        
        # Calculate angular distortion only for valid cases
        # Use configurable threshold for numerical stability with very small distances
        min_distance = DIFF_SETTLEMENT_MIN_DISTANCE_THRESHOLD
        
        # Apply minimum distance threshold for numerical stability
        df_dist = df_dist.where(df_dist >= min_distance, min_distance)
        
        # Calculate for valid cases: 1 / (settlement_diff / distance)
        valid_mask = (df_dist >= min_distance) & (abs_ds > 0)
        diff_settlement = diff_settlement.where(~valid_mask, 1 / (abs_ds / df_dist))
        
        # Handle special cases
        # Case: Zero settlement difference with non-zero distance = perfect alignment
        # Use a very large finite number instead of infinity for downstream compatibility
        perfect_alignment_value = DIFF_SETTLEMENT_PERFECT_ALIGNMENT_VALUE
        perfect_alignment_mask = (df_dist >= min_distance) & (abs_ds == 0.0)
        diff_settlement = diff_settlement.where(~perfect_alignment_mask, perfect_alignment_value)
        
        # Apply upper triangular mask to avoid result duplication
        # Only lower triangular elements are meaningful (symmetric matrix)
        mask = np.triu(np.ones(diff_settlement.shape, dtype=bool))
        diff_settlement = diff_settlement.mask(mask, None)
        
        # Add load case identifier for multi-case compilation
        diff_settlement.insert(0, 'LOAD CASE', sls)
        diff_settlement_list.append(diff_settlement)

        # Progress reporting for long calculations
        if log_callback and (i % 5 == 0 or i == total_cases - 1):
            progress = int((i + 1) / total_cases * 100)

    # Combine results from all load cases into multi-index dataframe
    # Create comprehensive differential settlement matrix with load case indexing
    df_diff_settlement_output = pd.concat(diff_settlement_list).reset_index()
    df_diff_settlement_output.set_index(['LOAD CASE', 'index'], inplace=True)
    
    # Combine absolute differential settlement results from all load cases
    # Create comprehensive absolute settlement difference matrix with load case indexing
    df_abs_ds_output = pd.concat(abs_ds_list).reset_index()
    df_abs_ds_output.set_index(['LOAD CASE', 'index'], inplace=True)
    
    # Combine distance matrix results from all load cases
    # Create comprehensive distance matrix with load case indexing
    df_dist_output = pd.concat(dist_list).reset_index()
    df_dist_output.set_index(['LOAD CASE', 'index'], inplace=True)
    
    # Final check: Ensure no infinity values remain in the results
    numeric_cols = df_diff_settlement_output.select_dtypes(include=[np.number]).columns
    inf_check = np.isinf(df_diff_settlement_output[numeric_cols]).any().any()
    if inf_check and log_callback:
        log_callback("⚠️  WARNING: Infinity values detected in final results - this should not happen")
        # Replace any remaining infinity values as a safety measure
        df_diff_settlement_output = df_diff_settlement_output.replace([np.inf, -np.inf], DIFF_SETTLEMENT_PERFECT_ALIGNMENT_VALUE)
    
    return {
        'diff_settlement': df_diff_settlement_output,
        'abs_ds': df_abs_ds_output,
        'dist': df_dist_output
    }
