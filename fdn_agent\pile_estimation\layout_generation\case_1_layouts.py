﻿"""
Case 1 Pile Layouts: Single Column + Single Sub-Load Cluster

Rules (Updated according to Pile_Layout_Rules.md):
- All layouts follow pile center = load center
- 1 pile: place under load center
- 2 piles: place along Major Local Axis at ±1.5 pile diameter from load center
- 3-8 piles: regular polygon layout around load center
- 9+ piles: symmetric grid layout excluding center position
- **BP Special Rule**: Always 1 pile at load center (maximum 1 pile constraint)

Enhanced Features:
- Proper local axis alignment for pile positioning
- Improved grid generation with optimal spacing
- Better integration with pile type pre-selection
- Enhanced BP constraint enforcement
"""

from math import ceil, sqrt, pi, cos, sin
from typing import List, Optional, Dict, Any, Callable
from ..data_types import Point2D
from ..pile_cap_geometry import LocalCoordinateSystem
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    create_timed_logger
)
from .layout_common import (
    calculate_load_center,
    calculate_polygon_radius,
    generate_polygon_positions,
    get_axis_directions,
    generate_line_positions,
    generate_symmetric_positions
)


def handle_case_1(cluster_data: dict, required_piles: int, min_spacing: float,
                  initial_local_system: Optional[LocalCoordinateSystem],
                  log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Main coordinator for Case 1 pile layouts.
    Determines the appropriate pile layout based on the number of required piles.

    Implements Rules 1.4 from Pile_Layout_Rules.md:
    - 1 pile: place under load center
    - 2 piles: place along Major Local Axis at ±1.5 pile diameter from load center
    - 3-8 piles: regular polygon layout around load center
    - 9+ piles: symmetric grid layout excluding center position

    Args:
        cluster_data (dict): Dictionary containing information about the load cluster,
                             including load points and column elements.
        required_piles (int): The number of piles required for the foundation.
        min_spacing (float): The minimum allowable spacing between piles.
        initial_local_system (Optional[LocalCoordinateSystem]): An optional local
                                                                coordinate system
                                                                to determine orientation.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: A list of 2D points representing the calculated pile positions.
    """
    log_function_entry(log_callback, "handle_case_1",
                      required_piles=required_piles, min_spacing=min_spacing)

    try:
        # Create performance timer for layout generation
        with create_timed_logger(log_callback, f"Case_1_layout_generation_{required_piles}_piles"):
            # Calculate load center using common function
            enhanced_log(log_callback, "Calculating load center for Case 1 layout", 'INFO')
            load_center = calculate_load_center(cluster_data)
            enhanced_log(log_callback, f"Load center calculated: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')

            # Determine layout strategy based on pile count
            enhanced_log(log_callback, f"Determining layout strategy for {required_piles} piles", 'INFO')

            # Generate layout based on pile count
            if required_piles == 1:
                enhanced_log(log_callback, "Using single pile layout at load center", 'INFO')
                result = [load_center]
                enhanced_log(log_callback, f"Single pile positioned at: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'DEBUG')
            elif required_piles == 2:
                enhanced_log(log_callback, "Using two-pile layout along major axis", 'INFO')
                result = _two_piles_layout(load_center, min_spacing, initial_local_system, log_callback)
            elif 3 <= required_piles <= 8:
                enhanced_log(log_callback, f"Using polygon layout for {required_piles} piles", 'INFO')
                result = _polygon_layout(load_center, required_piles, min_spacing, log_callback)
            else:  # 9+ piles
                enhanced_log(log_callback, f"Using grid layout for {required_piles} piles", 'INFO')
                result = _grid_layout(load_center, required_piles, min_spacing, initial_local_system, log_callback)

            # Validate result
            if result:
                enhanced_log(log_callback, f"Case 1 layout generated successfully: {len(result)} pile positions", 'INFO')
                log_validation_result(log_callback, "pile_count_match", len(result) == required_piles,
                                    f"Expected {required_piles}, got {len(result)}")
            else:
                enhanced_log(log_callback, "Case 1 layout generation failed: no positions generated", 'ERROR')

            log_function_exit(log_callback, "handle_case_1", f"{len(result)} positions")
            return result

    except Exception as e:
        enhanced_log(log_callback, f"Error in Case 1 layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_1", "error")
        raise


def handle_case_1_with_preselection(cluster_data: dict, 
                                   preselection_result: Any,
                                   min_spacing: float,
                                   initial_local_system: Optional[LocalCoordinateSystem],
                                   log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Handle Case 1 pile layout using pre-selected pile type and positions.
    This function uses the deterministic pile positions from pre-selection 
    to ensure consistency between pre-selection and final layout.
    
    Enhanced to better handle BP constraints and grid optimization.

    Args:
        cluster_data (dict): Dictionary containing information about the load cluster.
        preselection_result: Pre-selection result containing selected pile type and positions.
        min_spacing (float): The minimum allowable spacing between piles (from selected pile type).
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: A list of 2D points representing the final pile positions.
    """
    log_function_entry(log_callback, "handle_case_1_with_preselection", 
                      min_spacing=min_spacing, has_preselection=preselection_result is not None)
    
    try:
        # Check if preselection result has viable positions
        if hasattr(preselection_result, 'viable_grid_positions') and preselection_result.viable_grid_positions:
            # Use the pre-selected pile positions directly for consistency
            enhanced_log(log_callback, f"Using pre-selected viable positions: {len(preselection_result.viable_grid_positions)} piles", 'INFO')
            result = preselection_result.viable_grid_positions
            log_function_exit(log_callback, "handle_case_1_with_preselection", f"{len(result)} pre-selected positions")
            return result
            
        elif hasattr(preselection_result, 'selected_pile_type'):
            # If no viable positions but we have a selected pile type, generate layout
            pile_type = preselection_result.selected_pile_type
            enhanced_log(log_callback, f"No viable positions found, generating layout for selected pile type", 'INFO')
            
            # Special handling for BP types - enforce 1 pile maximum
            if hasattr(pile_type, 'pile_type') and pile_type.pile_type.name == 'BP':
                enhanced_log(log_callback, "BP type detected - applying 1 pile constraint", 'INFO')
                load_center = calculate_load_center(cluster_data)
                result = [load_center]  # BP always uses 1 pile at load center
                log_function_exit(log_callback, "handle_case_1_with_preselection", "1 BP pile at center")
                return result
            
            # For other pile types, calculate required piles
            if hasattr(pile_type, 'capacity_per_pile'):
                total_load = sum(point[2] for point in cluster_data.get('load_points', []) if len(point) >= 3)
                enhanced_log(log_callback, f"Calculating required piles for total load: {total_load:.1f} kN", 'DEBUG')
                if total_load > 0:
                    required_piles = max(1, int(ceil(total_load / pile_type.capacity_per_pile)))
                    enhanced_log(log_callback, f"Required piles: {required_piles} (capacity per pile: {pile_type.capacity_per_pile:.1f} kN)", 'INFO')
                    result = handle_case_1(cluster_data, required_piles, min_spacing, initial_local_system, log_callback)
                    log_function_exit(log_callback, "handle_case_1_with_preselection", f"{len(result)} calculated positions")
                    return result
        
        # No preselection data available
        enhanced_log(log_callback, "No preselection data available - using default single pile layout", 'WARNING')
        required_piles = 1
        result = handle_case_1(cluster_data, required_piles, min_spacing, initial_local_system, log_callback)
        log_function_exit(log_callback, "handle_case_1_with_preselection", f"{len(result)} default positions")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in preselection layout handling: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_1_with_preselection", "error")
        raise


def handle_case_1_bp_layout(cluster_data: dict, 
                           pile_capacity: float,
                           min_spacing: float,
                           initial_local_system: Optional[LocalCoordinateSystem],
                           log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Special Case 1 layout handler for BP (Bored Pile) types.
    
    Implements BP Special Rule: Always 1 pile at load center (maximum 1 pile constraint).
    
    Args:
        cluster_data (dict): Dictionary containing information about the load cluster.
        pile_capacity (float): Capacity of the BP pile type.
        min_spacing (float): Minimum spacing (not used for single pile).
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        log_callback: Optional logging callback function.
        
    Returns:
        List[Point2D]: Single pile position at load center.
    """
    log_function_entry(log_callback, "handle_case_1_bp_layout", pile_capacity=pile_capacity)
    
    try:
        enhanced_log(log_callback, "Applying BP special rule: 1 pile at load center", 'INFO')
        load_center = calculate_load_center(cluster_data)
        enhanced_log(log_callback, f"BP pile positioned at load center: ({load_center[0]:.3f}, {load_center[1]:.3f})", 'INFO')
        
        # Validate pile capacity if load data is available
        if 'load_points' in cluster_data:
            total_load = sum(point[2] for point in cluster_data.get('load_points', []) if len(point) >= 3)
            if total_load > 0:
                utilization = total_load / pile_capacity
                log_validation_result(log_callback, "bp_capacity_check", pile_capacity >= total_load,
                                    f"BP capacity: {pile_capacity:.1f} kN vs load: {total_load:.1f} kN (utilization: {utilization:.1%})")
        
        result = [load_center]
        log_function_exit(log_callback, "handle_case_1_bp_layout", "1 pile at center")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in BP layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "handle_case_1_bp_layout", "error")
        raise


def _two_piles_layout(load_center: Point2D, min_spacing: float,
                      initial_local_system: Optional[LocalCoordinateSystem],
                      log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generates a layout for two piles using enhanced positioning rules.

    Implements Rule 1.4: Place piles along Major Local Axis at ±1.5 pile diameter from load center.
    This is enhanced from the original ±min_spacing to ±1.5*pile_diameter for better load distribution.

    Args:
        load_center (Point2D): The center point for pile placement.
        min_spacing (float): Minimum spacing between piles.
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system for orientation.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: Two pile positions along the major axis.
    """
    log_function_entry(log_callback, "_two_piles_layout", min_spacing=min_spacing)

    try:
        # Get axis directions from common function
        enhanced_log(log_callback, "Getting axis directions for two-pile layout", 'DEBUG')
        x_axis_direction, _ = get_axis_directions(initial_local_system)
        enhanced_log(log_callback, f"Major axis direction: ({x_axis_direction[0]:.3f}, {x_axis_direction[1]:.3f})", 'DEBUG')

        # Enhanced spacing calculation: use 1.5 * pile diameter for better load distribution
        # Estimate pile diameter from min_spacing (typically min_spacing = 3 * pile_diameter)
        estimated_pile_diameter = min_spacing / 3.0
        pile_offset = 1.5 * estimated_pile_diameter
        enhanced_log(log_callback, f"Estimated pile diameter: {estimated_pile_diameter:.3f}m, initial offset: {pile_offset:.3f}m", 'DEBUG')

        # Ensure minimum spacing requirement is met
        actual_spacing = max(pile_offset * 2, min_spacing)
        actual_offset = actual_spacing / 2
        enhanced_log(log_callback, f"Final spacing: {actual_spacing:.3f}m, offset from center: {actual_offset:.3f}m", 'DEBUG')

        # Generate positions along major axis
        pos1 = (
            load_center[0] - actual_offset * x_axis_direction[0],
            load_center[1] - actual_offset * x_axis_direction[1]
        )
        pos2 = (
            load_center[0] + actual_offset * x_axis_direction[0],
            load_center[1] + actual_offset * x_axis_direction[1]
        )

        enhanced_log(log_callback, f"Two-pile positions: P1({pos1[0]:.3f}, {pos1[1]:.3f}), P2({pos2[0]:.3f}, {pos2[1]:.3f})", 'INFO')

        result = [pos1, pos2]
        log_validation_result(log_callback, "two_pile_spacing", actual_spacing >= min_spacing,
                            f"Spacing: {actual_spacing:.3f}m >= {min_spacing:.3f}m")

        log_function_exit(log_callback, "_two_piles_layout", f"2 positions")
        return result

    except Exception as e:
        enhanced_log(log_callback, f"Error in two-pile layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_two_piles_layout", "error")
        raise


def _polygon_layout(load_center: Point2D, num_piles: int, min_spacing: float,
                   log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generates a regular polygon layout for 3 to 8 piles using common functions.

    Implements Rule 1.4: Use regular polygon pile layout for 3-8 piles.
    """
    log_function_entry(log_callback, "_polygon_layout", num_piles=num_piles, min_spacing=min_spacing)

    try:
        enhanced_log(log_callback, f"Calculating polygon radius for {num_piles} piles", 'DEBUG')
        radius = calculate_polygon_radius(num_piles, min_spacing)
        enhanced_log(log_callback, f"Polygon radius: {radius:.3f}m", 'DEBUG')

        enhanced_log(log_callback, f"Generating {num_piles} polygon positions around load center", 'DEBUG')
        result = generate_polygon_positions(load_center, num_piles, radius)

        enhanced_log(log_callback, f"Polygon layout generated: {len(result)} positions", 'INFO')
        for i, pos in enumerate(result[:5]):  # Log first 5 positions for debugging
            enhanced_log(log_callback, f"  Polygon position {i+1}: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
        if len(result) > 5:
            enhanced_log(log_callback, f"  ... and {len(result)-5} more positions", 'DEBUG')
        log_validation_result(log_callback, "polygon_pile_count", len(result) == num_piles,
                            f"Expected {num_piles}, got {len(result)}")

        log_function_exit(log_callback, "_polygon_layout", f"{len(result)} positions")
        return result

    except Exception as e:
        enhanced_log(log_callback, f"Error in polygon layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_polygon_layout", "error")
        raise


def _grid_layout(load_center: Point2D, num_piles: int, min_spacing: float,
                 initial_local_system: Optional[LocalCoordinateSystem],
                 log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generates a symmetric grid layout for 9 or more piles.

    Implements Rule 1.4: Use symmetric grid layout excluding center position.
    Enhanced with better symmetry and spacing optimization.

    Args:
        load_center (Point2D): The center point for pile placement.
        num_piles (int): Number of piles to place.
        min_spacing (float): Minimum spacing between piles.
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system for orientation.
        log_callback: Optional logging callback function.

    Returns:
        List[Point2D]: Grid positions excluding center, optimized for symmetry.
    """
    log_function_entry(log_callback, "_grid_layout", num_piles=num_piles, min_spacing=min_spacing)

    try:
        # Get axis directions using common function
        enhanced_log(log_callback, "Getting axis directions for grid layout", 'DEBUG')
        x_axis_direction, y_axis_direction = get_axis_directions(initial_local_system)
        enhanced_log(log_callback, f"Grid axes - X: ({x_axis_direction[0]:.3f}, {x_axis_direction[1]:.3f}), "
                                  f"Y: ({y_axis_direction[0]:.3f}, {y_axis_direction[1]:.3f})", 'DEBUG')

        # Generate symmetric grid positions without center pile
        enhanced_log(log_callback, f"Generating optimized symmetric grid for {num_piles} piles", 'INFO')
        grid_positions = _generate_optimized_symmetric_grid_positions(num_piles, min_spacing, log_callback)
        enhanced_log(log_callback, f"Generated {len(grid_positions)} local grid positions", 'DEBUG')

        # Transform to global coordinates using local coordinate system
        enhanced_log(log_callback, "Transforming local grid positions to global coordinates", 'DEBUG')
        positions = []
        for i, (local_x, local_y) in enumerate(grid_positions):
            global_x = (load_center[0] +
                       local_x * x_axis_direction[0] +
                       local_y * y_axis_direction[0])
            global_y = (load_center[1] +
                       local_x * x_axis_direction[1] +
                       local_y * y_axis_direction[1])
            positions.append((global_x, global_y))

            if i < 5:  # Log first few positions for debugging
                enhanced_log(log_callback, f"Position {i+1}: local({local_x:.3f}, {local_y:.3f}) -> "
                                          f"global({global_x:.3f}, {global_y:.3f})", 'DEBUG')

        enhanced_log(log_callback, f"Grid layout generated: {len(positions)} positions", 'INFO')
        
        # Validate minimum spacing between adjacent piles
        spacing_violations = 0
        for i in range(len(positions)):
            for j in range(i+1, len(positions)):
                distance = sqrt((positions[i][0] - positions[j][0])**2 + (positions[i][1] - positions[j][1])**2)
                if distance < min_spacing - 0.001:  # Small tolerance for floating point
                    spacing_violations += 1
                    enhanced_log(log_callback, f"Spacing violation between piles {i+1} and {j+1}: {distance:.3f}m < {min_spacing:.3f}m", 'WARNING')
        
        log_validation_result(log_callback, "grid_pile_count", len(positions) <= num_piles,
                            f"Generated {len(positions)} positions for {num_piles} requested")
        log_validation_result(log_callback, "grid_spacing_check", spacing_violations == 0,
                            f"Found {spacing_violations} spacing violations")

        log_function_exit(log_callback, "_grid_layout", f"{len(positions)} positions")
        return positions

    except Exception as e:
        enhanced_log(log_callback, f"Error in grid layout generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_grid_layout", "error")
        raise


def _generate_optimized_symmetric_grid_positions(num_piles: int, min_spacing: float,
                                               log_callback: Optional[Callable] = None) -> List[tuple]:
    """
    Generates optimized symmetric grid positions without a center pile.

    Enhanced algorithm that:
    1. Creates a larger grid to ensure enough positions (Rule 1.2 compliance)
    2. Grid is centered at (0,0) representing load center with proper spacing
    3. Sorts positions by distance from center
    4. Selects positions maintaining perfect symmetry
    5. Prioritizes positions closer to center for better load distribution
    6. Excludes center position for final layout as per Rule 1.4

    Args:
        num_piles (int): The total number of piles to generate positions for.
        min_spacing (float): The minimum spacing between grid points.
        log_callback: Optional logging callback function.

    Returns:
        List[tuple]: A list of (x, y) tuples representing the symmetric grid positions.
    """
    log_function_entry(log_callback, "_generate_optimized_symmetric_grid_positions",
                      num_piles=num_piles, min_spacing=min_spacing)

    try:
        # Create a generous grid to ensure we have enough symmetric positions
        max_grid_extent = max(4, int(ceil(sqrt(num_piles / 0.6))))  # More conservative estimate
        enhanced_log(log_callback, f"Grid extent: ±{max_grid_extent} (total grid: {(2*max_grid_extent+1)**2} positions)", 'DEBUG')

        # Generate all grid positions with proper grid centering at (0,0)
        # This ensures the grid has one intersection at load center as per Rule 1.2
        enhanced_log(log_callback, "Generating all possible grid positions", 'DEBUG')
        all_positions = []
        for i in range(-max_grid_extent, max_grid_extent + 1):
            for j in range(-max_grid_extent, max_grid_extent + 1):
                if i == 0 and j == 0:  # Skip center position for final layout as per Rule 1.4
                    continue
                x = i * min_spacing
                y = j * min_spacing
                distance = sqrt(x*x + y*y)
                all_positions.append((x, y, distance))

        enhanced_log(log_callback, f"Generated {len(all_positions)} candidate positions (excluding center)", 'DEBUG')

        # Sort by distance from center for optimal selection
        all_positions.sort(key=lambda pos: pos[2])
        enhanced_log(log_callback, f"Sorted positions by distance from center", 'DEBUG')

        # Select positions maintaining perfect symmetry
        enhanced_log(log_callback, "Selecting symmetric positions", 'DEBUG')
        selected_positions = []
        used_positions = set()

        for x, y, dist in all_positions:
            if len(selected_positions) >= num_piles:
                break

            if (x, y) in used_positions:
                continue

            # Find complete symmetric group for this position
            symmetric_group = _get_complete_symmetric_group(x, y, min_spacing, log_callback)

            # Filter out positions already used
            available_in_group = [pos for pos in symmetric_group if pos not in used_positions]

            # Add available positions from this symmetric group
            positions_to_add = available_in_group[:min(len(available_in_group),
                                                     num_piles - len(selected_positions))]

            if positions_to_add:
                enhanced_log(log_callback, f"Adding {len(positions_to_add)} symmetric positions at distance {dist:.3f}m", 'DEBUG')

            for pos in positions_to_add:
                selected_positions.append(pos)
                used_positions.add(pos)

        enhanced_log(log_callback, f"Selected {len(selected_positions)} symmetric grid positions", 'INFO')
        log_validation_result(log_callback, "symmetric_grid_generation", len(selected_positions) > 0,
                            f"Generated {len(selected_positions)} positions")

        log_function_exit(log_callback, "_generate_optimized_symmetric_grid_positions", f"{len(selected_positions)} positions")
        return selected_positions

    except Exception as e:
        enhanced_log(log_callback, f"Error in symmetric grid generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_generate_optimized_symmetric_grid_positions", "error")
        raise


def _get_complete_symmetric_group(x: float, y: float, min_spacing: float,
                                 log_callback: Optional[Callable] = None) -> List[tuple]:
    """
    Calculates the complete symmetric group for a given (x, y) coordinate.
    
    Enhanced to ensure perfect symmetry by including all reflections:
    - Original point
    - Reflection across x-axis
    - Reflection across y-axis  
    - Reflection across origin (both axes)
    
    Args:
        x (float): The x-coordinate of the point.
        y (float): The y-coordinate of the point.
        min_spacing (float): The minimum spacing (for consistency).
        log_callback: Optional logging callback function.

    Returns:
        List[tuple]: A list of (x, y) tuples representing all symmetric positions.
    """
    log_function_entry(log_callback, "_get_complete_symmetric_group", x=x, y=y)
    
    try:
        positions = []
        
        # The original position
        positions.append((x, y))
        enhanced_log(log_callback, f"Original position: ({x:.3f}, {y:.3f})", 'DEBUG')
        
        # Add symmetric counterparts if they're different
        if x != 0:
            positions.append((-x, y))
            enhanced_log(log_callback, f"X-reflection: ({-x:.3f}, {y:.3f})", 'DEBUG')
        if y != 0:
            positions.append((x, -y))
            enhanced_log(log_callback, f"Y-reflection: ({x:.3f}, {-y:.3f})", 'DEBUG')
        if x != 0 and y != 0:
            positions.append((-x, -y))
            enhanced_log(log_callback, f"Origin-reflection: ({-x:.3f}, {-y:.3f})", 'DEBUG')
        
        # Remove duplicates while preserving order
        unique_positions = []
        seen = set()
        for pos in positions:
            if pos not in seen:
                unique_positions.append(pos)
                seen.add(pos)
        
        enhanced_log(log_callback, f"Symmetric group generated: {len(unique_positions)} unique positions", 'DEBUG')
        log_function_exit(log_callback, "_get_complete_symmetric_group", f"{len(unique_positions)} positions")
        return unique_positions
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in symmetric group generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "_get_complete_symmetric_group", "error")
        raise


def generate_case_1_possible_pile_grid(load_center: Point2D, min_spacing: float,
                                     initial_local_system: Optional[LocalCoordinateSystem],
                                     max_pile_cap_boundary: Optional[List[Point2D]] = None,
                                     log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate possible pile grid positions for DXF visualization according to Rule 1.2.
    
    This function generates the complete grid including the load center position,
    which is used for pre-selection visualization in DXF files.
    Different from final layout selection which may exclude center position.
    
    Implements Rule 1.2:
    - Grid centered at Load Center with one grid intersection at load center
    - Load Center MUST be included as one of the Possible Pile Locations
    - Grid spacing equals minimum spacing of pile type
    - Grid size: 2 rows in major/minor axis outside optimal rectangle
    
    Args:
        load_center (Point2D): The load center coordinates.
        min_spacing (float): Minimum spacing for grid generation.
        initial_local_system (Optional[LocalCoordinateSystem]): Local coordinate system.
        max_pile_cap_boundary (Optional[List[Point2D]]): Maximum pile cap boundary for filtering.
        log_callback: Optional logging callback function.
        
    Returns:
        List[Point2D]: All possible pile grid positions including load center.
    """
    log_function_entry(log_callback, "generate_case_1_possible_pile_grid", 
                      min_spacing=min_spacing, boundary_provided=max_pile_cap_boundary is not None)
    
    try:
        # Get axis directions using common function
        enhanced_log(log_callback, "Getting axis directions for pile grid generation", 'DEBUG')
        x_axis_direction, y_axis_direction = get_axis_directions(initial_local_system)
        enhanced_log(log_callback, f"Grid axes - X: ({x_axis_direction[0]:.3f}, {x_axis_direction[1]:.3f}), "
                                  f"Y: ({y_axis_direction[0]:.3f}, {y_axis_direction[1]:.3f})", 'DEBUG')
        
        # Generate generous grid size as per Rule 1.2 (2 rows outside optimal rectangle)
        max_grid_extent = 6  # Generous grid for pre-selection visualization
        enhanced_log(log_callback, f"Grid extent: ±{max_grid_extent} (total grid: {(2*max_grid_extent+1)**2} positions)", 'INFO')
        
        # Generate all grid positions INCLUDING center (0,0) for Rule 1.2 compliance
        enhanced_log(log_callback, "Generating all possible grid positions (including load center)", 'DEBUG')
        all_positions = []
        boundary_filtered_count = 0
        
        for i in range(-max_grid_extent, max_grid_extent + 1):
            for j in range(-max_grid_extent, max_grid_extent + 1):
                # Include ALL positions including center for possible pile locations
                local_x = i * min_spacing
                local_y = j * min_spacing
                
                # Transform to global coordinates using local coordinate system
                global_x = (load_center[0] + 
                           local_x * x_axis_direction[0] + 
                           local_y * y_axis_direction[0])
                global_y = (load_center[1] + 
                           local_x * x_axis_direction[1] + 
                           local_y * y_axis_direction[1])
                
                position = (global_x, global_y)
                
                # Filter by maximum pile cap boundary if provided
                if max_pile_cap_boundary is None:
                    all_positions.append(position)
                else:
                    # Check if position is within maximum pile cap boundary
                    if _point_in_polygon(position, max_pile_cap_boundary, log_callback):
                        all_positions.append(position)
                    else:
                        boundary_filtered_count += 1
        
        enhanced_log(log_callback, f"Grid generation complete: {len(all_positions)} positions", 'INFO')
        if boundary_filtered_count > 0:
            enhanced_log(log_callback, f"Filtered {boundary_filtered_count} positions outside pile cap boundary", 'DEBUG')
        
        # Validate that load center is included
        load_center_included = any(
            abs(pos[0] - load_center[0]) < 0.001 and abs(pos[1] - load_center[1]) < 0.001 
            for pos in all_positions
        )
        log_validation_result(log_callback, "load_center_in_grid", load_center_included,
                            "Load center must be included in possible pile locations (Rule 1.2)")
        
        log_function_exit(log_callback, "generate_case_1_possible_pile_grid", f"{len(all_positions)} positions")
        return all_positions
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in pile grid generation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "generate_case_1_possible_pile_grid", "error")
        raise


def _point_in_polygon(point: Point2D, polygon: List[Point2D], 
                     log_callback: Optional[Callable] = None) -> bool:
    """
    Check if a point is inside a polygon using ray casting algorithm.
    
    Args:
        point (Point2D): Point to check.
        polygon (List[Point2D]): Polygon vertices.
        log_callback: Optional logging callback function.
        
    Returns:
        bool: True if point is inside polygon.
    """
    if not polygon or len(polygon) < 3:
        enhanced_log(log_callback, "No boundary constraint provided - allowing all positions", 'DEBUG')
        return True  # No boundary constraint
    
    try:
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in point-in-polygon check: {str(e)}", 'WARNING')
        return True  # Allow position if check fails


def should_show_preselection_in_dxf(pile_type_name: str, required_piles: int,
                                   log_callback: Optional[Callable] = None) -> bool:
    """
    Determine if pre-selection visualization should be shown in DXF for Case 1.
    
    Implements Rule 1.4 enhancement:
    Only DHP & SHP with More than 8 piles need to show pre-selection in DXF,
    as only these cases use possible pile location grid generation.
    
    Args:
        pile_type_name (str): Name of pile type ('DHP', 'SHP', 'BP').
        required_piles (int): Number of required piles.
        log_callback: Optional logging callback function.
        
    Returns:
        bool: True if pre-selection should be shown in DXF.
    """
    log_function_entry(log_callback, "should_show_preselection_in_dxf", 
                      pile_type=pile_type_name, required_piles=required_piles)
    
    try:
        # Apply Rule 1.4 enhancement logic
        show_preselection = pile_type_name in ['DHP', 'SHP'] and required_piles > 8
        
        enhanced_log(log_callback, f"Pre-selection DXF visualization check:", 'DEBUG')
        enhanced_log(log_callback, f"  - Pile type: {pile_type_name}", 'DEBUG')
        enhanced_log(log_callback, f"  - Required piles: {required_piles}", 'DEBUG')
        enhanced_log(log_callback, f"  - Show in DXF: {show_preselection}", 'DEBUG')
        
        if show_preselection:
            enhanced_log(log_callback, f"Pre-selection visualization required for {pile_type_name} with {required_piles} piles", 'INFO')
        else:
            enhanced_log(log_callback, f"Pre-selection visualization not required for {pile_type_name} with {required_piles} piles", 'INFO')
        
        log_function_exit(log_callback, "should_show_preselection_in_dxf", show_preselection)
        return show_preselection
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in preselection DXF check: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "should_show_preselection_in_dxf", "error")
        return False


def calculate_case_1_pile_requirements(total_load: float, available_pile_types: List[dict],
                                     log_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Calculate pile requirements for Case 1 based on available pile types.
    
    Implements Rule 1.3: Apply pile type priority (DHP→SHP→BP) with special BP constraint.
    
    Args:
        total_load (float): Total load to support (kN).
        available_pile_types (List[dict]): Available pile types with capacities.
        log_callback: Optional logging callback function.
        
    Returns:
        Dict[str, Any]: Selected pile type and required count with BP constraint enforcement.
    """
    log_function_entry(log_callback, "calculate_case_1_pile_requirements", 
                      total_load=total_load, available_types=len(available_pile_types))
    
    try:
        enhanced_log(log_callback, f"Calculating pile requirements for total load: {total_load:.1f} kN", 'INFO')
        
        # Sort pile types by priority: DHP SHP BP
        pile_type_priority = {'DHP': 1, 'SHP': 2, 'BP': 3}
        sorted_pile_types = sorted(
            available_pile_types, 
            key=lambda x: pile_type_priority.get(x.get('type', 'BP'), 99)
        )
        
        enhanced_log(log_callback, f"Evaluating {len(sorted_pile_types)} pile types in priority order", 'DEBUG')
        
        for i, pile_type in enumerate(sorted_pile_types):
            pile_type_name = pile_type.get('type', 'Unknown')
            capacity = pile_type.get('capacity', 0)
            
            enhanced_log(log_callback, f"Evaluating pile type {i+1}: {pile_type_name} (capacity: {capacity:.1f} kN)", 'DEBUG')
            
            if capacity <= 0:
                enhanced_log(log_callback, f"Skipping {pile_type_name} - invalid capacity: {capacity}", 'WARNING')
                continue
                
            # Calculate required piles
            required_piles = max(1, int(ceil(total_load / capacity)))
            enhanced_log(log_callback, f"Initial calculation: {required_piles} piles needed for {pile_type_name}", 'DEBUG')
            
            # Apply BP constraint: maximum 1 pile
            if pile_type_name == 'BP':
                enhanced_log(log_callback, "Applying BP special constraint: maximum 1 pile", 'DEBUG')
                if required_piles > 1:
                    # Check if single BP pile can handle the load
                    if capacity >= total_load:
                        utilization = total_load / capacity
                        enhanced_log(log_callback, f"Single BP pile sufficient: {capacity:.1f} kN >= {total_load:.1f} kN (utilization: {utilization:.1%})", 'INFO')
                        result = {
                            'selected_pile_type': pile_type,
                            'required_piles': 1,
                            'total_capacity': capacity,
                            'utilization_ratio': utilization,
                            'constraint_applied': 'BP_max_1_pile'
                        }
                        log_function_exit(log_callback, "calculate_case_1_pile_requirements", "BP selected")
                        return result
                    else:
                        enhanced_log(log_callback, f"Single BP pile insufficient: {capacity:.1f} kN < {total_load:.1f} kN - skipping BP", 'WARNING')
                        continue
                else:
                    # Single BP pile is sufficient
                    utilization = total_load / capacity
                    enhanced_log(log_callback, f"Single BP pile selected: {capacity:.1f} kN >= {total_load:.1f} kN (utilization: {utilization:.1%})", 'INFO')
                    result = {
                        'selected_pile_type': pile_type,
                        'required_piles': 1,
                        'total_capacity': capacity,
                        'utilization_ratio': utilization,
                        'constraint_applied': 'BP_max_1_pile'
                    }
                    log_function_exit(log_callback, "calculate_case_1_pile_requirements", "BP selected")
                    return result
            else:
                # DHP or SHP - no special constraints
                total_capacity = required_piles * capacity
                utilization = total_load / total_capacity
                enhanced_log(log_callback, f"{pile_type_name} selected: {required_piles} piles, total capacity: {total_capacity:.1f} kN (utilization: {utilization:.1%})", 'INFO')
                result = {
                    'selected_pile_type': pile_type,
                    'required_piles': required_piles,
                    'total_capacity': total_capacity,
                    'utilization_ratio': utilization,
                    'constraint_applied': None
                }
                log_function_exit(log_callback, "calculate_case_1_pile_requirements", f"{pile_type_name} selected")
                return result
        
        # No suitable pile type found
        enhanced_log(log_callback, "No suitable pile type found for the given load", 'ERROR')
        result = {
            'selected_pile_type': None,
            'required_piles': 0,
            'total_capacity': 0,
            'utilization_ratio': float('inf'),
            'constraint_applied': 'NO_SUITABLE_PILE_TYPE'
        }
        log_function_exit(log_callback, "calculate_case_1_pile_requirements", "no suitable type")
        return result
        
    except Exception as e:
        enhanced_log(log_callback, f"Error in pile requirements calculation: {str(e)}", 'ERROR')
        log_function_exit(log_callback, "calculate_case_1_pile_requirements", "error")
        raise
