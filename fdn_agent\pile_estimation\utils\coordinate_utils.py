﻿"""
Coordinate Processing Utilities

Functions for resolving and processing coordinates from Excel inputs.
"""

from typing import List, Dict, Any, Tuple, Union, Optional
import pandas as pd
from math import sqrt, atan2, pi, cos, sin

from shapely.geometry import Polygon, Point
from ..data_types import ExcelInputs, Point2D, ColumnData, WallData, OptimalCoordinateSystem, OptimalRectangleResult, GeometricBounds
from ..exceptions import InputDataError, GeometryError
from .logging_utils import (
    enhanced_log, log_function_entry, log_function_exit, log_progress,
    log_performance_metric, log_validation_result, log_algorithm_step,
    log_calculation_result, log_error_with_context, create_timed_logger
)


def resolve_coordinates_from_excel(excel_inputs: Union[ExcelInputs, Dict[str, pd.DataFrame]], log_callback=None) -> Tuple[List[ColumnData], List[WallData]]:
    """
    Resolve coordinates from Excel inputs.
    
    Args:
        excel_inputs: Excel input data containing Point, Column, and Wall DataFrames
        log_callback: Optional callback for logging messages
        
    Returns:
        Tuple of (columns, walls) where:
        - columns: List of ColumnData tuples (name, x, y, base_level)
        - walls: List of WallData tuples (name, points_list, base_level)
        
    Raises:
        InputDataError: If required data is missing or invalid
        GeometryError: If coordinate resolution fails
    """
    log_function_entry(log_callback, "resolve_coordinates_from_excel")
    
    with create_timed_logger(log_callback, "coordinate_resolution") as timer:
        try:
            # Input validation
            enhanced_log(log_callback, "Validating input data structure", 'DEBUG')
            if not hasattr(excel_inputs, 'Point') or not hasattr(excel_inputs, 'Column') or not hasattr(excel_inputs, 'Wall'):
                error_msg = "excel_inputs must have Point, Column, and Wall attributes"
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise InputDataError(error_msg)
            
            # Additional validation for the attributes being DataFrames
            for attr_name in ['Point', 'Column', 'Wall']:
                attr_value = getattr(excel_inputs, attr_name)
                if not isinstance(attr_value, pd.DataFrame):
                    error_msg = f"excel_inputs.{attr_name} must be a DataFrame"
                    enhanced_log(log_callback, error_msg, 'ERROR')
                    raise InputDataError(error_msg)
                    
                enhanced_log(log_callback, f"Found {attr_name} DataFrame with {len(attr_value)} rows", 'DEBUG')
            
            # Get dataframes
            enhanced_log(log_callback, "Extracting DataFrames from excel_inputs", 'DEBUG')
            df_point = excel_inputs.Point.copy()
            df_column = excel_inputs.Column.copy()
            df_wall = excel_inputs.Wall.copy()
            
            log_calculation_result(log_callback, "Total input points", len(df_point))
            log_calculation_result(log_callback, "Total input columns", len(df_column))
            log_calculation_result(log_callback, "Total input walls", len(df_wall))
            
            # Validate input data using validation utilities
            enhanced_log(log_callback, "Performing detailed coordinate DataFrame validation", 'INFO')
            from .validation_utils import validate_coordinate_dataframes
            validate_coordinate_dataframes(df_point, df_column, df_wall)
            log_validation_result(log_callback, "coordinate_dataframes", True, "All DataFrames contain required columns")
            
            # Create point lookup dictionary
            enhanced_log(log_callback, "Building point coordinate lookup dictionary", 'DEBUG')
            point_dict = df_point.set_index('Point')[['X (m)', 'Y (m)']].to_dict('index')
            log_calculation_result(log_callback, "Point dictionary entries", len(point_dict))
            
            # Process columns and walls
            enhanced_log(log_callback, "Processing column coordinate data", 'INFO')
            columns = _process_column_data(df_column, point_dict, log_callback)
            
            enhanced_log(log_callback, "Processing wall coordinate data", 'INFO')
            walls = _process_wall_data(df_wall, point_dict, log_callback)
            
            log_calculation_result(log_callback, "Successfully processed columns", len(columns))
            log_calculation_result(log_callback, "Successfully processed walls", len(walls))
            
            log_function_exit(log_callback, "resolve_coordinates_from_excel", f"({len(columns)} columns, {len(walls)} walls)")
            return columns, walls
            
        except Exception as e:
            log_error_with_context(log_callback, e, "resolve_coordinates_from_excel")
            raise


def _process_column_data(df_column: pd.DataFrame, 
                        point_dict: Dict[str, Dict[str, float]],
                        log_callback=None) -> List[ColumnData]:
    """
    Process column data into structured format.
    
    Args:
        df_column: DataFrame containing column data
        point_dict: Dictionary mapping point names to coordinates
        log_callback: Optional callback for logging messages
        
    Returns:
        List of ColumnData tuples (name, x, y, base_level)
        
    Raises:
        GeometryError: If there are issues with the column data
    """
    log_function_entry(log_callback, "_process_column_data", 
                      df_columns_count=len(df_column), point_dict_size=len(point_dict))
    
    columns = []
    processed_count = 0
    error_count = 0
    
    for _, row in df_column.iterrows():
        try:
            column_name = row['Column']
            center_point_name = row['Center Point']
            base_level = float(row['Base Level'])
            
            enhanced_log(log_callback, f"Processing column '{column_name}' with center point '{center_point_name}'", 'DEBUG')
            
            # Get center point coordinates
            if center_point_name not in point_dict:
                error_msg = f"Center point {center_point_name} not found in point data for column {column_name}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                error_count += 1
                raise GeometryError(error_msg)
                
            x = point_dict[center_point_name]['X (m)']
            y = point_dict[center_point_name]['Y (m)']
            
            columns.append((column_name, x, y, base_level))
            processed_count += 1
            
            enhanced_log(log_callback, f"Column '{column_name}' processed: ({x:.3f}, {y:.3f}) @ level {base_level}", 'DEBUG')
            
            # Log progress for large datasets
            if len(df_column) > 10 and processed_count % max(1, len(df_column) // 10) == 0:
                log_progress(log_callback, processed_count, len(df_column), "column processing")
            
        except Exception as e:
            error_count += 1
            log_error_with_context(log_callback, e, f"processing column {row.get('Column', 'Unknown')}")
            raise
            
    log_calculation_result(log_callback, "Columns successfully processed", processed_count)
    if error_count > 0:
        enhanced_log(log_callback, f"Column processing completed with {error_count} errors", 'WARNING')
    
    log_function_exit(log_callback, "_process_column_data", f"{len(columns)} columns")
    return columns


def _process_wall_data(df_wall: pd.DataFrame, 
                      point_dict: Dict[str, Dict[str, float]],
                      log_callback=None) -> List[WallData]:
    """
    Process wall data into structured format.
    
    Args:
        df_wall: DataFrame containing wall data
        point_dict: Dictionary mapping point names to coordinates
        log_callback: Optional callback for logging messages
        
    Returns:
        List of WallData tuples (name, points_list, base_level)
        
    Raises:
        GeometryError: If there are issues with the wall data
    """
    log_function_entry(log_callback, "_process_wall_data", 
                      df_walls_count=len(df_wall), point_dict_size=len(point_dict))
    
    walls = []
    processed_count = 0
    error_count = 0
    total_wall_points = 0
    
    for _, row in df_wall.iterrows():
        try:
            wall_name = row['Wall']
            points_str = row['Points']
            base_level = float(row['Base Level'])
            
            enhanced_log(log_callback, f"Processing wall '{wall_name}' with points string: '{points_str}'", 'DEBUG')
            
            # Parse semicolon-separated point names
            if pd.isna(points_str) or not points_str.strip():
                error_msg = f"No points defined for wall {wall_name}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                error_count += 1
                raise GeometryError(error_msg)
            
            point_names = [pt.strip() for pt in str(points_str).split(';') if pt.strip()]
            if not point_names:
                error_msg = f"No valid points found for wall {wall_name}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                error_count += 1
                raise GeometryError(error_msg)
            
            enhanced_log(log_callback, f"Wall '{wall_name}' has {len(point_names)} points: {point_names}", 'DEBUG')
            
            # Collect wall points
            wall_points = []
            missing_points = []
            
            for point_name in point_names:
                if not _validate_point_exists(point_name, point_dict):
                    missing_points.append(point_name)
                    continue
                
                point_data = point_dict[point_name]
                wall_points.append((point_data['X (m)'], point_data['Y (m)']))
                total_wall_points += 1
            
            if missing_points:
                error_msg = f"Points {missing_points} not found for wall {wall_name}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                error_count += 1
                raise GeometryError(error_msg)
            
            if not wall_points:
                error_msg = f"No valid points found for wall {wall_name}"
                enhanced_log(log_callback, error_msg, 'ERROR')
                error_count += 1
                raise GeometryError(error_msg)
            
            walls.append((wall_name, wall_points, base_level))
            processed_count += 1
            
            enhanced_log(log_callback, f"Wall '{wall_name}' processed with {len(wall_points)} points @ level {base_level}", 'DEBUG')
            
            # Log progress for large datasets
            if len(df_wall) > 10 and processed_count % max(1, len(df_wall) // 10) == 0:
                log_progress(log_callback, processed_count, len(df_wall), "wall processing")
                
        except Exception as e:
            error_count += 1
            log_error_with_context(log_callback, e, f"processing wall {row.get('Wall', 'Unknown')}")
            raise
            
    log_calculation_result(log_callback, "Walls successfully processed", processed_count)
    log_calculation_result(log_callback, "Total wall points processed", total_wall_points)
    if error_count > 0:
        enhanced_log(log_callback, f"Wall processing completed with {error_count} errors", 'WARNING')
    
    log_function_exit(log_callback, "_process_wall_data", f"{len(walls)} walls")
    return walls


def _validate_point_exists(point_name: str, point_dict: Dict[str, Dict[str, float]]) -> bool:
    """
    Validate that a point exists in the point dictionary with valid coordinates.
    
    Args:
        point_name: Name of the point to validate
        point_dict: Dictionary mapping point names to coordinates
        
    Returns:
        bool: True if point exists and has valid coordinates
    """
    if point_name not in point_dict:
        return False
        
    point_data = point_dict[point_name]
    return all(
        isinstance(coord, (int, float)) and not pd.isna(coord)
        for coord in [point_data.get('X (m)'), point_data.get('Y (m)')]
    )


def find_minimum_area_bounding_rectangle(polygon: Polygon, 
                                        tolerance: float = 0.05,
                                        log_callback=None) -> OptimalRectangleResult:
    """
    Find the minimum area bounding rectangle for a polygon using rotating calipers algorithm.
    
    Args:
        polygon: Shapely polygon representing the pile cap
        tolerance: Tolerance for considering global alignment (default 5%)
        log_callback: Optional callback for logging messages
        
    Returns:
        OptimalRectangleResult containing the optimal coordinate system and comparison data
        
    Raises:
        GeometryError: If the polygon is invalid or empty
    """
    log_function_entry(log_callback, "find_minimum_area_bounding_rectangle", tolerance=tolerance)
    
    with create_timed_logger(log_callback, "minimum_area_rectangle_calculation") as timer:
        try:
            # Input validation
            if not polygon or polygon.is_empty:
                error_msg = "Invalid or empty polygon provided"
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise GeometryError(error_msg)
            
            enhanced_log(log_callback, f"Starting optimization for polygon with area {polygon.area:.3f}", 'INFO')
            
            # First, calculate the global axis-aligned bounding box
            enhanced_log(log_callback, "Calculating global axis-aligned bounding box", 'DEBUG')
            global_bounds = _calculate_global_bounds(polygon)
            global_aligned_area = global_bounds.area
            log_calculation_result(log_callback, "Global aligned area", global_aligned_area, "m²")
            
            # Get the convex hull to simplify the problem
            enhanced_log(log_callback, "Computing convex hull for optimization", 'DEBUG')
            convex_hull = polygon.convex_hull if hasattr(polygon, 'convex_hull') else polygon
            
            # Get the boundary coordinates
            if hasattr(convex_hull, 'exterior'):
                coords = list(convex_hull.exterior.coords)[:-1]  # Remove duplicate last point
            else:
                coords = list(convex_hull.coords)
                
            if len(coords) < 3:
                error_msg = "Polygon must have at least 3 vertices"
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise GeometryError(error_msg)
                
            enhanced_log(log_callback, f"Working with {len(coords)} convex hull vertices", 'DEBUG')
            
            # Find minimum area orientation
            min_area = float('inf')
            best_rect = None
            best_angle = 0
            n_points = len(coords)
            
            enhanced_log(log_callback, "Starting edge-based orientation optimization", 'INFO')
            log_algorithm_step(log_callback, "rotating_calipers", "edge_orientation_pass", f"{n_points} edges to test")
            
            # First pass: Test orientations based on polygon edges
            edge_tested_count = 0
            for i in range(n_points):
                # Calculate edge direction
                p1 = coords[i]
                p2 = coords[(i + 1) % n_points]
                
                # Edge vector
                edge_vector = (p2[0] - p1[0], p2[1] - p1[1])
                edge_length = sqrt(edge_vector[0]**2 + edge_vector[1]**2)
                
                if edge_length < 1e-10:  # Skip degenerate edges
                    enhanced_log(log_callback, f"Skipping degenerate edge {i} with length {edge_length}", 'DEBUG')
                    continue
                    
                # Normalize and get angle
                edge_unit = (edge_vector[0] / edge_length, edge_vector[1] / edge_length)
                angle = atan2(edge_unit[1], edge_unit[0])
                
                # Test this orientation
                rect_data = _compute_bounding_rect_for_angle(coords, angle)
                edge_tested_count += 1
                
                if rect_data and rect_data['area'] < min_area:
                    min_area = rect_data['area']
                    best_rect = rect_data
                    best_angle = angle
                    enhanced_log(log_callback, f"New best area found: {min_area:.3f} m² at edge {i} (angle {angle:.3f} rad)", 'DEBUG')
            
            log_performance_metric(log_callback, "edge_orientations_tested", edge_tested_count)
            log_calculation_result(log_callback, "Best area after edge optimization", min_area, "m²")
            
            # Second pass: Test fine-grained rotations (0.1 degree increments)
            enhanced_log(log_callback, "Starting fine-grained rotation optimization (0.1° increments)", 'INFO')
            log_algorithm_step(log_callback, "rotating_calipers", "fine_rotation_pass", "1800 angles (0-180°)")
            
            fine_tested_count = 0
            improvements_found = 0
            
            for angle_deg in range(0, 1800):  # 0 to 180 degrees in 0.1 degree steps
                angle = (angle_deg / 10.0) * (pi / 180.0)  # Convert to radians
                
                rect_data = _compute_bounding_rect_for_angle(coords, angle)
                fine_tested_count += 1
                
                if rect_data and rect_data['area'] < min_area:
                    min_area = rect_data['area']
                    best_rect = rect_data
                    best_angle = angle
                    improvements_found += 1
                    
                    if improvements_found % 10 == 0:  # Log every 10th improvement
                        enhanced_log(log_callback, f"Fine optimization improvement #{improvements_found}: {min_area:.3f} m² at {angle_deg/10:.1f}°", 'DEBUG')
                
                # Progress logging for long operations
                if fine_tested_count % 300 == 0:  # Every 30 degrees
                    log_progress(log_callback, fine_tested_count, 1800, "fine rotation optimization")
            
            log_performance_metric(log_callback, "fine_orientations_tested", fine_tested_count)
            log_performance_metric(log_callback, "improvements_found", improvements_found)
            
            if best_rect is None:
                error_msg = "Failed to compute minimum bounding rectangle"
                enhanced_log(log_callback, error_msg, 'ERROR')
                raise GeometryError(error_msg)
                
            log_calculation_result(log_callback, "Final optimized area", min_area, "m²")
            
            # Ensure the long axis is the x-axis in local coordinates
            enhanced_log(log_callback, "Optimizing axis orientation for long axis alignment", 'DEBUG')
            width = best_rect['width']
            height = best_rect['height']
            
            if height > width:
                enhanced_log(log_callback, f"Rotating by 90° to align long axis (was {width:.3f}×{height:.3f})", 'DEBUG')
                # Rotate by 90 degrees to make the long axis the x-axis
                best_angle += pi / 2
                width, height = height, width
                # Recompute rectangle with new angle
                best_rect = _compute_bounding_rect_for_angle(coords, best_angle)
                if best_rect:
                    width = best_rect['width']
                    height = best_rect['height']
                    if height > width:  # Double check and swap if needed
                        width, height = height, width
                        enhanced_log(log_callback, "Applied additional swap for axis alignment", 'DEBUG')
            
            log_calculation_result(log_callback, "Final rectangle dimensions", f"{width:.3f}×{height:.3f}", "m")
            
            # Calculate area improvement
            rotated_area = min_area
            area_improvement = (global_aligned_area - rotated_area) / global_aligned_area
            log_calculation_result(log_callback, "Area improvement", area_improvement * 100, "%")
            
            # Determine if we should use global alignment
            use_global_alignment = area_improvement < tolerance
            alignment_decision = "global" if use_global_alignment else "rotated"
            enhanced_log(log_callback, f"Alignment decision: {alignment_decision} (improvement {area_improvement*100:.2f}% vs threshold {tolerance*100:.1f}%)", 'INFO')
            
            # Create the coordinate system
            if use_global_alignment:
                enhanced_log(log_callback, "Using global axis alignment", 'INFO')
                optimal_system = OptimalCoordinateSystem(
                    origin=global_bounds.center,
                    rotation_angle=0.0,
                    long_axis_length=max(global_bounds.width, global_bounds.height),
                    short_axis_length=min(global_bounds.width, global_bounds.height),
                    bounding_rect_corners=_get_global_aligned_corners(global_bounds),
                    is_global_aligned=True
                )
            else:
                enhanced_log(log_callback, f"Using rotated optimal rectangle (angle: {best_angle:.3f} rad = {best_angle*180/pi:.1f}°)", 'INFO')
                optimal_system = OptimalCoordinateSystem(
                    origin=best_rect['center'],
                    rotation_angle=best_angle,
                    long_axis_length=width,
                    short_axis_length=height,
                    bounding_rect_corners=best_rect['corners'],
                    is_global_aligned=False
                )
            
            result = OptimalRectangleResult(
                local_system=optimal_system,
                rotated_area=rotated_area,
                global_aligned_area=global_aligned_area,
                area_improvement=area_improvement * 100,  # Convert to percentage
                use_global_alignment=use_global_alignment,
                tolerance_threshold=tolerance
            )
            
            log_function_exit(log_callback, "find_minimum_area_bounding_rectangle", f"area_improvement={area_improvement*100:.2f}%")
            return result
            
        except Exception as e:
            log_error_with_context(log_callback, e, "find_minimum_area_bounding_rectangle")
            raise


def global_to_local_coordinates(global_point: Tuple[float, float], 
                               local_system: OptimalCoordinateSystem,
                               log_callback=None) -> Tuple[float, float]:
    """
    Transform a point from global coordinates to local coordinates.
    
    Args:
        global_point: Point in global coordinate system
        local_system: Local coordinate system parameters
        log_callback: Optional callback for logging messages
        
    Returns:
        Point in local coordinate system
    """
    enhanced_log(log_callback, f"Transforming global point {global_point} to local coordinates", 'DEBUG')
    
    # Translate to origin
    translated_x = global_point[0] - local_system.origin[0]
    translated_y = global_point[1] - local_system.origin[1]
    
    # Rotate to local orientation
    cos_a = cos(-local_system.rotation_angle)  # Negative for inverse rotation
    sin_a = sin(-local_system.rotation_angle)
    
    local_x = cos_a * translated_x + sin_a * translated_y
    local_y = -sin_a * translated_x + cos_a * translated_y
    
    local_point = (local_x, local_y)
    enhanced_log(log_callback, f"Global {global_point} → Local {local_point}", 'DEBUG')
    
    return local_point


def local_to_global_coordinates(local_point: Tuple[float, float], 
                               local_system: OptimalCoordinateSystem,
                               log_callback=None) -> Tuple[float, float]:
    """
    Transform a point from local coordinates to global coordinates.
    
    Args:
        local_point: Point in local coordinate system
        local_system: Local coordinate system parameters
        log_callback: Optional callback for logging messages
        
    Returns:
        Point in global coordinate system
    """
    enhanced_log(log_callback, f"Transforming local point {local_point} to global coordinates", 'DEBUG')
    
    # Rotate to global orientation
    cos_a = cos(local_system.rotation_angle)
    sin_a = sin(local_system.rotation_angle)
    
    rotated_x = cos_a * local_point[0] - sin_a * local_point[1]
    rotated_y = sin_a * local_point[0] + cos_a * local_point[1]
    
    # Translate from origin
    global_x = rotated_x + local_system.origin[0]
    global_y = rotated_y + local_system.origin[1]
    
    global_point = (global_x, global_y)
    enhanced_log(log_callback, f"Local {local_point} → Global {global_point}", 'DEBUG')
    
    return global_point


def generate_local_grid_positions(local_system: OptimalCoordinateSystem,
                                 pile_diameter: float,
                                 min_spacing: float,
                                 edge_clearance: float = 0.3,
                                 log_callback=None) -> List[Tuple[float, float]]:
    """
    Generate grid-based pile positions in local coordinates aligned with the local axes.
    
    Args:
        local_system: Local coordinate system parameters
        pile_diameter: Pile diameter in meters
        min_spacing: Minimum pile spacing in meters
        edge_clearance: Minimum distance from pile center to rectangle edge
        log_callback: Optional callback for logging messages
        
    Returns:
        List of pile positions in local coordinates
    """
    log_function_entry(log_callback, "generate_local_grid_positions",
                      pile_diameter=pile_diameter, min_spacing=min_spacing, edge_clearance=edge_clearance)
    
    with create_timed_logger(log_callback, "local_grid_generation") as timer:
        effective_spacing = max(min_spacing, 2.5 * pile_diameter)
        pile_radius = pile_diameter / 2
        
        log_calculation_result(log_callback, "Effective pile spacing", effective_spacing, "m")
        
        # Adaptive edge clearance for narrow/linear pile caps
        aspect_ratio = local_system.long_axis_length / local_system.short_axis_length
        min_dimension = min(local_system.long_axis_length, local_system.short_axis_length)
        
        log_calculation_result(log_callback, "Pile cap aspect ratio", aspect_ratio)
        log_calculation_result(log_callback, "Minimum pile cap dimension", min_dimension, "m")
        
        adaptive_edge_clearance = edge_clearance
        pile_radius_for_clearance = pile_radius
        
        if aspect_ratio > 10 and min_dimension < 0.5:  # Very narrow caps (walls)
            adaptive_edge_clearance = max(0.02, min_dimension * 0.05)
            if min_dimension < pile_diameter:
                pile_radius_for_clearance = min_dimension * 0.1
            enhanced_log(log_callback, "Detected very narrow pile cap (wall-like) - using minimal clearances", 'INFO')
        elif aspect_ratio > 5 and min_dimension < 1.0:  # Moderately narrow caps
            adaptive_edge_clearance = max(0.05, min_dimension * 0.1)
            enhanced_log(log_callback, "Detected moderately narrow pile cap - reducing clearances", 'INFO')
        elif min_dimension < pile_diameter * 2:  # Small caps
            adaptive_edge_clearance = max(0.1, min_dimension * 0.15)
            enhanced_log(log_callback, "Detected small pile cap - adjusting clearances", 'INFO')
        
        total_clearance = adaptive_edge_clearance + pile_radius_for_clearance
        log_calculation_result(log_callback, "Adaptive edge clearance", adaptive_edge_clearance, "m")
        log_calculation_result(log_callback, "Total clearance", total_clearance, "m")
        
        usable_width = local_system.long_axis_length - 2 * total_clearance
        usable_height = local_system.short_axis_length - 2 * total_clearance
        
        log_calculation_result(log_callback, "Usable grid width", usable_width, "m")
        log_calculation_result(log_callback, "Usable grid height", usable_height, "m")
        
        if usable_width <= 0 or usable_height <= 0:
            enhanced_log(log_callback, "Insufficient space for grid - placing single pile at center", 'WARNING')
            log_function_exit(log_callback, "generate_local_grid_positions", "1 pile (center only)")
            return [(0, 0)]  # Single pile at center
        
        # Calculate number of piles in each direction
        n_x = max(1, int(usable_width / effective_spacing) + 1)
        n_y = max(1, int(usable_height / effective_spacing) + 1)
        
        log_calculation_result(log_callback, "Grid dimensions (n_x × n_y)", f"{n_x} × {n_y}")
        
        # Calculate actual spacing to center the grid
        spacing_x = usable_width / (n_x - 1) if n_x > 1 else 0
        spacing_y = usable_height / (n_y - 1) if n_y > 1 else 0
        
        log_calculation_result(log_callback, "Actual spacing X", spacing_x, "m")
        log_calculation_result(log_callback, "Actual spacing Y", spacing_y, "m")
        
        # Generate grid positions ensuring load center (0,0) is ALWAYS included
        positions = []
        
        # Always include the load center (0,0) first
        positions.append((0.0, 0.0))
        enhanced_log(log_callback, "Added load center position (0,0)", 'DEBUG')
        
        # Calculate grid based on centering around (0,0)
        # Determine how many grid points we can fit on each side of center
        half_width_points = int(usable_width / (2 * effective_spacing))
        half_height_points = int(usable_height / (2 * effective_spacing))
        
        enhanced_log(log_callback, f"Grid half-spans: {half_width_points} × {half_height_points}", 'DEBUG')
        
        # Add positions along the axes first (ensures load center connectivity)
        axis_positions_added = 0
        for i in range(1, half_width_points + 1):
            x_pos = i * effective_spacing
            x_neg = -i * effective_spacing
            if abs(x_pos) <= usable_width / 2:
                positions.append((x_pos, 0.0))
                axis_positions_added += 1
            if abs(x_neg) <= usable_width / 2:
                positions.append((x_neg, 0.0))
                axis_positions_added += 1
        
        for j in range(1, half_height_points + 1):
            y_pos = j * effective_spacing
            y_neg = -j * effective_spacing
            if abs(y_pos) <= usable_height / 2:
                positions.append((0.0, y_pos))
                axis_positions_added += 1
            if abs(y_neg) <= usable_height / 2:
                positions.append((0.0, y_neg))
                axis_positions_added += 1
        
        enhanced_log(log_callback, f"Added {axis_positions_added} axis-aligned positions", 'DEBUG')
        
        # Add remaining grid positions in all quadrants
        quadrant_positions_added = 0
        for i in range(1, half_width_points + 1):
            for j in range(1, half_height_points + 1):
                quadrant_positions = [
                    (i * effective_spacing, j * effective_spacing),    # Q1
                    (-i * effective_spacing, j * effective_spacing),   # Q2
                    (-i * effective_spacing, -j * effective_spacing),  # Q3
                    (i * effective_spacing, -j * effective_spacing)    # Q4
                ]
                
                for x, y in quadrant_positions:
                    if (abs(x) <= usable_width / 2 and abs(y) <= usable_height / 2):
                        positions.append((x, y))
                        quadrant_positions_added += 1
        
        enhanced_log(log_callback, f"Added {quadrant_positions_added} quadrant positions", 'DEBUG')
        log_calculation_result(log_callback, "Total grid positions generated", len(positions))
        
        log_function_exit(log_callback, "generate_local_grid_positions", f"{len(positions)} positions")
        return positions


def _compute_bounding_rect_for_angle(coords: List[Tuple[float, float]], 
                                    angle: float,
                                    log_callback=None) -> Dict[str, Any]:
    """
    Compute bounding rectangle for a given orientation angle.
    
    Args:
        coords: List of polygon vertex coordinates
        angle: Rotation angle in radians
        log_callback: Optional callback for logging messages
        
    Returns:
        Dictionary with rectangle properties
        
    Raises:
        GeometryError: If computation fails
    """
    try:
        cos_a = cos(angle)
        sin_a = sin(angle)
        
        # Transform all points to the rotated coordinate system
        transformed_points = []
        for x, y in coords:
            new_x = cos_a * x + sin_a * y
            new_y = -sin_a * x + cos_a * y
            transformed_points.append((new_x, new_y))
        
        # Find bounding box in transformed coordinates
        min_x = min(p[0] for p in transformed_points)
        max_x = max(p[0] for p in transformed_points)
        min_y = min(p[1] for p in transformed_points)
        max_y = max(p[1] for p in transformed_points)
        
        width = max_x - min_x
        height = max_y - min_y
        area = width * height
        
        # Center in transformed coordinates
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2
        
        # Transform center back to original coordinates
        original_center_x = cos_a * center_x - sin_a * center_y
        original_center_y = sin_a * center_x + cos_a * center_y
        
        # Calculate corners in original coordinates
        corners = []
        for dx, dy in [(-width/2, -height/2), (width/2, -height/2), 
                       (width/2, height/2), (-width/2, height/2)]:
            # Transform corner relative to center
            corner_x = cos_a * dx - sin_a * dy + original_center_x
            corner_y = sin_a * dx + cos_a * dy + original_center_y
            corners.append((corner_x, corner_y))
        
        return {
            'area': area,
            'width': width,
            'height': height,
            'center': (original_center_x, original_center_y),
            'corners': corners
        }
        
    except Exception as e:
        if log_callback:
            log_error_with_context(log_callback, e, f"computing bounding rectangle for angle {angle:.3f}")
        raise GeometryError(f"Failed to compute bounding rectangle for angle {angle}: {e}")


def generate_optimized_grid_positions(local_system: OptimalCoordinateSystem,
                                    pile_diameter: float,
                                    min_spacing: float,
                                    pile_cap: Polygon,
                                    edge_clearance: float = 0.3,
                                    log_callback=None) -> List[Tuple[float, float]]:
    """
    Generate optimized grid positions with shifting to maximize pile count within pile cap.
    
    Args:
        local_system: Local coordinate system parameters
        pile_diameter: Pile diameter in meters
        min_spacing: Minimum pile spacing in meters
        pile_cap: Pile cap polygon for boundary checking
        edge_clearance: Minimum distance from pile center to rectangle edge
        log_callback: Optional callback for logging messages
        
    Returns:
        List of pile positions in local coordinates that maximize pile count
    """
    log_function_entry(log_callback, "generate_optimized_grid_positions",
                      pile_diameter=pile_diameter, min_spacing=min_spacing, edge_clearance=edge_clearance)
    
    with create_timed_logger(log_callback, "optimized_grid_generation") as timer:
        from shapely.geometry import Point
        
        effective_spacing = max(min_spacing, 2.0 * pile_diameter)
        log_calculation_result(log_callback, "Effective spacing", effective_spacing, "m")
        
        # Create initial large grid that covers expanded area
        expansion_factor = 1.5  # Make grid larger than pile cap
        expanded_width = local_system.long_axis_length * expansion_factor
        expanded_height = local_system.short_axis_length * expansion_factor
        
        log_calculation_result(log_callback, "Expanded grid dimensions", f"{expanded_width:.3f} × {expanded_height:.3f}", "m")
        
        # Calculate grid dimensions - make it dense enough to capture all possibilities
        n_x = max(3, int(expanded_width / effective_spacing) + 3)
        n_y = max(3, int(expanded_height / effective_spacing) + 3)
        
        log_calculation_result(log_callback, "Base grid size", f"{n_x} × {n_y}")
        
        # Test different grid offsets to find maximum pile count
        max_piles = 0
        best_positions = []
        
        # Maximum shift is min_spacing in each direction
        max_shift = min_spacing
        shift_steps = 5  # Number of test positions along each axis
        total_shift_tests = (shift_steps + 1) ** 2
        
        enhanced_log(log_callback, f"Starting grid shift optimization with {total_shift_tests} shift combinations", 'INFO')
        log_algorithm_step(log_callback, "grid_optimization", "shift_testing", f"max_shift={max_shift:.3f}m, steps={shift_steps}")
        
        shift_tests_completed = 0
        improvements_found = 0
        
        for shift_x_ratio in [i / shift_steps for i in range(shift_steps + 1)]:
            for shift_y_ratio in [i / shift_steps for i in range(shift_steps + 1)]:
                # Calculate actual shift values
                shift_x = (shift_x_ratio - 0.5) * max_shift
                shift_y = (shift_y_ratio - 0.5) * max_shift
                
                enhanced_log(log_callback, f"Testing shift ({shift_x:.3f}, {shift_y:.3f})", 'DEBUG')
                
                # Generate grid with this offset
                grid_positions = _generate_shifted_grid(
                    local_system, n_x, n_y, effective_spacing, 
                    expansion_factor, shift_x, shift_y
                )
                
                # Convert to global coordinates and check pile cap containment
                valid_positions = []
                for local_pos in grid_positions:
                    global_pos = local_to_global_coordinates(local_pos, local_system, log_callback)
                    point = Point(global_pos[0], global_pos[1])
                    if pile_cap.contains(point) or pile_cap.touches(point):
                        valid_positions.append(local_pos)
                
                shift_tests_completed += 1
                
                # Update best configuration if this gives more piles
                if len(valid_positions) > max_piles:
                    max_piles = len(valid_positions)
                    best_positions = valid_positions.copy()
                    improvements_found += 1
                    enhanced_log(log_callback, f"New best configuration: {max_piles} piles with shift ({shift_x:.3f}, {shift_y:.3f})", 'INFO')
                
                # Progress logging for long operations
                if shift_tests_completed % max(1, total_shift_tests // 10) == 0:
                    log_progress(log_callback, shift_tests_completed, total_shift_tests, "grid shift optimization")
        
        log_performance_metric(log_callback, "shift_tests_completed", shift_tests_completed)
        log_performance_metric(log_callback, "improvements_found", improvements_found)
        log_calculation_result(log_callback, "Maximum piles achieved", max_piles)
        
        enhanced_log(log_callback, f"Grid optimization completed - achieved {max_piles} piles in pile cap boundary", 'INFO')
        log_function_exit(log_callback, "generate_optimized_grid_positions", f"{len(best_positions)} optimized positions")
        
        return best_positions


def _generate_shifted_grid(local_system: OptimalCoordinateSystem,
                          n_x: int, n_y: int, 
                          spacing: float,
                          expansion_factor: float,
                          shift_x: float, shift_y: float) -> List[Tuple[float, float]]:
    """
    Generate a grid with specified shift offsets.
    
    Args:
        local_system: Local coordinate system
        n_x, n_y: Number of grid points in each direction
        spacing: Grid spacing
        expansion_factor: Factor to expand grid beyond pile cap
        shift_x, shift_y: Shift offsets in local coordinates
        
    Returns:
        List of grid positions in local coordinates
    """
    expanded_width = local_system.long_axis_length * expansion_factor
    expanded_height = local_system.short_axis_length * expansion_factor
    
    # Calculate actual spacing to fit the grid
    spacing_x = expanded_width / (n_x - 1) if n_x > 1 else 0
    spacing_y = expanded_height / (n_y - 1) if n_y > 1 else 0
    
    # Apply minimum spacing constraint
    spacing_x = max(spacing_x, spacing)
    spacing_y = max(spacing_y, spacing)
    
    # Generate grid positions with shift
    positions = []
    start_x = -expanded_width / 2 + shift_x
    start_y = -expanded_height / 2 + shift_y
    
    for i in range(n_x):
        for j in range(n_y):
            x = start_x + i * spacing_x
            y = start_y + j * spacing_y
            positions.append((x, y))
    
    return positions


def visualize_optimal_rectangle_in_dxf(msp, local_system: OptimalCoordinateSystem, layer_name: str = 'OPTIMAL_RECTANGLE'):
    """
    Visualize the optimal minimum area bounding rectangle in DXF.
    
    Args:
        msp: DXF modelspace
        local_system: Local coordinate system with rectangle corners
        layer_name: DXF layer name for the rectangle
    """
    if not (hasattr(local_system, 'bounding_rect_corners') and local_system.bounding_rect_corners):
        raise GeometryError("Local system must have bounding_rect_corners")
    
    corners = local_system.bounding_rect_corners
    
    # Create polyline for the optimal rectangle
    polyline = msp.add_lwpolyline(corners)
    polyline.close()
    polyline.dxf.layer = layer_name
    
    # Add dimension labels
    center = local_system.origin
    msp.add_text(
        f"Optimal Rectangle\n{local_system.long_axis_length:.2f}m  {local_system.short_axis_length:.2f}m",
        dxfattribs={'layer': layer_name, 'height': 0.3}
    ).set_placement((center[0], center[1] - 1.0))


def _calculate_global_bounds(polygon: Polygon) -> GeometricBounds:
    """
    Calculate global axis-aligned bounding box for a polygon.
    
    Args:
        polygon: Input polygon
        
    Returns:
        GeometricBounds object with bounding box information
    """
    bounds = polygon.bounds  # (minx, miny, maxx, maxy)
    min_x, min_y, max_x, max_y = bounds
    
    width = max_x - min_x
    height = max_y - min_y
    center = ((min_x + max_x) / 2, (min_y + max_y) / 2)
    area = width * height
    
    return GeometricBounds(
        min_x=min_x,
        max_x=max_x,
        min_y=min_y,
        max_y=max_y,
        width=width,
        height=height,
        center=center,
        area=area
    )


def _get_global_aligned_corners(bounds: GeometricBounds) -> List[Tuple[float, float]]:
    """
    Get corner coordinates for a global axis-aligned bounding box.
    
    Args:
        bounds: GeometricBounds object
        
    Returns:
        List of corner coordinates in counter-clockwise order
    """
    return [
        (bounds.min_x, bounds.min_y),  # Bottom-left
        (bounds.max_x, bounds.min_y),  # Bottom-right
        (bounds.max_x, bounds.max_y),  # Top-right
        (bounds.min_x, bounds.max_y)   # Top-left
    ]

