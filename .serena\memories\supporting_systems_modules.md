# Supporting Systems & Utilities

## Data Input/Output System (`read/`)
- **`read_geometry.py`**: Structural geometry processing:
  - `_process_pile_bp_data()`, `_process_pile_shp_data()` for pile data
  - `_process_pile_dhp_data()`, `_process_pile_mp_data()` for different pile types
- **`read_geology.py`**: Geological data processing with borehole SPT data
- **`read_loading.py`**: Load pattern and case processing with data validation
- **`read_property.py`**: Material property reading
- **`read_steel.py`**: Steel section library integration

## Project Initialization (`initialization/`)
- **`data_operations.py`**: Core data operations:
  - `init_input()`, `init_output()` for project setup
  - `update_excel_geology()`, `update_excel_loading()` for data updates
  - `read_all_input_with_log()` for comprehensive input reading
- **`file_operations.py`**: File management with safe writing operations
- **`init_input_*.py`**: Specialized initialization for geometry, geology, loading, properties

## SAFE API Integration (`safe_api/`)
- **`main.py`**: Complete workflow automation:
  - `automate_build_fem()`, `automate_foundation_design()` for full automation
  - `automate_complete_workflow()` for end-to-end processing
- **`models.py`**: SAFE model setup and analysis execution
- **`safe_connection.py`**: SAFE software connection management
- **`data_processor.py`**: SAFE result processing and analysis
- **`database_manager.py`**: SQLite database operations for results storage

## Communication System (`email_notifications/`)
- **`notification.py`**: Email-based authentication system:
  - `generate_password_key()` for secure password generation
  - `send_password_email()` for OTP delivery
  - `create_smtp_ssl_context()` for secure email transmission
  - SSL fallback mechanisms for email reliability

## Slab Design System (`design_slab/`)
- **`design_slab.py`**: Complete slab design workflow:
  - SAFE database reading and Excel processing
  - Design data classes (FilePath, DesignData)
  - Strip drawing parameters and PT matching
  - External data integration

## Steel Library (`Library_Steel/`)
- **Steel section database**: SteelSection.xlsx with comprehensive steel properties
- **Integration functions**: Steel section reading and property extraction

## Complete SAFE API System (`safe_api/`)
- **`models.py`**: SAFE model management and analysis execution
  - `setup_file_paths_and_read_inputs()` for file path configuration
  - `initialize_model()` for SAFE model initialization with units
  - `import_and_run_analysis()` for data import and analysis execution
- **`safe_connection.py`**: SAFE software connection management
  - `connect_to_safe()` for establishing SAFE application connections
  - `setup_safe_connection()` for environment-based connection setup
  - Support for local/remote connections and instance attachment
- **`data_processor.py`**: SAFE result processing and analysis
  - `extract_safe22_dataframe()` for SAFE database table extraction
  - `process_point_object_connectivity()` for structural connectivity
  - `process_frame_assignments_local_axes()` for frame orientation data
  - `process_joint_displacements()` for displacement result processing
- **`database_manager.py`**: SQLite database operations for results storage
  - `save_df_to_sqlite()` with performance optimizations
  - `save_df_to_database()` with comprehensive error handling
  - `setup_database_path()` for dynamic database configuration

## Comprehensive Data Reading System (`read/`)
- **`read_geometry.py`**: Advanced structural geometry processing
  - `_process_pile_bp_data()`, `_process_pile_shp_data()` for bored/socket piles
  - `_process_pile_dhp_data()`, `_process_pile_mp_data()` for driven/mini piles
  - Pile type consolidation and coordinate processing
  - Site boundary and structural element integration
- **`read_geology.py`**: Geological data processing with SPT integration
  - `_process_borehole_spt_data()` for SPT value processing and validation
  - Borehole location and geological data merging
  - Comprehensive geological parameter validation
- **`read_loading.py`**: Load pattern and case processing
  - `_process_load_pattern_data()` for load type classification
  - `_process_load_case_initial_data()` for load case initialization
  - Multi-header Excel processing for complex load combinations
- **`read_property.py`**: Material property reading and validation
  - Concrete, steel, rebar, tendon property processing
  - Beam, pile, and slab property integration
- **`read_steel.py`**: Steel section library integration
  - H-section steel data processing
  - Steel grade classification and property extraction

## Advanced Project Initialization (`initialization/`)
- **`data_operations.py`**: Core data operations and workflow orchestration
  - `init_input()` with comprehensive error handling and logging
  - `init_output()` for output data structure preparation
  - `update_excel_geology()`, `update_excel_loading()` for data continuity
  - `read_all_input_with_log()` for complete input processing
  - Mini pile spring initialization for specialized foundation types
- **`file_operations.py`**: File management with safe writing operations
  - `safe_write_file()` with UTF-8 encoding for Unicode support
  - `init_file_paths()` for comprehensive file path management
  - Output directory creation with permission handling

## Foundation Design System (`design_fdn/`)
- **`pile_capacity.py`**: Comprehensive pile capacity calculations
  - Multi-pile type support (BP, SHP, DHP, MP) with specialized algorithms
  - Rock parameter integration following GEO TGN 53 and COP 2017 standards
  - Friction capacity calculations for different soil/rock conditions
  - Steel and concrete capacity verification with international codes
- **`data_processing.py`**: Design data processing and optimization
  - `_process_pile_sls_batch()` for efficient SLS load combination processing
  - `cal_pile_sls()` with multi-processing optimization
  - Batch processing for large-scale analysis with progress tracking

## Build FEM Utility System (`build_fem/functions.py`)
- **Geometric Calculations**: Beam, wall, and structural element length/orientation
- **Load Transformations**: Global-to-local coordinate system conversions
- **Core Wall Processing**: Lateral load-resisting system assembly
- **Enhanced Logging Integration**: Comprehensive logging with performance metrics
- **Engineering Conventions**: Right-hand rule coordinates with structural standards