﻿"""
Common utilities for pile layout generation across all cases.

This module contains shared functions used by multiple layout cases to reduce code duplication
and improve maintainability.

Functions:
- Load center calculations (unified for all cases)
- Wall analysis utilities
- Grid generation utilities
- Geometric transformations
- Layout positioning utilities
- Distance and spacing calculations
- Enhanced logging with backward compatibility

Author: Foundation Automation System
"""

import inspect
from math import sqrt, pi, cos, sin, ceil, atan2
from typing import List, Optional, Any, Dict, Tuple, Callable
import numpy as np

from ..data_types import Point2D
from ..pile_cap_geometry.pile_cap_geometry import LocalCoordinateSystem
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_validation_result,
    log_calculation_result,
    log_performance_metric,
    log_error_with_context,
    log_constraint_check,
    create_timed_logger
)

# Module-level logger configuration
MODULE_NAME = "layout_common"
enhanced_log(None, f"Loading {MODULE_NAME} module with enhanced logging support", 'INFO')


def calculate_load_center(cluster_data: dict, log_callback: Optional[Callable] = None) -> Point2D:
    """
    Unified load center calculation for all cases.
    
    Args:
        cluster_data (dict): Dictionary containing load points and structural elements
        log_callback: Optional callback function for logging
    
    Returns:
        Point2D: A tuple (x, y) representing the calculated load center
    """
    log_function_entry(log_callback, "calculate_load_center", cluster_has_data=bool(cluster_data))
    
    try:
        load_points = cluster_data.get('load_points', [])
        
        enhanced_log(log_callback, f"Processing cluster with {len(load_points)} load points", 'DEBUG')
        
        if load_points:
            # Calculate load-weighted centroid
            total_load = sum(load for _, _, load in load_points)
            enhanced_log(log_callback, f"Total load: {total_load:.2f} kN", 'DEBUG')
            
            if total_load > 0:
                load_center_x = sum(x * load for x, y, load in load_points) / total_load
                load_center_y = sum(y * load for x, y, load in load_points) / total_load
                result = (load_center_x, load_center_y)
                
                enhanced_log(log_callback, f"Load-weighted centroid calculated: ({load_center_x:.3f}, {load_center_y:.3f})", 'INFO')
                log_calculation_result(log_callback, "load_center", f"({load_center_x:.3f}, {load_center_y:.3f})", "meters")
                log_validation_result(log_callback, "load_center_calculation", True, f"Total load: {total_load:.2f} kN")
                
                log_function_exit(log_callback, "calculate_load_center", result)
                return result
            else:
                # No loads, use geometric centroid
                enhanced_log(log_callback, "Zero total load detected, using geometric centroid", 'WARNING')
                geometric_center = (
                    sum(x for x, y, _ in load_points) / len(load_points),
                    sum(y for x, y, _ in load_points) / len(load_points)
                )
                
                enhanced_log(log_callback, f"Geometric centroid: ({geometric_center[0]:.3f}, {geometric_center[1]:.3f})", 'INFO')
                log_calculation_result(log_callback, "geometric_center", f"({geometric_center[0]:.3f}, {geometric_center[1]:.3f})", "meters")
                log_validation_result(log_callback, "load_center_calculation", True, "Fallback to geometric centroid")
                
                log_function_exit(log_callback, "calculate_load_center", geometric_center)
                return geometric_center
        
        # No load points available
        enhanced_log(log_callback, "No load points available, returning origin", 'WARNING')
        result = (0.0, 0.0)
        log_validation_result(log_callback, "load_center_calculation", False, "No load points available")
        
        log_function_exit(log_callback, "calculate_load_center", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating load center")
        log_function_exit(log_callback, "calculate_load_center", "error")
        return (0.0, 0.0)


def calculate_overall_load_center(sub_clusters: Dict[str, Dict[str, Any]], log_callback: Optional[Callable] = None) -> Point2D:
    """
    Calculate the overall load center from multiple sub-clusters.
    
    Args:
        sub_clusters: Dictionary of sub-cluster data
        log_callback: Optional callback function for logging
        
    Returns:
        Point2D: Overall load center weighted by cluster loads
    """
    log_function_entry(log_callback, "calculate_overall_load_center", cluster_count=len(sub_clusters))
    
    try:
        total_load = 0.0
        weighted_x = 0.0
        weighted_y = 0.0
        total_load_points = 0
        
        enhanced_log(log_callback, f"Processing {len(sub_clusters)} sub-clusters for overall load center", 'INFO')
        
        for cluster_name, cluster_data in sub_clusters.items():
            load_points = cluster_data.get('load_points', [])
            cluster_load = 0.0
            
            enhanced_log(log_callback, f"Processing cluster '{cluster_name}' with {len(load_points)} load points", 'DEBUG')
            
            for x, y, load in load_points:
                total_load += load
                weighted_x += x * load
                weighted_y += y * load
                cluster_load += load
                total_load_points += 1
            
            enhanced_log(log_callback, f"Cluster '{cluster_name}': {cluster_load:.2f} kN total load", 'DEBUG')
        
        enhanced_log(log_callback, f"Total processing: {total_load_points} load points, {total_load:.2f} kN total load", 'INFO')
        log_calculation_result(log_callback, "total_load", total_load, "kN")
        
        if total_load > 0:
            result = (weighted_x / total_load, weighted_y / total_load)
            enhanced_log(log_callback, f"Overall load center calculated: ({result[0]:.3f}, {result[1]:.3f})", 'INFO')
            log_calculation_result(log_callback, "overall_load_center", f"({result[0]:.3f}, {result[1]:.3f})", "meters")
            log_validation_result(log_callback, "overall_load_center_calculation", True, f"Total load: {total_load:.2f} kN")
            
            log_function_exit(log_callback, "calculate_overall_load_center", result)
            return result
        
        enhanced_log(log_callback, "No loads found across all clusters, returning origin", 'WARNING')
        result = (0.0, 0.0)
        log_validation_result(log_callback, "overall_load_center_calculation", False, "No loads found")
        
        log_function_exit(log_callback, "calculate_overall_load_center", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating overall load center")
        log_function_exit(log_callback, "calculate_overall_load_center", "error")
        return (0.0, 0.0)


def find_primary_wall(walls: List[Any], log_callback: Optional[Callable] = None) -> Optional[Any]:
    """
    Find the primary (longest) wall from a list of walls.
    
    Args:
        walls: List of wall objects with geometric information
        log_callback: Optional callback function for logging
        
    Returns:
        The longest wall object if found, otherwise None
    """
    log_function_entry(log_callback, "find_primary_wall", wall_count=len(walls))
    
    try:
        if not walls:
            enhanced_log(log_callback, "No walls provided for primary wall selection", 'DEBUG')
            log_validation_result(log_callback, "primary_wall_selection", False, "No walls available")
            log_function_exit(log_callback, "find_primary_wall", None)
            return None
        
        enhanced_log(log_callback, f"Analyzing {len(walls)} walls to find primary wall", 'INFO')
        
        if len(walls) == 1:
            enhanced_log(log_callback, "Single wall found, selecting as primary", 'DEBUG')
            log_validation_result(log_callback, "primary_wall_selection", True, "Single wall automatically selected")
            log_function_exit(log_callback, "find_primary_wall", "single_wall")
            return walls[0]
        
        # Find longest wall
        longest_wall = None
        max_length = 0.0
        valid_walls = 0
        
        enhanced_log(log_callback, "Calculating wall lengths to find longest wall", 'DEBUG')
        
        for i, wall in enumerate(walls):
            try:
                if len(wall) >= 2:
                    wall_points = wall[1] if isinstance(wall[1], list) else []
                    if len(wall_points) >= 2:
                        start_point = wall_points[0]
                        end_point = wall_points[-1]
                        
                        if (isinstance(start_point, (list, tuple)) and len(start_point) >= 2 and
                            isinstance(end_point, (list, tuple)) and len(end_point) >= 2):
                            
                            wall_length = calculate_distance(start_point, end_point)
                            valid_walls += 1
                            
                            enhanced_log(log_callback, f"Wall {i}: length = {wall_length:.3f}m", 'DEBUG')
                            
                            if wall_length > max_length:
                                max_length = wall_length
                                longest_wall = wall
                                enhanced_log(log_callback, f"New longest wall found: {wall_length:.3f}m", 'DEBUG')
                        else:
                            enhanced_log(log_callback, f"Wall {i}: invalid point format", 'WARNING')
                    else:
                        enhanced_log(log_callback, f"Wall {i}: insufficient points ({len(wall_points)})", 'WARNING')
                else:
                    enhanced_log(log_callback, f"Wall {i}: invalid wall structure", 'WARNING')
                    
            except Exception as e:
                enhanced_log(log_callback, f"Error processing wall {i}: {str(e)}", 'ERROR')
                continue
        
        if longest_wall is not None:
            enhanced_log(log_callback, f"Primary wall selected: {max_length:.3f}m length from {valid_walls} valid walls", 'INFO')
            log_calculation_result(log_callback, "primary_wall_length", max_length, "meters")
            log_validation_result(log_callback, "primary_wall_selection", True, f"Length: {max_length:.3f}m")
        else:
            enhanced_log(log_callback, f"No valid primary wall found from {len(walls)} walls", 'WARNING')
            log_validation_result(log_callback, "primary_wall_selection", False, "No valid walls found")
        
        log_function_exit(log_callback, "find_primary_wall", f"length={max_length:.3f}m" if longest_wall else None)
        return longest_wall
        
    except Exception as e:
        log_error_with_context(log_callback, e, "finding primary wall")
        log_function_exit(log_callback, "find_primary_wall", "error")
        return None


def calculate_distance(point1: Point2D, point2: Point2D, log_callback: Optional[Callable] = None) -> float:
    """
    Calculate Euclidean distance between two points.
    
    Args:
        point1: First point (x, y)
        point2: Second point (x, y)
        log_callback: Optional callback function for logging
        
    Returns:
        Distance between the points
    """
    log_function_entry(log_callback, "calculate_distance", point1=point1, point2=point2)
    
    try:
        distance = sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
        
        enhanced_log(log_callback, f"Distance calculated: {distance:.6f}m between ({point1[0]:.3f}, {point1[1]:.3f}) and ({point2[0]:.3f}, {point2[1]:.3f})", 'DEBUG')
        log_calculation_result(log_callback, "euclidean_distance", distance, "meters")
        
        log_function_exit(log_callback, "calculate_distance", distance)
        return distance
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating distance between points")
        log_function_exit(log_callback, "calculate_distance", "error")
        return 0.0


def calculate_polygon_radius(num_piles: int, min_spacing: float, log_callback: Optional[Callable] = None) -> float:
    """
    Calculate the radius needed for a regular polygon layout to maintain minimum spacing.
    
    Args:
        num_piles: Number of piles in the polygon
        min_spacing: Minimum spacing between adjacent piles
        log_callback: Optional callback function for logging
        
    Returns:
        Required radius for the polygon
    """
    log_function_entry(log_callback, "calculate_polygon_radius", num_piles=num_piles, min_spacing=min_spacing)
    
    try:
        # Validate inputs
        if num_piles <= 0:
            enhanced_log(log_callback, f"Invalid number of piles: {num_piles}", 'ERROR')
            log_validation_result(log_callback, "polygon_radius_calculation", False, "Invalid pile count")
            log_function_exit(log_callback, "calculate_polygon_radius", "error")
            return 0.0
        
        if min_spacing <= 0:
            enhanced_log(log_callback, f"Invalid minimum spacing: {min_spacing}", 'ERROR')
            log_validation_result(log_callback, "polygon_radius_calculation", False, "Invalid spacing")
            log_function_exit(log_callback, "calculate_polygon_radius", "error")
            return 0.0
        
        enhanced_log(log_callback, f"Calculating polygon radius for {num_piles} piles with {min_spacing:.3f}m spacing", 'DEBUG')
        
        angle_between_piles = 2 * pi / num_piles
        radius = min_spacing / (2 * sin(angle_between_piles / 2))
        
        enhanced_log(log_callback, f"Polygon layout: angle between piles = {angle_between_piles:.6f} rad ({angle_between_piles * 180 / pi:.2f}°)", 'DEBUG')
        enhanced_log(log_callback, f"Required polygon radius: {radius:.3f}m", 'INFO')
        
        log_calculation_result(log_callback, "polygon_radius", radius, "meters")
        log_constraint_check(log_callback, "minimum_spacing", min_spacing, min_spacing, True)
        log_validation_result(log_callback, "polygon_radius_calculation", True, f"Radius: {radius:.3f}m")
        
        log_function_exit(log_callback, "calculate_polygon_radius", radius)
        return radius
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating polygon radius")
        log_function_exit(log_callback, "calculate_polygon_radius", "error")
        return 0.0


def generate_polygon_positions(center: Point2D, num_piles: int, radius: float, log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate positions for a regular polygon layout.
    
    Args:
        center: Center point of the polygon
        num_piles: Number of piles
        radius: Radius of the polygon
        log_callback: Optional callback function for logging
        
    Returns:
        List of pile positions
    """
    log_function_entry(log_callback, "generate_polygon_positions", center=center, num_piles=num_piles, radius=radius)
    
    try:
        # Validate inputs
        if num_piles <= 0:
            enhanced_log(log_callback, f"Invalid number of piles: {num_piles}", 'ERROR')
            log_validation_result(log_callback, "polygon_position_generation", False, "Invalid pile count")
            log_function_exit(log_callback, "generate_polygon_positions", "error")
            return []
        
        if radius <= 0:
            enhanced_log(log_callback, f"Invalid radius: {radius}", 'ERROR')
            log_validation_result(log_callback, "polygon_position_generation", False, "Invalid radius")
            log_function_exit(log_callback, "generate_polygon_positions", "error")
            return []
        
        enhanced_log(log_callback, f"Generating {num_piles} polygon positions at center ({center[0]:.3f}, {center[1]:.3f}) with radius {radius:.3f}m", 'INFO')
        
        positions = []
        angle_between_piles = 2 * pi / num_piles
        
        enhanced_log(log_callback, f"Angle between piles: {angle_between_piles:.6f} rad ({angle_between_piles * 180 / pi:.2f}°)", 'DEBUG')
        
        for i in range(num_piles):
            angle = i * angle_between_piles
            x = center[0] + radius * cos(angle)
            y = center[1] + radius * sin(angle)
            positions.append((x, y))
            
            if i < 3:  # Log first 3 positions for debugging
                enhanced_log(log_callback, f"Pile {i}: angle={angle:.3f} rad, position=({x:.3f}, {y:.3f})", 'DEBUG')
        
        enhanced_log(log_callback, f"Generated {len(positions)} polygon positions successfully", 'INFO')
        log_calculation_result(log_callback, "polygon_positions_generated", len(positions), "positions")
        
        # Validate spacing for first few positions
        if len(positions) >= 2:
            first_spacing = calculate_distance(positions[0], positions[1])
            enhanced_log(log_callback, f"Verification: spacing between first two piles: {first_spacing:.3f}m", 'DEBUG')
            
        log_validation_result(log_callback, "polygon_position_generation", True, f"Generated {len(positions)} positions")
        
        log_function_exit(log_callback, "generate_polygon_positions", f"{len(positions)} positions")
        return positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generating polygon positions")
        log_function_exit(log_callback, "generate_polygon_positions", "error")
        return []


def apply_coordinate_transformation(positions: List[Point2D], 
                                  translation: Point2D = (0.0, 0.0),
                                  rotation_angle: float = 0.0,
                                  log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Apply coordinate transformation (translation and rotation) to a list of positions.
    
    Args:
        positions: List of positions to transform
        translation: Translation vector (dx, dy)
        rotation_angle: Rotation angle in radians
        log_callback: Optional callback function for logging
        
    Returns:
        List of transformed positions
    """
    log_function_entry(log_callback, "apply_coordinate_transformation", 
                      position_count=len(positions), translation=translation, rotation_angle=rotation_angle)
    
    try:
        enhanced_log(log_callback, f"Applying coordinate transformation to {len(positions)} positions", 'INFO')
        enhanced_log(log_callback, f"Translation: ({translation[0]:.3f}, {translation[1]:.3f})", 'DEBUG')
        enhanced_log(log_callback, f"Rotation: {rotation_angle:.6f} rad ({rotation_angle * 180 / pi:.2f}°)", 'DEBUG')
        
        # Check for identity transformation
        if rotation_angle == 0.0 and translation == (0.0, 0.0):
            enhanced_log(log_callback, "Identity transformation detected, returning copy of original positions", 'DEBUG')
            result = positions.copy()
            log_validation_result(log_callback, "coordinate_transformation", True, "Identity transformation")
            log_function_exit(log_callback, "apply_coordinate_transformation", f"{len(result)} positions")
            return result
        
        # Pre-calculate trigonometric values
        cos_angle = cos(rotation_angle)
        sin_angle = sin(rotation_angle)
        
        enhanced_log(log_callback, f"Transformation matrix: cos={cos_angle:.6f}, sin={sin_angle:.6f}", 'DEBUG')
        
        transformed_positions = []
        for i, (x, y) in enumerate(positions):
            # Apply rotation first
            if rotation_angle != 0.0:
                rotated_x = x * cos_angle - y * sin_angle
                rotated_y = x * sin_angle + y * cos_angle
            else:
                rotated_x, rotated_y = x, y
            
            # Apply translation
            final_x = rotated_x + translation[0]
            final_y = rotated_y + translation[1]
            
            transformed_positions.append((final_x, final_y))
            
            # Log first few transformations for debugging
            if i < 3:
                enhanced_log(log_callback, f"Position {i}: ({x:.3f}, {y:.3f}) → ({final_x:.3f}, {final_y:.3f})", 'DEBUG')
        
        enhanced_log(log_callback, f"Coordinate transformation completed: {len(transformed_positions)} positions transformed", 'INFO')
        log_calculation_result(log_callback, "positions_transformed", len(transformed_positions), "positions")
        
        # Validate transformation
        if len(transformed_positions) == len(positions):
            log_validation_result(log_callback, "coordinate_transformation", True, f"Transformed {len(transformed_positions)} positions")
        else:
            enhanced_log(log_callback, f"Warning: position count mismatch after transformation", 'WARNING')
            log_validation_result(log_callback, "coordinate_transformation", False, "Position count mismatch")
        
        log_function_exit(log_callback, "apply_coordinate_transformation", f"{len(transformed_positions)} positions")
        return transformed_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "applying coordinate transformation")
        log_function_exit(log_callback, "apply_coordinate_transformation", "error")
        return positions.copy()  # Return original positions on error


def get_axis_directions(local_system: Optional[LocalCoordinateSystem], log_callback: Optional[Callable] = None) -> Tuple[Point2D, Point2D]:
    """
    Get the x and y axis directions from a local coordinate system.
    
    Args:
        local_system: Optional local coordinate system
        log_callback: Optional callback function for logging
        
    Returns:
        Tuple of (x_axis_direction, y_axis_direction)
    """
    log_function_entry(log_callback, "get_axis_directions", has_local_system=local_system is not None)
    
    try:
        if local_system:
            rotation_angle = local_system.rotation_angle
            enhanced_log(log_callback, f"Local coordinate system detected with rotation angle: {rotation_angle:.6f} rad ({rotation_angle * 180 / pi:.2f}°)", 'DEBUG')
            
            x_axis_direction = (cos(rotation_angle), sin(rotation_angle))
            y_axis_direction = (-sin(rotation_angle), cos(rotation_angle))
            
            enhanced_log(log_callback, f"X-axis direction: ({x_axis_direction[0]:.6f}, {x_axis_direction[1]:.6f})", 'DEBUG')
            enhanced_log(log_callback, f"Y-axis direction: ({y_axis_direction[0]:.6f}, {y_axis_direction[1]:.6f})", 'DEBUG')
            
            log_calculation_result(log_callback, "axis_directions", f"rotated by {rotation_angle * 180 / pi:.2f}°", "degrees")
            log_validation_result(log_callback, "axis_directions_calculation", True, "Local coordinate system applied")
        else:
            enhanced_log(log_callback, "No local coordinate system provided, using global axes", 'DEBUG')
            x_axis_direction = (1.0, 0.0)
            y_axis_direction = (0.0, 1.0)
            
            log_calculation_result(log_callback, "axis_directions", "global axes", "standard")
            log_validation_result(log_callback, "axis_directions_calculation", True, "Global coordinate system used")
        
        result = (x_axis_direction, y_axis_direction)
        log_function_exit(log_callback, "get_axis_directions", f"x={x_axis_direction}, y={y_axis_direction}")
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "getting axis directions")
        # Return default global axes on error
        result = ((1.0, 0.0), (0.0, 1.0))
        log_function_exit(log_callback, "get_axis_directions", "error")
        return result


def generate_line_positions(center: Point2D, num_piles: int, spacing: float, 
                          direction: Point2D = (1.0, 0.0), log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate positions for piles arranged in a line.
    
    Args:
        center: Center point of the line
        num_piles: Number of piles
        spacing: Spacing between piles
        direction: Direction vector (normalized)
        log_callback: Optional callback function for logging
        
    Returns:
        List of pile positions along the line
    """
    log_function_entry(log_callback, "generate_line_positions", 
                      center=center, num_piles=num_piles, spacing=spacing, direction=direction)
    
    try:
        # Validate inputs
        if num_piles <= 0:
            enhanced_log(log_callback, f"Invalid number of piles: {num_piles}", 'ERROR')
            log_validation_result(log_callback, "line_position_generation", False, "Invalid pile count")
            log_function_exit(log_callback, "generate_line_positions", "error")
            return []
        
        if spacing <= 0:
            enhanced_log(log_callback, f"Invalid spacing: {spacing}", 'ERROR')
            log_validation_result(log_callback, "line_position_generation", False, "Invalid spacing")
            log_function_exit(log_callback, "generate_line_positions", "error")
            return []
        
        enhanced_log(log_callback, f"Generating {num_piles} line positions at center ({center[0]:.3f}, {center[1]:.3f})", 'INFO')
        enhanced_log(log_callback, f"Line parameters: spacing={spacing:.3f}m, direction=({direction[0]:.3f}, {direction[1]:.3f})", 'DEBUG')
        
        if num_piles == 1:
            enhanced_log(log_callback, "Single pile layout, returning center position", 'DEBUG')
            result = [center]
            log_validation_result(log_callback, "line_position_generation", True, "Single pile at center")
            log_function_exit(log_callback, "generate_line_positions", "1 position")
            return result
        
        # Calculate start offset to center the line
        total_length = (num_piles - 1) * spacing
        start_offset = -total_length / 2
        
        enhanced_log(log_callback, f"Line layout: total_length={total_length:.3f}m, start_offset={start_offset:.3f}m", 'DEBUG')
        
        positions = []
        for i in range(num_piles):
            offset = start_offset + i * spacing
            x = center[0] + offset * direction[0]
            y = center[1] + offset * direction[1]
            positions.append((x, y))
            
            if i < 3:  # Log first 3 positions for debugging
                enhanced_log(log_callback, f"Pile {i}: offset={offset:.3f}m, position=({x:.3f}, {y:.3f})", 'DEBUG')
        
        enhanced_log(log_callback, f"Generated {len(positions)} line positions successfully", 'INFO')
        log_calculation_result(log_callback, "line_positions_generated", len(positions), "positions")
        
        # Validate spacing
        if len(positions) >= 2:
            actual_spacing = calculate_distance(positions[0], positions[1])
            enhanced_log(log_callback, f"Verification: spacing between adjacent piles: {actual_spacing:.3f}m", 'DEBUG')
            spacing_matches = abs(actual_spacing - spacing) < 1e-6
            log_constraint_check(log_callback, "line_spacing", actual_spacing, spacing, spacing_matches)
        
        log_validation_result(log_callback, "line_position_generation", True, f"Generated {len(positions)} positions")
        
        log_function_exit(log_callback, "generate_line_positions", f"{len(positions)} positions")
        return positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generating line positions")
        log_function_exit(log_callback, "generate_line_positions", "error")
        return []


def generate_symmetric_positions(base_positions: List[Point2D], 
                               include_origin: bool = False, log_callback: Optional[Callable] = None) -> List[Point2D]:
    """
    Generate symmetric positions including reflections across axes.
    
    Args:
        base_positions: Base positions to generate symmetries for
        include_origin: Whether to include the origin (0,0) if not already present
        log_callback: Optional callback function for logging
        
    Returns:
        List of all symmetric positions
    """
    log_function_entry(log_callback, "generate_symmetric_positions", 
                      base_count=len(base_positions), include_origin=include_origin)
    
    try:
        enhanced_log(log_callback, f"Generating symmetric positions from {len(base_positions)} base positions", 'INFO')
        enhanced_log(log_callback, f"Include origin: {include_origin}", 'DEBUG')
        
        all_positions = []
        seen = set()
        
        # Add base positions
        enhanced_log(log_callback, "Adding base positions", 'DEBUG')
        for i, pos in enumerate(base_positions):
            if pos not in seen:
                all_positions.append(pos)
                seen.add(pos)
                if i < 3:  # Log first few base positions
                    enhanced_log(log_callback, f"Base position {i}: ({pos[0]:.3f}, {pos[1]:.3f})", 'DEBUG')
        
        original_count = len(all_positions)
        enhanced_log(log_callback, f"Added {original_count} unique base positions", 'DEBUG')
        
        # Add symmetric reflections
        enhanced_log(log_callback, "Generating symmetric reflections", 'DEBUG')
        reflections_added = 0
        
        for x, y in base_positions:
            symmetric_candidates = []
            
            # Original position (already added above)
            # Reflection across x-axis
            if y != 0:
                symmetric_candidates.append((x, -y))
            
            # Reflection across y-axis
            if x != 0:
                symmetric_candidates.append((-x, y))
            
            # Reflection across origin
            if x != 0 and y != 0:
                symmetric_candidates.append((-x, -y))
            
            # Add candidates if not already seen
            for candidate in symmetric_candidates:
                if candidate not in seen:
                    all_positions.append(candidate)
                    seen.add(candidate)
                    reflections_added += 1
        
        enhanced_log(log_callback, f"Added {reflections_added} symmetric reflections", 'DEBUG')
        
        # Add origin if requested and not already present
        origin_added = False
        if include_origin and (0.0, 0.0) not in seen:
            all_positions.append((0.0, 0.0))
            origin_added = True
            enhanced_log(log_callback, "Added origin (0,0) to symmetric positions", 'DEBUG')
        
        enhanced_log(log_callback, f"Symmetric position generation completed: {len(all_positions)} total positions", 'INFO')
        log_calculation_result(log_callback, "symmetric_positions_generated", len(all_positions), "positions")
        
        # Log generation statistics
        enhanced_log(log_callback, f"Generation breakdown: {original_count} base + {reflections_added} reflections" + 
                    (f" + 1 origin" if origin_added else ""), 'DEBUG')
        
        log_validation_result(log_callback, "symmetric_position_generation", len(all_positions) > 0, 
                            f"Generated {len(all_positions)} positions")
        
        log_function_exit(log_callback, "generate_symmetric_positions", f"{len(all_positions)} positions")
        return all_positions
        
    except Exception as e:
        log_error_with_context(log_callback, e, "generating symmetric positions")
        log_function_exit(log_callback, "generate_symmetric_positions", "error")
        return base_positions.copy()  # Return original positions on error


def calculate_grid_dimensions(total_piles: int, max_piles_per_row: Optional[int] = None, log_callback: Optional[Callable] = None) -> Tuple[int, int]:
    """
    Calculate optimal grid dimensions for a given number of piles.
    
    Args:
        total_piles: Total number of piles to arrange
        max_piles_per_row: Maximum piles per row (optional constraint)
        log_callback: Optional callback function for logging
        
    Returns:
        Tuple of (rows, cols) for the grid
    """
    log_function_entry(log_callback, "calculate_grid_dimensions", 
                      total_piles=total_piles, max_piles_per_row=max_piles_per_row)
    
    try:
        enhanced_log(log_callback, f"Calculating optimal grid dimensions for {total_piles} piles", 'INFO')
        if max_piles_per_row:
            enhanced_log(log_callback, f"Maximum piles per row constraint: {max_piles_per_row}", 'DEBUG')
        
        # Validate input
        if total_piles <= 0:
            enhanced_log(log_callback, f"Invalid pile count: {total_piles}", 'ERROR')
            result = (0, 0)
            log_validation_result(log_callback, "grid_dimension_calculation", False, "Invalid pile count")
            log_function_exit(log_callback, "calculate_grid_dimensions", result)
            return result
        
        if total_piles == 1:
            enhanced_log(log_callback, "Single pile grid: 1x1", 'DEBUG')
            result = (1, 1)
            log_validation_result(log_callback, "grid_dimension_calculation", True, "Single pile")
            log_function_exit(log_callback, "calculate_grid_dimensions", result)
            return result
        
        # Try to make a roughly square grid
        sqrt_piles = int(sqrt(total_piles))
        enhanced_log(log_callback, f"Square root approximation: {sqrt_piles}", 'DEBUG')
        
        # Find the best factorization
        best_rows = sqrt_piles
        best_cols = ceil(total_piles / best_rows)
        
        enhanced_log(log_callback, f"Initial grid calculation: {best_rows}x{best_cols} = {best_rows * best_cols}", 'DEBUG')
        
        # Apply max_piles_per_row constraint if specified
        if max_piles_per_row and best_cols > max_piles_per_row:
            enhanced_log(log_callback, f"Applying column constraint: {best_cols} > {max_piles_per_row}", 'DEBUG')
            best_cols = max_piles_per_row
            best_rows = ceil(total_piles / best_cols)
            enhanced_log(log_callback, f"Constrained grid: {best_rows}x{best_cols} = {best_rows * best_cols}", 'DEBUG')
        
        result = (best_rows, best_cols)
        grid_capacity = best_rows * best_cols
        efficiency = total_piles / grid_capacity if grid_capacity > 0 else 0
        
        enhanced_log(log_callback, f"Final grid dimensions: {best_rows}x{best_cols} (capacity: {grid_capacity}, efficiency: {efficiency:.2%})", 'INFO')
        log_calculation_result(log_callback, "grid_dimensions", f"{best_rows}x{best_cols}", "rows x cols")
        log_calculation_result(log_callback, "grid_efficiency", f"{efficiency:.2%}", "percent")
        
        # Constraint checks
        if max_piles_per_row:
            log_constraint_check(log_callback, "max_piles_per_row", best_cols, max_piles_per_row, best_cols <= max_piles_per_row)
        
        log_validation_result(log_callback, "grid_dimension_calculation", True, f"{best_rows}x{best_cols} grid")
        
        log_function_exit(log_callback, "calculate_grid_dimensions", result)
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating grid dimensions")
        result = (1, total_piles) if total_piles > 0 else (0, 0)  # Fallback to single row
        log_function_exit(log_callback, "calculate_grid_dimensions", "error")
        return result


def extract_wall_geometry(wall: Any, log_callback: Optional[Callable] = None) -> Optional[Tuple[Point2D, Point2D, Point2D, float]]:
    """
    Extract geometric properties from a wall object.
    
    Args:
        wall: Wall object containing geometric information
        log_callback: Optional callback function for logging
        
    Returns:
        Tuple of (start_point, end_point, direction_vector, length) or None if invalid
    """
    log_function_entry(log_callback, "extract_wall_geometry", wall_provided=wall is not None)
    
    try:
        enhanced_log(log_callback, "Extracting geometric properties from wall object", 'DEBUG')
        
        # Validate wall structure
        if len(wall) < 2:
            enhanced_log(log_callback, f"Invalid wall structure: insufficient elements ({len(wall)})", 'WARNING')
            log_validation_result(log_callback, "wall_geometry_extraction", False, "Insufficient wall elements")
            log_function_exit(log_callback, "extract_wall_geometry", None)
            return None
        
        wall_points = wall[1] if isinstance(wall[1], list) else []
        enhanced_log(log_callback, f"Wall contains {len(wall_points)} points", 'DEBUG')
        
        if len(wall_points) < 2:
            enhanced_log(log_callback, f"Invalid wall points: insufficient points ({len(wall_points)})", 'WARNING')
            log_validation_result(log_callback, "wall_geometry_extraction", False, "Insufficient wall points")
            log_function_exit(log_callback, "extract_wall_geometry", None)
            return None
        
        # Extract start and end points
        try:
            start_point = (float(wall_points[0][0]), float(wall_points[0][1]))
            end_point = (float(wall_points[-1][0]), float(wall_points[-1][1]))
            
            enhanced_log(log_callback, f"Wall endpoints: start=({start_point[0]:.3f}, {start_point[1]:.3f}), "
                                      f"end=({end_point[0]:.3f}, {end_point[1]:.3f})", 'DEBUG')
            
        except (ValueError, TypeError, IndexError) as e:
            enhanced_log(log_callback, f"Error extracting wall endpoints: {str(e)}", 'ERROR')
            log_validation_result(log_callback, "wall_geometry_extraction", False, "Invalid point coordinates")
            log_function_exit(log_callback, "extract_wall_geometry", None)
            return None
        
        # Calculate wall vector and length
        wall_vector = (end_point[0] - start_point[0], end_point[1] - start_point[1])
        wall_length = sqrt(wall_vector[0]**2 + wall_vector[1]**2)
        
        enhanced_log(log_callback, f"Wall vector: ({wall_vector[0]:.3f}, {wall_vector[1]:.3f})", 'DEBUG')
        enhanced_log(log_callback, f"Wall length: {wall_length:.3f}m", 'DEBUG')
        
        if wall_length == 0:
            enhanced_log(log_callback, "Zero-length wall detected", 'WARNING')
            log_validation_result(log_callback, "wall_geometry_extraction", False, "Zero-length wall")
            log_function_exit(log_callback, "extract_wall_geometry", None)
            return None
        
        # Normalize direction vector
        direction_vector = (wall_vector[0] / wall_length, wall_vector[1] / wall_length)
        
        enhanced_log(log_callback, f"Normalized direction: ({direction_vector[0]:.6f}, {direction_vector[1]:.6f})", 'DEBUG')
        
        result = (start_point, end_point, direction_vector, wall_length)
        
        enhanced_log(log_callback, f"Wall geometry extraction successful: length={wall_length:.3f}m", 'INFO')
        log_calculation_result(log_callback, "wall_length", wall_length, "meters")
        log_calculation_result(log_callback, "wall_direction", f"({direction_vector[0]:.3f}, {direction_vector[1]:.3f})", "normalized")
        log_validation_result(log_callback, "wall_geometry_extraction", True, f"Length: {wall_length:.3f}m")
        
        log_function_exit(log_callback, "extract_wall_geometry", f"length={wall_length:.3f}m")
        return result
        
    except Exception as e:
        log_error_with_context(log_callback, e, "extracting wall geometry")
        log_function_exit(log_callback, "extract_wall_geometry", "error")
        return None


def calculate_perpendicular_direction(direction: Point2D, log_callback: Optional[Callable] = None) -> Point2D:
    """
    Calculate the perpendicular direction to a given direction vector.
    
    Args:
        direction: Direction vector (should be normalized)
        log_callback: Optional callback function for logging
        
    Returns:
        Perpendicular direction vector (90 degrees counter-clockwise)
    """
    log_function_entry(log_callback, "calculate_perpendicular_direction", direction=direction)
    
    try:
        enhanced_log(log_callback, f"Calculating perpendicular direction for vector ({direction[0]:.6f}, {direction[1]:.6f})", 'DEBUG')
        
        # Validate input direction
        magnitude = sqrt(direction[0]**2 + direction[1]**2)
        enhanced_log(log_callback, f"Input direction magnitude: {magnitude:.6f}", 'DEBUG')
        
        if magnitude == 0:
            enhanced_log(log_callback, "Zero-magnitude direction vector", 'WARNING')
            log_validation_result(log_callback, "perpendicular_direction_calculation", False, "Zero-magnitude vector")
            result = (0.0, 0.0)
            log_function_exit(log_callback, "calculate_perpendicular_direction", result)
            return result
        
        # Calculate perpendicular vector (90 degrees counter-clockwise)
        perpendicular = (-direction[1], direction[0])
        
        # Verify perpendicular magnitude
        perp_magnitude = sqrt(perpendicular[0]**2 + perpendicular[1]**2)
        
        enhanced_log(log_callback, f"Perpendicular direction: ({perpendicular[0]:.6f}, {perpendicular[1]:.6f})", 'DEBUG')
        enhanced_log(log_callback, f"Perpendicular magnitude: {perp_magnitude:.6f}", 'DEBUG')
        
        # Verify orthogonality (dot product should be zero)
        dot_product = direction[0] * perpendicular[0] + direction[1] * perpendicular[1]
        enhanced_log(log_callback, f"Orthogonality check (dot product): {dot_product:.10f}", 'DEBUG')
        
        is_orthogonal = abs(dot_product) < 1e-10
        log_constraint_check(log_callback, "orthogonality", abs(dot_product), 1e-10, is_orthogonal)
        
        enhanced_log(log_callback, f"Perpendicular direction calculation successful", 'INFO')
        log_calculation_result(log_callback, "perpendicular_direction", f"({perpendicular[0]:.6f}, {perpendicular[1]:.6f})", "normalized")
        log_validation_result(log_callback, "perpendicular_direction_calculation", True, 
                            f"Magnitude: {perp_magnitude:.6f}, Orthogonal: {is_orthogonal}")
        
        log_function_exit(log_callback, "calculate_perpendicular_direction", perpendicular)
        return perpendicular
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculating perpendicular direction")
        # Return a default perpendicular direction on error
        result = (0.0, 1.0) if abs(direction[0]) > abs(direction[1]) else (-1.0, 0.0)
        log_function_exit(log_callback, "calculate_perpendicular_direction", "error")
        return result

