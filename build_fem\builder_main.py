"""
Foundation-Automation SAFE Model Builder - Main Automation Controller

This module provides the main automation functions for the Foundation-Automation system,
orchestrating the complete workflow from input initialization through SAFE model generation.
It serves as a high-level controller that coordinates various subsystems including:

- File path initialization and validation
- Input data processing and validation
- Soil spring generation for geotechnical modeling
- SAFE 16/22 model generation with structural properties

Key Features:
- Automated input/output initialization with comprehensive error handling
- Support for both new and existing project data integration
- Lateral soil spring generation for pile-soil interaction modeling
- SAFE model generation with design strip options
- Progress and logging callback support for GUI integration
- Robust error handling with detailed diagnostics

Workflow Overview:
1. Initialize file paths and validate project structure
2. Load and process input data from Excel files
3. Optionally integrate existing project data
4. Generate lateral soil springs based on geotechnical parameters
5. Create SAFE models with structural properties and load combinations
6. Export models in both SAFE 16 and SAFE 22 formats

Dependencies:
- initialization.data_operations: Data loading and Excel processing
- initialization.error_handling: Standardized error reporting
- initialization.file_operations: File path management and validation
- build_fem.gen_safe: SAFE model generation engine
- build_fem.gen_soil_spring: Soil spring calculation module
- build_fem.build_fem_config: Configuration and version management

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 structural analysis software
"""

from initialization.data_operations import init_input, init_output, update_excel_geology, update_excel_loading, \
    read_all_input_with_log, update_excel_property
from initialization.error_handling import get_error_details
from initialization.file_operations import init_file_paths, init_existing_file_paths
from build_fem import gen_safe
from build_fem import build_fem_config as config

def automated_initialize_setup(input_folder, existing_folder=None, log_callback=None):
    """
    Automated initialization of file paths and input data for FEM workflow.
    
    Performs complete setup of the Foundation Automation system including file path
    initialization, Excel data loading, existing data integration, and output
    preparation. This function orchestrates the entire input preparation workflow
    required for subsequent SAFE model generation.
    """
    try:
        # Validate input folder parameter
        if not input_folder:
            error_msg = "Error: No input folder specified - cannot proceed with initialization"
            if log_callback:
                log_callback(error_msg)
            return None, None, None

        # Initialize project file path structure and validate file accessibility
        if log_callback:
            log_callback(f"Initializing file paths for project: {input_folder}")
        file_paths = init_file_paths(input_folder, log_callback)
        if not file_paths:
            if log_callback:
                log_callback("Failed to initialize file paths - check folder structure and permissions")
            return None, None, None        # Initialize existing project file paths if provided for data continuity
        if existing_folder:
            if log_callback:
                log_callback(f"Integrating existing project data from: {existing_folder}")
            try:
                file_paths = init_existing_file_paths(file_paths, existing_folder, log_callback)
            except Exception as e:
                error_details = get_error_details(e, 'init_existing_file_paths')
                if log_callback:
                    log_callback(f"Warning: Failed to initialize existing file paths: {error_details['error_message']}")
                    log_callback("Continuing without existing files - new project setup will be used")        # Load and validate all Excel input files (geometry, loading, geology, properties)
        if log_callback:
            log_callback("Loading Excel input files and validating data structure...")
        try:
            excel_inputs = init_input(file_paths, log_callback)
        except Exception as e:
            error_details = get_error_details(e, 'init_input')
            error_msg = f"Failed to initialize inputs: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
                log_callback("Check Excel file format and data integrity")
            return None, None, None

        # Update Excel property data with latest material and section definitions
        # This ensures material properties are current and consistent with design standards
        if log_callback:
            log_callback("Updating material properties and section definitions...")
        try:
            excel_inputs = update_excel_property(file_paths, excel_inputs, log_callback)
        except Exception as e:
            error_details = get_error_details(e, 'update_excel_property')
            if log_callback:
                log_callback(f"Warning: Failed to update Excel property: {error_details['error_message']}")
                log_callback("Continuing with existing property data - may affect material assignments")

        # Integrate data from existing project files if available for continuity
        if existing_folder:
            # Update geological data from existing project - preserves site-specific soil parameters
            if log_callback:
                log_callback("Updating geological data from existing project...")
            try:
                excel_inputs = update_excel_geology(file_paths, excel_inputs, log_callback)
            except Exception as e:
                error_details = get_error_details(e, 'update_excel_geology')
                if log_callback:
                    log_callback(f"Warning: Failed to update geology from existing: {error_details['error_message']}")
                    log_callback("Continuing with default geological parameters")

            # Update loading data from existing project - maintains load case consistency
            if log_callback:
                log_callback("Updating loading data from existing project...")
            try:
                excel_inputs = update_excel_loading(file_paths, excel_inputs, log_callback)
            except Exception as e:
                error_details = get_error_details(e, 'update_excel_loading')
                if log_callback:
                    log_callback(f"Warning: Failed to update loading from existing: {error_details['error_message']}")
                    log_callback("Continuing with default loading patterns")

        # Initialize output data structures for results storage
        if log_callback:
            log_callback("Initializing output data structures...")
        try:
            excel_outputs = init_output(excel_inputs, log_callback)
        except Exception as e:
            error_details = get_error_details(e, 'init_output')
            error_msg = f"Failed to initialize outputs: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
                log_callback("Check output template configuration")
            return None, None, None

        if log_callback:
            log_callback("Initialization completed successfully - ready for analysis workflow")

        return file_paths, excel_inputs, excel_outputs

    except Exception as e:
        error_details = get_error_details(e, 'automated_initialize_setup')
        error_msg = (f"Critical error during initialization: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
        return None, None, None


def automated_generate_soil_spring(file_paths, excel_inputs, log_callback=None, progress_callback=None):
    """
    Automated generation of lateral soil springs for pile-soil interaction modeling.
    
    This function generates lateral soil spring coefficients based on geotechnical
    parameters and pile geometry. The soil springs represent the soil-pile interaction
    behavior for lateral loading conditions in foundation analysis.

    The soil spring generation process includes:
    - Input data validation and refreshing from Excel files
    - Geotechnical parameter processing and validation
    - Pile geometry analysis and segmentation
    - Lateral soil spring coefficient calculation using established methods
    - Export of results to Excel templates for SAFE model integration
    """
    try:
        if not file_paths:
            error_msg = "Error: File paths object is None"
            if log_callback:
                log_callback(error_msg)
            return False
            
        if not excel_inputs:
            error_msg = "Error: Excel inputs object is None"
            if log_callback:
                log_callback(error_msg)
            return False        # Refresh input data to ensure consistency with any file system changes
        if log_callback:
            log_callback("Refreshing input files before generating soil springs...")
        
        try:
            excel_inputs_refreshed = read_all_input_with_log(excel_inputs, file_paths, log_callback)
        except Exception as e:
            error_details = get_error_details(e, 'read_all_input_with_log')
            error_msg = f"Failed to refresh input files: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            return False

        # Generate lateral soil springs based on geotechnical parameters and pile geometry
        # This module calculates soil-pile interaction coefficients for structural analysis
        try:
            from build_fem import gen_soil_spring
            gen_soil_spring(
                excel_inputs_refreshed,
                file_paths,
                log_callback=log_callback,
                progress_callback=progress_callback
            )
        except ImportError as e:
            error_msg = "Error: Failed to import soil_spring module - check module installation"
            if log_callback:
                log_callback(error_msg)
            return False
        except Exception as e:
            error_details = get_error_details(e, 'gen_soil_spring')
            error_msg = (f"Failed to generate soil springs: {error_details['error_type']}\n"
                        f"Message: {error_details['error_message']}\n"
                        f"Location: {error_details['location']}")
            if log_callback:
                log_callback(error_msg)
                log_callback("Full traceback:")
                log_callback(error_details['full_traceback'])
            return False

        if log_callback:
            log_callback("Soil spring generation completed successfully")
        return True

    except Exception as e:
        error_details = get_error_details(e, 'automated_generate_soil_spring')
        error_msg = (f"Critical error in soil spring generation: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
        return False


def automated_generate_safe_model(file_paths, excel_inputs, excel_outputs,
                                  safe16_dfs, safe22_dfs, design_strip_option=1,
                                  log_callback=None, progress_callback=None):
    """
    Automated generation of complete SAFE finite element models for structural analysis.
    
    This function orchestrates the creation of comprehensive SAFE models including:
    - Structural geometry and member definitions
    - Material properties and section assignments
    - Load cases and load combinations
    - Boundary conditions and soil spring integration
    - Design parameters and analysis options
    - Export in both SAFE 16 and SAFE 22 formats

    The model generation process includes:
    - Input data validation and refreshing from Excel sources
    - Structural geometry processing and node/element generation
    - Material property assignment and section property creation
    - Load case definition and load combination generation
    - Soil spring integration from geotechnical analysis
    - Design strip generation for analysis optimization
    - Model export in multiple SAFE format versions
    """
    try:
        # Validate inputs
        if not file_paths:
            error_msg = "Error: File paths object is None"
            if log_callback:
                log_callback(error_msg)
            return False
            
        if not excel_inputs:
            error_msg = "Error: Excel inputs object is None"
            if log_callback:
                log_callback(error_msg)
            return False
            
        if not excel_outputs:
            error_msg = "Error: Excel outputs object is None"
            if log_callback:
                log_callback(error_msg)
            return False

        if not safe16_dfs:
            error_msg = "Error: SAFE16 dataframes object is None"
            if log_callback:
                log_callback(error_msg)
            return False
            
        if not safe22_dfs:
            error_msg = "Error: SAFE22 dataframes object is None"
            if log_callback:
                log_callback(error_msg)
            return False        # Refresh input data to ensure all Excel files are current and synchronized
        if log_callback:
            log_callback("Refreshing input files before generating SAFE model...")
        
        try:
            excel_inputs_refreshed = read_all_input_with_log(excel_inputs, file_paths, log_callback)
        except Exception as e:
            error_details = get_error_details(e, 'read_all_input_with_log')
            error_msg = f"Failed to refresh input files: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            return False

        # Validate lateral soil spring data availability - critical for pile-soil interaction modeling
        if excel_inputs_refreshed.LateralSoilSpring.empty:
            if log_callback:
                log_callback("WARNING: LateralSoilSpring data is empty during SAFE model generation.")
                log_callback("This may result in incomplete pile-soil interaction modeling.")        # Generate comprehensive SAFE model with all structural components and analysis settings
        # This creates both SAFE 16 and SAFE 22 format files for maximum compatibility
        try:
            gen_safe(
                file_paths,
                excel_inputs_refreshed,
                excel_outputs,
                safe16_dfs,
                safe22_dfs,
                design_strip_option,
                log_callback,
                progress_callback
            )
        except Exception as e:
            error_details = get_error_details(e, 'gen_safe')
            error_msg = (f"Failed to generate SAFE model: {error_details['error_type']}\n"
                        f"Message: {error_details['error_message']}\n"
                        f"Location: {error_details['location']}")
            if log_callback:
                log_callback(error_msg)
                log_callback("Full traceback:")
                log_callback(error_details['full_traceback'])
            return False

        # Log successful completion with version information for documentation
        if log_callback:
            log_callback("SAFE model generation completed successfully.")
            log_callback(f"SAFE Model Builder Version: {config.BUILD_FEM_VERSION}")
            log_callback("Generated models ready for structural analysis in SAFE software")
        return True

    except Exception as e:
        error_details = get_error_details(e, 'automated_generate_safe_model')
        error_msg = (f"Critical error in SAFE model generation: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
        return False
