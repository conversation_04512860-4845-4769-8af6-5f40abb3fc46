"""
SAFE 16 File Writing Module
This module contains SAFE 16 specific functionality for writing:
- Building properties and shapes
- Export operations
- Geometry
- Loads and load combinations
- Materials
- Structural properties
"""

# Import all public functions and classes
from .safe16_bp_shp_dhp import *
from .safe16_class import *
from .safe16_export import *
from .safe16_geometry import *
from .safe16_load import *
from .safe16_load_comb import *
from .safe16_material import *
from .safe16_str_prop import *

# Explicitly import private functions needed by other modules
from .safe16_geometry import (
    _write_point_coordinates_safe16,
    _write_beam_geometry_lines_safe16,
    _write_beam_property_assignments_safe16,
    _write_beam_insertion_points_safe16,
    _write_wall_geometry_lines_safe16,
    _write_column_assign_safe16,
    _write_slab_assign_ObjGeomAreas01General_safe16,
    _write_lkp_ObjGeomAreas01General_safe16,
    _write_slab_assign_SoilPropertyAssignments_safe16,
    _write_slab_assign_SlabPropertyAssignments_safe16,
    _write_slab_group_definitions_safe16,
    _write_slab_group_assign_safe16,
    _write_line_load_assign_safe16,
    _write_opening_assign_SlabPropertyAssignments_safe16,
    _write_opening_ObjGeomAreas01General_safe16
)

from .safe16_bp_shp_dhp import (
    _write_pile_points_safe16,
    _write_pile_column_safe16,
    _write_pile_prop_assign_safe16,
    _write_pile_point_insertion_safe16,
    _write_pile_local_axis_safe16,
    _write_pile_end_release_safe16,
    _write_pile_end_restraint_safe16,
    _write_pile_spring_safe16,
    _write_pile_group_safe16
)

from .safe16_load import (
    _write_line_load_safe16,
    _write_pile_load_safe16,
    _write_slab_load_safe16,
    _write_lkp_load_safe16,
    _write_beam_load_safe16,
    _write_column_load_safe16,
    _write_wall_load_safe16,
    _write_corewall_load_safe16,
    _write_point_load_safe16
)

from .safe16_load_comb import (
    _write_load_pattern_safe16,
    _write_load_case_safe16,
    _write_load_comb_to_safe16,
    _write_general_load_cases_safe16,
    _write_static_load_cases_safe16
)

from .safe16_material import (
    _write_general_material_properties_safe16,
    _write_steel_materials_safe16,
    _write_concrete_materials_safe16,
    _write_rebar_materials_safe16,
    _write_tendon_materials_safe16
)

from .safe16_str_prop import (
    _write_beam_rectangular_section_safe16,
    _write_column_circular_section_safe16,
    _write_column_h_section_safe16,
    _write_slab_general_props_safe16,
    _write_slab_solid_props_safe16,
    _write_slab_prop_safe16,
    _write_beam_general_properties_safe16
)

__all__ = [
    'safe16_bp_shp_dhp',
    'safe16_class',
    'safe16_export',
    'safe16_geometry',
    'safe16_load',
    'safe16_load_comb',
    'safe16_material',
    'safe16_str_prop',
    'Safe16DataFrames',
    'export_safe16_f2k',
    'export_safe16_excel',
    # Geometry private functions
    '_write_point_coordinates_safe16',
    '_write_beam_geometry_lines_safe16',
    '_write_beam_property_assignments_safe16',
    '_write_beam_insertion_points_safe16',
    '_write_wall_geometry_lines_safe16',
    '_write_column_assign_safe16',
    '_write_slab_assign_ObjGeomAreas01General_safe16',
    '_write_lkp_ObjGeomAreas01General_safe16',
    '_write_slab_assign_SoilPropertyAssignments_safe16',
    '_write_slab_assign_SlabPropertyAssignments_safe16',
    '_write_slab_group_definitions_safe16',
    '_write_slab_group_assign_safe16',
    '_write_line_load_assign_safe16',
    '_write_opening_assign_SlabPropertyAssignments_safe16',
    '_write_opening_ObjGeomAreas01General_safe16',
    # BP SHP DHP private functions
    '_write_pile_points_safe16',
    '_write_pile_column_safe16',
    '_write_pile_prop_assign_safe16',
    '_write_pile_point_insertion_safe16',
    '_write_pile_local_axis_safe16',
    '_write_pile_end_release_safe16',
    '_write_pile_end_restraint_safe16',
    '_write_pile_spring_safe16',
    '_write_pile_group_safe16',
    # Load private functions
    '_write_line_load_safe16',
    '_write_pile_load_safe16',
    '_write_slab_load_safe16',
    '_write_lkp_load_safe16',
    '_write_beam_load_safe16',
    '_write_column_load_safe16',
    '_write_wall_load_safe16',
    '_write_corewall_load_safe16',
    '_write_point_load_safe16',
    # Load combination private functions
    '_write_load_pattern_safe16',
    '_write_load_case_safe16',
    '_write_load_comb_to_safe16',
    '_write_general_load_cases_safe16',
    '_write_static_load_cases_safe16',
    # Material private functions
    '_write_general_material_properties_safe16',
    '_write_steel_materials_safe16',
    '_write_concrete_materials_safe16',
    '_write_rebar_materials_safe16',
    '_write_tendon_materials_safe16',
    # Structural properties private functions
    '_write_beam_rectangular_section_safe16',
    '_write_column_circular_section_safe16',
    '_write_column_h_section_safe16',
    '_write_slab_general_props_safe16',
    '_write_slab_solid_props_safe16',
    '_write_slab_prop_safe16',
    '_write_beam_general_properties_safe16'
]