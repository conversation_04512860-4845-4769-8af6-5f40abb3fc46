## Detailed Case-Specific Rules

### Case 1: Single Column Only

#### 1.1 Find Local Axis of Pile Cap
- Initial Pile Cap is the Convex Hull of the Column offset each edge by Edge Distance defined by User in GUI
- Initial Pile Cap shall be trimmed by and within the Site Boundary
- Initial Pile Cap is useful and shall be plotted in DXF
- Find the Optimal Rectangle of the Initial Pile Cap
- The Local Axis of the Pile Cap is the Longer and shorter Axis of the Optimal Rectangle
- The Major Local Axis is the Longer Axis while the Minor Local Axis is the shorter Axis
- The Local Axis shall be stored as this will be used later for each Column Wall Cluster

#### 1.2 Find Possible Pile Layout for each Pile Type (DHP, SHP, BP)
- Each Pile Type has its own Possible Pile Layout (different BP diameter have different layout)
- Create the Maximum Pile Cap of the Pile Type (Maximum Pile Cap = Initial Pile Cap offset by 1 Pile Diameter)
- Each Maximum Pile Cap for Each Pile Type shall be plotted in DXF together with corresponding Possible Pile Location
- **CRITICAL**: Plot maximum pile caps in preselection cap layers: PRESELECTION_[TYPE]_CAP or PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m_CAP
- Possible Pile Location is the grid following the Pile Cap Local Axis with center at Load Center of Column Wall Cluster
- **CRITICAL**: The Load Center MUST be included as one of the Possible Pile Locations in the grid (grid point positioned exactly at load center)
- **CRITICAL**: Grid spacing creates regular pattern with one grid intersection at the load center coordinates
- **CRITICAL**: For BP Case 1, ignore grid generation and place position exactly at Load Center
- Grid size shall be big enough having 2 rows in major axis and 2 rows in minor axis outside optimal rectangle
- Spacing of Possible Pile Grid is the Min Spacing of each Pile Type  
- Possible Pile Location shall be within Maximum Pile Cap, locations outside shall be deleted
- Generate the Possible Pile Location and Maximum Pile Cap, Save them in DXF
- **Enhanced Geometry Processing**: Use improved wall polyline buffering for accurate pile cap boundaries
- **Site Boundary Filtering**: Filter all possible pile positions to ensure compliance with site limits

#### 1.3 Find Ideal Pile Type for each Column Wall Cluster
- Count the total possible pile of each Pile Type
- Calculate the total Pile Capacity and check if it is larger and equal to the Total Load of the Column Wall Cluster
- The Sequence of Pile Type Selection is always DHP→SHP→BP (For BP, start with smaller pile diameter then lower capacity)
- **For BP**: Maximum pile is 1 only. This controls BP type selection. If BP type needs more than 1 BP, that type shall not be chosen
- **CRITICAL**: Each individual Column Wall Cluster shall have its own selected Pile Type stored separately
- The selected Pile Type of each Column Wall cluster shall be stored for later use
- Calculate the Total Pile Number of Selected Pile Type needed by Round up of Total Load / Pile Capacity to Integer
- **AI Pre-Selection Analysis**: Generate comprehensive evaluation summary for all pile types showing capacity utilization and feasibility

#### 1.4 Generate Pile Layout for each Pile Type according to Possible Pile Location

**For DHP & SHP:**
- **1 pile needed**: Place it under the load center
- **2 piles needed**: Place them along Major Local Axis at -1.5 and **** Pile Diameter from load center
- **3 to 8 piles needed**: Use regular polygon pile layout
- **More than 8 piles needed**: Use Possible Pile Location to generate. Do not choose pile at Load Center. Place piles symmetrically. Closer to load center is better. Layout should be close to Circle using Possible Pile Location
  - **DXF Pre-Selection Visualization**: Only DHP & SHP with More than 8 piles need to show pre-selection grid positions and maximum pile cap in DXF, as only these cases use the possible pile location grid generation

**For BP:**
- Since it is controlled to be 1 pile only, place it at the Load Center
- **CRITICAL**: BP pile MUST be positioned exactly at the Load Center coordinates

#### 1.5 Plot in DXF
- Plot the pile layout of actual shape
- Plot initial pile cap and optimal rectangle
- Plot Column and wall actual shape
- Plot load centroid, pile centroid
- Plot the Pre-Selection Pile Location of each pile type (as Cross) and corresponding Maximum Pile Cap
- **CRITICAL**: ALL pre-selected pile positions and their corresponding Maximum Pile Caps MUST be plotted in DXF
- **CRITICAL**: Each pile type's grid positions and boundaries MUST be visible in separate layers
- **CRITICAL**: BP layers must be named PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m
- **CRITICAL**: Maximum Pile Cap boundaries must be plotted in the same layer as their corresponding pile type pre-selection positions
- Plot Site Boundary, Pile Cap
- **Enhanced Visualization**: Use layer-based organization with color coding for different pile types (Red: DHP, Green: SHP, Magenta: BP, Yellow: Selected)
- **Professional AutoCAD DXF**: Include title blocks, dimensions, and detailed annotations

### Case 2: Single Wall Only

#### 2.1 Find Local Axis of Pile Cap
- Same as Case 1.1

#### 2.2 Find Possible Pile Layout for each Pile Type (DHP, SHP, BP)
- Each Pile Type has its own Possible Pile Layout (different BP diameter have different layout)
- Create the Maximum Pile Cap of the Pile Type (Maximum Pile Cap = Initial Pile Cap offset by 1 Pile Diameter)
- Each Maximum Pile Cap for Each Pile Type shall be plotted in DXF together with corresponding Possible Pile Location
- Possible Pile Location is the grid following Pile Cap Local Axis with center at Load Center of Column Wall Cluster
- Grid size shall be big enough having 2 rows in major axis and 2 rows in minor axis outside optimal rectangle
- Spacing of Possible Pile Grid is the Min Spacing of each Pile Type
- **Shift the grid** in Major and Minor Local Axis by Maximum of 1 Min Spacing. Find Possible Pile Layout with Maximum Piles inside Maximum Pile Cap
- Possible Pile Location shall be within Maximum Pile Cap, locations outside shall be deleted
- Generate the Possible Pile Location and Maximum Pile Cap, Save them in DXF
- **Wall Geometry Enhancement**: Process continuous wall segments into unified polylines for better pile cap accuracy
- **Grid Optimization**: Use shifting algorithms to maximize pile count within geometric constraints

#### 2.3 Find Ideal Pile Type for each Column Wall Cluster
- Count the total possible pile of each Pile Type
- Calculate the total Pile Capacity and check if it is larger and equal to the Total Load of Column Wall Cluster
- The Sequence of Pile Type Selection is always DHP→SHP→BP (For BP, start with smaller pile diameter then lower capacity)
- **For BP**: If wall length ≤ 3 Pile Diameter, max pile is 1, else 2. This rule controls BP type selection. If BP type cannot follow this rule, that type shall not be chosen
- The selected Pile Type of each Column Wall cluster shall be stored for later use
- Calculate the Total Pile Number of Selected Pile Type needed by Round up of Total Load / Pile Capacity to Integer
- **Enhanced Load Analysis**: Calculate load centroids considering wall distributed loads and concentrated column loads

#### 2.4 Generate Pile Layout for each Pile Type according to Possible Pile Location

**For DHP & SHP:**
- Upgrade the pile number to a bigger even number
- The pile layout shall be placed symmetrically and evenly along the wall line
- The closer to the wall the better. Pick piles in these manners until pile number is enough

**For BP:**
- **1 pile only**: Place it at load center
- **2 piles**: Place them at the start point and end point of wall line
- **DXF Pre-Selection Visualization**: Only BP with 1 or 2 piles do NOT show pre-selection in DXF (since they use direct positioning). All other pile types and pile counts need to show possible pile location and corresponding maximum pile cap in the same layer

#### 2.5 Plot DXF
- Same as Case 1.5
- **Wall-Specific Visualization**: Enhanced wall representation showing actual wall thickness and geometry
- **Multiple Row Layouts**: Clear visualization of pile arrangements along wall direction with proper spacing indicators

### Case 4: Others (Complex Layouts)

#### 3.1 Find Local Axis of Pile Cap
- Same as Case 1.1

#### 3.2 Find Possible Pile Layout for each Pile Type (DHP, SHP, BP)
- Same as Case 2.2

#### 3.3 Find Ideal Pile Type for each Column Wall Cluster
- Count the total possible pile of each Pile Type
- Calculate the total Pile Capacity and check if it is larger and equal to the Total Load of Column Wall Cluster
- The Sequence of Pile Type Selection is always DHP→SHP→BP (For BP, start with smaller pile diameter then lower capacity)
- The selected Pile Type of each Column Wall cluster shall be stored for later use
- Calculate the Total Pile Number of Selected Pile Type needed by Round up of Total Load / Pile Capacity to Integer

#### 3.4 Generate Pile Layout for each Pile Type according to Possible Pile Location

**For DHP & SHP & BP:**
- Use NSGA-III to find the optimal pile layout
- **Multi-Objective Optimization**: Consider safety/structural performance, geometric optimization, spacing distribution, and loading optimization
- **Advanced Genetic Algorithm**: Use simplified NSGA-III with population-based evolution for optimal pile placement
- **Constraint Handling**: Automatic repair operators ensure minimum spacing and site boundary compliance

#### 3.5 Plot DXF
- Same as Case 1.5
- **Complex Layout Visualization**: Enhanced representation for multi-cluster arrangements with clear group identification
- **CRITICAL**: ALL pile types (DHP, SHP, BP) must show possible pile positions as cross markers in DXF pre-selection
- **CRITICAL**: Maximum Pile Cap boundaries must be plotted in the same layer as their corresponding pile type pre-selection positions
