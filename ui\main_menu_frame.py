"""Main menu frame UI component."""

import tkinter as tk
from typing import Callable, Dict

from ui.components import BaseFrame, HeaderFrame, TimerLabel, create_menu_bar, show_about


class MainMenuFrame(BaseFrame):
    """Main application menu frame."""
    
    def __init__(self, parent, username: str, user_type: str, 
                 menu_items: Dict[str, Callable], on_logout: Callable):
        super().__init__(parent)
          # Clear window and add header
        self.clear_window()
        # Recreate menu bar after clearing window
        create_menu_bar(parent, show_about)
        HeaderFrame(parent, show_user=True, username=username, 
                   user_type=user_type, on_logout=on_logout)
        
        # Main frame
        main_frame = tk.Frame(parent)
        main_frame.pack(expand=True, fill="both", padx=40, pady=20)
        
        tk.Label(main_frame, text="Program Menu:", font=("Arial", 14, "bold")).pack(anchor="w", pady=(0, 20))
        
        # Create menu buttons
        for i, (label, command) in enumerate(menu_items.items(), 1):
            tk.Button(
                main_frame,
                text=f"{i}. {label}",
                command=command,
                width=40,
                height=2
            ).pack(pady=10)
        
        # Timer label
        self.timer_label = TimerLabel(main_frame)
        self.timer_label.pack(pady=10)
    
    def get_timer_label(self) -> TimerLabel:
        """Get the timer label for updates."""
        return self.timer_label
