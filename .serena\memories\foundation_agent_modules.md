# Foundation Agent System (`fdn_agent/`)

## Core Agent Components
- **`agent_main.py`**: Main automation workflow:
  - `automated_initialize_setup()` for project initialization
  - `run_pile_estimation_with_multi_type_optimization()` for advanced optimization
  - `process_selected_pile_types_for_optimization()` for type-specific analysis
  - Usage notification and results formatting
- **`agent_gui.py`**: FdnAgentGUI class for intelligent foundation design interface

## Advanced Pile Estimation System (`pile_estimation/`)
### Core Architecture
- **`pile_workflow_coordinator.py`**: Main workflow coordination with multi-type optimization
- **`exceptions.py`**: Comprehensive exception hierarchy for all estimation aspects

### Clustering & Grouping (`clustering/`)
- **`core_grouping.py`**: Element grouping with automatic threshold optimization
- **`enhanced_clustering.py`**: EnhancedClusteringEngine with advanced algorithms
- **`load_clustering.py``: Load-based sub-clustering for pile distribution
- **`element.py`**: Core Element class and centroid calculations

### Layout Generation (`layout_generation/`)
- **Case-specific layouts**: Specialized algorithms for different structural configurations
  - `case_1_layouts.py`: Point loads and isolated footings
  - `case_2_layouts.py`: Wall foundations and linear structures
  - `case_4_layouts.py`: Complex geometries with multiple constraints
- **`genetic_fitness.py`**: Genetic algorithm for layout optimization
- **`layout_engine.py`**: Main layout generation coordination

### Multi-Objective Optimization (`optimization/`)
- **`enhanced_nsga3_optimizer.py`**: Enhanced NSGA-III implementation
- **`nsga3_optimizer.py`**: Standard DEAP-based NSGA-III optimizer

### Pile Type Selection (`pile_type_selection/`)
- **`core.py`**: IntegratedPileEstimationEngine with preselection
- **`pile_type_preselection.py`**: Advanced pile type selection algorithms
- **`utils.py`**: Utility functions for type selection

### Visualization System (`visualizer/`)
- **`core.py`**: Main DXF creation with comprehensive analysis visualization
- **`pile_plotters.py`**: Specialized pile plotting with cap generation
- **`analysis_plotters.py`**: Preselection analysis and optimization results
- **`structure_plotters.py`**: Column and wall visualization

## Utility Systems
- **Component architecture**: Modular GUI components (base_gui, file_management, pile_types)
- **Data types**: Comprehensive type definitions for all estimation aspects
- **Validation**: Multi-layer validation for coordinates, geometry, and results