# Load Calculator Package for Pile Estimation

This package is dedicated to calculating loads from structural elements, determining pile requirements, and computing load-weighted centroids within the pile foundation estimation system.

## Overview

The `load_calculator` package provides a modular and robust set of functions for all load-related computations, ensuring accurate assessment of structural element loads and their impact on pile design.

## Key Modules

- `basic_calculations.py`: Contains fundamental functions for calculating total axial loads for individual elements and groups of elements from input data.
- `centroid_calculations.py`: Provides functions for calculating load-weighted centroids for groups of elements, which is crucial for optimal pile placement.
- `pile_calculations.py`: Implements logic for determining the required number of piles based on total loads and pile capacity, as well as individual element pile requirements.
- `utils.py`: Offers utility functions for custom load type calculations and generating load summaries.
- `__init__.py`: Exports key functions from the sub-modules for easy access and defines backward compatibility aliases.