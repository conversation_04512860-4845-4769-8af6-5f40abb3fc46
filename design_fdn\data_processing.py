import multiprocessing as mp
import os
from functools import partial
from math import log

import numpy as np
import pandas as pd
from tqdm import tqdm

import sqlite3

# Use __init__.py imports for design_fdn modules
from design_fdn.sls_design.deflection import _apply_unit_conversions_and_rounding


def _process_pile_sls_batch(df_batch, df_element_forces, force_columns):
    """Process a batch of SLS load combinations and return results"""
    list_sls = []

    for _, row in df_batch.iterrows():
        combo_name = row['Combo (Text)']

        # Get non-zero scale factors only
        sf_cols = row.drop(['Combo (Text)', 'ULS/SLS'])
        non_zero_sf = sf_cols[sf_cols != 0]

        if len(non_zero_sf) == 0:
            continue

        # More efficient approach using a list to collect all scaled DataFrames
        scaled_dfs = []
        for load, sf in non_zero_sf.items():
            # Filter once for each load case
            condition = df_element_forces['OutputCase'] == load
            if condition.any():
                sf_load = df_element_forces.loc[condition].copy()
                # Vectorized scaling of forces
                sf_load[force_columns] = sf_load[force_columns] * sf
                scaled_dfs.append(sf_load)

        if not scaled_dfs:
            continue

        # Concatenate once outside the loop
        temp = pd.concat(scaled_dfs, axis=0, ignore_index=True)

        # Group and sum operations
        row_sls = temp.groupby(
            by=['Line', 'Station', 'CaseType', 'LineElem', 'ElemStation'], sort=False).sum().reset_index()

        row_sls['OutputCase'] = combo_name
        row_sls = row_sls[
            ['Line', 'Station', 'OutputCase', 'CaseType', 'P', 'V2', 'V3', 'T', 'M2', 'M3', 'LineElem', 'ElemStation']]
        row_sls['CaseType'] = 'Combination'
        list_sls.append(row_sls)

    # Return results or empty dataframe if no results
    if list_sls:
        return pd.concat(list_sls, ignore_index=True)
    return pd.DataFrame()


def cal_pile_sls(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    # calculate uls load comb of pile from generic load
    df_sls_sf = excel_inputs.LoadComb.copy()
    is_sls = df_sls_sf['Combo (Text)'].str.contains('SLS', case=False)
    condition = (df_sls_sf['ULS/SLS'] == 'SLS') & is_sls
    df_sls_sf = df_sls_sf[condition].reset_index(drop=True)

    # sls of pile element load
    force = ['P', 'V2', 'V3', 'T', 'M2', 'M3']
    df_element_forces = safe_mdbs.ElementForcesColumnsAndBraces.copy()

    total_items = len(df_sls_sf.index)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("🔄 SLS PILE ANALYSIS - SERVICEABILITY LIMIT STATE CALCULATIONS")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Total Load Combinations: {total_items}")
        log_callback(f"   • Analysis Type: Multi-processing parallel computation")
        log_callback(f"   • Force Components: P, V2, V3, T, M2, M3")
        log_callback("")

    # Split the load combinations into batches of maximum size
    batch_size = 20
    batches = [df_sls_sf.iloc[i:i + batch_size] for i in range(0, len(df_sls_sf), batch_size)]

    num_batches = len(batches)
    if log_callback:
        log_callback(f"📦 BATCH PROCESSING CONFIGURATION:")
        log_callback(f"   • Number of batches: {num_batches}")
        log_callback(f"   • Batch size: {batch_size} load combinations per batch")
        log_callback(f"   • Optimization: Parallel processing for enhanced performance")

    # Determine optimal number of processes (default to CPU count minus 1, but min 1)
    num_processes = max(1, os.cpu_count())

    # Create a partial function with the common arguments
    process_batch_partial = partial(_process_pile_sls_batch,
                                    df_element_forces=df_element_forces,
                                    force_columns=force)

    # Process batches in parallel
    results = []
    with mp.Pool(processes=num_processes) as pool:
        if log_callback:
            log_callback("")
            log_callback(f"🚀 INITIATING SLS PARALLEL COMPUTATION:")
            log_callback(f"   • CPU Cores Available: {os.cpu_count()}")
            log_callback(f"   • Active Processes: {num_processes}")
            log_callback(f"   • Processing Strategy: Load-balanced batch distribution")
            log_callback("")

        # Execute the partial function on each batch
        for i, result_df in enumerate(pool.imap(process_batch_partial, batches)):
            if log_callback and i % max(1, num_batches // 10) == 0:
                progress = (i + 1) / num_batches * 100
                log_callback(f"🔄 SLS Processing Progress: {progress:.1f}% ({i + 1}/{num_batches} batches completed)")

            if not result_df.empty:
                results.append(result_df)

    if log_callback:
        log_callback("")
        log_callback("🔄 FINALIZING SLS ANALYSIS:")
        log_callback("   • Consolidating results from all parallel processes")
        log_callback("   • Applying numerical rounding and data formatting")
        log_callback("   • Preparing output for database export")

    # Combine all results
    if results:
        df_sls = pd.concat(results, ignore_index=True)

        columns_to_round = ['P', 'V2', 'V3', 'T', 'M2', 'M3']
        df_sls[columns_to_round] = df_sls[columns_to_round].round(2)

        excel_outputs.PileSLS = df_sls.sort_values(['Line', 'OutputCase'], ascending=[True, True])
        # Save to SQLite
        with sqlite3.connect(file_paths.ExcelOutputPileSLS) as conn:
            excel_outputs.PileSLS.to_sql('PileSLS', conn, if_exists='replace', index=False)

        if log_callback:
            log_callback("")
            log_callback("✅ SLS DATABASE EXPORT COMPLETED SUCCESSFULLY")
            log_callback(f"   • Output File: {file_paths.ExcelOutputPileSLS}")
            log_callback(f"   • Data Format: SQLite database with indexed pile forces")
            log_callback(f"   • Force Components: P, V2, V3, T, M2, M3 (rounded to 2 decimals)")
    else:
        excel_outputs.PileSLS = pd.DataFrame()
        if log_callback:
            log_callback("")
            log_callback("⚠️  SLS ANALYSIS WARNING: No Results Generated")
            log_callback("   • Possible Issues: Invalid load combinations or missing data")
            log_callback("   • Check: Load combination definitions and element forces")
            log_callback("   • Impact: Empty SLS results dataset created")

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ SLS PILE ANALYSIS COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Total Load Combinations Processed: {total_items}")
        log_callback(f"   • Analysis Complete: Serviceability Limit State verification")
        log_callback(f"   • Output Ready: SQLite database with SLS force combinations")
        log_callback("")

    return excel_outputs


def _process_pile_uls_batch(df_batch, df_element_forces, force_columns):
    """Process a batch of load combinations for ULS calculations"""
    list_uls = []

    for _, row in df_batch.iterrows():
        combo_name = row['Combo (Text)']

        # Get non-zero scale factors only
        sf_cols = row.drop(['Combo (Text)', 'ULS/SLS'])
        non_zero_sf = sf_cols[sf_cols != 0]

        if len(non_zero_sf) == 0:
            continue

        # More efficient approach using a list comprehension to collect all scaled DataFrames
        scaled_dfs = []
        for load, sf in non_zero_sf.items():
            # Filter once for each load case
            condition = df_element_forces['OutputCase'] == load
            if condition.any():
                sf_load = df_element_forces.loc[condition].copy()
                # Vectorized scaling of forces
                sf_load[force_columns] = sf_load[force_columns] * sf
                scaled_dfs.append(sf_load)

        if not scaled_dfs:
            continue

        # Concatenate once outside the loop
        temp = pd.concat(scaled_dfs, axis=0, ignore_index=True)

        # Group and sum operations
        row_uls = temp.groupby(
            by=['Line', 'Station', 'CaseType', 'LineElem', 'ElemStation'], sort=False).sum().reset_index()

        row_uls['OutputCase'] = combo_name
        row_uls = row_uls[
            ['Line', 'Station', 'OutputCase', 'CaseType', 'P', 'V2', 'V3', 'T', 'M2', 'M3', 'LineElem', 'ElemStation']]
        row_uls['CaseType'] = 'Combination'
        list_uls.append(row_uls)

    # Return results or empty dataframe if no results
    if list_uls:
        return pd.concat(list_uls, ignore_index=True)
    return pd.DataFrame()


def cal_pile_uls(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    # calculate uls load comb of pile from generic load
    df_uls_sf = excel_inputs.LoadComb.copy()
    condition = df_uls_sf['ULS/SLS'] == 'ULS'
    df_uls_sf = df_uls_sf[condition].reset_index(drop=True)

    # uls of pile element load without p-delta
    force = ['P', 'V2', 'V3', 'T', 'M2', 'M3']
    df_element_forces = safe_mdbs.ElementForcesColumnsAndBraces.copy()

    total_items = len(df_uls_sf.index)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("🔄 ULS PILE ANALYSIS - ULTIMATE LIMIT STATE CALCULATIONS")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Total Load Combinations: {total_items}")
        log_callback(f"   • Analysis Type: Multi-processing parallel computation")
        log_callback(f"   • Force Components: P, V2, V3, T, M2, M3")
        log_callback("")

    # Split the load combinations into batches of maximum size
    batch_size = 20
    batches = [df_uls_sf.iloc[i:i + batch_size] for i in range(0, len(df_uls_sf), batch_size)]

    num_batches = len(batches)
    if log_callback:
        log_callback(f"📦 BATCH PROCESSING CONFIGURATION:")
        log_callback(f"   • Number of batches: {num_batches}")
        log_callback(f"   • Batch size: {batch_size} load combinations per batch")
        log_callback(f"   • Optimization: Parallel processing for enhanced performance")

    # Determine optimal number of processes (default to CPU count minus 1, but min 1)
    num_processes = max(1, os.cpu_count())

    # Create a partial function with the common arguments
    process_batch_partial = partial(_process_pile_uls_batch,
                                    df_element_forces=df_element_forces,
                                    force_columns=force)

    # Process batches in parallel
    results = []
    with mp.Pool(processes=num_processes) as pool:
        if log_callback:
            log_callback("")
            log_callback(f"🚀 INITIATING ULS PARALLEL COMPUTATION:")
            log_callback(f"   • CPU Cores Available: {os.cpu_count()}")
            log_callback(f"   • Active Processes: {num_processes}")
            log_callback(f"   • Processing Strategy: Load-balanced batch distribution")
            log_callback("")

        # Execute the partial function on each batch
        for i, result_df in enumerate(pool.imap(process_batch_partial, batches)):
            if log_callback and i % max(1, num_batches // 10) == 0:
                progress = (i + 1) / num_batches * 100
                log_callback(f"🔄 ULS Processing Progress: {progress:.1f}% ({i + 1}/{num_batches} batches completed)")

            if not result_df.empty:
                results.append(result_df)

    if log_callback:
        log_callback("")
        log_callback("🔄 FINALIZING ULS ANALYSIS:")
        log_callback("   • Consolidating results from all parallel processes")
        log_callback("   • Applying numerical rounding and data formatting")
        log_callback("   • Preparing output for database export")

    # Combine all results
    if results:
        df_uls = pd.concat(results, ignore_index=True)

        columns_to_round = ['P', 'V2', 'V3', 'T', 'M2', 'M3']
        df_uls[columns_to_round] = df_uls[columns_to_round].astype(float).round(2)

        excel_outputs.PileULS = df_uls.sort_values(['Line', 'OutputCase'], ascending=[True, True])

        # Save to SQLite
        with sqlite3.connect(file_paths.ExcelOutputPileULS) as conn:
            excel_outputs.PileULS.to_sql('PileULS', conn, if_exists='replace', index=False)

        if log_callback:
            log_callback("")
            log_callback("✅ ULS DATABASE EXPORT COMPLETED SUCCESSFULLY")
            log_callback(f"   • Output File: {file_paths.ExcelOutputPileULS}")
            log_callback(f"   • Data Format: SQLite database with indexed pile forces")
            log_callback(f"   • Force Components: P, V2, V3, T, M2, M3 (rounded to 2 decimals)")
    else:
        excel_outputs.PileULS = pd.DataFrame()
        if log_callback:
            log_callback("")
            log_callback("⚠️  ULS ANALYSIS WARNING: No Results Generated")
            log_callback("   • Possible Issues: Invalid load combinations or missing data")
            log_callback("   • Check: Load combination definitions and element forces")
            log_callback("   • Impact: Empty ULS results dataset created")

    if log_callback:
        log_callback("")
        log_callback("✅ ULS ANALYSIS PHASE COMPLETED - PROCEEDING TO P-DELTA CALCULATIONS")
        log_callback(f"   • Total Load Combinations Processed: {total_items}")
        log_callback(f"   • Next Phase: P-Delta effect analysis for lateral displacements")
        log_callback("")

    # Calculate P-Delta effects for ULS analysis
    excel_outputs = cal_pile_uls_pdelta(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)

    return excel_outputs


def cal_pile_uls_pdelta(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Calculate P-Delta effects for pile elements based on lateral displacements.
    
    This simplified version processes lateral load patterns more efficiently by:
    - Combining multiple loops into single operations
    - Using vectorized pandas operations
    - Reducing redundant data copying and calculations
    """
    # Get required data
    pile_localxy = excel_outputs.PileLocalXY.copy()
    element_forces = excel_outputs.PileULS.copy()
    
    # Identify and merge lateral load patterns
    lateral_patterns = _get_lateral_load_patterns(excel_inputs, log_callback)
    element_forces = _add_lateral_loadpat_column(element_forces, excel_inputs, lateral_patterns, log_callback)
    
    # Calculate lateral displacements efficiently
    element_forces = _calculate_lateral_displacements(element_forces, pile_localxy, lateral_patterns, log_callback)
    
    # Apply P-Delta effects
    pile_pdelta = _apply_pdelta_effects(element_forces, log_callback)
    
    # Save results to sqlite
    excel_outputs.PileULSPDelta = pile_pdelta
    # Save to SQLite
    with sqlite3.connect(file_paths.ExcelOutputPileULSPDeltaCal) as conn:
        excel_outputs.PileULSPDelta.to_sql('PileULSPDelta', conn, if_exists='replace', index=False)
    if log_callback:
        log_callback("")
        log_callback("✅ P-DELTA CALCULATION EXPORT COMPLETED")
        log_callback(f"   • Detailed File: {file_paths.ExcelOutputPileULSPDeltaCal}")
        log_callback(f"   • Contains: Displacement calculations and P-Delta moment additions")
        log_callback(f"   • Format: SQLite database with comprehensive P-Delta analysis")

    # Prepare final output for P-Delta results
    column_titles = ['Line', 'Station', 'OutputCase', 'CaseType', 'P', 'V2', 'V3', 'T', 'M2', 'M3', 'LineElem', 'ElemStation']
    excel_outputs.PileULS = pile_pdelta[column_titles]

    with sqlite3.connect(file_paths.ExcelOutputPileULSPDelta) as conn:
        excel_outputs.PileULS.to_sql('PileULS', conn, if_exists='replace', index=False)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ ULS P-DELTA ANALYSIS COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Final Output File: {file_paths.ExcelOutputPileULSPDelta}")
        log_callback(f"   • Analysis Complete: Ultimate Limit State with P-Delta effects")
        log_callback(f"   • Ready for: Foundation design verification and optimization")
        log_callback("")
    
    return excel_outputs


def _get_lateral_load_patterns(excel_inputs, log_callback=None):
    """Extract lateral load patterns (SOIL, WIND) from load pattern data."""
    lateral_load_types = ['SOIL', 'WIND']
    loadpat_df = excel_inputs.LoadPat
    
    lateral_patterns = set(
        loadpat_df[loadpat_df['Load Type'].isin(lateral_load_types)]['LoadPat (Text)'].tolist()
    )
    
    if log_callback and lateral_patterns:
        log_callback("🌬️  LATERAL LOAD PATTERN DETECTION:")
        log_callback(f"   • Identified Patterns: {', '.join(sorted(lateral_patterns))}")
        log_callback(f"   • Load Types: SOIL and WIND lateral forces")
        log_callback(f"   • Application: P-Delta displacement calculations")
    
    return lateral_patterns


def _add_lateral_loadpat_column(element_forces, excel_inputs, lateral_patterns, log_callback=None):
    """Add lateral load pattern column to element forces dataframe."""
    loadcomb_df = excel_inputs.LoadComb.copy()
    
    # Find columns that exist in load combinations and are lateral patterns
    available_lateral_cols = [col for col in loadcomb_df.columns if col in lateral_patterns]
    
    if available_lateral_cols:
        # Create lateral load pattern column more efficiently
        lateral_mask = loadcomb_df[available_lateral_cols] != 0
        loadcomb_df['Lateral LoadPat'] = lateral_mask.apply(
            lambda row: ';'.join(row.index[row]), axis=1
        )
    else:
        loadcomb_df['Lateral LoadPat'] = ''
    
    # Merge with element forces
    element_forces = pd.merge(
        element_forces,
        loadcomb_df[['Combo (Text)', 'Lateral LoadPat']],
        left_on='OutputCase',
        right_on='Combo (Text)',
        how='left'
    ).drop('Combo (Text)', axis=1)
    
    if log_callback:
        log_callback("✅ LATERAL LOAD PATTERN INTEGRATION COMPLETED")
        log_callback("   • Merged lateral load patterns with element force data")
        log_callback("   • Enhanced dataset ready for displacement calculations")
        log_callback("   • Pattern mapping applied to all load combinations")
    
    return element_forces


def _calculate_lateral_displacements(element_forces, pile_localxy, lateral_patterns, log_callback=None):
    """Calculate lateral displacements for all patterns efficiently."""
    # Initialize displacement columns
    displacement_cols = {}
    total_u2 = np.zeros(len(element_forces))
    total_u3 = np.zeros(len(element_forces))
    
    if not lateral_patterns:
        # No lateral patterns - initialize with zeros
        element_forces['Total U2 (mm)'] = 0.0
        element_forces['Total U3 (mm)'] = 0.0
        element_forces['Total U23 (mm)'] = 0.0
        element_forces['Need P-Delta'] = 'No'
        
        if log_callback:
            log_callback("ℹ️  P-DELTA ASSESSMENT: No Lateral Patterns Detected")
            log_callback("   • Analysis Result: No SOIL or WIND lateral load patterns found")
            log_callback("   • P-Delta Status: Not required for this load configuration")
            log_callback("   • Impact: Standard ULS analysis without displacement effects")
        return element_forces
    
    # Process all lateral patterns in a single pass
    for pattern in sorted(lateral_patterns):
        # Get displacement data for this pattern
        pattern_data = pile_localxy[pile_localxy['OutputCase'] == pattern][['Line', 'U2 (mm)', 'U3 (mm)']]
        
        if pattern_data.empty:
            continue
            
        # Initialize pattern-specific columns
        element_forces[f'{pattern}_U2 (mm)'] = 0.0
        element_forces[f'{pattern}_U3 (mm)'] = 0.0
        
        # Find rows that contain this pattern
        pattern_mask = element_forces['Lateral LoadPat'].str.contains(f'\\b{pattern}\\b', na=False, regex=True)
        
        if pattern_mask.any():
            # Merge displacement data efficiently
            temp_merge = element_forces.loc[pattern_mask, ['Line']].merge(pattern_data, on='Line', how='left')
            
            # Update displacement values
            u2_values = temp_merge['U2 (mm)'].fillna(0.0).values
            u3_values = temp_merge['U3 (mm)'].fillna(0.0).values
            
            element_forces.loc[pattern_mask, f'{pattern}_U2 (mm)'] = u2_values
            element_forces.loc[pattern_mask, f'{pattern}_U3 (mm)'] = u3_values
            
            # Add to totals
            total_u2[pattern_mask] += u2_values
            total_u3[pattern_mask] += u3_values
    
    # Calculate total displacements and P-Delta requirement
    element_forces['Total U2 (mm)'] = total_u2
    element_forces['Total U3 (mm)'] = total_u3
    element_forces['Total U23 (mm)'] = np.sqrt(total_u2**2 + total_u3**2)
    element_forces['Need P-Delta'] = np.where(element_forces['Total U23 (mm)'] > 25, 'Yes', 'No')
    
    if log_callback:
        pdelta_count = (element_forces['Need P-Delta'] == 'Yes').sum()
        total_count = len(element_forces)
        log_callback("📏 P-DELTA DISPLACEMENT ASSESSMENT COMPLETED")
        log_callback(f"   • Total Records Analyzed: {total_count}")
        log_callback(f"   • P-Delta Required: {pdelta_count} records (displacement > 25mm)")
        log_callback(f"   • Threshold Criterion: 25mm lateral displacement limit")
        log_callback(f"   • Analysis Status: Ready for P-Delta moment calculations")
    
    return element_forces


def _apply_pdelta_effects(element_forces, log_callback=None):
    """Apply P-Delta effects to compression piles with significant displacement."""
    pile_pdelta = element_forces.copy()
    
    # P-Delta only applies to compression piles with significant displacement
    pdelta_condition = (pile_pdelta['P'] < 0) & (pile_pdelta['Need P-Delta'] == 'Yes')
    
    # Calculate additional moments efficiently
    pile_pdelta['P*U2'] = 0.0
    pile_pdelta['P*U3'] = 0.0
    
    if pdelta_condition.any():
        # Calculate P*displacement products for compression piles
        p_abs = pile_pdelta.loc[pdelta_condition, 'P'].abs()
        pile_pdelta.loc[pdelta_condition, 'P*U2'] = p_abs * pile_pdelta.loc[pdelta_condition, 'Total U2 (mm)']/1000
        pile_pdelta.loc[pdelta_condition, 'P*U3'] = p_abs * pile_pdelta.loc[pdelta_condition, 'Total U3 (mm)']/1000
    
    # Add P-Delta moments to original moments
    pile_pdelta['M2_Add'] = pile_pdelta['P*U3']  # M2 from U3 displacement
    pile_pdelta['M3_Add'] = pile_pdelta['P*U2']  # M3 from U2 displacement
    
    # Store original moments by renaming columns
    pile_pdelta.rename(columns={'M2': 'M2_Original', 'M3': 'M3_Original'}, inplace=True)

    # Apply P-Delta effects
    pile_pdelta['M2'] = pile_pdelta['M2_Original'] + pile_pdelta['M2_Add']
    pile_pdelta['M3'] = pile_pdelta['M3_Original'] + pile_pdelta['M3_Add']

    if log_callback:
        compression_count = (pile_pdelta['P'] < 0).sum()
        pdelta_applied_count = pdelta_condition.sum()
        log_callback("🔧 P-DELTA MOMENT APPLICATION COMPLETED")
        log_callback(f"   • Total Compression Piles: {compression_count}")
        log_callback(f"   • P-Delta Effects Applied: {pdelta_applied_count} piles")
        log_callback(f"   • Calculation Method: P×displacement moment additions")
        log_callback(f"   • Moment Components: M2_Add and M3_Add applied to original moments")
    
    return pile_pdelta


def process_nsf(safe_mdbs, excel_inputs, log_callback=None):
    # Filter for NSF rows first to avoid unnecessary operations
    nsf_mask = safe_mdbs.ElementForcesColumnsAndBraces['OutputCase'] == 'NSF'
    
    if not nsf_mask.any():
        if log_callback:
            log_callback("ℹ️  NSF PROCESSING: No NSF Load Cases Detected")
            log_callback("   • Search Result: No NSF output cases found in element forces")
            log_callback("   • Impact: No user-defined NSF values to apply")
            log_callback("   • Status: Proceeding with standard force processing")
        return safe_mdbs
    
    # Create NSF mapping (negative values)
    nsf_map = excel_inputs.Pile.set_index('Pile Mark')['NSF (kN)'].to_dict()
    
    # Extract pile marks and apply mapping only to NSF rows
    pile_marks = safe_mdbs.ElementForcesColumnsAndBraces.loc[nsf_mask, 'Line'].str.split('_').str[0]
    safe_mdbs.ElementForcesColumnsAndBraces.loc[nsf_mask, 'P'] = -pile_marks.map(nsf_map)
    
    if log_callback:
        log_callback("✅ NSF PROCESSING COMPLETED SUCCESSFULLY")
        log_callback("   • Applied user-defined NSF values from Pile table")
        log_callback("   • Target: ElementForcesColumnsAndBraces NSF load cases")
        log_callback("   • Mapping: Pile marks to corresponding NSF force values")
        log_callback("   • Format: Negative values applied for compression forces")
    
    return safe_mdbs


def cal_settlement(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    # Get top points more efficiently
    df_point_coord = safe_mdbs.PointCoord[safe_mdbs.PointCoord['GlobalZ'] == 0].copy()
    top_points = df_point_coord['Point'].tolist()

    df_nodal_displacements = safe_mdbs.NodalDisplacements
    df_top_nodal_displacements = df_nodal_displacements[
        df_nodal_displacements['Point'].isin(top_points)
    ].copy()

    # Get SLS settlement combinations
    df_sls_sf = excel_inputs.LoadComb
    sls_mask = (
        (df_sls_sf['Type (Text)'] == 'Linear Add') &
        (df_sls_sf['ULS/SLS'] == 'SLS') &
        df_sls_sf['Combo (Text)'].str.contains('_SETT', case=False, na=False)
    )
    df_sls_sf = df_sls_sf[sls_mask].copy()

    displacement_cols = ['Ux', 'Uy', 'Uz', 'Rx', 'Ry', 'Rz']
    load_comb_list = []

    total_items = len(df_sls_sf)
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("📐 SETTLEMENT ANALYSIS - FOUNDATION DISPLACEMENT CALCULATIONS")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Total SLS Settlement Combinations: {total_items}")
        log_callback(f"   • Analysis Focus: Top-level points (GlobalZ = 0)")
        log_callback(f"   • Displacement Components: Ux, Uy, Uz, Rx, Ry, Rz")
        log_callback("")

    # Process load combinations more efficiently
    for i, (index, row) in enumerate(df_sls_sf.iterrows()):
        # Get non-zero load factors
        load_factors = {col: row[col] for col in df_sls_sf.columns if row[col] != 0 and col not in ['Combo (Text)', 'Type (Text)', 'ULS/SLS']}

        if load_factors:
            # Filter and scale displacements in one operation
            temp_dfs = []
            for load, factor in load_factors.items():
                mask = df_top_nodal_displacements['OutputCase'] == load
                if mask.any():
                    temp_df = df_top_nodal_displacements[mask].copy()
                    temp_df[displacement_cols] *= factor
                    temp_dfs.append(temp_df)

            if temp_dfs:
                combined_df = pd.concat(temp_dfs, ignore_index=True)
                data_sls = combined_df.groupby(['Node', 'Point', 'CaseType'], sort=False)[displacement_cols].sum().reset_index()
                data_sls['OutputCase'] = row['Combo (Text)']
                load_comb_list.append(data_sls)

        if log_callback and (i % 5 == 0 or i == total_items - 1):
            progress = int((i + 1) / total_items * 100)
            log_callback(f"🔄 Settlement Analysis Progress: {progress}% ({i + 1}/{total_items} combinations processed)")

    df_sls = pd.concat(load_comb_list, ignore_index=True)

    # Create element mappings more efficiently
    element_mappings = _create_element_mappings(excel_inputs)
    df_sls['Element'] = df_sls.apply(lambda row: _map_point_to_element(row['Point'], element_mappings), axis=1)

    # Apply unit conversions and rounding
    _apply_unit_conversions_and_rounding(df_sls)

    excel_outputs.Settlement = df_sls

    # Save to SQLite
    with sqlite3.connect(file_paths.ExcelOutputSettlement) as conn:
        excel_outputs.Settlement.to_sql('ExcelOutputSettlement', conn, if_exists='replace', index=False)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ SETTLEMENT ANALYSIS COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • All load combinations processed and analyzed")
        log_callback("   • Element mappings applied (columns, walls, slabs, piles)")
        log_callback("   • Unit conversions and rounding completed")
        log_callback("   • SQLite database export successful")
        log_callback("")

    return excel_outputs


def _create_element_mappings(excel_inputs):
    """Create mappings from SAFE points to element names"""
    mappings = {}

    # Column mappings
    for _, row in excel_inputs.Column.iterrows():
        mappings[row['Center Point']] = row['Column']

    # Wall mappings
    for _, row in excel_inputs.Wall.iterrows():
        if pd.notna(row.get('Center Point')):
            mappings[row['Center Point']] = f"{row['Wall']}"

    # Slab mappings
    for _, row in excel_inputs.Slab.iterrows():
        if pd.notna(row.get('Points')):
            points = str(row['Points']).split(';')
            for point in points:
                if point.strip():
                    mappings[point.strip()] = row['Slab']

    return mappings


def _map_point_to_element(point, mappings):
    """Map a point to its element name"""
    if '_T' in str(point):
        return point
    return mappings.get(point, '')


def rotate_displacement(df, name_x, name_y, name_angle):
    """
    Perform vectorized coordinate transformation of displacement components to local axes.

    This function transforms displacement components from global coordinate system to
    element local coordinate system using rotation matrices. The transformation is
    essential for analyzing pile behavior in local coordinate systems where the
    pile's primary axis and cross-sectional properties are defined.
    """
    
    # Convert element orientation angles from degrees to radians for trigonometric calculations
    angle_rad = np.radians(df[name_angle])

    # Pre-calculate trigonometric functions for vectorized operations
    # This improves computational efficiency for large datasets
    cos_theta = np.cos(angle_rad)
    sin_theta = np.sin(angle_rad)

    # Apply unified coordinate transformation (anti-clockwise rotation)
    # At 0°: U2 -> +X, U3 -> +Y; At 90°: U2 -> +Y, U3 -> -X; At 180°: U2 -> -X, U3 -> -Y; At 270°: U2 -> -Y, U3 -> +X
    df['U2'] = cos_theta * df[name_x] + sin_theta * df[name_y]
    df['U3'] = -sin_theta * df[name_x] + cos_theta * df[name_y]

    return df


def cal_pile_local_deflection(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Calculate pile element deflections in local coordinate systems for structural analysis.

    This function processes pile displacement data from SAFE analysis results, transforms
    displacements to local coordinate systems, and prepares comprehensive deflection data
    for pile element design and analysis. Local coordinate analysis is essential for
    pile design as it aligns with pile cross-sectional properties and design procedures.

    The analysis workflow includes:
    - Pile element identification and filtering from analysis results
    - Local axis orientation data extraction and processing
    - Coordinate transformation from global to local displacement components
    - Unit conversion and data formatting for engineering analysis
    - Database storage for result management and retrieval
    """

    # Prepare local axis orientation data for coordinate transformation
    # This data contains the angular orientation of each pile element's local coordinate system
    df_column_local_axes = safe_mdbs.ColumnLocalAxes.copy()
    df_line = safe_mdbs.Line.copy()
    df_line = df_line[['Line', 'PointI', 'PointJ']]

    merged_df = pd.merge(df_column_local_axes, df_line, on='Line')

    # merge safe_mdbs.NodalDisplacements to merged_df using 'PointJ' in merged_df and 'Point' in df_nodal_displacements
    df_nodal_displacements = safe_mdbs.NodalDisplacements.copy()
    merged_df = pd.merge(merged_df, df_nodal_displacements, left_on='PointJ', right_on='Point')

    # Transform global displacements to local coordinate system using rotation matrices
    # This is critical for pile analysis as design procedures use local coordinate systems
    merged_df = rotate_displacement(merged_df, 'Ux', 'Uy', 'Angle')

    # Store intermediate results in excel_outputs for potential downstream use
    excel_outputs.PileLocalXY = merged_df

    # Apply comprehensive unit conversions for engineering analysis
    # Convert displacements to millimeters and maintain rotations in radians
    unit_conversions = {
        'Ux': ('Ux (mm)', 1000),    # Global X displacement to mm
        'Uy': ('Uy (mm)', 1000),    # Global Y displacement to mm
        'Uz': ('Uz (mm)', 1000),    # Global Z displacement to mm
        'Rx': ('Rx (Rad)', 1),      # Global X rotation (already in radians)
        'Ry': ('Ry (Rad)', 1),      # Global Y rotation (already in radians)
        'Rz': ('Rz (Rad)', 1),      # Global Z rotation (already in radians)
        'U2': ('U2 (mm)', 1000),    # Local 2-axis displacement to mm
        'U3': ('U3 (mm)', 1000)     # Local 3-axis displacement to mm
    }

    # Execute unit conversions and column renaming efficiently
    for old_col, (new_col, factor) in unit_conversions.items():
        merged_df[new_col] = merged_df[old_col] * factor
        # Remove old column if name changed to avoid duplicate columns
        if old_col != new_col.split()[0]:
            merged_df.drop(old_col, axis=1, inplace=True)

    # Apply engineering precision rounding for different data types
    # Displacement values: 2 decimal places (0.01 mm precision)
    # Rotation values: 6 decimal places (high precision for small angles)
    round_specs = {
        2: ['Ux (mm)', 'Uy (mm)', 'Uz (mm)', 'U2 (mm)', 'U3 (mm)'],
        6: ['Rx (Rad)', 'Ry (Rad)', 'Rz (Rad)']
    }

    for decimals, cols in round_specs.items():
        merged_df[cols] = merged_df[cols].round(decimals)

    # Save processed results to SQLite database for persistent storage and retrieval
    # Using context manager ensures proper database connection handling
    with sqlite3.connect(file_paths.ExcelOutputPileLocalXY) as conn:
        excel_outputs.PileLocalXY.to_sql('ExcelOutputPileLocalXY', conn, if_exists='replace', index=False)

    # Log successful completion of pile local deflection calculations
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ PILE LOCAL DEFLECTION ANALYSIS COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • Coordinate transformation completed for all pile elements")
        log_callback("   • Global displacements converted to local coordinate systems")
        log_callback("   • SQLite database export successful with processed deflections")
        log_callback("")

    return excel_outputs
