"""
Joint Reaction Processor Module for ETABS CWLS

This module handles the processing of joint reaction data from ETABS to create
load schedules for ETABS 2016/2017 models. It processes point reactions and
creates comprehensive load schedules with proper formatting.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional joint reaction processing algorithms

Key Functions:
    create_schedule_joint_reaction: Main joint reaction processing workflow
    process_joint_reaction_load_case: Process individual load cases
    write_schedule_to_excel: Write schedules with retry logic

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import pandas as pd
import numpy as np
import time
from typing import Optional, Callable

from etabs_cwls.core import _class
from etabs_cwls.io._read import read_excel, read_mdbs
from etabs_cwls.io._write import write_excel
from etabs_cwls.processing.coordinate_transformer import integer_round
from etabs_cwls.config.exceptions import DataValidationError, ETABSConnectionError, FileOperationError, CalculationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger
)


def create_schedule_joint_reaction(
    file_path: _class.file_path,
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> _class.design_data:
    """
    Create load schedule from joint reaction data (ETABS 2016/2017 only).
    
    This function processes joint reaction data to create a comprehensive load
    schedule with forces and moments for each load case. It reads the load
    mapping configuration and processes each load case to generate the final
    schedule with proper formatting.
    
    Args:
        file_path: File path object with configured paths
        design_data: Design data object to populate
        log_callback: Optional logging callback function
        
    Returns:
        Updated design data object with joint reaction schedule
        
    Raises:
        DataValidationError: If input parameters are invalid
        ETABSConnectionError: If joint reaction data access fails
        FileOperationError: If Excel operations fail
        CalculationError: If schedule calculation fails
        
    Example:
        >>> design_data = create_schedule_joint_reaction(file_path, design_data)
    """
    log_function_entry(log_callback, "create_schedule_joint_reaction")
    
    try:
        # Input validation
        if not isinstance(file_path, _class.file_path):
            raise DataValidationError(
                f"file_path must be a file_path instance, got {type(file_path).__name__}",
                field_name="file_path",
                invalid_value=type(file_path).__name__,
                expected_type="file_path",
                error_code="INVALID_FILE_PATH_TYPE"
            )
        
        if not isinstance(design_data, _class.design_data):
            raise DataValidationError(
                f"design_data must be a design_data instance, got {type(design_data).__name__}",
                field_name="design_data",
                invalid_value=type(design_data).__name__,
                expected_type="design_data",
                error_code="INVALID_DESIGN_DATA_TYPE"
            )
        
        enhanced_log(log_callback, "Starting joint reaction schedule creation", 'INFO')
        
        with create_timed_logger(log_callback, "joint_reaction_schedule_creation") as timer:
            # Read load mapping configuration
            enhanced_log(log_callback, "Reading load mapping configuration", 'DEBUG')
            design_data.df_mapping = read_excel(file_path.design_excel_path, file_path.mapping, log_callback)
            log_calculation_result(log_callback, "mapping_entries", len(design_data.df_mapping), "entries")
            
            # Read joint reaction data
            enhanced_log(log_callback, "Reading joint reaction data", 'DEBUG')
            try:
                table_name = 'Joint Reactions'
                design_data.df_jtreaction = read_mdbs(file_path.accessfile1, table_name, log_callback)
                log_calculation_result(log_callback, "joint_reactions", len(design_data.df_jtreaction), "reactions")
                
            except Exception as e:
                raise ETABSConnectionError(
                    f"Failed to read joint reactions: {str(e)}",
                    database_path=file_path.accessfile1,
                    table_name=table_name,
                    error_code="JOINT_REACTION_READ_FAILED"
                ) from e
            
            # Get unique point labels
            unique_points = design_data.df_jtreaction['Label'].unique()
            log_calculation_result(log_callback, "unique_points", len(unique_points), "points")
            
            # Initialize schedule DataFrame with multi-level columns
            column = pd.MultiIndex.from_tuples([('Point Label', '')])
            design_data.df_schedule = pd.DataFrame(columns=column)
            
            # Process each load case
            enhanced_log(log_callback, "Processing load cases for joint reaction schedule", 'INFO')
            processed_cases = 0
            
            for index, row in design_data.df_mapping.iterrows():
                loadcase = row['Load Case']
                loadname = row.get('Load Name', row.get('Load Name(Gravity)', ''))
                
                # Skip if no load name specified
                if pd.isna(loadname) or not loadname.strip():
                    enhanced_log(log_callback, f"Skipping load case '{loadcase}' - no load name specified", 'DEBUG')
                    continue
                
                enhanced_log(log_callback, f"Processing load case: {loadcase} -> {loadname}", 'DEBUG')
                
                try:
                    # Create schedule for this load case
                    one_load_schedule = process_joint_reaction_load_case(
                        design_data.df_jtreaction, unique_points, loadcase, loadname, log_callback
                    )
                    
                    # Merge with main schedule
                    if design_data.df_schedule.empty:
                        design_data.df_schedule = one_load_schedule
                    else:
                        design_data.df_schedule = pd.merge(
                            design_data.df_schedule, one_load_schedule, 
                            on=[('Point Label', '')], how='outer'
                        )
                    
                    processed_cases += 1
                    
                except Exception as e:
                    raise CalculationError(
                        f"Failed to process load case '{loadcase}': {str(e)}",
                        calculation_type="joint_reaction_schedule",
                        input_parameters={"loadcase": loadcase, "loadname": loadname},
                        error_code="LOAD_CASE_PROCESSING_FAILED"
                    ) from e
            
            # Reset index and finalize schedule
            design_data.df_schedule = design_data.df_schedule.reset_index(drop=True)
            
            log_calculation_result(log_callback, "processed_load_cases", processed_cases, "cases")
            log_calculation_result(log_callback, "schedule_rows", len(design_data.df_schedule), "rows")
            log_calculation_result(log_callback, "schedule_columns", len(design_data.df_schedule.columns), "columns")
            
            # Write to Excel with retry logic
            write_schedule_to_excel(design_data.df_schedule, file_path, file_path.loadschedule, log_callback)
        
        enhanced_log(log_callback, "Joint reaction schedule creation completed successfully", 'INFO')
        log_validation_result(log_callback, "joint_reaction_schedule_creation", True,
                            f"Successfully created schedule with {processed_cases} load cases")
        log_function_exit(log_callback, "create_schedule_joint_reaction", "Success")
        
        return design_data
        
    except Exception as e:
        log_error_with_context(log_callback, e, "create_schedule_joint_reaction")
        log_validation_result(log_callback, "joint_reaction_schedule_creation", False, str(e))
        raise


def process_joint_reaction_load_case(
    df_jtreaction: pd.DataFrame,
    unique_points: np.ndarray,
    loadcase: str,
    loadname: str,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Process a single load case for joint reaction schedule.
    
    Args:
        df_jtreaction: Joint reaction DataFrame
        unique_points: Array of unique point labels
        loadcase: Load case name
        loadname: Load name in ETABS
        log_callback: Optional logging callback function
        
    Returns:
        DataFrame with processed load case data
        
    Raises:
        CalculationError: If processing fails
    """
    log_function_entry(log_callback, "process_joint_reaction_load_case", 
                      loadcase=loadcase, loadname=loadname)
    
    try:
        # Create DataFrame for this load case
        df_one_load_schedule = pd.DataFrame(unique_points, columns=['Point Label'])
        
        # Process each point
        for index2, row2 in df_one_load_schedule.iterrows():
            point_label = row2['Point Label']
            
            # Filter data for this point and load case
            filter_condition = (
                (df_jtreaction['Label'] == point_label) & 
                (df_jtreaction['CaseCombo'] == loadname)
            )
            
            filtered_data = df_jtreaction.loc[filter_condition]
            
            # Process force and moment components
            force_mapping = {
                'FX': f'{loadcase}Vx',
                'FY': f'{loadcase}Vy', 
                'FZ': f'{loadcase}P',
                'MX': f'{loadcase}Mx',
                'MY': f'{loadcase}My',
                'MZ': f'{loadcase}Mz'
            }
            
            for etabs_component, schedule_component in force_mapping.items():
                if len(filtered_data) > 0 and etabs_component in filtered_data.columns:
                    value = filtered_data[etabs_component].iloc[0]
                    # Apply engineering rounding (round to nearest 100)
                    rounded_value = integer_round(value)
                else:
                    rounded_value = 0
                
                df_one_load_schedule.at[index2, schedule_component] = rounded_value
        
        # Set up multi-level columns
        columns = pd.MultiIndex.from_tuples([
            ('Point Label', ''),
            (loadcase, 'Vx (kN)'),
            (loadcase, 'Vy (kN)'),
            (loadcase, 'Axial (kN)'),
            (loadcase, 'Mx (kNm)'),
            (loadcase, 'My (kNm)'),
            (loadcase, 'Mz (kNm)')
        ])
        
        # Rename columns to match the multi-level structure
        column_mapping = {
            'Point Label': ('Point Label', ''),
            f'{loadcase}Vx': (loadcase, 'Vx (kN)'),
            f'{loadcase}Vy': (loadcase, 'Vy (kN)'),
            f'{loadcase}P': (loadcase, 'Axial (kN)'),
            f'{loadcase}Mx': (loadcase, 'Mx (kNm)'),
            f'{loadcase}My': (loadcase, 'My (kNm)'),
            f'{loadcase}Mz': (loadcase, 'Mz (kNm)')
        }
        
        df_one_load_schedule = df_one_load_schedule.rename(columns=column_mapping)
        
        log_calculation_result(log_callback, f"{loadcase}_processed_points", len(df_one_load_schedule), "points")
        log_function_exit(log_callback, "process_joint_reaction_load_case", "Success")
        
        return df_one_load_schedule
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"process_joint_reaction_load_case({loadcase})")
        raise CalculationError(
            f"Failed to process joint reaction load case '{loadcase}': {str(e)}",
            calculation_type="joint_reaction_load_case",
            input_parameters={"loadcase": loadcase, "loadname": loadname},
            error_code="JOINT_REACTION_LOAD_CASE_FAILED"
        ) from e


def write_schedule_to_excel(
    schedule_df: pd.DataFrame,
    file_path: _class.file_path,
    sheet_name: str,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Write schedule DataFrame to Excel with retry logic.
    
    Args:
        schedule_df: Schedule DataFrame to write
        file_path: File path object
        sheet_name: Name of the sheet to write
        log_callback: Optional logging callback function
        
    Raises:
        FileOperationError: If Excel write fails
    """
    log_function_entry(log_callback, "write_schedule_to_excel", sheet_name=sheet_name)
    
    try:
        enhanced_log(log_callback, f"Writing schedule to Excel sheet: {sheet_name}", 'INFO')
        
        max_retries = 5
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                write_excel(schedule_df, file_path.design_excel_path, sheet_name, log_callback)
                enhanced_log(log_callback, "Schedule written to Excel successfully", 'INFO')
                break
                
            except PermissionError as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise FileOperationError(
                        f"Cannot write Excel file after {max_retries} attempts. "
                        f"Please close the Excel file: {file_path.design_excel_path}",
                        file_path=file_path.design_excel_path,
                        operation="write",
                        error_code="EXCEL_FILE_LOCKED"
                    ) from e
                
                enhanced_log(log_callback, f"Excel file locked, retry {retry_count}/{max_retries}", 'WARNING')
                print(f"⚠️  Excel file is locked. Retrying in 3 seconds... ({retry_count}/{max_retries})")
                time.sleep(3)
                
            except Exception as e:
                raise FileOperationError(
                    f"Failed to write schedule to Excel: {str(e)}",
                    file_path=file_path.design_excel_path,
                    operation="write",
                    error_code="SCHEDULE_WRITE_FAILED"
                ) from e
        
        log_function_exit(log_callback, "write_schedule_to_excel", "Success")
        
    except Exception as e:
        log_error_with_context(log_callback, e, "write_schedule_to_excel")
        raise


# Export all functions
__all__ = [
    'create_schedule_joint_reaction',
    'process_joint_reaction_load_case',
    'write_schedule_to_excel'
]
