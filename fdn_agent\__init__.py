"""Foundation Agent module for intelligent pile estimation and layout."""

# Import with error handling
try:
    from .agent_gui import FdnAgentGUI, fdn_agent
except ImportError:
    FdnAgentGUI = None
    fdn_agent = None

try:
    from .agent_main import (
        automated_initialize_setup,
        run_pile_estimation,
        automated_initialize_setup_with_piles,
        run_pile_estimation_basic,
        run_pile_estimation_with_visualization
    )
except ImportError:
    automated_initialize_setup = None
    run_pile_estimation = None
    automated_initialize_setup_with_piles = None
    run_pile_estimation_basic = None
    run_pile_estimation_with_visualization = None

__all__ = [
    'FdnAgentGUI',
    'fdn_agent',
    'automated_initialize_setup',
    'run_pile_estimation',
    'automated_initialize_setup_with_piles',
    'run_pile_estimation_basic',
    'run_pile_estimation_with_visualization'
]
