# Foundation Design Module Structure (`design_fdn/`)

## Main Foundation Design Directory
```
design_fdn/
├── 📄 designer_main.py          # Core design automation
├── 📄 designer_gui.py           # Design checker GUI
├── 📄 design_fdn_config.py      # Design configuration (80+ constants)
├── 📄 data_processing.py        # Core calculation functions
├── 📄 pile_capacity.py          # Pile capacity calculations
├── 📄 piling_schedule.py        # Piling schedule generation
├── 📄 read.py                   # SAFE database reading
├── 📄 initialization.py         # Excel initialization
├── 📄 error_handling.py         # Error management
├── 📄 README.md                 # Design documentation
├── 📄 __init__.py               # Module initialization
├── 📁 __pycache__/              # Compiled Python files
├── 📁 sls_design/               # Serviceability Limit State design
├── 📁 uls_design_bp/            # Ultimate Limit State - Bored Piles
└── 📁 uls_design_hp/            # Ultimate Limit State - H-Piles
```

## 🔍 SLS Design Subdirectory (`sls_design/`)
```
sls_design/
├── 📄 angular_rotation.py       # Slab angular rotation checks
├── 📄 deflection.py             # Pile deflection calculations
├── 📄 diff_settlement.py        # Differential settlement analysis
├── 📄 settlement.py             # Settlement calculations
├── 📄 README.md                 # SLS design documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## 🏗️ ULS Bored Pile Design (`uls_design_bp/`)
```
uls_design_bp/
├── 📄 circular_column.py        # CircularColumn class & rebar coords
├── 📄 group_bp.py               # Pile grouping and processing
├── 📄 nm_curve_calc.py          # N-M interaction curves
├── 📄 nm_database.py            # Pre-calculated curve database
├── 📄 nm_validation.py          # N-M validation checks
├── 📄 nm_visualization.py       # N-M curve visualization
├── 📄 rebar_design.py           # Optimal rebar design
├── 📄 uls_bp_nm.py             # Main N-M design workflow
├── 📄 uls_bp_v.py              # Shear design calculations
├── 📄 prokon_c13.py            # Prokon file generation
├── 📄 README.md                 # ULS BP documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

## ⚡ ULS H-Pile Design (`uls_design_hp/`)
```
uls_design_hp/
├── 📄 steel_design.py           # Steel design strength calculations
├── 📄 uls_hp_nmv.py            # Combined N-M-V checks
├── 📄 README.md                 # ULS HP documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```