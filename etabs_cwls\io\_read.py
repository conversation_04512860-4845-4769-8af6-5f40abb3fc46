"""
Data Reading Operations for ETABS CWLS Module

This module provides functions for reading data from various sources including
Microsoft Access databases (.mdb, .accdb) and Excel files. It includes
comprehensive error handling, validation, and enhanced logging.

Functions:
    read_mdbs: Read data from Microsoft Access database tables
    read_excel: Read data from Excel worksheets
    validate_database_connection: Validate database connectivity
    get_table_info: Get information about database tables
"""

import pyodbc
import pandas as pd
import os
from typing import Optional, List, Dict, Any, Callable
from pathlib import Path

from etabs_cwls.config.exceptions import FileOperationError, ETABSConnectionError, DataValidationError
from etabs_cwls.config.logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger
)


def read_mdbs(
    file_path: str,
    table_name: str,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Read data from Microsoft Access database table.

    This function connects to a Microsoft Access database and reads all data
    from the specified table. It includes comprehensive error handling for
    database connection issues, missing tables, and data access problems.

    Args:
        file_path: Path to the Microsoft Access database file (.mdb or .accdb)
        table_name: Name of the table to read from
        log_callback: Optional logging callback function

    Returns:
        pandas.DataFrame: DataFrame containing the table data

    Raises:
        FileOperationError: If the database file is not found or inaccessible
        ETABSConnectionError: If database connection or table access fails
        DataValidationError: If input parameters are invalid

    Example:
        >>> df = read_mdbs("model.accdb", "Pier Forces")
        >>> print(df.head())
    """
    log_function_entry(log_callback, "read_mdbs", file_path=file_path, table_name=table_name)

    try:
        # Input validation
        if not isinstance(file_path, str) or not file_path.strip():
            raise DataValidationError(
                "File path must be a non-empty string",
                field_name="file_path",
                invalid_value=file_path,
                expected_type="str",
                error_code="INVALID_FILE_PATH"
            )

        if not isinstance(table_name, str) or not table_name.strip():
            raise DataValidationError(
                "Table name must be a non-empty string",
                field_name="table_name",
                invalid_value=table_name,
                expected_type="str",
                error_code="INVALID_TABLE_NAME"
            )

        # Validate file existence and accessibility
        abs_file_path = os.path.abspath(file_path)
        if not os.path.exists(abs_file_path):
            raise FileOperationError(
                f"Database file not found: {abs_file_path}",
                file_path=abs_file_path,
                operation="read",
                error_code="DATABASE_FILE_NOT_FOUND"
            )

        if not os.access(abs_file_path, os.R_OK):
            raise FileOperationError(
                f"Database file is not readable: {abs_file_path}",
                file_path=abs_file_path,
                operation="read",
                error_code="DATABASE_FILE_NOT_READABLE"
            )

        enhanced_log(log_callback, f"Reading table '{table_name}' from database: {abs_file_path}", 'INFO')

        # Create database connection with timing
        with create_timed_logger(log_callback, "database_connection") as timer:
            try:
                connection_string = f"Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={abs_file_path};"
                conn = pyodbc.connect(connection_string)
                enhanced_log(log_callback, "Database connection established successfully", 'DEBUG')

            except pyodbc.Error as e:
                raise ETABSConnectionError(
                    f"Failed to connect to database: {str(e)}",
                    database_path=abs_file_path,
                    error_code="DATABASE_CONNECTION_FAILED"
                ) from e

        # Read data from table with timing
        with create_timed_logger(log_callback, "table_read") as timer:
            try:
                # Use parameterized query to prevent SQL injection
                # Note: Table names cannot be parameterized, but we validate the input above
                sql = f'SELECT * FROM [{table_name}]'
                enhanced_log(log_callback, f"Executing SQL: {sql}", 'DEBUG')

                df = pd.read_sql(sql, conn)

                # Log results
                log_calculation_result(log_callback, "rows_read", len(df), "rows")
                log_calculation_result(log_callback, "columns_read", len(df.columns), "columns")
                enhanced_log(log_callback, f"Successfully read {len(df)} rows from table '{table_name}'", 'INFO')

            except pyodbc.Error as e:
                error_msg = str(e).lower()
                if "no such table" in error_msg or "table" in error_msg and "not found" in error_msg:
                    raise ETABSConnectionError(
                        f"Table '{table_name}' not found in database",
                        database_path=abs_file_path,
                        table_name=table_name,
                        error_code="TABLE_NOT_FOUND"
                    ) from e
                else:
                    raise ETABSConnectionError(
                        f"Failed to read from table '{table_name}': {str(e)}",
                        database_path=abs_file_path,
                        table_name=table_name,
                        error_code="TABLE_READ_FAILED"
                    ) from e

            except pd.errors.DatabaseError as e:
                raise ETABSConnectionError(
                    f"Database error while reading table '{table_name}': {str(e)}",
                    database_path=abs_file_path,
                    table_name=table_name,
                    error_code="PANDAS_DATABASE_ERROR"
                ) from e

            finally:
                # Ensure connection is closed
                try:
                    conn.close()
                    enhanced_log(log_callback, "Database connection closed", 'DEBUG')
                except Exception:
                    pass  # Ignore errors when closing connection

        # Validate result
        if df.empty:
            enhanced_log(log_callback, f"Warning: Table '{table_name}' is empty", 'WARNING')

        log_validation_result(log_callback, "database_read", True,
                            f"Successfully read {len(df)} rows from '{table_name}'")
        log_function_exit(log_callback, "read_mdbs", f"DataFrame with {len(df)} rows")

        return df

    except Exception as e:
        log_error_with_context(log_callback, e, f"read_mdbs(file_path='{file_path}', table_name='{table_name}')")
        log_validation_result(log_callback, "database_read", False, str(e))
        raise


def read_excel(
    path_excel_input: str,
    sheet_name: str,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Read data from Excel worksheet.

    This function reads data from a specified worksheet in an Excel file.
    It includes comprehensive error handling for file access issues,
    missing worksheets, and data reading problems.

    Args:
        path_excel_input: Path to the Excel file (.xlsx, .xls)
        sheet_name: Name of the worksheet to read from
        log_callback: Optional logging callback function

    Returns:
        pandas.DataFrame: DataFrame containing the worksheet data

    Raises:
        FileOperationError: If the Excel file is not found or inaccessible
        DataValidationError: If input parameters are invalid

    Example:
        >>> df = read_excel("design.xlsx", "Parameters")
        >>> print(df.head())
    """
    log_function_entry(log_callback, "read_excel",
                      path_excel_input=path_excel_input, sheet_name=sheet_name)

    try:
        # Input validation
        if not isinstance(path_excel_input, str) or not path_excel_input.strip():
            raise DataValidationError(
                "Excel file path must be a non-empty string",
                field_name="path_excel_input",
                invalid_value=path_excel_input,
                expected_type="str",
                error_code="INVALID_EXCEL_PATH"
            )

        if not isinstance(sheet_name, str) or not sheet_name.strip():
            raise DataValidationError(
                "Sheet name must be a non-empty string",
                field_name="sheet_name",
                invalid_value=sheet_name,
                expected_type="str",
                error_code="INVALID_SHEET_NAME"
            )

        # Validate file existence and accessibility
        abs_file_path = os.path.abspath(path_excel_input)
        if not os.path.exists(abs_file_path):
            raise FileOperationError(
                f"Excel file not found: {abs_file_path}",
                file_path=abs_file_path,
                operation="read",
                error_code="EXCEL_FILE_NOT_FOUND"
            )

        if not os.access(abs_file_path, os.R_OK):
            raise FileOperationError(
                f"Excel file is not readable: {abs_file_path}",
                file_path=abs_file_path,
                operation="read",
                error_code="EXCEL_FILE_NOT_READABLE"
            )

        enhanced_log(log_callback, f"Reading sheet '{sheet_name}' from Excel file: {abs_file_path}", 'INFO')

        # Read Excel file with timing
        with create_timed_logger(log_callback, "excel_read") as timer:
            try:
                df = pd.read_excel(abs_file_path, sheet_name=sheet_name)

                # Log results
                log_calculation_result(log_callback, "rows_read", len(df), "rows")
                log_calculation_result(log_callback, "columns_read", len(df.columns), "columns")
                enhanced_log(log_callback, f"Successfully read {len(df)} rows from sheet '{sheet_name}'", 'INFO')

            except FileNotFoundError as e:
                raise FileOperationError(
                    f"Excel file not found: {abs_file_path}",
                    file_path=abs_file_path,
                    operation="read",
                    error_code="EXCEL_FILE_NOT_FOUND"
                ) from e

            except ValueError as e:
                error_msg = str(e).lower()
                if "worksheet" in error_msg or "sheet" in error_msg:
                    raise FileOperationError(
                        f"Worksheet '{sheet_name}' not found in Excel file",
                        file_path=abs_file_path,
                        operation="read",
                        error_code="WORKSHEET_NOT_FOUND"
                    ) from e
                else:
                    raise FileOperationError(
                        f"Error reading Excel file: {str(e)}",
                        file_path=abs_file_path,
                        operation="read",
                        error_code="EXCEL_READ_ERROR"
                    ) from e

            except PermissionError as e:
                raise FileOperationError(
                    f"Permission denied accessing Excel file: {abs_file_path}",
                    file_path=abs_file_path,
                    operation="read",
                    error_code="EXCEL_PERMISSION_DENIED"
                ) from e

            except Exception as e:
                raise FileOperationError(
                    f"Unexpected error reading Excel file: {str(e)}",
                    file_path=abs_file_path,
                    operation="read",
                    error_code="EXCEL_UNEXPECTED_ERROR"
                ) from e

        # Validate result
        if df.empty:
            enhanced_log(log_callback, f"Warning: Sheet '{sheet_name}' is empty", 'WARNING')

        log_validation_result(log_callback, "excel_read", True,
                            f"Successfully read {len(df)} rows from '{sheet_name}'")
        log_function_exit(log_callback, "read_excel", f"DataFrame with {len(df)} rows")

        return df

    except Exception as e:
        log_error_with_context(log_callback, e, f"read_excel(path='{path_excel_input}', sheet='{sheet_name}')")
        log_validation_result(log_callback, "excel_read", False, str(e))
        raise


def validate_database_connection(
    file_path: str,
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate database connectivity without reading data.

    Args:
        file_path: Path to the Microsoft Access database file
        log_callback: Optional logging callback function

    Returns:
        True if connection is successful

    Raises:
        FileOperationError: If file is not accessible
        ETABSConnectionError: If connection fails
    """
    log_function_entry(log_callback, "validate_database_connection", file_path=file_path)

    try:
        abs_file_path = os.path.abspath(file_path)

        # Check file existence
        if not os.path.exists(abs_file_path):
            raise FileOperationError(
                f"Database file not found: {abs_file_path}",
                file_path=abs_file_path,
                operation="validate",
                error_code="DATABASE_FILE_NOT_FOUND"
            )

        # Test connection
        connection_string = f"Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={abs_file_path};"

        try:
            conn = pyodbc.connect(connection_string)
            conn.close()
            enhanced_log(log_callback, f"Database connection validated: {abs_file_path}", 'INFO')

        except pyodbc.Error as e:
            raise ETABSConnectionError(
                f"Database connection validation failed: {str(e)}",
                database_path=abs_file_path,
                error_code="CONNECTION_VALIDATION_FAILED"
            ) from e

        log_validation_result(log_callback, "database_connection", True, "Connection validated successfully")
        log_function_exit(log_callback, "validate_database_connection", True)
        return True

    except Exception as e:
        log_error_with_context(log_callback, e, f"validate_database_connection('{file_path}')")
        log_validation_result(log_callback, "database_connection", False, str(e))
        raise


def get_table_info(
    file_path: str,
    log_callback: Optional[Callable] = None
) -> Dict[str, List[str]]:
    """
    Get information about tables and columns in the database.

    Args:
        file_path: Path to the Microsoft Access database file
        log_callback: Optional logging callback function

    Returns:
        Dictionary mapping table names to lists of column names

    Raises:
        FileOperationError: If file is not accessible
        ETABSConnectionError: If database access fails
    """
    log_function_entry(log_callback, "get_table_info", file_path=file_path)

    try:
        abs_file_path = os.path.abspath(file_path)

        # Validate file
        if not os.path.exists(abs_file_path):
            raise FileOperationError(
                f"Database file not found: {abs_file_path}",
                file_path=abs_file_path,
                operation="analyze",
                error_code="DATABASE_FILE_NOT_FOUND"
            )

        enhanced_log(log_callback, f"Analyzing database structure: {abs_file_path}", 'INFO')

        connection_string = f"Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={abs_file_path};"

        try:
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()

            # Get table information
            table_info = {}

            # Get all tables
            tables = cursor.tables(tableType='TABLE')
            table_names = [row.table_name for row in tables]

            enhanced_log(log_callback, f"Found {len(table_names)} tables", 'DEBUG')

            # Get columns for each table
            for table_name in table_names:
                try:
                    columns = cursor.columns(table=table_name)
                    column_names = [row.column_name for row in columns]
                    table_info[table_name] = column_names
                    enhanced_log(log_callback, f"Table '{table_name}': {len(column_names)} columns", 'DEBUG')

                except Exception as e:
                    enhanced_log(log_callback, f"Warning: Could not get columns for table '{table_name}': {e}", 'WARNING')
                    table_info[table_name] = []

            conn.close()

        except pyodbc.Error as e:
            raise ETABSConnectionError(
                f"Failed to analyze database structure: {str(e)}",
                database_path=abs_file_path,
                error_code="DATABASE_ANALYSIS_FAILED"
            ) from e

        log_calculation_result(log_callback, "tables_found", len(table_info), "tables")
        log_validation_result(log_callback, "database_analysis", True,
                            f"Successfully analyzed {len(table_info)} tables")
        log_function_exit(log_callback, "get_table_info", f"Info for {len(table_info)} tables")

        return table_info

    except Exception as e:
        log_error_with_context(log_callback, e, f"get_table_info('{file_path}')")
        log_validation_result(log_callback, "database_analysis", False, str(e))
        raise


def get_excel_sheet_names(
    file_path: str,
    log_callback: Optional[Callable] = None
) -> List[str]:
    """
    Get list of sheet names in an Excel file.

    Args:
        file_path: Path to the Excel file
        log_callback: Optional logging callback function

    Returns:
        List of sheet names

    Raises:
        FileOperationError: If file is not accessible
    """
    log_function_entry(log_callback, "get_excel_sheet_names", file_path=file_path)

    try:
        abs_file_path = os.path.abspath(file_path)

        # Validate file
        if not os.path.exists(abs_file_path):
            raise FileOperationError(
                f"Excel file not found: {abs_file_path}",
                file_path=abs_file_path,
                operation="analyze",
                error_code="EXCEL_FILE_NOT_FOUND"
            )

        enhanced_log(log_callback, f"Getting sheet names from: {abs_file_path}", 'INFO')

        try:
            # Read Excel file to get sheet names
            excel_file = pd.ExcelFile(abs_file_path)
            sheet_names = excel_file.sheet_names
            excel_file.close()

        except Exception as e:
            raise FileOperationError(
                f"Failed to read Excel file structure: {str(e)}",
                file_path=abs_file_path,
                operation="analyze",
                error_code="EXCEL_ANALYSIS_FAILED"
            ) from e

        log_calculation_result(log_callback, "sheets_found", len(sheet_names), "sheets")
        enhanced_log(log_callback, f"Found sheets: {', '.join(sheet_names)}", 'DEBUG')
        log_validation_result(log_callback, "excel_analysis", True,
                            f"Successfully found {len(sheet_names)} sheets")
        log_function_exit(log_callback, "get_excel_sheet_names", f"{len(sheet_names)} sheets")

        return sheet_names

    except Exception as e:
        log_error_with_context(log_callback, e, f"get_excel_sheet_names('{file_path}')")
        log_validation_result(log_callback, "excel_analysis", False, str(e))
        raise


# Export all functions
__all__ = [
    'read_mdbs',
    'read_excel',
    'validate_database_connection',
    'get_table_info',
    'get_excel_sheet_names'
]


