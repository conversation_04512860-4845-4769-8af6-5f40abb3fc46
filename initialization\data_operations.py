from datetime import datetime
from math import pi
from pathlib import Path

import pandas as pd

from build_fem import build_fem_config as config
from build_fem.build_fem_config import SHEET_BOREHOLE_SPT

import initialization.init_input_geology as init_input_geology
import initialization.init_input_geometry as init_input_geometry
import initialization.init_input_loading as init_input_loading
import initialization.init_input_property as init_input_property

from read import read_loading
from read import read_geometry
from read import read_property
from read import read_geology
from read import read_steel

from initialization.error_handling import get_error_details
import main_class as _main_class


def init_input(file_paths, log_callback=None):
    try:
        # Create an instance of ExcelInputs to store input data
        excel_inputs = _main_class.ExcelInputs()

        excel_inputs = read_all_input_with_log(
            excel_inputs, file_paths, log_callback)

        # Initialize mini pile spring data if the MP index is not empty
        if excel_inputs.MP.index.size != 0:
            if log_callback:
                log_callback("Initializing mini pile spring...")
            try:
                excel_inputs = mini_pile_spring(
                    file_paths.ExcelOutputMP, excel_inputs)
            except Exception as e:
                error_details = get_error_details(e, 'mini_pile_spring initialization')
                error_msg = f"Error initializing mini pile spring: {error_details['error_message']}"
                if log_callback:
                    log_callback(error_msg)
                    log_callback(f"Location: {error_details['location']}")
                raise

        return excel_inputs

    except Exception as e:
        error_details = get_error_details(e, 'init_input')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise


def init_output(excel_inputs, log_callback=None):
    """
    Initialize output data for the SAFE model.

    Args:
        excel_inputs (_main_class.ExcelInputs): An object containing the initialized input data.
        log_callback (callable, optional): A function to log messages. If provided, it will be used to log the initialization process.

    Returns:
        _main_class.ExcelOutputs: An object containing the initialized output data.

    Functionality:
        - Logs the initialization process if a log callback is provided.
        - Creates an instance of `ExcelOutputs` to store output data.
        - Initializes output loading data using the provided input data.
    """
    if log_callback:
        log_callback("Initializing output loading...")
    excel_outputs = _main_class.ExcelOutputs()
    excel_outputs = init_output_loading(
        excel_inputs, excel_outputs)
    return excel_outputs


def update_excel_geology(file_paths, excel_inputs, log_callback=None):
    try:
        if log_callback:
            log_callback("Updating Excel geology...")

        # Check if existing geology file exists
        if not Path(file_paths.ExistingExcelGeology).exists():
            error_msg = f"Error: Existing geology file not found: {file_paths.ExistingExcelGeology}"
            if log_callback:
                log_callback(error_msg)
            raise FileNotFoundError(error_msg)
        
        # read borehole data from file_path.ExistingExcelGeology of sheet "BoreholeSPT" using pandas
        try:
            df_existing_borehole_spt = pd.read_excel(
                file_paths.ExistingExcelGeology, sheet_name=SHEET_BOREHOLE_SPT)
        except Exception as e:
            error_msg = f"Error reading BoreholeSPT sheet from {file_paths.ExistingExcelGeology}: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            raise

        # make a list of unique "Borehole Name"
        if 'Borehole' not in df_existing_borehole_spt.columns:
            error_msg = "Error: 'Borehole' column not found in existing BoreholeSPT sheet"
            if log_callback:
                log_callback(error_msg)
            raise KeyError(error_msg)

        unique_borehole_names = df_existing_borehole_spt['Borehole'].unique()

        if log_callback:
            log_callback(
                f"Found {len(unique_borehole_names)} unique boreholes in existing file")

        # write if the rows excel_inputs.BoreholeSPT with unique_borehole_names then drop and replace the rows with new data
        # from df_existing_borehole_spt
        for name in unique_borehole_names:
            try:
                # check if the name is in excel_inputs.BoreholeSPT
                if name in excel_inputs.BoreholeSPT['Borehole'].values:
                    # drop the rows with the same name
                    excel_inputs.BoreholeSPT = excel_inputs.BoreholeSPT[
                        excel_inputs.BoreholeSPT['Borehole'] != name]
                    # reset the index
                    excel_inputs.BoreholeSPT = excel_inputs.BoreholeSPT.reset_index(
                        drop=True)
                    # add all the rows with the same name from df_existing_borehole_spt to excel_inputs.BoreholeSPT
                    excel_inputs.BoreholeSPT = pd.concat(
                        [excel_inputs.BoreholeSPT,
                         df_existing_borehole_spt[df_existing_borehole_spt['Borehole'] == name]],
                        ignore_index=True)
                    if log_callback:
                        log_callback(f"Updated borehole data for '{name}'")
            except Exception as e:
                error_msg = f"Error updating borehole '{name}': {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise
          # reset the index
        excel_inputs.BoreholeSPT = excel_inputs.BoreholeSPT.reset_index(drop=True)

        try:
            if log_callback:
                log_callback(
                    f"Exporting updated geology Excel to: {file_paths.ExcelGeology}")
            with pd.ExcelWriter(file_paths.ExcelGeology) as writer:
                excel_inputs.BoreholeSPT.to_excel(
                    writer, sheet_name=SHEET_BOREHOLE_SPT, index=False)
                excel_inputs.PileSoilSpringSetting.to_excel(
                    writer, sheet_name=config.SHEET_PILE_SOIL_SPRING_SETTING, index=False)
                excel_inputs.LateralSoilSpring.to_excel(
                    writer, sheet_name=config.SHEET_LATERAL_SOIL_SPRING, index=False)
            # Print message indicating successful export
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            message = f'{now}: Updated Excel Input (Geology) based on Existing Excel Input (Geology)!'
            log_callback(message) if log_callback else print(message)
        except PermissionError as e:
            error_msg = f"Error: Permission denied when writing to {file_paths.ExcelGeology}. File may be open in another program."
            if log_callback:
                log_callback(error_msg)
            raise
        except Exception as e:
            error_details = get_error_details(e, 'update_excel_geology - export')
            error_msg = f"Error exporting geology Excel: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        return excel_inputs

    except Exception as e:
        error_details = get_error_details(e, 'update_excel_geology')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise


def update_excel_loading(file_paths, excel_inputs, log_callback=None):
    try:
        if log_callback:
            log_callback("Updating Excel loading...")

        # Check if existing loading file exists
        if not Path(file_paths.ExistingExcelLoading).exists():
            error_msg = f"Error: Existing loading file not found: {file_paths.ExistingExcelLoading}"
            if log_callback:
                log_callback(error_msg)
            raise FileNotFoundError(error_msg)
        
        # Dictionary to store sheet names and their expected columns
        sheet_configs = {
            config.SHEET_LOAD_PAT: None,
            config.SHEET_LOAD_CASE: None,
            config.SHEET_LOAD_COMB: None,
            config.SHEET_INPUT_LOAD_POINT_LOAD: [('Point Data', 'Point Load')],
            config.SHEET_INPUT_LOAD_LINE_LOAD: [('Line Data', 'Line Load')],
            config.SHEET_INPUT_LOAD_PILE: [('Pile Data', 'Pile Mark')],
            config.SHEET_INPUT_LOAD_LKP: [('LKP Data', 'LKP Type')],
            config.SHEET_INPUT_LOAD_SLAB: [('Slab Data', 'Slab Mark')],
            config.SHEET_INPUT_LOAD_BEAM: [('Beam Data', 'Beam Mark')],
            config.SHEET_INPUT_LOAD_COLUMN: [('Column Data', 'Column Mark')],
            config.SHEET_INPUT_LOAD_WALL: [('Wall Data', 'Wall Mark')],
            config.SHEET_INPUT_LOAD_CORE_WALL: [('CoreWall Data', 'CoreWall Mark')]
        }

        # Read all sheets with error handling
        loaded_sheets = {}
        for sheet_name, expected_cols in sheet_configs.items():
            try:
                if sheet_name in ['LoadPat', 'LoadCase', 'LoadComb']:
                    df = pd.read_excel(file_paths.ExistingExcelLoading, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(file_paths.ExistingExcelLoading, sheet_name=sheet_name, header=[0, 1])
                loaded_sheets[sheet_name] = df
                if log_callback:
                    log_callback(f"Successfully loaded sheet: {sheet_name}")
            except Exception as e:
                error_msg = f"Error reading sheet '{sheet_name}' from {file_paths.ExistingExcelLoading}: {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise        # Assign loaded sheets to variables
        df_existing_load_pattern = loaded_sheets[config.SHEET_LOAD_PAT]
        df_existing_load_case = loaded_sheets[config.SHEET_LOAD_CASE]
        df_existing_load_comb = loaded_sheets[config.SHEET_LOAD_COMB]
        df_existing_input_load_point = loaded_sheets[config.SHEET_INPUT_LOAD_POINT_LOAD]
        df_existing_input_load_line = loaded_sheets[config.SHEET_INPUT_LOAD_LINE_LOAD]
        df_existing_input_load_pile = loaded_sheets[config.SHEET_INPUT_LOAD_PILE]
        df_existing_input_load_lkp = loaded_sheets[config.SHEET_INPUT_LOAD_LKP]
        df_existing_input_load_slab = loaded_sheets[config.SHEET_INPUT_LOAD_SLAB]
        df_existing_input_load_beam = loaded_sheets[config.SHEET_INPUT_LOAD_BEAM]
        df_existing_input_load_column = loaded_sheets[config.SHEET_INPUT_LOAD_COLUMN]
        df_existing_input_load_wall = loaded_sheets[config.SHEET_INPUT_LOAD_WALL]
        df_existing_input_load_corewall = loaded_sheets[config.SHEET_INPUT_LOAD_CORE_WALL]

        # Process each load type with individual error handling
        merge_operations = [
            ('point', excel_inputs.InputLoadPoint, df_existing_input_load_point, [('Point Data', 'Point Load')]),
            ('line', excel_inputs.InputLoadLine, df_existing_input_load_line, [('Line Data', 'Line Load')]),
            ('pile', excel_inputs.InputLoadPile, df_existing_input_load_pile, [('Pile Data', 'Pile Mark')]),
            ('slab', excel_inputs.InputLoadSlab, df_existing_input_load_slab, [('Slab Data', 'Slab Mark')]),
            ('lkp', excel_inputs.InputLoadLKP, df_existing_input_load_lkp, [('LKP Data', 'LKP Type')]),
            ('beam', excel_inputs.InputLoadBeam, df_existing_input_load_beam, [('Beam Data', 'Beam Mark')]),
            ('column', excel_inputs.InputLoadColumn, df_existing_input_load_column, [('Column Data', 'Column Mark')]),
            ('wall', excel_inputs.InputLoadWall, df_existing_input_load_wall, [('Wall Data', 'Wall Mark')]),
            ('corewall', excel_inputs.InputLoadCoreWall, df_existing_input_load_corewall, [('CoreWall Data', 'CoreWall Mark')])
        ]

        for load_type, input_df, existing_df, merge_cols in merge_operations:
            if log_callback:
                log_callback(f"Merging {load_type} load data...")
            try:
                unique_names = input_df[merge_cols[0]].drop_duplicates()
                if log_callback:
                    log_callback(f"{load_type.capitalize()} Load entries: {len(unique_names)} items")
                if not unique_names.empty:
                    merged_df = pd.merge(
                        input_df[[merge_cols[0]]],
                        existing_df,
                        on=merge_cols,
                        how='left'
                    )
                    # Update the appropriate attribute
                    setattr(excel_inputs, f'InputLoad{load_type.capitalize()}', merged_df)
            except KeyError as e:
                error_msg = f"Error: Required column {str(e)} not found in {load_type} load data"
                if log_callback:
                    log_callback(error_msg)
                raise
            except Exception as e:
                error_details = get_error_details(e, f'merge_{load_type}_load')
                error_msg = f"Error merging {load_type} load data: {error_details['error_message']}"
                if log_callback:
                    log_callback(error_msg)
                    log_callback(f"Location: {error_details['location']}")
                raise
        
        # store to class excel_inputs
        excel_inputs.LoadPat = df_existing_load_pattern
        excel_inputs.LoadCase = df_existing_load_case
        excel_inputs.LoadComb = df_existing_load_comb

        try:
            if log_callback:
                log_callback(
                    f"Exporting updated loading Excel to: {file_paths.ExcelLoading}")
            with pd.ExcelWriter(file_paths.ExcelLoading) as writer:
                # Export simple sheets
                excel_inputs.LoadPat.to_excel(
                    writer, sheet_name=config.SHEET_LOAD_PAT, index=False)
                excel_inputs.LoadCase.to_excel(
                    writer, sheet_name=config.SHEET_LOAD_CASE, index=False)
                excel_inputs.LoadComb.to_excel(
                    writer, sheet_name=config.SHEET_LOAD_COMB, index=False)

                # Export multi-header sheets
                export_configs = [
                    (config.SHEET_INPUT_LOAD_POINT_LOAD, excel_inputs.InputLoadPoint),
                    (config.SHEET_INPUT_LOAD_LINE_LOAD, excel_inputs.InputLoadLine),
                    (config.SHEET_INPUT_LOAD_PILE, excel_inputs.InputLoadPile),
                    (config.SHEET_INPUT_LOAD_SLAB, excel_inputs.InputLoadSlab),
                    (config.SHEET_INPUT_LOAD_LKP, excel_inputs.InputLoadLKP),
                    (config.SHEET_INPUT_LOAD_BEAM, excel_inputs.InputLoadBeam),
                    (config.SHEET_INPUT_LOAD_COLUMN, excel_inputs.InputLoadColumn),
                    (config.SHEET_INPUT_LOAD_WALL, excel_inputs.InputLoadWall),
                    (config.SHEET_INPUT_LOAD_CORE_WALL, excel_inputs.InputLoadCoreWall)
                ]

                for sheet_name, df in export_configs:
                    try:
                        # Create a copy of the dataframe to avoid modifying the original
                        df_export = df.copy()
                        
                        # Drop specific columns for Wall and CoreWall sheets
                        if sheet_name == config.SHEET_INPUT_LOAD_WALL:
                            if ('Wall Data', 'Wall Length (m)') in df_export.columns:
                                df_export = df_export.drop(columns=[('Wall Data', 'Wall Length (m)')])
                        elif sheet_name == config.SHEET_INPUT_LOAD_CORE_WALL:
                            if ('CoreWall Data', 'CoreWall Length (m)') in df_export.columns:
                                df_export = df_export.drop(columns=[('CoreWall Data', 'CoreWall Length (m)')])
                        
                        df_title = pd.DataFrame(
                            columns=df_export.droplevel([1], axis=1).columns)
                        df_value = df_export.droplevel(0, axis=1)
                        df_title.to_excel(
                            writer, sheet_name=sheet_name, index=False)
                        df_value.to_excel(
                            writer, sheet_name=sheet_name, startrow=1, index=False)
                    except Exception as e:
                        error_msg = f"Error exporting sheet '{sheet_name}': {str(e)}"
                        if log_callback:
                            log_callback(error_msg)
                        raise

            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            message = f'{now}: Updated Excel Input (Loading) based on Existing Excel Input (Loading)!'
            log_callback(message) if log_callback else print(message)

        except PermissionError as e:
            error_msg = f"Error: Permission denied when writing to {file_paths.ExcelLoading}. File may be open in another program."
            if log_callback:
                log_callback(error_msg)
            raise
        except Exception as e:
            error_details = get_error_details(e, 'update_excel_loading - export')
            error_msg = f"Error exporting loading Excel: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        return excel_inputs

    except Exception as e:
        error_details = get_error_details(e, 'update_excel_loading')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise


def read_all_input_with_log(excel_inputs, file_paths, log_callback=None):
    """Wrapper for _Main_Read.read_all_input that captures print output"""
    try:
        # Read geometry data from the specified file
        if log_callback:
            log_callback("Reading geometry data...")

        path = Path(file_paths.ExcelGeometry)
        if not path.exists():
            if log_callback:
                log_callback(
                    "Geometry Excel file not found, initializing default data...")
            try:
                excel_inputs = init_input_geometry.init_input_geometry(
                    excel_inputs, file_paths)
            except Exception as e:
                error_msg = f"Error initializing geometry data: {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise

        if log_callback:
            log_callback(f"Reading geometry data from {path}")
        try:
            excel_inputs = read_geometry.read_input_geometry(excel_inputs, file_paths)
        except Exception as e:
            error_details = get_error_details(e, 'read_input_geometry')
            error_msg = f"Error reading geometry data: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        # Read property data
        if log_callback:
            log_callback("Reading property data...")

        path = Path(file_paths.ExcelProperty)
        if not path.exists():
            if log_callback:
                log_callback(
                    "Property Excel file not found, initializing default data...")
            try:
                excel_inputs = init_input_property.init_input_property(
                    excel_inputs, file_paths)
            except Exception as e:
                error_msg = f"Error initializing property data: {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise

        if log_callback:
            log_callback(f"Reading property data from {path}")
        try:
            excel_inputs = read_property.read_input_property(excel_inputs, file_paths)
        except Exception as e:
            error_details = get_error_details(e, 'read_input_property')
            error_msg = f"Error reading property data: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        # Read geology data
        if log_callback:
            log_callback("Reading geology data...")

        path = Path(file_paths.ExcelGeology)
        if not path.exists():
            if log_callback:
                log_callback(
                    "Geology Excel file not found, initializing default data...")
            try:
                excel_inputs = init_input_geology.init_input_geology(
                    excel_inputs, file_paths)
            except Exception as e:
                error_msg = f"Error initializing geology data: {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise

        if log_callback:
            log_callback(f"Reading geology data from {path}")
        try:
            excel_inputs = read_geology.read_input_geology(excel_inputs, file_paths)
        except Exception as e:
            error_details = get_error_details(e, 'read_input_geology')
            error_msg = f"Error reading geology data: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        # Read loading data
        if log_callback:
            log_callback("Reading loading data...")

        path = Path(file_paths.ExcelLoading)
        if not path.exists():
            if log_callback:
                log_callback(
                    "Loading Excel file not found, initializing default data...")
            try:
                excel_inputs = init_input_loading.init_input_loading(
                    excel_inputs, file_paths)
            except Exception as e:
                error_msg = f"Error initializing loading data: {str(e)}"
                if log_callback:
                    log_callback(error_msg)
                raise

        if log_callback:
            log_callback(f"Reading loading data from {path}")
        try:
            excel_inputs = read_loading.read_input_loading(excel_inputs, file_paths)
        except Exception as e:
            error_details = get_error_details(e, 'read_input_loading')
            error_msg = f"Error reading loading data: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        # Read steel data
        if log_callback:
            log_callback("Reading steel data...")
        try:
            excel_inputs = read_steel.read_input_steel(excel_inputs, file_paths)
        except Exception as e:
            error_details = get_error_details(e, 'read_input_steel')
            error_msg = f"Error reading steel data: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        if log_callback:
            log_callback("All input data loaded successfully")

        return excel_inputs

    except Exception as e:
        error_details = get_error_details(e, 'read_all_input_with_log')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise

def update_excel_property(file_paths, excel_inputs, log_callback=None):
    try:
        if log_callback:
            log_callback("Updating Excel property...")

        df_slab = excel_inputs.Slab.copy()
        df_bp = excel_inputs.BP.copy()
        df_beam = excel_inputs.Beam.copy()

        path_excel_input = file_paths.ExcelProperty
        path_excel_output = file_paths.ExcelProperty

        # Check if input file exists
        if not Path(path_excel_input).exists():
            error_msg = f"Error: Property Excel file not found: {path_excel_input}"
            if log_callback:
                log_callback(error_msg)
            raise FileNotFoundError(error_msg)
        
        # Safely read Excel sheets
        try:
            df_concrete = pd.read_excel(path_excel_input, sheet_name=config.SHEET_CONCRETE)
            df_steel = pd.read_excel(path_excel_input, sheet_name=config.SHEET_STEEL)
            df_rebar = pd.read_excel(path_excel_input, sheet_name=config.SHEET_REBAR)
            df_tendon = pd.read_excel(path_excel_input, sheet_name=config.SHEET_TENDON)

            df_slab_prop = pd.read_excel(path_excel_input, sheet_name=config.SHEET_SLAB_PROP)
            df_bp_prop = pd.read_excel(path_excel_input, sheet_name=config.SHEET_BP_PROP)
            df_hp_prop = pd.read_excel(path_excel_input, sheet_name=config.SHEET_HP_PROP)
            df_beam_prop = pd.read_excel(path_excel_input, sheet_name=config.SHEET_BEAM_PROP)
        except Exception as e:
            error_details = get_error_details(e, 'update_excel_property - read sheets')
            error_msg = f"Error reading property sheets: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
            raise

        # update slab prop based on rhinos
        try:
            s_slab_prop = df_slab['Slab Prop'].drop_duplicates().dropna()
            condition = ~s_slab_prop.isin(df_slab_prop['Slab Prop'])
            s_slab_prop = s_slab_prop[condition]
            if not s_slab_prop.empty:
                if log_callback:
                    log_callback(f"Adding {len(s_slab_prop)} new slab properties")
                df = s_slab_prop.str.extract(r'([A-Za-z])(\d+)_(\w+)')
                df.columns = ['Slab Prop', 'Thickness (m)', 'Material']
                df['Slab Prop'] = s_slab_prop
                df['Thickness (m)'] = df['Thickness (m)'].astype('int64') / 1000

                df_slab_prop = pd.concat([df_slab_prop, df], ignore_index=True)
        except Exception as e:
            error_msg = f"Error updating slab properties: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            raise

        # update column prop based on rhinos
        try:
            s_column_prop = df_bp['Pile Section'].drop_duplicates()
            condition = ~s_column_prop.isin(df_bp_prop['Column'])
            s_column_prop = s_column_prop[condition]
            if not s_column_prop.empty:
                if log_callback:
                    log_callback(f"Adding {len(s_column_prop)} new column properties")
                df = s_column_prop.str.extract(r'(\D+)(\d+)_(\w+)')
                df.columns = ['Column', 'Diameter (m)', 'Material']
                df['Column'] = s_column_prop
                df['Type'] = 'Circular'
                df['Diameter (m)'] = df['Diameter (m)'].astype('int64') / 1000
                df['AutoRigid (Yes/No)'] = 'Yes'

                df_bp_prop = pd.concat([df_bp_prop, df], ignore_index=True)
        except Exception as e:
            error_msg = f"Error updating column properties: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            raise

        # update beam prop based on rhinos
        try:
            s_beam_prop = df_beam['Beam Prop'].drop_duplicates()
            condition = ~s_beam_prop.isin(df_beam_prop['Beam Prop'])
            s_beam_prop = s_beam_prop[condition]
            if not s_beam_prop.empty:
                if log_callback:
                    log_callback(f"Adding {len(s_beam_prop)} new beam properties")
                df = s_beam_prop.str.extract(r'(\D)(\d+)X(\d+)_(\w+)')
                df.columns = ['Beam Prop', 'Depth (m)',
                            'Width (m)', 'Material']
                df['Beam Prop'] = s_beam_prop
                df['Depth (m)'] = df['Depth (m)'].astype('int64') / 1000
                df['Width (m)'] = df['Width (m)'].astype('int64') / 1000

                df_beam_prop = pd.concat([df_beam_prop, df], ignore_index=True)
        except Exception as e:
            error_msg = f"Error updating beam properties: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            raise        # export updated excel
        try:
            if log_callback:
                log_callback(f"Exporting updated property Excel to: {path_excel_output}")
            with pd.ExcelWriter(path_excel_output) as writer:
                df_concrete.to_excel(writer, sheet_name=config.SHEET_CONCRETE, index=False)
                df_steel.to_excel(writer, sheet_name=config.SHEET_STEEL, index=False)
                df_rebar.to_excel(writer, sheet_name=config.SHEET_REBAR, index=False)
                df_tendon.to_excel(writer, sheet_name=config.SHEET_TENDON, index=False)
                df_beam_prop.to_excel(writer, sheet_name=config.SHEET_BEAM_PROP, index=False)
                df_bp_prop.to_excel(writer, sheet_name=config.SHEET_BP_PROP, index=False)
                df_hp_prop.to_excel(writer, sheet_name=config.SHEET_HP_PROP, index=False)
                df_slab_prop.to_excel(writer, sheet_name=config.SHEET_SLAB_PROP, index=False)
        except PermissionError as e:
            error_msg = f"Error: Permission denied when writing to {path_excel_output}. File may be open in another program."
            if log_callback:
                log_callback(error_msg)
            raise
        except Exception as e:
            error_details = get_error_details(e, 'update_excel_property - export')
            error_msg = f"Error exporting property Excel: {error_details['error_message']}"
            if log_callback:
                log_callback(error_msg)
                log_callback(f"Location: {error_details['location']}")
            raise

        return excel_inputs

    except Exception as e:
        error_details = get_error_details(e, 'update_excel_property')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise


def mini_pile_spring(path_csv_mp, excel_inputs):
    # calculate mini pile spring
    df_mp_spring = excel_inputs.MP.copy()
    df_mp_spring[['Num of Rebar', 'Rebar Size (mm)']] = df_mp_spring['Pile Section'].str.split('T', expand=True)
    df_mp_spring['Num of Rebar'] = df_mp_spring['Num of Rebar'].astype(float)
    df_mp_spring['Rebar Size (mm)'] = df_mp_spring['Rebar Size (mm)'].astype(float)
    df_mp_spring['dx (m)'] = abs(df_mp_spring['X (m)'] - df_mp_spring['BX (m)'])
    df_mp_spring['dy (m)'] = abs(df_mp_spring['Y (m)'] - df_mp_spring['BY (m)'])
    df_mp_spring['dz (m)'] = abs(df_mp_spring['Pile Cap Bottom Level (mPD)'] - df_mp_spring['Founding Level (mPD)'])
    df_mp_spring['Pile Length (m)'] = (df_mp_spring['dx (m)'] ** 2 + df_mp_spring['dy (m)'] ** 2 + df_mp_spring[
        'dz (m)'] ** 2) ** 0.5
    df_mp_spring['E_Rebar (kN/mm2)'] = 200  # Young's modulus kN/mm2
    area_rebar = pi / 4 * df_mp_spring['Rebar Size (mm)'] ** 2
    df_mp_spring['Area_Rebars (mm2)'] = df_mp_spring['Num of Rebar'] * area_rebar
    df_mp_spring['Pile Stiffness (kN/m)'] = df_mp_spring['E_Rebar (kN/mm2)'] * df_mp_spring['Area_Rebars (mm2)'] / \
                                            df_mp_spring['Pile Length (m)']
    df_mp_spring['Pile Stiffness X (kN/m)'] = df_mp_spring['Pile Stiffness (kN/m)'] / df_mp_spring['Pile Length (m)'] * \
                                              df_mp_spring['dx (m)']
    df_mp_spring['Pile Stiffness Y (kN/m)'] = df_mp_spring['Pile Stiffness (kN/m)'] / df_mp_spring['Pile Length (m)'] * \
                                              df_mp_spring['dy (m)']
    df_mp_spring['Pile Stiffness Z (kN/m)'] = df_mp_spring['Pile Stiffness (kN/m)'] / df_mp_spring['Pile Length (m)'] * \
                                              df_mp_spring['dz (m)']

    excel_inputs.MPSpring = df_mp_spring
    df_mp_spring.to_csv(path_csv_mp)
    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now}: Calculated Mini-Pile Spring!')
    return excel_inputs


def init_output_loading(excel_inputs, excel_outputs):
    load_patterns = excel_inputs.LoadPat['LoadPat (Text)'].values

    # point load
    columns_title = [('Point Data', 'Point Load'),
                     ('Point Data', 'Point (Text)'),
                     ('Point Data', 'Cap Thickness (m)')]
    forces = ['Vx (kN)', 'Vy (kN)', 'Fz (kN)',
              'Mx (kNm)', 'My (kNm)',
              'Mx_Add (kNm)', 'My_Add (kNm)']
    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.PointLoad = pd.DataFrame(columns=columns)

    # line load
    columns_title = [('Line Data', 'Line Load'),
                     ('Line Data', 'Line Length (m)'),
                     ('Line Data', 'Line Theta (deg)'),
                     ('Line Data', 'Cap Thickness (m)')]

    forces = ['V1 (kN/m)', 'V3 (kN/m)', 'Fz (kN/m)', 'M1 (kNm/m)', 'M3 (kNm/m)', 'Mz (kNm/m)',
              'Fz_M3 (kN/m)', 'M1_Add (kNm)', 'M3_Add (kNm)', 'Fz_M3_Add (kN/m)',
              'V1_Start (kN/m)', 'V1_End (kN/m)',
              'V3_Start (kN/m)', 'V3_End (kN/m)',
              'Fz_Start (kN/m)', 'Fz_End (kN/m)',
              'M1_Start (kNm/m)', 'M1_End (kNm/m)',
              'Fz_Add_Start (kN/m)', 'Fz_Add_End (kN/m)',
              'M1_Add_Start (kNm/m)', 'M1_Add_End (kNm/m)']

    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.LineLoad = pd.DataFrame(columns=columns)

    # line load
    columns_title = [('Beam Data', 'Beam Mark'),
                     ('Beam Data', 'Beam Length (m)'),
                     ('Beam Data', 'Beam Theta (deg)'),
                     ('Beam Data', 'Cap Thickness (m)')]

    forces = ['V1 (kN/m)', 'V3 (kN/m)', 'Fz (kN/m)', 'M1 (kNm/m)', 'M3 (kNm/m)', 'Mz (kNm/m)',
              'Fz_M3 (kN/m)', 'M1_Add (kNm)', 'M3_Add (kNm)', 'Fz_M3_Add (kN/m)',
              'V1_Start (kN/m)', 'V1_End (kN/m)',
              'V3_Start (kN/m)', 'V3_End (kN/m)',
              'Fz_Start (kN/m)', 'Fz_End (kN/m)',
              'M1_Start (kNm/m)', 'M1_End (kNm/m)',
              'Fz_Add_Start (kN/m)', 'Fz_Add_End (kN/m)',
              'M1_Add_Start (kNm/m)', 'M1_Add_End (kNm/m)']

    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.BeamLoad = pd.DataFrame(columns=columns)

    # column load
    columns_title = [('Column Data', 'Column Mark'),
                     ('Column Data', 'Area (m2)'),
                     ('Column Data', 'Cap Thickness (m)'),
                     ('Column Data', 'Area/Point Load (A/P)')]
    forces = ['Vx (kN)', 'Vy (kN)', 'Fz (kN)', 'Vx (kPa)', 'Vy (kPa)', 'Fz (kPa)', 'Mx (kNm)', 'My (kNm)',
              'Mx_Add (kNm)', 'My_Add (kNm)']
    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.ColumnLoad = pd.DataFrame(columns=columns)

    # wall load
    columns_title = [('Wall Data', 'Wall Mark'),
                     ('Wall Data', 'Wall Length (m)'),
                     ('Wall Data', 'Wall Theta (deg)'),
                     ('Wall Data', 'Cap Thickness (m)')]

    forces = ['Vx (kN)', 'Vy (kN)', 'Fz (kN)',
              'Mx (kNm)', 'My (kNm)',
              'M1 (kNm)', 'M3 (kNm)', 'Fz_M3 (kN/m)',
              'Mx_Add (kNm)', 'My_Add (kNm)',
              'M1_Add (kNm)', 'M3_Add (kNm)', 'Fz_M3_Add (kN/m)',
              'Vx_Start (kN/m)', 'Vx_End (kN/m)',
              'Vy_Start (kN/m)', 'Vy_End (kN/m)',
              'Fz_Start (kN/m)', 'Fz_End (kN/m)',
              'M1_Start (kNm/m)', 'M1_End (kNm/m)',
              'Fz_Add_Start (kN/m)', 'Fz_Add_End (kN/m)',
              'M1_Add_Start (kNm/m)', 'M1_Add_End (kNm/m)']
    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.WallLoad = pd.DataFrame(columns=columns)

    # corewall load
    columns_title = [('CoreWall Data', 'CoreWall Mark'),
                     ('CoreWall Data', 'CoreWall Length (m)'),
                     ('CoreWall Data', 'Cap Thickness (m)')]

    forces = ['Vx (kN)', 'Vy (kN)', 'Fz (kN)', 'Mx (kNm)', 'My (kNm)',
              'Mx_Add (kNm)', 'My_Add (kNm)',
              'Vx (kN/m)', 'Vy (kN/m)', 'Fz (kN/m)', 'Mx (kNm/m)', 'My (kNm/m)',
              'Mx_Add (kNm/m)', 'My_Add (kNm/m)']
    columns_load = [(i, j) for i in load_patterns for j in forces]
    columns = pd.MultiIndex.from_tuples(columns_title + columns_load)
    excel_outputs.CoreWallLoad = pd.DataFrame(columns=columns)

    return excel_outputs
