import sys
import traceback


def get_error_details(e, function_name):
    """Get detailed error information including traceback"""
    exc_type, exc_value, exc_traceback = sys.exc_info()

    # Get the specific line where error occurred
    tb_lines = traceback.format_tb(exc_traceback)
    error_location = tb_lines[-1].strip() if tb_lines else "Unknown location"

    # Format error details
    error_details = {
        'function': function_name,
        'error_type': exc_type.__name__ if exc_type else 'Unknown',
        'error_message': str(e),
        'location': error_location,
        'full_traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    }

    return error_details
