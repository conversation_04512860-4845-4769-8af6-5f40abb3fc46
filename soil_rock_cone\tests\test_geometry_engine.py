"""
Unit tests for GeometryEngine module.
"""

import unittest
import pandas as pd
import numpy as np
import trimesh

from soil_rock_cone.geometry_engine import GeometryEngine, PileGeometry


class TestGeometryEngine(unittest.TestCase):
    """Test cases for GeometryEngine class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.engine = GeometryEngine(mesh_resolution=16)
        
        # Test pile parameters
        self.pile_params = {
            'pile_id': 'TEST_P1',
            'x_coord': 10.0,
            'y_coord': 20.0,
            'top_elevation': 100.0,
            'bottom_elevation': 80.0,
            'radius': 0.5,
            'pile_length': 20.0,
            'projection_angle': 15.0,
            'frustum_base_radius': 1.5
        }
        
    def test_initialization(self):
        """Test GeometryEngine initialization."""
        engine = GeometryEngine()
        self.assertEqual(engine.mesh_resolution, 32)
        self.assertGreater(engine.mesh_resolution, 0)
        
    def test_create_cylinder(self):
        """Test cylinder creation."""
        cylinder = self.engine._create_cylinder(
            center=(0, 0, 0),
            radius=1.0,
            height=10.0
        )
        
        self.assertIsInstance(cylinder, trimesh.Trimesh)
        self.assertGreater(len(cylinder.vertices), 0)
        self.assertGreater(len(cylinder.faces), 0)
        self.assertGreater(cylinder.volume, 0)
        
    def test_create_frustum(self):
        """Test frustum creation."""
        frustum = self.engine._create_frustum(
            center=(0, 0, 0),
            top_radius=0.5,
            bottom_radius=1.5,
            height=10.0
        )
        
        self.assertIsInstance(frustum, trimesh.Trimesh)
        self.assertGreater(len(frustum.vertices), 0)
        self.assertGreater(len(frustum.faces), 0)
        self.assertGreater(frustum.volume, 0)
        
    def test_create_pile_geometry(self):
        """Test complete pile geometry creation."""
        geometry = self.engine.create_pile_geometry(self.pile_params)
        
        self.assertIsInstance(geometry, PileGeometry)
        self.assertEqual(geometry.pile_id, 'TEST_P1')
        self.assertIsInstance(geometry.part1_cylinder, trimesh.Trimesh)
        self.assertIsInstance(geometry.part2_frustum, trimesh.Trimesh)
        self.assertIsInstance(geometry.part3_cylinder, trimesh.Trimesh)
        self.assertIsInstance(geometry.combined_mesh, trimesh.Trimesh)
        
        # Check that all parts have positive volume
        self.assertGreater(geometry.part1_cylinder.volume, 0)
        self.assertGreater(geometry.part2_frustum.volume, 0)
        self.assertGreater(geometry.part3_cylinder.volume, 0)
        self.assertGreater(geometry.combined_mesh.volume, 0)
        
    def test_union_meshes(self):
        """Test mesh union operation."""
        mesh1 = self.engine._create_cylinder((0, 0, 0), 1.0, 5.0)
        mesh2 = self.engine._create_cylinder((0, 0, 2.5), 1.0, 5.0)
        
        union_mesh = self.engine._union_meshes([mesh1, mesh2])
        
        self.assertIsInstance(union_mesh, trimesh.Trimesh)
        self.assertGreater(union_mesh.volume, 0)
        # Union volume should be less than sum of individual volumes due to overlap
        self.assertLess(union_mesh.volume, mesh1.volume + mesh2.volume)
        
    def test_validate_mesh(self):
        """Test mesh validation."""
        valid_mesh = self.engine._create_cylinder((0, 0, 0), 1.0, 5.0)
        self.assertTrue(self.engine.validate_mesh(valid_mesh))
        
        # Test with None mesh
        self.assertFalse(self.engine.validate_mesh(None))
        
    def test_create_all_pile_geometries(self):
        """Test creation of multiple pile geometries."""
        pile_data = pd.DataFrame([
            {
                'pile_id': 'P1',
                'x_coord': 10.0,
                'y_coord': 10.0,
                'top_elevation': 100.0,
                'bottom_elevation': 80.0,
                'radius': 0.5,
                'pile_length': 20.0,
                'projection_angle': 15.0,
                'frustum_base_radius': 1.5
            },
            {
                'pile_id': 'P2',
                'x_coord': 20.0,
                'y_coord': 20.0,
                'top_elevation': 95.0,
                'bottom_elevation': 75.0,
                'radius': 0.6,
                'pile_length': 20.0,
                'projection_angle': 30.0,
                'frustum_base_radius': 1.8
            }
        ])
        
        geometries = self.engine.create_all_pile_geometries(pile_data)
        
        self.assertEqual(len(geometries), 2)
        self.assertIn('P1', geometries)
        self.assertIn('P2', geometries)
        
        for pile_id, geometry in geometries.items():
            self.assertIsInstance(geometry, PileGeometry)
            self.assertEqual(geometry.pile_id, pile_id)
            
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Test with zero radius
        with self.assertRaises(ValueError):
            self.engine._create_cylinder((0, 0, 0), 0.0, 5.0)
            
        # Test with negative height
        with self.assertRaises(ValueError):
            self.engine._create_cylinder((0, 0, 0), 1.0, -5.0)
            
        # Test with invalid frustum (top radius > bottom radius for soil)
        params = self.pile_params.copy()
        params['frustum_base_radius'] = 0.3  # Smaller than pile radius
        
        # This should still work but might produce unexpected geometry
        geometry = self.engine.create_pile_geometry(params)
        self.assertIsInstance(geometry, PileGeometry)


if __name__ == '__main__':
    unittest.main()
