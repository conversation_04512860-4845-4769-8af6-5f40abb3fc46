# Development & Meta Files Structure

## 🔧 Development Configuration Files
```
.idea/                           # IDE configuration (excluded from repo)
.serena/                         # Serena MCP configuration
├── project.yml                  # Serena project configuration
├── cache/                       # Serena cache directory
└── memories/                    # Serena memory files
    ├── advanced_features_capabilities.md
    ├── code_style_conventions.md
    ├── core_architecture_modules.md
    ├── current_auth_analysis.md
    ├── detailed_security_implementation.md
    ├── development_workflow_build.md
    ├── etabs_integration_modules.md
    ├── fem_building_modules.md
    ├── foundation_agent_modules.md
    ├── foundation_design_modules.md
    ├── project_overview.md
    ├── software_integration_dependencies.md
    ├── suggested_commands.md
    └── supporting_systems_modules.md
```

## 📦 Python Cache Directories (`__pycache__/`)
Found in every Python module directory:
- Contains compiled Python bytecode (.pyc files)
- Automatically generated by Python interpreter
- Improves import performance
- Safe to delete (will be regenerated)

## 📋 Documentation Files Pattern
Throughout the project structure:
- **README.md**: Module-specific documentation
- **Integration reports**: Test results and validation
- **Refactoring summaries**: Change tracking
- **Rules and guidelines**: Design rule documentation

## 🏗️ Project Metadata
- **`.gitignore`**: Git exclusion rules
- **`requirements.txt`**: Python dependency specification
- **`nuitka embed.txt`**: Build system configuration
- **`SECURITY.md`**: Security guidelines and policies

## 📊 Total Project Statistics
- **Total directories**: 59 folders
- **Total Python files**: 200+ .py files
- **Documentation files**: 15+ README/MD files
- **Configuration files**: 10+ config files
- **Data files**: 1 Excel database (SteelSection.xlsx)
- **Build artifacts**: Multiple __pycache__ directories

## 🔍 File Organization Patterns
1. **Hierarchical module structure**: Clear parent-child relationships
2. **Consistent naming**: snake_case for Python files
3. **Documentation co-location**: README.md in each major module
4. **Test integration**: Tests alongside production code
5. **Configuration separation**: Dedicated config directories
6. **Cache management**: Automatic Python bytecode caching