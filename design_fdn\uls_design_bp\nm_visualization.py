"""
Ultimate Limit State (ULS) N-M Interaction Curve Visualization for Bored Pile Foundations

This module provides comprehensive visualization and analysis tools for N-M (axial force-moment)
interaction curves in Ultimate Limit State design of bored pile foundations. The module enables
engineers to visualize interaction curves, verify load point compliance, generate design reports,
and export analysis results in both graphical and tabular formats.

Key Features:
- Interactive N-M interaction curve plotting with multiple scaling options
- Load point verification against interaction curve boundaries with color-coded status
- Comprehensive visualization of curve key points (maximum moment, maximum/minimum axial force)
- Multi-format output generation (PNG plots, Excel spreadsheets with multiple worksheets)
- Real-time design verification with pass/fail status indicators
- Automated report generation with detailed analysis results and rebar coordination data

Author: Foundation-Automation Development Team
Version: Compatible with ULS design workflows and international foundation design standards
"""

import os
from typing import Optional, Dict, Tuple, Any
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
# Use direct imports to avoid circular import issues in the ULS design package
from .circular_column import CircularColumn, calculate_rebar_coordinates
from design_fdn.design_fdn_config import (SHEET_BP_LOAD, SHEET_BP_FAILED_LOAD,
                                          SHEET_BP_NM_CURVE, SHEET_BP_REBAR_COORD)
import matplotlib as mpl
import matplotlib.path as mplPath


def export_nm_check(column: CircularColumn, 
                   df_nm_curve: pd.DataFrame, 
                   df_load_nm: pd.DataFrame, 
                   scale: float = 1.0,
                   show: bool = False, 
                   path_output_folder: Optional[str] = None,
                   output_name: Optional[str] = None) -> bool:
    """
    Generate comprehensive N-M interaction curve visualization with load point verification.
    
    This function creates a detailed visualization of N-M interaction curves with overlaid
    load points, providing immediate visual feedback on design adequacy. The function
    supports multiple curve scaling options, automated pass/fail analysis, and comprehensive
    output generation including both graphical plots and detailed Excel reports.

    The visualization process includes:
    - N-M interaction curve plotting with optional scaling for safety margin analysis
    - Load point classification and color-coded status display (Pass/Warning/Fail)
    - Key point identification and annotation (maximum moment, axial force extremes)
    - Comprehensive design information display including steel ratio and verification status
    - Multi-format output generation for documentation and reporting purposes
    """
    # Configure matplotlib for non-interactive backend to ensure compatibility
    # with automated processing environments and prevent GUI dependencies
    mpl.use('Agg')
    plt.ioff()  # Turn off interactive mode for batch processing
    
    # Extract key points from the interaction curve for annotation display
    # These points represent critical design values used in foundation analysis
    m_max_idx = df_nm_curve['M (kNm)'].idxmax()  # Maximum moment capacity point
    n_max_idx = df_nm_curve['N (kN)'].idxmax()   # Maximum compression capacity
    n_min_idx = df_nm_curve['N (kN)'].idxmin()   # Maximum tension capacity (if applicable)
    
    # Organize key points for display and analysis reference
    key_points = {
        'm_max': (df_nm_curve.at[m_max_idx, 'M (kNm)'], df_nm_curve.at[m_max_idx, 'N (kN)']),
        'n_max': (df_nm_curve.at[n_max_idx, 'M (kNm)'], df_nm_curve.at[n_max_idx, 'N (kN)']),
        'n_min': (df_nm_curve.at[n_min_idx, 'M (kNm)'], df_nm_curve.at[n_min_idx, 'N (kN)'])
    }
    
    # Create figure with optimal dimensions for engineering visualization
    fig, ax = plt.subplots(figsize=(14, 6))  # Wide format for better curve visibility
    
    # Extract curve data for plotting and analysis
    curve_m = df_nm_curve['M (kNm)'].values  # Moment capacity array
    curve_n = df_nm_curve['N (kN)'].values   # Axial force capacity array
    
    # Plot primary interaction curve (100% capacity)
    ax.plot(curve_m, curve_n, label='100%', linewidth=2, color='blue')
    
    # Plot scaled curve if different scaling factor is applied
    # This enables safety margin visualization and conservative design verification
    if scale != 1:
        ax.plot(curve_m * scale, curve_n * scale, 
               linestyle='--', label=f'{int(scale * 100)}%', 
               linewidth=1.5, color='orange')
    
    # Extract load point data for verification analysis
    load_m = df_load_nm['M (kNm)'].values  # Applied moment values
    load_n = df_load_nm['N (kN)'].values   # Applied axial force values    
    # Create polygon paths for geometric containment analysis
    # This enables precise determination of load point positions relative to interaction curves
    curve_points = np.column_stack((curve_m, curve_n))  # Combine M and N into coordinate pairs
    poly_path_100 = mplPath.Path(curve_points)  # Create path object for 100% curve
    
    # Create scaled curve path if scaling is applied
    if scale != 1:
        scaled_points = np.column_stack((curve_m * scale, curve_n * scale))
        poly_path_scaled = mplPath.Path(scaled_points)
    else:
        poly_path_scaled = poly_path_100  # Use same path if no scaling
    
    # Perform geometric containment analysis for load point classification
    load_points = np.column_stack((load_m, load_n))  # Combine load M and N coordinates
    inside_100 = poly_path_100.contains_points(load_points)      # Check against 100% curve
    inside_scaled = poly_path_scaled.contains_points(load_points) # Check against scaled curve
    
    # Classify load points based on containment analysis results
    # This creates a hierarchical classification system for design verification
    mask_pass = inside_scaled           # Points within scaled curve (safe with margin)
    mask_warning = inside_100 & ~inside_scaled  # Points between curves (review needed)
    mask_fail = ~inside_100            # Points outside 100% curve (unsafe)
    
    # Plot load points with color-coded status for immediate visual feedback
    if np.any(mask_pass):
        ax.scatter(load_m[mask_pass], load_n[mask_pass], 
                  s=20, color='green', alpha=0.6, label='Pass', zorder=3)
    if np.any(mask_warning):
        ax.scatter(load_m[mask_warning], load_n[mask_warning], 
                  s=20, color='orange', alpha=0.6, label='Warning', zorder=3)
    if np.any(mask_fail):
        ax.scatter(load_m[mask_fail], load_n[mask_fail], 
                  s=20, color='red', alpha=0.6, label='Fail', zorder=3)
    
    # Determine overall design verification status
    # All points must be within 100% curve for design approval
    bool_pass = np.all(inside_100)
    
    # Add comprehensive annotations including key points and design information
    _add_text_annotations(ax, key_points, column.steel_ratio, bool_pass)
    
    # Configure plot appearance for professional presentation
    ax.set_title(f"N-M Interaction Curve - {output_name or column.segment_mark}", 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('Bending Moment M (kNm)', fontsize=12)
    ax.set_ylabel('Axial Load N (kN)', fontsize=12)
    ax.grid(True, alpha=0.3)  # Subtle grid for easier reading
    ax.legend(loc='upper left', fontsize=10)
    
    # Generate comprehensive outputs if file paths are provided
    if path_output_folder and output_name:
        _save_outputs(fig, column, df_nm_curve, df_load_nm, 
                     path_output_folder, output_name, bool_pass, inside_100)
    
    # Display plot interactively if requested
    if show:
        plt.show()
    
    # Clean up resources to prevent memory leaks in batch processing
    plt.close(fig)
    
    return bool_pass


def _add_text_annotations(ax: plt.Axes, 
                         key_points: Dict[str, Tuple[float, float]], 
                         steel_ratio: float, 
                         check_pass: bool) -> None:
    """
    Add comprehensive text annotations to the N-M interaction curve plot.
    
    This helper function enhances the visualization by adding critical design information
    directly to the plot, including key curve points, steel ratio information, and
    overall verification status. The annotations are positioned to avoid interfering
    with the curve visualization while providing maximum informational value.

    The annotation system includes:
    - Key point coordinates (maximum moment, axial force extremes) with formatted values
    - Steel reinforcement ratio display with percentage formatting
    - Overall design verification status with color-coded emphasis
    - Professional formatting with consistent typography and positioning
    """
    # Display key curve points with formatted coordinates
    # Start from top and work downward to avoid overlapping
    text_y = 1.15  # Start above the plot area using axes coordinates
    
    for label, (m, n) in key_points.items():
        # Format label for display (convert underscore notation to readable text)
        label_text = label.replace('_', ' ').title()  # 'm_max' becomes 'M Max'
        
        # Display coordinates with rounded values for engineering clarity
        ax.text(0, text_y, f"{label_text}: ({round(m)}, {round(n)})",
               transform=ax.transAxes, fontsize=10, 
               verticalalignment='top', fontfamily='monospace')
        text_y -= 0.05  # Move down for next annotation
    
    # Display steel reinforcement ratio in upper right corner
    # Convert decimal to percentage with appropriate precision
    ax.text(1, 1.1, f"Steel Ratio: {round(steel_ratio * 100, 2)}%",
           transform=ax.transAxes, ha='right', fontsize=12,
           fontweight='bold', color='navy')
    
    # Display overall verification status with color coding for immediate recognition
    check_text = "OK" if check_pass else "NOT OK"
    check_color = "green" if check_pass else "red"
    
    ax.text(1, 1, f"Check: {check_text}",
           transform=ax.transAxes, ha='right', fontsize=12,
           color=check_color, weight='bold', 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))


def _save_outputs(fig: plt.Figure, 
                 column: CircularColumn, 
                 df_nm_curve: pd.DataFrame,
                 df_load_nm: pd.DataFrame, 
                 output_folder: str, 
                 output_name: str,
                 bool_pass: bool, 
                 inside_100: np.ndarray) -> None:
    """
    Save comprehensive analysis outputs including plots and detailed Excel reports.
    
    This function generates professional-quality output files for design documentation,
    including high-resolution plots and multi-sheet Excel workbooks with organized
    analysis data. The outputs are suitable for engineering reports, client presentations,
    and regulatory submissions requiring detailed foundation design verification.

    The output generation process includes:
    - High-resolution PNG plot generation with optimized formatting
    - Multi-sheet Excel workbook creation with organized data tables
    - Failed load identification and detailed analysis breakdown
    - Rebar coordinate data for reinforcement layout verification
    - Automated directory creation and file management
    """
    # Create output directory structure if it doesn't exist
    # This ensures safe file operations even in new project environments
    os.makedirs(output_folder, exist_ok=True)
    
    # Generate high-resolution PNG plot for reports and presentations
    plot_path = os.path.join(output_folder, f"{output_name}.png")
    fig.savefig(plot_path, dpi=150, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    
    # Calculate rebar coordinates for reinforcement layout documentation
    # This provides comprehensive geometry data for construction drawings
    df_rebar = calculate_rebar_coordinates(column)
    
    # Identify failed load cases for detailed analysis and reporting
    if not bool_pass and len(inside_100) > 1:
        # Extract load cases that exceed the interaction curve boundary
        df_failed = df_load_nm.iloc[~inside_100].copy()
    else:
        # Create empty DataFrame with consistent column structure for no failures
        df_failed = pd.DataFrame(columns=df_load_nm.columns)
    
    # Generate comprehensive Excel workbook with multiple analysis sheets
    excel_path = os.path.join(output_folder, f"{output_name}.xlsx")
    
    # Use ExcelWriter for efficient multi-sheet operations
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # Sheet 1: Complete load case data for verification analysis
        df_load_nm.to_excel(writer, sheet_name=SHEET_BP_LOAD, index=False)
        
        # Sheet 2: Failed load cases requiring design attention
        df_failed.to_excel(writer, sheet_name=SHEET_BP_FAILED_LOAD, index=False)
        
        # Sheet 3: N-M interaction curve coordinates for further analysis
        df_nm_curve.to_excel(writer, sheet_name=SHEET_BP_NM_CURVE, index=False)
        
        # Sheet 4: Rebar coordinate data for reinforcement layout verification
        df_rebar.to_excel(writer, sheet_name=SHEET_BP_REBAR_COORD, index=False)
