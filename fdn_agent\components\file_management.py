"""File management components"""
import tkinter as tk
from pathlib import Path
from tkinter import filedialog, messagebox
from tkinter import ttk


class FileManagementFrame:
    """File selection and management interface"""
    
    def __init__(self, parent, folder_path_var, existing_folder_path_var, output_dir_var, log_callback, initialize_callback):
        self.parent = parent
        self.folder_path_var = folder_path_var
        self.existing_folder_path_var = existing_folder_path_var
        self.output_dir_var = output_dir_var
        self.log_callback = log_callback
        self.initialize_callback = initialize_callback
        self.create_ui()
    
    def create_ui(self):
        """Create file management interface"""
        file_frame = ttk.LabelFrame(self.parent, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input folder selection
        self._create_folder_row(file_frame, "Input Folder:", self.folder_path_var, self.browse_folder)
        
        # Existing input folder selection
        self._create_folder_row(file_frame, "Existing Input:", self.existing_folder_path_var, self.browse_existing_folder)
        
        # Initialize button
        init_btn_frame = ttk.Frame(file_frame)
        init_btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        init_btn = ttk.Button(init_btn_frame, text="1. Initialize Setup", 
                             command=self.initialize_callback, width=25)
        init_btn.pack(pady=5)
    
    def _create_folder_row(self, parent, label_text, path_var, browse_command):
        """Create a folder selection row"""
        folder_frame = ttk.Frame(parent)
        folder_frame.pack(fill=tk.X, pady=3)
        
        ttk.Label(folder_frame, text=label_text, width=20, anchor="w").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(folder_frame, textvariable=path_var, font=("Arial", 9)).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(folder_frame, text="Browse...", width=12, command=browse_command).pack(side=tk.LEFT)
    
    def browse_folder(self):
        """Browse for input folder"""
        folder_path = filedialog.askdirectory(title="Select Input Folder")
        if folder_path:
            folder_path = str(Path(folder_path))
            self.folder_path_var.set(folder_path)
            
            # Auto-set output directory
            output_dir = str(Path(folder_path) / "pile_estimation_results")
            self.output_dir_var.set(output_dir)
            
            self.log_callback(f"Selected input folder: {folder_path}")
            self.log_callback(f"Output directory auto-set to: {output_dir}")
    
    def browse_existing_folder(self):
        """Browse for existing input folder"""
        folder_path = filedialog.askdirectory(title="Select Existing Input Folder")
        if folder_path:
            folder_path = str(Path(folder_path))
            self.existing_folder_path_var.set(folder_path)
            
            # Auto-set output directory
            output_dir = str(Path(folder_path) / "pile_estimation_results")
            self.output_dir_var.set(output_dir)
            
            self.log_callback(f"Selected existing input folder: {folder_path}")
            self.log_callback(f"Output directory auto-set to: {output_dir}")
    
    def browse_output_directory(self):
        """Browse for output directory"""
        # Default to input folder if available
        initial_dir = self.folder_path_var.get().strip() if self.folder_path_var.get().strip() else None
        
        directory_path = filedialog.askdirectory(
            title="Select Output Directory (should be within Input Folder)",
            initialdir=initial_dir
        )
        if directory_path:
            self.output_dir_var.set(str(Path(directory_path)))
            self.log_callback(f"Output directory set to: {directory_path}")
