﻿"""
Element Class and Utilities

Contains the internal Element class and related geometry utilities for structural element representation.
"""

from typing import List, <PERSON><PERSON>
from ..exceptions import GeometryError
from ..utils.logging_utils import (
    enhanced_log, log_function_entry, log_function_exit, 
    log_calculation_result, log_error_with_context, log_validation_result
)


class _Element:
    """Internal representation of a structural element for grouping."""
    __slots__ = ['name', 'element_type', 'points', 'centroid', 'data']
    
    def __init__(self, name: str, element_type: str, points: List[Tuple[float, float, float]], 
                 data, log_callback=None):
        """
        Initialize a structural element for clustering.
        
        Args:
            name: Element name/identifier
            element_type: Type of element ('column' or 'wall')
            points: List of 3D points representing the element
            data: Original element data
            log_callback: Optional callback for logging element creation
        """
        log_function_entry(log_callback, f"_Element.__init__", 
                          name=name, element_type=element_type, 
                          points_count=len(points))
        
        try:
            # Validate inputs
            if not name:
                raise GeometryError("Element name cannot be empty")
            if element_type not in ['column', 'wall']:
                raise GeometryError(f"Invalid element type: {element_type}")
            if not points:
                raise GeometryError("Element must have at least one point")
            
            # Validate point format
            for i, point in enumerate(points):
                if not isinstance(point, (tuple, list)) or len(point) != 3:
                    raise GeometryError(f"Point {i} must be a 3D coordinate tuple/list")
                try:
                    # Ensure all coordinates are numeric
                    float(point[0]), float(point[1]), float(point[2])
                except (ValueError, TypeError):
                    raise GeometryError(f"Point {i} coordinates must be numeric")
            
            log_validation_result(log_callback, "Element Input Validation", True, 
                                f"name='{name}', type={element_type}, {len(points)} points")
            
            self.name = name
            self.element_type = element_type
            self.points = points
            self.data = data
            
            # Calculate centroid
            enhanced_log(log_callback, f"Computing centroid for {element_type} '{name}'", 'DEBUG')
            self.centroid = calculate_centroid_3d(points, log_callback)
            
            # Log element creation details
            self._log_element_details(log_callback)
            
            log_function_exit(log_callback, f"_Element.__init__", f"Element '{name}' created successfully")
            
        except Exception as e:
            log_error_with_context(log_callback, e, f"_Element.__init__ for '{name}'")
            raise
    
    def _log_element_details(self, log_callback):
        """Log detailed information about the created element."""
        x, y, z = self.centroid
        log_calculation_result(log_callback, f"{self.element_type.title()} Centroid", 
                             f"({x:.2f}, {y:.2f}, {z:.2f})", "coordinates")
        
        if self.element_type == 'column':
            enhanced_log(log_callback, f"Created column element '{self.name}' at centroid ({x:.2f}, {y:.2f}, {z:.2f})", 'DEBUG')
        elif self.element_type == 'wall':
            # Calculate wall length
            wall_length = 0.0
            if len(self.points) > 1:
                for i in range(len(self.points) - 1):
                    x1, y1, z1 = self.points[i]
                    x2, y2, z2 = self.points[i + 1]
                    segment_length = ((x2 - x1)**2 + (y2 - y1)**2 + (z2 - z1)**2)**0.5
                    wall_length += segment_length
            
            log_calculation_result(log_callback, f"Wall '{self.name}' Length", wall_length, "meters")
            enhanced_log(log_callback, f"Created wall element '{self.name}' with {len(self.points)} points, "
                                     f"length {wall_length:.2f}m, centroid ({x:.2f}, {y:.2f}, {z:.2f})", 'DEBUG')
    
    def get_bounding_box(self, log_callback=None) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """
        Calculate the 3D bounding box of the element.
        
        Args:
            log_callback: Optional callback for logging bounding box calculation
            
        Returns:
            Tuple of (min_point, max_point) defining the bounding box
        """
        log_function_entry(log_callback, f"_Element.get_bounding_box", element_name=self.name)
        
        try:
            if not self.points:
                raise GeometryError(f"Element '{self.name}' has no points for bounding box calculation")
            
            # Find min and max coordinates
            xs = [p[0] for p in self.points]
            ys = [p[1] for p in self.points]
            zs = [p[2] for p in self.points]
            
            min_point = (min(xs), min(ys), min(zs))
            max_point = (max(xs), max(ys), max(zs))
            
            # Calculate bounding box dimensions
            width = max_point[0] - min_point[0]
            height = max_point[1] - min_point[1]
            depth = max_point[2] - min_point[2]
            
            log_calculation_result(log_callback, f"Bounding Box Dimensions for '{self.name}'", 
                                 f"W:{width:.2f} × H:{height:.2f} × D:{depth:.2f}", "meters")
            log_calculation_result(log_callback, f"Bounding Box Min Point", 
                                 f"({min_point[0]:.2f}, {min_point[1]:.2f}, {min_point[2]:.2f})", "coordinates")
            log_calculation_result(log_callback, f"Bounding Box Max Point", 
                                 f"({max_point[0]:.2f}, {max_point[1]:.2f}, {max_point[2]:.2f})", "coordinates")
            
            result = (min_point, max_point)
            log_function_exit(log_callback, f"_Element.get_bounding_box", "bounding box calculated")
            return result
            
        except Exception as e:
            log_error_with_context(log_callback, e, f"_Element.get_bounding_box for '{self.name}'")
            raise
    
    def distance_to(self, other: '_Element', log_callback=None) -> float:
        """
        Calculate the 3D distance between this element and another element.
        
        Args:
            other: Another _Element instance
            log_callback: Optional callback for logging distance calculation
            
        Returns:
            Euclidean distance between element centroids
        """
        log_function_entry(log_callback, f"_Element.distance_to", 
                          from_element=self.name, to_element=other.name)
        
        try:
            if not isinstance(other, _Element):
                raise GeometryError("Distance calculation requires another _Element instance")
            
            # Calculate Euclidean distance between centroids
            dx = self.centroid[0] - other.centroid[0]
            dy = self.centroid[1] - other.centroid[1]
            dz = self.centroid[2] - other.centroid[2]
            
            distance = (dx*dx + dy*dy + dz*dz)**0.5
            
            log_calculation_result(log_callback, f"Distance '{self.name}' to '{other.name}'", 
                                 distance, "meters")
            enhanced_log(log_callback, f"Distance components - dx:{dx:.2f}, dy:{dy:.2f}, dz:{dz:.2f}", 'DEBUG')
            
            log_function_exit(log_callback, f"_Element.distance_to", f"{distance:.3f}m")
            return distance
            
        except Exception as e:
            log_error_with_context(log_callback, e, f"_Element.distance_to from '{self.name}' to '{other.name if hasattr(other, 'name') else 'unknown'}'")
            raise
    
    def __str__(self) -> str:
        """String representation of the element."""
        return f"{self.element_type.title()}('{self.name}', centroid={self.centroid}, points={len(self.points)})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the element."""
        return f"_Element(name='{self.name}', type='{self.element_type}', points={len(self.points)}, centroid={self.centroid})"


def calculate_centroid_3d(points: List[Tuple[float, float, float]], log_callback=None) -> Tuple[float, float, float]:
    """
    Calculate the centroid of a set of 3D points.
    
    Args:
        points: List of 3D coordinate tuples
        log_callback: Optional callback for logging centroid calculation
        
    Returns:
        Centroid coordinates as (x, y, z) tuple
        
    Raises:
        GeometryError: If point list is empty or contains invalid points
    """
    log_function_entry(log_callback, "calculate_centroid_3d", points_count=len(points))
    
    try:
        # Validate input
        if not points:
            raise GeometryError("Cannot calculate centroid of empty point list")
        
        if len(points) == 1:
            enhanced_log(log_callback, "Single point provided, centroid equals the point", 'DEBUG')
            result = points[0]
            log_function_exit(log_callback, "calculate_centroid_3d", f"({result[0]:.3f}, {result[1]:.3f}, {result[2]:.3f})")
            return result
        
        # Validate point format and collect coordinates
        valid_points = []
        for i, point in enumerate(points):
            if not isinstance(point, (tuple, list)) or len(point) != 3:
                raise GeometryError(f"Point {i} must be a 3D coordinate tuple/list")
            try:
                # Convert to float and validate
                x, y, z = float(point[0]), float(point[1]), float(point[2])
                valid_points.append((x, y, z))
            except (ValueError, TypeError):
                raise GeometryError(f"Point {i} coordinates must be numeric: {point}")
        
        log_validation_result(log_callback, "Point Validation", True, f"{len(valid_points)} valid points")
        
        # Calculate centroid
        enhanced_log(log_callback, f"Computing centroid from {len(valid_points)} points", 'DEBUG')
        sum_x = sum(p[0] for p in valid_points)
        sum_y = sum(p[1] for p in valid_points)
        sum_z = sum(p[2] for p in valid_points)
        n = len(valid_points)
        
        centroid = (sum_x / n, sum_y / n, sum_z / n)
        
        # Log calculation details
        log_calculation_result(log_callback, "Sum of X coordinates", sum_x, "units")
        log_calculation_result(log_callback, "Sum of Y coordinates", sum_y, "units") 
        log_calculation_result(log_callback, "Sum of Z coordinates", sum_z, "units")
        log_calculation_result(log_callback, "Centroid", f"({centroid[0]:.3f}, {centroid[1]:.3f}, {centroid[2]:.3f})", "coordinates")
        
        log_function_exit(log_callback, "calculate_centroid_3d", f"({centroid[0]:.3f}, {centroid[1]:.3f}, {centroid[2]:.3f})")
        return centroid
        
    except Exception as e:
        log_error_with_context(log_callback, e, "calculate_centroid_3d")
        raise

