# SAFE22 Integration Package

**Advanced Finite Element Analysis Integration for Foundation Automation System**

This package provides comprehensive Python integration with SAFE22 software, CSI's advanced finite element analysis platform for reinforced concrete slab design and analysis. Designed specifically for the Foundation Automation system, it enables sophisticated structural analysis workflows with enhanced material modeling, nonlinear analysis capabilities, and modern design code compliance.

## Overview

SAFE22 represents the latest generation of finite element analysis software for reinforced concrete structures, offering significant advances over SAFE16 including:

- **Enhanced Material Models**: Advanced stress-strain relationships, nonlinear concrete behavior, and sophisticated steel modeling
- **Improved Analysis Capabilities**: Cracking analysis, post-tensioning effects, and dynamic analysis options  
- **Modern Design Integration**: Updated design codes, automated reinforcement design, and punching shear analysis
- **Advanced Meshing**: Intelligent mesh generation, localized refinement, and edge constraint management
- **Comprehensive Load Handling**: Complex load combinations, construction sequence analysis, and time-dependent effects

## Package Architecture

### Core Data Management

#### `safe22_class.py` - Central Data Structure Management
- **Safe22DataFrames Class**: Comprehensive data container for all SAFE22 model components
- **Multi-Level DataFrame Management**: Handles complex nested data structures with MultiIndex columns
- **Analysis Options Control**: Advanced modeling settings, cracking analysis parameters, and SAPFire integration
- **Data Validation**: Automatic consistency checking and error detection across related data tables
- **Memory Optimization**: Efficient handling of large structural models with thousands of elements

**Key Features:**
- Standardized DataFrame initialization for 100+ SAFE22 data tables
- Automatic column structure generation with proper MultiIndex formatting
- Built-in data validation and consistency checking
- Support for both 2D and 3D analysis models
- Integration with advanced SAFE22 features (cracking analysis, design optimization)

#### `safe22_geometry.py` - Advanced Geometric Processing
- **3D Coordinate Management**: Full 3D modeling capabilities with coordinate system transformations
- **Complex Object Connectivity**: Point, line, and area object relationships with automatic connectivity
- **Mesh Generation Control**: Intelligent meshing with size control and refinement options
- **Edge Constraint Management**: Sophisticated boundary condition handling and constraint definitions
- **Group Management**: Hierarchical object organization for complex structural systems

**Advanced Capabilities:**
- Automatic mesh generation with size optimization
- Complex slab geometry with openings and irregular shapes
- Beam-slab connectivity with automatic joint detection
- Coordinate system transformations and local axes management
- Support for curved elements and complex geometries

#### `safe22_material.py` - Comprehensive Material Property Management
- **Multi-Material Support**: Concrete, steel, rebar, and tendon materials with full property definitions
- **Nonlinear Behavior Modeling**: Advanced stress-strain relationships for accurate analysis
- **Code-Compliant Properties**: Material properties meeting international design code requirements
- **Automatic Property Calculations**: Derived properties (shear modulus, etc.) based on material type
- **Unit System Management**: Consistent unit handling with automatic conversions

**Material Modeling Features:**
- **Concrete**: Compressive strength, modulus, stress-strain curves, cracking behavior
- **Steel**: Yield strength, ultimate strength, strain hardening, hysteretic behavior
- **Rebar**: Reinforcement properties, bond-slip relationships, development lengths
- **Tendon**: Prestressing steel properties, relaxation characteristics, anchorage behavior

### Structural Element Processing

#### `safe22_str_prop.py` - Structural Property Definitions
- **Cross-Section Management**: Comprehensive section property definitions for all element types
- **Advanced Section Types**: Rectangular, circular, T-beam, L-beam, and custom section support
- **Material Assignment**: Automatic material property assignment to structural elements
- **Design Integration**: Section properties optimized for design code requirements
- **Property Modifiers**: Support for cracked section properties and stiffness modifications

**Section Types Supported:**
- **Beams**: Rectangular, T-beam, L-beam, general shapes with reinforcement details
- **Columns**: Rectangular, circular, composite sections with confinement modeling
- **Slabs**: Solid slabs, ribbed slabs, waffle slabs with reinforcement patterns
- **Walls**: Shear walls with opening definitions and boundary elements

#### `safe22_bp_shp_dhp.py` - Beam Properties and Shape Definitions
- **Advanced Beam Modeling**: Complex beam cross-sections with detailed geometric properties
- **Shape Library Management**: Standard and custom beam shapes with parametric definitions
- **Dimension Control**: Precise geometric control for beam sizing and proportioning
- **Integration with Design**: Beam properties optimized for strength and serviceability requirements
- **Construction Sequence**: Support for staged construction and time-dependent analysis

### Load Analysis and Combinations

#### `safe22_load.py` - Advanced Load Management
- **Comprehensive Load Types**: Dead, live, wind, seismic, thermal, and construction loads
- **Load Pattern Definitions**: Flexible load pattern creation with spatial and temporal variations
- **Dynamic Load Analysis**: Support for response spectrum and time history analysis
- **Load Assignment**: Automatic load assignment to structural elements with proper distribution
- **Load Verification**: Built-in load checking and validation against design requirements

**Load Types and Applications:**
- **Static Loads**: Uniform, concentrated, line loads with precise positioning
- **Dynamic Loads**: Seismic response spectrum, wind loads, vibration analysis
- **Thermal Loads**: Temperature effects, thermal gradients, and expansion joints
- **Construction Loads**: Staged loading, construction sequence, and temporary conditions

#### `safe22_load_comb.py` - Sophisticated Load Combination Management
- **Code-Based Combinations**: Automatic generation of design load combinations per building codes
- **Ultimate Limit State (ULS)**: Strength-based combinations for member design
- **Serviceability Limit State (SLS)**: Service-based combinations for deflection and cracking
- **Envelope Analysis**: Automatic envelope generation for maximum design forces
- **Wind-Specific Analysis**: Specialized wind load combinations and directional effects

**Advanced Combination Features:**
- **Automatic Envelope Generation**: Maximum and minimum force envelopes for design
- **Settlement Analysis**: Differential settlement combinations and foundation effects
- **Construction Sequence**: Time-dependent load combinations for staged construction
- **Design Optimization**: Load combinations optimized for efficient design

### Export and Integration

#### `safe22_export.py` - Model Export and File Management
- **Native SAFE22 Format**: Direct export to SAFE22 model files (.f2k format)
- **Excel Integration**: Comprehensive Excel export for data review and manual editing
- **Data Validation**: Pre-export validation ensuring model completeness and consistency
- **File Management**: Robust file handling with error recovery and backup capabilities
- **Version Control**: Model versioning and change tracking for design iterations

## Technical Specifications

### Analysis Capabilities

**Linear Analysis:**
- Static analysis with full 3D behavior
- Modal analysis for dynamic properties
- Response spectrum analysis for seismic design
- Linear elastic material behavior

**Nonlinear Analysis:**
- Material nonlinearity with advanced stress-strain curves
- Geometric nonlinearity for large displacement effects
- Cracking analysis with tension stiffening
- Post-tensioning effects and time-dependent behavior

**Design Integration:**
- Automated reinforcement design per ACI, Eurocode, and other international codes
- Punching shear analysis with detailed capacity calculations
- Deflection analysis with long-term effects
- Crack width calculations and serviceability checks

### Material Models

**Concrete Behavior:**
- Compressive strength: 20-100 MPa (C20-C100 grades)
- Stress-strain relationships: Parabolic, linear, and user-defined curves
- Cracking behavior: Tension stiffening, crack spacing, and width calculations
- Time-dependent effects: Creep, shrinkage, and aging

**Steel Behavior:**
- Yield strength: 235-690 MPa (S235-S690 grades)
- Stress-strain curves: Elastic-plastic, strain hardening, and ultimate failure
- Hysteretic behavior: Kinematic and isotropic hardening models
- Temperature effects: High-temperature behavior and fire resistance

**Reinforcement Modeling:**
- Rebar grades: Grade 40, 60, 80, 100 (275-690 MPa yield strength)
- Bond-slip relationships: Perfect bond and detailed bond models
- Development lengths: Code-based calculations with modification factors
- Confinement effects: Confined concrete behavior with stirrup interaction

**Prestressing Steel:**
- Tendon grades: Grade 250, 270, 300 (1725-2070 MPa ultimate strength)
- Relaxation characteristics: Low-relaxation and stress-relieved strands
- Anchorage behavior: Detailed anchorage zone modeling
- Time-dependent losses: Elastic shortening, creep, shrinkage, and relaxation

### Unit System and Conventions

**Primary Units:**
- Force: kN (kiloNewtons)
- Length: m (meters)  
- Stress: kN/m² (converted to MPa for display)
- Temperature: °C (Celsius)
- Time: days (for time-dependent analysis)

**Coordinate System:**
- Right-hand coordinate system (X-Y-Z)
- Z-axis vertical (positive upward)
- Global and local coordinate systems supported
- Automatic coordinate transformations

## Installation and Setup

### Prerequisites

```python
# Required packages
pandas >= 1.3.0          # DataFrame operations and data manipulation
numpy >= 1.20.0          # Numerical computations and array operations
openpyxl >= 3.0.0        # Excel file operations and formatting
typing >= 3.7.0          # Type hints for improved code clarity
```

### Basic Setup

```python
from build_fem.write_safe.safe22.safe22_class import Safe22DataFrames
from build_fem.write_safe.safe22.safe22_material import material_MatPropGeneral_safe22
from build_fem.write_safe.safe22.safe22_geometry import geometry_PointCoordinates_safe22

# Initialize SAFE22 data structure
safe22_dfs = Safe22DataFrames()

# Verify initialization
print(f"Initialized {len(safe22_dfs.__dict__)} SAFE22 data tables")
print(f"Analysis options: {safe22_dfs.AnalysisOptions.shape}")
```

## Usage Examples

### Complete Model Creation Workflow

```python
# 1. Initialize SAFE22 model
from build_fem.write_safe.safe22 import *

safe22_model = Safe22DataFrames()

# 2. Define materials
material_data = {
    'Concrete': pd.DataFrame({
        'Material': ['C30', 'C40'], 
        'Type': ['Concrete', 'Concrete'],
        'Fc (kN/m2)': [30000, 40000],
        'UnitWt (kN/m3)': [25, 25],
        'E (kN/m2)': [30000000, 35000000],
        'U (Unitless)': [0.2, 0.2],
        'A (1/C)': [1e-5, 1e-5]
    }),
    'Steel': pd.DataFrame({
        'Material': ['S355'], 
        'Type': ['Steel'],
        'Fy (kN/m2)': [355000],
        'Fu (kN/m2)': [510000],
        'UnitWt (kN/m3)': [78.5],
        'E (kN/m2)': [200000000],
        'U (Unitless)': [0.3],
        'A (1/C)': [1.2e-5]
    }),
    'Rebar': pd.DataFrame({
        'Material': ['REBAR_60'], 
        'Type': ['Rebar'],
        'Fy (kN/m2)': [420000],
        'Fu (kN/m2)': [620000],
        'UnitWt (kN/m3)': [78.5],
        'E (kN/m2)': [200000000],
        'A (1/C)': [1.2e-5]
    })
}

# Process materials
safe22_model, materials = material_MatPropGeneral_safe22(material_data, safe22_model)
safe22_model, mech_props = material_MatPropBasicMechProps_safe22(material_data, safe22_model)
safe22_model, concrete_props = material_MatPropConcreteData_safe22(material_data, safe22_model)
safe22_model, steel_props = materials_MatPropSteelData_safe22(material_data, safe22_model)
safe22_model, rebar_props = material_MatPropRebarData_safe22(material_data, safe22_model)

print(f"Processed {len(materials)} materials with full property definitions")

# 3. Define geometry
geometry_data = pd.DataFrame({
    'Point': ['P1', 'P2', 'P3', 'P4'],
    'CoordSys': ['GLOBAL', 'GLOBAL', 'GLOBAL', 'GLOBAL'],
    'CoordType': ['Cartesian', 'Cartesian', 'Cartesian', 'Cartesian'],
    'XorR': [0, 10, 10, 0],
    'Y': [0, 0, 8, 8],
    'Z': [0, 0, 0, 0]
})

safe22_model, points = geometry_PointCoordinates_safe22(geometry_data, safe22_model)
print(f"Created {len(points)} point coordinates")

# 4. Export model
from build_fem.write_safe.safe22.safe22_export import export_safe22_f2k

file_paths = type('obj', (object,), {'f2kSAFE22Model': 'example_model.f2k'})
content = export_safe22_f2k(file_paths, safe22_model)
print(f"Exported SAFE22 model with {len(content)} characters")
```

### Advanced Analysis Configuration

```python
# Configure advanced analysis options
safe22_model.AnalysisOptions.loc[0] = {
    'TwoDimensionalOnly': 'No',
    'RigidDiaphragmAtTop': 'No', 
    'IgnoreVerticalOffsets': 'Yes',
    'AutomaticMeshGeneration': 'Yes',
    'MaximumMeshSize': 1.0,
    'MeshOption': 'Rectangular',
    'LocalizedMeshing': 'No'
}

# Configure cracking analysis
safe22_model.CrackingAnalysisOptions.loc[0] = {
    'ReinforcementSource': 'User and Designed',
    'MinimumTensionRatio': 0.0018,
    'MinimumCompressionRatio': 0.0,
    'AnalyzeForCracking': 'Yes',
    'IncludeTensionStiffening': 'Yes'
}

print("Configured advanced analysis options for nonlinear analysis")
```

### Load Combination Generation

```python
# Define load patterns
load_patterns = pd.DataFrame({
    'LoadPattern': ['DEAD', 'LIVE', 'WIND_X', 'SEISMIC_X'],
    'Type': ['Dead', 'Live', 'Wind', 'Seismic'],
    'SelfWtMult': [1.0, 0.0, 0.0, 0.0]
})

# Generate ULS combinations
uls_combinations = pd.DataFrame({
    'Combination': ['ULS_1', 'ULS_2', 'ULS_3'],
    'Type': ['Linear Add', 'Linear Add', 'Linear Add'],
    'Cases': [
        'DEAD*1.35 + LIVE*1.5',
        'DEAD*1.35 + LIVE*1.5 + WIND_X*1.5', 
        'DEAD*1.0 + LIVE*0.3 + SEISMIC_X*1.0'
    ]
})

print(f"Generated {len(uls_combinations)} ULS load combinations")
```

## Advanced Features

### Nonlinear Analysis Capabilities

**Material Nonlinearity:**
- Concrete cracking and tension stiffening
- Steel yielding and strain hardening
- Rebar bond-slip behavior
- Time-dependent material behavior

**Geometric Nonlinearity:**
- Large displacement effects
- P-Delta analysis
- Construction sequence analysis
- Cable and tendon behavior

**Analysis Types:**
- Static nonlinear (pushover)
- Dynamic nonlinear (time history)
- Staged construction analysis
- Long-term behavior prediction

### Design Integration Features

**Automated Design:**
- Flexural reinforcement design
- Shear reinforcement design  
- Punching shear analysis
- Deflection and crack width checks

**Code Compliance:**
- ACI 318 (American Concrete Institute)
- Eurocode 2 (European Standard)
- AS 3600 (Australian Standard)
- Other international codes

**Design Optimization:**
- Minimum reinforcement requirements
- Constructability considerations
- Economic optimization
- Sustainability metrics

### Performance and Scalability

**Model Size Capabilities:**
- Points: Up to 100,000+ nodes
- Elements: Up to 50,000+ elements
- Load Cases: Up to 1,000+ combinations
- Materials: Unlimited material library

**Memory Management:**
- Efficient DataFrame operations
- Lazy loading for large models
- Memory optimization algorithms
- Garbage collection integration

**Processing Speed:**
- Vectorized operations using NumPy
- Parallel processing for large models
- Optimized algorithms for common operations
- Progress tracking for long operations

## Best Practices and Guidelines

### Model Development

1. **Material Definition:**
   ```python
   # Always validate material properties
   assert concrete_fc >= 20, "Minimum concrete strength is 20 MPa"
   assert steel_fy <= 690, "Maximum steel yield strength is 690 MPa"
   ```

2. **Geometry Creation:**
   ```python
   # Use consistent coordinate systems
   # Validate point connectivity
   # Check for geometric inconsistencies
   ```

3. **Load Application:**
   ```python
   # Verify load magnitudes and directions
   # Check load combination factors
   # Validate load case definitions
   ```

### Error Handling and Validation

**Data Validation:**
```python
def validate_material_properties(material_df):
    """Validate material property ranges and consistency."""
    required_columns = ['Material', 'Type', 'Fc (kN/m2)', 'E (kN/m2)']
    missing_columns = set(required_columns) - set(material_df.columns)
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Validate property ranges
    if (material_df['Fc (kN/m2)'] < 20000).any():
        raise ValueError("Concrete strength below minimum 20 MPa")
    
    return True
```

**Error Recovery:**
```python
try:
    safe22_model, materials = material_MatPropGeneral_safe22(excel_inputs, safe22_model)
except KeyError as e:
    print(f"Missing required data: {e}")
except ValueError as e:
    print(f"Invalid data values: {e}")
```

### Performance Optimization

**Large Model Handling:**
```python
# Use chunked processing for large datasets
chunk_size = 1000
for i in range(0, len(large_dataframe), chunk_size):
    chunk = large_dataframe.iloc[i:i+chunk_size]
    process_chunk(chunk)
```

**Memory Management:**
```python
# Clear intermediate variables
del intermediate_dataframe
import gc
gc.collect()
```

## Integration with Foundation Automation

### Workflow Integration

The SAFE22 package integrates seamlessly with the Foundation Automation system:

1. **Input Processing**: Excel-based input processing from foundation design modules
2. **Model Generation**: Automatic SAFE22 model creation from foundation geometry
3. **Analysis Execution**: Integration with SAFE22 software for analysis execution  
4. **Results Processing**: Post-processing of analysis results for design verification
5. **Report Generation**: Automated report generation with analysis results and design checks

### Data Flow

```
Foundation Design → Excel Inputs → SAFE22 Model → Analysis → Results → Design Verification
```

### API Integration

```python
from fdn_agent.agent_main import FoundationAgent
from build_fem.write_safe.safe22 import Safe22DataFrames

# Integrate with foundation agent
foundation_agent = FoundationAgent()
safe22_model = Safe22DataFrames()

# Process foundation data through SAFE22
foundation_results = foundation_agent.process_with_safe22(safe22_model)
```

## Troubleshooting and Support

### Common Issues

**Material Property Errors:**
- Verify material property units and ranges
- Check for missing material definitions
- Validate stress-strain curve parameters

**Geometry Issues:**
- Check point coordinate consistency
- Verify element connectivity
- Validate coordinate system definitions

**Load Combination Problems:**
- Verify load pattern definitions
- Check combination factor ranges
- Validate load case assignments

**Export Failures:**
- Check file path permissions
- Verify data completeness
- Validate SAFE22 format requirements

### Debugging Tools

```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Validate model completeness
def validate_safe22_model(safe22_dfs):
    """Comprehensive model validation."""
    validation_results = {}
    
    # Check required data tables
    required_tables = ['MaterialProperties', 'PointCoordinates', 'LoadPatterns']
    for table in required_tables:
        if hasattr(safe22_dfs, table):
            df = getattr(safe22_dfs, table)
            validation_results[table] = {
                'exists': True,
                'rows': len(df),
                'columns': len(df.columns)
            }
        else:
            validation_results[table] = {'exists': False}
    
    return validation_results
```

## Version History and Compatibility

### SAFE22 Version Compatibility
- SAFE22 v22.0.0+: Full feature support
- SAFE22 v21.x: Limited advanced feature support
- SAFE22 v20.x: Basic functionality only

### Package Version History
- v5.6.9: Current version with comprehensive material modeling
- v5.6.8: Enhanced geometry processing and load combinations
- v5.6.7: Initial SAFE22 integration and basic functionality

### Migration from SAFE16
For users migrating from SAFE16, key differences include:
- Enhanced material models requiring additional parameters
- Advanced analysis options with new configuration settings
- Improved load combination generation with envelope capabilities
- Updated export formats with additional data validation

## Contributing and Development

### Development Environment Setup
```bash
# Clone repository
git clone https://github.com/foundation-automation/safe22-integration.git

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/
```

### Code Style Guidelines
- Follow PEP 8 coding standards
- Use type hints for all function parameters and returns
- Include comprehensive docstrings for all functions and classes
- Maintain consistent error handling patterns

### Testing Framework
```python
import pytest
from build_fem.write_safe.safe22 import Safe22DataFrames

def test_material_property_processing():
    """Test material property processing functionality."""
    safe22_model = Safe22DataFrames()
    # Test implementation
    assert len(safe22_model.MaterialProperties) >= 0
```

---

**Foundation Automation Development Team**  
**Version 5.6.9 - SAFE22 Compatible**  
**Last Updated: 2024**

For technical support and questions, please contact the Foundation Automation development team or refer to the comprehensive inline documentation within each module.
