﻿"""
Centroid Calculations

Functions for calculating load-weighted centroids.
"""

from typing import List, <PERSON><PERSON>, Dict, Union, Optional
import pandas as pd
import numpy as np

from ..data_types import ColumnData, WallData, Point2D
from ..data_types import ExcelInputs
from ..exceptions import LoadCalculationError, InputDataError, GeometryError
from .basic_calculations import calculate_element_load
from ..utils.logging_utils import (
    enhanced_log,
    log_function_entry,
    log_function_exit,
    log_progress,
    log_performance_metric,
    log_validation_result,
    log_calculation_result,
    log_constraint_check,
    log_error_with_context,
    create_timed_logger
)


def calculate_load_centroids(group_elements: Dict[str, List[Union[ColumnData, WallData]]], 
                            excel_inputs: ExcelInputs, log_callback=None) -> Tuple[Point2D, Dict[str, float], List[str]]:
    """
    Calculate load-weighted centroid for a group of elements.
    
    Args:
        group_elements: Dictionary with 'columns' and 'walls' keys
        excel_inputs: ExcelInputs object with load data
        log_callback: Optional callback for logging progress
        
    Returns:
        Tuple of (centroid, load_details, warnings)
        
    Raises:
        LoadCalculationError: If centroid calculation fails
        InputDataError: If input data is invalid
        GeometryError: If geometry calculation fails
    """
    log_function_entry(log_callback, "calculate_load_centroids",
                      num_columns=len(group_elements.get('columns', [])),
                      num_walls=len(group_elements.get('walls', [])))

    with create_timed_logger(log_callback, "Load Centroid Calculation") as timer:
        try:
            log_validation_result(log_callback, "Input Validation", True, "Starting validation")
            enhanced_log(log_callback, "Starting load-weighted centroid calculation", 'INFO')
        
            # Validate input parameters
            if not group_elements or not any(group_elements.values()):
                error_msg = "No elements provided for centroid calculation"
                log_validation_result(log_callback, "Group Elements Validation", False, error_msg)
                log_error_with_context(log_callback, InputDataError(error_msg), "calculate_load_centroids input validation")
                raise InputDataError(error_msg)
                
            if excel_inputs is None:
                error_msg = "ExcelInputs object is required for centroid calculation"
                log_validation_result(log_callback, "Excel Inputs Validation", False, error_msg)
                log_error_with_context(log_callback, InputDataError(error_msg), "calculate_load_centroids input validation")
                raise InputDataError(error_msg)
            
            # Validate that excel_inputs has the required load data attributes
            if not hasattr(excel_inputs, 'InputLoadColumn') or not hasattr(excel_inputs, 'InputLoadWall'):
                error_msg = "ExcelInputs object must have InputLoadColumn and InputLoadWall attributes"
                log_validation_result(log_callback, "Excel Inputs Attributes", False, error_msg)
                log_error_with_context(log_callback, InputDataError(error_msg), "calculate_load_centroids attribute validation")
                raise InputDataError(error_msg)
            
            log_validation_result(log_callback, "Input Parameters", True, "All input parameters validated successfully")
            
            num_columns = len(group_elements.get('columns', []))
            num_walls = len(group_elements.get('walls', []))
            log_performance_metric(log_callback, "Input Columns", num_columns, "elements")
            log_performance_metric(log_callback, "Input Walls", num_walls, "elements")
            enhanced_log(log_callback, f"Centroid calculation for {num_columns} columns and {num_walls} walls", 'INFO')
            
            warnings = []
            loads = []
            positions = []
            load_details = {}
    
            # Process columns
            if 'columns' in group_elements and group_elements['columns']:
                if excel_inputs.InputLoadColumn is None:
                    error_msg = "Column load DataFrame not available in excel_inputs"
                    log_validation_result(log_callback, "Column DataFrame", False, error_msg)
                    log_error_with_context(log_callback, InputDataError(error_msg), "calculate_load_centroids column validation")
                    raise InputDataError(error_msg)
                    
                log_progress(log_callback, 0, num_columns, "Processing columns for centroid calculation")
                enhanced_log(log_callback, f"Processing {num_columns} columns for centroid calculation", 'INFO')
                col_warnings = _process_column_centroids(
                    group_elements['columns'], 
                    excel_inputs.InputLoadColumn,
                    loads, positions, load_details, log_callback
                )
                warnings.extend(col_warnings)
                num_processed_columns = len([k for k in load_details.keys() if any(col[0] == k for col in group_elements['columns'])])
                log_progress(log_callback, num_processed_columns, num_columns, "Columns processed for centroid calculation")
                enhanced_log(log_callback, f"Processed {num_processed_columns} column centroids", 'INFO')
            else:
                enhanced_log(log_callback, "No columns to process for centroid calculation", 'DEBUG')
    
            # Process walls
            if 'walls' in group_elements and group_elements['walls']:
                if not hasattr(excel_inputs, 'InputLoadWall') or excel_inputs.InputLoadWall is None:
                    error_msg = "Wall load DataFrame not available in excel_inputs"
                    log_validation_result(log_callback, "Wall DataFrame", False, error_msg)
                    log_error_with_context(log_callback, InputDataError(error_msg), "calculate_load_centroids wall validation")
                    raise InputDataError(error_msg)
                    
                log_progress(log_callback, 0, num_walls, "Processing walls for centroid calculation")
                enhanced_log(log_callback, f"Processing {num_walls} walls for centroid calculation", 'INFO')
                wall_warnings = _process_wall_centroids(
                    group_elements['walls'],
                    excel_inputs.InputLoadWall,
                    loads, positions, load_details, log_callback
                )
                warnings.extend(wall_warnings)
                num_processed_walls = len([k for k in load_details.keys() if any(wall[0] == k for wall in group_elements['walls'])])
                log_progress(log_callback, num_processed_walls, num_walls, "Walls processed for centroid calculation")
                enhanced_log(log_callback, f"Processed {num_processed_walls} wall centroids", 'INFO')
            else:
                enhanced_log(log_callback, "No walls to process for centroid calculation", 'DEBUG')
    
            # Calculate centroid
            if not loads or sum(loads) <= 0:
                error_msg = "No valid loads found for centroid calculation"
                log_validation_result(log_callback, "Load Validation", False, f"{error_msg} (total loads: {len(loads)}, sum: {sum(loads) if loads else 0})")
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "calculate_load_centroids load validation")
                raise LoadCalculationError(error_msg)
    
            total_load = sum(loads)
            log_performance_metric(log_callback, "Total Load", total_load, "kN")
            log_performance_metric(log_callback, "Load Points", len(loads), "points")
            enhanced_log(log_callback, f"Calculating weighted centroid from {len(loads)} load points with total load {total_load:.2f} kN", 'DEBUG')
    
            centroid = _calculate_weighted_centroid(loads, positions, log_callback)
    
            # Log final results
            log_calculation_result(log_callback, "Load-Weighted Centroid", 
                                 f"Location: ({centroid[0]:.3f}, {centroid[1]:.3f}), Total Load: {total_load:.2f} kN")
            enhanced_log(log_callback, f"Load-weighted centroid calculation completed successfully:", 'INFO')
            enhanced_log(log_callback, f"  Centroid location: ({centroid[0]:.3f}, {centroid[1]:.3f})", 'INFO')
            enhanced_log(log_callback, f"  Total elements: {len(load_details)}", 'INFO')
            enhanced_log(log_callback, f"  Total load: {total_load:.2f} kN", 'INFO')
    
            if warnings:
                enhanced_log(log_callback, f"Centroid calculation completed with {len(warnings)} warnings", 'WARNING')
                for i, warning in enumerate(warnings[:5], 1):  # Log first 5 warnings
                    enhanced_log(log_callback, f"  Warning {i}: {warning}", 'WARNING')
                if len(warnings) > 5:
                    enhanced_log(log_callback, f"  ... and {len(warnings) - 5} more warnings", 'WARNING')
    
            log_function_exit(log_callback, "calculate_load_centroids", 
                            centroid_x=centroid[0], centroid_y=centroid[1], 
                            total_load=total_load, warnings_count=len(warnings))
            return centroid, load_details, warnings
            
        except (LoadCalculationError, InputDataError, GeometryError) as e:
            log_error_with_context(log_callback, e, "calculate_load_centroids execution")
            raise
        except Exception as e:
            error_msg = f"Unexpected error in centroid calculation: {e}"
            log_error_with_context(log_callback, Exception(error_msg), "calculate_load_centroids unexpected error")
            raise LoadCalculationError(error_msg) from e


def _process_column_centroids(columns: List, df: pd.DataFrame,
                            loads: List[float], positions: List[Point2D], 
                            load_details: Dict[str, float], log_callback=None) -> List[str]:    
    """Process column loads and positions for centroid calculation."""
    log_function_entry(log_callback, "_process_column_centroids", 
                      num_columns=len(columns))
    
    # Debug: Log sample of column data to identify data types
    enhanced_log(log_callback, f"DEBUG: Processing {len(columns)} columns. Sample data types:", 'DEBUG')
    for i, column_data in enumerate(columns[:3]):  # Log first 3 columns
        if column_data and len(column_data) >= 4:
            enhanced_log(log_callback, f"DEBUG: Column {i}: name='{column_data[0]}' (type: {type(column_data[0])}), x='{column_data[1]}' (type: {type(column_data[1])}), y='{column_data[2]}' (type: {type(column_data[2])}), z='{column_data[3]}' (type: {type(column_data[3])})", 'DEBUG')
    with create_timed_logger(log_callback, "Column Centroids Processing") as timer:
        try:
            enhanced_log(log_callback, f"Processing {len(columns)} columns for centroid calculation", 'DEBUG')
    
            if df.empty:
                error_msg = "Column DataFrame is empty for centroid calculation"
                log_validation_result(log_callback, "Column DataFrame", False, error_msg)
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_column_centroids dataframe validation")
                raise LoadCalculationError(error_msg)
            log_validation_result(log_callback, "Column DataFrame", True, f"DataFrame has {len(df)} rows")
            
            warnings = []
            processed_count = 0
            failed_columns = []
            
            enhanced_log(log_callback, f"DEBUG: Initialized variables - processed_count={processed_count} (type: {type(processed_count)})", 'DEBUG')
            
            for i, column_data in enumerate(columns):
                column_name = f"column_{i}"  # Initialize with default name to avoid UnboundLocalError
                try:
                    enhanced_log(log_callback, f"DEBUG: Processing column {i+1}/{len(columns)}", 'DEBUG')
                    log_progress(log_callback, i + 1, len(columns), f"Processing column")
                    
                    if not column_data or len(column_data) < 4:
                        warning_msg = f"Invalid column data format at index {i} - insufficient data"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                        continue
                        
                    column_name, x, y, z = column_data[:4]
                    
                    if not column_name or not column_name.strip():
                        warning_msg = f"Empty column name found at index {i}"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                        continue
                        
                    # Validate coordinates
                    try:
                        x_coord = float(x)
                        y_coord = float(y)
                    except (ValueError, TypeError):
                        warning_msg = f"Invalid coordinates for column {column_name}: ({x}, {y})"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                        continue
                    
                    # Ensure coordinates are numeric
                    if not isinstance(x_coord, (int, float)) or not isinstance(y_coord, (int, float)):
                        warning_msg = f"Non-numeric coordinates for column {column_name}: ({x_coord}, {y_coord})"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                        continue
                    
                    enhanced_log(log_callback, f"Processing column centroid {i+1}/{len(columns)}: '{column_name}' at ({x_coord:.2f}, {y_coord:.2f})", 'DEBUG')
                    
                    column_load, col_warnings = calculate_element_load(df, column_name, log_callback=log_callback)
                    warnings.extend(col_warnings)
                    if column_load > 0:
                        # Debug logging before adding to lists
                        enhanced_log(log_callback, f"DEBUG: Adding column load. column_load={column_load} (type: {type(column_load)}), x_coord={x_coord} (type: {type(x_coord)}), y_coord={y_coord} (type: {type(y_coord)})", 'DEBUG')
                        
                        loads.append(column_load)
                        positions.append((x_coord, y_coord))
                        load_details[column_name] = column_load
                        
                        # Debug: Check processed_count before increment
                        enhanced_log(log_callback, f"DEBUG: Before increment in column loop - processed_count={processed_count} (type: {type(processed_count)})", 'DEBUG')
                        processed_count += 1

                        # Validate processed_count remains an integer after increment
                        if not isinstance(processed_count, int):
                            error_msg = f"processed_count became non-integer after increment: {processed_count} (type: {type(processed_count)})"
                            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                            log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_column_centroids processed_count type validation")
                            raise LoadCalculationError(error_msg)

                        # Debug: Check processed_count after increment
                        enhanced_log(log_callback, f"DEBUG: After increment in column loop - processed_count={processed_count} (type: {type(processed_count)})", 'DEBUG')
                        
                        log_calculation_result(log_callback, f"Column '{column_name}' Load", 
                                             f"{column_load:.2f} kN at ({x_coord:.2f}, {y_coord:.2f})")
                        enhanced_log(log_callback, f"Column '{column_name}': {column_load:.2f} kN at ({x_coord:.2f}, {y_coord:.2f})", 'DEBUG')
                    else:
                        warning_msg = f"Zero or negative load for column {column_name}: {column_load:.2f} kN"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                except LoadCalculationError as e:
                    failed_columns.append(column_name)
                    error_msg = f"Failed to process column {column_name} for centroid: {e}"
                    log_error_with_context(log_callback, e, f"_process_column_centroids column {column_name}")
                    enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                    raise LoadCalculationError(error_msg)
                except Exception as e:
                    # Catch any other errors in the column processing loop
                    enhanced_log(log_callback, f"ERROR: Unexpected error processing column {column_name}: {e} (type: {type(e)})", 'ERROR')
                    enhanced_log(log_callback, f"DEBUG: Current state - processed_count={processed_count} (type: {type(processed_count)}), i={i}, column_data={column_data}", 'DEBUG')
                    raise
    
            if processed_count == 0:
                error_msg = f"No valid columns could be processed for centroid calculation from {len(columns)} provided"
                log_validation_result(log_callback, "Column Processing", False, error_msg)
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_column_centroids processing validation")
                raise LoadCalculationError(error_msg)

            # Ensure processed_count is numeric for division with proper type validation
            try:
                if not isinstance(processed_count, (int, float)):
                    enhanced_log(log_callback, f"WARNING: processed_count is not numeric (type: {type(processed_count)}, value: {processed_count}). Converting to int.", 'WARNING')
                    processed_count = int(float(str(processed_count))) if processed_count else 0
                else:
                    processed_count = int(processed_count)

                # Validate that processed_count is now a proper integer
                if not isinstance(processed_count, int) or processed_count < 0:
                    raise ValueError(f"Invalid processed_count after conversion: {processed_count} (type: {type(processed_count)})")

            except (ValueError, TypeError) as e:
                error_msg = f"Failed to convert processed_count to integer: {processed_count} (type: {type(processed_count)}), error: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_column_centroids processed_count conversion")
                raise LoadCalculationError(error_msg)

            # Debug logging before division
            enhanced_log(log_callback, f"DEBUG: About to calculate success_rate. processed_count={processed_count} (type: {type(processed_count)}), len(columns)={len(columns)} (type: {type(len(columns))})", 'DEBUG')

            try:
                # Ensure both operands are numeric before division
                columns_count = int(len(columns))
                if columns_count <= 0:
                    raise ValueError(f"Invalid columns count: {columns_count}")

                success_rate = (processed_count / columns_count) * 100
                enhanced_log(log_callback, f"DEBUG: Successfully calculated success_rate={success_rate}", 'DEBUG')
            except (TypeError, ValueError, ZeroDivisionError) as e:
                error_msg = f"Division error in success_rate calculation: processed_count={processed_count} (type: {type(processed_count)}), len(columns)={len(columns)} (type: {type(len(columns))}), error: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_column_centroids success_rate calculation")
                raise LoadCalculationError(error_msg)
            log_performance_metric(log_callback, "Column Processing Success Rate", success_rate, "%")
            log_performance_metric(log_callback, "Processed Columns", processed_count, "columns")
            enhanced_log(log_callback, f"Column centroid processing completed: {processed_count}/{len(columns)} columns processed successfully ({success_rate:.1f}% success rate)", 'INFO')
    
            if failed_columns:
                enhanced_log(log_callback, f"Failed column centroids: {', '.join(failed_columns)}", 'WARNING')
    
            log_function_exit(log_callback, "_process_column_centroids", 
                            processed_count=processed_count, warnings_count=len(warnings))
            return warnings
            
        except Exception as e:
            log_error_with_context(log_callback, e, "_process_column_centroids execution")
            raise


def _process_wall_centroids(walls: List, df: pd.DataFrame,
                          loads: List[float], positions: List[Point2D], 
                          load_details: Dict[str, float], log_callback=None) -> List[str]:
    """Process wall loads and positions for centroid calculation."""
    log_function_entry(log_callback, "_process_wall_centroids", 
                      num_walls=len(walls))
    
    with create_timed_logger(log_callback, "Wall Centroids Processing") as timer:
        try:
            enhanced_log(log_callback, f"Processing {len(walls)} walls for centroid calculation", 'DEBUG')
    
            if df.empty:
                error_msg = "Wall DataFrame is empty for centroid calculation"
                log_validation_result(log_callback, "Wall DataFrame", False, error_msg)
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_wall_centroids dataframe validation")
                raise LoadCalculationError(error_msg)
            log_validation_result(log_callback, "Wall DataFrame", True, f"DataFrame has {len(df)} rows")
            
            warnings = []
            processed_count = 0
            failed_walls = []
    
            for i, wall_data in enumerate(walls):
                wall_name = f"wall_{i}"  # Initialize with default name to avoid UnboundLocalError
                log_progress(log_callback, i + 1, len(walls), f"Processing wall")
                
                if not wall_data or len(wall_data) < 3:
                    warning_msg = f"Invalid wall data format at index {i} - insufficient data"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                    continue
                    
                wall_name = wall_data[0]
                points = wall_data[1]
                
                if not wall_name or not wall_name.strip():
                    warning_msg = f"Empty wall name found at index {i}"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                    continue
                    
                if not points or not isinstance(points, list):
                    warning_msg = f"Invalid or missing points for wall {wall_name}"
                    warnings.append(warning_msg)
                    enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                    continue
                
                try:
                    enhanced_log(log_callback, f"Processing wall centroid {i+1}/{len(walls)}: '{wall_name}' with {len(points)} points", 'DEBUG')
                    
                    wall_load, wall_warnings = calculate_element_load(df, wall_name, log_callback=log_callback)
                    warnings.extend(wall_warnings)
                    
                    if wall_load > 0:
                        # Calculate wall centroid
                        wall_centroid = _calculate_wall_centroid(points, log_callback)
                        loads.append(wall_load)
                        positions.append(wall_centroid)
                        load_details[wall_name] = wall_load
                        processed_count += 1

                        # Validate processed_count remains an integer after increment
                        if not isinstance(processed_count, int):
                            error_msg = f"processed_count became non-integer after increment: {processed_count} (type: {type(processed_count)})"
                            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                            log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_wall_centroids processed_count type validation")
                            raise LoadCalculationError(error_msg)
                        log_calculation_result(log_callback, f"Wall '{wall_name}' Load", 
                                             f"{wall_load:.2f} kN at centroid ({wall_centroid[0]:.2f}, {wall_centroid[1]:.2f})")
                        enhanced_log(log_callback, f"Wall '{wall_name}': {wall_load:.2f} kN at centroid ({wall_centroid[0]:.2f}, {wall_centroid[1]:.2f})", 'DEBUG')
                    else:
                        warning_msg = f"Zero or negative load for wall {wall_name}: {wall_load:.2f} kN"
                        warnings.append(warning_msg)
                        enhanced_log(log_callback, f"WARNING: {warning_msg}", 'WARNING')
                        
                except (LoadCalculationError, GeometryError) as e:
                    failed_walls.append(wall_name)
                    error_msg = f"Failed to process wall {wall_name} for centroid: {e}"
                    log_error_with_context(log_callback, e, f"_process_wall_centroids wall {wall_name}")
                    enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                    raise LoadCalculationError(error_msg)
    
            if processed_count == 0:
                error_msg = f"No valid walls could be processed for centroid calculation from {len(walls)} provided"
                log_validation_result(log_callback, "Wall Processing", False, error_msg)
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_wall_centroids processing validation")
                raise LoadCalculationError(error_msg)

            # Ensure processed_count is numeric for division with proper type validation
            try:
                if not isinstance(processed_count, (int, float)):
                    enhanced_log(log_callback, f"WARNING: processed_count is not numeric (type: {type(processed_count)}, value: {processed_count}). Converting to int.", 'WARNING')
                    processed_count = int(float(str(processed_count))) if processed_count else 0
                else:
                    processed_count = int(processed_count)

                # Validate that processed_count is now a proper integer
                if not isinstance(processed_count, int) or processed_count < 0:
                    raise ValueError(f"Invalid processed_count after conversion: {processed_count} (type: {type(processed_count)})")

            except (ValueError, TypeError) as e:
                error_msg = f"Failed to convert processed_count to integer: {processed_count} (type: {type(processed_count)}), error: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_wall_centroids processed_count conversion")
                raise LoadCalculationError(error_msg)

            # Debug logging before division
            enhanced_log(log_callback, f"DEBUG: About to calculate wall success_rate. processed_count={processed_count} (type: {type(processed_count)}), len(walls)={len(walls)} (type: {type(len(walls))})", 'DEBUG')

            try:
                # Ensure both operands are numeric before division
                walls_count = int(len(walls))
                if walls_count <= 0:
                    raise ValueError(f"Invalid walls count: {walls_count}")

                success_rate = (processed_count / walls_count) * 100
                enhanced_log(log_callback, f"DEBUG: Successfully calculated wall success_rate={success_rate}", 'DEBUG')
            except (TypeError, ValueError, ZeroDivisionError) as e:
                error_msg = f"Division error in wall success_rate calculation: processed_count={processed_count} (type: {type(processed_count)}), len(walls)={len(walls)} (type: {type(len(walls))}), error: {e}"
                enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
                log_error_with_context(log_callback, LoadCalculationError(error_msg), "_process_wall_centroids success_rate calculation")
                raise LoadCalculationError(error_msg)
            log_performance_metric(log_callback, "Wall Processing Success Rate", success_rate, "%")
            log_performance_metric(log_callback, "Processed Walls", processed_count, "walls")
            enhanced_log(log_callback, f"Wall centroid processing completed: {processed_count}/{len(walls)} walls processed successfully ({success_rate:.1f}% success rate)", 'INFO')
    
            if failed_walls:
                enhanced_log(log_callback, f"Failed wall centroids: {', '.join(failed_walls)}", 'WARNING')
    
            log_function_exit(log_callback, "_process_wall_centroids", 
                            processed_count=processed_count, warnings_count=len(warnings))
            return warnings
            
        except Exception as e:
            log_error_with_context(log_callback, e, "_process_wall_centroids execution")
            raise


def _calculate_wall_centroid(points: List[Point2D], log_callback=None) -> Point2D:
    """Calculate geometric centroid of wall points."""
    log_function_entry(log_callback, "_calculate_wall_centroid", num_points=len(points))
    
    try:
        enhanced_log(log_callback, f"Calculating wall centroid from {len(points)} points", 'DEBUG')
        
        if not points:
            error_msg = "No points provided for wall centroid calculation"
            log_validation_result(log_callback, "Points Validation", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_wall_centroid points validation")
            raise GeometryError(error_msg)
        
        if len(points) < 2:
            error_msg = "At least 2 points required for wall centroid calculation"
            log_validation_result(log_callback, "Points Count", False, f"{error_msg} (provided: {len(points)})")
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_wall_centroid points count validation")
            raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "Points Count", True, f"{len(points)} points provided")
        
        # Validate all points
        valid_points = []
        for i, point in enumerate(points):
            if not isinstance(point, (list, tuple)) or len(point) < 2:
                error_msg = f"Invalid point format at index {i}"
                log_validation_result(log_callback, f"Point {i} Format", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), f"_calculate_wall_centroid point {i} validation")
                raise GeometryError(error_msg)
            try:
                x_coord = float(point[0])
                y_coord = float(point[1])
                # Ensure coordinates are valid numbers
                if not (isinstance(x_coord, (int, float)) and isinstance(y_coord, (int, float))):
                    raise ValueError(f"Coordinates must be numeric: ({x_coord}, {y_coord})")
                valid_points.append((x_coord, y_coord))
                enhanced_log(log_callback, f"Point {i+1}: ({x_coord:.3f}, {y_coord:.3f})", 'DEBUG')
            except (ValueError, TypeError) as e:
                error_msg = f"Invalid coordinate values at point index {i}: ({point[0]}, {point[1]})"
                log_validation_result(log_callback, f"Point {i} Coordinates", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), f"_calculate_wall_centroid point {i} coordinate validation")
                raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "All Points", True, f"All {len(valid_points)} points validated successfully")
        
        # Ensure all coordinates are numeric before division
        for i, p in enumerate(valid_points):
            if not isinstance(p[0], (int, float)) or not isinstance(p[1], (int, float)):
                error_msg = f"Non-numeric coordinate detected at point {i}: ({p[0]}, {p[1]})"
                log_validation_result(log_callback, f"Point {i} Numeric Check", False, error_msg)
                raise GeometryError(error_msg)
        
        centroid_x = sum(float(p[0]) for p in valid_points) / len(valid_points)
        centroid_y = sum(float(p[1]) for p in valid_points) / len(valid_points)
        
        if not all(np.isfinite([centroid_x, centroid_y])):
            error_msg = "Calculated wall centroid coordinates are not finite"
            log_validation_result(log_callback, "Centroid Calculation", False, f"{error_msg} ({centroid_x}, {centroid_y})")
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_wall_centroid centroid calculation")
            raise GeometryError(error_msg)
        
        log_calculation_result(log_callback, "Wall Centroid", f"({centroid_x:.3f}, {centroid_y:.3f})")
        enhanced_log(log_callback, f"Wall centroid calculated: ({centroid_x:.3f}, {centroid_y:.3f})", 'DEBUG')
        
        log_function_exit(log_callback, "_calculate_wall_centroid", 
                        centroid_x=centroid_x, centroid_y=centroid_y)
        return (centroid_x, centroid_y)
        
    except Exception as e:
        log_error_with_context(log_callback, e, "_calculate_wall_centroid execution")
        raise


def _calculate_weighted_centroid(loads: List[float], positions: List[Point2D], log_callback=None) -> Point2D:
    """Calculate load-weighted centroid."""
    log_function_entry(log_callback, "_calculate_weighted_centroid", 
                      num_loads=len(loads), num_positions=len(positions))
    
    try:
        enhanced_log(log_callback, f"Calculating weighted centroid from {len(loads)} loads and {len(positions)} positions", 'DEBUG')
        
        if not loads or not positions:
            error_msg = "Empty loads or positions provided for weighted centroid calculation"
            log_validation_result(log_callback, "Input Data", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_weighted_centroid input validation")
            raise GeometryError(error_msg)
            
        if len(loads) != len(positions):
            error_msg = f"Loads and positions lists must have same length (loads: {len(loads)}, positions: {len(positions)})"
            log_validation_result(log_callback, "Data Length", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_weighted_centroid length validation")
            raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "Data Length", True, f"Both loads and positions have {len(loads)} elements")
        
        # Validate that all loads are numeric
        for i, load in enumerate(loads):
            try:
                numeric_load = float(load)
                if not isinstance(numeric_load, (int, float)):
                    raise ValueError(f"Load must be numeric: {load}")
                # Replace with validated numeric value
                loads[i] = numeric_load
            except (ValueError, TypeError) as e:
                error_msg = f"Invalid load value at index {i}: {load} (error: {e})"
                log_validation_result(log_callback, f"Load {i} Validation", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), f"_calculate_weighted_centroid load {i} validation")
                raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "All Loads", True, f"All {len(loads)} loads validated as numeric")
        
        total_load = sum(loads)
        # Ensure total_load is numeric
        try:
            total_load = float(total_load)
            if not isinstance(total_load, (int, float)) or not (total_load > 0):
                raise ValueError(f"Invalid total load: {total_load}")
        except (ValueError, TypeError) as e:
            error_msg = f"Total load must be a positive number for centroid calculation (got: {total_load}, error: {e})"
            log_validation_result(log_callback, "Total Load Type", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_weighted_centroid total load validation")
            raise GeometryError(error_msg)
            
        if total_load <= 0:
            error_msg = f"Total load must be positive for centroid calculation (total: {total_load})"
            log_validation_result(log_callback, "Total Load", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_weighted_centroid total load validation")
            raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "Total Load", True, f"Total load: {total_load:.2f} kN")
        
        # Validate all positions
        for i, pos in enumerate(positions):
            if not isinstance(pos, (list, tuple)) or len(pos) < 2:
                error_msg = f"Invalid position format at index {i}"
                log_validation_result(log_callback, f"Position {i} Format", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), f"_calculate_weighted_centroid position {i} validation")
                raise GeometryError(error_msg)
            try:
                float(pos[0])
                float(pos[1])
            except (ValueError, TypeError):
                error_msg = f"Invalid coordinate values at position index {i}: ({pos[0]}, {pos[1]})"
                log_validation_result(log_callback, f"Position {i} Coordinates", False, error_msg)
                log_error_with_context(log_callback, GeometryError(error_msg), f"_calculate_weighted_centroid position {i} coordinate validation")
                raise GeometryError(error_msg)
        
        log_validation_result(log_callback, "All Positions", True, f"All {len(positions)} positions validated successfully")
        
        # Ensure all coordinates are numeric before weighted centroid calculation
        for i, pos in enumerate(positions):
            if not isinstance(pos[0], (int, float)) or not isinstance(pos[1], (int, float)):
                error_msg = f"Non-numeric position coordinate detected at index {i}: ({pos[0]}, {pos[1]})"
                log_validation_result(log_callback, f"Position {i} Numeric Check", False, error_msg)
                raise GeometryError(error_msg)
        
        # Calculate load-weighted centroid
        centroid_x = sum(float(load) * float(pos[0]) for load, pos in zip(loads, positions)) / total_load
        centroid_y = sum(float(load) * float(pos[1]) for load, pos in zip(loads, positions)) / total_load
        
        if not all(np.isfinite([centroid_x, centroid_y])):
            error_msg = f"Calculated centroid coordinates are not finite: ({centroid_x}, {centroid_y})"
            log_validation_result(log_callback, "Centroid Calculation", False, error_msg)
            log_error_with_context(log_callback, GeometryError(error_msg), "_calculate_weighted_centroid centroid calculation")
            raise GeometryError(error_msg)
          # Log detailed calculation info
        max_load = max(loads)
        min_load = min(loads)
        # Ensure total_load is numeric before division
        try:
            total_load_float = float(total_load)
            loads_count = len(loads)
            avg_load = total_load_float / loads_count
        except (ValueError, TypeError, ZeroDivisionError) as e:
            error_msg = f"Failed to calculate average load: total_load={total_load}, loads_count={len(loads)}, error={e}"
            enhanced_log(log_callback, f"ERROR: {error_msg}", 'ERROR')
            avg_load = 0.0  # Fallback value
        
        log_performance_metric(log_callback, "Total Load", total_load, "kN")
        log_performance_metric(log_callback, "Max Load", max_load, "kN")
        log_performance_metric(log_callback, "Min Load", min_load, "kN")
        log_performance_metric(log_callback, "Average Load", avg_load, "kN")
        log_calculation_result(log_callback, "Load-Weighted Centroid", f"({centroid_x:.3f}, {centroid_y:.3f})")
        
        enhanced_log(log_callback, f"Weighted centroid calculation details:", 'DEBUG')
        enhanced_log(log_callback, f"  Total load: {total_load:.2f} kN", 'DEBUG')
        enhanced_log(log_callback, f"  Load range: {min_load:.2f} - {max_load:.2f} kN (avg: {avg_load:.2f} kN)", 'DEBUG')
        enhanced_log(log_callback, f"  Calculated centroid: ({centroid_x:.3f}, {centroid_y:.3f})", 'DEBUG')
        
        log_function_exit(log_callback, "_calculate_weighted_centroid", 
                        centroid_x=centroid_x, centroid_y=centroid_y, total_load=total_load)
        return (centroid_x, centroid_y)
        
    except Exception as e:
        log_error_with_context(log_callback, e, "_calculate_weighted_centroid execution")
        raise

