"""Foundation design module for structural analysis and design."""

# GUI Components
try:
    from .designer_gui import SafeDesignCheckerGUI
except ImportError:
    SafeDesignCheckerGUI = None

# Main design functions
try:
    from .designer_main import (
        initialize_file_paths,
        read_input_data,
        run_uls_sls_calculations,
        run_design_automation,
        design_bp,
        design_shp,
        design_dhp,
        design_mp,
        delete_previous_results
    )
except ImportError:
    initialize_file_paths = None
    read_input_data = None
    run_uls_sls_calculations = None
    run_design_automation = None
    design_bp = None
    design_shp = None
    design_dhp = None
    design_mp = None
    delete_previous_results = None

# Data processing functions
try:
    from .data_processing import (
        cal_pile_sls,
        cal_pile_uls,
        cal_pile_uls_pdelta,
        cal_pile_uls_pdelta,
        process_nsf,
        cal_settlement,
        cal_pile_local_deflection
    )
except ImportError:
    cal_pile_sls = None
    cal_pile_uls = None
    cal_pile_uls_pdelta = None
    cal_pile_uls_pdelta = None
    process_nsf = None
    cal_settlement = None
    cal_pile_local_deflection = None

# Pile capacity functions
try:
    from .pile_capacity import (
        cal_capacity_bp,
        cal_capacity_shp,
        cal_capacity_dhp,
        cal_capacity_mp,
        cal_capacity_piles
    )
except ImportError:
    cal_capacity_bp = None
    cal_capacity_shp = None
    cal_capacity_dhp = None
    cal_capacity_mp = None
    cal_capacity_piles = None

# Piling schedule functions
try:
    from .piling_schedule import (
        cal_stepping_effect,
        gen_piling_schedule
    )
except ImportError:
    cal_stepping_effect = None
    gen_piling_schedule = None

# Read functions
try:
    from .read import read_pile_base_load
except ImportError:
    read_pile_base_load = None

# Initialization functions
try:
    from .initialization import init_excel_bp_rebar, init_excel_bp_segment_rebar
except ImportError:
    init_excel_bp_rebar = None
    init_excel_bp_segment_rebar = None

# SLS design functions
try:
    from .sls_design import (
        check_slab_angular_rotation_xy,
        cal_lateral_displacement,
        check_pile_deflection,
        check_differential_settlement
    )
    
except ImportError:
    check_slab_angular_rotation_xy = None
    cal_lateral_displacement = None
    check_pile_deflection = None
    check_differential_settlement = None

__all__ = [
    'SafeDesignCheckerGUI',
    'initialize_file_paths',
    'read_input_data', 
    'run_uls_sls_calculations',
    'run_design_automation',
    'design_bp',
    'design_shp',
    'design_dhp',
    'design_mp',
    'delete_previous_results',
    'cal_pile_sls',
    'cal_pile_uls',
    'cal_pile_uls_pdelta',
    'process_nsf',
    'cal_settlement',
    'cal_capacity_bp',
    'cal_capacity_shp',
    'cal_capacity_dhp',
    'cal_capacity_mp',
    'cal_capacity_piles',
    'cal_stepping_effect',
    'gen_piling_schedule',
    'read_pile_base_load',
    'init_excel_bp_rebar',
    'init_excel_bp_segment_rebar',
    'check_slab_angular_rotation_xy',
    'cal_pile_local_deflection',
    'cal_lateral_displacement',
    'check_pile_deflection',
    'check_differential_settlement'
]