"""
Dimension and annotation plotting functions for DXF visualization.

This module contains functions for adding dimensions, title blocks,
and other annotations to DXF drawings.
"""

from typing import Dict, Optional
import time
import math
from ezdxf.enums import TextEntityAlignment
from shapely.geometry import Polygon

from ..data_types import PileGroupResult
from .utils import safe_get


def add_title_block_dxf(msp, group_results: Dict[str, PileGroupResult], text_height: float, log_callback=None) -> None:
    """
    Add title block with project information.
    
    Args:
        msp: DXF model space
        group_results: Dictionary of group results
        text_height: Height for text
        log_callback: Optional logging callback
    """
    try:
        # Calculate statistics
        total_piles = sum(len(safe_get(data, 'pile_locations', [])) for data in group_results.values())
        total_groups = len(group_results)
        
        # Count groups with preselection analysis using same logic as plotting function
        groups_with_preselection = 0
        for group_data in group_results.values():
            preselection_analysis = _extract_preselection_analysis(group_data)
            if preselection_analysis:
                groups_with_preselection += 1
        
        # Title block position (bottom left)
        title_x = -15.0
        title_y = -8.0
        
        title_lines = [
            "COMPREHENSIVE PILE ESTIMATION RESULTS",
            f"Total Groups: {total_groups}",
            f"Total Piles: {total_piles}",
            f"Groups with Preselection: {groups_with_preselection}",
            "",
            "LAYER LEGEND:",
            "PILE_CAPS: Final pile caps (convex hull + edge distance)",
            "INITIAL_PILE_CAPS: Original pile caps (user edge distance)",
            "OPTIMAL_RECTANGLE: Minimum area rectangles (Red dash-dot)",
            "PRESELECTION_DHP: DHP possible positions (Red dotted crosses)",
            "PRESELECTION_SHP: SHP possible positions (Green dotted crosses)",
            "PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m: BP possible positions with specifications",
            "PRESELECTION_DHP_CAP: DHP maximum pile cap boundaries (Red dashed)",
            "PRESELECTION_SHP_CAP: SHP maximum pile cap boundaries (Green dashed)",
            "PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m_CAP: BP maximum pile cap boundaries",
            "PILES_DHP: Final DHP pile positions (Red I-sections)",
            "PILES_SHP: Final SHP pile positions (Green I-sections + sockets)",
            "PILES_BP: Final BP pile positions (Magenta circles)",
            "",
            "BP LAYER NAMING CONVENTION:",
            "Example: PRESELECTION_BP_41155kN_2m (41.155kN capacity, 2m diameter)",
            "Case 1: BP positions MUST be at load centroid of column",
            "",
            f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        for i, line in enumerate(title_lines):
            if line:  # Skip empty lines
                text = msp.add_text(
                    line,
                    dxfattribs={
                        'layer': 'TEXT',
                        'height': text_height * (1.2 if i == 0 else 0.8 if line.startswith('INFO:') else 1.0)
                    }
                )
                text.set_placement((title_x, title_y - i * text_height * 1.2), align=TextEntityAlignment.LEFT)
        
    except Exception as e:
        if log_callback:
            log_callback(f"Error adding title block: {e}")


def _extract_preselection_analysis(group_data) -> Optional[dict]:
    """Extract preselection analysis from group data."""
    preselection_analysis = None
    
    # Check if it's directly available as attribute
    if hasattr(group_data, 'preselection_analysis'):
        preselection_analysis = group_data.preselection_analysis
    
    # Check if it's in a dictionary format
    if preselection_analysis is None and isinstance(group_data, dict):
        preselection_analysis = group_data.get('preselection_analysis', {})
    
    # Check safe_get method
    if not preselection_analysis:
        preselection_analysis = safe_get(group_data, 'preselection_analysis', {})
    
    return preselection_analysis if preselection_analysis else None


def add_dimensions_dxf(msp, site_boundary: Polygon, text_height: float, log_callback=None) -> None:
    """
    Add comprehensive professional dimensions for site boundary, pile spacing, and pile caps.
    
    Args:
        msp: DXF model space
        site_boundary: Site boundary polygon
        text_height: Height for dimension text
        log_callback: Optional logging callback
    """
    try:
        if log_callback:
            log_callback("Adding comprehensive dimensions...")
        
        # Add site boundary dimensions
        _add_site_boundary_dimensions(msp, site_boundary, text_height, log_callback)
        
        # Add pile spacing dimensions
        add_pile_spacing_dimensions(msp, text_height, log_callback)
        
        # Add pile cap dimensions
        add_pile_cap_dimensions(msp, text_height, log_callback)
        
        if log_callback:
            log_callback("Comprehensive dimensions added successfully")
            
    except Exception as e:
        if log_callback:
            log_callback(f"Error adding dimensions: {e}")


def _add_site_boundary_dimensions(msp, site_boundary: Polygon, text_height: float, log_callback=None) -> None:
    """Add dimensions for site boundary."""
    try:
        if not site_boundary or site_boundary.is_empty:
            return
        
        # Get boundary coordinates
        coords = list(site_boundary.exterior.coords)
        if len(coords) < 4:  # Need at least 3 unique points (4 with closure)
            return
        
        # Calculate overall dimensions
        xs = [coord[0] for coord in coords[:-1]]  # Exclude duplicate last point
        ys = [coord[1] for coord in coords[:-1]]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        width = max_x - min_x
        height = max_y - min_y
        
        # Add overall width dimension (bottom)
        dim_y = min_y - 2.0
        msp.add_line((min_x, dim_y), (max_x, dim_y), dxfattribs={'layer': 'DIMENSIONS'})
        msp.add_line((min_x, dim_y - 0.2), (min_x, dim_y + 0.2), dxfattribs={'layer': 'DIMENSIONS'})
        msp.add_line((max_x, dim_y - 0.2), (max_x, dim_y + 0.2), dxfattribs={'layer': 'DIMENSIONS'})
        
        # Add width text
        text = msp.add_text(
            f"{width:.2f}m",
            dxfattribs={'layer': 'TEXT', 'height': text_height * 0.8}
        )
        text.set_placement(((min_x + max_x) / 2, dim_y - 0.5), align=TextEntityAlignment.MIDDLE_CENTER)
        
        # Add overall height dimension (left)
        dim_x = min_x - 2.0
        msp.add_line((dim_x, min_y), (dim_x, max_y), dxfattribs={'layer': 'DIMENSIONS'})
        msp.add_line((dim_x - 0.2, min_y), (dim_x + 0.2, min_y), dxfattribs={'layer': 'DIMENSIONS'})
        msp.add_line((dim_x - 0.2, max_y), (dim_x + 0.2, max_y), dxfattribs={'layer': 'DIMENSIONS'})
        
        # Add height text
        text = msp.add_text(
            f"{height:.2f}m",
            dxfattribs={'layer': 'TEXT', 'height': text_height * 0.8}
        )
        text.set_placement((dim_x - 0.5, (min_y + max_y) / 2), align=TextEntityAlignment.MIDDLE_CENTER)
        text.dxf.rotation = 90  # Rotate text for vertical dimension
        
        if log_callback:
            log_callback(f"Site boundary dimensions added: {width:.2f}m × {height:.2f}m")
            
    except Exception as e:
        if log_callback:
            log_callback(f"Error adding site boundary dimensions: {e}")


def add_pile_spacing_dimensions(msp, text_height: float, log_callback=None) -> None:
    """
    Add pile spacing dimensions between adjacent piles.
    
    Args:
        msp: DXF model space
        text_height: Height for dimension text
        log_callback: Optional logging callback
    """
    try:
        # Find pile entities to measure spacing
        pile_positions = []

        for entity in msp:
            if (hasattr(entity.dxf, 'layer') and
                entity.dxf.layer.startswith('PILES_') and
                entity.dxftype() in ['CIRCLE', 'LWPOLYLINE']):

                if entity.dxftype() == 'CIRCLE':
                    pile_positions.append((entity.dxf.center[0], entity.dxf.center[1]))
                elif entity.dxftype() == 'LWPOLYLINE' and len(entity) > 0:
                    # Use first point of polyline as pile position
                    first_point = entity[0]
                    pile_positions.append((first_point[0], first_point[1]))

        if len(pile_positions) < 2:
            if log_callback:
                log_callback("Insufficient pile positions for spacing dimensions")
            return

        # Calculate spacing between adjacent piles and add dimensions
        spacing_count = 0
        for i in range(len(pile_positions)):
            for j in range(i + 1, len(pile_positions)):
                pos1 = pile_positions[i]
                pos2 = pile_positions[j]
                
                distance = math.sqrt((pos2[0] - pos1[0])**2 + (pos2[1] - pos1[1])**2)
                
                # Only show dimensions for reasonable spacing (not too far apart)
                if 1.0 <= distance <= 10.0:
                    # Add dimension line
                    mid_x = (pos1[0] + pos2[0]) / 2
                    mid_y = (pos1[1] + pos2[1]) / 2
                    
                    # Offset dimension line slightly
                    offset_x = 0.3 if pos2[0] > pos1[0] else -0.3
                    offset_y = 0.3 if pos2[1] > pos1[1] else -0.3
                    
                    msp.add_line(pos1, pos2, dxfattribs={'layer': 'DIMENSIONS', 'color': 6})
                    
                    # Add spacing text
                    text = msp.add_text(
                        f"{distance:.2f}m",
                        dxfattribs={'layer': 'TEXT', 'height': text_height * 0.6, 'color': 6}
                    )
                    text.set_placement((mid_x + offset_x, mid_y + offset_y), align=TextEntityAlignment.MIDDLE_CENTER)
                    
                    spacing_count += 1
                    
                    # Limit number of spacing dimensions to avoid clutter
                    if spacing_count >= 5:
                        break
            
            if spacing_count >= 5:
                break

        if log_callback:
            log_callback(f"Added {spacing_count} pile spacing dimensions")

    except Exception as e:
        if log_callback:
            log_callback(f"Error adding pile spacing dimensions: {e}")


def add_pile_cap_dimensions(msp, text_height: float, log_callback=None) -> None:
    """
    Add dimensions for pile cap polygons.

    Args:
        msp: DXF model space
        text_height: Height for dimension text
        log_callback: Optional logging callback
    """
    try:
        pile_cap_count = 0

        for entity in msp:
            if (hasattr(entity.dxf, 'layer') and
                entity.dxf.layer == 'PILE_CAPS' and
                entity.dxftype() == 'LWPOLYLINE'):

                # Get pile cap vertices
                vertices = []
                for point in entity:
                    vertices.append((point[0], point[1]))

                if len(vertices) >= 3:
                    # Calculate bounding box dimensions
                    xs = [v[0] for v in vertices]
                    ys = [v[1] for v in vertices]

                    min_x, max_x = min(xs), max(xs)
                    min_y, max_y = min(ys), max(ys)

                    cap_width = max_x - min_x
                    cap_height = max_y - min_y

                    # Add width dimension (top of pile cap)
                    dim_y = max_y + 0.5
                    msp.add_line((min_x, dim_y), (max_x, dim_y),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})
                    msp.add_line((min_x, dim_y - 0.1), (min_x, dim_y + 0.1),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})
                    msp.add_line((max_x, dim_y - 0.1), (max_x, dim_y + 0.1),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})

                    # Add width text
                    text = msp.add_text(
                        f"{cap_width:.2f}m",
                        dxfattribs={'layer': 'TEXT', 'height': text_height * 0.6, 'color': 3}
                    )
                    text.set_placement(((min_x + max_x) / 2, dim_y + 0.2), align=TextEntityAlignment.MIDDLE_CENTER)

                    # Add height dimension (right of pile cap)
                    dim_x = max_x + 0.5
                    msp.add_line((dim_x, min_y), (dim_x, max_y),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})
                    msp.add_line((dim_x - 0.1, min_y), (dim_x + 0.1, min_y),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})
                    msp.add_line((dim_x - 0.1, max_y), (dim_x + 0.1, max_y),
                               dxfattribs={'layer': 'DIMENSIONS', 'color': 3})

                    # Add height text
                    text = msp.add_text(
                        f"{cap_height:.2f}m",
                        dxfattribs={'layer': 'TEXT', 'height': text_height * 0.6, 'color': 3}
                    )
                    text.set_placement((dim_x + 0.2, (min_y + max_y) / 2), align=TextEntityAlignment.MIDDLE_CENTER)
                    text.dxf.rotation = 90  # Rotate text for vertical dimension

                    pile_cap_count += 1

                    if log_callback:
                        log_callback(f"Added pile cap dimension: {cap_width:.2f}m × {cap_height:.2f}m")

                    # Limit to first few pile caps to avoid clutter
                    if pile_cap_count >= 3:
                        break

    except Exception as e:
        if log_callback:
            log_callback(f"Error adding pile cap dimensions: {e}")
