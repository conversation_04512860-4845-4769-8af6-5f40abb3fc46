# Foundation Automation - Foundation Design Package

The design_fdn package is the core foundation design system for the Foundation Automation project. It provides comprehensive tools for foundation design, analysis, and verification, including both Ultimate Limit State (ULS) and Serviceability Limit State (SLS) design calculations.

## Package Structure

The package is organized into several key modules and subpackages:

### Core Modules

1. `__init__.py`
   - Main package initialization
   - Exports all design functions
   - Defines package interface

2. `designer_gui.py`
   - Graphical user interface for foundation design
   - Implements SafeDesignCheckerGUI
   - Provides interactive design tools

3. `designer_main.py`
   - Main design functionality
   - Contains core design functions:
     - `run_uls_sls_calculations`: Combined ULS and SLS calculations
     - `run_design_automation`: Automated design process
     - `design_bp`: Bored pile design
     - `design_shp`: Steel H-pile design
     - `design_dhp`: Double H-pile design
     - `design_mp`: Multiple pile design

4. `data_processing.py`
   - Data processing functions
   - Implements:
     - Pile calculations (SLS and ULS)
     - P-delta effects
     - Settlement calculations
     - NSF processing

5. `pile_capacity.py`
   - Pile capacity calculations
   - Implements capacity calculations for:
     - Bored piles
     - Steel H-piles
     - Double H-piles
     - Multiple piles

6. `piling_schedule.py`
   - Piling schedule generation
   - Implements:
     - Stepping effect calculations
     - Piling schedule generation

7. `read.py`
   - Input data reading functions
   - Data validation and processing

8. `error_handling.py`
   - Error handling and validation
   - Exception management

9. `initialization.py`
   - Package initialization
   - File path management
   - Configuration setup

10. `design_fdn_config.py`
    - Configuration settings
    - Design parameters
    - Default values

### Subpackages

1. `sls_design/`
   - Serviceability Limit State design
   - Angular rotation checks
   - Deflection analysis
   - Settlement calculations

2. `uls_design_bp/`
   - Ultimate Limit State design for bored piles
   - N-M interaction analysis
   - Shear design
   - Rebar optimization

3. `uls_design_hp/`
   - Ultimate Limit State design for H-piles
   - Steel section design
   - H-pile verification
   - N-M-V interaction analysis

## Key Functions

### Main Design Functions
- `run_uls_sls_calculations`: Combined ULS and SLS calculations
- `run_design_automation`: Automated design process
- `design_bp`: Bored pile design
- `design_shp`: Steel H-pile design
- `design_dhp`: Double H-pile design
- `design_mp`: Multiple pile design

### Data Processing
- `cal_pile_sls`: SLS calculations
- `cal_pile_uls`: ULS calculations
- `cal_pile_pdelta`: P-delta effects
- `cal_settlement`: Settlement calculations
- `process_nsf`: NSF processing

### Pile Capacity
- `cal_capacity_bp`: Bored pile capacity
- `cal_capacity_shp`: H-pile capacity
- `cal_capacity_dhp`: Double H-pile capacity
- `cal_capacity_mp`: Multiple pile capacity
- `cal_capacity_piles`: General pile capacity

### Piling Schedule
- `cal_stepping_effect`: Stepping effect calculations
- `gen_piling_schedule`: Schedule generation

## Design Criteria

The package implements design criteria for:
1. Ultimate Limit State (ULS)
   - Strength verification
   - Stability checks
   - Material utilization

2. Serviceability Limit State (SLS)
   - Deformation limits
   - Movement criteria
   - Structural performance

3. Pile Types
   - Bored piles
   - Steel H-piles
   - Double H-piles
   - Multiple pile groups

## Usage Examples

### GUI Interface
```python
from design_fdn.designer_gui import SafeDesignCheckerGUI

# Create and show GUI
designer = SafeDesignCheckerGUI()
designer.show()
```

### Automated Design
```python
from design_fdn.designer_main import run_design_automation

# Run automated design process
design_results = run_design_automation(
    input_data=input_data,
    design_parameters=design_parameters
)
```

### Pile Capacity Calculation
```python
from design_fdn.pile_capacity import cal_capacity_bp

# Calculate bored pile capacity
capacity = cal_capacity_bp(
    pile_properties=pile_properties,
    loading_conditions=loading_conditions
)
```

## Best Practices

1. Use automated design process for comprehensive analysis
2. Verify input data accuracy
3. Follow local building codes
4. Document assumptions and calculations
5. Perform sensitivity analysis
6. Validate results

## Error Handling

The package includes error handling for:
- Invalid input data
- Calculation failures
- Limit criteria violations
- Geometric constraints
- Material property validation

## Integration Points

This package integrates with:
- Foundation Automation GUI system
- Structural analysis modules
- Material property databases
- Load combination modules
- Visualization tools
- SAFE integration

## Dependencies

The package relies on external dependencies:
- numpy: Numerical calculations
- pandas: Data handling
- matplotlib: Visualization
- scikit-learn: Optimization

## Version

Current version aligns with Foundation Automation system version V5.3

## Subpackage Documentation

For detailed documentation of subpackages:
- [sls_design/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/design_fdn/sls_design/README.md:0:0-0:0)
- [uls_design_bp/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/design_fdn/uls_design_bp/README.md:0:0-0:0)
- [uls_design_hp/README.md](cci:7://file:///c:/Users/<USER>/CursorProjects/Foundation-Automation/design_fdn/uls_design_hp/README.md:0:0-0:0)
