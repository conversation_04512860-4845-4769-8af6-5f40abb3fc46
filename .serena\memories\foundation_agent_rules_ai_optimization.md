# Foundation Agent Rules and AI Optimization System

## AI-Driven Pile Layout Generation Rules

### Core Design Philosophy
- **ZERO FALLBACK POLICY**: Absolute prohibition of fallback mechanisms, backup strategies, or error recovery
- **Single-Path Execution**: One algorithm path per function with explicit failure modes
- **AI Integration**: Comprehensive AI analysis for optimal pile type selection
- **NSGA-III Optimization**: Multi-objective optimization for complex pile foundation design

### Pile Type Selection Priority
1. **DHP (Driven H-Piles)**: First priority with steel section specifications
2. **SHP (Socket H-Piles)**: Second priority with enhanced capacity
3. **BP (Bored Piles)**: Third priority with diameter-specific variants

### Case-Based Layout Rules

#### **Case 1: Single Column Only**
1. **Pile Cap Generation**:
   - Initial pile cap = convex hull of column + edge distance
   - Maximum pile cap = initial cap + (1 × pile diameter)
   - Local axis determination from optimal rectangle
   - Site boundary trimming and validation

2. **Pile Layout Generation**:
   - **1 pile**: Position at load center
   - **2 piles**: Along major axis at ±1.5 pile diameters
   - **3-8 piles**: Regular polygon layout
   - **>8 piles**: Grid-based symmetric placement
   - **BP**: EXACTLY at load center (maximum 1 pile)

3. **Grid Generation Rules**:
   - Grid spacing = minimum pile spacing
   - Load center MUST be grid intersection point
   - 2 rows in major/minor axis outside optimal rectangle
   - Enhanced geometry processing with wall polyline buffering

#### **Multi-Column Cases (Case 2, 4)**
- Advanced clustering algorithms for complex structural arrangements
- Multi-objective optimization using NSGA-III
- Constraint satisfaction for geometric and capacity requirements

### DXF Layer Organization Standards

#### **Pre-selection Layers**:
- `PRESELECTION_DHP`: Red dotted cross markers
- `PRESELECTION_SHP`: Green dotted cross markers  
- `PRESELECTION_BP_[CAPACITY]kN_[DIAMETER]m`: Magenta dotted (specific variants)
- `PRESELECTION_[TYPE]_CAP`: Dashed boundary lines for maximum pile caps

#### **Final Design Layers**:
- `PILE_CAPS`: Final pile cap boundaries
- `PILES_DHP`, `PILES_SHP`, `PILES_BP`: Final pile positions
- `COLUMNS`, `WALLS`: Structural elements
- `LOAD_CENTROIDS`: Load centers with annotations

### AI Pre-Selection Analysis
- **Comprehensive Evaluation**: All pile types analyzed for each structural group
- **Capacity Utilization**: Load requirements vs. pile capacity matching
- **Geometric Constraints**: Site boundary and spacing requirement validation
- **Visualization**: Single consolidated DXF with pre-selection rationale
- **Layer-based Organization**: Individual pile type specifications with clear identification

### Enhanced Data Processing
- **Excel Integration**: Detailed coordinate resolution from Point, Column, Wall dataframes
- **Wall Polyline Processing**: Continuous wall geometry from segmented data
- **Column Section Integration**: Actual section dimensions when available
- **Site Boundary Extraction**: Automatic boundary processing from Excel
- **Validation Framework**: Comprehensive input validation and error reporting

### Quality Assurance Framework
- **Zero Tolerance Policy**: No fallback mechanisms or graceful degradation
- **Explicit Failure Modes**: All failures must be loud and immediate
- **Single Algorithm Paths**: No alternative or backup algorithms
- **Comprehensive Testing**: System must fail test cases for invalid inputs
- **Professional Documentation**: Clear rules with no fallback references

This system enforces strict engineering discipline while leveraging advanced AI for optimal foundation design automation.