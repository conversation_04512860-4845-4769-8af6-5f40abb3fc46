# Foundation Automation - Ultimate Limit State Design for Bored Piles

The uls_design_bp package provides Ultimate Limit State (ULS) design calculations for bored piles, focusing on structural safety and capacity verification in foundation engineering.

## Package Structure

The package is organized into several key modules:

### Core Modules

1. `__init__.py`
   - Package initialization
   - Exports main ULS design functions
   - Defines package interface

2. `circular_column.py`
   - Handles circular column design
   - Implements:
     - `CircularColumn`: Main column design class
     - `create_column`: Column creation function
     - `calculate_rebar_coordinates`: Rebar positioning calculations

3. `group_bp.py`
   - Manages bored pile groups
   - Implements group design and analysis functions
   - Handles pile group interactions

4. `nm_curve_calc.py`
   - Calculates N-M interaction curves
   - Implements:
     - `calculate_nm_curve`: Full curve generation
     - `calculate_nm_point`: Specific point calculations
     - `calculate_rebar_stress`: Rebar stress analysis

5. `nm_database.py`
   - Manages N-M curve database
   - Implements:
     - `save_nm_curves_db`: Curve storage
     - `load_nm_curves_db`: Curve retrieval
     - `pre_calculate_nm_curves`: Pre-computation
     - `get_curve_from_db`: Curve lookup

6. `nm_validation.py`
   - Validates N-M curve results
   - Implements:
     - `check_nm_points`: Point validation
     - `filter_load_by_segment`: Load filtering
     - `calculate_utilization`: Utilization factor calculation

7. `nm_visualization.py`
   - Handles N-M curve visualization
   - Implements `export_nm_check`: Visualization export

8. `prokon_c13.py`
   - Prokon C13 design integration
   - Implements Prokon-specific design functions

9. `rebar_design.py`
   - Manages rebar design and optimization
   - Implements:
     - `update_rebar_configuration`: Rebar updates
     - `design_optimal_rebar`: Optimization functions

10. `uls_bp_nm.py`
    - Main ULS design for N-M interaction
    - Implements:
      - `design_all_bp_segment_nm`: Full segment design
      - `group_bp_segment_rebar`: Group rebar design
      - `column_effective_length_factor`: Length factor calculations

11. `uls_bp_v.py`
    - Main ULS design for shear
    - Implements:
      - `check_bp_shear`: Shear verification
      - `design_bp_links`: Link design
      - `design_bp_shear`: Shear design

## Key Functions

### N-M Interaction Design
- `design_all_bp_segment_nm`: Comprehensive N-M design
- `calculate_nm_curve`: N-M curve generation
- `calculate_nm_point`: Specific point calculations
- `calculate_rebar_stress`: Rebar stress analysis
- `export_nm_check`: Visualization export

### Shear Design
- `check_bp_shear`: Shear verification
- `design_bp_links`: Link design
- `design_bp_shear`: Shear design

### Rebar Design
- `update_rebar_configuration`: Rebar updates
- `design_optimal_rebar`: Optimization functions
- `calculate_rebar_coordinates`: Positioning calculations

### Group Design
- `group_bp_segment_rebar`: Group rebar design
- `check_bp_shear`: Group shear verification

## Design Criteria

The package implements ULS design criteria for:
1. N-M Interaction
   - Moment capacity
   - Axial load capacity
   - Rebar stress limits
   - Concrete stress limits

2. Shear
   - Shear capacity
   - Link spacing requirements
   - Development length

3. Column Design
   - Effective length factors
   - Slenderness effects
   - Buckling considerations

## Usage Examples

### N-M Interaction Design
```python
from uls_design_bp import design_all_bp_segment_nm

# Design N-M interaction
nm_design = design_all_bp_segment_nm(
    segment_properties=segment_properties,
    loading_conditions=loading_conditions
)
```

### Shear Design
```python
from uls_design_bp import check_bp_shear, design_bp_links

# Verify shear capacity
shear_check = check_bp_shear(
    pile_properties=pile_properties,
    loading_conditions=loading_conditions
)

# Design shear reinforcement
shear_design = design_bp_links(
    shear_check=shear_check,
    material_properties=material_properties
)
```

### Rebar Design
```python
from uls_design_bp import design_optimal_rebar

# Optimize rebar configuration
rebar_design = design_optimal_rebar(
    column_properties=column_properties,
    loading_conditions=loading_conditions
)
```

## Best Practices

1. Verify input data accuracy
2. Consider site-specific conditions
3. Follow local building codes
4. Document assumptions and calculations
5. Perform sensitivity analysis
6. Validate results against manual calculations

## Error Handling

The package includes error handling for:
- Invalid input data
- Calculation failures
- Limit criteria violations
- Geometric constraints
- Material property validation

## Integration Points

This package integrates with:
- Foundation Automation design system
- Structural analysis modules
- Material property databases
- Load combination modules
- Visualization tools

## Design Standards

The package implements ULS design standards for:
- Structural safety
- Capacity verification
- Material utilization
- Foundation behavior

## Dependencies

The package relies on external dependencies:
- matplotlib: Visualization
- numpy: Numerical calculations
- pandas: Data handling
- scikit-learn: Optimization

## Version

Current version aligns with Foundation Automation system version V5.3
