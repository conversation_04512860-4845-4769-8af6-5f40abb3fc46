"""Load Combination Processing Module for SAFE16/SAFE22 Structural Analysis

This module provides comprehensive functionality for processing load patterns, load cases, 
and load combinations from Excel inputs and converting them to both SAFE16 and SAFE22 
finite element model formats. It handles the complete load combination workflow including 
pattern definitions, case specifications, and combination formulations for structural 
analysis and design.

The module serves as the central hub for load combination processing, automatically 
generating compatible data structures for both SAFE16 and SAFE22 analysis platforms 
while ensuring compliance with structural design codes and load combination requirements.

Key Functionality:
- Load pattern processing and definition for various load types
- Load case processing with scale factors and load pattern associations
- Load combination generation for ultimate and serviceability limit states
- Concrete frame design combination data processing
- Concrete slab design combination data processing
- Dual SAFE16/SAFE22 compatibility with format-specific optimizations

Load Combination Framework:
The module implements a comprehensive load combination framework that supports:
- **Load Patterns**: Basic load definitions (Dead, Live, Wind, Seismic, etc.)
- **Load Cases**: Specific loading scenarios with scale factors and pattern combinations
- **Load Combinations**: Design combinations for ultimate and serviceability limit states
- **Design Integration**: Automatic combination generation for concrete design

Load Pattern Types Supported:
- **Dead Loads (DL)**: Permanent structural and non-structural loads
- **Live Loads (LL)**: Variable occupancy and usage loads
- **Wind Loads (WL)**: Environmental wind loading in multiple directions
- **Seismic Loads (EQ)**: Earthquake loading in orthogonal directions
- **Snow Loads (SL)**: Environmental snow loading on roof systems
- **Construction Loads (CL)**: Temporary loads during construction phases
- **Temperature Loads (TL)**: Thermal effects and temperature variations
- **Prestress Loads (PS)**: Post-tensioning and prestressing effects

Load Case Processing Features:
- Automatic scale factor application for load pattern combinations
- Multiple load pattern associations within single load cases
- Linear static analysis case generation
- Load case naming conventions and organization
- Integration with analysis solver requirements

Load Combination Types:
- **Ultimate Limit State (ULS)**: Strength design combinations per design codes
- **Serviceability Limit State (SLS)**: Deflection and crack control combinations
- **Seismic Design**: Special earthquake load combinations
- **Wind Design**: Wind load combinations with directional effects
- **Construction**: Temporary load combinations during construction

Design Code Compliance:
- ACI 318: American Concrete Institute design combinations
- ASCE 7: Minimum design loads and load combinations
- IBC: International Building Code load requirements
- Custom combinations: User-defined load combination formulations

SAFE Integration Features:
- Seamless SAFE16/SAFE22 compatibility with version-specific optimizations
- Automatic design combination generation for concrete elements
- Frame and slab design combination data processing
- Load combination validation and error checking
- Performance optimization for large-scale structural models

Technical Specifications:
- Load factor precision and rounding control
- Load combination equation parsing and validation
- Combination naming conventions and organization
- Design envelope generation for multiple load combinations
- Load case sequencing and dependency management

Performance Considerations:
- Efficient processing of hundreds of load combinations
- Memory-optimized data structures for complex loading scenarios
- Parallel processing capabilities for both SAFE16 and SAFE22 formats
- Automatic validation and error handling throughout the process
- Optimized DataFrame operations for large-scale structural models

Error Handling and Validation:
- Comprehensive error checking for load combination validity
- Graceful handling of missing load patterns or cases
- Load factor validation and range checking
- Combination equation syntax validation
- Automatic recovery from processing errors

Dependencies:
- build_fem.write_safe.safe16: SAFE16-specific load combination functions
- build_fem.write_safe.safe22: SAFE22-specific load combination functions
- datetime: Timestamp generation for process tracking and error logging
- pandas: DataFrame operations and data manipulation

Usage:
    from build_fem.write_safe.write_load_comb import (
        write_load_pattern, write_load_case, write_load_combination
    )
    
    # Process load patterns
    safe16_dfs, safe22_dfs = write_load_pattern(excel_inputs, safe16_dfs, safe22_dfs)
    
    # Process load cases
    safe16_dfs, safe22_dfs = write_load_case(excel_inputs, safe16_dfs, safe22_dfs)
    
    # Process load combinations
    safe16_dfs, safe22_dfs = write_load_combination(excel_inputs, safe16_dfs, safe22_dfs)

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

from datetime import datetime

# SAFE16-specific load combination processing functions
from build_fem.write_safe.safe16 import (
    _write_load_pattern_safe16,           # Load pattern definitions for SAFE16
    _write_load_case_safe16,              # Load case to pattern associations for SAFE16
    _write_load_comb_to_safe16,           # Load combination definitions for SAFE16
    _write_general_load_cases_safe16,     # General load case properties for SAFE16
    _write_static_load_cases_safe16       # Static load case properties for SAFE16
)

# SAFE22-specific load combination processing functions
from build_fem.write_safe.safe22 import (
    # Load pattern processing for SAFE22
    load_pattern_LoadPatternDefinitions_safe22,    # Load pattern definitions and properties
    
    # Load case processing for SAFE22
    load_case_LoadCasesLinearStatic_safe22,        # Linear static load case definitions
    
    # Load combination processing for SAFE22
    load_comb_LoadCombinationDefinitions_safe22,   # Load combination definitions
    load_comb_ConcFrameDesignComboData_safe22,     # Concrete frame design combinations
    load_comb_ConcSlabDesignComboData_safe22       # Concrete slab design combinations
)


def write_load_pattern(excel_inputs, safe16_dfs, safe22_dfs):
    """Process load patterns for both SAFE16 and SAFE22 structural analysis formats.

    Processes load pattern definitions from Excel input data and converts them to both 
    SAFE16 and SAFE22 finite element formats. Load patterns represent the fundamental 
    building blocks of structural loading, defining basic load types such as dead loads, 
    live loads, wind loads, and seismic loads that will be combined in various ways 
    for analysis and design.

    Load patterns serve as the foundation for all load combinations and are essential 
    for organizing and categorizing different types of structural loads. Each pattern 
    represents a specific load type with consistent characteristics and application 
    methods throughout the structural model.
    """
    # Process load patterns for SAFE16 format
    safe16_dfs = _write_load_pattern_safe16(excel_inputs, safe16_dfs)

    # Process load patterns for SAFE22 format with enhanced pattern definitions
    safe22_dfs, df_append = load_pattern_LoadPatternDefinitions_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def write_load_case(excel_inputs, safe16_dfs, safe22_dfs):
    """Process load cases for both SAFE16 and SAFE22 structural analysis formats.

    Processes load case definitions from Excel input data and converts them to both 
    SAFE16 and SAFE22 finite element formats. Load cases represent specific loading 
    scenarios that combine one or more load patterns with appropriate scale factors 
    to create realistic loading conditions for structural analysis.

    Load cases serve as intermediate building blocks between load patterns and load 
    combinations, allowing for the creation of specific loading scenarios such as 
    "Dead + Live", "Dead + Wind", or "Dead + Seismic" with appropriate load factors 
    applied to each pattern component.

    The function handles the complete load case processing workflow including general 
    properties, static analysis parameters, and load pattern associations with scale 
    factors for both SAFE16 and SAFE22 platforms.
    """
    # Process SAFE16 general load case properties
    # Define basic load case identification, type, and classification parameters
    safe16_dfs = _write_general_load_cases_safe16(excel_inputs, safe16_dfs)

    # Process SAFE16 static load case properties
    # Configure linear static analysis parameters and solver settings
    safe16_dfs = _write_static_load_cases_safe16(excel_inputs, safe16_dfs)

    # Process load case to pattern associations with scale factors
    # Link load patterns to load cases with appropriate scaling factors
    safe16_dfs = _write_load_case_safe16(excel_inputs, safe16_dfs)
    
    # Process SAFE22 linear static load case definitions
    # Enhanced load case processing with SAFE22-specific features
    safe22_dfs, df_append = load_case_LoadCasesLinearStatic_safe22(excel_inputs, safe22_dfs)

    return safe16_dfs, safe22_dfs


def write_load_combination(excel_inputs, safe16_dfs, safe22_dfs):
    """Process load combinations for both SAFE16 and SAFE22 structural analysis formats.

    Processes load combination definitions from Excel input data and converts them to both 
    SAFE16 and SAFE22 finite element formats. Load combinations represent the final step 
    in load processing, combining multiple load cases with appropriate factors to create 
    design-level loading conditions that comply with structural design codes.

    Load combinations are essential for structural design as they define the critical 
    loading conditions used for ultimate limit state (strength) design and serviceability 
    limit state (deflection/crack control) design. The function automatically generates 
    combinations for both general analysis and concrete design applications.

    The function includes comprehensive error handling to ensure robust processing even 
    when load combination data contains inconsistencies or missing information, preventing 
    system crashes while providing detailed error reporting.
    """
    try:
        # Process load combinations for SAFE16 format
        # Generate basic load combination definitions with case references and factors
        safe16_dfs = _write_load_comb_to_safe16(excel_inputs, safe16_dfs)

        # Process load combinations for SAFE22 format with enhanced features
        # Generate enhanced load combination definitions for SAFE22 analysis
        safe22_dfs, df_append = load_comb_LoadCombinationDefinitions_safe22(excel_inputs, safe22_dfs)

        # Process concrete frame design combination data for SAFE22
        # Generate design-specific combinations for concrete frame elements (beams, columns)
        safe22_dfs, df_append = load_comb_ConcFrameDesignComboData_safe22(excel_inputs, safe22_dfs)

        # Process concrete slab design combination data for SAFE22
        # Generate design-specific combinations for concrete slab elements
        safe22_dfs, df_append = load_comb_ConcSlabDesignComboData_safe22(excel_inputs, safe22_dfs)

        return safe16_dfs, safe22_dfs

    except Exception as e:
        # Comprehensive error handling to prevent system crashes
        # Log detailed error information with timestamp for debugging
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{now} Error processing load combinations: {str(e)}")
        
        # Return original DataFrames to maintain system stability
        # This ensures the analysis can continue even if load combinations fail
        return safe16_dfs, safe22_dfs