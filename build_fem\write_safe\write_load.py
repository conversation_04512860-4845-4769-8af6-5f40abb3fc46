"""Load Processing Module for SAFE16/SAFE22 Structural Analysis

This module provides comprehensive functionality for processing various types of structural 
loads from Excel inputs and converting them to both SAFE16 and SAFE22 finite element model 
formats. It handles the complete load processing workflow including load validation, 
transformation, output generation, and format-specific data structure creation.

The module serves as a unified interface for all structural loading conditions, automatically 
generating compatible data structures for both SAFE16 and SAFE22 analysis platforms while 
maintaining consistency in load application and analysis approaches.

Key Functionality:
- Point load processing for concentrated forces and moments
- Line load processing for distributed loads along structural elements
- Pile load processing for foundation loading conditions
- Slab load processing for area-distributed loads
- Loading Key Plan (LKP) processing for standardized load patterns
- Beam load processing for linear structural elements
- Column load processing for vertical structural elements
- Wall load processing for planar structural elements
- Core wall load processing for lateral force-resisting systems

Load Types Supported:
- **Point Loads**: Concentrated forces and moments at specific locations
- **Line Loads**: Distributed loads along beams, walls, or structural lines
- **Area Loads**: Uniform or varying loads distributed over slab areas
- **Foundation Loads**: Loads transferred through pile foundations
- **Element Loads**: Loads applied directly to structural elements
- **Environmental Loads**: Wind, seismic, thermal, and other environmental effects

SAFE Integration Features:
- Dual SAFE16/SAFE22 compatibility for maximum software version support
- Consistent load application across both platforms
- Automatic load combination and pattern generation
- Integrated load output reporting and verification
- Comprehensive load validation and error checking

Load Processing Workflow:
1. Excel input data extraction and validation
2. Load magnitude and distribution verification
3. Load pattern and combination processing
4. SAFE16 format-specific data structure generation
5. SAFE22 format-specific data structure generation
6. Load output generation for verification and reporting
7. Consistency validation across both formats

Structural Analysis Integration:
- Load pattern definition for various analysis cases
- Load combination generation for ultimate and serviceability limit states
- Load transfer mechanisms through structural connectivity
- Load distribution analysis for complex structural systems
- Dynamic load effects and response spectrum analysis support

Technical Specifications:
- Load unit consistency (kN, kN/m, kN/m², kN·m)
- Coordinate system alignment with structural geometry
- Load application point and direction specification
- Load duration and time-dependent effects consideration
- Load factor application for design load combinations

Performance Considerations:
- Efficient processing of large load datasets with thousands of load cases
- Memory-optimized data structures for complex loading scenarios
- Automatic validation and error checking throughout the process
- Parallel processing capabilities for both SAFE16 and SAFE22 formats
- Optimized DataFrame operations for large-scale structural models

Dependencies:
- build_fem.write_safe.write_load_output: Load output generation and reporting
- build_fem.write_safe.safe16: SAFE16-specific load processing functions
- build_fem.write_safe.safe22: SAFE22-specific load processing functions
- datetime: Timestamp generation for process tracking
- pandas: DataFrame operations and data manipulation

Usage:
    from build_fem.write_safe.write_load import (
        write_point_load, write_line_load, write_slab_load
    )
    
    # Process point loads
    safe16_dfs, safe22_dfs, excel_outputs = write_point_load(
        excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
    )
    
    # Process line loads
    safe16_dfs, safe22_dfs, excel_outputs = write_line_load(
        excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
    )

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

from datetime import datetime

# Load output generation and reporting module
from build_fem.write_safe import write_load_output

# SAFE16-specific load processing functions
from build_fem.write_safe.safe16 import (
    _write_line_load_safe16,        # Line load processing for SAFE16
    _write_pile_load_safe16,        # Pile load processing for SAFE16
    _write_slab_load_safe16,        # Slab load processing for SAFE16
    _write_lkp_load_safe16,         # Loading Key Plan processing for SAFE16
    _write_beam_load_safe16,        # Beam load processing for SAFE16
    _write_column_load_safe16,      # Column load processing for SAFE16
    _write_wall_load_safe16,        # Wall load processing for SAFE16
    _write_corewall_load_safe16,    # Core wall load processing for SAFE16
    _write_point_load_safe16        # Point load processing for SAFE16
)

# SAFE22-specific load processing functions
from build_fem.write_safe.safe22 import (
    # Point load processing functions for SAFE22
    point_load_GroupDefinitions_safe22,      # Point load group definitions
    point_load_GroupAssignments_safe22,      # Point load group assignments
    point_load_JointAssignsFloorMeshOpt_safe22,  # Joint mesh optimization
    point_load_JointLoadsForce_safe22,       # Joint force load assignments
    
    # Line load processing for SAFE22
    _write_line_load_safe22,                 # Line load processing for SAFE22
    
    # Pile load processing functions for SAFE22
    pile_load_GroupDefinitions_safe22,       # Pile load group definitions
    pile_load_GroupAssignments_safe22,       # Pile load group assignments
    pile_load_JointLoadsForce_safe22,        # Pile joint force loads
    
    # Area load processing functions for SAFE22
    slab_load_AreaLoadsUniform_safe22,       # Slab uniform area loads
    lkp_load_AreaLoadsUniform_safe22,        # LKP uniform area loads
    
    # Element load processing functions for SAFE22
    _write_beam_load_safe22,                 # Beam load processing for SAFE22
    _write_column_load_safe22,               # Column load processing for SAFE22
    _write_wall_load_safe22,                 # Wall load processing for SAFE22
    _write_corewall_load_safe22              # Core wall load processing for SAFE22
)


def write_point_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process point loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes concentrated forces and moments applied at specific points in the structural 
    model from Excel input data and converts them to both SAFE16 and SAFE22 finite element 
    formats. Point loads represent concentrated loading conditions such as equipment loads, 
    column reactions, or specific applied forces at discrete locations.

    The function handles the complete point load processing workflow including data validation, 
    load output generation for verification, and format-specific data structure creation for 
    both SAFE platforms. Point loads are essential for accurate structural analysis where 
    concentrated effects dominate the structural response.
    """    
    # Extract and prepare point load input data
    try:
        df_input_load_point = excel_inputs.InputLoadPoint.fillna(0).copy()
    except AttributeError:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadPoint attribute not found. Skipping point load processing.')
        return safe16_dfs, safe22_dfs, excel_outputs

    # Validate input data availability
    if df_input_load_point.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadPoint is empty. Skipping point load processing.')
        return safe16_dfs, safe22_dfs, excel_outputs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_point_load_output(excel_inputs, excel_outputs)

    # Process point loads for SAFE16 format
    safe16_dfs = _write_point_load_safe16(excel_inputs, safe16_dfs)

    # Process point loads for SAFE22 format with enhanced features
    # Group definitions: Define load groups for organized analysis
    safe22_dfs, df_append = point_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    
    # Group assignments: Assign points to load groups
    safe22_dfs, df_append = point_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
    
    # Joint mesh optimization: Optimize mesh at load application points
    safe22_dfs, df_append = point_load_JointAssignsFloorMeshOpt_safe22(excel_inputs, safe22_dfs)
    
    # Joint force assignments: Apply force and moment loads to joints
    safe22_dfs, df_append = point_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed point load schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs


def write_line_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process line loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes distributed loads applied along linear structural elements from Excel input 
    data and converts them to both SAFE16 and SAFE22 finite element formats. Line loads 
    represent distributed loading conditions such as wall loads on beams, uniform loads 
    along structural members, or varying loads distributed over linear elements.

    The function handles the complete line load processing workflow including data validation, 
    load output generation for verification, and format-specific data structure creation for 
    both SAFE platforms. Line loads are critical for accurate analysis of beams, walls, 
    and other linear structural elements.
    """
    # Extract and prepare line load input data
    df_input_load_line = excel_inputs.InputLoadLine.fillna(0).copy()

    # Validate input data availability
    if df_input_load_line.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadLine is empty. Skipping line load processing.')
        return safe16_dfs, safe22_dfs, excel_outputs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_line_load_output(excel_inputs, excel_outputs)
    
    # Process line loads for SAFE16 format
    safe16_dfs = _write_line_load_safe16(excel_inputs, safe16_dfs)
    
    # Process line loads for SAFE22 format
    safe22_dfs = _write_line_load_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed line load schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs


def write_pile_load(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile loads for both SAFE16 and SAFE22 foundation analysis formats.

    Processes loads applied to pile foundation elements from Excel input data and converts 
    them to both SAFE16 and SAFE22 finite element formats. Pile loads represent foundation 
    loading conditions including axial loads, lateral loads, and moments transferred from 
    the superstructure to the foundation system.

    The function handles the complete pile load processing workflow including data validation 
    and format-specific data structure creation for both SAFE platforms. Pile loads are 
    essential for accurate foundation analysis and design verification.
    """
    # Extract and prepare pile load input data
    df_input_load_pile = excel_inputs.InputLoadPile.fillna(0).copy()

    # Validate input data availability
    if df_input_load_pile.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadPile is empty. Skipping pile load processing.')
        return safe16_dfs, safe22_dfs

    # Process pile loads for SAFE16 format
    safe16_dfs = _write_pile_load_safe16(excel_inputs, safe16_dfs)

    # Process pile loads for SAFE22 format with enhanced features
    # Group definitions: Define pile load groups for organized analysis
    safe22_dfs, df_append = pile_load_GroupDefinitions_safe22(excel_inputs, safe22_dfs)
    
    # Group assignments: Assign piles to load groups
    safe22_dfs, df_append = pile_load_GroupAssignments_safe22(excel_inputs, safe22_dfs)
    
    # Joint force assignments: Apply loads to pile head joints
    safe22_dfs, df_append = pile_load_JointLoadsForce_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed pile input loading schedule to SAFE format!')
    return safe16_dfs, safe22_dfs


def write_slab_load(excel_inputs, safe16_dfs, safe22_dfs):
    """Process slab loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes area loads applied to slab elements from Excel input data and converts them 
    to both SAFE16 and SAFE22 finite element formats. Slab loads represent distributed 
    loading conditions over floor areas including dead loads, live loads, and other 
    area-distributed forces applied to concrete slabs.

    The function handles the complete slab load processing workflow including data validation 
    and format-specific data structure creation for both SAFE platforms. Slab loads are 
    fundamental for accurate floor system analysis and design.
    """
    # Extract and prepare slab load input data
    df_input_load_slab = excel_inputs.InputLoadSlab.fillna(0).copy()

    # Validate input data availability
    if df_input_load_slab.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadSlab is empty. Skipping slab load processing.')
        return safe16_dfs, safe22_dfs

    # Process slab loads for SAFE16 format
    safe16_dfs = _write_slab_load_safe16(excel_inputs, safe16_dfs)
    
    # Process slab loads for SAFE22 format with uniform area load assignments
    safe22_dfs, df_append = slab_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)
    
    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed slab input loading schedule to SAFE format!')
    return safe16_dfs, safe22_dfs


def write_lkp_load(excel_inputs, safe16_dfs, safe22_dfs):
    """Process Loading Key Plan (LKP) loads for both SAFE16 and SAFE22 analysis formats.

    Processes standardized loading patterns defined in Loading Key Plans from Excel input 
    data and converts them to both SAFE16 and SAFE22 finite element formats. LKP loads 
    represent predefined, standardized loading conditions that are commonly applied across 
    multiple areas or structural elements for consistent analysis.

    Loading Key Plans provide a systematic approach to load application by defining standard 
    load magnitudes and patterns that can be referenced and applied to multiple structural 
    areas, ensuring consistency in analysis and reducing input errors.
    """
    # Extract and prepare LKP load input data
    df_input_load_lkp = excel_inputs.InputLoadLKP.fillna(0).copy()

    # Validate input data availability
    if df_input_load_lkp.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadLKP is empty. Skipping LKP load processing.')
        return safe16_dfs, safe22_dfs

    # Process LKP loads for SAFE16 format
    safe16_dfs = _write_lkp_load_safe16(excel_inputs, safe16_dfs)
    
    # Process LKP loads for SAFE22 format with uniform area load assignments
    safe22_dfs, df_append = lkp_load_AreaLoadsUniform_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed Loading Key Plan to SAFE format!')
    return safe16_dfs, safe22_dfs


def write_beam_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process beam loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes loads applied directly to beam elements from Excel input data and converts 
    them to both SAFE16 and SAFE22 finite element formats. Beam loads represent various 
    loading conditions applied to linear structural elements including distributed loads, 
    point loads, and moment applications along beam spans.

    The function handles the complete beam load processing workflow including data validation, 
    load output generation for verification, and format-specific data structure creation for 
    both SAFE platforms. Beam loads are crucial for accurate analysis of primary and 
    secondary beam systems.
    """
    # Extract and prepare beam load input data
    df_input_load_beam = excel_inputs.InputLoadBeam.fillna(0).copy()
    
    # Validate input data availability
    if df_input_load_beam.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadBeam is empty. Skipping beam load processing.')
        return safe16_dfs, safe22_dfs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_beam_load_output(excel_inputs, excel_outputs)
    
    # Process beam loads for SAFE16 format
    safe16_dfs = _write_beam_load_safe16(excel_inputs, safe16_dfs)
    
    # Process beam loads for SAFE22 format
    safe22_dfs = _write_beam_load_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed beam load schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs


def write_column_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process column loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes loads applied directly to column elements from Excel input data and converts 
    them to both SAFE16 and SAFE22 finite element formats. Column loads represent vertical 
    and lateral loading conditions applied to column elements including axial loads, 
    moments, and lateral forces from various load sources.

    The function handles the complete column load processing workflow including data validation, 
    load output generation for verification, and format-specific data structure creation for 
    both SAFE platforms. Column loads are essential for accurate analysis of vertical 
    load-bearing elements and lateral force-resisting systems.
    """
    # Extract and prepare column load input data
    df_input_load_column = excel_inputs.InputLoadColumn.fillna(0).copy()

    # Validate input data availability
    if df_input_load_column.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadColumn is empty. Skipping column load processing.')
        return safe16_dfs, safe22_dfs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_column_load_output(excel_inputs, excel_outputs)
    
    # Process column loads for SAFE16 format
    safe16_dfs = _write_column_load_safe16(excel_inputs, safe16_dfs)
    
    # Process column loads for SAFE22 format
    safe22_dfs = _write_column_load_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed column input loading schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs


def write_wall_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process wall loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes loads applied to wall elements from Excel input data and converts them 
    to both SAFE16 and SAFE22 finite element formats. Wall loads represent various 
    loading conditions applied to planar structural elements including in-plane and 
    out-of-plane forces, distributed pressures, and line loads along wall edges.

    The function handles the complete wall load processing workflow including data validation, 
    load output generation for verification, and format-specific data structure creation for 
    both SAFE platforms. Wall loads are crucial for accurate analysis of shear walls, 
    load-bearing walls, and other planar structural elements.
    """
    # Extract and prepare wall load input data
    df_input_load_wall = excel_inputs.InputLoadWall.fillna(0).copy()

    # Validate input data availability
    if df_input_load_wall.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadWall is empty. Skipping wall load processing.')
        return safe16_dfs, safe22_dfs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_wall_load_output(excel_inputs, excel_outputs)
    
    # Process wall loads for SAFE16 format
    safe16_dfs = _write_wall_load_safe16(excel_inputs, safe16_dfs)
    
    # Process wall loads for SAFE22 format
    safe22_dfs = _write_wall_load_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed wall input loading schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs


def write_corewall_load(excel_inputs, safe16_dfs, safe22_dfs, excel_outputs):
    """Process core wall loads for both SAFE16 and SAFE22 structural analysis formats.

    Processes loads applied to core wall systems from Excel input data and converts them 
    to both SAFE16 and SAFE22 finite element formats. Core wall loads represent complex 
    loading conditions for lateral force-resisting systems including shear forces, 
    overturning moments, and torsional effects from wind and seismic loading.

    Core walls are critical structural elements that provide lateral stability to tall 
    buildings and must be accurately modeled with appropriate loading conditions for 
    safe and efficient structural design. The function handles the complete core wall 
    load processing workflow with optimized performance and readability.
    """
    # Extract and prepare core wall load input data
    df_input_load_corewall = excel_inputs.InputLoadCoreWall.fillna(0).copy()

    # Validate input data availability
    if df_input_load_corewall.empty:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} InputLoadCoreWall is empty. Skipping core wall load processing.')
        return safe16_dfs, safe22_dfs

    # Generate load output data for verification and reporting
    excel_outputs = write_load_output._write_corewall_load_output(excel_inputs, excel_outputs)
    
    # Process core wall loads for SAFE16 format
    safe16_dfs = _write_corewall_load_safe16(excel_inputs, safe16_dfs)
    
    # Process core wall loads for SAFE22 format
    safe22_dfs = _write_corewall_load_safe22(excel_inputs, safe22_dfs)

    # Print confirmation message with timestamp
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f'{now} Transformed corewall input loading schedule to SAFE format!')
    return safe16_dfs, safe22_dfs, excel_outputs
