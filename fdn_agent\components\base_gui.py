"""Base GUI class with common functionality"""
import os
import sys
import threading
import tkinter as tk
from datetime import datetime
from pathlib import Path
from tkinter import messagebox, StringVar
from tkinter import ttk


class BaseGUI:
    """Base class for GUI components with common functionality"""
    
    # Log level constants
    LOG_DEBUG = 'DEBUG'
    LOG_INFO = 'INFO'
    LOG_WARNING = 'WARNING'
    LOG_ERROR = 'ERROR'
    LOG_CRITICAL = 'CRITICAL'
    TERMINAL_LOG_LEVELS = {LOG_ERROR, LOG_WARNING, LOG_CRITICAL}
    
    def __init__(self, parent, title="Application", geometry="700x500"):
        self.window = self._create_window(parent, title, geometry)
        self.status_var = StringVar()
        self.username = self.get_username()
        self.old_stdout = sys.stdout
    
    def _create_window(self, parent, title, geometry):
        """Create and configure the main window"""
        window = tk.Toplevel(parent)
        window.title(title)
        window.geometry(geometry)
        window.resizable(True, True)
        try:
            window.iconbitmap('AIS.ico')
        except:
            pass
        return window
    
    def get_username(self):
        """Get username for logging purposes"""
        try:
            return os.getlogin()
        except:
            return "unknown_user"
    
    def log_to_console(self, message, log_level=None):
        """Enhanced logging with thread safety"""
        if log_level is None:
            log_level = self._auto_detect_log_level(message)
        
        valid_levels = {self.LOG_DEBUG, self.LOG_INFO, self.LOG_WARNING, self.LOG_ERROR, self.LOG_CRITICAL}
        if log_level not in valid_levels:
            log_level = self.LOG_INFO
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{log_level}] {message}"
        
        if log_level in self.TERMINAL_LOG_LEVELS:
            print(formatted_message)
        
        def _update_log():
            if hasattr(self, 'log_text'):
                self.log_text.configure(state="normal")
                self.log_text.insert("end", f"{formatted_message}\n")
                self.log_text.see("end")
                self.log_text.configure(state="disabled")
                self.window.update_idletasks()
        
        if threading.current_thread() == threading.main_thread():
            _update_log()
        else:
            self.window.after(0, _update_log)
    
    def _auto_detect_log_level(self, message):
        """Auto-detect log level from message content"""
        message_lower = message.lower()
        
        critical_keywords = ['critical', 'fatal', 'system failure', 'critical failure']
        if any(keyword in message_lower for keyword in critical_keywords):
            return self.LOG_CRITICAL
        
        error_keywords = ['error', 'failed', 'failure', 'exception', '❌']
        if any(keyword in message_lower for keyword in error_keywords):
            return self.LOG_ERROR
        
        warning_keywords = ['warning', 'caution', 'attention', 'important', 'note:']
        if any(keyword in message_lower for keyword in warning_keywords):
            return self.LOG_WARNING
        
        debug_keywords = ['debug:', 'processing', '...', 'analyzing', 'calculating']
        if any(keyword in message_lower for keyword in debug_keywords):
            return self.LOG_DEBUG
        
        return self.LOG_INFO
    
    def update_progress(self, percent, description=""):
        """Update the progress bar and progress description (thread-safe)"""
        def _update_progress():
            if hasattr(self, 'progress_bar'):
                self.progress_bar.configure(value=percent)
            if hasattr(self, 'progress_var'):
                self.progress_var.set(description)
            self.window.update_idletasks()
        
        if threading.current_thread() == threading.main_thread():
            _update_progress()
        else:
            self.window.after(0, _update_progress)
    
    def _show_error(self, message, exception_instance=None):
        """Show error message and log it"""
        self.log_to_console(f"ERROR: {message}", self.LOG_ERROR)
        if exception_instance:
            self.log_to_console(f"Exception details: {str(exception_instance)}", self.LOG_ERROR)
        messagebox.showerror("Error", message)
