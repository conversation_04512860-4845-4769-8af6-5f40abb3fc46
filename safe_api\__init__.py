﻿"""
SAFE API Module

This package provides a modular interface for SAFE (Structural Analysis & Design) automation.
The package is organized into focused modules for better code management and maintainability.

Main Components:
- safe_environment: SAFE API environment setup
- safe_connection: Connection management
- data_processor: Data extraction and processing
- database_manager: Database operations
- models: Model initialization and analysis
- main: Main orchestration logic

Usage:
    from safe_api.main import main
    main()

Or import specific components:
    from safe_api.safe_connection import setup_safe_connection
    from safe_api.data_processor import extract_safe22_dataframe
"""

# Import main functions for easy access
try:
    from .main import (
        automate_build_fem,
        automate_foundation_design,
        automate_complete_workflow,
        AutomationLogger
    )
    from .safe_connection import setup_safe_connection
    from .data_processor import (
        extract_safe22_dataframe,
        process_point_object_connectivity,
        process_frame_assignments_local_axes,
        process_joint_displacements,
        process_element_forces_columns,
        setup_load_combinations_for_display
    )
    from .database_manager import (
        save_df_to_sqlite,
        save_df_to_database,
        setup_database_path,
        process_and_save_dataframe
    )
    from .models import (
        setup_file_paths_and_read_inputs,
        initialize_model,
        import_and_run_analysis,
        setup_model_and_inputs,
        get_sap_model_object
    )
    
    __all__ = [
        'automate_build_fem',
        'automate_foundation_design',
        'automate_complete_workflow',
        'AutomationLogger',
        'setup_file_paths_and_read_inputs',
        'initialize_model',
        'import_and_run_analysis',
        'setup_model_and_inputs',
        'get_sap_model_object',
        'save_df_to_sqlite',
        'save_df_to_database',
        'setup_database_path',
        'process_and_save_dataframe',
        'extract_safe22_dataframe',
        'process_point_object_connectivity',
        'process_frame_assignments_local_axes',
        'process_joint_displacements',
        'process_element_forces_columns',
        'setup_load_combinations_for_display'
    ]
except ImportError as e:
    # Handle case where dependencies might not be available
    print(f"Warning: Some modules could not be imported: {e}")
    __all__ = []

__version__ = "1.0.0"
__author__ = "Foundation Automation Team"
