"""
Data Processing Module

This module handles data extraction from SAFE database tables and 
conversion to pandas DataFrames for further processing.
"""

import numpy as np
import pandas as pd


def extract_safe22_dataframe(db_tables, selected_table="Point Object Connectivity"):
    """
    Extracts data from SAFE database tables and returns a DataFrame.
    
    Args:
        db_tables: SAFE database tables object
        selected_table (str): Name of the table to extract
        
    Returns:
        pandas.DataFrame: Extracted data as DataFrame
    """
    print(f"Attempting to extract table: '{selected_table}'")

    table_version = 0
    number_records = 0
    table_data = []
    field_key_list = []
    fields_keys_included = []
    group_name = ""
    ret = -1  # Initialize ret to a value indicating failure

    try:
        [ret, group_name, table_version, fields_keys_included, number_records,
         table_data] = db_tables.GetTableForDisplayArray(
            selected_table, field_key_list, group_name, table_version, fields_keys_included, number_records, table_data)

        print(f"GetTableForDisplayArray for '{selected_table}' returned: {ret}")

        if ret != 0:
            print(
                f"ERROR: SAFE API call GetTableForDisplayArray for table '{selected_table}' failed with return code: {ret}.")
            print(f"       Number of records reported: {number_records}, Fields: {list(fields_keys_included)}")
            return pd.DataFrame()  # Return empty DataFrame if API call was not successful

        print(f"Number of records for '{selected_table}': {number_records}")
        print(f"Fields for '{selected_table}': {list(fields_keys_included)}")

    except Exception as e:
        print(f"ERROR: Failed to get table '{selected_table}': {e}")
        return pd.DataFrame()

    fields_keys_included = list(fields_keys_included)
    table_data = list(table_data)

    num_columns = len(fields_keys_included)

    if not table_data or len(table_data) % num_columns != 0:
        print(f"ERROR: Data for table '{selected_table}' is empty or malformed.")
        print(f"       Data length: {len(table_data)}, Columns: {num_columns}")
        return pd.DataFrame()

    num_rows = len(table_data) // num_columns
    reshaped_data = np.array(table_data).reshape(num_rows, num_columns)
    df = pd.DataFrame(data=reshaped_data, columns=fields_keys_included)
    print(f"Successfully created DataFrame with shape: {df.shape}")
    return df


def process_point_object_connectivity(db_tables):
    """
    Process Point Object Connectivity table.
    
    Args:
        db_tables: SAFE database tables object
        
    Returns:
        pandas.DataFrame: Processed DataFrame
    """
    selected_table = "Point Object Connectivity"
    df = extract_safe22_dataframe(db_tables, selected_table)
    return df


def process_frame_assignments_local_axes(db_tables):
    """
    Process Frame Assignments - Local Axes table.
    
    Args:
        db_tables: SAFE database tables object
        
    Returns:
        pandas.DataFrame: Processed DataFrame
    """
    selected_table = "Frame Assignments - Local Axes"
    df = extract_safe22_dataframe(db_tables, selected_table)
    return df


def process_joint_displacements(db_tables):
    """
    Process Joint Displacements table with column renaming.
    
    Args:
        db_tables: SAFE database tables object
        
    Returns:
        pandas.DataFrame: Processed DataFrame with renamed columns
    """
    selected_table = "Joint Displacements"
    df = extract_safe22_dataframe(db_tables, selected_table)
    
    # Rename columns for consistency
    df = df.rename(columns={
        "UniqueName": "Unique Name",
        "OutputCase": "Output Case",
        "CaseType": "Case Type"
    })
    
    return df


def process_element_forces_columns(db_tables):
    """
    Process Element Forces - Columns table with column renaming.
    
    Args:
        db_tables: SAFE database tables object
        
    Returns:
        pandas.DataFrame: Processed DataFrame with renamed columns
    """
    selected_table = "Element Forces - Columns"
    df = extract_safe22_dataframe(db_tables, selected_table)
    
    # Rename columns for consistency
    df = df.rename(columns={
        "UniqueName": "Unique Name",
        "OutputCase": "Output Case",
        "CaseType": "Case Type",
        "ElemStation": "Elem Station",
    })
    
    return df


def setup_load_combinations_for_display(db_tables):
    """
    Set load combinations for display.
    
    Args:
        db_tables: SAFE database tables object
        
    Returns:
        int: Return code from the operation
    """
    ret_load_combo = db_tables.SetLoadCombinationsSelectedForDisplay([])
    print(f"\nSetLoadCombinationsSelectedForDisplay returned: {ret_load_combo}")
    return ret_load_combo
