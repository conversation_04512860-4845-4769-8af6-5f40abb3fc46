"""Convert structural geometry data from Excel to SAFE16 format.

Processes points, beams, walls, columns, slabs, openings, and line loads 
for SAFE16 structural analysis with proper formatting and group assignments."""

import numpy as np
import pandas as pd


def _write_point_coordinates_safe16(excel_inputs, safe16_dfs):
    """Convert point data from Excel to SAFE16 coordinates, filtering out temporary points."""    # Copy point data to avoid modifying original
    df_point = excel_inputs.Point.copy()

    # Filter out temporary points (containing '_T' suffix)
    df_point_filtered = df_point[
        ~df_point['Point'].str.contains('_T', na=False)
    ].copy()

    # Create point_data_df with SAFE16 column structure
    point_data_df = pd.DataFrame({
        'Point (Text)': df_point_filtered['Point'],
        'GlobalX (m)': df_point_filtered['X (m)'],
        'GlobalY (m)': df_point_filtered['Y (m)'],
        'GlobalZ (m)': 0,  # Z coordinate set to 0 for 2D analysis
        'SpecialPt (Yes/No)': 'Yes'  # Mark all points as special points
    })

    # Convert to SAFE16 DataFrame format with proper column structure
    df_append_16 = pd.DataFrame(
        point_data_df.values, columns=safe16_dfs.ObjGeomPointCoordinates.columns
    )
    
    # Append to existing SAFE16 point coordinates
    safe16_dfs.ObjGeomPointCoordinates = pd.concat(
        [safe16_dfs.ObjGeomPointCoordinates, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_beam_geometry_lines_safe16(excel_inputs, safe16_dfs):
    """Convert beam data from Excel to SAFE16 line geometry with start/end points."""    # Copy beam data to avoid modifying original
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for SAFE16 compatibility
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to extract start and end points
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Create geometry dataframe with required columns and defaults
    df_geom = df_beam[['Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()
    df_geom['LineType (Text)'] = None  # Will be determined by SAFE
    df_geom['Length (m)'] = None  # Will be calculated by SAFE

    # Create DataFrame with MultiIndex columns and append to SAFE16 dataframes
    target_columns = safe16_dfs.ObjGeomLines01General.columns.tolist()
    df_append = pd.DataFrame(df_geom.values, columns=pd.MultiIndex.from_tuples(target_columns))
    safe16_dfs.ObjGeomLines01General = pd.concat([safe16_dfs.ObjGeomLines01General, df_append], ignore_index=True)

    return safe16_dfs


def _write_beam_property_assignments_safe16(excel_inputs, safe16_dfs):
    """Assign beam properties to beam elements from Excel input data."""    
    # Copy beam data to avoid modifying original
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for SAFE16 compatibility
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to extract start and end points
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Filter to only include beams with assigned properties
    prop_assign_df = df_beam[~df_beam['BeamProp (Text)'].isnull()].reset_index(drop=True)

    # Extract relevant columns for property assignment
    df_16 = prop_assign_df[['Line (Text)', 'BeamProp (Text)']].copy()

    # Convert to SAFE16 DataFrame format
    target_columns = safe16_dfs.BeamPropertyAssignments.columns.tolist()
    df_append_16 = pd.DataFrame(
        df_16.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing beam property assignments
    safe16_dfs.BeamPropertyAssignments = pd.concat(
        [safe16_dfs.BeamPropertyAssignments, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_beam_insertion_points_safe16(excel_inputs, safe16_dfs):
    """
    Write beam insertion points to SAFE16 format.
    
    Defines beam insertion points (connection points) for beam elements.
    Sets default insertion point to box edge midpoint with zero offsets.
    """    
    # Copy beam data to avoid modifying original
    df_beam = excel_inputs.Beam.copy()

    # Rename columns for SAFE16 compatibility
    df_beam.rename(columns={
        'Beam': 'Line (Text)',
        'Beam Prop': 'BeamProp (Text)'
    }, inplace=True)

    # Split "Points" by ';' to extract start and end points
    df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)

    # Filter to only include valid beam elements
    insertion_point_df = df_beam[~df_beam['Line (Text)'].isnull()].reset_index(drop=True)

    # Create insertion point data with default values
    df_16 = pd.DataFrame()
    df_16['Line (Text)'] = insertion_point_df['Line (Text)']
    df_16['CardinalPt'] = '8 (box edge midpoint)'  # Standard insertion point
    # Set all offsets to zero (no eccentricity)
    df_16['OffsetXI'] = 0  # X offset at start point
    df_16['OffsetYI'] = 0  # Y offset at start point
    df_16['OffsetZI'] = 0  # Z offset at start point
    df_16['OffsetXJ'] = 0  # X offset at end point
    df_16['OffsetYJ'] = 0  # Y offset at end point
    df_16['OffsetZJ'] = 0  # Z offset at end point

    # Convert to SAFE16 DataFrame format
    target_columns = safe16_dfs.BeamInsertionPoint.columns.tolist()
    df_append_16 = pd.DataFrame(
        df_16.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing beam insertion points
    safe16_dfs.BeamInsertionPoint = pd.concat(
        [safe16_dfs.BeamInsertionPoint, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_wall_geometry_lines_safe16(excel_inputs, safe16_dfs):
    """
    Write wall geometry lines to SAFE16 format.
    
    Processes wall data from Excel inputs and converts them to SAFE16 
    ObjGeomLines01General format. Creates line elements for structural walls.
    """    
    # Copy wall data to avoid modifying original
    df_wall = excel_inputs.Wall.copy()
    
    # Split "Points" by ';' to extract start and end points
    df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
    
    # Rename columns for SAFE16 compatibility
    df_wall.rename(
        columns={
            'Wall': 'Line (Text)',
            'Center Point': 'Center Point (Text)',
            'Wall Group': 'WallGroup (Text)'
        },
        inplace=True
    )

    # Extract essential columns for line geometry
    df_wall = df_wall[['Line (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()

    # Remove rows where any key identifiers are missing
    df_wall.dropna(subset=['Line (Text)', 'PointI (Text)', 'PointJ (Text)'], inplace=True)

    # Prepare wall geometry data for SAFE16
    df_16 = df_wall.copy()
    df_16['LineType (Text)'] = None  # Will be determined by SAFE
    df_16['Length (m)'] = None  # Will be calculated by SAFE

    # Convert to SAFE16 DataFrame format
    target_columns = safe16_dfs.ObjGeomLines01General.columns.tolist()
    df_append_16 = pd.DataFrame(
        df_16.values, columns=pd.MultiIndex.from_tuples(target_columns)
    )
    
    # Append to existing line geometry
    safe16_dfs.ObjGeomLines01General = pd.concat(
        [safe16_dfs.ObjGeomLines01General, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_column_assign_safe16(excel_inputs, safe16_dfs):
    """
    Write column assignments to SAFE16 format.
    
    Processes column data from Excel inputs and converts them to SAFE16 
    ObjGeomAreas01General format. Handles multi-point column definitions
    by processing points in groups of 4.
    """    
    
    # Copy column data to avoid modifying original
    df_column = excel_inputs.Column.copy()

    df_column['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_column.rename(
        columns={
            'Column': 'Area (Text)',
            'Center Point': 'Center Point (Text)'},
        inplace=True
    )

    # Process each column if data exists
    if df_column.index.size != 0:
        for i in range(df_column.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_column.columns and not pd.isna(df_column['Points'][i]):
                # Split points by semicolon to get individual point names
                points = df_column['Points'][i].split(';')

                # Process points in groups of 4 (SAFE16 limitation for area elements)
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Get up to 4 points, padding with NaN if fewer available
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    # Add new row to SAFE16 areas dataframe
                    row_16 = safe16_dfs.ObjGeomAreas01General.index.size
                    if is_row_start:
                        # First row includes column name and auto flag
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_column['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            df_column['Auto (Yes/No)'][i], None, None]
                        is_row_start = False
                    else:
                        # Subsequent rows for same column (continuation)
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_column['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]
    return safe16_dfs


def _create_safe16_slab_area_data(slab_name, points_list):
    """
    Create standardized slab area data for SAFE16 format.
    
    Helper function to create properly formatted slab area data with up to 16 points.
    This function ensures consistent data structure for slab elements.
    """
    # Calculate number of points defining the slab
    num_points = len(points_list)
    
    # Create base data structure with slab name and point count
    data = {
        ('Area (Text)', ''): slab_name,
        ('NumPoints (Number)', ''): num_points,
        ('LineType (Text)', ''): None,  # Default line type
        ('Thickness (mm)', ''): None,  # Thickness assigned via properties
    }
    
    # Add up to 16 point definitions (SAFE16 limitation)
    for i in range(1, 17):  # Point1 through Point16
        point_val = points_list[i - 1] if i <= num_points else None
        data[(f'Point{i} (Text)', '')] = point_val
        
    return pd.DataFrame([data])  # Return as single-row DataFrame


def _write_slab_assign_ObjGeomAreas01General_safe16(excel_inputs, safe16_dfs):
    """
    Write slab assignments to SAFE16 ObjGeomAreas01General format.
    
    Processes slab data from Excel inputs and converts them to SAFE16 area format.
    Handles multi-point slab definitions by processing points in groups of 4.
    """
    # Copy slab data to avoid modifying original
    df_slab = excel_inputs.Slab.copy()

    df_slab['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    # Process each slab if data exists
    if df_slab.index.size != 0:
        for i in range(df_slab.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_slab.columns and not pd.isna(df_slab['Points'][i]):
                # Split points by semicolon to get individual point names
                points = df_slab['Points'][i].split(';')

                # Process points in groups of 4 (SAFE16 area limitation)
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Get up to 4 points, padding with NaN if fewer available
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    # Add new row to SAFE16 areas dataframe
                    row_16 = safe16_dfs.ObjGeomAreas01General.index.size
                    if is_row_start:
                        # First row includes slab name and auto flag
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_slab['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            df_slab['Auto (Yes/No)'][i], None, None]
                        is_row_start = False
                    else:
                        # Subsequent rows for same slab (continuation)
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_slab['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]
    return safe16_dfs


def _write_opening_ObjGeomAreas01General_safe16(excel_inputs, safe16_dfs):
    """
    Write opening assignments to SAFE16 ObjGeomAreas01General format.
    
    Processes opening data from Excel inputs and converts them to SAFE16 area format.
    Openings are treated as special area elements with no structural properties.
    """    # Copy opening data to avoid modifying original
    df_opening = excel_inputs.Opening.copy()

    df_opening['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_opening.rename(
        columns={
            'Opening': 'Area (Text)'},
        inplace=True
    )

    # Process each opening if data exists
    if df_opening.index.size != 0:
        for i in range(df_opening.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_opening.columns and not pd.isna(df_opening['Points'][i]):
                # Split points by semicolon to get individual point names
                points = df_opening['Points'][i].split(';')

                # Process points in groups of 4 (SAFE16 area limitation)
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Get up to 4 points, padding with NaN if fewer available
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    # Add new row to SAFE16 areas dataframe
                    row_16 = safe16_dfs.ObjGeomAreas01General.index.size
                    if is_row_start:
                        # First row includes opening name and auto flag
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_opening['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            df_opening['Auto (Yes/No)'][i], None, None]
                        is_row_start = False
                    else:
                        # Subsequent rows for same opening (continuation)
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_opening['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]
    return safe16_dfs


def _write_lkp_ObjGeomAreas01General_safe16(excel_inputs, safe16_dfs):
    """
    Write LKP (Load Patch) assignments to SAFE16 ObjGeomAreas01General format.
    
    Processes LKP (Load Patch) data from Excel inputs and converts them to SAFE16 
    area format. LKPs are used to define concentrated load areas on slabs.
    """    
    # Copy LKP data to avoid modifying original
    df_lkp = excel_inputs.LKP.copy()

    df_lkp['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_lkp.rename(
        columns={
            'LKP': 'Area (Text)',
            'Load Group': 'LoadGroup (Text)'},
        inplace=True
    )

    # Process each LKP if data exists
    if df_lkp.index.size != 0:
        for i in range(df_lkp.index.size):
            # Check if 'Points' column exists and has data
            if 'Points' in df_lkp.columns and not pd.isna(df_lkp['Points'][i]):
                # Split points by semicolon to get individual point names
                points = df_lkp['Points'][i].split(';')

                # Process points in groups of 4 (SAFE16 area limitation)
                is_row_start = True
                for j in range(0, len(points), 4):
                    # Get up to 4 points, padding with NaN if fewer available
                    point_group = points[j:j+4]
                    while len(point_group) < 4:
                        point_group.append(np.nan)

                    # Add new row to SAFE16 areas dataframe
                    row_16 = safe16_dfs.ObjGeomAreas01General.index.size
                    if is_row_start:
                        # First row includes LKP name and auto flag
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_lkp['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            df_lkp['Auto (Yes/No)'][i], None, None]
                        is_row_start = False
                    else:
                        # Subsequent rows for same LKP (continuation)
                        safe16_dfs.ObjGeomAreas01General.loc[row_16] = [
                            df_lkp['Area (Text)'][i], None, point_group[0],
                            point_group[1], point_group[2], point_group[3],
                            None, None, None]
    return safe16_dfs


def _write_slab_assign_SoilPropertyAssignments_safe16(excel_inputs, safe16_dfs):
    """
    Write slab soil property assignments to SAFE16 format.
    
    Assigns soil properties to slab elements by processing Excel input data
    and converting to SAFE16 SoilPropertyAssignments format. This links
    slab elements to their supporting soil properties.
    """    
    # Copy slab data to avoid modifying original
    df_slab = excel_inputs.Slab.copy()
    df_slab['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    # Clean soil property data - replace empty strings with None
    df_slab['SoilProp (Text)'] = df_slab['SoilProp (Text)'].replace(' ', None)

    # Filter to only include slabs with assigned soil properties
    condition = ~df_slab['SoilProp (Text)'].isnull()
    df = df_slab.loc[condition].reset_index(drop=True)
    
    if df.index.size != 0:
        # Extract relevant columns for soil property assignment
        df = df[['Area (Text)', 'SoilProp (Text)']]
        
        # Define SAFE16 column structure for soil property assignments
        tuples_16 = [('TABLE:  Soil Property Assignments', 'Area', 'Text'),
                     ('TABLE:  Soil Property Assignments', 'SoilProp', 'Text')]
        
        # Convert to SAFE16 DataFrame format
        df_append_16 = pd.DataFrame(df.values, columns=tuples_16)
        
        # Append to existing soil property assignments
        safe16_dfs.SoilPropertyAssignments = pd.concat(
            [safe16_dfs.SoilPropertyAssignments, df_append_16], ignore_index=True)
    return safe16_dfs


def _write_slab_assign_SlabPropertyAssignments_safe16(excel_inputs, safe16_dfs):
    """
    Write slab property assignments to SAFE16 format.
    
    Assigns slab properties to slab elements by processing Excel input data
    and converting to SAFE16 SlabPropertyAssignments format. This links
    slab elements to their material and thickness properties.
    """
    # Copy slab data to avoid modifying original    # Copy slab data to avoid modifying original
    df_slab = excel_inputs.Slab.copy()

    df_slab['OpeningType (Text)'] = 'None'  # No opening type for regular slabs
    df_slab['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_slab.rename(
        columns={
            'Slab': 'Area (Text)',
            'Soil Prop': 'SoilProp (Text)',
            'Slab Prop': 'SlabProp (Text)',
            'Load Group': 'LoadGroup (Text)',
            'Slab Group': 'SlabGroup (Text)'},
        inplace=True
    )

    # Filter to only include slabs with assigned slab properties
    condition = ~df_slab['SlabProp (Text)'].isnull()
    df = df_slab.loc[condition].reset_index(drop=True)
    
    if df.index.size != 0:
        # Extract relevant columns for slab property assignment
        df = df[['Area (Text)', 'SlabProp (Text)', 'OpeningType (Text)']]
        
        # Define SAFE16 column structure for slab property assignments
        tuples_16 = [('TABLE:  Slab Property Assignments', 'Area', 'Text'),
                     ('TABLE:  Slab Property Assignments', 'SlabProp', 'Text'),
                     ('TABLE:  Slab Property Assignments', 'OpeningType', 'Text')]
        
        # Convert to SAFE16 DataFrame format
        df_append_16 = pd.DataFrame(df.values, columns=tuples_16)
        
        # Append to existing slab property assignments
        safe16_dfs.SlabPropertyAssignments = pd.concat(
            [safe16_dfs.SlabPropertyAssignments, df_append_16], ignore_index=True)
    return safe16_dfs


def _write_slab_group_definitions_safe16(excel_inputs, safe16_dfs):
    """
    Write slab group definitions to SAFE16 format.
    
    Creates group definitions for slab elements by processing Excel input data
    and converting to SAFE16 GroupDefinitions format. Groups are used to
    organize and manage multiple slab elements together.
    """    # Copy slab data to avoid modifying original
    df_slab = excel_inputs.Slab.copy()
    
    # Remove rows without group assignments
    df_slab.dropna(subset=['Slab Group'], inplace=True)

    # Get target column structure from SAFE16 dataframes
    target_columns_tuples = safe16_dfs.GroupDefinitions.columns.tolist()

    # Create list to store new group definitions
    df_append_16_list = []
    
    # Use unique group names to avoid duplicate definitions
    for group_name in df_slab['Slab Group'].unique():
        df_append_16_list.append({
            target_columns_tuples[0]: group_name, 
            target_columns_tuples[1]: 'Red'  # Default color for group visualization
        })

    # Convert to DataFrame with proper column structure
    df_append_16 = pd.DataFrame(df_append_16_list)
    df_append_16 = df_append_16[list(pd.MultiIndex.from_tuples(target_columns_tuples))]

    # Append to existing group definitions
    safe16_dfs.GroupDefinitions = pd.concat(
        [safe16_dfs.GroupDefinitions, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_opening_assign_SlabPropertyAssignments_safe16(excel_inputs, safe16_dfs):
    """
    Write opening slab property assignments to SAFE16 format.
    
    Assigns opening properties to opening elements by processing Excel input data
    and converting to SAFE16 SlabPropertyAssignments format. Openings are treated
    as special slab elements with no structural properties.
    """    
    # Copy opening data to avoid modifying original
    df_opening = excel_inputs.Opening.copy()

    # Set default property values for openings
    df_opening['SlabProp (Text)'] = 'None'  # No structural properties for openings
    df_opening['OpeningType (Text)'] = 'Unloaded'  # Standard unloaded opening
    df_opening['Auto (Yes/No)'] = 'No'  # Manual definition (not auto-generated)
    
    # Rename columns for SAFE16 compatibility
    df_opening.rename(
        columns={
            'Opening': 'Area (Text)'},
        inplace=True
    )

    # Process all opening elements if data exists
    if df_opening.index.size != 0:
        # Extract relevant columns for opening property assignment
        df = df_opening[['Area (Text)', 'SlabProp (Text)', 'OpeningType (Text)']]
        
        # Define SAFE16 column structure for slab property assignments
        tuples_16 = [('TABLE:  Slab Property Assignments', 'Area', 'Text'),
                     ('TABLE:  Slab Property Assignments', 'SlabProp', 'Text'),
                     ('TABLE:  Slab Property Assignments', 'OpeningType', 'Text')]
        
        # Convert to SAFE16 DataFrame format
        df_append_16 = pd.DataFrame(df.values, columns=tuples_16)
        
        # Append to existing slab property assignments
        safe16_dfs.SlabPropertyAssignments = pd.concat(
            [safe16_dfs.SlabPropertyAssignments, df_append_16], ignore_index=True)
    return safe16_dfs


def _write_slab_group_assign_safe16(excel_inputs, safe16_dfs):
    """
    Write slab group assignments to SAFE16 format.
    
    Assigns individual slab elements to groups by processing Excel input data
    and converting to SAFE16 GroupAssignments format. This creates the actual
    membership relationships between slabs and their groups.
    """    # Copy slab data to avoid modifying original
    df_slab = excel_inputs.Slab.copy()
    
    # Remove rows without group assignments
    df_slab.dropna(subset=['Slab Group'], inplace=True)

    # Get target column structure from SAFE16 dataframes
    target_columns_tuples = safe16_dfs.GroupAssignments.columns.tolist()

    # Create list to store new group assignments
    all_new_assignments_16 = []
    
    # Create individual assignments for each slab-group pair
    for _, row in df_slab.iterrows():
        slab_name = row['Slab']      # Individual slab element name
        group_name = row['Slab Group']  # Corresponding group name

        all_new_assignments_16.append({
            target_columns_tuples[0]: group_name,   # Group name
            target_columns_tuples[1]: 'Area',       # ObjectType for slab areas
            target_columns_tuples[2]: slab_name     # Object name (slab)
        })

    # Return unchanged if no assignments to process
    if not all_new_assignments_16:
        return safe16_dfs

    # Convert to DataFrame with proper column structure
    df_append_16 = pd.DataFrame(all_new_assignments_16)
    df_append_16 = df_append_16[list(pd.MultiIndex.from_tuples(target_columns_tuples))]

    # Append to existing group assignments
    safe16_dfs.GroupAssignments = pd.concat(
        [safe16_dfs.GroupAssignments, df_append_16], ignore_index=True
    )
    return safe16_dfs


def _write_line_load_assign_safe16(excel_inputs, safe16_dfs):
    """
    Write line load assignments to SAFE16 format.
    
    Processes line load data from Excel inputs and converts them to SAFE16 
    ObjGeomLines01General format. Line loads are linear loading elements
    that apply distributed loads along specified line segments.
    """    # Copy line load data to avoid modifying original
    df_lineload = excel_inputs.LineLoad.copy()

    # Split "Points" by ';' to extract start and end points
    df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

    # Rename columns for SAFE16 compatibility
    df_lineload.rename(columns={'Line Load': 'Line Load (Text)'}, inplace=True)

    # Define required columns and filter data
    required_cols = ['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']
    df_line_loads = df_lineload[required_cols].copy()
    
    # Remove rows with missing essential data
    df_line_loads.dropna(subset=required_cols, inplace=True)

    # Create geometry data for line loads
    df_geom = df_line_loads[['Line Load (Text)', 'PointI (Text)', 'PointJ (Text)']].copy()
    df_geom['LineType (Text)'] = None  # Will be determined by SAFE
    df_geom['Length (m)'] = None  # Will be calculated by SAFE

    # Get target column structure and create column mapping
    target_cols = safe16_dfs.ObjGeomLines01General.columns.tolist()
    rename_map = {
        'Line Load (Text)': target_cols[0][0],
        'PointI (Text)': target_cols[1][0],
        'PointJ (Text)': target_cols[2][0]
    }
    df_geom.rename(columns=rename_map, inplace=True)

    # Ensure all required columns exist with appropriate defaults
    for col_tuple in target_cols:
        col_name = col_tuple[0]
        if col_name not in df_geom.columns:
            df_geom[col_name] = None  # Set appropriate default value

    # Convert to SAFE16 DataFrame format
    df_append_16 = pd.DataFrame(
        df_geom.values, columns=pd.MultiIndex.from_tuples(target_cols))

    # Append to existing line geometry
    safe16_dfs.ObjGeomLines01General = pd.concat(
        [safe16_dfs.ObjGeomLines01General, df_append_16], ignore_index=True
    )

    return safe16_dfs
