"""
Overlap Analyzer Module for 3D Pile Volume Analysis

This module handles detection and analysis of overlapping volumes between
pile geometries using spatial indexing and boolean operations.
"""

import trimesh
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Set
import logging
from dataclasses import dataclass
from scipy.spatial import cKDTree

from .geometry_engine import PileGeometry

logger = logging.getLogger(__name__)


@dataclass
class OverlapResult:
    """Container for overlap analysis results."""
    pile1_id: str
    pile2_id: str
    overlap_volume: float
    overlap_type: str  # 'part1-part1', 'part1-part2', etc.
    overlap_mesh: Optional[trimesh.Trimesh] = None


class OverlapAnalyzer:
    """
    Handles detection and analysis of overlapping volumes between pile geometries.
    """
    
    def __init__(self, spatial_tolerance: float = 0.1):
        """
        Initialize the OverlapAnalyzer.
        
        Args:
            spatial_tolerance: Tolerance for spatial proximity checks
        """
        self.spatial_tolerance = spatial_tolerance
        
    def find_potential_overlaps(self, geometries: Dict[str, PileGeometry]) -> List[Tuple[str, str]]:
        """
        Find pairs of piles that potentially overlap using spatial indexing.
        
        Args:
            geometries: Dictionary of pile geometries
            
        Returns:
            List of pile ID pairs that may overlap
        """
        if len(geometries) < 2:
            return []
            
        # Extract pile centers and bounding boxes
        pile_ids = list(geometries.keys())
        centers = []
        max_radii = []
        
        for pile_id in pile_ids:
            geometry = geometries[pile_id]
            bounds = geometry.combined_mesh.bounds

            # Handle different bounds formats
            if len(bounds) >= 6:  # 3D bounds: [min_x, min_y, min_z, max_x, max_y, max_z]
                center_x = (bounds[0] + bounds[3]) / 2
                center_y = (bounds[1] + bounds[4]) / 2
                center_z = (bounds[2] + bounds[5]) / 2
                centers.append([center_x, center_y, center_z])

                # Calculate maximum radius (conservative estimate)
                max_radius = max(
                    bounds[3] - bounds[0],  # x extent
                    bounds[4] - bounds[1],  # y extent
                    bounds[5] - bounds[2]   # z extent
                ) / 2
                max_radii.append(max_radius)

            elif len(bounds) >= 4:  # 2D bounds: [min_x, min_y, max_x, max_y]
                center_x = (bounds[0] + bounds[2]) / 2
                center_y = (bounds[1] + bounds[3]) / 2

                # Get z-bounds from vertices
                vertices = geometry.combined_mesh.vertices
                if vertices.shape[1] >= 3:
                    center_z = (vertices[:, 2].min() + vertices[:, 2].max()) / 2
                    z_extent = vertices[:, 2].max() - vertices[:, 2].min()
                else:
                    center_z = 0
                    z_extent = 0

                centers.append([center_x, center_y, center_z])

                # Calculate maximum radius
                max_radius = max(
                    bounds[2] - bounds[0],  # x extent
                    bounds[3] - bounds[1],  # y extent
                    z_extent                # z extent
                ) / 2
                max_radii.append(max_radius)

            else:
                # Fallback: use mesh vertices directly
                vertices = geometry.combined_mesh.vertices
                if len(vertices) > 0:
                    center = vertices.mean(axis=0)
                    if len(center) >= 3:
                        centers.append(center[:3])
                    else:
                        centers.append([center[0], center[1], 0])

                    # Calculate radius as maximum distance from center
                    distances = np.linalg.norm(vertices[:, :3] - center[:3], axis=1)
                    max_radii.append(distances.max())
                else:
                    centers.append([0, 0, 0])
                    max_radii.append(1.0)
            
        centers = np.array(centers)
        max_radii = np.array(max_radii)
        
        # Build spatial index
        tree = cKDTree(centers)
        
        # Find potential overlaps
        potential_pairs = []
        
        for i, pile_id1 in enumerate(pile_ids):
            # Query neighbors within potential overlap distance
            search_radius = max_radii[i] * 2 + self.spatial_tolerance
            neighbor_indices = tree.query_ball_point(centers[i], search_radius)
            
            for j in neighbor_indices:
                if j > i:  # Avoid duplicates and self-comparison
                    pile_id2 = pile_ids[j]
                    potential_pairs.append((pile_id1, pile_id2))
                    
        logger.info(f"Found {len(potential_pairs)} potential overlap pairs")
        return potential_pairs
        
    def analyze_overlap(self, geometry1: PileGeometry, geometry2: PileGeometry) -> List[OverlapResult]:
        """
        Analyze overlap between two pile geometries.
        
        Args:
            geometry1: First pile geometry
            geometry2: Second pile geometry
            
        Returns:
            List of overlap results for different component combinations
        """
        overlaps = []
        
        # Define component pairs to check
        component_pairs = [
            ('part1_cylinder', 'part1_cylinder', 'part1-part1'),
            ('part1_cylinder', 'part2_frustum', 'part1-part2'),
            ('part1_cylinder', 'part3_cylinder', 'part1-part3'),
            ('part2_frustum', 'part2_frustum', 'part2-part2'),
            ('part2_frustum', 'part3_cylinder', 'part2-part3'),
            ('part3_cylinder', 'part3_cylinder', 'part3-part3'),
        ]
        
        for comp1, comp2, overlap_type in component_pairs:
            try:
                mesh1 = getattr(geometry1, comp1)
                mesh2 = getattr(geometry2, comp2)
                
                # Calculate intersection
                overlap_volume, overlap_mesh = self._calculate_intersection(mesh1, mesh2)
                
                if overlap_volume > self.spatial_tolerance:
                    overlaps.append(OverlapResult(
                        pile1_id=geometry1.pile_id,
                        pile2_id=geometry2.pile_id,
                        overlap_volume=overlap_volume,
                        overlap_type=overlap_type,
                        overlap_mesh=overlap_mesh
                    ))
                    
            except Exception as e:
                logger.warning(f"Error analyzing {overlap_type} overlap between "
                             f"{geometry1.pile_id} and {geometry2.pile_id}: {e}")
                
        return overlaps
        
    def _calculate_intersection(self, mesh1: trimesh.Trimesh, 
                              mesh2: trimesh.Trimesh) -> Tuple[float, Optional[trimesh.Trimesh]]:
        """
        Calculate intersection volume and mesh between two meshes.
        
        Args:
            mesh1: First mesh
            mesh2: Second mesh
            
        Returns:
            Tuple of (intersection_volume, intersection_mesh)
        """
        try:
            # Check if meshes are valid
            if not self._is_valid_mesh(mesh1) or not self._is_valid_mesh(mesh2):
                return 0.0, None
                
            # Quick bounding box check
            if not self._bounding_boxes_overlap(mesh1, mesh2):
                return 0.0, None
                
            # Calculate intersection
            intersection = mesh1.intersection(mesh2)
            
            if intersection is None or intersection.vertices is None or len(intersection.vertices) == 0:
                return 0.0, None
                
            # Calculate volume
            volume = self._calculate_mesh_volume(intersection)
            
            return volume, intersection if volume > 0 else None
            
        except Exception as e:
            logger.debug(f"Intersection calculation failed: {e}")
            return 0.0, None
            
    def _is_valid_mesh(self, mesh: trimesh.Trimesh) -> bool:
        """Check if mesh is valid for intersection calculations."""
        return (mesh is not None and 
                mesh.vertices is not None and 
                len(mesh.vertices) > 0 and
                mesh.faces is not None and 
                len(mesh.faces) > 0)
                
    def _bounding_boxes_overlap(self, mesh1: trimesh.Trimesh, mesh2: trimesh.Trimesh) -> bool:
        """Check if bounding boxes of two meshes overlap."""
        bounds1 = mesh1.bounds
        bounds2 = mesh2.bounds
        
        # Check overlap in each dimension
        return (bounds1[0] <= bounds2[3] and bounds2[0] <= bounds1[3] and  # x
                bounds1[1] <= bounds2[4] and bounds2[1] <= bounds1[4] and  # y
                bounds1[2] <= bounds2[5] and bounds2[2] <= bounds1[5])     # z
                
    def _calculate_mesh_volume(self, mesh: trimesh.Trimesh) -> float:
        """Calculate mesh volume with error handling."""
        try:
            if not mesh.is_watertight:
                logger.debug("Intersection mesh is not watertight")
                
            volume = float(mesh.volume)
            return abs(volume)  # Ensure positive volume
            
        except Exception as e:
            logger.debug(f"Error calculating intersection volume: {e}")
            return 0.0
            
    def analyze_all_overlaps(self, geometries: Dict[str, PileGeometry]) -> Dict[Tuple[str, str], float]:
        """
        Analyze overlaps between all pile pairs.
        
        Args:
            geometries: Dictionary of pile geometries
            
        Returns:
            Dictionary mapping pile pairs to total overlap volumes
        """
        # Find potential overlaps
        potential_pairs = self.find_potential_overlaps(geometries)
        
        # Analyze each potential overlap
        overlap_volumes = {}
        all_overlaps = []
        
        for pile1_id, pile2_id in potential_pairs:
            geometry1 = geometries[pile1_id]
            geometry2 = geometries[pile2_id]
            
            overlaps = self.analyze_overlap(geometry1, geometry2)
            all_overlaps.extend(overlaps)
            
            # Sum total overlap volume for this pair
            total_overlap = sum(overlap.overlap_volume for overlap in overlaps)
            
            if total_overlap > 0:
                overlap_volumes[(pile1_id, pile2_id)] = total_overlap
                
        logger.info(f"Found {len(overlap_volumes)} overlapping pile pairs with "
                   f"{len(all_overlaps)} component overlaps")
        
        return overlap_volumes
        
    def create_overlap_report(self, overlaps: List[OverlapResult]) -> pd.DataFrame:
        """
        Create a detailed overlap report.
        
        Args:
            overlaps: List of overlap results
            
        Returns:
            DataFrame with overlap details
        """
        if not overlaps:
            return pd.DataFrame(columns=[
                'pile1_id', 'pile2_id', 'overlap_type', 'overlap_volume'
            ])
            
        data = []
        for overlap in overlaps:
            data.append({
                'pile1_id': overlap.pile1_id,
                'pile2_id': overlap.pile2_id,
                'overlap_type': overlap.overlap_type,
                'overlap_volume': overlap.overlap_volume
            })
            
        df = pd.DataFrame(data)
        
        # Add summary statistics
        logger.info(f"Overlap report created with {len(df)} entries")
        logger.info(f"Total overlap volume: {df['overlap_volume'].sum():.3f}")
        
        return df
        
    def get_overlap_statistics(self, overlap_volumes: Dict[Tuple[str, str], float]) -> Dict[str, float]:
        """
        Calculate overlap statistics.
        
        Args:
            overlap_volumes: Dictionary of overlap volumes
            
        Returns:
            Dictionary with overlap statistics
        """
        if not overlap_volumes:
            return {
                'total_overlaps': 0,
                'total_overlap_volume': 0.0,
                'average_overlap_volume': 0.0,
                'max_overlap_volume': 0.0,
                'min_overlap_volume': 0.0
            }
            
        volumes = list(overlap_volumes.values())
        
        return {
            'total_overlaps': len(overlap_volumes),
            'total_overlap_volume': sum(volumes),
            'average_overlap_volume': np.mean(volumes),
            'max_overlap_volume': max(volumes),
            'min_overlap_volume': min(volumes)
        }
