from datetime import datetime
from math import dist
from math import radians, cos, sin

import numpy as np
import pandas as pd

from fdn_agent import fdn_agent_config as config


def cal_beam_length(excel_inputs):
    df_beam_length = pd.DataFrame(
        columns=['Beam Mark', 'Beam Length (m)', 'Beam <PERSON> (deg)'])

    df_beam_assign = excel_inputs.Beam.copy()
    df_point_assign = excel_inputs.Point.copy()

    # rename df_beam_assign 'Beam' to 'Line (Text)' for consistency
    df_beam_assign.rename(columns={'Beam': 'Line (Text)'}, inplace=True)
    # explode df_beam_assign['Points'] by ';' to form 'PointI (Text)' and 'PointJ (Text)'
    df_beam_assign[['PointI (Text)', 'PointJ (Text)']] = df_beam_assign['Points'].str.split(';', expand=True)
    for i in range(df_beam_assign.index.size):
        beam_name = df_beam_assign.loc[i, 'Line (Text)']
        point_i = df_beam_assign.loc[i, 'PointI (Text)']
        condition = df_point_assign['Point'] == point_i
        point_i_x = df_point_assign.loc[condition, 'X (m)'].values[0]
        point_i_y = df_point_assign.loc[condition, 'Y (m)'].values[0]

        point_j = df_beam_assign.loc[i, 'PointJ (Text)']
        condition = df_point_assign['Point'] == point_j
        point_j_x = df_point_assign.loc[condition, 'X (m)'].values[0]
        point_j_y = df_point_assign.loc[condition, 'Y (m)'].values[0]
        beam_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

        dx = point_j_x - point_i_x
        dy = point_j_y - point_i_y

        if point_i_x == point_j_x:
            beam_theta = 90
        else:
            beam_theta = np.rad2deg(np.arctan(dy / dx))

        row = df_beam_length.index.size
        df_beam_length.loc[row] = [beam_name, beam_length, beam_theta]
    return df_beam_length


def cal_line_length(excel_inputs):
    df_line_length = pd.DataFrame(
        columns=['Line Load', 'Line Length (m)', 'Line Theta (deg)'])

    df_lineload = excel_inputs.LineLoad.copy()
    df_point = excel_inputs.Point.copy()
    for i in range(df_lineload.index.size):
        lineload_name = df_lineload.loc[i, 'Line Load']

        df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)

        point_i = df_lineload.loc[i, 'PointI (Text)']
        condition = df_point['Point'] == point_i
        point_i_x = df_point.loc[condition, 'X (m)'].values[0]
        point_i_y = df_point.loc[condition, 'Y (m)'].values[0]

        point_j = df_lineload.loc[i, 'PointJ (Text)']
        condition = df_point['Point'] == point_j
        point_j_x = df_point.loc[condition, 'X (m)'].values[0]
        point_j_y = df_point.loc[condition, 'Y (m)'].values[0]
        line_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

        dx = point_j_x - point_i_x
        dy = point_j_y - point_i_y

        if point_i_x == point_j_x:
            line_theta = 90
        else:
            line_theta = np.rad2deg(np.arctan(dy / dx))

        row = df_line_length.index.size
        df_line_length.loc[row] = [lineload_name, line_length, line_theta]
    return df_line_length


def cal_wall_length(excel_inputs):
    df_wall_length = pd.DataFrame(
        columns=['Wall Name', 'Wall Length (m)', 'Wall Theta (deg)'])

    df_wall = excel_inputs.Wall.copy()
    df_point = excel_inputs.Point.copy()
    for i in range(df_wall.index.size):
        wall_name = df_wall.loc[i, 'Wall']
        # explode df_wall['Points'] by ';' to form 'PointI (Text)' and 'PointJ (Text)'
        df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)

        point_i = df_wall.loc[i, 'PointI (Text)']
        condition = df_point['Point'] == point_i
        point_i_x = df_point.loc[condition, 'X (m)'].values[0]
        point_i_y = df_point.loc[condition, 'Y (m)'].values[0]

        point_j = df_wall.loc[i, 'PointJ (Text)']
        condition = df_point['Point'] == point_j
        point_j_x = df_point.loc[condition, 'X (m)'].values[0]
        point_j_y = df_point.loc[condition, 'Y (m)'].values[0]
        wall_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

        dx = point_j_x - point_i_x
        dy = point_j_y - point_i_y

        if point_i_x == point_j_x:
            wall_theta = 90
        else:
            wall_theta = np.rad2deg(np.arctan(dy / dx))

        row = df_wall_length.index.size
        df_wall_length.loc[row] = [wall_name, wall_length, wall_theta]
    return df_wall_length


def cal_corewall_length(excel_inputs, df_wall_length):
    df_core_wall_length = pd.DataFrame(
        columns=['CoreWall Name', 'CoreWall Length (m)'])
    df_wall = excel_inputs.Wall.copy()
    
    # Get unique wall groups
    unique_wall_groups = df_wall['Wall Group'].unique()
    
    for core_wall_name in unique_wall_groups:
        core_wall_length = 0
        
        # Find all walls that belong to this core wall group
        condition = df_wall['Wall Group'] == core_wall_name
        walls_in_group = df_wall.loc[condition, 'Wall']
        
        for wall_name in walls_in_group:
            # Find the length of each wall in the group
            wall_condition = df_wall_length['Wall Name'] == wall_name
            if not df_wall_length.loc[wall_condition].empty:
                wall_length = df_wall_length.loc[wall_condition, 'Wall Length (m)'].values[0]
                core_wall_length = core_wall_length + wall_length

        row = df_core_wall_length.index.size
        df_core_wall_length.loc[row] = [core_wall_name, core_wall_length]
    return df_core_wall_length


def transform_wall_load_global(t, length, deg, v_x, v_y, f_z, m_x, m_y, m_z):
    rad = radians(deg)
    # Additional moment due to pile cap shear on top
    mx_add = 0 - (v_y * t)
    my_add = 0 + (v_x * t)

    if deg >= 0:
        # FOR POSITIVE SLOPE WALL, 0 deg WALL & 90 deg WALL
        m_1 = (cos(rad) * m_x + sin(rad) * m_y)
        m_3 = (sin(rad) * m_x - cos(rad) * m_y)
    else:
        # FOR NEGATIVE SLOPE WALL
        rad = radians(abs(deg))
        m_1 = (cos(rad) * m_x - sin(rad) * m_y)
        m_3 = (-sin(rad) * m_x - cos(rad) * m_y)

    # line load due to moment
    fz_m3 = (6 * m_3 / length ** 2)

    if deg >= 0:
        # FOR POSITIVE SLOPE WALL, 0 deg WALL & 90 deg WALL
        m1_add = (cos(rad) * mx_add + sin(rad) * my_add)
        m3_add = (sin(rad) * mx_add - cos(rad) * my_add)
    else:
        # FOR NEGATIVE SLOPE WALL
        rad = radians(abs(deg))
        m1_add = (cos(rad) * mx_add - sin(rad) * my_add)
        m3_add = (-sin(rad) * mx_add - cos(rad) * my_add)
    # Additional line load due to moment of top pile cap shear
    fz_m3_add = (6 * m3_add / length ** 2)

    s_x = v_x / length
    s_y = v_y / length
    s_z_start = f_z / length + fz_m3
    s_z_end = f_z / length - fz_m3
    s_m1 = m_1 / length

    s_z_start_add = fz_m3_add
    s_z_end_add = -fz_m3_add
    s_m1_add = m1_add / length

    s_mz = m_z / length

    return mx_add, my_add, m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add, s_x, s_y, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz


def transform_line_load_local(t, length, v_1, v_3, f_z, m_1, m_3, m_z):
    # per meter in kN/m and kNm/m

    m_3 = m_3 * length  # kNm

    # line load due to moment
    fz_m3 = (6 * m_3 / length ** 2)  # kN/m

    s_1 = v_1  # kN/m
    s_3 = v_3  # kN/m
    s_z_start = f_z - fz_m3  # kN/m
    s_z_end = f_z + fz_m3  # kN/m
    s_m1 = m_1  # kN/m
    s_mz = m_z  # kN/m

    # Additional moment due to pile cap shear on top
    m1_add = 0 + (v_3 * t)  # kNm/m
    m3_add = 0 - (v_1 * length * t)  # kNm

    # Additional line load due to moment of top pile cap shear
    fz_m3_add = (6 * m3_add / length ** 2)  # kN/m

    s_z_start_add = -fz_m3_add  # kN/m
    s_z_end_add = fz_m3_add  # kN/m
    s_m1_add = m1_add  # kN/m

    return m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz


def export_output_loading(file_paths, excel_outputs, log_callback=None):
    df_output_load_point = excel_outputs.PointLoad
    df_output_load_line = excel_outputs.LineLoad
    df_output_load_beam = excel_outputs.BeamLoad
    df_output_load_column = excel_outputs.ColumnLoad
    df_output_load_wall = excel_outputs.WallLoad
    df_output_load_corewall = excel_outputs.CoreWallLoad
    with pd.ExcelWriter(file_paths.ExcelOutputLoading) as writer:
        # point load record
        df_title = pd.DataFrame(columns=df_output_load_point.droplevel([1], axis=1).columns)
        df_value = df_output_load_point.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_POINT_LOAD, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_POINT_LOAD, startrow=1, index=False)

        # line load record
        df_title = pd.DataFrame(columns=df_output_load_line.droplevel([1], axis=1).columns)
        df_value = df_output_load_line.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_LINE_LOAD, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_LINE_LOAD, startrow=1, index=False)

        # beam load record
        df_title = pd.DataFrame(columns=df_output_load_beam.droplevel([1], axis=1).columns)
        df_value = df_output_load_beam.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_BEAM, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_BEAM, startrow=1, index=False)

        # column load record
        df_title = pd.DataFrame(columns=df_output_load_column.droplevel([1], axis=1).columns)
        df_value = df_output_load_column.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_COLUMN, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_COLUMN, startrow=1, index=False)

        # wall load record
        df_title = pd.DataFrame(columns=df_output_load_wall.droplevel([1], axis=1).columns)
        df_value = df_output_load_wall.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_WALL, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_WALL, startrow=1, index=False)

        # corewall load record
        df_title = pd.DataFrame(columns=df_output_load_corewall.droplevel([1], axis=1).columns)
        df_value = df_output_load_corewall.droplevel(0, axis=1)
        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_CORE_WALL, index=False)
        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_CORE_WALL, startrow=1, index=False)    # Print message indicating successful export
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if log_callback:
        log_callback(f'{now} Export Excel Column Load Record')


def _get_limit_state_settings(limit_state):
    """
    Get the design settings for a load combination based on limit state.

    Parameters:
    -----------
    limit_state : str
        Limit state flag ('ULS' or 'SLS')

    Returns:
    --------
    dict
        Dictionary with design setting flags
    """
    if limit_state == 'ULS':
        return {
            'strength': 'Yes',
            'initialization': 'No',
            'norm': 'No',
            'long': 'No'
        }
    else:  # SLS
        return {
            'strength': 'No',
            'initialization': 'Yes',
            'norm': 'Yes',
            'long': 'Yes'
        }


def _get_wind_load_patterns(load_pattern_df):
    """
    Extract wind load pattern names from load pattern data.

    Parameters:
    -----------
    load_pattern_df : DataFrame
        Load pattern data

    Returns:
    --------
    ndarray
        Array of wind load pattern names
    """
    condition = load_pattern_df['Load Type'] == 'WIND'
    return load_pattern_df.loc[condition, 'LoadPat (Text)'].values
