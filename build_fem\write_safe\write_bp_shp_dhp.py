"""Pile Foundation Data Processing Module for SAFE16/SAFE22 Integration

This module provides comprehensive functionality for processing pile foundation data from Excel 
inputs and converting it to both SAFE16 and SAFE22 finite element model formats. It handles 
the complete pile foundation modeling workflow including pile geometry, properties, boundary 
conditions, soil-structure interaction, and group management.

The module serves as a unified interface for pile foundation processing, automatically generating 
compatible data structures for both SAFE16 and SAFE22 analysis platforms. This dual-format 
approach ensures compatibility across different versions of SAFE software while maintaining 
consistency in foundation modeling approaches.

Key Functionality:
- Pile point coordinate generation and management
- Pile column element creation with proper connectivity
- Pile property assignment and material specification
- Point insertion and coordinate system management
- Local axis definition for pile orientation
- End release conditions for pile-cap connections
- End restraint conditions for pile-soil interaction
- Soil spring modeling for lateral pile behavior
- Group management for organized pile classification

Foundation Types Supported:
- Bored Piles (BP): Large diameter cast-in-place concrete piles
- Small Diameter Piles (SHP): Driven or bored piles with smaller diameters
- Driven H-Piles (DHP): Steel H-section piles driven into the ground

Pile Foundation Components:
- Pile Points: Discrete coordinate locations defining pile geometry
- Pile Columns: Structural elements representing pile shafts
- Property Assignments: Material and section property assignments to pile elements
- Boundary Conditions: Support conditions and connection details
- Soil Springs: Lateral and vertical soil-structure interaction modeling
- Group Classifications: Organized grouping for analysis and design purposes

SAFE Integration Features:
- Dual SAFE16/SAFE22 compatibility for maximum software version support
- Consistent data structure generation across both platforms
- Automatic coordinate system management and transformations
- Integrated soil-structure interaction modeling
- Comprehensive boundary condition handling

Soil-Structure Interaction:
- Lateral soil springs based on soil properties and pile dimensions
- Vertical soil springs for axial pile capacity modeling
- Nonlinear soil behavior representation through spring properties
- Soil layering effects and varying soil conditions
- Group effects and pile-to-pile interaction considerations

Design Integration:
- Foundation design data integration from foundation design modules
- Pile capacity calculations and verification
- Settlement analysis and serviceability checks
- Group effect considerations for pile foundations
- Load distribution among individual piles in groups

Performance Considerations:
- Efficient processing of large pile groups with hundreds of piles
- Memory-optimized data structures for complex foundation systems
- Parallel processing capabilities for both SAFE16 and SAFE22 formats
- Automatic validation and error checking throughout the process

Dependencies:
- build_fem.write_safe.safe16: SAFE16-specific pile processing functions
- build_fem.write_safe.safe22: SAFE22-specific pile processing functions
- pandas: DataFrame operations and data manipulation
- datetime: Timestamp generation for process tracking

Usage:
    from build_fem.write_safe.write_bp_shp_dhp import write_bp_shp_dhp
    
    # Process pile foundation data for both SAFE16 and SAFE22
    safe16_dfs, safe22_dfs = write_bp_shp_dhp(excel_inputs, safe16_dfs, safe22_dfs)
    
    # Verify successful processing
    print(f"Processed {len(excel_inputs.Pile)} pile foundations")

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

from datetime import datetime

from build_fem.write_safe.safe16 import (
    _write_pile_points_safe16, _write_pile_column_safe16,
    _write_pile_prop_assign_safe16, _write_pile_point_insertion_safe16,
    _write_pile_local_axis_safe16, _write_pile_end_release_safe16,
    _write_pile_end_restraint_safe16, _write_pile_spring_safe16, _write_pile_group_safe16
)
from build_fem.write_safe.safe22 import (
    _write_pile_points_safe22, _write_pile_column_safe22,
    _write_pile_prop_assign_safe22, _write_pile_point_insertion_safe22,
    _write_pile_local_axis_safe22, _write_pile_end_release_safe22,
    _write_pile_end_restraint_safe22, _write_pile_spring_safe22, _write_pile_group_safe22
)


def _write_pile_points(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile point coordinates for both SAFE16 and SAFE22 formats.

    Generates pile point coordinates from Excel input data and processes them for both SAFE16 
    and SAFE22 finite element models. This function creates the fundamental geometric points 
    that define pile locations, elevations, and coordinate system references required for 
    structural analysis.

    Pile points represent discrete locations along pile shafts, typically including pile top, 
    pile bottom, and intermediate points for soil layer interfaces. The function ensures 
    consistent coordinate generation across both SAFE platforms while maintaining proper 
    coordinate system references and elevation management.
    """
    # Process pile point coordinates for SAFE16 format
    safe16_dfs = _write_pile_points_safe16(excel_inputs, safe16_dfs)
    
    # Process pile point coordinates for SAFE22 format
    safe22_dfs = _write_pile_points_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_column(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile column elements for both SAFE16 and SAFE22 formats.

    Creates pile column elements from Excel input data and processes them for both SAFE16 
    and SAFE22 finite element models. This function generates the structural column elements 
    that represent pile shafts, establishing connectivity between pile points and defining 
    the structural framework for pile foundation analysis.

    Column elements represent the physical pile shafts as structural members capable of 
    carrying axial loads, moments, and shear forces. The function ensures proper element 
    connectivity, maintains consistent element naming, and establishes the structural 
    relationships required for finite element analysis.
    """
    # Process pile column elements for SAFE16 format
    safe16_dfs = _write_pile_column_safe16(excel_inputs, safe16_dfs)
    
    # Process pile column elements for SAFE22 format
    safe22_dfs = _write_pile_column_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_prop_assign(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile property assignments for both SAFE16 and SAFE22 formats.

    Assigns material and section properties to pile elements from Excel input data and processes 
    them for both SAFE16 and SAFE22 finite element models. This function establishes the 
    relationship between pile elements and their structural properties, ensuring proper material 
    behavior and section characteristics for analysis.

    Property assignments define the structural characteristics of pile elements including 
    material properties (concrete, steel), cross-sectional properties (area, moment of inertia), 
    and behavioral parameters required for accurate structural analysis and design.
    """
    # Process pile property assignments for SAFE16 format
    safe16_dfs = _write_pile_prop_assign_safe16(excel_inputs, safe16_dfs)
    
    # Process pile property assignments for SAFE22 format
    safe22_dfs = _write_pile_prop_assign_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_point_insertion(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile point insertion data for both SAFE16 and SAFE22 formats.

    Manages pile point insertion properties from Excel input data and processes them for both 
    SAFE16 and SAFE22 finite element models. This function handles the geometric positioning 
    and insertion point definitions that control how pile elements are positioned relative to 
    their reference points and coordinate systems.

    Point insertion properties define the relationship between pile element centerlines and 
    their reference coordinate systems, enabling precise control over pile positioning for 
    accurate modeling of pile-cap connections and structural geometry.
    """
    # Process pile point insertion data for SAFE16 format
    safe16_dfs = _write_pile_point_insertion_safe16(excel_inputs, safe16_dfs)
    
    # Process pile point insertion data for SAFE22 format
    safe22_dfs = _write_pile_point_insertion_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_local_axis(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile local axis definitions for both SAFE16 and SAFE22 formats.

    Defines pile local coordinate systems from Excel input data and processes them for both 
    SAFE16 and SAFE22 finite element models. This function establishes the local axis 
    orientations for pile elements, ensuring proper directional behavior for structural 
    analysis and design calculations.

    Local axis definitions control the orientation of pile element coordinate systems, 
    affecting the interpretation of loads, moments, and design forces. Proper local axis 
    definition is critical for accurate pile behavior modeling and design verification.
    """
    # Process pile local axis definitions for SAFE16 format
    safe16_dfs = _write_pile_local_axis_safe16(excel_inputs, safe16_dfs)
    
    # Process pile local axis definitions for SAFE22 format
    safe22_dfs = _write_pile_local_axis_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_end_release(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile end release conditions for both SAFE16 and SAFE22 formats.

    Defines pile end release conditions from Excel input data and processes them for both 
    SAFE16 and SAFE22 finite element models. This function establishes the boundary conditions 
    at pile ends, controlling the transfer of forces and moments between pile elements and 
    connected structures (pile caps, soil).

    End release conditions define which force and moment components are transferred at pile 
    connections. These conditions are critical for accurate modeling of pile-cap connections, 
    pile-soil interaction, and overall structural behavior under various loading conditions.
    """
    # Process pile end release conditions for SAFE16 format
    safe16_dfs = _write_pile_end_release_safe16(excel_inputs, safe16_dfs)
    
    # Process pile end release conditions for SAFE22 format
    safe22_dfs = _write_pile_end_release_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_end_restraint(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile end restraint conditions for both SAFE16 and SAFE22 formats.

    Defines pile end restraint conditions from Excel input data and processes them for both 
    SAFE16 and SAFE22 finite element models. This function establishes the support conditions 
    at pile ends, typically representing the interaction between pile tips and bearing soil 
    or bedrock layers.

    End restraint conditions define the support characteristics at pile ends, controlling 
    displacement and rotation constraints. These conditions are essential for accurate 
    modeling of pile-soil interaction, bearing capacity, and overall foundation behavior.
    """
    # Process pile end restraint conditions for SAFE16 format
    safe16_dfs = _write_pile_end_restraint_safe16(excel_inputs, safe16_dfs)
    
    # Process pile end restraint conditions for SAFE22 format
    safe22_dfs = _write_pile_end_restraint_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_spring(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile soil spring properties for both SAFE16 and SAFE22 formats.

    Defines pile soil spring properties from Excel input data and processes them for both 
    SAFE16 and SAFE22 finite element models. This function establishes the soil-structure 
    interaction modeling through spring elements that represent the lateral and vertical 
    soil resistance along pile shafts.

    Soil springs model the complex interaction between pile elements and surrounding soil, 
    accounting for soil stiffness, nonlinear behavior, and varying soil conditions with depth. 
    These springs are essential for accurate pile behavior prediction under lateral and 
    vertical loading conditions.
    """
    # Process pile soil spring properties for SAFE16 format
    safe16_dfs = _write_pile_spring_safe16(excel_inputs, safe16_dfs)
    
    # Process pile soil spring properties for SAFE22 format
    safe22_dfs = _write_pile_spring_safe22(excel_inputs, safe22_dfs)
    
    return safe16_dfs, safe22_dfs


def _write_pile_group(excel_inputs, safe16_dfs, safe22_dfs):
    """Process pile group definitions and assignments for both SAFE16 and SAFE22 formats.

    Creates pile group definitions from Excel input data and processes them for both SAFE16 
    and SAFE22 finite element models. This function establishes organized grouping of pile 
    elements and points for efficient analysis management, design verification, and results 
    processing.

    Pile groups provide hierarchical organization of pile foundation elements, enabling 
    systematic analysis control, design verification, and results interpretation. Groups 
    are created based on pile marks, element types, and structural locations for optimal 
    analysis workflow management.
    """
    # Extract pile and soil spring data for group processing
    df_pile = excel_inputs.Pile.copy()                    # Pile foundation data
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()  # Soil spring interaction data
    pile_marks_series = df_pile['Pile Mark']              # Individual pile identifiers
    all_point_names = df_soil_spring['Point Name']        # All point names from soil springs

    # Create comprehensive group names list for pile organization
    # This provides both individual pile groups and global pile categories
    group_names_list = []
    
    # Individual pile line groups: Separate line elements by pile mark
    group_names_list.extend(['B.PileLine_' + pm for pm in pile_marks_series])
    
    # Individual pile point groups: Separate point elements by pile mark
    group_names_list.extend(['B.PilePoint_' + pm for pm in pile_marks_series])
    
    # Global pile groups: Organize all piles by element type and location
    group_names_list.extend(['A.Pile_Top', 'A.Pile_Bottom', 'A.Pile_Line', 'A.Pile_Point'])

    # Process point names to identify different pile locations and elements
    # Top points: Points at pile head level (connection to pile cap)
    top_points = all_point_names[all_point_names.str.contains('_T', na=False)]
    
    # Bottom points: Points at pile tip level (bearing on soil/rock)
    bottom_points = all_point_names[all_point_names.str.contains('_B', na=False)]
    
    # Line segments: Identify pile shaft elements (exclude bottom end segments)
    # This mask identifies segment names that are not end segments at the bottom
    mask_pile_line_segments = ~all_point_names.str.contains('_B', na=False).shift(-1, fill_value=False)
    line_segments_for_pile_line_group = all_point_names[mask_pile_line_segments]

    # Process pile group data for SAFE16 format
    safe16_dfs = _write_pile_group_safe16(
        safe16_dfs,
        pile_marks_series,                    # Individual pile identifiers
        all_point_names,                      # Complete point name list
        top_points,                           # Pile top points
        bottom_points,                        # Pile bottom points
        line_segments_for_pile_line_group,    # Pile line segments
        group_names_list                      # Complete group names list
    )
    
    # Process pile group data for SAFE22 format
    safe22_dfs = _write_pile_group_safe22(
        safe22_dfs,
        pile_marks_series,                    # Individual pile identifiers
        all_point_names,                      # Complete point name list
        top_points,                           # Pile top points
        bottom_points,                        # Pile bottom points
        line_segments_for_pile_line_group,    # Pile line segments
        group_names_list                      # Complete group names list
    )

    return safe16_dfs, safe22_dfs


def write_bp_shp_dhp(excel_inputs, safe16_dfs, safe22_dfs):
    """Main function to process complete pile foundation data for SAFE16 and SAFE22 formats.

    Orchestrates the complete pile foundation data processing workflow from Excel inputs to 
    both SAFE16 and SAFE22 finite element model formats. This function serves as the primary 
    interface for pile foundation modeling, handling all aspects of pile geometry, properties, 
    boundary conditions, and soil-structure interaction.

    The function processes three main pile foundation types: Bored Piles (BP), Small Diameter 
    Piles (SHP), and Driven H-Piles (DHP), converting comprehensive foundation design data 
    into analysis-ready finite element models for both SAFE software versions.

    Processing Workflow:
    1. Pile point coordinate generation and management
    2. Pile column element creation with proper connectivity  
    3. Material and section property assignments
    4. Point insertion and coordinate system management
    5. Local axis definition for proper pile orientation
    6. End release conditions for pile-cap connections
    7. End restraint conditions for pile-soil interaction
    8. Soil spring modeling for lateral pile behavior
    9. Group management for organized pile classification
    """
    # Check if any pile foundation data is present for processing
    # Only proceed if at least one pile type contains data
    if not excel_inputs.BP.empty or not excel_inputs.SHP.empty or not excel_inputs.DHP.empty:
        
        # Step 1: Generate pile point coordinates for geometric definition
        safe16_dfs, safe22_dfs = _write_pile_points(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 2: Create pile column elements for structural connectivity
        safe16_dfs, safe22_dfs = _write_pile_column(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 3: Assign material and section properties to pile elements
        safe16_dfs, safe22_dfs = _write_pile_prop_assign(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 4: Configure point insertion for accurate geometric positioning
        safe16_dfs, safe22_dfs = _write_pile_point_insertion(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 5: Define local coordinate systems for proper pile orientation
        safe16_dfs, safe22_dfs = _write_pile_local_axis(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 6: Set end release conditions for pile-cap connection modeling
        safe16_dfs, safe22_dfs = _write_pile_end_release(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 7: Configure end restraint conditions for pile-soil interaction
        safe16_dfs, safe22_dfs = _write_pile_end_restraint(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 8: Implement soil spring modeling for comprehensive soil-structure interaction
        safe16_dfs, safe22_dfs = _write_pile_spring(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Step 9: Create pile groups for organized analysis management
        safe16_dfs, safe22_dfs = _write_pile_group(excel_inputs, safe16_dfs, safe22_dfs)
        
        # Print confirmation message with timestamp indicating successful processing
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f'{now} Transformed soil spring and pile data to SAFE format!')
        
    return safe16_dfs, safe22_dfs
