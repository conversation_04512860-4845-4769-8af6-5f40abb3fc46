# Complete Project Folder Structure

## Root Directory Structure
```
Foundation-Automation/
├── 📄 main.py                    # Application entry point
├── 📄 main_class.py              # Core data classes
├── 📄 app_controller.py          # Main application controller
├── 📄 requirements.txt           # Python dependencies
├── 📄 README.md                  # Project documentation
├── 📄 SECURITY.md               # Security guidelines
├── 📄 AIS.ico                   # Application icon
├── 📄 nuitka embed.txt          # Build configuration
├── 📄 __init__.py               # Package initialization
└── 📁 __pycache__/              # Python cache files
```

## Major Module Directories

### 🔐 Authentication System (`auth/`)
```
auth/
├── 📄 security_manager.py       # Main SecurityManager class
├── 📄 README.md                 # Authentication documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

### ⚙️ Configuration (`config/`)
```
config/
├── 📄 app_config.py             # Central application configuration
├── 📄 ssl_config.py             # SSL/TLS configuration
├── 📄 README.md                 # Configuration documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

### 🎨 User Interface (`ui/`)
```
ui/
├── 📄 components.py             # Base UI components
├── 📄 login_frame.py            # Login interface
├── 📄 main_menu_frame.py        # Main menu interface
├── 📄 version_selection_frame.py # Version selection
├── 📄 README.md                 # UI documentation
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

### 📧 Email Notifications (`email_notifications/`)
```
email_notifications/
├── 📄 notification.py          # Email notification system
├── 📄 __init__.py               # Module initialization
└── 📁 __pycache__/              # Compiled Python files
```

### 📚 Steel Library (`Library_Steel/`)
```
Library_Steel/
├── 📄 SteelSection.xlsx         # Steel section database
└── 📄 __init__.py               # Module initialization
```