# Detailed Security Implementation Analysis

## SecurityManager Class Architecture

### Core Security Features
- **Password Hashing**: SHA-256 with salt for secure password storage
- **Session Tokens**: Cryptographically secure tokens using `secrets.token_hex()`
- **Account Lockout**: Progressive lockout system with both user-specific and system-wide protection
- **Security Logging**: JSON-based security log with comprehensive tracking

### Advanced Google Drive Integration
The `get_authorized_users()` method implements sophisticated user authorization:

#### Multi-URL Support
- Handles different Google Drive URL formats (`/d/` and `id=` parameters)
- Tries multiple download URLs for redundancy
- Implements virus scan warning bypass with confirm tokens

#### Robust HTTP Handling
- SSL configuration with fallback options
- Proxy support through environment variables
- Browser-like User-Agent headers to avoid blocking
- Comprehensive error handling and retry logic

#### Content Processing Intelligence
- JSON array parsing for structured user lists
- Plain text parsing with regex validation
- HTML tag removal for mixed content
- BOM (Byte Order Mark) handling for different encodings
- Username format validation: `^[a-z]+\.[a-z]+(?:\.[a-z]+)?$`

### Security Logging System
- **File-based logging**: JSON format with UTF-8 encoding
- **Automatic recovery**: Reinitializes corrupted logs
- **Comprehensive tracking**: Login attempts, failures, session tokens, user types
- **Timestamp management**: ISO format timestamps for precise tracking

### Lockout Mechanisms
1. **User-specific lockout**: Per-user attempt counting
2. **System-wide lockout**: Global protection after max attempts
3. **Automatic expiry**: Time-based lockout expiration
4. **Reset on success**: Clears failed attempts on successful login

### Session Management
- **Token verification**: Secure session token validation
- **User type tracking**: Distinguishes between Base and Ultimate users
- **Email integration**: Automatic email address construction
- **Cross-session persistence**: Maintains session state across application restarts