from datetime import datetime
from pathlib import Path

from build_fem import build_fem_config as config
from initialization.error_handling import get_error_details
import main_class as main_class


def safe_write_file(file_path, content, mode='w'):
    """
    Safely write content to a file using UTF-8 encoding.
    
    This function prevents encoding issues like 'cp950' codec errors
    when writing files that may contain Unicode characters.
    
    Args:
        file_path (str or Path): Path to the file to write
        content (str): Content to write to the file
        mode (str): File opening mode (default: 'w')
    
    Returns:
        bool: True if successful, False otherwise
    
    Raises:
        Exception: Re-raises any exception that occurs during file writing
    """
    try:
        path_obj = Path(file_path)
        # Ensure the directory exists
        path_obj.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path_obj, mode, encoding='utf-8') as file:
            file.write(content)
        return True
    except Exception as e:
        print(f"Error writing file {file_path}: {e}")
        raise


def init_file_paths(folder_path, log_callback=None):
    """
    Initialize file paths for SAFE model inputs and outputs.

    Args:
        folder_path (str): Path to the folder containing input files.
        log_callback (callable, optional): Function to log messages. If not provided, messages will be printed to the console.

    Returns:
        _main_class.FilePaths: An object containing the initialized file paths for SAFE model inputs and outputs.
                               Returns None if the folder_path is not provided.

    Functionality:
        - Logs the folder location of the SAFE Excel Master Data.
        - Creates an output directory named 'SAFE Model' if it does not exist.
        - Defines and initializes paths for various input and output files used in the SAFE model.
    """
    try:
        # Initialize file paths object
        file_paths = main_class.FilePaths()
        base_path = Path(folder_path)

        # Log or print the folder location
        message = f'Folder Location of SAFE Excel Master Data = {base_path}'
        log_callback(message) if log_callback else print(message)

        # Return None if folder_path is not provided
        if not folder_path:
            error_msg = "Error: No folder path provided"
            if log_callback:
                log_callback(error_msg)
            return None

        # Check if base path exists
        if not base_path.exists():
            error_msg = f"Error: The specified folder does not exist: {base_path}"
            if log_callback:
                log_callback(error_msg)
            raise FileNotFoundError(error_msg)

        # Create output directory if it doesn't exist
        output_dir = base_path / 'SAFE Model'
        try:
            if not output_dir.exists():
                output_dir.mkdir(parents=True, exist_ok=True)
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                message = f'{now} The new directory ({output_dir}) is created!\n'
                log_callback(message) if log_callback else print(message)
        except PermissionError as e:
            error_msg = f"Error: Permission denied when creating output directory: {output_dir}"
            if log_callback:
                log_callback(error_msg)
            raise PermissionError(error_msg) from e        # Define file paths
        file_paths.ExcelProperty = str(base_path / config.EXCEL_PROPERTY_FILENAME)
        file_paths.ExcelGeometry = str(base_path / config.EXCEL_GEOMETRY_FILENAME)
        file_paths.ExcelGeology = str(base_path / config.EXCEL_GEOLOGY_FILENAME)
        file_paths.ExcelLoading = str(base_path / config.EXCEL_LOADING_FILENAME)

        file_paths.ExcelSAFE16Model = str(output_dir / config.EXCEL_SAFE16_MODEL_FILENAME)
        file_paths.f2kSAFE16Model = str(output_dir / config.F2K_SAFE16_MODEL_FILENAME)

        file_paths.ExcelSAFE22Model = str(output_dir / config.EXCEL_SAFE22_MODEL_FILENAME)
        file_paths.f2kSAFE22Model = str(output_dir / config.F2K_SAFE22_MODEL_FILENAME)

        file_paths.ExcelOutputMP = str(base_path / config.EXCEL_OUTPUT_MP_FILENAME)
        file_paths.ExcelOutputLoading = str(base_path / config.EXCEL_OUTPUT_LOADING_FILENAME)
        file_paths.Log = str(base_path / config.RPA_LOG)

        return file_paths

    except Exception as e:
        error_details = get_error_details(e, 'init_file_paths')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
        raise


def init_existing_file_paths(file_paths, folder_path, log_callback=None):
    try:
        base_path = Path(folder_path)

        # Log or print the folder location
        message = f'Folder Location of Existing SAFE Excel Master Data = {base_path}'
        log_callback(message) if log_callback else print(message)

        # Return None if folder_path is not provided
        if not folder_path:
            error_msg = "Error: No existing folder path provided"
            if log_callback:
                log_callback(error_msg)
            return None

        # Check if base path exists
        if not base_path.exists():
            error_msg = f"Error: The specified existing folder does not exist: {base_path}"
            if log_callback:
                log_callback(error_msg)
            raise FileNotFoundError(error_msg)        # Define file paths
        file_paths.ExistingExcelProperty = str(
            base_path / config.EXCEL_PROPERTY_FILENAME)
        file_paths.ExistingExcelGeometry = str(
            base_path / config.EXCEL_GEOMETRY_FILENAME)
        file_paths.ExistingExcelGeology = str(
            base_path / config.EXCEL_GEOLOGY_FILENAME)
        file_paths.ExistingExcelLoading = str(
            base_path / config.EXCEL_LOADING_FILENAME)

        return file_paths

    except Exception as e:
        error_details = get_error_details(e, 'init_existing_file_paths')
        error_msg = (f"Error in {error_details['function']}: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Location: {error_details['location']}")
        if log_callback:
            log_callback(error_msg)
        raise
