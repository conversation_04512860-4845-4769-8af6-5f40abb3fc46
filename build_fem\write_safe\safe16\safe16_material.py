"""Convert material property definitions from Excel to SAFE16 format.

Processes concrete, steel, rebar, and tendon materials with proper strength values
and default assignments for SAFE16 reinforced concrete design."""

import pandas as pd


def _write_general_material_properties_safe16(excel_inputs, safe16_dfs):
    """Consolidate material definitions from all types into unified general properties table."""
    # Create copies of input DataFrames to avoid modifying originals
    df_concrete = excel_inputs.Concrete.copy()
    df_steel = excel_inputs.Steel.copy()
    df_rebar = excel_inputs.Rebar.copy()
    df_tendon = excel_inputs.Tendon.copy()

    # Consolidate material identification from all material types
    # Extract only the essential identification columns
    titles = ['Material', 'Type']
    df_material = pd.concat(
        [df_concrete[titles],
         df_steel[titles],
         df_rebar[titles],
         df_tendon[titles]
         ],
        ignore_index=True
    )

    # Assign default display properties for SAFE16 interface
    df_material['Color'] = 'Green'  # Default color for all materials in SAFE16 display
    df_material['None'] = None      # Placeholder column for SAFE16 format compatibility

    # Convert to SAFE16 format with proper MultiIndex column structure
    # Get column structure from target DataFrame to ensure compatibility
    target_columns_16 = safe16_dfs.MaterialProp01General.columns.tolist()

    # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
    df_16 = pd.DataFrame(df_material.values, columns=pd.MultiIndex.from_tuples(target_columns_16))

    # Append consolidated material properties to existing SAFE16 data
    safe16_dfs.MaterialProp01General = pd.concat(
        [safe16_dfs.MaterialProp01General, df_16],
        ignore_index=True
    )
    return safe16_dfs


def _write_steel_materials_safe16(excel_inputs, safe16_dfs):
    """Convert structural steel material properties from Excel to SAFE16 format."""
    df_steel = excel_inputs.Steel.copy()

    if not df_steel.empty:
        # Extract required columns for SAFE16 steel material definition
        # These properties are essential for structural steel design
        steel_props_16 = df_steel[[
            'Material',      # Steel material identifier
            'E (kN/m2)',     # Elastic modulus (Young's modulus)
            'U (Unitless)',  # Poisson's ratio
            'A (1/C)',       # Coefficient of thermal expansion
            'UnitWt (kN/m3)', # Unit weight for self-weight calculation
            'Fy (kN/m2)',    # Yield strength for design
            'Fu (kN/m2)'     # Ultimate tensile strength for design
        ]].copy()

        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns_16 = safe16_dfs.MaterialProp02Steel.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_16 = pd.DataFrame(
            steel_props_16.values,
            columns=pd.MultiIndex.from_tuples(target_columns_16)
        )

        # Append steel material properties to existing SAFE16 data
        safe16_dfs.MaterialProp02Steel = pd.concat(
            [safe16_dfs.MaterialProp02Steel, df_16],
            ignore_index=True
        )
    return safe16_dfs


def _write_concrete_materials_safe16(excel_inputs, safe16_dfs):
    """Process concrete material properties for SAFE16.
    
    Converts concrete material definitions from Excel input to SAFE16 format.
    Concrete materials are the primary structural material for slabs, beams, and
    foundations. This function handles concrete-specific properties including
    compressive strength and lightweight concrete classification.
    """
    df_concrete = excel_inputs.Concrete.copy()
    
    # Set default lightweight concrete flag to 'No' for normal weight concrete
    # This flag affects design calculations in SAFE16 (shear, development length, etc.)
    df_concrete['LtWtConc (Yes/No)'] = 'No'  # Default to normal weight concrete

    if not df_concrete.empty:
        # Extract required columns for SAFE16 concrete material definition
        # These properties are essential for concrete design and analysis
        concrete_props_16 = df_concrete[[
            'Material',         # Concrete material identifier
            'E (kN/m2)',        # Elastic modulus
            'U (Unitless)',     # Poisson's ratio
            'A (1/C)',          # Coefficient of thermal expansion
            'UnitWt (kN/m3)',   # Unit weight for self-weight calculation
            'Fc (kN/m2)',       # Compressive strength (primary design parameter)
            'LtWtConc (Yes/No)' # Lightweight concrete flag
        ]].copy()

        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns_16 = safe16_dfs.MaterialProp03Concrete.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_16 = pd.DataFrame(
            concrete_props_16.values,
            columns=pd.MultiIndex.from_tuples(target_columns_16)
        )

        # Append concrete material properties to existing SAFE16 data
        safe16_dfs.MaterialProp03Concrete = pd.concat(
            [safe16_dfs.MaterialProp03Concrete, df_16],
            ignore_index=True
        )
    return safe16_dfs


def _write_rebar_materials_safe16(excel_inputs, safe16_dfs):
    """Process reinforcement bar (rebar) material properties for SAFE16.
    
    Converts reinforcement steel material definitions from Excel input to SAFE16 format.
    Rebar materials are used for reinforced concrete design including flexural and
    shear reinforcement. This function handles rebar-specific properties for
    reinforcement design calculations.
    """
    df_rebar = excel_inputs.Rebar.copy()

    if not df_rebar.empty:
        # Extract required columns for SAFE16 rebar material definition
        # These properties are essential for reinforcement design
        rebar_props_16 = df_rebar[[
            'Material',      # Rebar material identifier
            'E (kN/m2)',     # Elastic modulus
            'UnitWt (kN/m3)', # Unit weight for reinforcement weight calculation
            'Fy (kN/m2)',    # Yield strength (primary design parameter)
            'Fu (kN/m2)'     # Ultimate tensile strength
        ]].copy()

        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns_16 = safe16_dfs.MaterialProp04Rebar.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_16 = pd.DataFrame(
            rebar_props_16.values,
            columns=pd.MultiIndex.from_tuples(target_columns_16)
        )

        # Append rebar material properties to existing SAFE16 data
        safe16_dfs.MaterialProp04Rebar = pd.concat(
            [safe16_dfs.MaterialProp04Rebar, df_16],
            ignore_index=True
        )
    return safe16_dfs


def _write_tendon_materials_safe16(excel_inputs, safe16_dfs):
    """Process post-tensioning tendon material properties for SAFE16.
    
    Converts post-tensioning tendon material definitions from Excel input to SAFE16 format.
    Tendon materials are high-strength steel used for post-tensioned concrete structures.
    This function handles tendon-specific properties for post-tensioning design.
    """
    # Note: Function uses excel_inputs.Material (not excel_inputs.Tendon)
    # This may be a data structure convention for tendon materials
    df_tendon = excel_inputs.Material.copy()

    if not df_tendon.empty:
        # Extract required columns for SAFE16 tendon material definition
        # These properties are essential for post-tensioning design
        tendon_props_16 = df_tendon[[
            'Material',      # Tendon material identifier
            'E (kN/m2)',     # Elastic modulus
            'UnitWt (kN/m3)', # Unit weight
            'Fy (kN/m2)',    # Yield strength
            'Fu (kN/m2)'     # Ultimate tensile strength (primary design parameter)
        ]].copy()

        # Convert to SAFE16 format with proper MultiIndex column structure
        # Get column structure from target DataFrame to ensure compatibility
        target_columns_16 = safe16_dfs.MaterialProp05Tendon.columns.tolist()

        # Create new DataFrame with appropriate SAFE16 MultiIndex column structure
        df_16 = pd.DataFrame(
            tendon_props_16.values,
            columns=pd.MultiIndex.from_tuples(target_columns_16)
        )

        # Append tendon material properties to existing SAFE16 data
        safe16_dfs.MaterialProp05Tendon = pd.concat(
            [safe16_dfs.MaterialProp05Tendon, df_16],
            ignore_index=True
        )
    return safe16_dfs
