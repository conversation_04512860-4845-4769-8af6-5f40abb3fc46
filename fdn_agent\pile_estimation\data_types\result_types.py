﻿"""
Result type definitions for pile estimation operations
"""

from typing import List, Any, TypedDict, Dict, Optional
from dataclasses import dataclass, field
from .basic_types import Point2D
from .element_types import GroupElements
from .pile_types import PileLocation, PileTypeSpec

class LoadData(TypedDict):
    """Load information for structural elements."""
    axial: float  # kN
    moment_x: float  # kNm
    moment_y: float  # kNm
    shear_x: float  # kN
    shear_y: float  # kN

class PileCapResult(TypedDict):
    """Results for a pile cap creation operation."""
    polygon: Any  # Shapely Polygon
    local_system: Any  # LocalCoordinateSystem or None
    is_valid: bool
    warnings: List[str]
    errors: List[str]

@dataclass
class PileGroupResult:
    """Results for a single pile group."""
    group_name: str  # Using group_name to match existing code
    elements: Any  # GroupElements or similar
    total_load_kn: float
    load_centroid: Optional[Point2D]
    load_details: Dict[str, Any]
    pile_cap_polygon: Any  # Shapely Polygon
    pile_locations: List[PileLocation]
    num_piles: int
    pile_capacity_kn: float
    utilization_ratio: float
    warnings: List[str] = field(default_factory=list)
    sub_clusters: Dict[str, Any] = field(default_factory=dict)
    pile_distribution: Dict[str, Any] = field(default_factory=dict)
    
    # Optional optimization fields
    selected_pile_spec: Optional[PileTypeSpec] = None
    optimization_details: Optional[Dict[str, Any]] = None

PileEstimationResult = Dict[str, Any]

