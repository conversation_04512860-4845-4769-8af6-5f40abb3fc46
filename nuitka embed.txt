# Enhanced command with full scientific library support and all requirements.txt packages
# Fixed sklearn FFT missing error - Nuitka now auto-enables numpy/matplotlib/anti-bloat plugins
nuitka --msvc=latest --mode=standalone --enable-plugin=tk-inter --include-package=scipy.fft --include-package=numpy.fft --include-package=sklearn --include-package-data=sklearn --windows-console-mode=disable --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/VSCodeProjects/Foundation-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/VSCodeProjects/Foundation-Automation/AIS.ico=AIS.ico --windows-company-name="<PERSON>ze" --windows-file-description="Foundation Design RPA" --windows-product-version="6.1" main.py

nuitka --msvc=latest --mode=standalone --enable-plugin=tk-inter --include-package=scipy.fft --include-package=numpy.fft --include-package=sklearn --include-package-data=sklearn --windows-console-mode=disable --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/CursorProjects/Foundation-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/CursorProjects/Foundation-Automation/AIS.ico=AIS.ico --windows-company-name="Alex Sze" --windows-file-description="Foundation Design RPA" --windows-product-version="6.1" main.py