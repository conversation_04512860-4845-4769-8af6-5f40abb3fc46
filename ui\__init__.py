"""User interface module for the Foundation Automation application."""

# Import UI components with error handling
try:
    from .components import (
        <PERSON><PERSON>ram<PERSON>, <PERSON>er<PERSON>ram<PERSON>, TimerLabel,
        create_menu_bar, setup_window_icon, show_about
    )
except ImportError:
    BaseFrame = None
    HeaderFrame = None
    TimerLabel = None
    create_menu_bar = None
    setup_window_icon = None
    show_about = None

try:
    from .login_frame import LoginFrame
except ImportError:
    LoginFrame = None

try:
    from .main_menu_frame import MainMenuFrame
except ImportError:
    MainMenuFrame = None

try:
    from .version_selection_frame import VersionSelectionFrame
except ImportError:
    VersionSelectionFrame = None

__all__ = [
    'BaseFrame',
    'HeaderFrame', 
    'TimerLabel',
    'create_menu_bar',
    'setup_window_icon',
    'show_about',
    'LoginFrame',
    'MainMenuFrame',
    'VersionSelectionFrame'
]
