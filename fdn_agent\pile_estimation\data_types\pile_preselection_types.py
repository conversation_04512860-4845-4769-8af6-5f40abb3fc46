﻿"""
Pile Type Pre-Selection Type Definitions

This module contains all data classes, enums and type definitions used in
the pile type pre-selection process.
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from shapely.geometry import Polygon

from .pile_types import PileType
from .basic_types import Point2D
from .element_types import GroupElements
from .config_types import LocalCoordinateSystem


@dataclass
class PileTypePreselectionCriteria:
    """Criteria for pile type pre-selection"""
    required_total_load: float  # kN
    group_elements: GroupElements
    excel_inputs: Any
    user_edge_distance: float  # m (user-defined edge distance from GUI)
    site_boundary: Optional[Polygon] = None
    safety_factor: float = 1.2
    # Note: enlargement_offset removed - now using pile diameter-based maximum pile cap generation


@dataclass
class PileTypeCandidate:
    """Represents a pile type candidate with its specifications"""
    pile_type: PileType
    capacity_per_pile: float  # kN
    min_spacing: float  # m
    section: str
    diameter: Optional[float] = None  # m (for BP, None for H-piles)
    
    @property
    def display_name(self) -> str:
        """Human-readable pile type name"""
        names = {
            PileType.DHP: "Driven H-Pile",
            PileType.SHP: "Socket H-Pile", 
            PileType.BP: "Bored Pile"
        }
        return names[self.pile_type]


@dataclass
class PileTypePreselectionResult:
    """Result of pile type pre-selection process"""
    selected_pile_type: PileTypeCandidate
    viable_grid_positions: List[Point2D]
    total_possible_piles: int
    total_capacity: float  # kN
    utilization_ratio: float
    initial_pile_cap: Polygon
    enlarged_pile_cap: Polygon  # Maximum pile cap for the selected pile type (Initial + pile_diameter offset)
    local_coordinate_system: LocalCoordinateSystem
    evaluation_summary: Dict[str, Any]
    all_pile_type_positions: Dict[str, List[Point2D]]  # All pile positions for each type
    all_possible_pile_boundaries: Dict[str, Polygon]  # Possible pile boundaries for each type
    preselection_dxf_path: Optional[str] = None  # Path to comprehensive DXF file

