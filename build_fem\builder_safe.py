"""SAFE Model Builder Module for SAFE16/SAFE22 Structural Analysis

This module provides the main orchestration functionality for generating complete SAFE16 
and SAFE22 finite element models from Excel input data. It serves as the central 
coordinator for the entire model generation workflow, managing the transformation of 
structural data through all processing stages with comprehensive error handling and 
progress tracking.

The module implements a robust, multi-step process that converts Excel-based structural 
input data into complete SAFE16 and SAFE22 finite element models ready for structural 
analysis and design. It handles all aspects of model generation including geometry, 
materials, properties, loads, and export operations.

Key Functionality:
- Complete SAFE16/SAFE22 model generation from Excel inputs
- Comprehensive error handling with graceful degradation
- Real-time progress tracking and user feedback
- Conditional processing based on available input data
- Dual-format export (Excel and F2K) for both SAFE versions
- Robust validation and error recovery mechanisms

Model Generation Workflow:
The module implements a 32-step process that transforms structural data through the 
following major phases:
1. **Load Definition**: Patterns, cases, and combinations processing
2. **Material Properties**: Concrete, steel, and other material definitions
3. **Structural Properties**: Beam, column, and slab property definitions
4. **Geometry Assignment**: Point, line, and area element assignments
5. **Load Application**: Various load types and loading conditions
6. **Design Integration**: Design strips and analysis parameters
7. **Model Export**: Multiple format exports for analysis software

Structural Components Processed:
- **Load Systems**: Patterns, cases, combinations, and load applications
- **Material Systems**: Concrete, steel, rebar, and soil properties
- **Structural Elements**: Beams, columns, slabs, walls, and foundations
- **Geometry Systems**: Points, lines, areas, and coordinate systems
- **Foundation Systems**: Piles, soil springs, and foundation elements
- **Design Systems**: Design strips, reinforcement, and code compliance

Error Handling Features:
- Individual step error isolation preventing cascade failures
- Comprehensive error logging with detailed diagnostic information
- Graceful degradation allowing partial model generation
- Failed step tracking and reporting for troubleshooting
- Full traceback capture for debugging complex issues

Progress Tracking System:
- Real-time progress updates with percentage completion
- Step-by-step progress descriptions for user feedback
- Callback-based progress reporting for GUI integration
- Completion status tracking for all processing steps

Export Capabilities:
- **SAFE16 Excel**: Comprehensive Excel format for SAFE16 software
- **SAFE22 Excel**: Enhanced Excel format for SAFE22 software  
- **SAFE16 F2K**: Text-based F2K format for SAFE16 import
- **Output Loading**: Load verification and reporting data

Performance Considerations:
- Efficient processing of large structural models with thousands of elements
- Memory-optimized data structures for complex building geometries
- Parallel processing capabilities where applicable
- Optimized DataFrame operations for large-scale data transformations

Quality Assurance Features:
- Comprehensive validation at each processing step
- Data consistency checking across all model components
- Automatic error detection and reporting
- Model completeness verification before export

Dependencies:
- build_fem.write_safe: Core SAFE processing modules
- build_fem.functions: Utility functions for model operations
- initialization.error_handling: Advanced error handling and diagnostics
- pandas: DataFrame operations and data manipulation
- openpyxl: Excel file operations and formatting

Usage:
    from build_fem.builder_safe import gen_safe
    
    # Generate complete SAFE model with progress tracking
    safe16_dfs = gen_safe(
        file_paths=file_paths,
        excel_inputs=excel_inputs, 
        excel_outputs=excel_outputs,
        safe16_dfs=safe16_dfs,
        safe22_dfs=safe22_dfs,
        option_design_strip=1,
        log_callback=log_function,
        progress_callback=progress_function
    )

Authors: <AUTHORS>
Version: 5.6.9
Last Modified: 2024
"""

# Core processing modules - use __init__.py imports where possible
from build_fem.write_safe import write_load           # Load processing functions
from build_fem import functions as _functions         # Utility functions for model operations
from build_fem.write_safe import write_geometry as _write_assign  # Geometry assignment functions

# SAFE processing functions - imported via __init__.py for clean namespace management
from build_fem.write_safe import (
    write_bp_shp_dhp,          # Pile foundation and soil spring processing
    write_beam_prop,           # Beam property definitions and processing
    write_column_prop,         # Column property definitions and processing
    write_slab_prop,           # Slab property definitions and processing
    write_load_pattern,        # Load pattern definitions and processing
    write_load_case,           # Load case definitions and processing
    write_load_combination,    # Load combination definitions and processing
    write_design_strip,        # Design strip generation for slab design
    write_material,            # Material property definitions and processing
    write_soil_prop            # Soil property definitions and processing
)

# Export functions - SAFE22 and SAFE16 format exports
from build_fem.write_safe.safe22 import export_safe22_f2k, export_safe22_excel  # SAFE22 exports
from build_fem.write_safe.safe16 import export_safe16_f2k, export_safe16_excel  # SAFE16 exports

# Error handling and diagnostics
from initialization.error_handling import get_error_details  # Advanced error diagnostic functions

def gen_safe(file_paths, excel_inputs, excel_outputs, safe16_dfs, safe22_dfs, option_design_strip=1, log_callback=None,
             progress_callback=None):
    """Generate complete SAFE16 and SAFE22 finite element models from Excel input data.

    This is the main orchestration function that coordinates the entire SAFE model generation 
    workflow. It processes Excel-based structural input data through a comprehensive 32-step 
    transformation process, converting it into complete SAFE16 and SAFE22 finite element 
    models ready for structural analysis and design.

    The function implements robust error handling with graceful degradation, allowing partial 
    model generation even when individual processing steps fail. It provides real-time 
    progress tracking and comprehensive logging for user feedback and debugging purposes.
    """
    # Initialize progress tracking and error handling
    total_steps = 32  # Total number of processing steps for accurate progress calculation
    current_step = 0  # Current step counter for progress tracking
    failed_steps = []  # List to track failed steps for comprehensive error reporting

    def update_progress(step_description=""):
        """Update progress tracking and notify callback if provided.
        
        Increments the current step counter and calculates progress percentage,
        then calls the progress callback with current status information.
        """
        nonlocal current_step
        current_step += 1
        if progress_callback:
            # Calculate progress percentage, ensuring it doesn't exceed 100%
            progress_percent = min(100, int(current_step / total_steps * 100))
            progress_callback(progress_percent, step_description)

    def safe_execute_step(step_func, step_name, *args, **kwargs):
        """Execute a processing step with comprehensive error handling and logging.
        
        Wraps individual processing steps with error handling, logging, and progress 
        tracking. Provides isolation between steps to prevent cascade failures while 
        maintaining detailed error reporting for debugging purposes.
        """
        try:
            # Log step initiation if logging callback is provided
            if log_callback:
                log_callback(f"{step_name}...")
            
            # Execute the processing step with provided arguments
            result = step_func(*args, **kwargs)
            
            # Update progress tracking with completion status
            update_progress(f"{step_name} complete")
            return result
            
        except Exception as e:
            # Capture comprehensive error diagnostics for debugging
            error_details = get_error_details(e, step_name)
            error_msg = (f"Error in {step_name}: {error_details['error_type']}\n"
                        f"Message: {error_details['error_message']}\n"
                        f"Location: {error_details['location']}")
            
            # Log detailed error information if logging callback is provided
            if log_callback:
                log_callback(error_msg)
            
            # Track failed step for final reporting
            failed_steps.append(step_name)
            
            # Re-raise exception to allow calling code to handle as needed
            raise

    try:
        # ============================================================================
        # PHASE 1: LOAD SYSTEM PROCESSING (Steps 1-3)
        # Process load patterns, cases, and combinations for structural analysis
        # ============================================================================
        
        # Step 1: Process load patterns (DL, LL, WL, EQ, etc.)
        # Load patterns define basic load types and serve as building blocks for combinations
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_load_pattern,
            "Transforming load pattern data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 2: Process load cases with scale factors and pattern associations
        # Load cases combine patterns with appropriate scaling for specific scenarios
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_load_case,
            "Transforming load case data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 3: Process load combinations for ultimate and serviceability design
        # Load combinations define design-level loading per structural codes
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_load_combination,
            "Transforming load combination data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # ============================================================================
        # PHASE 2: MATERIAL AND PROPERTY PROCESSING (Steps 4-8)
        # Process material properties and structural element section properties
        # ============================================================================
        
        # Step 4: Process material properties (concrete, steel, rebar, etc.)
        # Material definitions provide fundamental properties for structural elements
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_material,
            "Transforming material data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 5: Process soil properties for foundation analysis
        # Soil properties define geotechnical parameters for foundation design
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_soil_prop,
            "Transforming soil property data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 6: Process beam section properties and geometric definitions
        # Beam properties define cross-sectional characteristics for linear elements
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_beam_prop,
            "Transforming beam property data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 7: Process column section properties and geometric definitions
        # Column properties define cross-sectional characteristics for vertical elements
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_column_prop,
            "Transforming column property data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 8: Process slab section properties and thickness definitions
        # Slab properties define thickness and material characteristics for area elements
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_slab_prop,
            "Transforming slab property data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # ============================================================================
        # PHASE 3: GEOMETRY AND ASSIGNMENT PROCESSING (Steps 9-18)
        # Process geometric definitions and property assignments for all elements
        # ============================================================================
        
        # Step 9: Process point coordinate definitions and restraint assignments
        # Point assignments define nodal coordinates and boundary conditions
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_point_assign,
            "Transforming point assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 10: Process beam element connectivity and property assignments
        # Beam assignments link geometry to section properties and materials
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_beam_assign,
            "Transforming beam assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 11: Process slab area definitions and property assignments
        # Slab assignments define area elements with thickness and material properties
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_slab_assign,
            "Transforming slab assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 12: Process opening definitions within slab areas
        # Opening assignments create voids in slab elements for stairs, elevators, etc.
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_opening_assign,
            "Transforming opening assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 13: Process Loading Key Plan (LKP) area assignments
        # LKP assignments apply standardized loading patterns to defined areas
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_lkp_assign,
            "Transforming lkp assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 14: Process column area definitions and property assignments
        # Column assignments define vertical elements with cross-sectional properties
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_column_assign,
            "Transforming column area assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 15: Process wall area definitions and property assignments
        # Wall assignments define planar structural elements for lateral resistance
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_wall_assign,
            "Transforming wall assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 16: Process line load application assignments
        # Line load assignments apply distributed loads to linear structural elements
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_line_load_assign,
            "Transforming line load assign data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # Step 17: Process slab group definitions for organized analysis
        # Slab groups organize related slab areas for design and analysis purposes
        safe16_dfs, safe22_dfs = safe_execute_step(
            _write_assign.write_slab_group,
            "Transforming slab group data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # ============================================================================
        # PHASE 4: FOUNDATION AND PILE PROCESSING (Step 18)
        # Process foundation elements including piles and soil springs
        # ============================================================================
        
        # Step 18: Process pile foundation and soil spring definitions
        # Foundation processing includes bored piles, driven piles, and soil-structure interaction
        safe16_dfs, safe22_dfs = safe_execute_step(
            write_bp_shp_dhp,
            "Transforming soil spring and pile data to SAFE format",
            excel_inputs, safe16_dfs, safe22_dfs
        )

        # ============================================================================
        # PHASE 5: DESIGN INTEGRATION (Step 19 - Optional)
        # Generate design strips for reinforced concrete slab design
        # ============================================================================
        
        # Step 19: Generate design strips for slab reinforcement design (optional)
        # Design strips create orthogonal strips for X and Y direction reinforcement design
        if option_design_strip == 1:
            safe16_dfs, safe22_dfs = safe_execute_step(
                write_design_strip,
                "Generating design strip to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs
            )

        # ============================================================================
        # PHASE 6: LOAD APPLICATION PROCESSING (Steps 20-28 - Conditional)
        # Process various load types based on available input data
        # ============================================================================
        # Step 20: Process point loads (concentrated forces and moments) - conditional
        # Point loads represent equipment loads, column reactions, and discrete applied forces
        if not excel_inputs.InputLoadPoint.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_point_load,
                "Transforming point load schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # Step 21: Process line loads (distributed loads along elements) - conditional
        # Line loads represent wall loads on beams, uniform loads, and varying distributions
        if not excel_inputs.InputLoadLine.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_line_load,
                "Transforming line load schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # Step 22: Process pile loads (foundation loading conditions) - conditional
        # Pile loads represent axial loads, lateral loads, and moments on foundation elements
        if not excel_inputs.InputLoadPile.empty:
            safe16_dfs, safe22_dfs = safe_execute_step(
                write_load.write_pile_load,
                "Transforming pile input loading schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs
            )

        # Step 23: Process slab loads (area-distributed loads) - conditional
        # Slab loads represent dead loads, live loads, and area-distributed forces on slabs
        if not excel_inputs.InputLoadSlab.empty:
            safe16_dfs, safe22_dfs = safe_execute_step(
                write_load.write_slab_load,
                "Transforming slab input loading schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs
            )

        # Step 24: Process Loading Key Plan (LKP) loads - conditional
        # LKP loads represent standardized loading patterns for consistent analysis
        if not excel_inputs.InputLoadLKP.empty:
            safe16_dfs, safe22_dfs = safe_execute_step(
                write_load.write_lkp_load,
                "Transforming Loading Key Plan to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs
            )

        # Step 25: Process beam loads (loads applied directly to beams) - conditional
        # Beam loads represent distributed loads, point loads, and moments on beam elements
        if not excel_inputs.InputLoadBeam.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_beam_load,
                "Transforming beam load schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # Step 26: Process column loads (axial and lateral loads on columns) - conditional
        # Column loads represent vertical loads, lateral forces, and moments on column elements
        if not excel_inputs.InputLoadColumn.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_column_load,
                "Transforming column input loading schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # Step 27: Process wall loads (in-plane and out-of-plane forces) - conditional
        # Wall loads represent shear forces, pressures, and distributed loads on wall elements
        if not excel_inputs.InputLoadWall.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_wall_load,
                "Transforming wall input loading schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # Step 28: Process core wall loads (lateral force-resisting system loads) - conditional
        # Core wall loads represent wind and seismic forces on lateral force-resisting systems
        if not excel_inputs.InputLoadCoreWall.empty:
            safe16_dfs, safe22_dfs, excel_outputs = safe_execute_step(
                write_load.write_corewall_load,
                "Transforming corewall input loading schedule to SAFE format",
                excel_inputs, safe16_dfs, safe22_dfs, excel_outputs
            )

        # ============================================================================
        # PHASE 7: MODEL EXPORT OPERATIONS (Steps 29-32)
        # Export complete models in multiple formats for analysis software
        # ============================================================================
        
        # Step 29: Export SAFE16 Excel format for comprehensive model review
        # Excel format provides user-friendly interface for data verification and editing
        safe_execute_step(
            export_safe16_excel,
            f"Exporting SAFE V16 Excel to: {file_paths.ExcelSAFE16Model}",
            file_paths, safe16_dfs
        )

        # Step 30: Export SAFE22 Excel format with enhanced features
        # SAFE22 Excel includes additional capabilities and improved formatting
        safe_execute_step(
            export_safe22_excel,
            f"Exporting SAFE V22 Excel to: {file_paths.ExcelSAFE22Model}",
            file_paths, safe22_dfs
        )

        # Step 31: Export SAFE16 F2K format for direct software import
        # F2K format provides text-based model definition for SAFE16 analysis
        safe_execute_step(
            export_safe16_f2k,
            f"Exporting SAFE V16 F2K to: {file_paths.f2kSAFE16Model}",
            file_paths, safe16_dfs
        )

        # Step 32: Export load verification and reporting data
        # Output loading provides comprehensive load verification and documentation
        safe_execute_step(
            _functions.export_output_loading,
            f"Exporting output loading to: {file_paths.ExcelOutputLoading}",
            file_paths, excel_outputs
        )

        # ============================================================================
        # COMPLETION AND FINAL REPORTING
        # Finalize progress tracking and provide completion status
        # ============================================================================
        
        # Ensure progress reaches 100% completion
        if progress_callback:
            progress_callback(100, "SAFE model generation complete")

        # Report any failed steps for user awareness and troubleshooting
        if failed_steps and log_callback:
            log_callback(f"WARNING: The following steps failed: {', '.join(failed_steps)}")

        # Return completed SAFE16 model data for further processing if needed
        return safe16_dfs

    except Exception as e:
        # ============================================================================
        # CRITICAL ERROR HANDLING
        # Handle catastrophic errors that prevent model generation completion
        # ============================================================================
        
        # Capture comprehensive error diagnostics for debugging
        error_details = get_error_details(e, 'gen_safe')
        error_msg = (f"Critical error in gen_safe: {error_details['error_type']}\n"
                    f"Message: {error_details['error_message']}\n"
                    f"Failed steps: {', '.join(failed_steps)}")
        
        # Log detailed error information if logging callback is available
        if log_callback:
            log_callback(error_msg)
            log_callback("Full traceback:")
            log_callback(error_details['full_traceback'])
        
        # Re-raise exception to allow calling code to handle appropriately
        raise
