# 3D Pile Volume Analysis Program Specification

## Executive Summary

This specification outlines the development of a comprehensive 3D volume and geometry analysis program for foundation engineering applications. The program will analyze pile systems with soil cylinder, soil frustum and rock frustum interaction volumes, handle overlapping geometries, and provide detailed volume calculations for engineering analysis.

## 1. Requirements Analysis

### 1.1 Input Data Requirements
The data in pandas dataframe "excel_input.Pile" shall be concatenated from "execel_input.BP", "excel_input.SHP", "excel_input.DHP" & "excel_input.MP".
The program shall accept the following data fields in column for each pile:
- **Pile Mark**: Unique identifier for each pile
- **Target Stratum**: Material type indicator (contains "Soil" or "Rock")
- **X (m)**: X-coordinate of pile center
- **Y (m)**: Y-coordinate of pile center
- **Pile Shaft Diameter (m)**: Diameter of the pile shaft
- **Pile Cap Bottom Level (mPD)**: Bottom elevation of pile cap (mPD = meters above Principal Datum)
- **Target Level (mPD)**: Target stratum elevation
- **Founding Level (mPD)**: Final pile foundation elevation

### 1.2 Site Boundary Requirements
The program shall accept site boundary data in "excel_input.SiteBoundary":
- **Site Boundary**: Collection of X,Y coordinate points forming a closed polyline
- **Boundary Format**: List of (X, Y) coordinate pairs in meters
- **Closure**: First and last points should form a closed polygon (automatically closed if not)
- **Volume Clipping**: All pile volumes extending outside the site boundary shall be clipped and excluded from calculations

### 1.3 Functional Requirements
1. **Geometric Model Creation**: Generate 3D geometric models for each pile system
2. **Site Boundary Clipping**: Clip all volumes to remain within the defined site boundary
3. **Volume Calculation**: Calculate volumes for all geometric components within the site boundary
4. **Overlap Detection**: Identify and quantify overlapping volumes between piles
5. **Volume Distribution**: Proportionally distribute overlapping volumes among contributing piles
6. **Traceability**: Maintain detailed calculation traces and audit trails
7. **Visualization**: Provide 3D visualization of results including site boundary
8. **Reporting**: Generate comprehensive reports with volume breakdowns

### 1.4 Performance Requirements
- Support for 1000+ piles in a single analysis
- Real-time visualization for all piles

## 2. Data Model and Input Specifications

### 2.1 Data Structure
Use pandas dataframe "excel_input.Pile" for pile data and "excel_input.SiteBoundary" for site boundary data.

### 2.2 Input Validation
- Coordinates must be valid floating-point numbers
- Diameter must be positive
- Elevations must satisfy: founding_level ≤ target_level ≤ pile_cap_bottom
- Target stratum must contain either "Soil" or "Rock"
- Pile marks must be unique
- Site boundary must contain at least 3 points
- Site boundary points must form a valid, non-self-intersecting polygon
- All pile centers should ideally be within or near the site boundary

## 3. Geometric Model Design

### 3.1 Part 1: Pile Cylinder
- **Geometry**: Circular cylinder
- **Location**: Centered at (X, Y)
- **Diameter**: Pile Shaft Diameter
- **Height**: From Pile Cap Bottom Level to Founding Level
- **Purpose**: Voiding agent for soil volumes

### 3.2 Part 2: Trimmed Soil/Rock Frustum
- **Geometry**: Truncated cone (frustum) with cylindrical void of Part 1
- **Base Level**: Founding Level
- **Top Level**: Target Level
- **Base Diameter**: Pile Shaft Diameter
- **Top Diameter**: Calculated using projection angle
- **Projection Angles**:
  - "Target Stratum" contains "Soil": 15 degrees from vertical
  - "Target Stratum" contains "Rock": 30 degrees from vertical
- **Calculation**:
  ```
  height = target_level - founding_level
  radius_increase = height * tan(angle)
  top_diameter = base_diameter + 2 * radius_increase
  ```
- **Void**: Part 1 pile cylinder volume subtracted
- **Material**: Material of Part 2 shall be recorded as "Soil" or "Rock"

### 3.3 Part 3: Trimmed Soil Cylinder
- **Geometry**: Circular cylinder with cylindrical void of Part 1
- **Location**: Centered at (X, Y)
- **Diameter**: Same as Part 2 frustum top diameter
- **Height**: From Target Level to Pile Cap Bottom Level
- **Void**: Part 1 pile cylinder volume subtracted

### 3.4 Site Boundary Clipping
- **Process**: All geometric parts (Part1, Part 2 and Part 3) shall be clipped by the site boundary
- **Method**: Boolean intersection with extruded site boundary polygon by cutting vertically
- **Extrusion Heights**: 
  - Part 1: From Pile Cap Bottom Level to Founding Level
  - Part 2: From Target Level to Founding Level
  - Part 3: From Pile Cap Bottom Level to Target Level
- **Volume Exclusion**: Any volume extending outside the site boundary is excluded from calculations

### 3.5 Overlap Considerations

#### 3.5.1 Overlap Types and Detection Methodology

**Primary Overlap Types:**
- **Part 2-2 Overlap**: Intersection between soil/rock frustums of different piles
- **Part 2-3 Overlap**: Intersection between one pile's frustum (Part 2) and another pile's soil cylinder (Part 3)
- **Part 3-3 Overlap**: Intersection between soil cylinders of different piles

**Detection Criteria:**
- **Minimum Volume Threshold**: Overlaps below 0.001 m³ shall be ignored to avoid numerical precision issues
- **Geometric Validation**: All overlap geometries must be valid, watertight meshes
- **Site Boundary Compliance**: Only overlaps occurring within the site boundary are considered

#### 3.5.2 Geometry after Clipping
- **Part 1**: Pile Cylinder (One object per pile, clipped by site boundary)
- **Part 2**: Isolated Soil/Rock Frustum (One object per pile, clipped by site boundary with all overlapping volumes removed)
- **Part 3**: Isolated Soil Cylinder (One object per pile, clipped by site boundary with all overlapping volumes removed)
- **Overlapping parts of Part 2-2**: Soil/Rock Frustum Intersections (Union objects created from overlapping frustum volumes, with detailed records of all contributing pile identifiers)
- **Overlapping parts of Part 2-3**: Mixed Geometry Intersections (Union objects created from overlapping soil/rock frustum and soil cylinder volumes, with detailed records of all contributing pile identifiers)
- **Overlapping parts of Part 3-3**: Soil Cylinder Intersections (Union objects created from overlapping soil cylinder volumes, with detailed records of all contributing pile identifiers)

**Note**: The above objects represent mutually exclusive geometric categories after overlap processing. Each volume element belongs to only one category, ensuring no double-counting in volume calculations.

## 4. Technology Stack and Library Selection

### 4.1 Primary Libraries

#### 4.1.1 Trimesh (Primary Geometry Engine)
- **Purpose**: Core geometric operations and boolean operations
- **Key Features**:
  - Robust boolean operations (union, difference, intersection)
  - Volume calculations
  - Mesh validation and repair
  - Collision detection
- **Usage**: Main library for all geometric calculations

#### 4.1.2 PyVista (Geometry Engine & Visualization Engine)
- **Purpose**: 3D visualization and result presentation
- **Key Features**:
  - VTK-based visualization
  - Interactive 3D plotting
  - Mesh manipulation and display
  - Export capabilities
- **Usage**: Visualization of results and geometric models

#### 4.1.3 Supporting Libraries
- **NumPy**: Numerical operations and array handling
- **Pandas**: Data management and analysis
- **SciPy**: Advanced mathematical operations
- **Matplotlib**: 2D plotting and charts
- **Shapely**: 2D geometric operations and polygon handling for site boundary processing

### 4.2 Backup/Alternative Libraries
- **Manifold3D**: For critical boolean operations requiring highest robustness
- **Additional PyVista**: Enhanced visualization options
- **MeshLab Python**: Additional mesh processing capabilities

## 6. Implementation Architecture

### 6.1 Data Flow

1. **Input Processing & Validation**: 
    - Load pile data from concatenated dataframes (BP, SHP, DHP, MP)
    - Load site boundary coordinates
    - Validate data integrity, coordinate systems, and geometric constraints
    - Check pile marks for uniqueness and required field completeness

2. **Site Boundary Processing**: 
    - Verify site boundary forms valid, non-self-intersecting polygon
    - Auto-close polygon if first and last points don't match
    - Create extruded 3D boundary volumes for clipping operations
    - Validate that pile centers are within reasonable proximity to boundary

3. **Individual Geometry Creation**: 
    - Generate Part 1 (pile cylinders) for all piles
    - Create Part 2 (soil/rock frustums) with material-specific projection angles
    - Generate Part 3 (soil cylinders) using frustum top diameters
    - Perform initial geometric validation and mesh quality checks

4. **Site Boundary Clipping**: 
    - Clip Part 1, Part 2, and Part 3 volumes by extruded site boundary
    - Track volume losses due to boundary constraints
    - Maintain original unclipped volumes for comparison reporting
    - Validate clipped geometries remain watertight

5. **Overlap Detection & Analysis**: 
    - Detect Part 2-2 (frustum-frustum) intersections using spatial indexing
    - Identify Part 2-3 (frustum-cylinder) overlaps
    - Find Part 3-3 (cylinder-cylinder) intersections
    - Filter overlaps below minimum volume threshold (0.001 m³)

6. **Overlap Processing & Volume Extraction**: 
    - Extract overlapping volumes using boolean intersection operations
    - Remove overlapping portions from individual pile geometries
    - Create union objects for each overlap type with contributing pile records
    - Ensure geometric consistency and no double-counting

7. **Volume Calculation & Distribution**: 
    - Calculate volumes for all isolated and overlapping geometries
    - Proportionally distribute shared volumes among contributing piles
    - Maintain detailed audit trail of all volume allocations
    - Validate total volume conservation

8. **Quality Assurance & Validation**: 
    - Verify mesh integrity and geometric validity
    - Check volume calculation accuracy against analytical solutions
    - Validate overlap detection completeness
    - Ensure site boundary compliance for all volumes

9. **Reporting & Output Generation**: 
    - Generate individual pile volume reports with clipping analysis
    - Create overlap summary tables with contributing pile details
    - Produce project-level statistics and efficiency metrics
    - Export data in multiple formats (CSV, JSON, PDF)

10. **3D Visualization & Review**: 
     - Create interactive 3D models showing all geometries
     - Visualize site boundary constraints and clipping effects
     - Display overlap regions with color-coded pile contributions
     - Generate publication-ready plots and diagrams

### 6.2 Module Architecture

- **DataProcessor**: Input validation and preprocessing
- **GeometryEngine**: 3D model creation and boolean operations  
- **BoundaryClipper**: Site boundary constraint handling
- **OverlapAnalyzer**: Intersection detection and volume extraction
- **VolumeCalculator**: Precise volume computation and distribution
- **Visualizer**: 3D rendering and interactive display
- **ReportGenerator**: Output formatting and export functionality

## 7. Output and Reporting Specifications

### 7.1 Individual Pile Volume Reports
The system shall generate a comprehensive pandas DataFrame containing detailed volume breakdowns for each pile, with subsequent export to CSV format for external analysis and record-keeping.

**DataFrame Structure and Column Specifications:**

**Primary Volume Components:**
- **pile_mark**: Unique pile identifier and basic parameters
- **original_part1_volume**: Pile cylinder volume before site boundary clipping (m³)
- **original_part2_volume**: Soil/rock frustum volume before site boundary clipping (m³)
- **original_part3_volume**: Soil cylinder volume before site boundary clipping (m³)
- **material_classification**: Material designation for Part 2 based on target stratum ("Soil" or "Rock")

**Site Boundary Clipping Analysis:**
- **clipped_part1_volume**: Pile cylinder volume after site boundary clipping (m³)
- **clipped_part2_volume**: Soil/rock frustum volume after site boundary clipping (m³)
- **clipped_part3_volume**: Soil cylinder volume after site boundary clipping (m³)
- **part1_boundary_loss**: Volume reduction due to site boundary limitations for Part 1 (m³)
- **part2_boundary_loss**: Volume reduction due to site boundary limitations for Part 2 (m³)
- **part3_boundary_loss**: Volume reduction due to site boundary limitations for Part 3 (m³)
- **part1_boundary_compliance_pct**: Ratio of clipped to original volumes for Part 1 (%)
- **part2_boundary_compliance_pct**: Ratio of clipped to original volumes for Part 2 (%)
- **part3_boundary_compliance_pct**: Ratio of clipped to original volumes for Part 3 (%)

**Non-Overlapping Volume Components:**
- **isolated_part1_volume**: Final pile cylinder volume (clipped, no overlaps applicable) (m³)
- **isolated_part2_volume**: Soil/rock frustum volume after overlap removal and site boundary clipping (m³)
- **isolated_part3_volume**: Soil cylinder volume after overlap removal and site boundary clipping (m³)

**Overlap Participation Summary:**
- **part2_2_overlap_count**: Number of frustum-frustum intersections involving this pile
- **part2_3_overlap_count**: Number of frustum-cylinder intersections involving this pile
- **part3_3_overlap_count**: Number of cylinder-cylinder intersections involving this pile
- **part2_2_overlap_codes**: Semicolon-separated list of overlap identification codes for Part 2-2 intersections
- **part2_3_overlap_codes**: Semicolon-separated list of overlap identification codes for Part 2-3 intersections
- **part3_3_overlap_codes**: Semicolon-separated list of overlap identification codes for Part 3-3 intersections

**Shared Volume Allocations:**
- **shared_part2_2_volume**: Total volume allocated from frustum-frustum overlaps (m³)
- **shared_part2_3_volume**: Total volume allocated from frustum-cylinder overlaps (m³)
- **shared_part3_3_volume**: Total volume allocated from cylinder-cylinder overlaps (m³)
- **total_shared_volume**: Sum of all allocated overlap volumes (m³)

**Final Volume Summary:**
- **total_soil_volume**: Sum of all soil-classified volumes including isolated and shared components (m³)
- **total_rock_volume**: Sum of all rock-classified volumes including isolated and shared components (m³)
- **total_volume**: Grand total of all volumes allocated to this pile (m³)

**CSV Export Specifications:**
- **File Format**: UTF-8 encoded CSV with comma delimiters
- **Numeric Precision**: Volume values rounded to 3 decimal places
- **Column Headers**: Descriptive headers matching DataFrame column names
- **Row Indexing**: Pile mark as primary identifier
- **File Naming Convention**: `cone_volume_analysis_[timestamp].csv`
- **Additional Metadata**: Header rows containing analysis parameters, site boundary area, and processing timestamp

### 7.2 Detailed Overlap Analysis Reports
The system shall generate a comprehensive pandas DataFrame containing detailed documentation for each detected overlap, with subsequent export to CSV format for external analysis and record-keeping.

**DataFrame Structure and Column Specifications:**

**Overlap Identification:**
- **overlap_code**: Unique identifier using format OVL-[Type]-[Sequential Number] (e.g., "OVL-P22-001", "OVL-P23-005")
- **overlap_type**: Classification category ("Part2-2", "Part2-3", "Part3-3")
- **contributing_pile_marks**: Semicolon-separated list of all intersecting pile identifiers
- **pile_count**: Number of piles participating in this overlap intersection

**Detailed Component Analysis:**
- **pile_part_combinations**: Detailed breakdown of which pile parts are intersecting (e.g., "Pile-A01:Part2 x Pile-B02:Part3")
- **contributing_pile_parts**: Semicolon-separated list of all pile parts involved in the overlap (e.g., "Pile-A01:Part2:Soil;Pile-B02:Part3:Rock;Pile-C03:Part2:Soil")

**Volume Information:**
- **overlap_volume**: Total intersection volume after site boundary clipping (m³)
- **volume_per_pile**: Volume allocated to each contributing pile (overlap_volume ÷ pile_count) (m³)
- **dominant_material_type**: Primary material classification based on Part 2 components ("Soil", "Rock", or "Mixed")

**Location Data:**
- **centroid_x**: X-coordinate of overlap volume center (m)
- **centroid_y**: Y-coordinate of overlap volume center (m)
- **centroid_z**: Z-coordinate of overlap volume center (m)

**Quality Checks:**
- **within_site_boundary**: Boolean flag indicating if overlap is fully contained within site boundary
- **geometric_validity**: Boolean flag indicating mesh is watertight and valid

**CSV Export Specifications:**
- **File Format**: UTF-8 encoded CSV with comma delimiters
- **Numeric Precision**: Volume values rounded to 4 decimal places, coordinates to 2 decimal places
- **Column Headers**: Descriptive headers matching DataFrame column names
- **Row Indexing**: Overlap code as primary identifier
- **File Naming Convention**: `overlap_analysis_[timestamp].csv`
- **Additional Metadata**: Header rows containing analysis parameters, total overlap count, and processing summary
- **Cross-Reference**: Include pile_mark references for easy correlation with individual pile reports

### 7.3 Export Formats
- CSV for tabular data
- VTK/STL/Other formats for 3D geometries that can be visualized in AutoCAD

## 8. Performance Considerations

### 8.1 Optimization Strategies
- **Spatial Indexing**: Use spatial data structures for efficient overlap detection
- **Parallel Processing**: Utilize multiprocessing for independent calculations
- **Memory Management**: Stream processing for large datasets
- **Caching**: Cache intermediate geometric results

### 8.2 Scalability Requirements
- Handle 1000+ piles for large projects
- Efficient memory usage for complex geometries
- Progress reporting for long-running calculations
- Incremental processing capabilities

## 9. Testing and Validation Strategy

### 9.1 Unit Testing
- Test individual pile geometry creation (cylinders, frustums)
- Verify volume calculations against known formulas
- Validate boolean operations (intersection, union, difference)
- Check input data validation routines
- Test site boundary processing functions

### 9.2 Integration Testing
- End-to-end workflow testing from input to output
- Multi-pile overlap scenarios
- Site boundary clipping verification
- Performance testing with large datasets
- Memory usage monitoring

### 9.3 Standard Test Dataset
**Test File**: Use `A.SAFEInput_Geometry.xlsx` in the `example` folder for all validation testing. Functions in `read` folder can read the excel file into excel_input class.

**Test Cases**:
- Simple two-pile overlaps
- Complex multi-pile intersections
- Soil vs. rock material handling
- Site boundary constraint testing
- Edge cases (touching piles, extreme dimensions)
- Compare results against hand calculations for accuracy verification

**Success Criteria**:
- Volume accuracy within 0.1% tolerance
- All meshes remain watertight and valid
- Consistent results across multiple runs
- Performance meets scalability requirements

## 10. Quality Assurance

### 10.1 Accuracy Requirements
- Volume calculations accurate to 0.1% for standard cases
- Geometric operations must maintain watertight meshes
- Mass conservation in volume distributions
- Consistent results across different pile orderings

### 10.2 Error Handling
- Graceful handling of degenerate geometries
- Robust boolean operation fallbacks
- Clear error messages and diagnostics
- Data validation and sanitization

## 11. Dependencies and Prerequisites

### 11.1 Python Environment
- Python 3.13+
- 64-bit system recommended
- Minimum 8GB RAM for large projects

### 11.2 Required Libraries
```
trimesh>=4.7.1
pyvista>=0.45.3
numpy>=2.3.2
pandas>=2.3.1
scipy>=1.16.0
matplotlib>=3.10.5
shapely>=2.1.1
```

### 13.3 Optional Libraries
```
manifold3d>=3.2.0  # For robust boolean operations
pyvista[optional]>=0.45.3  # Enhanced visualization
meshlab-python     # Additional mesh processing
rtree>=1.4.0       # Spatial indexing
```

## 14. Conclusion

This specification provides a comprehensive framework for developing a sophisticated 3D pile volume analysis program. The combination of Trimesh for geometric operations, PyVista for visualization, Shapely for 2D polygon operations, and careful algorithm design will deliver a robust, accurate, and user-friendly solution for foundation engineering applications.

The inclusion of site boundary clipping ensures that volume calculations are constrained to the actual project area, providing more accurate and practical results for engineering analysis. The modular architecture ensures maintainability and extensibility, while the detailed testing strategy guarantees reliability. The implementation plan provides a clear path from initial development to production deployment.